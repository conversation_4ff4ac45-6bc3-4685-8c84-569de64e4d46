/**
 * @file test-sprint2-optimization.js
 * @description Teste dos sistemas implementados no Sprint 2
 * @version 1.0.0
 */

import { SystemIntegration } from './src/api/services/core/SystemIntegration.js';

class Sprint2OptimizationTest {
  constructor() {
    this.systemIntegration = new SystemIntegration();
    this.testResults = {
      cache: { passed: 0, failed: 0, tests: [] },
      logging: { passed: 0, failed: 0, tests: [] },
      health: { passed: 0, failed: 0, tests: [] },
      config: { passed: 0, failed: 0, tests: [] }
    };
  }

  async runAllTests() {
    console.log('🧪 INICIANDO TESTES DO SPRINT 2 - OTIMIZAÇÃO');
    console.log('=============================================\n');

    try {
      await this.testCacheIntelligente();
      await this.testLogsEstruturados();
      await this.testHealthChecks();
      await this.testConfiguracaoCentralizada();
      
      this.showResults();
      
    } catch (error) {
      console.error('❌ Erro nos testes:', error);
    }
  }

  async testCacheIntelligente() {
    console.log('📦 TESTANDO CACHE INTELIGENTE');
    console.log('==============================');
    
    const cache = this.systemIntegration.getCache();
    
    // Teste 1: Set e Get básico
    await this.runTest('cache', 'Set e Get básico', async () => {
      await cache.set('test_key', 'test_value', { ttl: 5000 });
      const value = await cache.get('test_key');
      return value === 'test_value';
    });

    // Teste 2: TTL e expiração
    await this.runTest('cache', 'TTL e expiração', async () => {
      await cache.set('expire_key', 'expire_value', { ttl: 100 });
      await new Promise(resolve => setTimeout(resolve, 150));
      const value = await cache.get('expire_key');
      return value === null;
    });

    // Teste 3: Invalidação por tags
    await this.runTest('cache', 'Invalidação por tags', async () => {
      await cache.set('tag_key1', 'value1', { tags: ['user', 'session'] });
      await cache.set('tag_key2', 'value2', { tags: ['user'] });
      await cache.invalidateByTags(['user']);
      const value1 = await cache.get('tag_key1');
      const value2 = await cache.get('tag_key2');
      return value1 === null && value2 === null;
    });

    // Teste 4: Métricas
    await this.runTest('cache', 'Métricas do cache', async () => {
      const metrics = cache.getMetrics();
      return metrics && typeof metrics.hitRate === 'number' && typeof metrics.size === 'number';
    });

    // Teste 5: Health status
    await this.runTest('cache', 'Health status', async () => {
      const health = cache.getHealthStatus();
      return health && health.status && health.memoryUsage !== undefined;
    });

    console.log('');
  }

  async testLogsEstruturados() {
    console.log('📝 TESTANDO LOGS ESTRUTURADOS');
    console.log('==============================');
    
    const logger = this.systemIntegration.getLogger();
    
    // Teste 1: Log básico
    await this.runTest('logging', 'Log básico', async () => {
      logger.info('Test log message', { userId: 'test_user' });
      return true; // Se não deu erro, passou
    });

    // Teste 2: Log com contexto
    await this.runTest('logging', 'Log com contexto', async () => {
      logger.logGameAction('ColorMatch', 'card_select', 'user123', 'session456', { score: 100 });
      return true;
    });

    // Teste 3: Log de erro
    await this.runTest('logging', 'Log de erro', async () => {
      logger.error('Test error message', { component: 'test' }, { stack: 'test stack' });
      return true;
    });

    // Teste 4: Log multissensorial
    await this.runTest('logging', 'Log multissensorial', async () => {
      logger.logMultisensoryEvent('sensor_update', 'user123', 'session456', { 
        visual: 0.8, 
        auditory: 0.6 
      });
      return true;
    });

    // Teste 5: Estatísticas
    await this.runTest('logging', 'Estatísticas de log', async () => {
      const stats = logger.getLogStats();
      return stats && stats.level && stats.transports !== undefined;
    });

    console.log('');
  }

  async testHealthChecks() {
    console.log('🏥 TESTANDO HEALTH CHECKS');
    console.log('==========================');
    
    const healthService = this.systemIntegration.getHealthService();
    
    // Teste 1: Health check básico
    await this.runTest('health', 'Health check básico', async () => {
      const health = await healthService.runHealthCheck();
      return health && health.overall && health.components && health.systemMetrics;
    });

    // Teste 2: Status de componentes
    await this.runTest('health', 'Status de componentes', async () => {
      const health = healthService.getHealthStatus();
      return health && Object.keys(health.components).length > 0;
    });

    // Teste 3: Métricas do sistema
    await this.runTest('health', 'Métricas do sistema', async () => {
      const health = await healthService.runHealthCheck();
      const metrics = health.systemMetrics;
      return metrics && metrics.memory && metrics.cpu && metrics.uptime !== undefined;
    });

    // Teste 4: Estatísticas de saúde
    await this.runTest('health', 'Estatísticas de saúde', async () => {
      const stats = healthService.getHealthStats();
      return stats && stats.totalChecks !== undefined && stats.healthyPercentage !== undefined;
    });

    // Teste 5: Histórico de saúde
    await this.runTest('health', 'Histórico de saúde', async () => {
      const history = healthService.getHealthHistory(10);
      return Array.isArray(history);
    });

    console.log('');
  }

  async testConfiguracaoCentralizada() {
    console.log('⚙️ TESTANDO CONFIGURAÇÃO CENTRALIZADA');
    console.log('======================================');
    
    const config = this.systemIntegration.getConfig();
    
    // Teste 1: Obter configuração básica
    await this.runTest('config', 'Obter configuração básica', async () => {
      const gameConfig = config.getGameConfig('colorMatch');
      return gameConfig && gameConfig.difficulties && gameConfig.scoring;
    });

    // Teste 2: Configuração por caminho
    await this.runTest('config', 'Configuração por caminho', async () => {
      const difficulty = config.get('games.colorMatch.difficulties.easy');
      return difficulty && difficulty.gridSize !== undefined;
    });

    // Teste 3: Configuração de AI Brain
    await this.runTest('config', 'Configuração de AI Brain', async () => {
      const aiConfig = config.getAIBrainConfig();
      return aiConfig && aiConfig.enabled !== undefined;
    });

    // Teste 4: Configuração multissensorial
    await this.runTest('config', 'Configuração multissensorial', async () => {
      const multisensoryConfig = config.getMultisensoryConfig();
      return multisensoryConfig && multisensoryConfig.modalidades && multisensoryConfig.thresholds;
    });

    // Teste 5: Informações do ambiente
    await this.runTest('config', 'Informações do ambiente', async () => {
      const envInfo = config.getEnvironmentInfo();
      return envInfo && envInfo.environment && envInfo.nodeVersion;
    });

    console.log('');
  }

  async runTest(category, testName, testFn) {
    try {
      const result = await testFn();
      if (result) {
        console.log(`✅ ${testName}`);
        this.testResults[category].passed++;
        this.testResults[category].tests.push({ name: testName, status: 'PASSED' });
      } else {
        console.log(`❌ ${testName}`);
        this.testResults[category].failed++;
        this.testResults[category].tests.push({ name: testName, status: 'FAILED' });
      }
    } catch (error) {
      console.log(`❌ ${testName} - ${error.message}`);
      this.testResults[category].failed++;
      this.testResults[category].tests.push({ name: testName, status: 'ERROR', error: error.message });
    }
  }

  showResults() {
    console.log('📊 RESULTADOS DOS TESTES');
    console.log('=========================\n');

    let totalPassed = 0;
    let totalFailed = 0;

    Object.entries(this.testResults).forEach(([category, results]) => {
      const categoryName = {
        cache: 'Cache Inteligente',
        logging: 'Logs Estruturados',
        health: 'Health Checks',
        config: 'Configuração Centralizada'
      }[category];

      console.log(`📦 ${categoryName}:`);
      console.log(`   ✅ Passou: ${results.passed}`);
      console.log(`   ❌ Falhou: ${results.failed}`);
      console.log(`   📊 Taxa de sucesso: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
      console.log('');

      totalPassed += results.passed;
      totalFailed += results.failed;
    });

    console.log('🎯 RESUMO GERAL:');
    console.log('================');
    console.log(`✅ Total de testes que passaram: ${totalPassed}`);
    console.log(`❌ Total de testes que falharam: ${totalFailed}`);
    console.log(`📊 Taxa de sucesso geral: ${((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1)}%`);

    if (totalFailed === 0) {
      console.log('\n🎉 TODOS OS TESTES PASSARAM! Sprint 2 implementado com sucesso!');
    } else {
      console.log('\n⚠️ Alguns testes falharam. Revisar implementação necessária.');
    }
  }
}

// Executar testes
const tester = new Sprint2OptimizationTest();
tester.runAllTests().then(() => {
  console.log('\n🏁 Testes concluídos!');
}).catch(error => {
  console.error('💥 Erro ao executar testes:', error);
});
