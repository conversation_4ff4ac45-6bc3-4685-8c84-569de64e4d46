# Estrutura para CSS Modular

## Padrão de Diretórios
```
src/
├── components/
│   ├── ComponentName/
│   │   ├── ComponentName.jsx
│   │   ├── ComponentName.module.css
│   │   └── index.js
```

## Regras de Implementação
1. **Um módulo CSS por componente**
2. **Nomeclatura**:
   - Arquivo CSS: `[NomeDoComponente].module.css`
   - Classe CSS: usa camelCase (ex: `.headerContainer`)
3. **Escopo local**:
   ```jsx
   import styles from './Componente.module.css';
   
   function Componente() {
     return <div className={styles.container}>Conteúdo</div>;
   }
   ```

## Benefícios
- Elimina conflitos de nomes de classes
- Facilita manutenção
- Permite tree-shaking automático
- Isolamento de estilos

## Componentes Prioritários para Conversão
1. `src/components/common/ResilienceStatus.jsx`
2. `src/components/navigation/Header.jsx`
3. `src/components/common/DatabaseStatus.jsx`
4. `src/components/common/TextToSpeech.jsx`
