/**
 * Teste do sistema de verificação de requisições duplicadas
 * Demonstra como o SystemOrchestrator previne processamento redundante
 */

import { SystemOrchestrator } from './src/api/services/core/SystemOrchestrator.js';

// Mock do DatabaseService
const mockDatabaseService = {
  query: async (query, params) => {
    console.log('🗄️ Mock SQL Query:', query.substring(0, 50) + '...');
    console.log('📋 Parameters count:', params?.length || 0);
    
    if (query.includes('INSERT INTO game_sessions')) {
      return [{ id: Math.floor(Math.random() * 1000) }];
    }
    return [];
  },
  saveCompleteSession: async (childId, gameName, sessionData) => {
    console.log('💾 Mock saveCompleteSession:', { childId, gameName, sessionId: sessionData.sessionId });
    return { success: true, id: Date.now() };
  },
  saveMetrics: async (childId, gameName, therapeuticMetrics, therapeuticAnalysis) => {
    console.log('📊 Mock saveMetrics:', { childId, gameName, metricsCount: Object.keys(therapeuticMetrics || {}).length });
    return { success: true };
  },
  isConnected: () => true
};

async function testDuplicateRequestPrevention() {
  try {
    console.log('🧪 Teste: Sistema de Verificação de Requisições Duplicadas\n');

    // Criar instância do SystemOrchestrator
    const orchestrator = await SystemOrchestrator.getInstance(mockDatabaseService, {
      logLevel: 'info'
    });

    // Dados de teste idênticos
    const gameData = {
      sessionId: 'test_session_duplicate_check',
      userId: 'user_123',
      gameId: 'ColorMatch',
      score: 500,
      level: 2,
      timeSpent: 120000,
      timestamp: Date.now()
    };

    console.log('📊 Dados de teste (serão enviados 3 vezes):');
    console.log(JSON.stringify(gameData, null, 2));
    console.log('\n🔄 Processando primeira requisição...');

    // Primeira requisição - deve processar normalmente
    const result1 = await orchestrator.processGameInput(gameData);
    console.log('✅ Primeira requisição:', result1.success ? 'PROCESSADA' : 'FALHOU');

    console.log('\n🔄 Processando segunda requisição (duplicada)...');

    // Segunda requisição idêntica - deve detectar duplicação
    const result2 = await orchestrator.processGameInput(gameData);
    console.log('🚫 Segunda requisição:', result2.success ? 'PROCESSADA' : 'DUPLICADA DETECTADA');

    console.log('\n⏱️ Aguardando 1 segundo...\n');
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('🔄 Processando terceira requisição (duplicada)...');

    // Terceira requisição - ainda deve detectar duplicação
    const result3 = await orchestrator.processGameInput(gameData);
    console.log('🚫 Terceira requisição:', result3.success ? 'PROCESSADA' : 'DUPLICADA DETECTADA');

    // Teste com dados ligeiramente diferentes
    console.log('\n🔄 Processando requisição com dados diferentes...');
    const differentData = {
      ...gameData,
      sessionId: 'test_session_different',
      score: 600
    };

    const result4 = await orchestrator.processGameInput(differentData);
    console.log('✅ Requisição diferente:', result4.success ? 'PROCESSADA' : 'FALHOU');

    // Verificar cache de idempotência
    console.log('\n📊 Estatísticas do cache de idempotência:');
    console.log('Cache size:', orchestrator.idempotencyCache?.size || 0);

    // Testar limpeza do cache
    orchestrator.cleanupIdempotencyCache();
    console.log('🧹 Cache limpo');

    return {
      test1: result1.success,
      test2: !result2.success, // Esperamos que seja duplicada
      test3: !result3.success, // Esperamos que seja duplicada
      test4: result4.success
    };

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
    return { error: error.message };
  }
}

async function testProcessGameMetricsDuplication() {
  try {
    console.log('\n🧪 Teste: Verificação de Duplicação em processGameMetrics\n');

    const orchestrator = await SystemOrchestrator.getInstance(mockDatabaseService);

    const metricsData = {
      sessionId: 'metrics_session_123',
      score: 750,
      correctAnswers: 15,
      wrongAnswers: 3,
      timeSpent: 180000,
      averageResponseTime: 1200
    };

    console.log('📊 Processando métricas pela primeira vez...');
    const result1 = await orchestrator.processGameMetrics('child_456', 'MemoryGame', metricsData);
    console.log('✅ Primeira tentativa:', result1.success ? 'PROCESSADA' : 'FALHOU');

    console.log('\n📊 Processando as mesmas métricas novamente...');
    const result2 = await orchestrator.processGameMetrics('child_456', 'MemoryGame', metricsData);
    console.log('🚫 Segunda tentativa:', result2.success ? 'PROCESSADA (INESPERADO)' : 'DUPLICADA DETECTADA (ESPERADO)');

    return {
      firstProcess: result1.success,
      secondProcess: result2.success === result1.success // Se retornou o mesmo resultado
    };

  } catch (error) {
    console.error('❌ Erro no teste de métricas:', error.message);
    return { error: error.message };
  }
}

// Executar testes
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('🚀 Iniciando testes do sistema de verificação de duplicação...\n');
  
  testDuplicateRequestPrevention()
    .then(results => {
      console.log('\n📈 Resultados do teste de duplicação:');
      console.log(JSON.stringify(results, null, 2));
      return testProcessGameMetricsDuplication();
    })
    .then(metricsResults => {
      console.log('\n📈 Resultados do teste de métricas:');
      console.log(JSON.stringify(metricsResults, null, 2));
      console.log('\n✅ Todos os testes concluídos!');
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ Erro geral nos testes:', error);
      process.exit(1);
    });
}

export { testDuplicateRequestPrevention, testProcessGameMetricsDuplication };
