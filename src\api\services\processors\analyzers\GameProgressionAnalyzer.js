/**
 * 🎯 GAME PROGRESSION ANALYZER
 * Analisador especializado em progressão e domínio de jogos
 * Portal Betina V3 - FASE CRÍTICA
 */

export class GameProgressionAnalyzer {
  constructor() {
    this.analyzerType = 'GameProgression';
    this.version = '3.0.0';
    this.capabilities = {
      progressionTracking: true,
      masteryAssessment: true,
      difficultyAdaptation: true,
      learningEfficiency: true,
      retentionAnalysis: true
    };
  }

  /**
   * Analisa dados de progressão do ProgressionMasteryCollector
   * @param {Object} progressionData - Dados do ProgressionMasteryCollector
   * @param {Object} gameContext - Contexto do jogo
   * @returns {Promise<Object>} Análise de progressão avançada
   */
  async analyze(progressionData, gameContext = {}) {
    try {
      console.log('🎯 GameProgressionAnalyzer: Iniciando análise de progressão...');

      if (!this.validateProgressionData(progressionData)) {
        return this.generateFallbackAnalysis('Dados de progressão insuficientes');
      }

      // Análises principais baseadas nos dados do ProgressionMasteryCollector
      const masteryAnalysis = this.analyzeMasteryProgression(progressionData);
      const adaptiveRecommendations = this.generateAdaptiveStrategy(progressionData, gameContext);
      const learningProfile = this.buildLearningProfile(progressionData);
      const riskAssessment = this.assessLearningRisks(progressionData);
      const therapeuticInsights = this.generateTherapeuticInsights(progressionData, gameContext);

      const results = {
        // Análise de domínio
        currentMasteryLevel: masteryAnalysis.level,
        masteryScore: masteryAnalysis.score,
        readinessForAdvancement: masteryAnalysis.readiness,
        masteryTrend: masteryAnalysis.trend,
        
        // Perfil de aprendizado
        learningStyle: learningProfile.style,
        optimalDifficulty: learningProfile.optimalDifficulty,
        learningEfficiency: learningProfile.efficiency,
        retentionCapacity: learningProfile.retention,
        
        // Estratégia adaptativa
        recommendedActions: adaptiveRecommendations.actions,
        difficultyAdjustment: adaptiveRecommendations.difficulty,
        interventionNeeds: adaptiveRecommendations.interventions,
        adaptationStrategy: adaptiveRecommendations.strategy,
        
        // Insights terapêuticos
        therapeuticGoals: therapeuticInsights.goals,
        progressionPlan: therapeuticInsights.plan,
        cognitiveIndicators: therapeuticInsights.cognitive,
        motivationalFactors: therapeuticInsights.motivation,
        
        // Avaliação de risco
        riskFactors: riskAssessment.factors,
        alertLevel: riskAssessment.level,
        recommendedSupport: riskAssessment.support,
        
        // Métricas de desempenho
        progressionVelocity: this.calculateProgressionVelocity(progressionData),
        consistencyIndex: this.calculateConsistencyIndex(progressionData),
        engagementLevel: this.assessEngagementLevel(progressionData),
        
        // Predições
        estimatedTimeToMastery: progressionData.timeToMastery || -1,
        nextMilestone: this.predictNextMilestone(progressionData),
        expectedProgression: this.calculateExpectedProgression(progressionData),
        
        // Metadados
        analysisTimestamp: new Date().toISOString(),
        analyzerVersion: this.version,
        dataQuality: this.assessAnalysisQuality(progressionData),
        gameContext: gameContext.gameType || 'unknown'
      };

      console.log('✅ GameProgressionAnalyzer: Análise concluída:', {
        level: results.currentMasteryLevel,
        readiness: results.readinessForAdvancement,
        riskLevel: results.alertLevel
      });

      return results;

    } catch (error) {
      console.error('❌ GameProgressionAnalyzer: Erro na análise:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de entrada do ProgressionMasteryCollector
   */
  validateProgressionData(data) {
    if (!data) return false;
    
    const requiredFields = ['currentLevel', 'masteryScore', 'progressionPath'];
    return requiredFields.every(field => data.hasOwnProperty(field));
  }

  /**
   * Analisa progressão de domínio
   */
  analyzeMasteryProgression(data) {
    return {
      level: data.currentLevel || 'unknown',
      score: data.masteryScore || 0,
      readiness: data.readinessForNext || false,
      trend: data.masteryTrend || 'stable',
      indicators: data.masteryIndicators || {},
      nextLevelReadiness: data.nextLevelReadiness || 0
    };
  }

  /**
   * Gera estratégia adaptativa
   */
  generateAdaptiveStrategy(data, context) {
    const actions = data.immediateRecommendations || [];
    const difficulty = data.optimalChallenge || context.currentDifficulty || 'medium';
    const interventions = data.interventionNeeds || [];
    
    return {
      actions,
      difficulty,
      interventions,
      strategy: data.adaptationStrategy || ['monitoring']
    };
  }

  /**
   * Constrói perfil de aprendizado
   */
  buildLearningProfile(data) {
    const challengePreference = data.challengePreference || 'medium';
    
    return {
      style: this.identifyLearningStyle(data),
      optimalDifficulty: challengePreference,
      efficiency: data.learningEfficiency || 0.5,
      retention: {
        shortTerm: data.shortTermRetention || 0.5,
        longTerm: data.longTermRetention || 0.5,
        transfer: data.transferLearning || 0.5
      }
    };
  }

  /**
   * Identifica estilo de aprendizado
   */
  identifyLearningStyle(data) {
    const growthPhase = data.growthPhase || 'unknown';
    const motivationLevel = data.motivationLevel || 0.5;
    const consistency = data.engagementConsistency || 0.5;
    
    if (motivationLevel > 0.8 && consistency > 0.7) {
      return 'self_directed';
    } else if (growthPhase === 'early_learning') {
      return 'guided_practice';
    } else if (consistency < 0.5) {
      return 'structured_support';
    }
    
    return 'adaptive_mixed';
  }

  /**
   * Avalia riscos de aprendizado
   */
  assessLearningRisks(data) {
    const riskFactors = data.riskFactors || [];
    const level = this.calculateRiskLevel(riskFactors);
    
    return {
      factors: riskFactors,
      level,
      support: this.recommendSupport(level, riskFactors)
    };
  }

  /**
   * Calcula nível de risco
   */
  calculateRiskLevel(riskFactors) {
    if (riskFactors.includes('performance_decline') || riskFactors.includes('low_motivation')) {
      return 'high';
    } else if (riskFactors.includes('high_variability') || riskFactors.includes('slow_progress')) {
      return 'medium';
    } else if (riskFactors.length > 0) {
      return 'low';
    }
    
    return 'minimal';
  }

  /**
   * Recomenda suporte baseado no risco
   */
  recommendSupport(level, factors) {
    const support = [];
    
    switch (level) {
      case 'high':
        support.push('Intervenção terapêutica intensiva');
        support.push('Ajuste imediato de dificuldade');
        break;
      case 'medium':
        support.push('Monitoramento aumentado');
        support.push('Estratégias de motivação');
        break;
      case 'low':
        support.push('Acompanhamento regular');
        break;
      default:
        support.push('Monitoramento padrão');
    }
    
    return support;
  }

  /**
   * Gera insights terapêuticos
   */
  generateTherapeuticInsights(data, context) {
    return {
      goals: data.mediumTermGoals || [],
      plan: data.longTermObjectives || [],
      cognitive: this.analyzeCognitiveIndicators(data),
      motivation: {
        level: data.motivationLevel || 0.5,
        consistency: data.engagementConsistency || 0.5,
        factors: this.identifyMotivationalFactors(data)
      }
    };
  }

  /**
   * Analisa indicadores cognitivos
   */
  analyzeCognitiveIndicators(data) {
    const indicators = data.masteryIndicators || {};
    
    return {
      accuracy: indicators.accuracy || 0,
      speed: indicators.speed || 0,
      consistency: indicators.consistency || 0,
      confidence: indicators.confidence || 0,
      efficiency: indicators.efficiency || 0,
      overallCognitive: this.calculateOverallCognitive(indicators)
    };
  }

  /**
   * Calcula índice cognitivo geral
   */
  calculateOverallCognitive(indicators) {
    const weights = { accuracy: 0.3, speed: 0.2, consistency: 0.25, confidence: 0.15, efficiency: 0.1 };
    
    return Object.entries(weights).reduce((total, [indicator, weight]) => {
      return total + (indicators[indicator] || 0) * weight;
    }, 0);
  }

  /**
   * Identifica fatores motivacionais
   */
  identifyMotivationalFactors(data) {
    const factors = [];
    
    if (data.motivationLevel > 0.7) factors.push('high_intrinsic_motivation');
    if (data.engagementConsistency > 0.7) factors.push('consistent_engagement');
    if (data.masteryTrend === 'improving') factors.push('positive_progress_feedback');
    if (data.challengePreference) factors.push('appropriate_difficulty');
    
    return factors;
  }

  /**
   * Calcula velocidade de progressão
   */
  calculateProgressionVelocity(data) {
    return data.improvementRate || 0;
  }

  /**
   * Calcula índice de consistência
   */
  calculateConsistencyIndex(data) {
    const indicators = data.masteryIndicators || {};
    return indicators.consistency || 0;
  }

  /**
   * Avalia nível de engajamento
   */
  assessEngagementLevel(data) {
    return data.engagementConsistency || 0.5;
  }

  /**
   * Prediz próximo marco
   */
  predictNextMilestone(data) {
    const milestones = data.milestones || [];
    const currentLevel = data.currentLevel;
    
    // Prever próximo marco baseado no progresso atual
    if (data.readinessForNext) {
      return `Avanço para próximo nível de ${currentLevel}`;
    } else if (data.masteryScore > 0.7) {
      return 'Consolidação do nível atual';
    } else {
      return 'Fortalecimento de habilidades básicas';
    }
  }

  /**
   * Calcula progressão esperada
   */
  calculateExpectedProgression(data) {
    return data.expectedProgression || 0.5;
  }

  /**
   * Avalia qualidade da análise
   */
  assessAnalysisQuality(data) {
    const dataQuality = data.dataQuality || {};
    return {
      score: dataQuality.score || 0.5,
      issues: dataQuality.issues || [],
      level: dataQuality.level || 'medium',
      reliability: this.calculateReliability(dataQuality)
    };
  }

  /**
   * Calcula confiabilidade da análise
   */
  calculateReliability(dataQuality) {
    const score = dataQuality.score || 0.5;
    const issueCount = (dataQuality.issues || []).length;
    
    return Math.max(0, score - (issueCount * 0.1));
  }

  /**
   * Gera análise de fallback
   */
  generateFallbackAnalysis(errorMessage) {
    return {
      currentMasteryLevel: 'unknown',
      masteryScore: 0,
      readinessForAdvancement: false,
      masteryTrend: 'unknown',
      learningStyle: 'adaptive_mixed',
      optimalDifficulty: 'medium',
      learningEfficiency: 0.5,
      retentionCapacity: { shortTerm: 0.5, longTerm: 0.5, transfer: 0.5 },
      recommendedActions: ['Coletar mais dados para análise'],
      difficultyAdjustment: 'maintain',
      interventionNeeds: ['improve_data_collection'],
      adaptationStrategy: ['monitoring'],
      therapeuticGoals: [],
      progressionPlan: [],
      cognitiveIndicators: { accuracy: 0, speed: 0, consistency: 0, confidence: 0, efficiency: 0, overallCognitive: 0 },
      motivationalFactors: { level: 0.5, consistency: 0.5, factors: [] },
      riskFactors: ['insufficient_data'],
      alertLevel: 'medium',
      recommendedSupport: ['Melhorar coleta de dados'],
      progressionVelocity: 0,
      consistencyIndex: 0.5,
      engagementLevel: 0.5,
      estimatedTimeToMastery: -1,
      nextMilestone: 'Estabelecer linha de base',
      expectedProgression: 0.5,
      analysisTimestamp: new Date().toISOString(),
      analyzerVersion: this.version,
      dataQuality: { score: 0, issues: [errorMessage], level: 'error', reliability: 0 },
      gameContext: 'unknown',
      status: 'fallback',
      error: errorMessage
    };
  }
}
