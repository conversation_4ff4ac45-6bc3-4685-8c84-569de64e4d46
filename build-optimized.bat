@echo off
echo 🚀 Executando build otimizado do Portal Betina V3...
echo.

echo 📦 Limpando cache do Node.js...
npm cache clean --force

echo 🧹 Removendo node_modules e reinstalando dependências...
rd /s /q node_modules 2>nul
npm install --production

echo 🔨 Executando build com otimizações...
npm run build

echo.
echo ✅ Build concluído! Verifique o tamanho dos arquivos:
echo.
dir dist\assets\*.js /o-s
echo.
echo 📊 Executando análise de bundle...
npm run analyze

pause
