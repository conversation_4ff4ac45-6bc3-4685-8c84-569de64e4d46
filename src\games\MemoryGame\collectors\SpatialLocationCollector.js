/**
 * 🗺️ SPATIAL LOCATION COLLECTOR V3
 * Coletor especializado para análise de memória espacial e localização
 * Localização: src/games/MemoryGame/collectors/SpatialLocationCollector.js
 */

export class SpatialLocationCollector {
  constructor() {
    this.id = 'spatial_location_v3';
    this.name = 'Memória de Localização Espacial V3';
    this.version = '3.0.0';
    this.category = 'spatial_processing';
    
    this.data = {
      spatialMaps: [],
      locationAccuracy: [],
      spatialDistortions: [],
      navigationPatterns: [],
      spatialWorkingMemory: [],
      coordinateMemory: [],
      spatialSequencing: [],
      mentalRotation: []
    };
  }

  // Coletar dados de tentativas de localização espacial
  collect(data) {
    const {
      originalLocations,
      userLocations,
      gridSize,
      responseTime,
      accuracy,
      difficulty,
      spatialConfiguration,
      timestamp
    } = data;

    // Análise espacial detalhada
    const spatialAnalysis = this.analyzeSpatialMemory(originalLocations, userLocations, gridSize);
    
    this.data.spatialMaps.push({
      original: originalLocations,
      response: userLocations,
      gridSize,
      configuration: spatialConfiguration,
      analysis: spatialAnalysis,
      responseTime,
      timestamp
    });

    // Precisão de localização
    this.data.locationAccuracy.push({
      accuracy,
      averageDistance: spatialAnalysis.averageDistance,
      exactMatches: spatialAnalysis.exactMatches,
      gridSize,
      timestamp
    });

    // Distorções espaciais
    const distortions = this.analyzeSpatialDistortions(originalLocations, userLocations, gridSize);
    this.data.spatialDistortions.push(distortions);

    // Padrões de navegação
    const navigationPattern = this.analyzeNavigationPattern(userLocations, originalLocations);
    this.data.navigationPatterns.push(navigationPattern);

    // Memória de trabalho espacial
    const spatialWorkingMemory = this.calculateSpatialWorkingMemory(originalLocations.length, accuracy);
    this.data.spatialWorkingMemory.push({
      load: spatialWorkingMemory,
      performance: accuracy,
      itemCount: originalLocations.length,
      timestamp
    });

    // Memória de coordenadas
    const coordinateAnalysis = this.analyzeCoordinateMemory(originalLocations, userLocations);
    this.data.coordinateMemory.push(coordinateAnalysis);

    // Sequenciamento espacial
    const spatialSequencing = this.analyzeSpatialSequencing(originalLocations, userLocations);
    this.data.spatialSequencing.push(spatialSequencing);
  }

  analyzeSpatialMemory(original, response, gridSize) {
    if (!original || !response) return { accuracy: 0, errors: [] };

    const distances = [];
    const errors = [];
    let exactMatches = 0;

    // Analisar cada localização
    for (let i = 0; i < Math.max(original.length, response.length); i++) {
      const origPos = original[i];
      const respPos = response[i];

      if (origPos && respPos) {
        const distance = this.calculateSpatialDistance(origPos, respPos);
        distances.push(distance);

        if (distance === 0) {
          exactMatches++;
        } else {
          errors.push({
            index: i,
            original: origPos,
            response: respPos,
            distance,
            direction: this.calculateDirection(origPos, respPos),
            type: this.categorizeSpatialError(distance, gridSize)
          });
        }
      } else if (!respPos) {
        errors.push({
          index: i,
          original: origPos,
          response: null,
          type: 'omission'
        });
      } else if (!origPos) {
        errors.push({
          index: i,
          original: null,
          response: respPos,
          type: 'false_positive'
        });
      }
    }

    const averageDistance = distances.length > 0 ? distances.reduce((sum, d) => sum + d, 0) / distances.length : 0;

    return {
      exactMatches,
      totalItems: original.length,
      averageDistance,
      maxDistance: Math.max(...distances, 0),
      errors,
      spatialAccuracy: exactMatches / original.length,
      proximityScore: this.calculateProximityScore(distances, gridSize)
    };
  }

  calculateSpatialDistance(pos1, pos2) {
    if (!pos1 || !pos2) return Infinity;
    
    // Distância euclidiana
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    
    return Math.sqrt(dx * dx + dy * dy);
  }

  calculateDirection(from, to) {
    if (!from || !to) return null;

    const dx = to.x - from.x;
    const dy = to.y - from.y;

    // Calcular ângulo em graus
    const angle = Math.atan2(dy, dx) * (180 / Math.PI);
    
    // Converter para direções cardinais
    if (angle >= -22.5 && angle < 22.5) return 'east';
    if (angle >= 22.5 && angle < 67.5) return 'southeast';
    if (angle >= 67.5 && angle < 112.5) return 'south';
    if (angle >= 112.5 && angle < 157.5) return 'southwest';
    if (angle >= 157.5 || angle < -157.5) return 'west';
    if (angle >= -157.5 && angle < -112.5) return 'northwest';
    if (angle >= -112.5 && angle < -67.5) return 'north';
    if (angle >= -67.5 && angle < -22.5) return 'northeast';
    
    return 'unknown';
  }

  categorizeSpatialError(distance, gridSize) {
    const cellSize = 1; // Assumindo células de tamanho 1
    
    if (distance <= cellSize) return 'adjacent';
    if (distance <= cellSize * 2) return 'near';
    if (distance <= gridSize * 0.5) return 'moderate';
    return 'far';
  }

  calculateProximityScore(distances, gridSize) {
    if (distances.length === 0) return 0;

    const maxPossibleDistance = Math.sqrt(gridSize * gridSize * 2);
    const proximityScores = distances.map(d => Math.max(0, 1 - (d / maxPossibleDistance)));
    
    return proximityScores.reduce((sum, score) => sum + score, 0) / proximityScores.length;
  }

  analyzeSpatialDistortions(original, response, gridSize) {
    const distortions = {
      centerBias: 0,
      edgeBias: 0,
      horizontalBias: 0,
      verticalBias: 0,
      quadrantBias: { q1: 0, q2: 0, q3: 0, q4: 0 },
      scalingError: 0,
      rotationError: 0
    };

    const center = gridSize / 2;

    // Analisar cada resposta
    response.forEach((respPos, index) => {
      const origPos = original[index];
      if (!origPos || !respPos) return;

      // Bias central
      const origDistanceFromCenter = this.calculateSpatialDistance(origPos, { x: center, y: center });
      const respDistanceFromCenter = this.calculateSpatialDistance(respPos, { x: center, y: center });
      
      if (respDistanceFromCenter < origDistanceFromCenter) {
        distortions.centerBias++;
      }

      // Bias de borda
      const isOrigOnEdge = this.isOnEdge(origPos, gridSize);
      const isRespOnEdge = this.isOnEdge(respPos, gridSize);
      
      if (!isOrigOnEdge && isRespOnEdge) {
        distortions.edgeBias++;
      }

      // Bias horizontal/vertical
      const dx = respPos.x - origPos.x;
      const dy = respPos.y - origPos.y;
      
      if (Math.abs(dx) > Math.abs(dy)) {
        distortions.horizontalBias++;
      } else if (Math.abs(dy) > Math.abs(dx)) {
        distortions.verticalBias++;
      }

      // Bias de quadrante
      const quadrant = this.getQuadrant(respPos, center);
      distortions.quadrantBias[quadrant]++;
    });

    // Normalizar por número de itens
    const itemCount = response.length;
    Object.keys(distortions).forEach(key => {
      if (key !== 'quadrantBias') {
        distortions[key] = distortions[key] / itemCount;
      }
    });

    return distortions;
  }

  isOnEdge(pos, gridSize) {
    return pos.x === 0 || pos.x === gridSize - 1 || pos.y === 0 || pos.y === gridSize - 1;
  }

  getQuadrant(pos, center) {
    if (pos.x >= center && pos.y >= center) return 'q1';
    if (pos.x < center && pos.y >= center) return 'q2';
    if (pos.x < center && pos.y < center) return 'q3';
    return 'q4';
  }

  analyzeNavigationPattern(userLocations, originalLocations) {
    const pattern = {
      strategy: 'unknown',
      efficiency: 0,
      pathLength: 0,
      backtracking: 0,
      clustering: 0
    };

    if (userLocations.length < 2) return pattern;

    // Calcular comprimento do caminho
    let totalDistance = 0;
    for (let i = 1; i < userLocations.length; i++) {
      totalDistance += this.calculateSpatialDistance(userLocations[i - 1], userLocations[i]);
    }
    pattern.pathLength = totalDistance;

    // Detectar estratégia
    pattern.strategy = this.detectNavigationStrategy(userLocations, originalLocations);

    // Calcular eficiência
    const directDistance = this.calculateSpatialDistance(userLocations[0], userLocations[userLocations.length - 1]);
    pattern.efficiency = directDistance > 0 ? directDistance / totalDistance : 1;

    // Detectar retrocessos
    pattern.backtracking = this.detectBacktracking(userLocations);

    // Analisar clustering
    pattern.clustering = this.analyzeClustering(userLocations);

    return pattern;
  }

  detectNavigationStrategy(userPath, originalPath) {
    if (userPath.length < 3) return 'insufficient_data';

    // Estratégia sequencial (ordem original)
    const sequentialScore = this.calculateSequentialScore(userPath, originalPath);
    if (sequentialScore > 0.8) return 'sequential';

    // Estratégia espacial (proximidade)
    const spatialScore = this.calculateSpatialClusteringScore(userPath);
    if (spatialScore > 0.7) return 'spatial_clustering';

    // Estratégia de varredura
    const sweepScore = this.calculateSweepScore(userPath);
    if (sweepScore > 0.6) return 'systematic_sweep';

    return 'mixed_or_random';
  }

  calculateSequentialScore(userPath, originalPath) {
    let sequentialMatches = 0;
    const minLength = Math.min(userPath.length, originalPath.length);

    for (let i = 0; i < minLength; i++) {
      if (this.positionsEqual(userPath[i], originalPath[i])) {
        sequentialMatches++;
      }
    }

    return minLength > 0 ? sequentialMatches / minLength : 0;
  }

  positionsEqual(pos1, pos2) {
    return pos1 && pos2 && pos1.x === pos2.x && pos1.y === pos2.y;
  }

  calculateSpatialClusteringScore(path) {
    if (path.length < 3) return 0;

    let clusterTransitions = 0;
    let totalTransitions = path.length - 1;

    for (let i = 1; i < path.length; i++) {
      const distance = this.calculateSpatialDistance(path[i - 1], path[i]);
      if (distance <= 2) { // Adjacente ou próximo
        clusterTransitions++;
      }
    }

    return totalTransitions > 0 ? clusterTransitions / totalTransitions : 0;
  }

  calculateSweepScore(path) {
    if (path.length < 4) return 0;

    // Detectar movimento sistemático (horizontal ou vertical)
    let systematicMoves = 0;
    let totalMoves = path.length - 1;

    for (let i = 1; i < path.length; i++) {
      const dx = path[i].x - path[i - 1].x;
      const dy = path[i].y - path[i - 1].y;

      // Movimento principalmente em uma direção
      if (Math.abs(dx) > Math.abs(dy) || Math.abs(dy) > Math.abs(dx)) {
        systematicMoves++;
      }
    }

    return totalMoves > 0 ? systematicMoves / totalMoves : 0;
  }

  detectBacktracking(path) {
    let backtrackCount = 0;

    for (let i = 2; i < path.length; i++) {
      // Verificar se voltou para uma posição anterior
      for (let j = 0; j < i - 1; j++) {
        if (this.positionsEqual(path[i], path[j])) {
          backtrackCount++;
          break;
        }
      }
    }

    return path.length > 0 ? backtrackCount / path.length : 0;
  }

  analyzeClustering(path) {
    if (path.length < 3) return 0;

    const clusters = this.identifyClusters(path);
    const avgClusterSize = clusters.reduce((sum, cluster) => sum + cluster.length, 0) / clusters.length;
    
    // Score baseado na formação de clusters coesos
    return Math.min(1, avgClusterSize / 3);
  }

  identifyClusters(path, maxDistance = 2) {
    const clusters = [];
    const visited = new Set();

    path.forEach((pos, index) => {
      if (visited.has(index)) return;

      const cluster = [index];
      visited.add(index);

      // Encontrar posições próximas
      for (let i = index + 1; i < path.length; i++) {
        if (visited.has(i)) continue;

        const distance = this.calculateSpatialDistance(pos, path[i]);
        if (distance <= maxDistance) {
          cluster.push(i);
          visited.add(i);
        }
      }

      clusters.push(cluster);
    });

    return clusters;
  }

  calculateSpatialWorkingMemory(itemCount, accuracy) {
    // Carga baseada no número de itens e complexidade espacial
    const baseLoad = Math.min(100, itemCount * 15);
    const performanceModifier = (1 - accuracy) * 20;
    
    return Math.min(100, baseLoad + performanceModifier);
  }

  analyzeCoordinateMemory(original, response) {
    const xAccuracy = this.calculateCoordinateAccuracy(original, response, 'x');
    const yAccuracy = this.calculateCoordinateAccuracy(original, response, 'y');

    return {
      xAccuracy,
      yAccuracy,
      overallCoordinateAccuracy: (xAccuracy + yAccuracy) / 2,
      coordinateBias: this.detectCoordinateBias(original, response)
    };
  }

  calculateCoordinateAccuracy(original, response, axis) {
    let exactMatches = 0;
    let totalItems = 0;

    for (let i = 0; i < Math.min(original.length, response.length); i++) {
      if (original[i] && response[i]) {
        totalItems++;
        if (original[i][axis] === response[i][axis]) {
          exactMatches++;
        }
      }
    }

    return totalItems > 0 ? exactMatches / totalItems : 0;
  }

  detectCoordinateBias(original, response) {
    let xBias = 0;
    let yBias = 0;
    let count = 0;

    for (let i = 0; i < Math.min(original.length, response.length); i++) {
      if (original[i] && response[i]) {
        xBias += response[i].x - original[i].x;
        yBias += response[i].y - original[i].y;
        count++;
      }
    }

    return count > 0 ? {
      x: xBias / count,
      y: yBias / count,
      magnitude: Math.sqrt((xBias * xBias + yBias * yBias) / (count * count))
    } : { x: 0, y: 0, magnitude: 0 };
  }

  analyzeSpatialSequencing(original, response) {
    const sequencePreservation = this.calculateSpatialSequencePreservation(original, response);
    const spatialOrder = this.analyzeSpatialOrder(original, response);

    return {
      sequencePreservation,
      spatialOrder,
      combinedScore: (sequencePreservation + spatialOrder.accuracy) / 2
    };
  }

  calculateSpatialSequencePreservation(original, response) {
    if (original.length !== response.length) return 0;

    let preservedTransitions = 0;
    let totalTransitions = original.length - 1;

    for (let i = 0; i < totalTransitions; i++) {
      const origVector = {
        x: original[i + 1].x - original[i].x,
        y: original[i + 1].y - original[i].y
      };
      const respVector = {
        x: response[i + 1].x - response[i].x,
        y: response[i + 1].y - response[i].y
      };

      // Verificar se a direção do movimento foi preservada
      const vectorSimilarity = this.calculateVectorSimilarity(origVector, respVector);
      if (vectorSimilarity > 0.5) {
        preservedTransitions++;
      }
    }

    return totalTransitions > 0 ? preservedTransitions / totalTransitions : 0;
  }

  calculateVectorSimilarity(v1, v2) {
    const magnitude1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const magnitude2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);

    if (magnitude1 === 0 || magnitude2 === 0) return 0;

    const dotProduct = (v1.x * v2.x + v1.y * v2.y);
    return dotProduct / (magnitude1 * magnitude2);
  }

  analyzeSpatialOrder(original, response) {
    // Analisar se a ordem espacial (por exemplo, da esquerda para direita) foi preservada
    const originalOrder = this.getSpatialOrder(original);
    const responseOrder = this.getSpatialOrder(response);

    const orderSimilarity = this.calculateOrderSimilarity(originalOrder, responseOrder);

    return {
      accuracy: orderSimilarity,
      originalPattern: originalOrder,
      responsePattern: responseOrder
    };
  }

  getSpatialOrder(positions) {
    // Ordenar posições por coordenada x, depois y
    return positions
      .map((pos, index) => ({ pos, originalIndex: index }))
      .sort((a, b) => {
        if (a.pos.x !== b.pos.x) return a.pos.x - b.pos.x;
        return a.pos.y - b.pos.y;
      })
      .map(item => item.originalIndex);
  }

  calculateOrderSimilarity(order1, order2) {
    if (order1.length !== order2.length) return 0;

    let matches = 0;
    for (let i = 0; i < order1.length; i++) {
      if (order1[i] === order2[i]) matches++;
    }

    return matches / order1.length;
  }

  // Gerar relatório de análise espacial
  generateReport() {
    const totalAttempts = this.data.spatialMaps.length;
    if (totalAttempts === 0) return null;

    const avgAccuracy = this.data.locationAccuracy.reduce((sum, item) => sum + item.accuracy, 0) / totalAttempts;
    const avgDistance = this.data.locationAccuracy.reduce((sum, item) => sum + item.averageDistance, 0) / totalAttempts;

    // Análise de distorções
    const distortionAnalysis = this.calculateAverageDistortions();
    
    // Padrões de navegação
    const navigationAnalysis = this.calculateNavigationPatterns();

    return {
      collectorId: this.id,
      totalAttempts,
      avgAccuracy: Math.round(avgAccuracy * 100) / 100,
      avgSpatialDistance: Math.round(avgDistance * 100) / 100,
      spatialDistortions: distortionAnalysis,
      navigationPatterns: navigationAnalysis,
      recommendations: this.generateSpatialRecommendations(avgAccuracy, distortionAnalysis),
      cognitiveInsights: {
        spatialWorkingMemoryCapacity: this.calculateSpatialCapacity(),
        spatialPrecision: this.calculateSpatialPrecision(),
        navigationEfficiency: this.calculateNavigationEfficiency(),
        coordinateMemoryStrength: this.calculateCoordinateMemoryStrength()
      }
    };
  }

  calculateAverageDistortions() {
    if (this.data.spatialDistortions.length === 0) return null;

    const distortions = this.data.spatialDistortions;
    const avg = {
      centerBias: 0,
      edgeBias: 0,
      horizontalBias: 0,
      verticalBias: 0,
      quadrantBias: { q1: 0, q2: 0, q3: 0, q4: 0 }
    };

    distortions.forEach(d => {
      avg.centerBias += d.centerBias;
      avg.edgeBias += d.edgeBias;
      avg.horizontalBias += d.horizontalBias;
      avg.verticalBias += d.verticalBias;
      Object.keys(avg.quadrantBias).forEach(q => {
        avg.quadrantBias[q] += d.quadrantBias[q];
      });
    });

    const count = distortions.length;
    Object.keys(avg).forEach(key => {
      if (key !== 'quadrantBias') {
        avg[key] = avg[key] / count;
      } else {
        Object.keys(avg.quadrantBias).forEach(q => {
          avg.quadrantBias[q] = avg.quadrantBias[q] / count;
        });
      }
    });

    return avg;
  }

  calculateNavigationPatterns() {
    if (this.data.navigationPatterns.length === 0) return null;

    const patterns = this.data.navigationPatterns;
    const strategies = {};
    
    patterns.forEach(p => {
      strategies[p.strategy] = (strategies[p.strategy] || 0) + 1;
    });

    const avgEfficiency = patterns.reduce((sum, p) => sum + p.efficiency, 0) / patterns.length;
    const avgBacktracking = patterns.reduce((sum, p) => sum + p.backtracking, 0) / patterns.length;

    return {
      preferredStrategy: this.getMostFrequentStrategy(strategies),
      strategies,
      avgEfficiency,
      avgBacktracking
    };
  }

  getMostFrequentStrategy(strategies) {
    return Object.keys(strategies).reduce((a, b) => strategies[a] > strategies[b] ? a : b);
  }

  calculateSpatialCapacity() {
    if (this.data.spatialWorkingMemory.length === 0) return 50;

    const recent = this.data.spatialWorkingMemory.slice(-5);
    const maxItems = Math.max(...recent.map(item => item.itemCount));
    const avgPerformance = recent.reduce((sum, item) => sum + item.performance, 0) / recent.length;

    return Math.min(100, (maxItems * 12) + (avgPerformance * 40));
  }

  calculateSpatialPrecision() {
    if (this.data.locationAccuracy.length === 0) return 50;

    const recent = this.data.locationAccuracy.slice(-5);
    const avgDistance = recent.reduce((sum, item) => sum + item.averageDistance, 0) / recent.length;
    const avgExactMatches = recent.reduce((sum, item) => sum + (item.exactMatches / item.accuracy), 0) / recent.length;

    // Precisão baseada na distância média e matches exatos
    const distancePrecision = Math.max(0, 100 - (avgDistance * 20));
    const exactnessPrecision = avgExactMatches * 100;

    return (distancePrecision + exactnessPrecision) / 2;
  }

  calculateNavigationEfficiency() {
    if (this.data.navigationPatterns.length === 0) return 50;

    const recent = this.data.navigationPatterns.slice(-5);
    const avgEfficiency = recent.reduce((sum, item) => sum + item.efficiency, 0) / recent.length;

    return avgEfficiency * 100;
  }

  calculateCoordinateMemoryStrength() {
    if (this.data.coordinateMemory.length === 0) return 50;

    const recent = this.data.coordinateMemory.slice(-5);
    const avgCoordinateAccuracy = recent.reduce((sum, item) => sum + item.overallCoordinateAccuracy, 0) / recent.length;

    return avgCoordinateAccuracy * 100;
  }

  generateSpatialRecommendations(accuracy, distortions) {
    const recommendations = [];

    if (accuracy < 0.7) {
      recommendations.push('Praticar com grids menores');
    }

    if (distortions && distortions.centerBias > 0.3) {
      recommendations.push('Exercícios para reduzir bias central');
    }

    if (distortions && distortions.edgeBias > 0.3) {
      recommendations.push('Treinar localização em bordas');
    }

    return recommendations;
  }

  reset() {
    this.data = {
      spatialMaps: [],
      locationAccuracy: [],
      spatialDistortions: [],
      navigationPatterns: [],
      spatialWorkingMemory: [],
      coordinateMemory: [],
      spatialSequencing: [],
      mentalRotation: []
    };
  }
}

export default SpatialLocationCollector;
