/**
 * @file SequenceReproductionCollector.js
 * @description Coletor especializado para análise de reprodução de sequências visuais
 * @version 3.0.0
 */

export class SequenceReproductionCollector {
  constructor() {
    this.name = 'SequenceReproductionCollector';
    this.version = '3.0.0';
    this.description = 'Analisa habilidades de reprodução de sequências visuais';
  }

  async collect(gameState) {
    try {
      const sequenceData = gameState.sequenceToReproduce || [];
      const playerData = gameState.playerSequence || [];
      const timeData = gameState.responseTime || 0;

      return {
        // Métricas de capacidade sequencial
        sequenceLength: sequenceData.length,
        reproductionAccuracy: this.calculateReproductionAccuracy(sequenceData, playerData),
        visualMemorySpan: this.assessVisualMemorySpan(sequenceData.length, gameState.difficulty),
        sequentialProcessingSpeed: this.calculateProcessingSpeed(timeData, sequenceData.length),
        
        // Análise de erros
        errorPositions: this.analyzeErrorPositions(sequenceData, playerData),
        errorTypes: this.categorizeErrors(sequenceData, playerData),
        
        // Padrões de performance
        consistencyScore: this.assessConsistency(gameState.sessionAttempts || []),
        improvementTrend: this.calculateImprovementTrend(gameState.sessionAttempts || []),
        
        // Métricas terapêuticas
        memoryLoadCapacity: this.assessMemoryLoad(sequenceData.length),
        attentionSustainedScore: this.evaluateAttentionSustained(timeData),
        cognitiveFlexibilityIndex: this.assessCognitiveFlexibility(gameState),
        
        timestamp: Date.now(),
        sessionId: gameState.sessionId || 'unknown'
      };
    } catch (error) {
      console.error('Erro no SequenceReproductionCollector:', error);
      return null;
    }
  }

  calculateReproductionAccuracy(original, reproduction) {
    if (!original.length || !reproduction.length) return 0;
    
    let correct = 0;
    const maxLength = Math.max(original.length, reproduction.length);
    
    for (let i = 0; i < maxLength; i++) {
      const origElement = original[i];
      const reprElement = reproduction[i];
      
      if (origElement && reprElement) {
        if (origElement.shape === reprElement.shape && origElement.color === reprElement.color) {
          correct++;
        }
      }
    }
    
    return (correct / original.length) * 100;
  }

  assessVisualMemorySpan(sequenceLength, difficulty) {
    const baseScore = sequenceLength * 10;
    const difficultyMultiplier = {
      easy: 1.0,
      medium: 1.2,
      hard: 1.5
    };
    
    return Math.min(100, baseScore * (difficultyMultiplier[difficulty] || 1.0));
  }

  calculateProcessingSpeed(responseTime, sequenceLength) {
    const optimalTime = sequenceLength * 1000; // 1 segundo por elemento
    const speedRatio = optimalTime / Math.max(responseTime, 1000);
    return Math.min(100, speedRatio * 100);
  }

  analyzeErrorPositions(original, reproduction) {
    const errors = [];
    
    for (let i = 0; i < original.length; i++) {
      const origElement = original[i];
      const reprElement = reproduction[i];
      
      if (!reprElement || 
          origElement.shape !== reprElement.shape || 
          origElement.color !== reprElement.color) {
        errors.push({
          position: i,
          expected: origElement,
          actual: reprElement || null,
          errorType: this.categorizeError(origElement, reprElement)
        });
      }
    }
    
    return errors;
  }

  categorizeError(expected, actual) {
    if (!actual) return 'missing';
    if (expected.shape !== actual.shape && expected.color !== actual.color) return 'complete_mismatch';
    if (expected.shape !== actual.shape) return 'shape_error';
    if (expected.color !== actual.color) return 'color_error';
    return 'unknown';
  }

  categorizeErrors(original, reproduction) {
    const errors = this.analyzeErrorPositions(original, reproduction);
    const categories = {
      shape_errors: 0,
      color_errors: 0,
      position_errors: 0,
      missing_elements: 0,
      complete_mismatches: 0
    };

    errors.forEach(error => {
      switch(error.errorType) {
        case 'shape_error':
          categories.shape_errors++;
          break;
        case 'color_error':
          categories.color_errors++;
          break;
        case 'missing':
          categories.missing_elements++;
          break;
        case 'complete_mismatch':
          categories.complete_mismatches++;
          break;
      }
    });

    return categories;
  }

  assessConsistency(attempts) {
    if (attempts.length < 2) return 100;
    
    const accuracies = attempts.map(attempt => attempt.accuracy || 0);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Menor desvio padrão = maior consistência
    return Math.max(0, 100 - (standardDeviation * 2));
  }

  calculateImprovementTrend(attempts) {
    if (attempts.length < 3) return 0;
    
    const recentAttempts = attempts.slice(-5); // Últimas 5 tentativas
    const firstHalf = recentAttempts.slice(0, Math.floor(recentAttempts.length / 2));
    const secondHalf = recentAttempts.slice(Math.floor(recentAttempts.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, att) => sum + (att.accuracy || 0), 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, att) => sum + (att.accuracy || 0), 0) / secondHalf.length;
    
    return secondAvg - firstAvg; // Positivo = melhoria, negativo = declínio
  }

  assessMemoryLoad(sequenceLength) {
    // Avalia a carga de memória baseada no comprimento da sequência
    const memoryCapacity = 7; // Capacidade média de memória de trabalho (Miller's Law)
    const loadPercentage = (sequenceLength / memoryCapacity) * 100;
    return Math.min(100, loadPercentage);
  }

  evaluateAttentionSustained(responseTime) {
    // Avalia atenção sustentada baseada no tempo de resposta
    const optimalRange = { min: 3000, max: 15000 }; // 3-15 segundos é ideal
    
    if (responseTime < optimalRange.min) {
      return 70; // Muito rápido, pode indicar impulsividade
    } else if (responseTime > optimalRange.max) {
      return Math.max(30, 100 - ((responseTime - optimalRange.max) / 1000) * 5);
    } else {
      return 100; // Tempo ideal
    }
  }

  assessCognitiveFlexibility(gameState) {
    // Avalia flexibilidade cognitiva baseada na adaptação a diferentes padrões
    const attempts = gameState.sessionAttempts || [];
    if (attempts.length < 3) return 50;
    
    const patternTypes = new Set();
    attempts.forEach(attempt => {
      if (attempt.patternType) {
        patternTypes.add(attempt.patternType);
      }
    });
    
    // Mais tipos de padrões = maior flexibilidade
    return Math.min(100, (patternTypes.size / 5) * 100);
  }
}
