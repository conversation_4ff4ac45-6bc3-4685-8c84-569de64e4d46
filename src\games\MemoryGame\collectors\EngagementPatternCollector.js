/**
 * 🎯 ENGAGEMENT PATTERN COLLECTOR - Portal Betina V3
 * Coletor especializado em análise de padrões de engajamento e motivação
 * Monitora níveis de interesse, motivação e persistência
 */

export class EngagementPatternCollector {
  constructor() {
    this.name = 'EngagementPatternCollector';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    this.sessionData = {
      currentSession: null,
      engagementEvents: [],
      motivationIndicators: [],
      attentionMarkers: []
    };
    
    console.log('🎯 EngagementPatternCollector inicializado');
  }

  /**
   * Inicializar nova sessão de coleta
   */
  startSession(sessionId, gameData = {}) {
    this.sessionData.currentSession = {
      sessionId,
      startTime: Date.now(),
      difficulty: gameData.difficulty || 'unknown',
      theme: gameData.theme || 'unknown',
      totalCards: gameData.cardsCount || 0,
      expectedDuration: this.calculateExpectedDuration(gameData),
      engagementEvents: [],
      motivationTracker: {
        initialEnthusiasm: 1.0,
        currentMotivation: 1.0,
        persistenceLevel: 0,
        frustrationLevel: 0,
        satisfactionLevel: 0
      },
      attentionMetrics: {
        focusIntensity: 1.0,
        attentionSpan: 0,
        distractionEvents: 0,
        reengagementCount: 0,
        consistencyScore: 0
      },
      flowStateIndicators: {
        challengeSkillBalance: 0,
        timeDistortion: 0,
        intrinsicMotivation: 0,
        mergedAction: 0,
        clearGoals: 0
      },
      engagementPhases: []
    };
    
    console.log(`🎯 EngagementPatternCollector: Nova sessão iniciada - ${sessionId}`);
  }

  /**
   * Calcular duração esperada baseada na dificuldade
   */
  calculateExpectedDuration(gameData) {
    const baseTimes = {
      'easy': 60000,    // 1 minuto
      'medium': 120000, // 2 minutos
      'hard': 180000,   // 3 minutos
      'expert': 300000  // 5 minutos
    };
    
    return baseTimes[gameData.difficulty] || 120000;
  }

  /**
   * Registrar interação para análise de engajamento
   */
  recordInteraction(interactionData) {
    if (!this.sessionData.currentSession) {
      console.warn('🎯 EngagementPatternCollector: Sessão não iniciada');
      return;
    }

    const timestamp = Date.now();
    const session = this.sessionData.currentSession;
    
    const engagementEvent = {
      timestamp,
      cardId: interactionData.cardId,
      isMatch: interactionData.isMatch || false,
      reactionTime: interactionData.reactionTime || 0,
      sessionProgress: this.calculateSessionProgress(timestamp),
      motivationIndicators: this.detectMotivationIndicators(interactionData, timestamp),
      attentionSignals: this.detectAttentionSignals(interactionData, timestamp),
      engagementLevel: this.calculateCurrentEngagement(interactionData, timestamp),
      flowIndicators: this.detectFlowIndicators(interactionData, timestamp),
      emotionalState: this.assessEmotionalState(interactionData, timestamp)
    };

    // Adicionar ao histórico da sessão
    session.engagementEvents.push(engagementEvent);
    
    // Atualizar métricas de motivação
    this.updateMotivationMetrics(engagementEvent);
    
    // Atualizar métricas de atenção
    this.updateAttentionMetrics(engagementEvent);
    
    // Atualizar indicadores de flow
    this.updateFlowIndicators(engagementEvent);
    
    // Detectar mudanças de fase no engajamento
    this.detectEngagementPhaseChanges(engagementEvent);
    
    console.log('🎯 EngagementPatternCollector: Evento de engajamento registrado', {
      engagementLevel: engagementEvent.engagementLevel,
      motivationIndicators: engagementEvent.motivationIndicators,
      flowIndicators: engagementEvent.flowIndicators
    });
  }

  /**
   * Calcular progresso da sessão
   */
  calculateSessionProgress(timestamp) {
    const session = this.sessionData.currentSession;
    const elapsedTime = timestamp - session.startTime;
    
    return Math.min(1.0, elapsedTime / session.expectedDuration);
  }

  /**
   * Detectar indicadores de motivação
   */
  detectMotivationIndicators(interactionData, timestamp) {
    const indicators = [];
    const session = this.sessionData.currentSession;
    const recentEvents = session.engagementEvents.slice(-5);
    
    // Indicador de persistência
    if (this.showsPersistence(interactionData, recentEvents)) {
      indicators.push('persistence');
    }
    
    // Indicador de entusiasmo
    if (this.showsEnthusiasm(interactionData, recentEvents)) {
      indicators.push('enthusiasm');
    }
    
    // Indicador de determinação
    if (this.showsDetermination(interactionData, recentEvents)) {
      indicators.push('determination');
    }
    
    // Indicador de desengajamento
    if (this.showsDisengagement(interactionData, recentEvents)) {
      indicators.push('disengagement');
    }
    
    // Indicador de frustração
    if (this.showsFrustration(interactionData, recentEvents)) {
      indicators.push('frustration');
    }
    
    // Indicador de satisfação
    if (this.showsSatisfaction(interactionData, recentEvents)) {
      indicators.push('satisfaction');
    }
    
    return indicators;
  }

  /**
   * Verificar persistência
   */
  showsPersistence(interactionData, recentEvents) {
    // Persistência = continuar tentando após erros
    const recentErrors = recentEvents.filter(e => !e.isMatch).length;
    return recentErrors >= 2 && interactionData.reactionTime < 5000;
  }

  /**
   * Verificar entusiasmo
   */
  showsEnthusiasm(interactionData, recentEvents) {
    // Entusiasmo = tempos de reação rápidos e consistentes
    const avgReactionTime = recentEvents.length > 0 ? 
      recentEvents.reduce((sum, e) => sum + e.reactionTime, 0) / recentEvents.length : 0;
    
    return interactionData.reactionTime < 2000 && avgReactionTime < 3000;
  }

  /**
   * Verificar determinação
   */
  showsDetermination(interactionData, recentEvents) {
    // Determinação = melhoria após sequência de erros
    const last3 = recentEvents.slice(-3);
    const hadErrors = last3.filter(e => !e.isMatch).length >= 2;
    
    return hadErrors && interactionData.isMatch;
  }

  /**
   * Verificar desengajamento
   */
  showsDisengagement(interactionData, recentEvents) {
    // Desengajamento = tempos de reação aumentando
    if (recentEvents.length < 3) return false;
    
    const times = recentEvents.map(e => e.reactionTime);
    times.push(interactionData.reactionTime);
    
    // Verificar tendência crescente nos tempos
    let increasing = 0;
    for (let i = 1; i < times.length; i++) {
      if (times[i] > times[i - 1]) increasing++;
    }
    
    return increasing >= times.length * 0.7; // 70% das transições são crescentes
  }

  /**
   * Verificar frustração
   */
  showsFrustration(interactionData, recentEvents) {
    // Frustração = múltiplos erros recentes + tempo de reação alto
    const recentErrors = recentEvents.filter(e => !e.isMatch).length;
    
    return recentErrors >= 3 && interactionData.reactionTime > 4000;
  }

  /**
   * Verificar satisfação
   */
  showsSatisfaction(interactionData, recentEvents) {
    // Satisfação = match após sequência de tentativas
    const hadAttempts = recentEvents.length >= 2;
    
    return hadAttempts && interactionData.isMatch && interactionData.reactionTime < 3000;
  }

  /**
   * Detectar sinais de atenção
   */
  detectAttentionSignals(interactionData, timestamp) {
    const signals = [];
    const session = this.sessionData.currentSession;
    const recentEvents = session.engagementEvents.slice(-5);
    
    // Sinal de foco intenso
    if (this.showsIntenseFocus(interactionData, recentEvents)) {
      signals.push('intense_focus');
    }
    
    // Sinal de distração
    if (this.showsDistraction(interactionData, recentEvents)) {
      signals.push('distraction');
    }
    
    // Sinal de recuperação de foco
    if (this.showsFocusRecovery(interactionData, recentEvents)) {
      signals.push('focus_recovery');
    }
    
    // Sinal de atenção sustentada
    if (this.showsSustainedAttention(interactionData, recentEvents)) {
      signals.push('sustained_attention');
    }
    
    // Sinal de atenção seletiva
    if (this.showsSelectiveAttention(interactionData, recentEvents)) {
      signals.push('selective_attention');
    }
    
    return signals;
  }

  /**
   * Verificar foco intenso
   */
  showsIntenseFocus(interactionData, recentEvents) {
    // Foco intenso = tempos de reação consistentemente baixos
    const allEvents = [...recentEvents, { reactionTime: interactionData.reactionTime }];
    const avgTime = allEvents.reduce((sum, e) => sum + e.reactionTime, 0) / allEvents.length;
    const variance = allEvents.reduce((sum, e) => sum + (e.reactionTime - avgTime) ** 2, 0) / allEvents.length;
    
    return avgTime < 2500 && Math.sqrt(variance) < 500; // Baixo tempo e baixa variância
  }

  /**
   * Verificar distração
   */
  showsDistraction(interactionData, recentEvents) {
    // Distração = aumento súbito no tempo de reação
    if (recentEvents.length === 0) return false;
    
    const lastEvent = recentEvents[recentEvents.length - 1];
    return interactionData.reactionTime > lastEvent.reactionTime * 1.5 && 
           interactionData.reactionTime > 4000;
  }

  /**
   * Verificar recuperação de foco
   */
  showsFocusRecovery(interactionData, recentEvents) {
    // Recuperação = melhoria após distração
    if (recentEvents.length < 2) return false;
    
    const last2 = recentEvents.slice(-2);
    const hadDistraction = last2.some(e => e.attentionSignals?.includes('distraction'));
    
    return hadDistraction && interactionData.reactionTime < 2500;
  }

  /**
   * Verificar atenção sustentada
   */
  showsSustainedAttention(interactionData, recentEvents) {
    // Atenção sustentada = consistência ao longo do tempo
    const allEvents = [...recentEvents, { reactionTime: interactionData.reactionTime }];
    
    if (allEvents.length < 4) return false;
    
    const times = allEvents.map(e => e.reactionTime);
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    const allWithinRange = times.every(time => Math.abs(time - avgTime) < avgTime * 0.3);
    
    return allWithinRange && avgTime < 3500;
  }

  /**
   * Verificar atenção seletiva
   */
  showsSelectiveAttention(interactionData, recentEvents) {
    // Atenção seletiva = rapidez após análise
    return interactionData.isMatch && interactionData.reactionTime < 2000;
  }

  /**
   * Calcular engajamento atual
   */
  calculateCurrentEngagement(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    
    // Fatores que influenciam o engajamento
    const reactionTimeFactor = this.calculateReactionTimeFactor(interactionData.reactionTime);
    const successFactor = interactionData.isMatch ? 1.0 : 0.6;
    const persistenceFactor = this.calculatePersistenceFactor();
    const progressFactor = this.calculateProgressFactor(timestamp);
    
    const engagement = (
      reactionTimeFactor * 0.3 +
      successFactor * 0.2 +
      persistenceFactor * 0.3 +
      progressFactor * 0.2
    );
    
    return Math.max(0, Math.min(1, engagement));
  }

  /**
   * Calcular fator de tempo de reação
   */
  calculateReactionTimeFactor(reactionTime) {
    // Curva otimizada: muito rápido ou muito lento = menor engajamento
    const optimal = 2000; // 2 segundos é considerado ótimo
    const deviation = Math.abs(reactionTime - optimal);
    
    return Math.max(0.2, 1 - (deviation / 5000)); // Decai até 0.2
  }

  /**
   * Calcular fator de persistência
   */
  calculatePersistenceFactor() {
    const session = this.sessionData.currentSession;
    const recentEvents = session.engagementEvents.slice(-10);
    
    if (recentEvents.length === 0) return 1.0;
    
    const errorCount = recentEvents.filter(e => !e.isMatch).length;
    const continueAfterErrors = errorCount > 0 ? 1.0 : 0.8;
    
    return continueAfterErrors;
  }

  /**
   * Calcular fator de progresso
   */
  calculateProgressFactor(timestamp) {
    const progress = this.calculateSessionProgress(timestamp);
    
    // Curva de engajamento: alto no início e final, menor no meio
    if (progress < 0.3) return 1.0; // Alto no início
    if (progress > 0.8) return 0.9; // Alto no final
    
    return 0.7; // Menor no meio
  }

  /**
   * Detectar indicadores de flow
   */
  detectFlowIndicators(interactionData, timestamp) {
    const indicators = [];
    const session = this.sessionData.currentSession;
    
    // Equilíbrio desafio-habilidade
    if (this.hasOptimalChallenge(interactionData)) {
      indicators.push('optimal_challenge');
    }
    
    // Distorção temporal
    if (this.hasTimeDistortion(timestamp)) {
      indicators.push('time_distortion');
    }
    
    // Motivação intrínseca
    if (this.hasIntrinsicMotivation(interactionData)) {
      indicators.push('intrinsic_motivation');
    }
    
    // Ação e consciência fundidas
    if (this.hasMergedAction(interactionData)) {
      indicators.push('merged_action');
    }
    
    // Objetivos claros
    if (this.hasClearGoals(interactionData)) {
      indicators.push('clear_goals');
    }
    
    return indicators;
  }

  /**
   * Verificar desafio ótimo
   */
  hasOptimalChallenge(interactionData) {
    // Desafio ótimo = nem muito fácil nem muito difícil
    const reactionTime = interactionData.reactionTime;
    return reactionTime >= 1500 && reactionTime <= 4000 && interactionData.isMatch;
  }

  /**
   * Verificar distorção temporal
   */
  hasTimeDistortion(timestamp) {
    const session = this.sessionData.currentSession;
    const elapsedTime = timestamp - session.startTime;
    const expectedTime = session.engagementEvents.length * 3000; // 3s por movimento esperado
    
    // Distorção = tempo percebido diferente do esperado
    return Math.abs(elapsedTime - expectedTime) > expectedTime * 0.3;
  }

  /**
   * Verificar motivação intrínseca
   */
  hasIntrinsicMotivation(interactionData) {
    // Motivação intrínseca = engajamento mesmo após erros
    const session = this.sessionData.currentSession;
    const recentEvents = session.engagementEvents.slice(-5);
    const hadErrors = recentEvents.filter(e => !e.isMatch).length >= 2;
    
    return hadErrors && interactionData.reactionTime < 3000;
  }

  /**
   * Verificar ação fundida
   */
  hasMergedAction(interactionData) {
    // Ação fundida = resposta automática e fluida
    return interactionData.reactionTime < 1500 && interactionData.isMatch;
  }

  /**
   * Verificar objetivos claros
   */
  hasClearGoals(interactionData) {
    // Objetivos claros = comportamento focado e direcionado
    const session = this.sessionData.currentSession;
    const recentEvents = session.engagementEvents.slice(-3);
    
    // Verificar se há uma estratégia consistente
    const successRate = recentEvents.filter(e => e.isMatch).length / Math.max(recentEvents.length, 1);
    return successRate >= 0.6;
  }

  /**
   * Avaliar estado emocional
   */
  assessEmotionalState(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    const motivationIndicators = this.detectMotivationIndicators(interactionData, timestamp);
    
    // Mapear indicadores para estados emocionais
    let emotionalState = 'neutral';
    let intensity = 0.5;
    
    if (motivationIndicators.includes('satisfaction')) {
      emotionalState = 'positive';
      intensity = 0.8;
    } else if (motivationIndicators.includes('enthusiasm')) {
      emotionalState = 'excited';
      intensity = 0.9;
    } else if (motivationIndicators.includes('frustration')) {
      emotionalState = 'frustrated';
      intensity = 0.7;
    } else if (motivationIndicators.includes('disengagement')) {
      emotionalState = 'disengaged';
      intensity = 0.3;
    } else if (motivationIndicators.includes('determination')) {
      emotionalState = 'determined';
      intensity = 0.8;
    }
    
    return { state: emotionalState, intensity };
  }

  /**
   * Atualizar métricas de motivação
   */
  updateMotivationMetrics(engagementEvent) {
    const session = this.sessionData.currentSession;
    const tracker = session.motivationTracker;
    const indicators = engagementEvent.motivationIndicators;
    
    // Atualizar motivação atual
    if (indicators.includes('enthusiasm') || indicators.includes('satisfaction')) {
      tracker.currentMotivation = Math.min(1.0, tracker.currentMotivation + 0.1);
    } else if (indicators.includes('frustration') || indicators.includes('disengagement')) {
      tracker.currentMotivation = Math.max(0.1, tracker.currentMotivation - 0.15);
    }
    
    // Atualizar persistência
    if (indicators.includes('persistence') || indicators.includes('determination')) {
      tracker.persistenceLevel = Math.min(1.0, tracker.persistenceLevel + 0.2);
    }
    
    // Atualizar frustração
    if (indicators.includes('frustration')) {
      tracker.frustrationLevel = Math.min(1.0, tracker.frustrationLevel + 0.2);
    } else if (indicators.includes('satisfaction')) {
      tracker.frustrationLevel = Math.max(0, tracker.frustrationLevel - 0.1);
    }
    
    // Atualizar satisfação
    if (indicators.includes('satisfaction')) {
      tracker.satisfactionLevel = Math.min(1.0, tracker.satisfactionLevel + 0.15);
    }
  }

  /**
   * Atualizar métricas de atenção
   */
  updateAttentionMetrics(engagementEvent) {
    const session = this.sessionData.currentSession;
    const metrics = session.attentionMetrics;
    const signals = engagementEvent.attentionSignals;
    
    // Atualizar intensidade do foco
    if (signals.includes('intense_focus')) {
      metrics.focusIntensity = Math.min(1.0, metrics.focusIntensity + 0.1);
    } else if (signals.includes('distraction')) {
      metrics.focusIntensity = Math.max(0.1, metrics.focusIntensity - 0.2);
      metrics.distractionEvents++;
    }
    
    // Atualizar span de atenção
    if (signals.includes('sustained_attention')) {
      metrics.attentionSpan++;
    }
    
    // Atualizar reengajamento
    if (signals.includes('focus_recovery')) {
      metrics.reengagementCount++;
    }
    
    // Calcular consistência
    this.updateAttentionConsistency();
  }

  /**
   * Atualizar consistência da atenção
   */
  updateAttentionConsistency() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    if (events.length < 5) return;
    
    const recentEvents = events.slice(-10);
    const engagementLevels = recentEvents.map(e => e.engagementLevel);
    
    const avgEngagement = engagementLevels.reduce((a, b) => a + b, 0) / engagementLevels.length;
    const variance = engagementLevels.reduce((sum, level) => 
      sum + (level - avgEngagement) ** 2, 0
    ) / engagementLevels.length;
    
    session.attentionMetrics.consistencyScore = 1 - Math.min(1, Math.sqrt(variance));
  }

  /**
   * Atualizar indicadores de flow
   */
  updateFlowIndicators(engagementEvent) {
    const session = this.sessionData.currentSession;
    const indicators = session.flowStateIndicators;
    const flowIndicators = engagementEvent.flowIndicators;
    
    // Atualizar cada indicador
    if (flowIndicators.includes('optimal_challenge')) {
      indicators.challengeSkillBalance = Math.min(1.0, indicators.challengeSkillBalance + 0.1);
    }
    
    if (flowIndicators.includes('time_distortion')) {
      indicators.timeDistortion = Math.min(1.0, indicators.timeDistortion + 0.15);
    }
    
    if (flowIndicators.includes('intrinsic_motivation')) {
      indicators.intrinsicMotivation = Math.min(1.0, indicators.intrinsicMotivation + 0.1);
    }
    
    if (flowIndicators.includes('merged_action')) {
      indicators.mergedAction = Math.min(1.0, indicators.mergedAction + 0.15);
    }
    
    if (flowIndicators.includes('clear_goals')) {
      indicators.clearGoals = Math.min(1.0, indicators.clearGoals + 0.1);
    }
  }

  /**
   * Detectar mudanças de fase no engajamento
   */
  detectEngagementPhaseChanges(engagementEvent) {
    const session = this.sessionData.currentSession;
    const currentLevel = engagementEvent.engagementLevel;
    const phases = session.engagementPhases;
    
    // Primeira fase ou mudança significativa
    if (phases.length === 0 || 
        Math.abs(currentLevel - phases[phases.length - 1].averageEngagement) > 0.3) {
      
      phases.push({
        startTime: engagementEvent.timestamp,
        startEvent: session.engagementEvents.length,
        averageEngagement: currentLevel,
        dominantState: engagementEvent.emotionalState.state,
        phaseType: this.classifyEngagementPhase(currentLevel, engagementEvent)
      });
    }
  }

  /**
   * Classificar fase de engajamento
   */
  classifyEngagementPhase(engagementLevel, engagementEvent) {
    const indicators = engagementEvent.motivationIndicators;
    
    if (engagementLevel > 0.8) {
      if (indicators.includes('enthusiasm')) return 'high_enthusiasm';
      if (engagementEvent.flowIndicators.length >= 2) return 'flow_state';
      return 'high_engagement';
    } else if (engagementLevel > 0.6) {
      if (indicators.includes('determination')) return 'determined_effort';
      return 'moderate_engagement';
    } else if (engagementLevel > 0.4) {
      if (indicators.includes('frustration')) return 'struggling';
      return 'low_engagement';
    } else {
      return 'disengaged';
    }
  }

  /**
   * Análise completa dos dados coletados
   */
  async analyze(gameData = {}) {
    const session = this.sessionData.currentSession;
    if (!session) {
      return this.getEmptyAnalysis();
    }

    const analysis = {
      collectorName: this.name,
      version: this.version,
      sessionId: session.sessionId,
      timestamp: Date.now(),
      
      // Dados básicos da sessão
      sessionSummary: {
        totalEvents: session.engagementEvents.length,
        sessionDuration: Date.now() - session.startTime,
        difficulty: session.difficulty,
        theme: session.theme,
        completed: gameData.completed || false,
        expectedDuration: session.expectedDuration
      },
      
      // Análise de motivação
      motivationAnalysis: {
        initialEnthusiasm: session.motivationTracker.initialEnthusiasm,
        finalMotivation: session.motivationTracker.currentMotivation,
        averageMotivation: this.calculateAverageMotivation(),
        motivationTrend: this.analyzeMotivationTrend(),
        persistenceLevel: session.motivationTracker.persistenceLevel,
        frustrationLevel: session.motivationTracker.frustrationLevel,
        satisfactionLevel: session.motivationTracker.satisfactionLevel,
        motivationStability: this.calculateMotivationStability()
      },
      
      // Análise de atenção
      attentionAnalysis: {
        averageFocusIntensity: session.attentionMetrics.focusIntensity,
        attentionSpan: session.attentionMetrics.attentionSpan,
        distractionEvents: session.attentionMetrics.distractionEvents,
        reengagementCount: session.attentionMetrics.reengagementCount,
        consistencyScore: session.attentionMetrics.consistencyScore,
        attentionPatterns: this.analyzeAttentionPatterns(),
        sustainabilityScore: this.calculateAttentionSustainability()
      },
      
      // Análise de flow
      flowAnalysis: {
        flowStateIndicators: session.flowStateIndicators,
        overallFlowScore: this.calculateOverallFlowScore(),
        flowDuration: this.calculateFlowDuration(),
        flowFrequency: this.calculateFlowFrequency(),
        optimalExperienceLevel: this.calculateOptimalExperienceLevel()
      },
      
      // Análise de fases de engajamento
      engagementPhases: {
        totalPhases: session.engagementPhases.length,
        phases: session.engagementPhases,
        dominantPhase: this.identifyDominantPhase(),
        phaseTransitions: this.analyzePhaseTransitions(),
        engagementStability: this.calculateEngagementStability()
      },
      
      // Análise emocional
      emotionalAnalysis: {
        emotionalJourney: this.analyzeEmotionalJourney(),
        dominantEmotion: this.identifyDominantEmotion(),
        emotionalStability: this.calculateEmotionalStability(),
        positiveEmotionRatio: this.calculatePositiveEmotionRatio(),
        emotionalResilience: this.calculateEmotionalResilience()
      },
      
      // Recomendações terapêuticas
      therapeuticRecommendations: this.generateTherapeuticRecommendations(),
      
      // Pontuação de capacidades
      capacityScores: {
        sustainedAttention: this.calculateSustainedAttentionScore(),
        motivationalResilience: this.calculateMotivationalResilienceScore(),
        engagementCapacity: this.calculateEngagementCapacityScore(),
        flowPotential: this.calculateFlowPotentialScore()
      }
    };

    // Armazenar análise
    this.collectedData.push(analysis);
    
    console.log('🎯 EngagementPatternCollector: Análise completa gerada', {
      overallFlowScore: analysis.flowAnalysis.overallFlowScore,
      dominantPhase: analysis.engagementPhases.dominantPhase?.type,
      therapeuticRecommendations: analysis.therapeuticRecommendations.length
    });

    return analysis;
  }

  /**
   * Calcular motivação média
   */
  calculateAverageMotivation() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    if (events.length === 0) return session.motivationTracker.currentMotivation;
    
    const motivationLevels = events.map(e => e.engagementLevel);
    return motivationLevels.reduce((a, b) => a + b, 0) / motivationLevels.length;
  }

  /**
   * Analisar tendência de motivação
   */
  analyzeMotivationTrend() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    if (events.length < 5) {
      return { trend: 'insufficient_data', slope: 0 };
    }
    
    const engagementLevels = events.map(e => e.engagementLevel);
    const slope = this.calculateLinearSlope(engagementLevels);
    
    return {
      trend: slope > 0.05 ? 'increasing' : slope < -0.05 ? 'decreasing' : 'stable',
      slope,
      dataPoints: engagementLevels.length
    };
  }

  /**
   * Calcular inclinação linear
   */
  calculateLinearSlope(values) {
    const n = values.length;
    if (n < 2) return 0;
    
    const x = Array.from({ length: n }, (_, i) => i);
    const meanX = (n - 1) / 2;
    const meanY = values.reduce((a, b) => a + b, 0) / n;
    
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < n; i++) {
      numerator += (x[i] - meanX) * (values[i] - meanY);
      denominator += (x[i] - meanX) ** 2;
    }
    
    return denominator === 0 ? 0 : numerator / denominator;
  }

  /**
   * Calcular estabilidade da motivação
   */
  calculateMotivationStability() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    if (events.length < 3) return 0;
    
    const engagementLevels = events.map(e => e.engagementLevel);
    const avgEngagement = engagementLevels.reduce((a, b) => a + b, 0) / engagementLevels.length;
    const variance = engagementLevels.reduce((sum, level) => 
      sum + (level - avgEngagement) ** 2, 0
    ) / engagementLevels.length;
    
    return 1 - Math.min(1, Math.sqrt(variance));
  }

  /**
   * Analisar padrões de atenção
   */
  analyzeAttentionPatterns() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    const patterns = {
      sustainedFocus: 0,
      intermittentFocus: 0,
      distractedPeriods: 0,
      recoveryPatterns: 0
    };
    
    let consecutiveFocus = 0;
    let consecutiveDistraction = 0;
    
    events.forEach(event => {
      const hasIntenseFocus = event.attentionSignals.includes('intense_focus');
      const hasDistraction = event.attentionSignals.includes('distraction');
      const hasRecovery = event.attentionSignals.includes('focus_recovery');
      
      if (hasIntenseFocus) {
        consecutiveFocus++;
        consecutiveDistraction = 0;
      } else if (hasDistraction) {
        consecutiveDistraction++;
        consecutiveFocus = 0;
      }
      
      if (consecutiveFocus >= 3) patterns.sustainedFocus++;
      if (consecutiveDistraction >= 2) patterns.distractedPeriods++;
      if (hasRecovery) patterns.recoveryPatterns++;
    });
    
    patterns.intermittentFocus = events.length - patterns.sustainedFocus - patterns.distractedPeriods;
    
    return patterns;
  }

  /**
   * Calcular sustentabilidade da atenção
   */
  calculateAttentionSustainability() {
    const session = this.sessionData.currentSession;
    const metrics = session.attentionMetrics;
    
    const sustainabilityFactor = metrics.consistencyScore * 0.4 +
                                (1 - (metrics.distractionEvents / Math.max(session.engagementEvents.length, 1))) * 0.3 +
                                (metrics.reengagementCount / Math.max(metrics.distractionEvents, 1)) * 0.3;
    
    return Math.max(0, Math.min(1, sustainabilityFactor));
  }

  /**
   * Calcular pontuação geral de flow
   */
  calculateOverallFlowScore() {
    const session = this.sessionData.currentSession;
    const indicators = session.flowStateIndicators;
    
    return (
      indicators.challengeSkillBalance * 0.25 +
      indicators.timeDistortion * 0.15 +
      indicators.intrinsicMotivation * 0.25 +
      indicators.mergedAction * 0.2 +
      indicators.clearGoals * 0.15
    );
  }

  /**
   * Calcular duração do flow
   */
  calculateFlowDuration() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    let flowDuration = 0;
    let inFlowState = false;
    let flowStart = 0;
    
    events.forEach(event => {
      const hasFlowIndicators = event.flowIndicators.length >= 2;
      
      if (hasFlowIndicators && !inFlowState) {
        inFlowState = true;
        flowStart = event.timestamp;
      } else if (!hasFlowIndicators && inFlowState) {
        inFlowState = false;
        flowDuration += event.timestamp - flowStart;
      }
    });
    
    // Se ainda estiver em flow no final
    if (inFlowState && events.length > 0) {
      flowDuration += events[events.length - 1].timestamp - flowStart;
    }
    
    return flowDuration;
  }

  /**
   * Calcular frequência de flow
   */
  calculateFlowFrequency() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    if (events.length === 0) return 0;
    
    const flowEvents = events.filter(e => e.flowIndicators.length >= 2);
    return flowEvents.length / events.length;
  }

  /**
   * Calcular nível de experiência ótima
   */
  calculateOptimalExperienceLevel() {
    const flowScore = this.calculateOverallFlowScore();
    const motivationLevel = this.calculateAverageMotivation();
    const attentionLevel = this.sessionData.currentSession.attentionMetrics.focusIntensity;
    
    return (flowScore * 0.5 + motivationLevel * 0.3 + attentionLevel * 0.2);
  }

  /**
   * Identificar fase dominante
   */
  identifyDominantPhase() {
    const session = this.sessionData.currentSession;
    const phases = session.engagementPhases;
    
    if (phases.length === 0) return null;
    
    // Encontrar fase com maior duração ou engajamento
    let dominantPhase = phases[0];
    let maxScore = 0;
    
    phases.forEach(phase => {
      const duration = this.calculatePhaseDuration(phase);
      const score = phase.averageEngagement * duration;
      
      if (score > maxScore) {
        maxScore = score;
        dominantPhase = phase;
      }
    });
    
    return {
      type: dominantPhase.phaseType,
      averageEngagement: dominantPhase.averageEngagement,
      dominantState: dominantPhase.dominantState
    };
  }

  /**
   * Calcular duração de uma fase
   */
  calculatePhaseDuration(phase) {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    const phaseEvents = events.slice(phase.startEvent);
    if (phaseEvents.length === 0) return 0;
    
    const endTime = phaseEvents[phaseEvents.length - 1].timestamp;
    return endTime - phase.startTime;
  }

  /**
   * Analisar transições de fase
   */
  analyzePhaseTransitions() {
    const session = this.sessionData.currentSession;
    const phases = session.engagementPhases;
    
    if (phases.length < 2) {
      return { transitions: 0, pattern: 'stable', volatility: 0 };
    }
    
    const transitions = [];
    for (let i = 1; i < phases.length; i++) {
      const prev = phases[i - 1];
      const curr = phases[i];
      
      transitions.push({
        from: prev.phaseType,
        to: curr.phaseType,
        engagementChange: curr.averageEngagement - prev.averageEngagement
      });
    }
    
    const volatility = this.calculatePhaseVolatility(phases);
    
    return {
      transitions: transitions.length,
      pattern: this.identifyTransitionPattern(transitions),
      volatility,
      details: transitions
    };
  }

  /**
   * Calcular volatilidade das fases
   */
  calculatePhaseVolatility(phases) {
    if (phases.length < 2) return 0;
    
    const engagementLevels = phases.map(p => p.averageEngagement);
    const avgEngagement = engagementLevels.reduce((a, b) => a + b, 0) / engagementLevels.length;
    const variance = engagementLevels.reduce((sum, level) => 
      sum + (level - avgEngagement) ** 2, 0
    ) / engagementLevels.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Identificar padrão de transição
   */
  identifyTransitionPattern(transitions) {
    if (transitions.length === 0) return 'stable';
    
    const positiveChanges = transitions.filter(t => t.engagementChange > 0.1).length;
    const negativeChanges = transitions.filter(t => t.engagementChange < -0.1).length;
    
    if (positiveChanges > negativeChanges * 2) return 'improving';
    if (negativeChanges > positiveChanges * 2) return 'declining';
    
    return 'fluctuating';
  }

  /**
   * Calcular estabilidade do engajamento
   */
  calculateEngagementStability() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    if (events.length < 3) return 0;
    
    const engagementLevels = events.map(e => e.engagementLevel);
    return this.calculateMotivationStability(); // Usa mesmo cálculo
  }

  /**
   * Analisar jornada emocional
   */
  analyzeEmotionalJourney() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    const journey = events.map(event => ({
      timestamp: event.timestamp,
      state: event.emotionalState.state,
      intensity: event.emotionalState.intensity,
      progress: event.sessionProgress
    }));
    
    return {
      journey,
      totalStates: new Set(journey.map(j => j.state)).size,
      averageIntensity: journey.reduce((sum, j) => sum + j.intensity, 0) / journey.length
    };
  }

  /**
   * Identificar emoção dominante
   */
  identifyDominantEmotion() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    const emotionCounts = {};
    let totalIntensity = 0;
    
    events.forEach(event => {
      const state = event.emotionalState.state;
      const intensity = event.emotionalState.intensity;
      
      emotionCounts[state] = (emotionCounts[state] || 0) + intensity;
      totalIntensity += intensity;
    });
    
    const dominantEmotion = Object.entries(emotionCounts)
      .sort(([_a, a], [_b, b]) => b - a)[0];
    
    if (!dominantEmotion) return { emotion: 'neutral', strength: 0 };
    
    return {
      emotion: dominantEmotion[0],
      strength: dominantEmotion[1] / totalIntensity
    };
  }

  /**
   * Calcular estabilidade emocional
   */
  calculateEmotionalStability() {
    const journey = this.analyzeEmotionalJourney();
    
    if (journey.journey.length < 3) return 0;
    
    const intensities = journey.journey.map(j => j.intensity);
    const avgIntensity = intensities.reduce((a, b) => a + b, 0) / intensities.length;
    const variance = intensities.reduce((sum, intensity) => 
      sum + (intensity - avgIntensity) ** 2, 0
    ) / intensities.length;
    
    return 1 - Math.min(1, Math.sqrt(variance));
  }

  /**
   * Calcular razão de emoções positivas
   */
  calculatePositiveEmotionRatio() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    if (events.length === 0) return 0;
    
    const positiveStates = ['positive', 'excited', 'determined'];
    const positiveEvents = events.filter(e => 
      positiveStates.includes(e.emotionalState.state)
    );
    
    return positiveEvents.length / events.length;
  }

  /**
   * Calcular resiliência emocional
   */
  calculateEmotionalResilience() {
    const session = this.sessionData.currentSession;
    const events = session.engagementEvents;
    
    let resilienceScore = 0;
    let negativeEvents = 0;
    
    for (let i = 0; i < events.length - 1; i++) {
      const current = events[i];
      const next = events[i + 1];
      
      if (current.emotionalState.state === 'frustrated' || 
          current.emotionalState.state === 'disengaged') {
        negativeEvents++;
        
        // Verificar recuperação
        if (next.emotionalState.state === 'positive' || 
            next.emotionalState.state === 'determined') {
          resilienceScore++;
        }
      }
    }
    
    return negativeEvents > 0 ? resilienceScore / negativeEvents : 1;
  }

  /**
   * Calcular pontuações de capacidade
   */
  calculateSustainedAttentionScore() {
    const sustainability = this.calculateAttentionSustainability();
    const consistency = this.sessionData.currentSession.attentionMetrics.consistencyScore;
    
    return Math.min(100, Math.round((sustainability * 60 + consistency * 40) * 100));
  }

  calculateMotivationalResilienceScore() {
    const resilience = this.calculateEmotionalResilience();
    const stability = this.calculateMotivationStability();
    
    return Math.min(100, Math.round((resilience * 70 + stability * 30) * 100));
  }

  calculateEngagementCapacityScore() {
    const avgMotivation = this.calculateAverageMotivation();
    const engagementStability = this.calculateEngagementStability();
    
    return Math.min(100, Math.round((avgMotivation * 60 + engagementStability * 40) * 100));
  }

  calculateFlowPotentialScore() {
    const flowScore = this.calculateOverallFlowScore();
    const flowFrequency = this.calculateFlowFrequency();
    
    return Math.min(100, Math.round((flowScore * 70 + flowFrequency * 30) * 100));
  }

  /**
   * Gerar recomendações terapêuticas
   */
  generateTherapeuticRecommendations() {
    const recommendations = [];
    const session = this.sessionData.currentSession;
    
    // Recomendações baseadas na motivação
    if (session.motivationTracker.currentMotivation < 0.4) {
      recommendations.push({
        area: 'motivation_enhancement',
        priority: 'high',
        title: 'Fortalecer Motivação',
        description: 'Baixos níveis de motivação detectados.',
        activities: [
          'Estabelecimento de objetivos claros e alcançáveis',
          'Sistema de recompensas graduais',
          'Técnicas de autorregulação motivacional'
        ]
      });
    }
    
    // Recomendações baseadas na atenção
    if (session.attentionMetrics.distractionEvents > session.engagementEvents.length * 0.3) {
      recommendations.push({
        area: 'attention_training',
        priority: 'medium',
        title: 'Treinar Atenção Sustentada',
        description: 'Múltiplos eventos de distração identificados.',
        activities: [
          'Exercícios de mindfulness',
          'Treinamento de foco atencional',
          'Técnicas de concentração progressiva'
        ]
      });
    }
    
    // Recomendações baseadas no flow
    const flowScore = this.calculateOverallFlowScore();
    if (flowScore < 0.4) {
      recommendations.push({
        area: 'flow_development',
        priority: 'medium',
        title: 'Desenvolver Estado de Flow',
        description: 'Baixa ocorrência de estados de flow.',
        activities: [
          'Ajuste do nível de desafio',
          'Desenvolvimento de habilidades específicas',
          'Técnicas de imersão total'
        ]
      });
    }
    
    return recommendations;
  }

  /**
   * Obter análise vazia
   */
  getEmptyAnalysis() {
    return {
      collectorName: this.name,
      version: this.version,
      sessionId: null,
      timestamp: Date.now(),
      error: 'No session data available',
      motivationAnalysis: null,
      attentionAnalysis: null,
      flowAnalysis: null,
      engagementPhases: null,
      emotionalAnalysis: null,
      therapeuticRecommendations: [],
      capacityScores: {
        sustainedAttention: 0,
        motivationalResilience: 0,
        engagementCapacity: 0,
        flowPotential: 0
      }
    };
  }

  /**
   * Finalizar sessão
   */
  endSession() {
    if (this.sessionData.currentSession) {
      console.log(`🎯 EngagementPatternCollector: Sessão finalizada - ${this.sessionData.currentSession.sessionId}`);
      this.sessionData.currentSession = null;
    }
  }

  /**
   * Obter dados coletados
   */
  getCollectedData() {
    return {
      collectorName: this.name,
      version: this.version,
      isActive: this.isActive,
      totalSessions: this.collectedData.length,
      lastSession: this.sessionData.currentSession,
      collectedData: this.collectedData
    };
  }

  /**
   * Limpar dados
   */
  clearData() {
    this.collectedData = [];
    this.sessionData = {
      currentSession: null,
      engagementEvents: [],
      motivationIndicators: [],
      attentionMarkers: []
    };
    console.log('🎯 EngagementPatternCollector: Dados limpos');
  }
}
