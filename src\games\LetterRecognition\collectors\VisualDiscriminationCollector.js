/**
 * 📝 VISUAL DISCRIMINATION COLLECTOR V3
 * Coletor especializado para atividade de discriminação visual
 * Portal Betina V3
 */

export class VisualDiscriminationCollector {
  constructor() {
    this.name = 'VisualDiscriminationCollector';
    this.version = '3.0.0';
    this.isActive = true;
    this.collectedData = [];
  }

  async collect(data) {
    try {
      const timestamp = new Date().toISOString();
      
      const analysisData = {
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: 'visual_discrimination',
        
        // Dados visuais
        targetLetter: data.targetLetter,
        similarLetters: data.similarLetters,
        selectedLetter: data.selectedLetter,
        expectedLetter: data.expectedLetter,
        isCorrect: data.selectedLetter === data.expectedLetter,
        responseTime: data.responseTime || 0,
        
        // Análise visual
        visualSimilarity: this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter),
        discriminationDifficulty: this.assessDiscriminationDifficulty(data),
        visualComplexity: this.assessVisualComplexity(data.targetLetter),
        
        // Métricas de processamento visual
        visualProcessingTime: data.behavioralMetrics?.visualProcessingTime || data.responseTime,
        visualAttention: this.assessVisualAttention(data),
        perceptualAccuracy: this.assessPerceptualAccuracy(data),
        visualMemory: this.assessVisualMemory(data),
        
        // Habilidades específicas
        featureDetection: this.assessFeatureDetection(data),
        spatialOrientation: this.assessSpatialOrientation(data),
        visualScanning: this.assessVisualScanning(data),
        
        // Padrões de erro
        errorType: this.classifyVisualError(data),
        errorAnalysis: this.analyzeVisualError(data)
      };
      
      this.collectedData.push(analysisData);
      return analysisData;
      
    } catch (error) {
      console.error('Erro no VisualDiscriminationCollector:', error);
      return null;
    }
  }

  calculateVisualSimilarity(letter1, letter2) {
    if (!letter1 || !letter2) return 0.5;
    if (letter1 === letter2) return 1.0;
    
    // Mapeamento de características visuais das letras
    const visualFeatures = {
      'b': { curves: 1, lines: 1, orientation: 'right', openings: 1, symmetry: 0 },
      'd': { curves: 1, lines: 1, orientation: 'left', openings: 1, symmetry: 0 },
      'p': { curves: 1, lines: 1, orientation: 'right', openings: 1, symmetry: 0 },
      'q': { curves: 1, lines: 1, orientation: 'left', openings: 1, symmetry: 0 },
      'n': { curves: 1, lines: 2, orientation: 'none', openings: 0, symmetry: 0 },
      'u': { curves: 1, lines: 2, orientation: 'none', openings: 1, symmetry: 1 },
      'm': { curves: 2, lines: 3, orientation: 'none', openings: 0, symmetry: 1 },
      'w': { curves: 2, lines: 4, orientation: 'none', openings: 0, symmetry: 1 },
      'a': { curves: 1, lines: 2, orientation: 'none', openings: 1, symmetry: 0 },
      'e': { curves: 1, lines: 3, orientation: 'none', openings: 1, symmetry: 0 },
      'o': { curves: 1, lines: 0, orientation: 'none', openings: 1, symmetry: 1 },
      'c': { curves: 1, lines: 0, orientation: 'none', openings: 1, symmetry: 0 },
      'i': { curves: 0, lines: 1, orientation: 'none', openings: 0, symmetry: 1 },
      'l': { curves: 0, lines: 1, orientation: 'none', openings: 0, symmetry: 1 }
    };
    
    const feat1 = visualFeatures[letter1.toLowerCase()] || { curves: 0.5, lines: 1, orientation: 'none', openings: 0.5, symmetry: 0.5 };
    const feat2 = visualFeatures[letter2.toLowerCase()] || { curves: 0.5, lines: 1, orientation: 'none', openings: 0.5, symmetry: 0.5 };
    
    let similarity = 0;
    
    // Similaridade de curvas (peso 0.3)
    const curveDiff = Math.abs(feat1.curves - feat2.curves);
    similarity += (1 - Math.min(1, curveDiff / 2)) * 0.3;
    
    // Similaridade de linhas (peso 0.2)
    const lineDiff = Math.abs(feat1.lines - feat2.lines);
    similarity += (1 - Math.min(1, lineDiff / 3)) * 0.2;
    
    // Orientação (peso 0.3)
    if (feat1.orientation === feat2.orientation) similarity += 0.3;
    else if ((feat1.orientation === 'left' && feat2.orientation === 'right') || 
             (feat1.orientation === 'right' && feat2.orientation === 'left')) {
      similarity += 0.1; // Orientações opostas têm alguma similaridade
    }
    
    // Aberturas (peso 0.1)
    const openingDiff = Math.abs(feat1.openings - feat2.openings);
    similarity += (1 - Math.min(1, openingDiff)) * 0.1;
    
    // Simetria (peso 0.1)
    if (feat1.symmetry === feat2.symmetry) similarity += 0.1;
    
    return Math.min(1.0, similarity);
  }

  assessVisualComplexity(letter) {
    if (!letter) return 0.5;
    
    // Complexidade baseada em características visuais
    const complexityMap = {
      // Letras simples
      'i': 0.1, 'l': 0.1, 'o': 0.2, 'c': 0.2,
      
      // Letras intermediárias
      'u': 0.3, 'n': 0.3, 'a': 0.4, 'e': 0.4,
      
      // Letras com orientação específica
      'b': 0.5, 'd': 0.5, 'p': 0.5, 'q': 0.5,
      
      // Letras complexas
      'm': 0.7, 'w': 0.8, 'g': 0.8, 'f': 0.9
    };
    
    return complexityMap[letter.toLowerCase()] || 0.5;
  }

  assessDiscriminationDifficulty(data) {
    let difficulty = 0.4;
    
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    
    // Maior similaridade visual = maior dificuldade
    difficulty += visualSimilarity * 0.4;
    
    const complexity = this.assessVisualComplexity(data.targetLetter);
    difficulty += complexity * 0.2;
    
    return Math.min(1.0, difficulty);
  }

  assessVisualAttention(data) {
    let attention = 0.6;
    
    if (data.selectedLetter === data.expectedLetter) {
      attention += 0.3;
      
      // Bônus baseado na dificuldade da discriminação
      const difficulty = this.assessDiscriminationDifficulty(data);
      if (difficulty > 0.7) attention += 0.1;
    }
    
    const responseTime = data.responseTime || 0;
    if (responseTime < 2000) attention += 0.1;
    else if (responseTime > 6000) attention -= 0.2;
    
    return Math.max(0.0, Math.min(1.0, attention));
  }

  assessPerceptualAccuracy(data) {
    let accuracy = 0.5;
    
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    const isCorrect = data.selectedLetter === data.expectedLetter;
    
    if (isCorrect) {
      accuracy += 0.4;
      
      // Bônus para discriminações difíceis (letras similares)
      if (visualSimilarity > 0.7) accuracy += 0.1;
    } else {
      // Penalidade baseada na facilidade da discriminação
      if (visualSimilarity < 0.3) accuracy -= 0.3;
    }
    
    return Math.max(0.0, Math.min(1.0, accuracy));
  }

  assessVisualMemory(data) {
    let memory = 0.6;
    
    // Memória visual baseada no tempo de resposta e precisão
    if (data.selectedLetter === data.expectedLetter) {
      memory += 0.3;
      
      const responseTime = data.responseTime || 0;
      if (responseTime < 2500) memory += 0.1; // Memória rápida
    }
    
    return Math.min(1.0, memory);
  }

  assessFeatureDetection(data) {
    let detection = 0.6;
    
    if (data.selectedLetter === data.expectedLetter) {
      detection += 0.3;
      
      const complexity = this.assessVisualComplexity(data.targetLetter);
      if (complexity > 0.6) detection += 0.1; // Detectou características complexas
    }
    
    return Math.min(1.0, detection);
  }

  assessSpatialOrientation(data) {
    let orientation = 0.6;
    
    // Verifica se o erro foi de orientação (b/d, p/q)
    const orientationPairs = [
      ['b', 'd'], ['p', 'q'], ['u', 'n'], ['m', 'w']
    ];
    
    const isOrientationPair = orientationPairs.some(pair => 
      (pair.includes(data.targetLetter?.toLowerCase()) && 
       pair.includes(data.selectedLetter?.toLowerCase()))
    );
    
    if (data.selectedLetter === data.expectedLetter) {
      orientation += 0.3;
      
      if (isOrientationPair) orientation += 0.1; // Acertou orientação difícil
    } else if (isOrientationPair) {
      orientation -= 0.2; // Erro de orientação
    }
    
    return Math.max(0.0, Math.min(1.0, orientation));
  }

  assessVisualScanning(data) {
    let scanning = 0.6;
    
    const responseTime = data.responseTime || 0;
    const similarLettersCount = data.similarLetters?.length || 1;
    
    if (data.selectedLetter === data.expectedLetter) {
      scanning += 0.3;
      
      // Bônus para escaneamento eficiente com muitas opções
      if (similarLettersCount > 3 && responseTime < 4000) {
        scanning += 0.1;
      }
    }
    
    // Penalidade por escaneamento muito lento
    if (responseTime > 8000) scanning -= 0.2;
    
    return Math.max(0.0, Math.min(1.0, scanning));
  }

  classifyVisualError(data) {
    if (data.selectedLetter === data.expectedLetter) return 'no_error';
    
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    
    // Verifica se é erro de orientação
    const orientationPairs = [
      ['b', 'd'], ['p', 'q'], ['u', 'n'], ['m', 'w']
    ];
    
    const isOrientationError = orientationPairs.some(pair => 
      (pair.includes(data.targetLetter?.toLowerCase()) && 
       pair.includes(data.selectedLetter?.toLowerCase()))
    );
    
    if (isOrientationError) return 'orientation_error';
    if (visualSimilarity > 0.7) return 'high_similarity_confusion';
    if (visualSimilarity > 0.4) return 'moderate_similarity_confusion';
    if (visualSimilarity < 0.2) return 'random_selection';
    
    return 'low_similarity_confusion';
  }

  analyzeVisualError(data) {
    if (data.selectedLetter === data.expectedLetter) return null;
    
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    const difficulty = this.assessDiscriminationDifficulty(data);
    
    return {
      errorType: this.classifyVisualError(data),
      visualSimilarity,
      discriminationDifficulty: difficulty,
      visualComplexity: this.assessVisualComplexity(data.targetLetter),
      possibleCauses: this.identifyVisualErrorCauses(data)
    };
  }

  identifyVisualErrorCauses(data) {
    const causes = [];
    
    const responseTime = data.responseTime || 0;
    if (responseTime < 1000) causes.push('impulsive_response');
    if (responseTime > 7000) causes.push('visual_processing_difficulty');
    
    const visualSimilarity = this.calculateVisualSimilarity(data.targetLetter, data.selectedLetter);
    if (visualSimilarity > 0.7) causes.push('high_visual_similarity');
    
    const errorType = this.classifyVisualError(data);
    if (errorType === 'orientation_error') causes.push('spatial_orientation_difficulty');
    
    const complexity = this.assessVisualComplexity(data.targetLetter);
    if (complexity > 0.7) causes.push('complex_visual_features');
    
    return causes;
  }

  generateSummary() {
    if (this.collectedData.length === 0) return null;
    
    const total = this.collectedData.length;
    const correct = this.collectedData.filter(d => d.isCorrect).length;
    const accuracy = correct / total;
    
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / total;
    const avgVisualSimilarity = this.collectedData.reduce((sum, d) => sum + d.visualSimilarity, 0) / total;
    const avgAttention = this.collectedData.reduce((sum, d) => sum + d.visualAttention, 0) / total;
    
    // Análise por tipo de erro
    const errorTypes = {};
    this.collectedData.forEach(d => {
      if (!errorTypes[d.errorType]) errorTypes[d.errorType] = 0;
      errorTypes[d.errorType]++;
    });
    
    // Performance por complexidade visual
    const complexityPerformance = {
      'simple': this.getPerformanceByComplexity(0, 0.3),
      'moderate': this.getPerformanceByComplexity(0.3, 0.6),
      'complex': this.getPerformanceByComplexity(0.6, 1.0)
    };
    
    return {
      collector: this.name,
      version: this.version,
      total,
      accuracy,
      avgResponseTime,
      avgVisualSimilarity,
      avgVisualAttention: avgAttention,
      errorTypes,
      complexityPerformance,
      recommendations: this.generateRecommendations()
    };
  }

  getPerformanceByComplexity(minComp, maxComp) {
    const data = this.collectedData.filter(d => 
      d.visualComplexity >= minComp && d.visualComplexity < maxComp
    );
    
    if (data.length === 0) return null;
    
    return {
      count: data.length,
      accuracy: data.filter(d => d.isCorrect).length / data.length,
      avgTime: data.reduce((sum, d) => sum + d.responseTime, 0) / data.length
    };
  }

  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    
    const recommendations = [];
    
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: 'visual_discrimination_training',
        priority: 'high',
        message: 'Pratique discriminação visual com letras simples.'
      });
    }
    
    // Recomendações baseadas em tipos de erro
    const orientationErrors = summary.errorTypes.orientation_error || 0;
    if (orientationErrors > summary.total * 0.3) {
      recommendations.push({
        type: 'orientation_training',
        priority: 'high',
        message: 'Foque em exercícios de orientação espacial (b/d, p/q).'
      });
    }
    
    if (summary.avgResponseTime > 6000) {
      recommendations.push({
        type: 'visual_processing_speed',
        priority: 'medium',
        message: 'Pratique reconhecimento visual rápido.'
      });
    }
    
    return recommendations;
  }

  reset() {
    this.collectedData = [];
  }

  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: new Date().toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
