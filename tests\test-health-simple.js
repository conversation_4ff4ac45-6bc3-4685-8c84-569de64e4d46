/**
 * Teste simples de Health Checks
 */

import { getSystemOrchestrator } from './src/api/services/core/SystemOrchestrator.js';
import { getHealthCheckService } from './src/api/services/core/health/HealthCheckService.js';

async function testHealthChecks() {
  console.log('🏥 Iniciando teste de Health Checks...\n');

  try {
    // Obter instâncias dos serviços
    const systemOrchestrator = getSystemOrchestrator();
    const healthService = getHealthCheckService();

    // Aguardar estabilização
    console.log('⏳ Aguardando estabilização dos componentes...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n📊 Status Geral dos Componentes:');
    console.log('================================');

    // Testar componentes individuais
    const components = [
      'system_orchestrator',
      'ai_brain', 
      'intelligent_cache',
      'behavioral_analyzer',
      'cognitive_analyzer',
      'therapeutic_analyzer',
      'progress_analyzer',
      'database'
    ];

    const results = {};

    for (const component of components) {
      try {
        const status = await healthService.checkComponent(component);
        const statusValue = status.status || 'unknown';
        const statusIcon = statusValue === 'healthy' ? '✅' : 
                          statusValue === 'degraded' ? '⚠️' : '❌';
        
        console.log(`${statusIcon} ${component}: ${statusValue.toUpperCase()}`);
        
        if (status.metrics) {
          console.log(`   📈 Response Time: ${status.metrics.responseTime || 'N/A'}ms`);
          if (status.metrics.successRate) {
            console.log(`   📊 Success Rate: ${(status.metrics.successRate * 100).toFixed(1)}%`);
          }
        }
        
        if (status.details && status.status !== 'healthy') {
          console.log(`   🔍 Details: ${status.details}`);
        }
        
        results[component] = status;
        
      } catch (error) {
        console.log(`❌ ${component}: ERRO - ${error.message}`);
        results[component] = { status: 'error', error: error.message };
      }
    }

    console.log('\n🌐 Status Geral do Sistema:');
    console.log('===========================');
    
    try {
      const overallHealth = await healthService.getOverallHealth();
      const overallStatus = overallHealth.status || overallHealth.overall || 'unknown';
      const healthScore = overallHealth.score || overallHealth.healthScore || 0;
      const systemUptime = overallHealth.uptime || overallHealth.systemMetrics?.uptime || 0;
      
      console.log(`📋 Status Geral: ${overallStatus.toUpperCase()}`);
      console.log(`📊 Score de Saúde: ${healthScore}/100`);
      console.log(`⚡ Uptime: ${Math.round(systemUptime / 1000)}s`);
      
      if (overallHealth.alerts && overallHealth.alerts.length > 0) {
        console.log('\n🚨 Alertas Críticos:');
        overallHealth.alerts.forEach(alert => {
          console.log(`   ⚠️  ${alert.component}: ${alert.message}`);
        });
      }
      
    } catch (error) {
      console.log(`❌ Erro ao obter status geral: ${error.message}`);
    }

    console.log('\n📈 Métricas dos Componentes:');
    console.log('============================');

    // Obter métricas detalhadas
    try {
      if (systemOrchestrator.getSystemMetrics) {
        const systemMetrics = systemOrchestrator.getSystemMetrics();
        console.log(`🎯 Total de análises: ${systemMetrics.totalAnalyses || 0}`);
        console.log(`✅ Análises bem-sucedidas: ${systemMetrics.successfulAnalyses || 0}`);
        console.log(`❌ Análises falhadas: ${systemMetrics.failedAnalyses || 0}`);
        
        if (systemMetrics.successfulAnalyses > 0) {
          const successRate = ((systemMetrics.successfulAnalyses / systemMetrics.totalAnalyses) * 100).toFixed(1);
          console.log(`📊 Taxa de sucesso: ${successRate}%`);
        }
      }
    } catch (error) {
      console.log(`⚠️  Erro ao obter métricas do sistema: ${error.message}`);
    }

    // Resumo final
    const healthyCount = Object.values(results).filter(r => r.status === 'healthy').length;
    const totalCount = Object.keys(results).length;
    
    console.log('\n📋 Resumo Final:');
    console.log('================');
    console.log(`✅ Componentes saudáveis: ${healthyCount}/${totalCount}`);
    console.log(`📊 Taxa de saúde: ${((healthyCount / totalCount) * 100).toFixed(1)}%`);
    
    if (healthyCount === totalCount) {
      console.log('🎉 Todos os componentes estão funcionando corretamente!');
    } else {
      console.log('⚠️  Alguns componentes precisam de atenção.');
    }

  } catch (error) {
    console.error('❌ Erro crítico no teste de health checks:', error);
  }
}

// Executar teste
testHealthChecks()
  .then(() => {
    console.log('\n✅ Teste de Health Checks concluído');
    process.exit(0);
  })
  .catch(error => {
    console.error('\n❌ Erro durante o teste:', error);
    process.exit(1);
  });
