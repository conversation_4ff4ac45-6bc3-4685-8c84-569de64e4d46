/**
 * @file gameSessionModel.js
 * @description Modelo para sessões de jogos
 */

import { pool } from '../../database/config/pool.js';

/**
 * Classe para gerenciar sessões de jogos
 */
export class GameSessionModel {
  /**
   * Buscar sessões por usuário
   * @param {string} userId - ID do usuário
   * @returns {Promise<Array>} Lista de sessões
   */
  static async findByUserId(userId) {
    try {
      const client = await pool.connect();
      const result = await client.query(
        'SELECT * FROM game_sessions WHERE user_id = $1 ORDER BY created_at DESC',
        [userId]
      );
      client.release();
      return result.rows;
    } catch (error) {
      console.error('Erro ao buscar sessões por usuário:', error);
      throw error;
    }
  }

  /**
   * Buscar sessão por ID
   * @param {string} sessionId - ID da sessão
   * @returns {Promise<Object>} Dados da sessão
   */
  static async findById(sessionId) {
    try {
      const client = await pool.connect();
      const result = await client.query(
        'SELECT * FROM game_sessions WHERE id = $1',
        [sessionId]
      );
      client.release();
      return result.rows[0];
    } catch (error) {
      console.error('Erro ao buscar sessão por ID:', error);
      throw error;
    }
  }

  /**
   * Criar nova sessão
   * @param {Object} sessionData - Dados da sessão
   * @returns {Promise<Object>} Sessão criada
   */
  static async create(sessionData) {
    try {
      const client = await pool.connect();
      const result = await client.query(
        `INSERT INTO game_sessions (user_id, game_type, start_time, end_time, score, metrics, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW()) RETURNING *`,
        [
          sessionData.userId,
          sessionData.gameType,
          sessionData.startTime,
          sessionData.endTime,
          sessionData.score,
          JSON.stringify(sessionData.metrics)
        ]
      );
      client.release();
      return result.rows[0];
    } catch (error) {
      console.error('Erro ao criar sessão:', error);
      throw error;
    }
  }

  /**
   * Atualizar sessão
   * @param {string} sessionId - ID da sessão
   * @param {Object} updateData - Dados para atualizar
   * @returns {Promise<Object>} Sessão atualizada
   */
  static async update(sessionId, updateData) {
    try {
      const client = await pool.connect();
      const result = await client.query(
        `UPDATE game_sessions 
         SET end_time = $2, score = $3, metrics = $4, updated_at = NOW()
         WHERE id = $1 RETURNING *`,
        [
          sessionId,
          updateData.endTime,
          updateData.score,
          JSON.stringify(updateData.metrics)
        ]
      );
      client.release();
      return result.rows[0];
    } catch (error) {
      console.error('Erro ao atualizar sessão:', error);
      throw error;
    }
  }

  /**
   * Buscar estatísticas por usuário
   * @param {string} userId - ID do usuário
   * @returns {Promise<Object>} Estatísticas
   */
  static async getStatsByUserId(userId) {
    try {
      const client = await pool.connect();
      const result = await client.query(
        `SELECT 
          COUNT(*) as total_sessions,
          AVG(score) as avg_score,
          MAX(score) as best_score,
          AVG(EXTRACT(EPOCH FROM (end_time - start_time))) as avg_duration
         FROM game_sessions 
         WHERE user_id = $1 AND end_time IS NOT NULL`,
        [userId]
      );
      client.release();
      return result.rows[0];
    } catch (error) {
      console.error('Erro ao buscar estatísticas:', error);
      throw error;
    }
  }
}

export default GameSessionModel;
