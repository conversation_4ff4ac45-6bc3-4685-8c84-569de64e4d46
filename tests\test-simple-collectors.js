/**
 * 🧪 TESTE SIMPLES DOS COLETORES
 * Teste básico para identificar problemas
 */

console.log('🚀 Iniciando teste simples...');

try {
  console.log('📦 Testando imports...');
  
  // Test ColorMatch
  console.log('🎨 Importando ColorMatch...');
  const { ColorMatchCollectorsHub } = await import('./src/games/ColorMatch/collectors/index.js');
  console.log('✅ ColorMatch importado');
  const colorHub = new ColorMatchCollectorsHub();
  console.log('✅ ColorMatch hub criado');
  
  // Test QuebraCabeca
  console.log('🧩 Importando QuebraCabeca...');
  const { QuebraCabecaCollectorsHub } = await import('./src/games/QuebraCabeca/collectors/index.js');
  console.log('✅ QuebraCabeca importado');
  const puzzleHub = new QuebraCabecaCollectorsHub();
  console.log('✅ QuebraCabeca hub criado');
  
  // Test CreativePainting
  console.log('🎨 Importando CreativePainting...');
  const { CreativePaintingCollectorsHub } = await import('./src/games/CreativePainting/collectors/index.js');
  console.log('✅ CreativePainting importado');
  const paintingHub = new CreativePaintingCollectorsHub();
  console.log('✅ CreativePainting hub criado');
  
  console.log('🎉 Teste simples concluído com sucesso!');
  
} catch (error) {
  console.error('❌ Erro no teste simples:', error.message);
  console.error('Stack:', error.stack);
}
