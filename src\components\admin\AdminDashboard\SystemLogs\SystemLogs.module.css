/**
 * 📋 SYSTEM LOGS V3 - UI/UX MODERNO
 * @file SystemLogs.module.css
 * @description Visualizador de Logs com design futurístico
 * @version 3.0.0
 * @features Glassmorphism, Organized layout, Real-time updates
 */

/* ===== VARIÁVEIS LOCAIS ===== */
:root {
  --logs-primary: #6366f1;
  --logs-success: #10b981;
  --logs-warning: #f59e0b;
  --logs-error: #ef4444;
  --logs-info: #3b82f6;

  --logs-bg-primary: #0f172a;
  --logs-bg-secondary: #1e293b;
  --logs-bg-glass: rgba(255, 255, 255, 0.1);

  --logs-text-primary: #f8fafc;
  --logs-text-secondary: #cbd5e1;
  --logs-text-muted: #94a3b8;

  --logs-border-radius: 16px;
  --logs-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== CONTAINER PRINCIPAL ===== */
.systemLogs {
  padding: 0;
  max-width: 100%;
  margin: 0;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HEADER ORGANIZADO ===== */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 20px;
  padding: 24px 32px;
  background: var(--logs-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--logs-border-radius);
  box-shadow: var(--logs-shadow);
}

.title {
  color: var(--logs-text-primary);
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title::before {
  content: '📋';
  font-size: 28px;
}

.controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filterLabel {
  font-size: 14px;
  color: var(--logs-text-secondary);
  font-weight: 500;
  white-space: nowrap;
}

.filterSelect {
  padding: 10px 16px;
  background: var(--logs-bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 14px;
  color: var(--logs-text-primary);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.filterSelect:focus {
  outline: none;
  border-color: var(--logs-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filterSelect option {
  background: var(--logs-bg-secondary);
  color: var(--logs-text-primary);
}

.searchInput {
  padding: 10px 16px;
  background: var(--logs-bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  font-size: 14px;
  color: var(--logs-text-primary);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  min-width: 240px;
}

.searchInput::placeholder {
  color: var(--logs-text-muted);
}

.searchInput:focus {
  outline: none;
  border-color: var(--logs-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

/* ===== CONTROLES MODERNOS ===== */
.autoRefreshToggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggleSwitch {
  position: relative;
  width: 48px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.toggleSwitch.active {
  background: var(--logs-primary);
  box-shadow: 0 0 12px rgba(99, 102, 241, 0.3);
}

.toggleSlider {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.toggleSwitch.active .toggleSlider {
  transform: translateX(24px);
}

.refreshButton {
  background: linear-gradient(135deg, var(--logs-info), #2563eb);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.refreshButton::before {
  content: '🔄';
  font-size: 16px;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.refreshButton:disabled {
  background: rgba(148, 163, 184, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.clearButton {
  background: linear-gradient(135deg, var(--logs-error), #dc2626);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.clearButton::before {
  content: '🗑️';
  font-size: 16px;
}

.clearButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

/* ===== BARRA DE ESTATÍSTICAS HORIZONTAL ===== */
.statsBar {
  background: var(--logs-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--logs-border-radius);
  padding: 20px 32px;
  margin: 0 32px 24px 32px;
  box-shadow: var(--logs-shadow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
}

.statItem {
  text-align: center;
  min-width: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.statValue {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 2px;
  line-height: 1;
}

.statLabel {
  font-size: 12px;
  color: var(--logs-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1;
}

.statError {
  color: var(--logs-error);
}

.statWarning {
  color: var(--logs-warning);
}

.statInfo {
  color: var(--logs-info);
}

.statSuccess {
  color: var(--logs-success);
}

.statDebug {
  color: #6c757d;
}

.statLabel {
  font-size: 0.8rem;
  color: #666;
  text-transform: uppercase;
}

.logsContainer {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.logsHeader {
  background: #f8f9fa;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logsTitle {
  font-weight: 600;
  color: #333;
  margin: 0;
}

.logsCount {
  font-size: 0.9rem;
  color: #666;
}

.logsList {
  max-height: 600px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
}

.logEntry {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f1f3f4;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  transition: background-color 0.2s;
}

.logEntry:hover {
  background: #f8f9fa;
}

.logEntry:last-child {
  border-bottom: none;
}

.logLevel {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  min-width: 60px;
  text-align: center;
  white-space: nowrap;
}

.levelError {
  background: #f8d7da;
  color: #721c24;
}

.levelWarning {
  background: #fff3cd;
  color: #856404;
}

.levelInfo {
  background: #d1ecf1;
  color: #0c5460;
}

.levelDebug {
  background: #d1d3d4;
  color: #383d41;
}

.logTimestamp {
  color: #666;
  min-width: 140px;
  white-space: nowrap;
}

.logService {
  color: #007bff;
  font-weight: 500;
  min-width: 100px;
  white-space: nowrap;
}

.logMessage {
  flex: 1;
  color: #333;
  word-break: break-word;
}

.logDetails {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #666;
  white-space: pre-wrap;
}

.logStackTrace {
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: #ffe6e6;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #dc3545;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
}

.noLogs {
  text-align: center;
  padding: 3rem;
  color: #666;
  font-size: 1.1rem;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.exportButton {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.exportButton:hover {
  background: #1e7e34;
}

/* Responsividade */
@media (max-width: 1024px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .controls {
    justify-content: space-between;
  }

  .logEntry {
    flex-direction: column;
    gap: 0.5rem;
  }

  .logTimestamp,
  .logService {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .statsBar {
    flex-direction: column;
  }

  .controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .filterGroup {
    width: 100%;
    justify-content: space-between;
  }

  .filterSelect,
  .searchInput {
    min-width: auto;
    flex: 1;
  }

  .logsContainer {
    font-size: 0.8rem;
  }

  .logLevel {
    min-width: 50px;
    font-size: 0.7rem;
  }

  .metricsGrid,
  .systemMetricsGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Seções de Métricas do Prometheus */
.prometheusSection {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.prometheusSection h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metricCard {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.metricTitle {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  font-weight: 500;
}

.metricValue {
  font-size: 1.5rem;
  font-weight: bold;
}

.alertsSection h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.alertsList {
  background: #f8f9fa;
  border-radius: 6px;
  overflow: hidden;
}

.alertItem {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.alertItem:last-child {
  border-bottom: none;
}

.alertWarning {
  border-left: 4px solid #ffc107;
  background: #fff8e1;
}

.alertError {
  border-left: 4px solid #dc3545;
  background: #ffebee;
}

.alertInfo {
  border-left: 4px solid #17a2b8;
  background: #e3f2fd;
}

.alertIcon {
  font-size: 1.2rem;
  margin-top: 0.1rem;
}

.alertContent {
  flex: 1;
}

.alertMessage {
  font-weight: 500;
  color: #333;
  margin-bottom: 0.25rem;
}

.alertTime {
  font-size: 0.85rem;
  color: #666;
}

/* Seção de Métricas do Sistema */
.systemMetricsSection {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.systemMetricsSection h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.systemMetricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.systemMetricCard {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
}

.headerActions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.autoRefreshLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  cursor: pointer;
}

.filters {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  align-items: center;
}

.searchBox {
  flex: 1;
  min-width: 250px;
}
