/**
 * 💾 DATABASE CONNECTION POOL
 * Pool de conexões PostgreSQL
 * Portal Betina V3
 */

import pg from 'pg';
import databaseConfig from './database.js';

const { Pool } = pg;

// Criar pool de conexões usando a configuração
const pool = new Pool({
  host: databaseConfig.connection.host,
  port: databaseConfig.connection.port,
  user: databaseConfig.connection.user,
  password: databaseConfig.connection.password,
  database: databaseConfig.connection.database,
  ssl: databaseConfig.connection.ssl,
  application_name: databaseConfig.connection.application_name,
  keepAlive: databaseConfig.connection.keepAlive,
  keepAliveInitialDelayMillis: databaseConfig.connection.keepAliveInitialDelayMillis,
  
  // Configurações do pool
  min: databaseConfig.pool.min,
  max: databaseConfig.pool.max,
  createTimeoutMillis: databaseConfig.pool.createTimeoutMillis,
  acquireTimeoutMillis: databaseConfig.pool.acquireTimeoutMillis,
  idleTimeoutMillis: databaseConfig.pool.idleTimeoutMillis,
  reapIntervalMillis: databaseConfig.pool.reapIntervalMillis,
  createRetryIntervalMillis: databaseConfig.pool.createRetryIntervalMillis,
  propagateCreateError: databaseConfig.pool.propagateCreateError
});

// Event listeners para monitoramento
pool.on('connect', (client) => {
  if (databaseConfig.logging.enabled) {
    console.log('✅ Nova conexão estabelecida com o banco de dados');
  }
});

pool.on('error', (err, client) => {
  console.error('❌ Erro inesperado no pool de conexões:', err);
});

pool.on('remove', (client) => {
  if (databaseConfig.logging.enabled && databaseConfig.logging.level === 'debug') {
    console.log('🔄 Cliente removido do pool');
  }
});

// Health check do pool
async function checkPoolHealth() {
  try {
    const client = await pool.connect();
    await client.query(databaseConfig.healthCheck.query);
    client.release();
    
    if (databaseConfig.logging.enabled && databaseConfig.logging.level === 'debug') {
      console.log('💚 Pool de conexões saudável');
    }
    return true;
  } catch (error) {
    console.error('❌ Health check do pool falhou:', error.message);
    return false;
  }
}

// Executar health check inicial
if (databaseConfig.healthCheck.enabled) {
  checkPoolHealth().then((healthy) => {
    if (healthy) {
      console.log(`✅ Pool de conexões inicializado com sucesso (${databaseConfig.pool.min}-${databaseConfig.pool.max} conexões)`);
    }
  });
  
  // Configurar health check periódico
  setInterval(checkPoolHealth, databaseConfig.healthCheck.interval);
}

// Função para finalizar o pool graciosamente
async function closePool() {
  try {
    await pool.end();
    console.log('✅ Pool de conexões finalizado com sucesso');
  } catch (error) {
    console.error('❌ Erro ao finalizar pool:', error.message);
  }
}

/**
 * Testa a conexão com o banco de dados
 */
export const testConnection = async () => {
  let client = null;
  
  try {
    console.log('🔍 Testando conexão com banco de dados...');
    client = await pool.connect();
    
    const result = await client.query('SELECT NOW() as current_time, version() as postgres_version');
    
    console.log('✅ Conexão com banco de dados bem-sucedida!');
    console.log('📅 Horário do servidor:', result.rows[0].current_time);
    console.log('🗄️ Versão PostgreSQL:', result.rows[0].postgres_version.split(' ')[0]);
    
    return true;
  } catch (error) {
    console.error('❌ Erro ao testar conexão:', error.message);
    return false;
  } finally {
    if (client) {
      client.release();
    }
  }
};

/**
 * Inicializa o banco de dados com retry automático
 */
export const initializeDatabase = async (retries = 3) => {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      console.log(`🚀 Tentativa ${attempt}/${retries} de inicialização do banco...`);
      
      const isConnected = await testConnection();
      
      if (isConnected) {
        console.log('✅ Banco de dados inicializado com sucesso!');
        return pool;
      } else {
        throw new Error('Falha na conexão');
      }
    } catch (error) {
      console.error(`❌ Tentativa ${attempt} falhou:`, error.message);
      
      if (attempt === retries) {
        throw new Error(`Falha ao inicializar banco após ${retries} tentativas: ${error.message}`);
      }
      
      // Aguardar antes da próxima tentativa
      const delay = 5000 * attempt;
      console.log(`⏳ Aguardando ${delay}ms antes da próxima tentativa...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

// Interceptar sinais de finalização
process.on('SIGINT', closePool);
process.on('SIGTERM', closePool);

export { pool, checkPoolHealth, closePool, testConnection };
export default pool;
