// ============================================================================
// IMAGE ASSOCIATION V3 - CONFIGURAÇÃO COMPLETA
// Sistema Avançado de Análise de Associação Conceitual e Processamento Semântico
// ============================================================================

export const ImageAssociationV3Config = {
  // Identificação do jogo
  gameId: 'imageAssociation',
  version: '3.0.0',
  title: 'Image Association V3',
  description: 'Sistema Avançado de Análise de Associação Conceitual e Processamento Semântico',

  // Configurações de sessão
  session: {
    duration: 900000, // 15 minutos
    activitiesCount: 6,
    cyclesPerActivity: 3,
    adaptiveDifficulty: true,
    multiSensoryEnabled: true
  },

  // Parâmetros terapêuticos
  therapeutic: {
    cognitiveTargets: [
      'associativeThinking',
      'visualMemory', 
      'categorization',
      'semanticProcessing',
      'conceptualFlexibility',
      'emotionalIntelligence'
    ],
    difficultyProgression: 'adaptive',
    reinforcementSchedule: 'variable',
    errorAnalysisEnabled: true
  },

  // Configuração das 6 atividades especializadas
  activities: {
    // 1. ASSOCIAÇÃO SIMPLES
    simpleAssociation: {
      id: 'simpleAssociation',
      name: 'Associação Simples',
      icon: '🔗',
      description: 'Conecte duas imagens que possuem relação direta e óbvia',
      
      difficulty: {
        beginner: {
          pairs: 3,
          timeLimit: 60000,
          hintsEnabled: true,
          visualCues: true
        },
        intermediate: {
          pairs: 4,
          timeLimit: 45000,
          hintsEnabled: true,
          visualCues: false
        },
        advanced: {
          pairs: 6,
          timeLimit: 30000,
          hintsEnabled: false,
          visualCues: false
        }
      },

      stimuli: {
        pairs: [
          { id: 1, primary: '🐕', secondary: '🦴', relationship: 'animal-food', difficulty: 1 },
          { id: 2, primary: '🔑', secondary: '🔒', relationship: 'tool-object', difficulty: 1 },
          { id: 3, primary: '☀️', secondary: '🌡️', relationship: 'cause-effect', difficulty: 2 },
          { id: 4, primary: '🌧️', secondary: '☂️', relationship: 'problem-solution', difficulty: 2 },
          { id: 5, primary: '📱', secondary: '🔌', relationship: 'device-accessory', difficulty: 3 },
          { id: 6, primary: '👁️', secondary: '👓', relationship: 'organ-aid', difficulty: 3 },
          { id: 7, primary: '🏠', secondary: '🔑', relationship: 'location-access', difficulty: 2 },
          { id: 8, primary: '🚗', secondary: '⛽', relationship: 'vehicle-fuel', difficulty: 2 }
        ]
      },

      scoring: {
        correctMatch: 100,
        incorrectMatch: -20,
        timeBonus: 50,
        hintPenalty: -10,
        perfectRound: 200
      }
    },

    // 2. ASSOCIAÇÃO CATEGÓRICA
    categoricalAssociation: {
      id: 'categoricalAssociation',
      name: 'Associação Categórica',
      icon: '🧩',
      description: 'Agrupe imagens por categorias semânticas',

      difficulty: {
        beginner: {
          categories: 3,
          itemsPerCategory: 3,
          timeLimit: 90000,
          showCategoryLabels: true
        },
        intermediate: {
          categories: 4,
          itemsPerCategory: 4,
          timeLimit: 75000,
          showCategoryLabels: true
        },
        advanced: {
          categories: 5,
          itemsPerCategory: 4,
          timeLimit: 60000,
          showCategoryLabels: false
        }
      },

      categories: {
        animals: {
          label: 'Animais',
          icon: '🐾',
          items: ['🐱', '🐸', '🦋', '🐧', '🦎', '🐰', '🦔', '🐨'],
          color: '#28a745'
        },
        fruits: {
          label: 'Frutas',
          icon: '🍓',
          items: ['🍎', '🍌', '🍇', '🍊', '🥝', '🍑', '🥭', '🍍'],
          color: '#fd7e14'
        },
        vehicles: {
          label: 'Veículos',
          icon: '🚙',
          items: ['🚗', '✈️', '🚲', '🚁', '🛵', '🚢', '🚆', '🚌'],
          color: '#007bff'
        },
        tools: {
          label: 'Ferramentas',
          icon: '🔧',
          items: ['🔨', '🪚', '🔧', '✂️', '📏', '🖊️', '🖌️', '📐'],
          color: '#6f42c1'
        },
        emotions: {
          label: 'Emoções',
          icon: '😊',
          items: ['😊', '😢', '😡', '😴', '😱', '🤔', '😍', '🤨'],
          color: '#e83e8c'
        }
      },

      scoring: {
        correctCategory: 80,
        incorrectCategory: -15,
        perfectCategory: 150,
        speedBonus: 30
      }
    },

    // 3. ASSOCIAÇÃO EMOCIONAL
    emotionalAssociation: {
      id: 'emotionalAssociation',
      name: 'Associação Emocional',
      icon: '🎭',
      description: 'Conecte imagens com estados emocionais correspondentes',

      difficulty: {
        beginner: {
          emotions: 4,
          situations: 8,
          timeLimit: 120000,
          emotionalContext: true
        },
        intermediate: {
          emotions: 5,
          situations: 10,
          timeLimit: 90000,
          emotionalContext: true
        },
        advanced: {
          emotions: 6,
          situations: 12,
          timeLimit: 60000,
          emotionalContext: false
        }
      },

      emotions: {
        happiness: {
          label: 'Alegria',
          icon: '😊',
          color: '#ffc107',
          situations: ['🎁', '🎂', '🌈', '🏆', '👶', '🎪'],
          triggers: ['surprise', 'achievement', 'celebration', 'love']
        },
        sadness: {
          label: 'Tristeza',
          icon: '😢',
          color: '#6c757d',
          situations: ['🌧️', '💔', '🥀', '👋', '📰', '🏚️'],
          triggers: ['loss', 'separation', 'disappointment', 'loneliness']
        },
        excitement: {
          label: 'Empolgação',
          icon: '🤩',
          color: '#e83e8c',
          situations: ['🎢', '🎮', '🎭', '🏃', '🎵', '🎊'],
          triggers: ['adventure', 'anticipation', 'energy', 'fun']
        },
        calmness: {
          label: 'Calma',
          icon: '😌',
          color: '#20c997',
          situations: ['🧘', '🌅', '🛀', '📚', '🍵', '🌊'],
          triggers: ['relaxation', 'peace', 'meditation', 'nature']
        },
        anger: {
          label: 'Raiva',
          icon: '😡',
          color: '#dc3545',
          situations: ['🚫', '⏰', '🚗', '📢', '🔥', '⚡'],
          triggers: ['frustration', 'injustice', 'obstruction', 'stress']
        },
        fear: {
          label: 'Medo',
          icon: '😱',
          color: '#6f42c1',
          situations: ['🌙', '👻', '🕷️', '⛈️', '🏥', '📉'],
          triggers: ['danger', 'unknown', 'threat', 'uncertainty']
        }
      },

      scoring: {
        correctEmotion: 90,
        incorrectEmotion: -25,
        emotionalInsight: 120,
        empathyBonus: 50
      }
    },

    // 4. ASSOCIAÇÃO POR FUNÇÃO
    functionalAssociation: {
      id: 'functionalAssociation',
      name: 'Associação por Função',
      icon: '🔍',
      description: 'Conecte objetos com suas funções ou usos específicos',

      difficulty: {
        beginner: {
          functions: 4,
          objects: 8,
          timeLimit: 100000,
          functionalHints: true
        },
        intermediate: {
          functions: 5,
          objects: 10,
          timeLimit: 80000,
          functionalHints: false
        },
        advanced: {
          functions: 6,
          objects: 12,
          timeLimit: 60000,
          functionalHints: false
        }
      },

      functions: {
        cutting: {
          label: 'Cortar',
          icon: '✂️',
          color: '#dc3545',
          objects: ['✂️', '🔪', '🪚', '⚔️', '💎'],
          contexts: ['kitchen', 'craft', 'garden', 'office']
        },
        writing: {
          label: 'Escrever',
          icon: '✍️',
          color: '#007bff',
          objects: ['✏️', '🖊️', '🖌️', '🖍️', '⌨️'],
          contexts: ['school', 'office', 'art', 'communication']
        },
        measuring: {
          label: 'Medir',
          icon: '📐',
          color: '#28a745',
          objects: ['📏', '📐', '⚖️', '🌡️', '⏰'],
          contexts: ['construction', 'science', 'cooking', 'health']
        },
        communication: {
          label: 'Comunicar',
          icon: '💬',
          color: '#fd7e14',
          objects: ['📱', '📞', '📻', '📺', '💻'],
          contexts: ['personal', 'business', 'emergency', 'entertainment']
        },
        transportation: {
          label: 'Transportar',
          icon: '🚚',
          color: '#6f42c1',
          objects: ['🚗', '🚲', '✈️', '🚢', '🛴'],
          contexts: ['daily', 'travel', 'cargo', 'recreation']
        },
        protection: {
          label: 'Proteger',
          icon: '🛡️',
          color: '#20c997',
          objects: ['⛑️', '👓', '🧤', '☂️', '🦺'],
          contexts: ['safety', 'weather', 'work', 'sports']
        }
      },

      scoring: {
        correctFunction: 85,
        incorrectFunction: -20,
        functionalReasoning: 110,
        contextualBonus: 40
      }
    },

    // 5. ASSOCIAÇÃO CONCEITUAL COMPLEXA
    conceptualAssociation: {
      id: 'conceptualAssociation',
      name: 'Associação Conceitual Complexa',
      icon: '🌐',
      description: 'Identifique relações abstratas e metafóricas entre conceitos',

      difficulty: {
        beginner: {
          concepts: 4,
          symbols: 8,
          timeLimit: 150000,
          abstractionLevel: 'low'
        },
        intermediate: {
          concepts: 5,
          symbols: 10,
          timeLimit: 120000,
          abstractionLevel: 'medium'
        },
        advanced: {
          concepts: 6,
          symbols: 12,
          timeLimit: 90000,
          abstractionLevel: 'high'
        }
      },

      concepts: {
        time: {
          label: 'Tempo',
          icon: '⏳',
          color: '#17a2b8',
          symbols: ['⏰', '📅', '🕰️', '⌛', '🌅', '🌙'],
          metaphors: ['river', 'circle', 'arrow', 'seasons'],
          abstractionLevel: 'medium'
        },
        justice: {
          label: 'Justiça',
          icon: '⚖️',
          color: '#6f42c1',
          symbols: ['⚖️', '🏛️', '👨‍⚖️', '📜', '🛡️', '⚖️'],
          metaphors: ['balance', 'blindfold', 'sword', 'scales'],
          abstractionLevel: 'high'
        },
        connection: {
          label: 'Conexão',
          icon: '🔗',
          color: '#28a745',
          symbols: ['🌉', '🤝', '💕', '🌐', '🔗', '📶'],
          metaphors: ['bridge', 'chain', 'network', 'bond'],
          abstractionLevel: 'medium'
        },
        growth: {
          label: 'Crescimento',
          icon: '📈',
          color: '#ffc107',
          symbols: ['🌱', '📈', '🏗️', '👶', '💪', '🎓'],
          metaphors: ['plant', 'stairs', 'journey', 'evolution'],
          abstractionLevel: 'medium'
        },
        freedom: {
          label: 'Liberdade',
          icon: '🦅',
          color: '#dc3545',
          symbols: ['🦅', '🗽', '🔓', '🌤️', '🕊️', '🏃'],
          metaphors: ['bird', 'open door', 'wind', 'wings'],
          abstractionLevel: 'high'
        },
        wisdom: {
          label: 'Sabedoria',
          icon: '🦉',
          color: '#e83e8c',
          symbols: ['🦉', '📚', '💡', '🔮', '👴', '🧙'],
          metaphors: ['owl', 'tree', 'light', 'depth'],
          abstractionLevel: 'high'
        }
      },

      scoring: {
        correctConcept: 120,
        incorrectConcept: -30,
        abstractReasoning: 150,
        metaphoricalThinking: 80,
        conceptualLeap: 200
      }
    },

    // 6. ASSOCIAÇÃO DE MEMÓRIA SEQUENCIAL
    sequentialAssociation: {
      id: 'sequentialAssociation',
      name: 'Associação de Memória Sequencial',
      icon: '🧠',
      description: 'Reconstituir sequências lógicas de imagens associadas',

      difficulty: {
        beginner: {
          sequenceLength: 3,
          sequences: 2,
          timeLimit: 120000,
          sequentialHints: true
        },
        intermediate: {
          sequenceLength: 4,
          sequences: 3,
          timeLimit: 100000,
          sequentialHints: false
        },
        advanced: {
          sequenceLength: 5,
          sequences: 4,
          timeLimit: 80000,
          sequentialHints: false
        }
      },

      sequences: {
        plantGrowth: {
          id: 'plantGrowth',
          name: 'Crescimento da Planta',
          type: 'biological',
          steps: [
            { order: 1, image: '🌱', label: 'Semente' },
            { order: 2, image: '🌿', label: 'Broto' },
            { order: 3, image: '🌸', label: 'Flor' },
            { order: 4, image: '🍎', label: 'Fruto' }
          ],
          logicalType: 'temporal-biological'
        },
        dayProgress: {
          id: 'dayProgress',
          name: 'Progressão do Dia',
          type: 'temporal',
          steps: [
            { order: 1, image: '🌅', label: 'Amanhecer' },
            { order: 2, image: '☀️', label: 'Manhã' },
            { order: 3, image: '🌇', label: 'Tarde' },
            { order: 4, image: '🌙', label: 'Noite' }
          ],
          logicalType: 'temporal-natural'
        },
        cookingProcess: {
          id: 'cookingProcess',
          name: 'Processo de Cozinhar',
          type: 'procedural',
          steps: [
            { order: 1, image: '🥕', label: 'Ingredientes' },
            { order: 2, image: '🔪', label: 'Preparar' },
            { order: 3, image: '🔥', label: 'Cozinhar' },
            { order: 4, image: '🍽️', label: 'Servir' }
          ],
          logicalType: 'procedural-cooking'
        },
        communication: {
          id: 'communication',
          name: 'Processo de Comunicação',
          type: 'social',
          steps: [
            { order: 1, image: '💭', label: 'Pensar' },
            { order: 2, image: '🗣️', label: 'Falar' },
            { order: 3, image: '👂', label: 'Ouvir' },
            { order: 4, image: '🤝', label: 'Entender' }
          ],
          logicalType: 'social-communication'
        },
        construction: {
          id: 'construction',
          name: 'Processo de Construção',
          type: 'procedural',
          steps: [
            { order: 1, image: '📐', label: 'Planejar' },
            { order: 2, image: '🧱', label: 'Fundação' },
            { order: 3, image: '🏗️', label: 'Construir' },
            { order: 4, image: '🏠', label: 'Finalizar' }
          ],
          logicalType: 'procedural-construction'
        }
      },

      scoring: {
        correctPosition: 60,
        incorrectPosition: -15,
        perfectSequence: 180,
        sequentialLogic: 100,
        memoryBonus: 70
      }
    }
  },

  // Sistema de análise avançada
  analytics: {
    cognitiveMetrics: [
      'associativeSpeed',
      'conceptualFlexibility',
      'visualMemoryRetention',
      'categoricalThinking',
      'emotionalRecognition',
      'functionalReasoning',
      'abstractThinking',
      'sequentialProcessing'
    ],
    
    errorPatterns: [
      'visualSimilarityConfusion',
      'semanticCategoryMixing',
      'emotionalMisattribution',
      'functionalOvergeneralization',
      'conceptualRigidity',
      'sequentialDisruption'
    ],

    adaptiveParameters: {
      difficultyAdjustment: 0.15,
      timeAdjustment: 0.1,
      hintThreshold: 0.3,
      masteryThreshold: 0.85
    }
  },

  // Configurações de UI/UX
  interface: {
    theme: 'glassmorphism',
    animations: {
      enabled: true,
      duration: 300,
      easing: 'ease-out'
    },
    accessibility: {
      highContrast: false,
      fontSize: 'medium',
      audioFeedback: true,
      hapticFeedback: true
    },
    multiSensory: {
      visual: {
        colorCoding: true,
        visualCues: true,
        animations: true
      },
      auditory: {
        soundEffects: true,
        verbalInstructions: true,
        backgroundMusic: false
      },
      tactile: {
        vibration: true,
        hapticPatterns: true
      }
    }
  },

  // Sistema de recompensas
  rewards: {
    points: {
      base: 100,
      multiplier: 1.2,
      perfectBonus: 2.0,
      speedBonus: 1.5
    },
    achievements: [
      {
        id: 'associationMaster',
        name: 'Mestre das Associações',
        description: 'Complete 50 associações corretas',
        icon: '🏆',
        threshold: 50
      },
      {
        id: 'speedThinker',
        name: 'Pensador Rápido',
        description: 'Complete uma atividade em menos de 30 segundos',
        icon: '⚡',
        threshold: 30000
      },
      {
        id: 'conceptualGenius',
        name: 'Gênio Conceitual',
        description: 'Acerte 10 associações abstratas consecutivas',
        icon: '🧠',
        threshold: 10
      }
    ]
  }
};

export default ImageAssociationV3Config;
