/**
 * 🎯 NAVEGAÇÃO UNIFICADA DOS DASHBOARDS PREMIUM
 * @file DashboardNavigation.jsx
 * @description Componente de navegação para todos os dashboards premium
 * @version 3.0.0
 * @premium true
 */

import React from 'react'
import styles from './DashboardNavigation.module.css'

const DashboardNavigation = ({ 
  activeDashboard, 
  onDashboardChange, 
  availableDashboards = [],
  showLabels = true,
  compact = false 
}) => {
  
  // Configuração dos dashboards principais
  const dashboardConfig = {
    performance: {
      id: 'performance',
      label: 'Performance',
      icon: '📊',
      description: 'Métricas de performance e uso',
      color: '#6366f1',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    ai: {
      id: 'ai',
      label: 'IA',
      icon: '🤖',
      description: 'Análise avançada com Inteligência Artificial',
      color: '#8b5cf6',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    neuropedagogical: {
      id: 'neuropedagogical',
      label: 'Neuropedagógico',
      icon: '🧠',
      description: 'Métricas especializadas para terapeutas',
      color: '#10b981',
      gradient: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)'
    },
    multisensory: {
      id: 'multisensory',
      label: 'Multissensorial',
      icon: '🎨',
      description: 'Análise detalhada de interações sensoriais',
      color: '#f59e0b',
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
    },
    realtime: {
      id: 'realtime',
      label: 'Tempo Real',
      icon: '⚡',
      description: 'Monitoramento em tempo real',
      color: '#ef4444',
      gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'
    }
  }

  // Filtrar dashboards disponíveis
  const dashboards = availableDashboards.length > 0 
    ? availableDashboards.map(id => dashboardConfig[id]).filter(Boolean)
    : Object.values(dashboardConfig)

  const handleDashboardClick = (dashboardId) => {
    if (onDashboardChange) {
      onDashboardChange(dashboardId)
    }
  }

  return (
    <div className={`${styles.navigation} ${compact ? styles.compact : ''}`}>
      <div className={styles.navigationHeader}>
        <h3 className={styles.title}>
          <span className={styles.titleIcon}>🎯</span>
          Dashboards Premium
        </h3>
        <div className={styles.subtitle}>
          Análise avançada e métricas especializadas
        </div>
      </div>

      <div className={styles.dashboardGrid}>
        {dashboards.map((dashboard) => (
          <button
            key={dashboard.id}
            className={`${styles.dashboardCard} ${
              activeDashboard === dashboard.id ? styles.active : ''
            }`}
            onClick={() => handleDashboardClick(dashboard.id)}
            style={{
              '--dashboard-color': dashboard.color,
              '--dashboard-gradient': dashboard.gradient
            }}
          >
            <div className={styles.cardIcon}>
              {dashboard.icon}
            </div>
            
            {showLabels && (
              <>
                <div className={styles.cardLabel}>
                  {dashboard.label}
                </div>
                
                {!compact && (
                  <div className={styles.cardDescription}>
                    {dashboard.description}
                  </div>
                )}
              </>
            )}

            {activeDashboard === dashboard.id && (
              <div className={styles.activeIndicator}>
                <div className={styles.activeIcon}>✓</div>
              </div>
            )}
          </button>
        ))}
      </div>

      {/* Indicador de status */}
      <div className={styles.statusIndicator}>
        <div className={styles.statusDot}></div>
        <span className={styles.statusText}>
          {dashboards.length} dashboards disponíveis
        </span>
      </div>
    </div>
  )
}

export default DashboardNavigation
