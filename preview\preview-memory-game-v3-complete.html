<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Game V3 - Preview das 6 Atividades</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .preview-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .activities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .activity-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .activity-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            background: rgba(255, 255, 255, 0.2);
        }

        .activity-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .activity-icon {
            font-size: 3rem;
            margin-right: 15px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .activity-title {
            color: white;
            font-size: 1.4rem;
            font-weight: 600;
            text-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        .activity-description {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.95rem;
            line-height: 1.5;
            margin-bottom: 20px;
        }

        .preview-area {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            min-height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }

        /* Specific activity styles */
        .pair-matching .preview-area {
            background: linear-gradient(135deg, rgba(255,107,107,0.2) 0%, rgba(255,142,142,0.2) 100%);
        }

        .sequence-memory .preview-area {
            background: linear-gradient(135deg, rgba(78,205,196,0.2) 0%, rgba(110,221,214,0.2) 100%);
        }

        .spatial-location .preview-area {
            background: linear-gradient(135deg, rgba(69,183,209,0.2) 0%, rgba(107,197,224,0.2) 100%);
        }

        .sound-patterns .preview-area {
            background: linear-gradient(135deg, rgba(150,206,180,0.2) 0%, rgba(168,216,196,0.2) 100%);
        }

        .image-reconstruction .preview-area {
            background: linear-gradient(135deg, rgba(254,202,87,0.2) 0%, rgba(255,213,116,0.2) 100%);
        }

        .number-sequence .preview-area {
            background: linear-gradient(135deg, rgba(154,121,223,0.2) 0%, rgba(174,142,232,0.2) 100%);
        }

        /* Activity previews */
        .memory-cards {
            display: grid;
            grid-template-columns: repeat(4, 60px);
            gap: 10px;
        }

        .memory-card {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .memory-card:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .sequence-items {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .sequence-item {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            font-weight: bold;
            color: white;
            border: 3px solid rgba(255, 255, 255, 0.4);
            animation: sequenceGlow 2s infinite alternate;
        }

        @keyframes sequenceGlow {
            0% { box-shadow: 0 0 10px rgba(78,205,196,0.5); }
            100% { box-shadow: 0 0 20px rgba(78,205,196,0.8); }
        }

        .spatial-grid {
            display: grid;
            grid-template-columns: repeat(4, 45px);
            gap: 8px;
        }

        .spatial-cell {
            width: 45px;
            height: 45px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .spatial-cell.highlighted {
            background: rgba(69, 183, 209, 0.6);
            box-shadow: 0 0 15px rgba(69, 183, 209, 0.8);
            animation: spatialPulse 1.5s infinite;
        }

        @keyframes spatialPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .sound-visualizer {
            display: flex;
            gap: 8px;
            align-items: end;
        }

        .sound-bar {
            width: 12px;
            background: linear-gradient(to top, #4CAF50, #81C784);
            border-radius: 6px;
            animation: soundWave 1s infinite ease-in-out;
        }

        @keyframes soundWave {
            0%, 100% { height: 20px; opacity: 0.7; }
            50% { height: 40px; opacity: 1; }
        }

        .sound-bar:nth-child(1) { animation-delay: 0s; }
        .sound-bar:nth-child(2) { animation-delay: 0.1s; }
        .sound-bar:nth-child(3) { animation-delay: 0.2s; }
        .sound-bar:nth-child(4) { animation-delay: 0.3s; }
        .sound-bar:nth-child(5) { animation-delay: 0.4s; }
        .sound-bar:nth-child(6) { animation-delay: 0.5s; }

        .puzzle-pieces {
            display: grid;
            grid-template-columns: repeat(3, 60px);
            gap: 5px;
        }

        .puzzle-piece {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #FFD54F, #FFF176);
            border-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: #333;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .puzzle-piece:hover {
            transform: rotate(5deg) scale(1.05);
        }

        .number-sequence-display {
            display: flex;
            gap: 12px;
        }

        .number-item {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #9C27B0, #BA68C8);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.4rem;
            font-weight: bold;
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: numberFloat 2s infinite ease-in-out alternate;
        }

        @keyframes numberFloat {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-5px); }
        }

        .features-section {
            margin-top: 40px;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .features-title {
            color: white;
            font-size: 1.8rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            color: white;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .feature-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .feature-description {
            font-size: 0.9rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .implementation-status {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.4);
            border-radius: 12px;
            padding: 15px;
            margin-top: 30px;
            text-align: center;
        }

        .status-text {
            color: #C8E6C9;
            font-weight: 600;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .activities-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .memory-cards {
                grid-template-columns: repeat(3, 50px);
            }
            
            .memory-card {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
            
            .spatial-grid {
                grid-template-columns: repeat(3, 40px);
            }
            
            .puzzle-pieces {
                grid-template-columns: repeat(2, 55px);
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="header">
            <h1>🧠 Memory Game V3 - Preview</h1>
            <p>Sistema completo com 6 atividades especializadas de memória, seguindo o padrão Letter Recognition V3</p>
        </div>

        <div class="activities-grid">
            <!-- 1. Correspondência de Pares -->
            <div class="activity-card pair-matching">
                <div class="activity-header">
                    <span class="activity-icon">🔄</span>
                    <h3 class="activity-title">Correspondência de Pares</h3>
                </div>
                <p class="activity-description">
                    Encontre os pares correspondentes virando as cartas. Desenvolve memória visual e reconhecimento de padrões.
                </p>
                <div class="preview-area">
                    <div class="memory-cards">
                        <div class="memory-card">🍎</div>
                        <div class="memory-card">❓</div>
                        <div class="memory-card">🐱</div>
                        <div class="memory-card">❓</div>
                        <div class="memory-card">❓</div>
                        <div class="memory-card">🍎</div>
                        <div class="memory-card">❓</div>
                        <div class="memory-card">🐱</div>
                    </div>
                </div>
            </div>

            <!-- 2. Memorização de Sequência -->
            <div class="activity-card sequence-memory">
                <div class="activity-header">
                    <span class="activity-icon">🧠</span>
                    <h3 class="activity-title">Memorização de Sequência</h3>
                </div>
                <p class="activity-description">
                    Memorize e reproduza sequências de elementos. Fortalece memória sequencial e atenção sustentada.
                </p>
                <div class="preview-area">
                    <div class="sequence-items">
                        <div class="sequence-item" style="background: #FF6B6B;">1</div>
                        <div class="sequence-item" style="background: #4ECDC4;">2</div>
                        <div class="sequence-item" style="background: #45B7D1;">3</div>
                        <div class="sequence-item" style="background: #96CEB4;">4</div>
                        <div class="sequence-item" style="background: #FECA57;">?</div>
                    </div>
                </div>
            </div>

            <!-- 3. Localização Espacial -->
            <div class="activity-card spatial-location">
                <div class="activity-header">
                    <span class="activity-icon">📍</span>
                    <h3 class="activity-title">Localização Espacial</h3>
                </div>
                <p class="activity-description">
                    Lembre das posições específicas de itens no grid. Desenvolve memória espacial e orientação visual.
                </p>
                <div class="preview-area">
                    <div class="spatial-grid">
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell highlighted">⭐</div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell highlighted">🔥</div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell highlighted">💎</div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                        <div class="spatial-cell"></div>
                    </div>
                </div>
            </div>

            <!-- 4. Padrões Sonoros -->
            <div class="activity-card sound-patterns">
                <div class="activity-header">
                    <span class="activity-icon">🎵</span>
                    <h3 class="activity-title">Padrões Sonoros</h3>
                </div>
                <p class="activity-description">
                    Reproduza sequências de sons e ritmos. Fortalece memória auditiva e processamento temporal.
                </p>
                <div class="preview-area">
                    <div class="sound-visualizer">
                        <div class="sound-bar"></div>
                        <div class="sound-bar"></div>
                        <div class="sound-bar"></div>
                        <div class="sound-bar"></div>
                        <div class="sound-bar"></div>
                        <div class="sound-bar"></div>
                    </div>
                </div>
            </div>

            <!-- 5. Reconstrução de Imagem -->
            <div class="activity-card image-reconstruction">
                <div class="activity-header">
                    <span class="activity-icon">🧩</span>
                    <h3 class="activity-title">Reconstrução de Imagem</h3>
                </div>
                <p class="activity-description">
                    Recomponha imagens fragmentadas. Desenvolve memória visual e raciocínio espacial.
                </p>
                <div class="preview-area">
                    <div class="puzzle-pieces">
                        <div class="puzzle-piece">1</div>
                        <div class="puzzle-piece">2</div>
                        <div class="puzzle-piece">3</div>
                        <div class="puzzle-piece">4</div>
                        <div class="puzzle-piece">5</div>
                        <div class="puzzle-piece">?</div>
                    </div>
                </div>
            </div>

            <!-- 6. Sequência Numérica -->
            <div class="activity-card number-sequence">
                <div class="activity-header">
                    <span class="activity-icon">🔢</span>
                    <h3 class="activity-title">Sequência Numérica</h3>
                </div>
                <p class="activity-description">
                    Memorize e reproduza sequências de números. Desenvolve memória de trabalho e processamento numérico.
                </p>
                <div class="preview-area">
                    <div class="number-sequence-display">
                        <div class="number-item">7</div>
                        <div class="number-item">3</div>
                        <div class="number-item">9</div>
                        <div class="number-item">1</div>
                        <div class="number-item">?</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="features-section">
            <h2 class="features-title">🚀 Recursos e Benefícios V3</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <span class="feature-icon">🎨</span>
                    <div class="feature-title">Design Moderno</div>
                    <div class="feature-description">Interface com glassmorphism, gradientes suaves e animações fluidas</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔄</span>
                    <div class="feature-title">Rotação Automática</div>
                    <div class="feature-description">Sistema inteligente de alternância entre atividades para desenvolvimento equilibrado</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📊</span>
                    <div class="feature-title">Métricas Avançadas</div>
                    <div class="feature-description">Análise detalhada específica para cada tipo de memória</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎯</span>
                    <div class="feature-title">Personalização</div>
                    <div class="feature-description">Adaptação automática baseada no desempenho e preferências do usuário</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📱</span>
                    <div class="feature-title">Responsivo</div>
                    <div class="feature-description">Funcionamento perfeito em desktop, tablet e mobile</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🧠</span>
                    <div class="feature-title">21 Coletores</div>
                    <div class="feature-description">15 existentes + 6 novos especializados por atividade</div>
                </div>
            </div>
        </div>

        <div class="implementation-status">
            <div class="status-text">
                ✅ Documento de Melhorias Criado | 🔄 Aguardando Aprovação para Implementação
            </div>
        </div>
    </div>

    <script>
        // Adicionar interatividade aos previews
        document.addEventListener('DOMContentLoaded', function() {
            // Animação das cartas de memória
            const memoryCards = document.querySelectorAll('.memory-card');
            memoryCards.forEach((card, index) => {
                card.addEventListener('click', function() {
                    if (this.textContent === '❓') {
                        const symbols = ['🍎', '🐱', '🌟', '🚀'];
                        this.textContent = symbols[index % symbols.length];
                        this.style.background = 'rgba(76, 175, 80, 0.3)';
                    }
                });
            });

            // Animação das peças do puzzle
            const puzzlePieces = document.querySelectorAll('.puzzle-piece');
            puzzlePieces.forEach(piece => {
                piece.addEventListener('click', function() {
                    this.style.transform = 'rotate(' + (Math.random() * 360) + 'deg) scale(1.1)';
                    setTimeout(() => {
                        this.style.transform = 'rotate(0deg) scale(1)';
                    }, 500);
                });
            });

            // Animação das células espaciais
            const spatialCells = document.querySelectorAll('.spatial-cell:not(.highlighted)');
            spatialCells.forEach(cell => {
                cell.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(69, 183, 209, 0.4)';
                });
                cell.addEventListener('mouseleave', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.2)';
                });
            });
        });
    </script>
</body>
</html>
