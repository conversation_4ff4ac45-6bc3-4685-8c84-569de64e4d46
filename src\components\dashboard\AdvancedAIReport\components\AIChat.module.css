/* AIChat.module.css - Estilos para o Chat IA */

.chatContainer {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  width: 380px;
  height: 600px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* Header do Chat */
.chatHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px 16px 0 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chatHeaderInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.aiAvatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.chatHeaderText {
  color: white;
}

.chatTitle {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.chatStatus {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  opacity: 0.9;
  margin-top: 2px;
}

.statusIndicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4444;
  animation: pulse 2s infinite;
}

.statusIndicator.connected {
  background: #4CAF50;
}

.statusIndicator.disconnected {
  background: #ff9800;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.closeButton {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Área de Mensagens */
.messagesContainer {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.messagesContainer::-webkit-scrollbar {
  width: 6px;
}

.messagesContainer::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.messagesContainer::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.messagesContainer::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.messageWrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.messageWrapper.ai {
  align-items: flex-start;
}

.messageWrapper.user {
  align-items: flex-end;
}

.messageContent {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  max-width: 85%;
}

.messageWrapper.user .messageContent {
  flex-direction: row-reverse;
}

.messageAvatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.messageWrapper.ai .messageAvatar {
  background: linear-gradient(135deg, #4CAF50, #45a049);
}

.messageWrapper.user .messageAvatar {
  background: linear-gradient(135deg, #2196F3, #1976D2);
}

.messageText {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
}

.messageWrapper.ai .messageText {
  background: #f5f5f5;
  color: #333;
  border-bottom-left-radius: 6px;
}

.messageWrapper.user .messageText {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-bottom-right-radius: 6px;
}

.messageTime {
  font-size: 11px;
  color: #666;
  padding: 0 8px;
}

/* Indicador de Digitação */
.typingIndicator {
  padding: 12px 16px;
  background: #f5f5f5;
  border-radius: 18px;
  border-bottom-left-radius: 6px;
  display: flex;
  gap: 4px;
  align-items: center;
}

.typingIndicator span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typingIndicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typingIndicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Input Container */
.inputContainer {
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 0 0 16px 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.inputWrapper {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.messageInput {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  resize: none;
  outline: none;
  transition: all 0.2s ease;
  min-height: 40px;
  max-height: 100px;
}

.messageInput:focus {
  border-color: #4CAF50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.messageInput:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.sendButton {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.sendButton:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.sendButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.inputHint {
  margin-top: 8px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.3;
}

/* Header Actions */
.headerActions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.expandButton {
  background: none;
  border: none;
  color: #667eea;
  font-size: 16px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.expandButton:hover {
  background-color: rgba(102, 126, 234, 0.1);
}

/* MCP Selector */
.mcpSelector {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 16px;
}

.mcpSelectorTitle {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mcpTabs {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.mcpTab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 16px;
  background: white;
  color: #4a5568;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.mcpTab:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mcpTab.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.mcpIcon {
  font-size: 14px;
}

.mcpName {
  font-weight: 500;
}

/* Chat expandido */
.chatContainer.expanded {
  width: 500px;
  height: 700px;
}

.chatContainer.expanded .messagesContainer {
  height: 450px;
}

/* Responsividade */
@media (max-width: 768px) {
  .chatContainer {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: none;
  }
  
  .chatHeader {
    border-radius: 0;
  }
  
  .inputContainer {
    border-radius: 0;
  }
}

@media (max-width: 480px) {
  .chatContainer {
    width: 100vw;
    height: 100vh;
  }
  
  .messagesContainer {
    padding: 12px;
  }
  
  .inputContainer {
    padding: 12px;
  }
}

/* Estados de hover e focus para acessibilidade */
.closeButton:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.sendButton:focus {
  outline: 2px solid rgba(76, 175, 80, 0.5);
  outline-offset: 2px;
}

.messageInput:focus {
  outline: none;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .messagesContainer {
    background: rgba(30, 30, 30, 0.95);
  }
  
  .messageWrapper.ai .messageText {
    background: #2a2a2a;
    color: #e0e0e0;
  }
  
  .typingIndicator {
    background: #2a2a2a;
  }
  
  .messageInput {
    background: rgba(40, 40, 40, 0.9);
    color: #e0e0e0;
    border-color: rgba(255, 255, 255, 0.1);
  }
}

/* AIBrain integration styles */
.aiBrainBadge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  font-size: 18px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  animation: pulseBrain 2s infinite;
}

@keyframes pulseBrain {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.aiBrainConnected {
  background-color: #00c851;
}

.aiBrainStatus {
  margin-left: 6px;
  font-size: 12px;
  color: #00c851;
  font-weight: 500;
}

/* Message with AIBrain enhanced content */
.messageContent.aiBrainEnhanced {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  border-left: 3px solid #667eea;
}

.aiBrainIcon {
  display: inline-block;
  margin-right: 6px;
  font-size: 14px;
}
