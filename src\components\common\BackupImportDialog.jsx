import React, { useState, useRef } from 'react';
import PropTypes from 'prop-types';
import { 
  Button, 
  Dialog, 
  DialogActions, 
  DialogContent, 
  DialogContentText, 
  DialogTitle,
  Box,
  Typography,
  LinearProgress,
  Paper,
  Snackbar,
  IconButton
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import CloudUploadIcon from '@material-ui/icons/CloudUpload';
import CloseIcon from '@material-ui/icons/Close';
import { useBackupImport } from '../../utils/backupExportSupport';

/**
 * Componente para importação de arquivos de backup
 * @param {Object} props - Propriedades do componente
 * @returns {React.Component}
 */
const BackupImportDialog = ({ open, onClose, onImportSuccess }) => {
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [validation, setValidation] = useState(null);
  const fileInputRef = useRef(null);
  const { importBackupFile, validateBackupData } = useBackupImport();

  const handleFileChange = (event) => {
    const selectedFile = event.target.files[0];
    
    if (!selectedFile) {
      setFile(null);
      setValidation(null);
      return;
    }
    
    if (!selectedFile.name.endsWith('.json')) {
      setError('O arquivo deve ter a extensão .json');
      setFile(null);
      return;
    }
    
    setFile(selectedFile);
    setError(null);
    
    // Ler e validar o arquivo
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const backupData = JSON.parse(e.target.result);
        const validationResult = validateBackupData(backupData);
        setValidation(validationResult);
        
        if (!validationResult.valid) {
          setError(validationResult.message);
        }
      } catch (error) {
        setValidation({ valid: false, message: 'Erro ao processar arquivo JSON' });
        setError('Erro ao processar arquivo: ' + error.message);
      }
    };
    reader.readAsText(selectedFile);
  };
  
  const handleImport = async () => {
    if (!file) return;
    
    setLoading(true);
    try {
      const result = await importBackupFile(file);
      setLoading(false);
      
      if (result.success) {
        onImportSuccess(result);
        onClose();
      } else {
        setError(result.message);
      }
    } catch (error) {
      setLoading(false);
      setError(typeof error === 'object' ? error.message : String(error));
    }
  };
  
  const handleCloseError = () => {
    setError(null);
  };
  
  const triggerFileInput = () => {
    fileInputRef.current.click();
  };
  
  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>Importar Backup</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Importe um arquivo de backup JSON exportado pelo Portal Betina.
            Os dados serão carregados na sessão atual e permitirão visualizar os dashboards sem conexão.
          </DialogContentText>
          
          <Box mt={3} mb={2}>
            <input
              type="file"
              accept=".json"
              style={{ display: 'none' }}
              ref={fileInputRef}
              onChange={handleFileChange}
            />
            <Paper 
              variant="outlined" 
              style={{ 
                padding: '20px', 
                textAlign: 'center',
                cursor: 'pointer',
                backgroundColor: '#f5f5f5'
              }}
              onClick={triggerFileInput}
            >
              <CloudUploadIcon fontSize="large" color="primary" />
              <Typography variant="body1" gutterBottom>
                {file ? file.name : 'Clique para selecionar um arquivo de backup'}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Apenas arquivos .json
              </Typography>
            </Paper>
          </Box>
          
          {loading && (
            <Box mt={2} mb={2}>
              <Typography variant="body2" gutterBottom>
                Processando arquivo...
              </Typography>
              <LinearProgress />
            </Box>
          )}
          
          {validation && (
            <Box mt={2}>
              <Alert 
                severity={validation.valid ? 'success' : 'error'}
                variant="outlined"
              >
                {validation.message}
                
                {validation.hasServerError && (
                  <Typography variant="body2" style={{ marginTop: '8px' }}>
                    <strong>Aviso:</strong> Este backup contém um aviso de erro do servidor.
                    Alguns dados podem estar incompletos.
                  </Typography>
                )}
              </Alert>
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={onClose} color="default">
            Cancelar
          </Button>
          <Button 
            onClick={handleImport} 
            color="primary" 
            disabled={loading || !file || (validation && !validation.valid)}
          >
            Importar
          </Button>
        </DialogActions>
      </Dialog>
      
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={handleCloseError}
        action={
          <IconButton size="small" color="inherit" onClick={handleCloseError}>
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      >
        <Alert onClose={handleCloseError} severity="error">
          {error}
        </Alert>
      </Snackbar>
    </>
  );
};

BackupImportDialog.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onImportSuccess: PropTypes.func.isRequired
};

export default BackupImportDialog;
