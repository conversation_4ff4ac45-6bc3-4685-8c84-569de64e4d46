# parse-srcset

A javascript parser for the [HTML5 srcset](http://www.w3.org/TR/html-srcset/) attribute, based on the [WHATWG reference algorithm](https://html.spec.whatwg.org/multipage/embedded-content.html#parse-a-srcset-attribute). It has an extensive test suite based on the [W3C srcset conformance checker](http://w3c-test.org/html/semantics/embedded-content/the-img-element/srcset/parse-a-srcset-attribute.html). It conforms to the jQuery JSCS style rules.

Tests are written using Intern-geezer for compatibility.

To run the tests in console:

```
$ npm test
```

Or in a browser, just open the html file at:

```
node_modules/intern-geezer/client.html?config=tests/intern
```

I’m on twitter [@tweetywheaty](https://twitter.com/tweetywheaty).
