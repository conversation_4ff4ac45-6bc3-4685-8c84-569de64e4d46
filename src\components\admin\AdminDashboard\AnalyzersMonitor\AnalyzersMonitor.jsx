/**
 * @file AnalyzersMonitor.jsx
 * @description Monitor de Analisadores Especializados - Área Administrativa
 * @version 3.0.0
 * @admin true
 * @datasource API Real + Fallback
 */

import React, { useState, useEffect } from 'react'
import adminApiService from '../../../../services/adminApiService'
import styles from './AnalyzersMonitor.module.css'

const AnalyzersMonitor = () => {
  const [analyzersData, setAnalyzersData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [selectedAnalyzer, setSelectedAnalyzer] = useState(null)
  const [dataSource, setDataSource] = useState('loading')
  const [lastUpdate, setLastUpdate] = useState(null)

  // Carregar dados reais dos analisadores
  const loadAnalyzersData = async () => {
    try {
      setLoading(true)
      const data = await adminApiService.getAnalyzersData()
      
      setAnalyzersData(data)
      setDataSource('api_real')
      setLastUpdate(new Date())
      
      console.log('✅ Dados dos analisadores carregados da API real:', data)
    } catch (error) {
      console.error('❌ Erro ao carregar dados dos analisadores:', error)
      setDataSource('fallback')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadAnalyzersData()
    const interval = setInterval(loadAnalyzersData, 60000) // Atualizar a cada 60s
    return () => clearInterval(interval)
  }, [])

  // Função para forçar atualização dos dados
  const refreshData = () => {
    adminApiService.clearCache()
    loadAnalyzersData()
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return '#4CAF50'
      case 'warning': return '#FF9800'
      case 'unhealthy': return '#F44336'
      default: return '#9E9E9E'
    }
  }

  const formatTime = (timestamp) => {
    const diff = Date.now() - new Date(timestamp).getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) return `${hours}h ${minutes % 60}m atrás`
    return `${minutes}m atrás`
  }

  const getDataSourceInfo = () => {
    switch (dataSource) {
      case 'api_real':
        return { icon: '🟢', text: 'Dados Reais da API', color: '#4CAF50' }
      case 'fallback':
        return { icon: '🟡', text: 'Dados de Fallback', color: '#FF9800' }
      case 'loading':
        return { icon: '🔄', text: 'Carregando...', color: '#2196F3' }
      default:
        return { icon: '🔴', text: 'Erro nos Dados', color: '#F44336' }
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Carregando dados dos analisadores...</p>
      </div>
    )
  }

  return (
    <div className={styles.analyzersMonitor}>
      {/* Header com informações da fonte dos dados */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        padding: '12px 16px',
        background: 'rgba(255, 255, 255, 0.08)',
        borderRadius: '10px',
        border: '1px solid rgba(255, 255, 255, 0.12)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <span style={{ fontSize: '20px' }}>🔬</span>
          <div>
            <h2 style={{ margin: 0, fontSize: '18px', color: '#fff', fontWeight: 'bold' }}>
              Monitor de Analisadores
            </h2>
            <p style={{ margin: 0, fontSize: '12px', color: '#ccc' }}>
              Dados em tempo real dos sistemas de análise
            </p>
          </div>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '6px',
            padding: '6px 12px',
            background: 'rgba(0, 0, 0, 0.2)',
            borderRadius: '8px',
            border: `1px solid ${getDataSourceInfo().color}33`
          }}>
            <span style={{ fontSize: '14px' }}>{getDataSourceInfo().icon}</span>
            <span style={{ 
              fontSize: '12px', 
              color: getDataSourceInfo().color,
              fontWeight: '600'
            }}>
              {getDataSourceInfo().text}
            </span>
          </div>
          
          {lastUpdate && (
            <div style={{ 
              fontSize: '11px', 
              color: '#999',
              textAlign: 'right'
            }}>
              <div>Última atualização:</div>
              <div style={{ fontWeight: 'bold', color: '#ccc' }}>
                {lastUpdate.toLocaleTimeString()}
              </div>
            </div>
          )}
          
          <button
            onClick={refreshData}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '8px',
              padding: '8px 12px',
              color: '#fff',
              fontSize: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'all 0.2s ease'
            }}
            onMouseOver={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.15)'}
            onMouseOut={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.1)'}
          >
            🔄 Atualizar
          </button>
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(370px, 1fr))', gap: '24px', margin: '24px 0' }}>
        {Object.entries(analyzersData).map(([key, analyzer]) => (
          <div key={key} style={{
            background: 'rgba(255, 255, 255, 0.13)',
            borderRadius: '16px',
            padding: '28px',
            border: '1.5px solid rgba(255, 255, 255, 0.25)',
            boxShadow: '0 4px 24px rgba(0,0,0,0.12)',
            backdropFilter: 'blur(12px)',
            transition: 'transform 0.2s ease',
            cursor: 'pointer',
            transform: selectedAnalyzer === key ? 'scale(1.03)' : 'scale(1)',
          }}
          onClick={() => setSelectedAnalyzer(selectedAnalyzer === key ? null : key)}
          >
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '18px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                <span style={{ fontSize: '40px', filter: 'drop-shadow(0 2px 6px #0002)' }}>
                  {analyzer.icon}
                </span>
                <div>
                  <h3 style={{ 
                    margin: 0, 
                    fontSize: '22px', 
                    fontWeight: 'bold', 
                    color: '#fff',
                    marginBottom: '4px',
                    letterSpacing: '0.5px',
                  }}>
                    {analyzer.name}
                  </h3>
                  <span style={{ 
                    color: getStatusColor(analyzer.status),
                    fontSize: '16px',
                    fontWeight: 'bold',
                    textTransform: 'lowercase',
                    letterSpacing: '0.5px',
                  }}>
                    {analyzer.status}
                  </span>
                </div>
              </div>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '16px' }}>
              {Object.entries(analyzer.metrics).map(([metricKey, value]) => (
                <div key={metricKey} style={{
                  background: 'rgba(0, 0, 0, 0.32)',
                  borderRadius: '10px',
                  padding: '14px 16px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '6px',
                  boxShadow: '0 2px 8px #0001',
                }}>
                  <span style={{ 
                    fontSize: '13px', 
                    color: '#e0e0e0',
                    textTransform: 'lowercase',
                    fontWeight: '500',
                    letterSpacing: '0.2px',
                  }}>
                    {metricKey.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                  </span>
                  <span style={{ 
                    fontSize: '18px', 
                    fontWeight: 'bold', 
                    color: '#fff',
                    lineHeight: '1.2',
                    textShadow: '0 1px 4px #0002',
                  }}>
                    {metricKey.includes('Time') || metricKey.includes('Analysis') || metricKey.includes('Assessment')
                      ? formatTime(value)
                      : value
                    }
                  </span>
                </div>
              ))}
            </div>

            {selectedAnalyzer === key && (
              <div style={{
                marginTop: '18px',
                padding: '18px',
                background: 'rgba(0, 0, 0, 0.22)',
                borderRadius: '10px',
                borderTop: '2px solid rgba(255, 255, 255, 0.3)',
                boxShadow: '0 2px 8px #0001',
              }}>
                <h4 style={{ 
                  margin: '0 0 12px 0', 
                  fontSize: '16px', 
                  color: '#fff',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '7px',
                  fontWeight: 'bold',
                  letterSpacing: '0.3px',
                }}>
                  📋 Detalhes Adicionais
                </h4>
                
                {analyzer.recentAnalyses && (
                  <div style={{ marginBottom: '10px' }}>
                    <h5 style={{ margin: '0 0 8px 0', fontSize: '12px', color: '#ccc' }}>Análises Recentes:</h5>
                    {analyzer.recentAnalyses.slice(0, 3).map((analysis, index) => (
                      <div key={index} style={{
                        background: 'rgba(255, 255, 255, 0.1)',
                        borderRadius: '4px',
                        padding: '6px 8px',
                        marginBottom: '4px',
                        fontSize: '11px',
                        color: '#fff'
                      }}>
                        <span>{analysis.childId}</span>
                        <span>{analysis.game}</span>
                        <span>Score: {analysis.score}</span>
                        <span>{formatTime(analysis.timestamp)}</span>
                      </div>
                    ))}
                  </div>
                )}

                {analyzer.domains && (
                  <div className={styles.detailSection}>
                    <h5>Domínios Cognitivos:</h5>
                    <div className={styles.domainsList}>
                      {analyzer.domains.map(domain => (
                        <span key={domain} className={styles.domainTag}>
                          {domain.replace(/_/g, ' ')}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {analyzer.approaches && (
                  <div className={styles.detailSection}>
                    <h5>Abordagens Terapêuticas:</h5>
                    <div className={styles.approachesList}>
                      {analyzer.approaches.map(approach => (
                        <span key={approach} className={styles.approachTag}>
                          {approach}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '12px',
        padding: '20px',
        margin: '20px 0',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        backdropFilter: 'blur(10px)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          gap: '20px'
        }}>
          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>🔬</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#fff', marginBottom: '2px' }}>
              {Object.keys(analyzersData).length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Analisadores Ativos</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>📈</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', marginBottom: '2px' }}>
              {Object.values(analyzersData).reduce((sum, analyzer) => 
                sum + (analyzer.metrics.analysesPerformed || analyzer.metrics.cognitiveAssessments || analyzer.metrics.progressReports || analyzer.metrics.sessionsAnalyzed || analyzer.metrics.therapeuticAnalyses || 0), 0
              )}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Total de Análises</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>⚡</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#f59e0b', marginBottom: '2px' }}>
              {(Object.values(analyzersData).reduce((sum, analyzer) => 
                sum + parseFloat(analyzer.metrics.cacheHitRate || analyzer.metrics.avgConfidence || analyzer.metrics.improvementRate || analyzer.metrics.avgEngagement || analyzer.metrics.outcomeSuccess || 0.8), 0
              ) / Object.keys(analyzersData).length).toFixed(2)}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Performance Média</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>✅</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', marginBottom: '2px' }}>
              {Object.values(analyzersData).filter(a => a.status === 'healthy').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Saudáveis</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export { AnalyzersMonitor }
export default AnalyzersMonitor
