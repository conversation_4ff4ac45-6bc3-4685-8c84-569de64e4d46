/**
 * ⏰ TEMPORAL PATTERN COLLECTOR - <PERSON><PERSON><PERSON><PERSON> V<PERSON>ua<PERSON>
 * Coleta especializada de dados sobre padrões temporais
 * Portal Betina V3
 */

export class TemporalPatternCollector {
  constructor() {
    this.collectorId = 'temporal_pattern';
    this.isActive = true;
    this.temporalMetrics = {
      sequenceTiming: [],
      rhythmPatterns: [],
      temporalOrdering: [],
      timingAccuracy: [],
      sequentialProcessing: []
    };
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Método principal de análise
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de padrões temporais
   */
  async analyze(data) {
    try {
      if (!data || !data.gameData) {
        console.warn('TemporalPatternCollector: Dados incompletos recebidos');
        return {
          sequenceTimingAccuracy: 0.5,
          rhythmRecognition: 'medium',
          temporalOrderingSkills: 0.5,
          timingPrecision: 0.5,
          sequentialProcessing: 0.5,
          overallTemporalScore: 0.5
        };
      }

      const temporalData = this.collectTemporalData(data.gameData, data.playerBehavior);
      
      return {
        sequenceTimingAccuracy: this.calculateSequenceTiming(temporalData),
        rhythmRecognition: this.assessRhythmRecognition(temporalData),
        temporalOrderingSkills: this.assessTemporalOrdering(temporalData),
        timingPrecision: this.assessTimingPrecision(temporalData),
        sequentialProcessing: this.assessSequentialProcessing(temporalData),
        overallTemporalScore: this.calculateOverallTemporalScore(temporalData)
      };
    } catch (error) {
      console.error('Erro no TemporalPatternCollector.analyze:', error);
      return {
        sequenceTimingAccuracy: 0.5,
        rhythmRecognition: 'medium',
        temporalOrderingSkills: 0.5,
        timingPrecision: 0.5,
        sequentialProcessing: 0.5,
        overallTemporalScore: 0.5
      };
    }
  }

  collectTemporalData(gameData, playerBehavior) {
    return {
      timestamp: Date.now(),
      sessionId: gameData.sessionId,
      timingInteractions: this.analyzeTimingInteractions(gameData, playerBehavior),
      rhythmData: this.analyzeRhythmPatterns(gameData, playerBehavior),
      orderingData: this.analyzeTemporalOrdering(gameData, playerBehavior),
      precisionData: this.analyzeTimingPrecision(gameData, playerBehavior),
      processingData: this.analyzeSequentialProcessing(gameData, playerBehavior)
    };
  }

  analyzeTimingInteractions(gameData, playerBehavior) {
    return {
      responseTime: playerBehavior?.responseTime || 1000,
      timingAccuracy: playerBehavior?.timingAccuracy || 0.5,
      expectedTiming: gameData?.expectedTiming || 1000,
      timingDeviation: Math.abs((playerBehavior?.responseTime || 1000) - (gameData?.expectedTiming || 1000))
    };
  }

  analyzeRhythmPatterns(gameData, playerBehavior) {
    return {
      rhythmAccuracy: 0.5,
      rhythmConsistency: 0.5,
      rhythmComplexity: gameData?.rhythmComplexity || 'medium',
      rhythmSpeed: 0.5
    };
  }

  analyzeTemporalOrdering(gameData, playerBehavior) {
    return {
      orderingAccuracy: 0.5,
      sequenceLength: gameData?.sequence?.length || 3,
      orderingSpeed: 0.5,
      orderingConsistency: 0.5
    };
  }

  analyzeTimingPrecision(gameData, playerBehavior) {
    return {
      precisionScore: 0.5,
      consistencyScore: 0.5,
      adaptabilityScore: 0.5,
      stabilityScore: 0.5
    };
  }

  analyzeSequentialProcessing(gameData, playerBehavior) {
    return {
      processingSpeed: 0.5,
      processingAccuracy: 0.5,
      processingConsistency: 0.5,
      processingEfficiency: 0.5
    };
  }

  calculateSequenceTiming(temporalData) {
    return temporalData.timingInteractions?.timingAccuracy || 0.5;
  }

  assessRhythmRecognition(temporalData) {
    const score = temporalData.rhythmData?.rhythmAccuracy || 0.5;
    if (score >= 0.8) return 'high';
    if (score >= 0.6) return 'medium';
    return 'low';
  }

  assessTemporalOrdering(temporalData) {
    return temporalData.orderingData?.orderingAccuracy || 0.5;
  }

  assessTimingPrecision(temporalData) {
    return temporalData.precisionData?.precisionScore || 0.5;
  }

  assessSequentialProcessing(temporalData) {
    return temporalData.processingData?.processingEfficiency || 0.5;
  }

  calculateOverallTemporalScore(temporalData) {
    const timing = this.calculateSequenceTiming(temporalData);
    const ordering = this.assessTemporalOrdering(temporalData);
    const precision = this.assessTimingPrecision(temporalData);
    const processing = this.assessSequentialProcessing(temporalData);
    
    return (timing + ordering + precision + processing) / 4;
  }

  updateTemporalMetrics(temporalData) {
    this.temporalMetrics.sequenceTiming.push(temporalData.timingInteractions);
    this.temporalMetrics.rhythmPatterns.push(temporalData.rhythmData);
    this.temporalMetrics.temporalOrdering.push(temporalData.orderingData);
    this.temporalMetrics.timingAccuracy.push(temporalData.precisionData);
    this.temporalMetrics.sequentialProcessing.push(temporalData.processingData);
  }

  getTemporalMetrics() {
    return this.temporalMetrics;
  }

  reset() {
    this.temporalMetrics = {
      sequenceTiming: [],
      rhythmPatterns: [],
      temporalOrdering: [],
      timingAccuracy: [],
      sequentialProcessing: []
    };
  }
}
