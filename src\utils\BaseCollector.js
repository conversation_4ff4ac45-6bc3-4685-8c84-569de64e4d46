/**
 * @file BaseCollector.js
 * @description Classe base para todos os coletores de dados do sistema
 * @version 1.0.0
 */

export class BaseCollector {
  constructor(name = 'BaseCollector') {
    this.name = name;
    this.isActive = true;
    this.data = [];
    this.metadata = {
      createdAt: new Date().toISOString(),
      version: '1.0.0',
      type: 'base'
    };
  }

  /**
   * Coleta dados de interação
   * @param {Object} data - Dados a serem coletados
   * @returns {Promise<boolean>} - Sucesso da operação
   */
  async collect(data) {
    try {
      if (!this.isActive) {
        console.warn(`Collector ${this.name} está inativo`);
        return false;
      }

      const enrichedData = {
        ...data,
        timestamp: new Date().toISOString(),
        collectorName: this.name,
        id: this.generateId()
      };

      this.data.push(enrichedData);
      await this.onDataCollected(enrichedData);
      
      return true;
    } catch (error) {
      console.error(`Erro no collector ${this.name}:`, error);
      return false;
    }
  }

  /**
   * Hook chamado após coleta de dados
   * @param {Object} data - Dados coletados
   */
  async onDataCollected(data) {
    // Implementação específica em subclasses
  }

  /**
   * Gera ID único para os dados
   * @returns {string} - ID único
   */
  generateId() {
    return `${this.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Obtém todos os dados coletados
   * @returns {Array} - Array de dados
   */
  getData() {
    return [...this.data];
  }

  /**
   * Filtra dados por critério
   * @param {Function} filter - Função de filtro
   * @returns {Array} - Dados filtrados
   */
  getFilteredData(filter) {
    return this.data.filter(filter);
  }

  /**
   * Limpa todos os dados coletados
   */
  clearData() {
    this.data = [];
  }

  /**
   * Ativa o collector
   */
  activate() {
    this.isActive = true;
  }

  /**
   * Desativa o collector
   */
  deactivate() {
    this.isActive = false;
  }

  /**
   * Obtém estatísticas básicas dos dados
   * @returns {Object} - Estatísticas
   */
  getStats() {
    return {
      totalEntries: this.data.length,
      firstEntry: this.data[0]?.timestamp || null,
      lastEntry: this.data[this.data.length - 1]?.timestamp || null,
      isActive: this.isActive,
      name: this.name
    };
  }

  /**
   * Exporta dados em formato JSON
   * @returns {Object} - Dados exportados
   */
  export() {
    return {
      metadata: this.metadata,
      stats: this.getStats(),
      data: this.getData()
    };
  }

  /**
   * Valida se os dados estão no formato correto
   * @param {Object} data - Dados para validar
   * @returns {boolean} - Se os dados são válidos
   */
  validateData(data) {
    return data && typeof data === 'object';
  }

  /**
   * Processa dados antes da coleta
   * @param {Object} data - Dados brutos
   * @returns {Object} - Dados processados
   */
  preprocessData(data) {
    return data;
  }

  /**
   * Análise básica dos dados coletados
   * @returns {Object} - Resultado da análise
   */
  analyze() {
    const stats = this.getStats();
    
    return {
      collector: this.name,
      totalInteractions: stats.totalEntries,
      timeSpan: {
        start: stats.firstEntry,
        end: stats.lastEntry
      },
      averageDataSize: this.data.length > 0 
        ? this.data.reduce((sum, item) => sum + JSON.stringify(item).length, 0) / this.data.length 
        : 0,
      dataTypes: [...new Set(this.data.map(item => item.type || 'unknown'))],
      status: this.isActive ? 'active' : 'inactive'
    };
  }
}
