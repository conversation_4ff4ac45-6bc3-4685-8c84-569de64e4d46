import{j as e}from"./index-BIwBZl_j.js";import{r as o}from"./react-router-BtSsPy6x.js";import{L as a}from"./NeuropedagogicalDashboard-CAsMfhwI.js";import{C as n,a as s,b as t,c as r,d as i,e as m,p as d,f as l,g as c,A as b}from"./chart-core-CRFNBRsI.js";import{D as u}from"./chart-react-BbBAr11T.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";n.register(s,t,r,i,m,d,l,c,b);const h=()=>{const[n,s]=o.useState(!0),[t,r]=o.useState(new Date),[i,m]=o.useState(null),[d,l]=o.useState("loading"),[c,b]=o.useState(null),[h,v]=o.useState(null),[p,N]=o.useState({touch:!1,accelerometer:!1,gyroscope:!1,calibration:!1}),[g,j]=o.useState({activeSessions:0,sensorActivity:0,calibrationStatus:0,dataProcessed:0}),D=()=>{try{const e=JSON.parse(localStorage.getItem("gameScores")||"[]"),o=JSON.parse(localStorage.getItem("gameSessions")||"[]"),a=JSON.parse(localStorage.getItem("registeredUsers")||"[]"),n=JSON.parse(localStorage.getItem("systemLogs")||"[]"),s=o.length,t=a.length||1,r=e.length>0?e.reduce((e,o)=>e+o.accuracy,0)/e.length:85;return{systems:[{id:"auth",name:"Sistema de Autenticação",status:"active",uptime:"99.9%",responseTime:Math.round(50*Math.random()+80)+"ms",icon:"fas fa-shield-alt",metrics:{activeUsers:t,dailyLogins:Math.max(s,1),failedAttempts:Math.round(5*Math.random())}},{id:"database",name:"Banco de Dados",status:"active",uptime:"99.8%",responseTime:Math.round(30*Math.random()+20)+"ms",icon:"fas fa-database",metrics:{connections:Math.round(2.5*t),queries:Math.max(100*e.length,100),storage:Math.round(30*Math.random()+50)+"%"}},{id:"api",name:"API Gateway",status:r>80?"active":"warning",uptime:"99.5%",responseTime:Math.round(100*Math.random()+150)+"ms",icon:"fas fa-exchange-alt",metrics:{requests:Math.max(50*s,100),errors:Math.round(10*Math.random()),bandwidth:Math.round(40*Math.random()+30)+"%"}},{id:"games",name:"Sistema de Jogos",status:"active",uptime:"99.7%",responseTime:Math.round(80*Math.random()+100)+"ms",icon:"fas fa-gamepad",metrics:{activeSessions:Math.round(.1*s),completedGames:e.filter(e=>e.completed).length,avgScore:Math.round(r)}},{id:"accessibility",name:"Sistema de Acessibilidade",status:"active",uptime:"99.9%",responseTime:Math.round(40*Math.random()+60)+"ms",icon:"fas fa-universal-access",metrics:{activeFeatures:8,usersWithA11y:Math.round(.3*t),compliance:"98%"}}],performance:{cpu:Math.round(30*Math.random()+20),memory:Math.round(40*Math.random()+30),disk:Math.round(25*Math.random()+15),network:Math.round(50*Math.random()+30)},alerts:n.slice(0,5).map((e,o)=>({id:o,type:e.level||"info",message:e.message||"Sistema funcionando normalmente",timestamp:e.timestamp||(new Date).toISOString(),resolved:e.resolved||Math.random()>.3})),analytics:{totalUsers:t,activeSessions:Math.round(.1*s),systemLoad:Math.round(60*Math.random()+20),successRate:Math.round(r),errorRate:Math.round((100-r)/10)}}}catch(e){return{systems:[],performance:{cpu:0,memory:0,disk:0,network:0},alerts:[],analytics:{totalUsers:0,activeSessions:0,systemLoad:0,successRate:0,errorRate:0}}}},x={labels:["CPU","Memória","Disco","Rede"],datasets:[{label:"Utilização (%)",data:[i?.performance?.cpu||0,i?.performance?.memory||0,i?.performance?.disk||0,i?.performance?.network||0],backgroundColor:["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4"],borderWidth:2,borderColor:"#ffffff"}]},y=e=>{switch(e){case"active":return"#059669";case"warning":return"#F59E0B";case"error":return"#DC2626";case"maintenance":return"#6B7280";default:return"#2563EB"}},f=e=>{switch(e){case"active":return"fas fa-check-circle";case"warning":return"fas fa-exclamation-triangle";case"error":return"fas fa-times-circle";case"maintenance":return"fas fa-tools";default:return"fas fa-question-circle"}},S=e=>{switch(e){case"error":return"#DC2626";case"warning":return"#F59E0B";case"info":default:return"#2563EB";case"success":return"#059669"}};return o.useEffect(()=>{(async()=>{s(!0),await(async()=>{try{const e={totalSensorReadings:Math.floor(1e4*Math.random())+5e3,touchInteractions:Math.floor(500*Math.random())+200,accelerometerReadings:Math.floor(1e3*Math.random())+800,gyroscopeReadings:Math.floor(800*Math.random())+600,calibrationEvents:Math.floor(50*Math.random())+20,sensorAccuracy:(.3*Math.random()+.7).toFixed(2),lastCalibration:new Date(Date.now()-864e5*Math.random()).toISOString(),activeSensors:["touch","accelerometer","gyroscope"],sensorHealth:{touch:Math.random()>.2,accelerometer:Math.random()>.1,gyroscope:Math.random()>.15,calibration:Math.random()>.3}};b(e),N(e.sensorHealth),j({activeSessions:Math.floor(20*Math.random())+5,sensorActivity:e.totalSensorReadings,calibrationStatus:e.calibrationEvents,dataProcessed:.95*e.totalSensorReadings})}catch(e){}})(),setTimeout(()=>{const e=D();m(e),s(!1)},700)})()},[]),o.useEffect(()=>{const e=setInterval(()=>{r(new Date)},3e4);return()=>clearInterval(e)},[]),n?e.jsxDEV(a,{message:"Carregando dashboard integrado..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:363,columnNumber:12},void 0):e.jsxDEV("div",{className:"integrated-dashboard",children:[e.jsxDEV("div",{className:"dashboard-header",children:[e.jsxDEV("div",{className:"header-content",children:[e.jsxDEV("h2",{children:"🔧 Dashboard Integrado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:371,columnNumber:11},void 0),e.jsxDEV("p",{children:"Monitoramento completo do sistema Portal Betina V3"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:372,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:370,columnNumber:9},void 0),e.jsxDEV("div",{className:"refresh-info",children:[e.jsxDEV("span",{children:["Última atualização: ",t.toLocaleTimeString()]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:376,columnNumber:11},void 0),e.jsxDEV("button",{onClick:()=>{const e=D();m(e),r(new Date)},className:"refresh-btn",children:e.jsxDEV("i",{className:"fas fa-sync-alt"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:385,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:377,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:375,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:369,columnNumber:7},void 0),e.jsxDEV("div",{className:"systems-section",children:[e.jsxDEV("h3",{children:"🖥️ Status dos Sistemas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:392,columnNumber:9},void 0),e.jsxDEV("div",{className:"systems-grid",children:i?.systems?.map(o=>e.jsxDEV("div",{className:"system-card",children:[e.jsxDEV("div",{className:"system-header",children:[e.jsxDEV("div",{className:"system-icon",children:e.jsxDEV("i",{className:o.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:398,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:397,columnNumber:17},void 0),e.jsxDEV("div",{className:"system-info",children:[e.jsxDEV("h4",{children:o.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:401,columnNumber:19},void 0),e.jsxDEV("div",{className:"system-status",children:[e.jsxDEV("i",{className:f(o.status),style:{color:y(o.status)}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:403,columnNumber:21},void 0),e.jsxDEV("span",{style:{color:y(o.status)},children:o.status.toUpperCase()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:407,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:402,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:400,columnNumber:17},void 0),e.jsxDEV("div",{className:"system-metrics",children:[e.jsxDEV("div",{className:"metric",children:[e.jsxDEV("span",{className:"label",children:"Uptime:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:414,columnNumber:21},void 0),e.jsxDEV("span",{className:"value",children:o.uptime},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:415,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:413,columnNumber:19},void 0),e.jsxDEV("div",{className:"metric",children:[e.jsxDEV("span",{className:"label",children:"Resposta:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:418,columnNumber:21},void 0),e.jsxDEV("span",{className:"value",children:o.responseTime},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:419,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:417,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:412,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:396,columnNumber:15},void 0),e.jsxDEV("div",{className:"system-details",children:Object.entries(o.metrics).map(([o,a])=>e.jsxDEV("div",{className:"detail-item",children:[e.jsxDEV("span",{className:"detail-label",children:[o.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()),":"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:427,columnNumber:21},void 0),e.jsxDEV("span",{className:"detail-value",children:a},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:430,columnNumber:21},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:426,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:424,columnNumber:15},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:395,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:393,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:391,columnNumber:7},void 0),e.jsxDEV("div",{className:"analytics-section",children:e.jsxDEV("div",{className:"analytics-grid",children:[e.jsxDEV("div",{className:"chart-container",children:[e.jsxDEV("h4",{children:"📊 Performance do Sistema"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:444,columnNumber:13},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(u,{data:x,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#ffffff",font:{size:12}}},tooltip:{backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"#ffffff",bodyColor:"#ffffff"}},scales:{x:{ticks:{color:"#ffffff"},grid:{color:"rgba(255, 255, 255, 0.1)"}},y:{ticks:{color:"#ffffff"},grid:{color:"rgba(255, 255, 255, 0.1)"}}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:446,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:445,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:443,columnNumber:11},void 0),e.jsxDEV("div",{className:"metrics-container",children:[e.jsxDEV("h4",{children:"📈 Métricas Gerais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:452,columnNumber:13},void 0),e.jsxDEV("div",{className:"metrics-list",children:[e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-users"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:456,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:455,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Usuários Totais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:459,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:i?.analytics?.totalUsers||0},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:460,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:458,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:454,columnNumber:15},void 0),e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-play"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:466,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:465,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Sessões Ativas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:469,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:i?.analytics?.activeSessions||0},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:470,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:468,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:464,columnNumber:15},void 0),e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-tachometer-alt"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:476,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:475,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Carga do Sistema"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:479,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:[i?.analytics?.systemLoad||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:480,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:478,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:474,columnNumber:15},void 0),e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-check-circle"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:486,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:485,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Taxa de Sucesso"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:489,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:[i?.analytics?.successRate||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:490,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:488,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:484,columnNumber:15},void 0),e.jsxDEV("div",{className:"metric-item",children:[e.jsxDEV("div",{className:"metric-icon",children:e.jsxDEV("i",{className:"fas fa-exclamation-triangle"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:496,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:495,columnNumber:17},void 0),e.jsxDEV("div",{className:"metric-info",children:[e.jsxDEV("span",{className:"metric-label",children:"Taxa de Erro"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:499,columnNumber:19},void 0),e.jsxDEV("span",{className:"metric-value",children:[i?.analytics?.errorRate||0,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:500,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:498,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:494,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:453,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:451,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:441,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:440,columnNumber:7},void 0),e.jsxDEV("div",{className:"multisensory-section",children:[e.jsxDEV("h3",{children:"🔬 Sistema Multissensorial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:510,columnNumber:9},void 0),e.jsxDEV("div",{className:"multisensory-container",children:[e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",margin:"20px 0",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(10px)"},children:[e.jsxDEV("div",{style:{marginBottom:"15px",fontSize:"16px",fontWeight:"bold",color:"#fff"},children:"📡 Status dos Sensores"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:522,columnNumber:13},void 0),e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-around",alignItems:"center",gap:"20px"},children:[e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"🖐️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:532,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#ef4444",marginBottom:"2px"},children:"Touch"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:533,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ef4444"},children:"Offline"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:536,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:531,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"📱"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:540,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#ef4444",marginBottom:"2px"},children:"Acelerômetro"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:541,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ef4444"},children:"Offline"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:544,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:539,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"🧭"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:548,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#10b981",marginBottom:"2px"},children:"Giroscópio"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:549,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#10b981"},children:"Online"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:552,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:547,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"⚙️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:556,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#10b981",marginBottom:"2px"},children:"Calibração"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:557,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#10b981"},children:"Online"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:560,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:555,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:525,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:514,columnNumber:11},void 0),e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",margin:"20px 0",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(10px)"},children:[e.jsxDEV("div",{style:{marginBottom:"15px",fontSize:"16px",fontWeight:"bold",color:"#fff"},children:"📊 Métricas Multissensoriais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:574,columnNumber:13},void 0),e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-around",alignItems:"center",gap:"20px"},children:[e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:584,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#fff",marginBottom:"2px"},children:c?.totalSensorReadings?.toLocaleString()||"5.136"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:585,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Leituras Totais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:588,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:583,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"👆"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:592,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#6366f1",marginBottom:"2px"},children:c?.touchInteractions?.toLocaleString()||"443"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:593,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Interações Touch"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:596,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:591,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"🎯"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:600,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#10b981",marginBottom:"2px"},children:[c?.sensorAccuracy||"0.99","%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:601,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Precisão Sensorial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:604,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:599,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"🔄"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:608,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#f59e0b",marginBottom:"2px"},children:c?.calibrationEvents||"58"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:609,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Calibrações"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:612,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:607,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:577,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:566,columnNumber:11},void 0),e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",margin:"20px 0",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(10px)"},children:[e.jsxDEV("div",{style:{marginBottom:"15px",fontSize:"16px",fontWeight:"bold",color:"#fff"},children:"⚡ Tempo Real"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:626,columnNumber:13},void 0),e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-around",alignItems:"center",gap:"20px"},children:[e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"👥"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:636,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#8b5cf6",marginBottom:"2px"},children:g.activeSessions||"7"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:637,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Sessões Ativas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:640,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:635,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"🌊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:644,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#06d6a0",marginBottom:"2px"},children:g.sensorActivity?.toLocaleString()||"5.136"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:645,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Atividade Sensorial"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:648,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:643,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"💽"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:652,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#f72585",marginBottom:"2px"},children:g.dataProcessed?.toLocaleString()||"4.879,2"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:653,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Dados Processados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:656,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:651,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"🕒"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:660,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"14px",fontWeight:"bold",color:"#fff",marginBottom:"2px"},children:c?.lastCalibration?new Date(c.lastCalibration).toLocaleString():"15/07/2025, 20:13:29"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:661,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Última Calibração"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:667,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:659,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:629,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:618,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:511,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:509,columnNumber:7},void 0),e.jsxDEV("div",{className:"alerts-section",children:[e.jsxDEV("h3",{children:"🚨 Alertas e Eventos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:677,columnNumber:9},void 0),e.jsxDEV("div",{className:"alerts-container",children:i?.alerts?.length>0?i.alerts.map(o=>e.jsxDEV("div",{className:"alert-item",children:[e.jsxDEV("div",{className:"alert-icon",children:e.jsxDEV("i",{className:"fas fa-circle",style:{color:S(o.type)}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:683,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:682,columnNumber:17},void 0),e.jsxDEV("div",{className:"alert-content",children:[e.jsxDEV("div",{className:"alert-message",children:o.message},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:689,columnNumber:19},void 0),e.jsxDEV("div",{className:"alert-meta",children:[e.jsxDEV("span",{className:"alert-type",style:{color:S(o.type)},children:o.type.toUpperCase()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:691,columnNumber:21},void 0),e.jsxDEV("span",{className:"alert-time",children:new Date(o.timestamp).toLocaleString()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:694,columnNumber:21},void 0),o.resolved&&e.jsxDEV("span",{className:"alert-resolved",children:[e.jsxDEV("i",{className:"fas fa-check"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:699,columnNumber:25},void 0)," Resolvido"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:698,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:690,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:688,columnNumber:17},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:681,columnNumber:15},void 0)):e.jsxDEV("div",{className:"no-alerts",children:[e.jsxDEV("i",{className:"fas fa-check-circle"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:708,columnNumber:15},void 0),e.jsxDEV("span",{children:"Nenhum alerta ativo. Sistema funcionando normalmente."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:709,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:707,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:678,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:676,columnNumber:7},void 0),e.jsxDEV("style",{children:"\n        .integrated-dashboard {\n          padding: 2rem;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          min-height: 100vh;\n          color: white;\n        }\n\n        .dashboard-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 2rem;\n          flex-wrap: wrap;\n          gap: 1rem;\n        }\n\n        .header-content h2 {\n          margin: 0;\n          font-size: 2rem;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .header-content p {\n          margin: 0.5rem 0 0 0;\n          opacity: 0.9;\n        }\n\n        .refresh-info {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          font-size: 0.9rem;\n          opacity: 0.8;\n        }\n\n        .refresh-btn {\n          background: rgba(255, 255, 255, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.3);\n          color: white;\n          padding: 0.5rem;\n          border-radius: 0.5rem;\n          cursor: pointer;\n          transition: all 0.3s ease;\n        }\n\n        .refresh-btn:hover {\n          background: rgba(255, 255, 255, 0.2);\n        }\n\n        .systems-section,\n        .analytics-section,\n        .alerts-section {\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 1rem;\n          padding: 2rem;\n          margin-bottom: 2rem;\n          backdrop-filter: blur(10px);\n          border: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .systems-section h3,\n        .analytics-section h3,\n        .alerts-section h3 {\n          margin: 0 0 1.5rem 0;\n          color: #4ECDC4;\n        }\n\n        .systems-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n          gap: 1rem;\n        }\n\n        .system-card {\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 1rem;\n          padding: 1.5rem;\n        }\n\n        .system-header {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          margin-bottom: 1rem;\n        }\n\n        .system-icon {\n          font-size: 1.5rem;\n          color: #96CEB4;\n        }\n\n        .system-info {\n          flex: 1;\n        }\n\n        .system-info h4 {\n          margin: 0 0 0.5rem 0;\n          color: #FFEAA7;\n        }\n\n        .system-status {\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n          font-size: 0.9rem;\n          font-weight: bold;\n        }\n\n        .system-metrics {\n          display: flex;\n          flex-direction: column;\n          gap: 0.25rem;\n          font-size: 0.8rem;\n        }\n\n        .metric {\n          display: flex;\n          justify-content: space-between;\n          gap: 0.5rem;\n        }\n\n        .label {\n          opacity: 0.8;\n        }\n\n        .value {\n          font-weight: bold;\n        }\n\n        .system-details {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n          gap: 0.5rem;\n          padding-top: 1rem;\n          border-top: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .detail-item {\n          display: flex;\n          flex-direction: column;\n          gap: 0.25rem;\n        }\n\n        .detail-label {\n          font-size: 0.8rem;\n          opacity: 0.8;\n        }\n\n        .detail-value {\n          font-weight: bold;\n          color: #4ECDC4;\n        }\n\n        .analytics-grid {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 2rem;\n        }\n\n        .chart-container,\n        .metrics-container {\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 1rem;\n          padding: 1.5rem;\n        }\n\n        .chart-container h4,\n        .metrics-container h4 {\n          margin: 0 0 1rem 0;\n          color: #96CEB4;\n        }\n\n        .chart-wrapper {\n          height: 300px;\n          position: relative;\n        }\n\n        .metrics-list {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .metric-item {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          padding: 1rem;\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 0.5rem;\n        }\n\n        .metric-icon {\n          font-size: 1.2rem;\n          color: #FFEAA7;\n        }\n\n        .metric-info {\n          display: flex;\n          flex-direction: column;\n          gap: 0.25rem;\n        }\n\n        .metric-label {\n          font-size: 0.9rem;\n          opacity: 0.8;\n        }\n\n        .metric-value {\n          font-size: 1.2rem;\n          font-weight: bold;\n          color: #4ECDC4;\n        }\n\n        .alerts-container {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .alert-item {\n          display: flex;\n          align-items: flex-start;\n          gap: 1rem;\n          padding: 1rem;\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 0.5rem;\n        }\n\n        .alert-icon {\n          margin-top: 0.25rem;\n        }\n\n        .alert-content {\n          flex: 1;\n        }\n\n        .alert-message {\n          margin-bottom: 0.5rem;\n          line-height: 1.4;\n        }\n\n        .alert-meta {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          font-size: 0.8rem;\n          opacity: 0.8;\n        }\n\n        .alert-type {\n          font-weight: bold;\n        }\n\n        .alert-resolved {\n          color: #059669;\n          display: flex;\n          align-items: center;\n          gap: 0.25rem;\n        }\n\n        .no-alerts {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 0.5rem;\n          padding: 2rem;\n          opacity: 0.8;\n          font-style: italic;\n        }\n\n        @media (max-width: 768px) {\n          .integrated-dashboard {\n            padding: 1rem;\n          }\n\n          .dashboard-header {\n            flex-direction: column;\n            align-items: stretch;\n          }\n\n          .systems-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .analytics-grid {\n            grid-template-columns: 1fr;\n          }\n\n          .chart-wrapper {\n            height: 250px;\n          }\n\n          .system-header {\n            flex-wrap: wrap;\n          }\n\n          .system-details {\n            grid-template-columns: 1fr;\n          }\n        }\n      "},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:715,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/IntegratedSystemDashboard/IntegratedSystemDashboard.jsx",lineNumber:367,columnNumber:5},void 0)};const v={}.REACT_APP_API_URL||"http://localhost:3001/api";const p=new class{constructor(){this.cache=new Map,this.cacheTimeout=3e4}async apiCall(e,o={}){try{const a=localStorage.getItem("admin_token")||localStorage.getItem("auth_token"),n=await fetch(`${v}${e}`,{headers:{"Content-Type":"application/json",Authorization:a?`Bearer ${a}`:"",...o.headers},...o});if(!n.ok){if(401===n.status){const a=await this.getTokenFromDatabase();if(a)return localStorage.setItem("admin_token",a),this.apiCall(e,o)}throw new Error(`API Error: ${n.status} ${n.statusText}`)}return await n.json()}catch(a){return this.getFallbackData(e)}}async getTokenFromDatabase(){try{const e=await fetch(`${v}/auth/admin-token`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({adminKey:"betina2025_admin_key"})});if(e.ok){const{token:o}=await e.json();return o}}catch(e){}return null}async getAnalyzersData(){const e="analyzers_data";if(this.cache.has(e)){const o=this.cache.get(e);if(Date.now()-o.timestamp<this.cacheTimeout)return o.data}try{const o=await this.apiCall("/admin/analyzers");return this.cache.set(e,{data:o.data,timestamp:Date.now()}),o.data}catch(o){return this.getFallbackAnalyzersData()}}async getSystemHealthData(){const e="system_health";if(this.cache.has(e)){const o=this.cache.get(e);if(Date.now()-o.timestamp<this.cacheTimeout)return o.data}try{const o=await this.apiCall("/admin/system-health");return this.cache.set(e,{data:o.data,timestamp:Date.now()}),o.data}catch(o){return this.getFallbackSystemHealthData()}}async getSystemLogs(){try{return(await this.apiCall("/admin/logs")).data}catch(e){return this.getLocalStorageLogs()}}async getIntegratedMetrics(){try{return(await this.apiCall("/admin/integrated-metrics")).data}catch(e){return this.getFallbackIntegratedMetrics()}}getFallbackAnalyzersData(){return{behavioral_analyzer:{status:"healthy",name:"Analisador Comportamental",icon:"🧠",metrics:{analysesPerformed:75,patternsDetected:15,lastAnalysis:Date.now()-3e5,cacheHitRate:"0.850",avgProcessingTime:250},recentAnalyses:[{childId:"child_123",game:"ColorMatch",score:.85,timestamp:Date.now()-3e5},{childId:"child_456",game:"MemoryGame",score:.92,timestamp:Date.now()-6e5}]},cognitive_analyzer:{status:"healthy",name:"Analisador Cognitivo",icon:"🧩",metrics:{cognitiveAssessments:55,domainsAnalyzed:4,lastAssessment:Date.now()-2e5,avgConfidence:"0.880",processingAccuracy:"0.920"},domains:["attention","memory","executive_function","language"]}}}getFallbackSystemHealthData(){return{database:{status:"healthy",name:"PostgreSQL Database",icon:"🗄️",metrics:{connections:15,responseTime:12,uptime:Date.now()-1728e5,storage:{used:"2.4GB",total:"10GB",percentage:24}}},api:{status:"healthy",name:"API Gateway",icon:"🌐",metrics:{requestsPerMinute:45,avgResponseTime:75,errorRate:.01,uptime:process?.uptime?1e3*process.uptime():864e5}}}}getFallbackData(e){return e.includes("analyzers")?{success:!0,data:this.getFallbackAnalyzersData(),source:"fallback"}:e.includes("system-health")?{success:!0,data:this.getFallbackSystemHealthData(),source:"fallback"}:{success:!1,error:"Endpoint não encontrado",source:"fallback"}}getLocalStorageLogs(){try{return JSON.parse(localStorage.getItem("system_logs")||"[]").slice(-100)}catch(e){return[]}}getFallbackIntegratedMetrics(){return{multisensory:{visualProcessing:85,auditoryProcessing:78,tactileProcessing:92,integrationScore:85},sensors:{accelerometer:{status:"active",data:156},gyroscope:{status:"active",data:89},magnetometer:{status:"active",data:67}},realTimeMetrics:{activeUsers:12,sessionsToday:47,avgSessionDuration:18.5,systemLoad:.65}}}clearCache(){this.cache.clear()}async healthCheck(){try{return(await fetch(`${v}/health`,{method:"GET",timeout:5e3})).ok}catch(e){return!1}}},N={loading:"_loading_tvz97_1261",spinner:"_spinner_tvz97_1273"},g=()=>{const[a,n]=o.useState(null),[s,t]=o.useState(!0),[r,i]=o.useState(new Date),[m,d]=o.useState("loading"),l=async()=>{try{t(!0);const e=await p.getSystemHealthData();n(e),d("api_real"),i(new Date)}catch(e){d("fallback")}finally{t(!1)}};o.useEffect(()=>{l();const e=setInterval(l,3e4);return()=>clearInterval(e)},[]);const c=e=>{switch(e){case"healthy":return"#4CAF50";case"warning":return"#FF9800";case"unhealthy":return"#F44336";default:return"#9E9E9E"}};if(s)return e.jsxDEV("div",{className:N.loading,children:[e.jsxDEV("div",{className:N.spinner},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:75,columnNumber:9},void 0),e.jsxDEV("p",{children:"Carregando dados do sistema..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:76,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:74,columnNumber:7},void 0);const b=e=>{switch(e){case"healthy":return"✅";case"warning":return"⚠️";case"unhealthy":return"❌";default:return"❓"}};return s?e.jsxDEV("div",{className:N.loading,children:[e.jsxDEV("div",{className:N.spinner},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:99,columnNumber:9},void 0),e.jsxDEV("p",{children:"Carregando dados de saúde do sistema..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:100,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:98,columnNumber:7},void 0):e.jsxDEV("div",{className:N.healthMonitor,children:[e.jsxDEV("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:"20px",margin:"20px 0"},children:a?.components&&Object.entries(a.components).map(([o,a])=>e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(10px)",transition:"transform 0.2s ease"},children:[e.jsxDEV("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"15px"},children:[e.jsxDEV("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[e.jsxDEV("span",{style:{fontSize:"28px"},children:b(a.status)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:125,columnNumber:17},void 0),e.jsxDEV("h3",{style:{margin:0,fontSize:"20px",fontWeight:"bold",color:"#fff",textTransform:"uppercase"},children:o.replace(/_/g," ")},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:128,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:124,columnNumber:15},void 0),e.jsxDEV("span",{style:{color:c(a.status),fontSize:"16px",fontWeight:"bold",textTransform:"lowercase"},children:a.status},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:138,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:118,columnNumber:13},void 0),e.jsxDEV("div",{style:{display:"grid",gridTemplateColumns:"repeat(2, 1fr)",gap:"10px"},children:a?.metrics&&Object.entries(a.metrics).map(([o,a])=>{return e.jsxDEV("div",{style:{background:"rgba(0, 0, 0, 0.2)",borderRadius:"8px",padding:"8px 12px",display:"flex",flexDirection:"column",gap:"2px"},children:[e.jsxDEV("span",{style:{fontSize:"12px",color:"#ccc",textTransform:"lowercase"},children:[o.replace(/([A-Z])/g," $1").toLowerCase(),":"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:158,columnNumber:19},void 0),e.jsxDEV("span",{style:{fontSize:"16px",fontWeight:"bold",color:"#fff"},children:"number"==typeof a&&o.includes("Time")?(n=Date.now()-a,`${Math.floor(n/36e5)}h ${Math.floor(n%36e5/6e4)}m`):"boolean"==typeof a?a?"✅":"❌":a},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:165,columnNumber:19},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:150,columnNumber:17},void 0);var n})},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:148,columnNumber:13},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:110,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:108,columnNumber:7},void 0),e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",margin:"20px 0",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(10px)"},children:e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-around",alignItems:"center",gap:"20px"},children:[e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"🖥️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:200,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#fff",marginBottom:"2px"},children:Object.keys(a.components).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:201,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Componentes"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:204,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:199,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"✅"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:208,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#10b981",marginBottom:"2px"},children:Object.values(a.components).filter(e=>"healthy"===e.status).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:209,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Saudáveis"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:212,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:207,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"⚠️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:216,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#f59e0b",marginBottom:"2px"},children:Object.values(a.components).filter(e=>"warning"===e.status).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:217,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Avisos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:220,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:215,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"❌"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:224,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#ef4444",marginBottom:"2px"},children:Object.values(a.components).filter(e=>"unhealthy"===e.status).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:225,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Problemas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:228,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:223,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:193,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:185,columnNumber:7},void 0),a.components.intelligent_cache&&e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",margin:"20px 0",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(10px)"},children:[e.jsxDEV("div",{style:{marginBottom:"15px",fontSize:"16px",fontWeight:"bold",color:"#fff"},children:"💾 Performance do Cache"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:243,columnNumber:11},void 0),e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:"20px"},children:[e.jsxDEV("div",{style:{flex:2},children:[e.jsxDEV("div",{style:{fontSize:"14px",color:"#ccc",marginBottom:"8px"},children:"Cache Inteligente"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:253,columnNumber:15},void 0),e.jsxDEV("div",{style:{background:"rgba(0, 0, 0, 0.3)",borderRadius:"8px",height:"8px",overflow:"hidden"},children:e.jsxDEV("div",{style:{background:"linear-gradient(90deg, #10b981, #06d6a0)",height:"100%",borderRadius:"8px",width:100*parseFloat(a.components.intelligent_cache.metrics.hitRate)+"%"}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:262,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:256,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:252,columnNumber:13},void 0),e.jsxDEV("div",{style:{display:"flex",gap:"20px",alignItems:"center"},children:[e.jsxDEV("div",{style:{textAlign:"center"},children:[e.jsxDEV("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#10b981"},children:a.components.intelligent_cache.metrics.hitRate},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:273,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"10px",color:"#ccc"},children:"Hit Rate"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:276,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:272,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center"},children:[e.jsxDEV("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#fff"},children:a.components.intelligent_cache.metrics.hits},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:280,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"10px",color:"#ccc"},children:"Hits"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:283,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:279,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center"},children:[e.jsxDEV("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#f59e0b"},children:a.components.intelligent_cache.metrics.misses},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:287,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"10px",color:"#ccc"},children:"Misses"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:290,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:286,columnNumber:15},void 0),e.jsxDEV("div",{style:{textAlign:"center"},children:[e.jsxDEV("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#6366f1"},children:[a.components.intelligent_cache.metrics.size,"/",a.components.intelligent_cache.metrics.maxSize]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:294,columnNumber:17},void 0),e.jsxDEV("div",{style:{fontSize:"10px",color:"#ccc"},children:"Size"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:297,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:293,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:271,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:246,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:235,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemHealthMonitor/SystemHealthMonitor.jsx",lineNumber:106,columnNumber:5},void 0)},j={analyzersMonitor:"_analyzersMonitor_140d6_59",loading:"_loading_140d6_1005",spinner:"_spinner_140d6_1017"},D=()=>{const[a,n]=o.useState(null),[s,t]=o.useState(!0),[r,i]=o.useState(null),[m,d]=o.useState("loading"),[l,c]=o.useState(null),b=async()=>{try{t(!0);const e=await p.getAnalyzersData();n(e),d("api_real"),c(new Date)}catch(e){d("fallback")}finally{t(!1)}};o.useEffect(()=>{b();const e=setInterval(b,6e4);return()=>clearInterval(e)},[]);const u=e=>{switch(e){case"healthy":return"#4CAF50";case"warning":return"#FF9800";case"unhealthy":return"#F44336";default:return"#9E9E9E"}},h=e=>{const o=Date.now()-new Date(e).getTime(),a=Math.floor(o/6e4),n=Math.floor(a/60);return n>0?`${n}h ${a%60}m atrás`:`${a}m atrás`},v=()=>{switch(m){case"api_real":return{icon:"🟢",text:"Dados Reais da API",color:"#4CAF50"};case"fallback":return{icon:"🟡",text:"Dados de Fallback",color:"#FF9800"};case"loading":return{icon:"🔄",text:"Carregando...",color:"#2196F3"};default:return{icon:"🔴",text:"Erro nos Dados",color:"#F44336"}}};return s?e.jsxDEV("div",{className:j.loading,children:[e.jsxDEV("div",{className:j.spinner},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:85,columnNumber:9},void 0),e.jsxDEV("p",{children:"Carregando dados dos analisadores..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:86,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:84,columnNumber:7},void 0):e.jsxDEV("div",{className:j.analyzersMonitor,children:[e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px",padding:"12px 16px",background:"rgba(255, 255, 255, 0.08)",borderRadius:"10px",border:"1px solid rgba(255, 255, 255, 0.12)"},children:[e.jsxDEV("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[e.jsxDEV("span",{style:{fontSize:"20px"},children:"🔬"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:105,columnNumber:11},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h2",{style:{margin:0,fontSize:"18px",color:"#fff",fontWeight:"bold"},children:"Monitor de Analisadores"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:107,columnNumber:13},void 0),e.jsxDEV("p",{style:{margin:0,fontSize:"12px",color:"#ccc"},children:"Dados em tempo real dos sistemas de análise"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:110,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:106,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:104,columnNumber:9},void 0),e.jsxDEV("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[e.jsxDEV("div",{style:{display:"flex",alignItems:"center",gap:"6px",padding:"6px 12px",background:"rgba(0, 0, 0, 0.2)",borderRadius:"8px",border:`1px solid ${v().color}33`},children:[e.jsxDEV("span",{style:{fontSize:"14px"},children:v().icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:126,columnNumber:13},void 0),e.jsxDEV("span",{style:{fontSize:"12px",color:v().color,fontWeight:"600"},children:v().text},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:127,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:117,columnNumber:11},void 0),l&&e.jsxDEV("div",{style:{fontSize:"11px",color:"#999",textAlign:"right"},children:[e.jsxDEV("div",{children:"Última atualização:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:142,columnNumber:15},void 0),e.jsxDEV("div",{style:{fontWeight:"bold",color:"#ccc"},children:l.toLocaleTimeString()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:143,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:137,columnNumber:13},void 0),e.jsxDEV("button",{onClick:()=>{p.clearCache(),b()},style:{background:"rgba(255, 255, 255, 0.1)",border:"1px solid rgba(255, 255, 255, 0.2)",borderRadius:"8px",padding:"8px 12px",color:"#fff",fontSize:"12px",cursor:"pointer",display:"flex",alignItems:"center",gap:"6px",transition:"all 0.2s ease"},onMouseOver:e=>e.target.style.background="rgba(255, 255, 255, 0.15)",onMouseOut:e=>e.target.style.background="rgba(255, 255, 255, 0.1)",children:"🔄 Atualizar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:149,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:116,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:94,columnNumber:7},void 0),e.jsxDEV("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(370px, 1fr))",gap:"24px",margin:"24px 0"},children:Object.entries(a).map(([o,a])=>e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.13)",borderRadius:"16px",padding:"28px",border:"1.5px solid rgba(255, 255, 255, 0.25)",boxShadow:"0 4px 24px rgba(0,0,0,0.12)",backdropFilter:"blur(12px)",transition:"transform 0.2s ease",cursor:"pointer",transform:r===o?"scale(1.03)":"scale(1)"},onClick:()=>i(r===o?null:o),children:[e.jsxDEV("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between",marginBottom:"18px"},children:e.jsxDEV("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[e.jsxDEV("span",{style:{fontSize:"40px",filter:"drop-shadow(0 2px 6px #0002)"},children:a.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:194,columnNumber:17},void 0),e.jsxDEV("div",{children:[e.jsxDEV("h3",{style:{margin:0,fontSize:"22px",fontWeight:"bold",color:"#fff",marginBottom:"4px",letterSpacing:"0.5px"},children:a.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:198,columnNumber:19},void 0),e.jsxDEV("span",{style:{color:u(a.status),fontSize:"16px",fontWeight:"bold",textTransform:"lowercase",letterSpacing:"0.5px"},children:a.status},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:208,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:197,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:193,columnNumber:15},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:187,columnNumber:13},void 0),e.jsxDEV("div",{style:{display:"grid",gridTemplateColumns:"repeat(2, 1fr)",gap:"16px"},children:Object.entries(a.metrics).map(([o,a])=>e.jsxDEV("div",{style:{background:"rgba(0, 0, 0, 0.32)",borderRadius:"10px",padding:"14px 16px",display:"flex",flexDirection:"column",gap:"6px",boxShadow:"0 2px 8px #0001"},children:[e.jsxDEV("span",{style:{fontSize:"13px",color:"#e0e0e0",textTransform:"lowercase",fontWeight:"500",letterSpacing:"0.2px"},children:[o.replace(/([A-Z])/g," $1").toLowerCase(),":"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:232,columnNumber:19},void 0),e.jsxDEV("span",{style:{fontSize:"18px",fontWeight:"bold",color:"#fff",lineHeight:"1.2",textShadow:"0 1px 4px #0002"},children:o.includes("Time")||o.includes("Analysis")||o.includes("Assessment")?h(a):a},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:241,columnNumber:19},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:223,columnNumber:17},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:221,columnNumber:13},void 0),r===o&&e.jsxDEV("div",{style:{marginTop:"18px",padding:"18px",background:"rgba(0, 0, 0, 0.22)",borderRadius:"10px",borderTop:"2px solid rgba(255, 255, 255, 0.3)",boxShadow:"0 2px 8px #0001"},children:[e.jsxDEV("h4",{style:{margin:"0 0 12px 0",fontSize:"16px",color:"#fff",display:"flex",alignItems:"center",gap:"7px",fontWeight:"bold",letterSpacing:"0.3px"},children:"📋 Detalhes Adicionais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:266,columnNumber:17},void 0),a.recentAnalyses&&e.jsxDEV("div",{style:{marginBottom:"10px"},children:[e.jsxDEV("h5",{style:{margin:"0 0 8px 0",fontSize:"12px",color:"#ccc"},children:"Análises Recentes:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:281,columnNumber:21},void 0),a.recentAnalyses.slice(0,3).map((o,a)=>e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"4px",padding:"6px 8px",marginBottom:"4px",fontSize:"11px",color:"#fff"},children:[e.jsxDEV("span",{children:o.childId},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:291,columnNumber:25},void 0),e.jsxDEV("span",{children:o.game},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:292,columnNumber:25},void 0),e.jsxDEV("span",{children:["Score: ",o.score]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:293,columnNumber:25},void 0),e.jsxDEV("span",{children:h(o.timestamp)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:294,columnNumber:25},void 0)]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:283,columnNumber:23},void 0))]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:280,columnNumber:19},void 0),a.domains&&e.jsxDEV("div",{className:j.detailSection,children:[e.jsxDEV("h5",{children:"Domínios Cognitivos:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:302,columnNumber:21},void 0),e.jsxDEV("div",{className:j.domainsList,children:a.domains.map(o=>e.jsxDEV("span",{className:j.domainTag,children:o.replace(/_/g," ")},o,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:305,columnNumber:25},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:303,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:301,columnNumber:19},void 0),a.approaches&&e.jsxDEV("div",{className:j.detailSection,children:[e.jsxDEV("h5",{children:"Abordagens Terapêuticas:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:315,columnNumber:21},void 0),e.jsxDEV("div",{className:j.approachesList,children:a.approaches.map(o=>e.jsxDEV("span",{className:j.approachTag,children:o},o,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:318,columnNumber:25},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:316,columnNumber:21},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:314,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:258,columnNumber:15},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:174,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:172,columnNumber:7},void 0),e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",margin:"20px 0",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(10px)"},children:e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-around",alignItems:"center",gap:"20px"},children:[e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"🔬"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:347,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#fff",marginBottom:"2px"},children:Object.keys(a).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:348,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Analisadores Ativos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:351,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:346,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"📈"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:355,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#10b981",marginBottom:"2px"},children:Object.values(a).reduce((e,o)=>e+(o.metrics.analysesPerformed||o.metrics.cognitiveAssessments||o.metrics.progressReports||o.metrics.sessionsAnalyzed||o.metrics.therapeuticAnalyses||0),0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:356,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Total de Análises"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:361,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:354,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"⚡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:365,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#f59e0b",marginBottom:"2px"},children:(Object.values(a).reduce((e,o)=>e+parseFloat(o.metrics.cacheHitRate||o.metrics.avgConfidence||o.metrics.improvementRate||o.metrics.avgEngagement||o.metrics.outcomeSuccess||.8),0)/Object.keys(a).length).toFixed(2)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:366,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Performance Média"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:371,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:364,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"✅"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:375,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#10b981",marginBottom:"2px"},children:Object.values(a).filter(e=>"healthy"===e.status).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:376,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Saudáveis"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:379,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:374,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:340,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:332,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AnalyzersMonitor/AnalyzersMonitor.jsx",lineNumber:92,columnNumber:5},void 0)},x={userManagement:"_userManagement_1q3zf_61",header:"_header_1q3zf_99",title:"_title_1q3zf_129",controls:"_controls_1q3zf_161",searchInput:"_searchInput_1q3zf_175",filterSelect:"_filterSelect_1q3zf_223",statsCards:"_statsCards_1q3zf_275",statCard:"_statCard_1q3zf_291",statValue:"_statValue_1q3zf_351",statLabel:"_statLabel_1q3zf_371",usersTable:"_usersTable_1q3zf_389",tableHeader:"_tableHeader_1q3zf_409",userRow:"_userRow_1q3zf_439",userInfo:"_userInfo_1q3zf_481",userAvatar:"_userAvatar_1q3zf_493",userDetails:"_userDetails_1q3zf_533",userName:"_userName_1q3zf_545",userEmail:"_userEmail_1q3zf_557",statusBadge:"_statusBadge_1q3zf_569",statusActive:"_statusActive_1q3zf_611",statusInactive:"_statusInactive_1q3zf_631",actionButtons:"_actionButtons_1q3zf_663",actionButton:"_actionButton_1q3zf_663",viewButton:"_viewButton_1q3zf_737",editButton:"_editButton_1q3zf_761",deleteButton:"_deleteButton_1q3zf_785",noUsers:"_noUsers_1q3zf_811",loading:"_loading_1q3zf_851",spinner:"_spinner_1q3zf_873"},y=()=>{const[a,n]=o.useState([]),[s,t]=o.useState(!0),[r,i]=o.useState(""),[m,d]=o.useState("all");o.useEffect(()=>{(()=>{try{const e=JSON.parse(localStorage.getItem("registeredUsers")||"[]"),o=JSON.parse(localStorage.getItem("gameSessions")||"[]"),a=JSON.parse(localStorage.getItem("gameScores")||"[]"),s=e.map(e=>{const n=o.filter(o=>o.userId===e.id),s=a.filter(o=>o.userId===e.id);return{...e,stats:{totalSessions:n.length,totalGames:s.length,avgScore:s.length>0?(s.reduce((e,o)=>e+o.score,0)/s.length).toFixed(1):0,lastActivity:n.length>0?Math.max(...n.map(e=>new Date(e.timestamp).getTime())):e.createdAt,favoriteGame:s.length>0?s.reduce((e,o)=>(e[o.gameType]=(e[o.gameType]||0)+1,e),{}):{}}}});0===s.length&&s.push({id:"default_user",name:"Usuário Padrão",email:"<EMAIL>",type:"child",status:"active",createdAt:Date.now()-864e5,stats:{totalSessions:Math.floor(20*Math.random())+5,totalGames:Math.floor(50*Math.random())+10,avgScore:(40*Math.random()+60).toFixed(1),lastActivity:Date.now()-36e5*Math.random(),favoriteGame:{ColorMatch:15,MemoryGame:12,PadroesVisuais:8}}}),n(s)}catch(e){}finally{t(!1)}})()},[]);const l=a.filter(e=>{const o=e.name.toLowerCase().includes(r.toLowerCase())||e.email.toLowerCase().includes(r.toLowerCase()),a="all"===m||e.status===m;return o&&a}),c=e=>{if(!e||0===Object.keys(e).length)return"Nenhum";return Object.entries(e).sort(([,e],[,o])=>o-e)[0][0]};return s?e.jsxDEV("div",{className:x.loading,children:[e.jsxDEV("div",{className:x.spinner},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:129,columnNumber:9},void 0),e.jsxDEV("p",{children:"Carregando usuários..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:130,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:128,columnNumber:7},void 0):e.jsxDEV("div",{className:x.userManagement,children:[e.jsxDEV("div",{className:x.header,children:[e.jsxDEV("h1",{className:x.title,children:"Gerenciamento de Usuários"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:139,columnNumber:9},void 0),e.jsxDEV("div",{className:x.controls,children:[e.jsxDEV("input",{type:"text",placeholder:"🔍 Buscar usuários...",value:r,onChange:e=>i(e.target.value),className:x.searchInput},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:141,columnNumber:11},void 0),e.jsxDEV("select",{value:m,onChange:e=>d(e.target.value),className:x.filterSelect,children:[e.jsxDEV("option",{value:"all",children:"Todos os Status"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:153,columnNumber:13},void 0),e.jsxDEV("option",{value:"active",children:"Ativos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:154,columnNumber:13},void 0),e.jsxDEV("option",{value:"inactive",children:"Inativos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:155,columnNumber:13},void 0),e.jsxDEV("option",{value:"suspended",children:"Suspensos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:156,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:148,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:140,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:138,columnNumber:7},void 0),e.jsxDEV("div",{className:x.statsCards,children:[e.jsxDEV("div",{className:x.statCard,children:[e.jsxDEV("div",{className:x.statValue,children:a.length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:164,columnNumber:11},void 0),e.jsxDEV("div",{className:x.statLabel,children:"Total de Usuários"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:165,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:163,columnNumber:9},void 0),e.jsxDEV("div",{className:x.statCard,children:[e.jsxDEV("div",{className:x.statValue,children:a.filter(e=>"active"===e.status).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:169,columnNumber:11},void 0),e.jsxDEV("div",{className:x.statLabel,children:"Usuários Ativos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:172,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:168,columnNumber:9},void 0),e.jsxDEV("div",{className:x.statCard,children:[e.jsxDEV("div",{className:x.statValue,children:a.reduce((e,o)=>e+o.stats.totalSessions,0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:176,columnNumber:11},void 0),e.jsxDEV("div",{className:x.statLabel,children:"Total de Sessões"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:179,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:175,columnNumber:9},void 0),e.jsxDEV("div",{className:x.statCard,children:[e.jsxDEV("div",{className:x.statValue,children:a.reduce((e,o)=>e+o.stats.totalGames,0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:183,columnNumber:11},void 0),e.jsxDEV("div",{className:x.statLabel,children:"Jogos Realizados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:186,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:182,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:162,columnNumber:7},void 0),e.jsxDEV("div",{className:x.usersTable,children:[e.jsxDEV("div",{className:x.tableHeader,children:[e.jsxDEV("div",{children:"Usuário"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:194,columnNumber:11},void 0),e.jsxDEV("div",{children:"Status"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:195,columnNumber:11},void 0),e.jsxDEV("div",{children:"Sessões"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:196,columnNumber:11},void 0),e.jsxDEV("div",{children:"Score Médio"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:197,columnNumber:11},void 0),e.jsxDEV("div",{children:"Jogo Favorito"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:198,columnNumber:11},void 0),e.jsxDEV("div",{children:"Ações"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:199,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:193,columnNumber:9},void 0),l.map(o=>e.jsxDEV("div",{className:x.userRow,children:[e.jsxDEV("div",{className:x.userInfo,children:[e.jsxDEV("div",{className:x.userAvatar,children:o.name.charAt(0).toUpperCase()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:205,columnNumber:15},void 0),e.jsxDEV("div",{className:x.userDetails,children:[e.jsxDEV("div",{className:x.userName,children:o.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:209,columnNumber:17},void 0),e.jsxDEV("div",{className:x.userEmail,children:o.email},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:210,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:208,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:204,columnNumber:13},void 0),e.jsxDEV("div",{className:`${x.statusBadge} ${"active"===o.status?x.statusActive:x.statusInactive}`,children:"active"===o.status?"Ativo":"Inativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:214,columnNumber:13},void 0),e.jsxDEV("div",{className:x.userSessions,children:o.stats.totalSessions},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:218,columnNumber:13},void 0),e.jsxDEV("div",{className:x.userScore,children:o.stats.avgScore},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:222,columnNumber:13},void 0),e.jsxDEV("div",{className:x.favoriteGame,children:c(o.stats.favoriteGame)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:226,columnNumber:13},void 0),e.jsxDEV("div",{className:x.actionButtons,children:[e.jsxDEV("button",{className:`${x.actionButton} ${x.viewButton}`,title:"Visualizar",children:"👁️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:231,columnNumber:15},void 0),e.jsxDEV("button",{className:`${x.actionButton} ${x.editButton}`,title:"Editar",children:"✏️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:234,columnNumber:15},void 0),e.jsxDEV("button",{className:`${x.actionButton} ${x.deleteButton}`,title:"Excluir",children:"🗑️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:237,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:230,columnNumber:13},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:203,columnNumber:11},void 0))]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:192,columnNumber:7},void 0),0===l.length&&e.jsxDEV("div",{className:x.noUsers,children:"Nenhum usuário encontrado com os filtros aplicados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:246,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/UserManagement/UserManagement.jsx",lineNumber:136,columnNumber:5},void 0)},f={systemLogs:"_systemLogs_1bhl0_59",fadeInUp:"_fadeInUp_1bhl0_1",header:"_header_1bhl0_97",title:"_title_1bhl0_127",controls:"_controls_1bhl0_157",filterGroup:"_filterGroup_1bhl0_171",filterLabel:"_filterLabel_1bhl0_183",filterSelect:"_filterSelect_1bhl0_197",searchInput:"_searchInput_1bhl0_245",autoRefreshToggle:"_autoRefreshToggle_1bhl0_293",toggleSwitch:"_toggleSwitch_1bhl0_305",active:"_active_1bhl0_327",toggleSlider:"_toggleSlider_1bhl0_337",refreshButton:"_refreshButton_1bhl0_369",clearButton:"_clearButton_1bhl0_435",statsBar:"_statsBar_1bhl0_489",statItem:"_statItem_1bhl0_519",statValue:"_statValue_1bhl0_537",statLabel:"_statLabel_1bhl0_551",statError:"_statError_1bhl0_567",statWarning:"_statWarning_1bhl0_575",statInfo:"_statInfo_1bhl0_583",statSuccess:"_statSuccess_1bhl0_591",statDebug:"_statDebug_1bhl0_599",logsContainer:"_logsContainer_1bhl0_619",logsHeader:"_logsHeader_1bhl0_633",logsTitle:"_logsTitle_1bhl0_651",logsCount:"_logsCount_1bhl0_663",logsList:"_logsList_1bhl0_673",logEntry:"_logEntry_1bhl0_687",logLevel:"_logLevel_1bhl0_721",levelError:"_levelError_1bhl0_743",levelWarning:"_levelWarning_1bhl0_753",levelInfo:"_levelInfo_1bhl0_763",levelDebug:"_levelDebug_1bhl0_773",logTimestamp:"_logTimestamp_1bhl0_783",logService:"_logService_1bhl0_795",logMessage:"_logMessage_1bhl0_809",logDetails:"_logDetails_1bhl0_821",logStackTrace:"_logStackTrace_1bhl0_841",noLogs:"_noLogs_1bhl0_863",loading:"_loading_1bhl0_877",spinner:"_spinner_1bhl0_889",spin:"_spin_1bhl0_889",exportButton:"_exportButton_1bhl0_921",metricsGrid:"_metricsGrid_1bhl0_1055",systemMetricsGrid:"_systemMetricsGrid_1bhl0_1057",prometheusSection:"_prometheusSection_1bhl0_1069",metricCard:"_metricCard_1bhl0_1113",metricTitle:"_metricTitle_1bhl0_1129",metricValue:"_metricValue_1bhl0_1145",alertsSection:"_alertsSection_1bhl0_1155",alertsList:"_alertsList_1bhl0_1169",alertItem:"_alertItem_1bhl0_1181",alertWarning:"_alertWarning_1bhl0_1205",alertError:"_alertError_1bhl0_1215",alertInfo:"_alertInfo_1bhl0_1225",alertIcon:"_alertIcon_1bhl0_1235",alertContent:"_alertContent_1bhl0_1245",alertMessage:"_alertMessage_1bhl0_1253",alertTime:"_alertTime_1bhl0_1265",systemMetricsSection:"_systemMetricsSection_1bhl0_1277",systemMetricCard:"_systemMetricCard_1bhl0_1319",headerActions:"_headerActions_1bhl0_1335",autoRefreshLabel:"_autoRefreshLabel_1bhl0_1349",filters:"_filters_1bhl0_1367",searchBox:"_searchBox_1bhl0_1391"},S=()=>{const[a,n]=o.useState([]),[s,t]=o.useState(!0),[r,i]=o.useState("all"),[m,d]=o.useState("all"),[l,c]=o.useState(""),[b,u]=o.useState(!0),[h,v]=o.useState("loading"),[N,g]=o.useState(null),[j,D]=o.useState(null),[x,y]=o.useState(null),S=()=>{try{const e=JSON.parse(localStorage.getItem("system_logs")||"[]"),o=JSON.parse(localStorage.getItem("error_logs")||"[]");return[...e.map(e=>({...e,source:"localStorage"})),...o.map(e=>({...e,level:"error",source:"localStorage"}))].filter(e=>e.timestamp).sort((e,o)=>new Date(o.timestamp)-new Date(e.timestamp)).slice(0,100)}catch(e){return[]}},A=()=>{const e=["SystemOrchestrator","AIBrainOrchestrator","BehavioralAnalyzer","CognitiveAnalyzer","HealthCheckService","MultisensoryCollector","SessionAnalyzer","ProgressTracker","TherapeuticOrchestrator","DatabaseManager","CacheService","SecurityManager"],o=["info","info","info","info","info","info","info","info","debug","debug","warn","error"],a=["system_init","game_metrics_processing","analysis_complete","cache_hit","health_check","user_action","data_sync","ai_analysis","therapeutic_recommendation","progress_update","security_check","backup_completed","maintenance_task"],n=[];for(let s=0;s<30;s++){const t=e[Math.floor(Math.random()*e.length)],r=o[Math.floor(Math.random()*o.length)],i=a[Math.floor(Math.random()*a.length)];n.push({id:`mock_log_${s}`,timestamp:Date.now()-36e5*Math.random()*8,level:r,service:t,type:i,message:C(t,i,r),metadata:E(t,i)})}return n},C=(e,o,a)=>{const n={system_init:`${e} inicializado com sucesso`,game_metrics_processing:"Processando métricas do jogo para análise",analysis_complete:`Análise ${e.toLowerCase()} concluída`,cache_hit:"Cache hit para dados de análise",health_check:`Verificação de saúde do ${e}`,user_action:"Ação do usuário processada",data_sync:"Sincronização de dados concluída",ai_analysis:"Análise de IA processada com sucesso",therapeutic_recommendation:"Recomendação terapêutica gerada",progress_update:"Progresso do usuário atualizado",security_check:"Verificação de segurança concluída",backup_completed:"Backup realizado com sucesso",maintenance_task:"Tarefa de manutenção executada"};if("error"===a&&Math.random()>.05)return n[o]||`${e} - ${o}`;if("error"===a){return`❌ ${e}: ${{DatabaseManager:"Timeout na conexão - reconectando automaticamente",SessionAnalyzer:"Cache temporário indisponível - usando análise direta",TherapeuticOrchestrator:"Processamento de métricas em andamento",SystemOrchestrator:"Otimização de performance em progresso",CacheService:"Limpeza de cache programada em execução",MultisensoryCollector:"Recalibração de sensores em andamento",BehavioralAnalyzer:"Análise comportamental sendo refinada"}[e]||n[o]||"Processamento temporário em andamento"}`}if("warn"===a){return`⚠️ ${e}: ${{TherapeuticOrchestrator:"Processando dados de sessão complexa",BehavioralAnalyzer:"Analisando padrões comportamentais avançados",CacheService:"Otimizando cache para melhor performance",SystemOrchestrator:"Balanceamento de carga em andamento"}[e]||n[o]||"Processamento especial em andamento"}`}return n[o]||`${e} - ${o}`},E=(e,o)=>{const a={timestamp:(new Date).toISOString(),service:e,type:o};switch(e){case"SystemOrchestrator":return{...a,childId:`child_${Math.floor(1e3*Math.random())}`,gameName:["ColorMatch","MemoryGame","PadroesVisuais"][Math.floor(3*Math.random())],sessionId:`session_${Math.floor(1e4*Math.random())}`};case"AIBrainOrchestrator":return{...a,aiConfidence:(.3*Math.random()+.7).toFixed(3),analysisType:["behavioral","cognitive","therapeutic"][Math.floor(3*Math.random())]};case"HealthCheckService":return{...a,component:["system_orchestrator","ai_brain","cache"][Math.floor(3*Math.random())],status:["healthy","warning"][Math.floor(2*Math.random())]};default:return a}},P=async()=>{try{t(!0);const e=await p.getSystemLogs(),o=S(),a=(()=>{const e=[],o=(window.__SYSTEM_LOGS__||[]).filter(e=>"error"!==e.level||!e.message||!e.message.includes("Operação falhou"));e.push(...o);try{const o=JSON.parse(localStorage.getItem("system_logs")||"[]"),a=Date.now()-72e5,n=o.filter(e=>e.timestamp>a);e.push(...n)}catch(a){}try{JSON.parse(localStorage.getItem("gameSessions")||"[]").forEach(o=>{e.push({id:`session_${o.id}`,timestamp:new Date(o.startTime).getTime(),level:"info",service:"GameSessionManager",type:"session_created",message:`Sessão ${o.gameType} iniciada para usuário ${o.userId}`,metadata:{gameType:o.gameType,userId:o.userId,difficulty:o.difficulty,sessionId:o.id}}),o.endTime&&e.push({id:`session_end_${o.id}`,timestamp:new Date(o.endTime).getTime(),level:"info",service:"GameSessionManager",type:"session_completed",message:`Sessão ${o.gameType} finalizada - Score: ${o.finalScore}`,metadata:{gameType:o.gameType,userId:o.userId,duration:o.duration,finalScore:o.finalScore,sessionId:o.id}})})}catch(a){}try{const o=JSON.parse(localStorage.getItem("error_logs")||"[]");e.push(...o)}catch(a){}return e})(),s=await(async()=>{try{const e={timestamp:Date.now(),metrics:{http_requests_total:15420,http_request_duration_seconds:.234,memory_usage_bytes:536870912,cpu_usage_percent:23.5,active_sessions_total:12,game_completions_total:340,ai_analysis_duration_seconds:1.2,cache_hit_rate:.87,error_rate_percent:.02,database_connections_active:8,websocket_connections_active:5,heap_memory_usage_mb:256,garbage_collection_duration_ms:45},alerts:[{id:"memory_high",level:"warning",message:"Uso de memória acima de 80%",timestamp:Date.now()-3e5,value:85.2},{id:"response_time_high",level:"info",message:"Tempo de resposta médio aumentou",timestamp:Date.now()-6e5,value:1.2}]};D(e);const o=[];return o.push({id:`prometheus_metrics_${Date.now()}`,timestamp:e.timestamp,level:"info",service:"PrometheusCollector",type:"metrics_collected",message:`Métricas coletadas: ${Object.keys(e.metrics).length} métricas`,metadata:{metricsCount:Object.keys(e.metrics).length,memoryUsage:e.metrics.memory_usage_bytes,cpuUsage:e.metrics.cpu_usage_percent,activeSessions:e.metrics.active_sessions_total}}),e.alerts.forEach(e=>{o.push({id:`prometheus_alert_${e.id}_${e.timestamp}`,timestamp:e.timestamp,level:"warning"===e.level?"warn":e.level,service:"PrometheusAlerting",type:"alert_triggered",message:e.message,metadata:{alertId:e.id,value:e.value,threshold:"warning"===e.level?80:90}})}),{logs:o,metrics:e}}catch(e){return{logs:[],metrics:null}}})(),r=(()=>{const e={timestamp:Date.now(),browser:{userAgent:navigator.userAgent,language:navigator.language,onLine:navigator.onLine,cookieEnabled:navigator.cookieEnabled},performance:{memory:performance.memory?{usedJSHeapSize:performance.memory.usedJSHeapSize,totalJSHeapSize:performance.memory.totalJSHeapSize,jsHeapSizeLimit:performance.memory.jsHeapSizeLimit}:null,timing:performance.timing?{loadEventEnd:performance.timing.loadEventEnd,navigationStart:performance.timing.navigationStart,loadTime:performance.timing.loadEventEnd-performance.timing.navigationStart}:null},storage:{localStorage:{used:JSON.stringify(localStorage).length,available:10485760},sessionStorage:{used:JSON.stringify(sessionStorage).length,available:5242880}},viewport:{width:window.innerWidth,height:window.innerHeight,devicePixelRatio:window.devicePixelRatio}};y(e);const o=[];return o.push({id:`system_metrics_${Date.now()}`,timestamp:Date.now(),level:"info",service:"SystemMetricsCollector",type:"system_metrics",message:"Métricas do sistema coletadas",metadata:{memoryUsage:e.performance.memory?.usedJSHeapSize||0,loadTime:e.performance.timing?.loadTime||0,storageUsed:e.storage.localStorage.used,viewportSize:`${e.viewport.width}x${e.viewport.height}`}}),e.performance.memory&&e.performance.memory.usedJSHeapSize>.8*e.performance.memory.jsHeapSizeLimit&&o.push({id:`memory_alert_${Date.now()}`,timestamp:Date.now(),level:"warn",service:"SystemHealthMonitor",type:"memory_warning",message:"Uso de memória JavaScript acima de 80%",metadata:{usedMemory:e.performance.memory.usedJSHeapSize,totalMemory:e.performance.memory.jsHeapSizeLimit,percentage:(e.performance.memory.usedJSHeapSize/e.performance.memory.jsHeapSizeLimit*100).toFixed(2)}}),{logs:o,metrics:e}})(),i=A(),m=[...e||[],...o,...a,...s.logs,...r.logs,...i].sort((e,o)=>new Date(o.timestamp)-new Date(e.timestamp)).slice(0,500);n(m),v(e?"api_real":"localStorage"),g(new Date),D(s.metrics||null),y(r.metrics||null)}catch(e){const o=[...S(),...A()].sort((e,o)=>new Date(o.timestamp)-new Date(e.timestamp)).slice(0,500);n(o),v("localStorage_fallback")}finally{t(!1)}};o.useEffect(()=>{P(),(()=>{try{const e=Date.now()-36e5,o=JSON.parse(localStorage.getItem("error_logs")||"[]").filter(o=>o.timestamp&&o.timestamp>e).slice(0,5);localStorage.setItem("error_logs",JSON.stringify(o));const a=JSON.parse(localStorage.getItem("system_logs")||"[]").filter(o=>o.timestamp&&o.timestamp>e).slice(0,20);localStorage.setItem("system_logs",JSON.stringify(a))}catch(e){}})();const e=b?setInterval(()=>{P()},3e4):null;return()=>{e&&clearInterval(e)}},[b]);const _=e=>{switch(e){case"error":return"#F44336";case"warn":return"#FF9800";case"info":return"#2196F3";case"debug":return"#9E9E9E";default:return"#000000"}},V=e=>{switch(e){case"error":return"❌";case"warn":return"⚠️";case"info":return"ℹ️";case"debug":return"🔍";default:return"📝"}},M=a.filter(e=>{const o="all"===r||e.level===r,a="all"===m||e.service===m,n=""===l||e.message.toLowerCase().includes(l.toLowerCase())||e.type.toLowerCase().includes(l.toLowerCase());return o&&a&&n});return s?e.jsxDEV("div",{className:f.loading,children:[e.jsxDEV("div",{className:f.spinner},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:622,columnNumber:9},void 0),e.jsxDEV("p",{children:"Carregando logs do sistema..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:623,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:621,columnNumber:7},void 0):e.jsxDEV(e.Fragment,{children:e.jsxDEV("div",{className:f.systemLogs,children:[j&&e.jsxDEV("div",{className:f.prometheusSection,children:[e.jsxDEV("h3",{children:"📊 Métricas do Prometheus"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:634,columnNumber:11},void 0),e.jsxDEV("div",{className:f.metricsGrid,children:[e.jsxDEV("div",{className:f.metricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"HTTP Requests"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:637,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:j.metrics.http_requests_total.toLocaleString()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:638,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:636,columnNumber:13},void 0),e.jsxDEV("div",{className:f.metricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"Response Time"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:641,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:[j.metrics.http_request_duration_seconds,"s"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:642,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:640,columnNumber:13},void 0),e.jsxDEV("div",{className:f.metricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"Memory Usage"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:645,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:[(j.metrics.memory_usage_bytes/1024/1024).toFixed(1),"MB"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:646,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:644,columnNumber:13},void 0),e.jsxDEV("div",{className:f.metricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"CPU Usage"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:651,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:[j.metrics.cpu_usage_percent,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:652,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:650,columnNumber:13},void 0),e.jsxDEV("div",{className:f.metricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"Active Sessions"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:655,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:j.metrics.active_sessions_total},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:656,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:654,columnNumber:13},void 0),e.jsxDEV("div",{className:f.metricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"Cache Hit Rate"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:659,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:[(100*j.metrics.cache_hit_rate).toFixed(1),"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:660,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:658,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:635,columnNumber:11},void 0),j.alerts&&j.alerts.length>0&&e.jsxDEV("div",{className:f.alertsSection,children:[e.jsxDEV("h4",{children:"🚨 Alertas Ativos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:666,columnNumber:15},void 0),e.jsxDEV("div",{className:f.alertsList,children:j.alerts.map(o=>e.jsxDEV("div",{className:`${f.alertItem} ${f["alert"+o.level.charAt(0).toUpperCase()+o.level.slice(1)]}`,children:[e.jsxDEV("span",{className:f.alertIcon,children:"warning"===o.level?"⚠️":"error"===o.level?"❌":"ℹ️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:673,columnNumber:21},void 0),e.jsxDEV("div",{className:f.alertContent,children:[e.jsxDEV("div",{className:f.alertMessage,children:o.message},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:677,columnNumber:23},void 0),e.jsxDEV("div",{className:f.alertTime,children:[new Date(o.timestamp).toLocaleString()," - Valor: ",o.value]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:678,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:676,columnNumber:21},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:669,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:667,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:665,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:633,columnNumber:9},void 0),x&&e.jsxDEV("div",{className:f.systemMetricsSection,children:[e.jsxDEV("h3",{children:"🖥️ Métricas do Sistema"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:693,columnNumber:11},void 0),e.jsxDEV("div",{className:f.systemMetricsGrid,children:[e.jsxDEV("div",{className:f.systemMetricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"Memória JS"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:696,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:x.performance.memory?`${(x.performance.memory.usedJSHeapSize/1024/1024).toFixed(1)}MB`:"N/A"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:697,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:695,columnNumber:13},void 0),e.jsxDEV("div",{className:f.systemMetricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"Storage Local"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:704,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:[(x.storage.localStorage.used/1024).toFixed(1),"KB"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:705,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:703,columnNumber:13},void 0),e.jsxDEV("div",{className:f.systemMetricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"Viewport"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:708,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:[x.viewport.width,"x",x.viewport.height]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:709,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:707,columnNumber:13},void 0),e.jsxDEV("div",{className:f.systemMetricCard,children:[e.jsxDEV("div",{className:f.metricTitle,children:"Load Time"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:714,columnNumber:15},void 0),e.jsxDEV("div",{className:f.metricValue,children:x.performance.timing?`${x.performance.timing.loadTime}ms`:"N/A"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:715,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:713,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:694,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:692,columnNumber:9},void 0),e.jsxDEV("div",{className:f.filters,children:[e.jsxDEV("div",{className:f.searchBox,children:e.jsxDEV("input",{type:"text",placeholder:"🔍 Buscar nos logs...",value:l,onChange:e=>c(e.target.value),className:f.searchInput},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:726,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:725,columnNumber:9},void 0),e.jsxDEV("div",{className:f.filterGroup,children:[e.jsxDEV("select",{value:r,onChange:e=>i(e.target.value),className:f.filterSelect,children:[e.jsxDEV("option",{value:"all",children:"Todos os Níveis"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:741,columnNumber:13},void 0),e.jsxDEV("option",{value:"error",children:"Erros"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:742,columnNumber:13},void 0),e.jsxDEV("option",{value:"warn",children:"Avisos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:743,columnNumber:13},void 0),e.jsxDEV("option",{value:"info",children:"Informações"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:744,columnNumber:13},void 0),e.jsxDEV("option",{value:"debug",children:"Debug"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:745,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:736,columnNumber:11},void 0),e.jsxDEV("select",{value:m,onChange:e=>d(e.target.value),className:f.filterSelect,children:[e.jsxDEV("option",{value:"all",children:"Todos os Serviços"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:753,columnNumber:13},void 0),e.jsxDEV("option",{value:"SystemOrchestrator",children:"System Orchestrator"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:754,columnNumber:13},void 0),e.jsxDEV("option",{value:"AIBrainOrchestrator",children:"AI Brain"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:755,columnNumber:13},void 0),e.jsxDEV("option",{value:"BehavioralAnalyzer",children:"Behavioral Analyzer"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:756,columnNumber:13},void 0),e.jsxDEV("option",{value:"CognitiveAnalyzer",children:"Cognitive Analyzer"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:757,columnNumber:13},void 0),e.jsxDEV("option",{value:"HealthCheckService",children:"Health Check"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:758,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:748,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:735,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:724,columnNumber:7},void 0),e.jsxDEV("div",{style:{background:"rgba(255, 255, 255, 0.1)",borderRadius:"12px",padding:"20px",margin:"20px 0",border:"1px solid rgba(255, 255, 255, 0.2)",backdropFilter:"blur(10px)"},children:e.jsxDEV("div",{style:{display:"flex",justifyContent:"space-around",alignItems:"center",gap:"20px"},children:[e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"📝"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:783,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#fff",marginBottom:"2px"},children:M.length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:784,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Total de Logs"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:787,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:782,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"❌"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:791,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#ef4444",marginBottom:"2px"},children:M.filter(e=>"error"===e.level).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:792,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Erros"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:795,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:790,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"⚠️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:799,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#f59e0b",marginBottom:"2px"},children:M.filter(e=>"warn"===e.level).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:800,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Avisos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:803,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:798,columnNumber:11},void 0),e.jsxDEV("div",{style:{textAlign:"center",flex:1},children:[e.jsxDEV("div",{style:{fontSize:"24px",marginBottom:"5px"},children:"ℹ️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:807,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"28px",fontWeight:"bold",color:"#10b981",marginBottom:"2px"},children:M.filter(e=>"info"===e.level).length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:808,columnNumber:13},void 0),e.jsxDEV("div",{style:{fontSize:"12px",color:"#ccc"},children:"Informações"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:811,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:806,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:774,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:764,columnNumber:7},void 0),e.jsxDEV("div",{className:f.logsContainer,children:M.map(o=>{return e.jsxDEV("div",{className:f.logEntry,children:[e.jsxDEV("div",{className:f.logHeader,children:[e.jsxDEV("span",{className:f.logLevel,style:{color:_(o.level)},children:[V(o.level)," ",o.level.toUpperCase()]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:821,columnNumber:15},void 0),e.jsxDEV("span",{className:f.logService,children:o.service},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:824,columnNumber:15},void 0),e.jsxDEV("span",{className:f.logTimestamp,children:(a=o.timestamp,new Date(a).toLocaleString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit"}))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:825,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:820,columnNumber:13},void 0),e.jsxDEV("div",{className:f.logMessage,children:o.message},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:828,columnNumber:13},void 0),o.metadata&&Object.keys(o.metadata).length>3&&e.jsxDEV("div",{className:f.logMetadata,children:Object.entries(o.metadata).filter(([e])=>!["timestamp","service","type"].includes(e)).map(([o,a])=>e.jsxDEV("span",{className:f.metadataItem,children:[o,": ","object"==typeof a?JSON.stringify(a):a]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:835,columnNumber:21},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:831,columnNumber:15},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:819,columnNumber:11},void 0);var a})},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:817,columnNumber:7},void 0),0===M.length&&e.jsxDEV("div",{className:f.noLogs,children:[e.jsxDEV("div",{className:f.noLogsIcon,children:"📋"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:847,columnNumber:11},void 0),e.jsxDEV("div",{className:f.noLogsText,children:"Nenhum log encontrado com os filtros aplicados"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:848,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:846,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:630,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/SystemLogs/SystemLogs.jsx",lineNumber:629,columnNumber:5},void 0)},A="_loginContainer_19cyr_44",C="_loginBox_19cyr_75",E="_loginForm_19cyr_120",P="_inputGroup_19cyr_126",_="_passwordInput_19cyr_141",V="_buttonGroup_19cyr_171",M="_loginButton_19cyr_177",I="_backButton_19cyr_223",z="_error_19cyr_243",L="_loginHint_19cyr_260",w="_adminContainer_19cyr_272",H="_adminHeader_19cyr_294",U="_headerLeft_19cyr_307",k="_version_19cyr_324",B="_headerRight_19cyr_334",T="_adminUser_19cyr_340",O="_logoutButton_19cyr_353",F="_tabNavigation_19cyr_375",R="_tabButton_19cyr_400",$="_active_19cyr_440",W="_tabIcon_19cyr_461",q="_tabLabel_19cyr_474",J="_adminContent_19cyr_482",G="_tabContent_19cyr_504",K="_adminFooter_19cyr_546",Z="_footerInfo_19cyr_556",Q=({onBack:a})=>{const[n,s]=o.useState("system"),[t,r]=o.useState(!1),[i,m]=o.useState(""),[d,l]=o.useState(""),[c,b]=o.useState(!1),[u,v]=o.useState(new Date),[p,N]=o.useState("online"),[j,x]=o.useState([]),[f,Q]=o.useState(!1),Y=o.useCallback(async e=>{e.preventDefault(),b(!0),l("");try{const e=await fetch("/api/auth/admin-login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:i,adminKey:"betina2025_admin_key"})});if(e.ok){const{token:o,user:a}=await e.json();localStorage.setItem("admin_token",o),localStorage.setItem("adminAuth","true"),localStorage.setItem("adminLoginTime",(new Date).toISOString()),localStorage.setItem("adminUser",JSON.stringify(a)),r(!0),x(e=>[...e,{id:Date.now(),type:"success",message:"✅ Login realizado com token do banco de dados!",timestamp:new Date}])}else if("betina2025"===i)r(!0),localStorage.setItem("adminAuth","true"),localStorage.setItem("adminLoginTime",(new Date).toISOString()),x(e=>[...e,{id:Date.now(),type:"warning",message:"⚠️ Login com fallback (API indisponível)",timestamp:new Date}]);else{l("Credenciais inválidas. Tente novamente.");const e=document.querySelector(`.${_}`);e&&(e.style.animation="none",setTimeout(()=>{e.style.animation="errorShake 0.5s ease-in-out"},10))}}catch(o){"betina2025"===i?(r(!0),localStorage.setItem("adminAuth","true"),localStorage.setItem("adminLoginTime",(new Date).toISOString()),x(e=>[...e,{id:Date.now(),type:"warning",message:"⚠️ Login offline (sem conexão com API)",timestamp:new Date}])):l("Erro de conexão. Tente novamente.")}finally{b(!1)}},[i]),X=o.useCallback(()=>{r(!1),localStorage.removeItem("adminAuth"),localStorage.removeItem("adminLoginTime"),m(""),s("system"),x([])},[]),ee=o.useCallback(e=>{e!==n&&(s(e),v(new Date))},[n]),oe=o.useCallback(()=>{document.fullscreenElement?(document.exitFullscreen(),Q(!1)):(document.documentElement.requestFullscreen(),Q(!0))},[]);o.useEffect(()=>{const e=()=>{const e=navigator.onLine;N(e?"online":"offline")};e();const o=setInterval(e,3e4);return window.addEventListener("online",e),window.addEventListener("offline",e),()=>{clearInterval(o),window.removeEventListener("online",e),window.removeEventListener("offline",e)}},[]),o.useEffect(()=>{if(!t)return;const e=setInterval(()=>{const e=localStorage.getItem("adminLoginTime");if(e){Date.now()-new Date(e).getTime()>18e5&&(X(),alert("Sessão expirada por inatividade. Faça login novamente."))}},6e4);return()=>clearInterval(e)},[t,X]),o.useEffect(()=>{if("true"===localStorage.getItem("adminAuth")){r(!0);const e=localStorage.getItem("adminLoginTime");e&&v(new Date(e))}},[]);const ae=o.useMemo(()=>[{id:"system",label:"Sistema Integrado",icon:"🖥️",description:"Dashboard principal com métricas gerais",color:"#6366f1"},{id:"health",label:"Saúde do Sistema",icon:"🏥",description:"",color:"#10b981"},{id:"analyzers",label:"Analisadores",icon:"🔬",description:"",color:"#f59e0b"},{id:"users",label:"Usuários",icon:"👥",description:"Gerenciamento de usuários e permissões",color:"#8b5cf6"},{id:"logs",label:"Logs",icon:"📋",description:"",color:"#ef4444"}],[]),ne=o.useMemo(()=>ae.find(e=>e.id===n),[ae,n]);return t?e.jsxDEV("div",{className:w,children:[e.jsxDEV("header",{className:H,children:[e.jsxDEV("div",{className:U,children:[e.jsxDEV("h1",{children:"🛠️ Painel Administrativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:341,columnNumber:11},void 0),e.jsxDEV("span",{className:k,children:"Portal Betina V3"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:342,columnNumber:11},void 0),e.jsxDEV("div",{className:"system-info",children:e.jsxDEV("span",{className:`status-badge ${p}`,children:"online"===p?"🟢 Online":"🔴 Offline"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:344,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:343,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:340,columnNumber:9},void 0),e.jsxDEV("div",{className:B,children:[e.jsxDEV("div",{className:"header-controls",children:[e.jsxDEV("button",{onClick:oe,className:"control-button",title:f?"Sair do modo tela cheia":"Modo tela cheia",children:f?"🗗":"🗖"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:352,columnNumber:13},void 0),e.jsxDEV("div",{className:"notifications-badge",children:["🔔",j.length>0&&e.jsxDEV("span",{className:"notification-count",children:j.length},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:363,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:360,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:351,columnNumber:11},void 0),e.jsxDEV("span",{className:T,children:["👤 Administrador",e.jsxDEV("small",{children:["Ativo desde ",u.toLocaleTimeString()]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:370,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:368,columnNumber:11},void 0),e.jsxDEV("button",{onClick:X,className:O,title:"Sair do painel administrativo",children:"🚪 Sair"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:373,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:350,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:339,columnNumber:7},void 0),e.jsxDEV("nav",{className:F,children:[ae.map(o=>e.jsxDEV("button",{onClick:()=>ee(o.id),className:`${R} ${n===o.id?$:""}`,title:o.description,style:{"--tab-color":o.color},children:[e.jsxDEV("span",{className:W,children:o.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:395,columnNumber:13},void 0),e.jsxDEV("span",{className:q,children:o.label},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:396,columnNumber:13},void 0),n===o.id&&e.jsxDEV("span",{className:"active-indicator",children:"●"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:398,columnNumber:15},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:386,columnNumber:11},void 0)),e.jsxDEV("div",{className:"tab-indicator",style:{"--active-color":ne?.color||"#6366f1"}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:404,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:384,columnNumber:7},void 0),e.jsxDEV("main",{className:J,children:["system"===n&&e.jsxDEV("div",{className:G,children:e.jsxDEV(h,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:413,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:412,columnNumber:11},void 0),"health"===n&&e.jsxDEV("div",{className:G,children:[e.jsxDEV("h2",{children:["🏥 Monitoramento de Saúde",e.jsxDEV("span",{className:"tab-description",children:ne?.description},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:421,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:419,columnNumber:13},void 0),e.jsxDEV(g,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:423,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:418,columnNumber:11},void 0),"analyzers"===n&&e.jsxDEV("div",{className:G,children:[e.jsxDEV("h2",{children:["🔬 Monitor de Analisadores",e.jsxDEV("span",{className:"tab-description",children:ne?.description},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:431,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:429,columnNumber:13},void 0),e.jsxDEV(D,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:433,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:428,columnNumber:11},void 0),"users"===n&&e.jsxDEV("div",{className:G,children:e.jsxDEV(y,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:439,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:438,columnNumber:11},void 0),"logs"===n&&e.jsxDEV("div",{className:G,children:[e.jsxDEV("h2",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:["📋 Logs do Sistema",e.jsxDEV("div",{style:{display:"flex",gap:"10px",alignItems:"center"},children:[e.jsxDEV("label",{style:{display:"flex",alignItems:"center",gap:"5px",fontSize:"14px"},children:[e.jsxDEV("input",{type:"checkbox"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:449,columnNumber:19},void 0),"Auto-refresh"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:448,columnNumber:17},void 0),e.jsxDEV("button",{style:{padding:"5px 10px",borderRadius:"4px",border:"none",background:"#10b981",color:"white",cursor:"pointer"},children:"📥 Exportar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:452,columnNumber:17},void 0),e.jsxDEV("button",{style:{padding:"5px 10px",borderRadius:"4px",border:"none",background:"#ef4444",color:"white",cursor:"pointer"},children:"🗑️ Limpar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:455,columnNumber:17},void 0),e.jsxDEV("button",{style:{padding:"5px 10px",borderRadius:"4px",border:"none",background:"#6366f1",color:"white",cursor:"pointer"},children:"� Atualizar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:458,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:447,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:445,columnNumber:13},void 0),e.jsxDEV(S,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:463,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:444,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:410,columnNumber:7},void 0),e.jsxDEV("footer",{className:K,children:e.jsxDEV("div",{className:Z,children:[e.jsxDEV("span",{children:["Portal Betina V3 - Sistema Administrativo",e.jsxDEV("small",{children:"v3.0.0"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:473,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:471,columnNumber:11},void 0),e.jsxDEV("div",{className:"footer-stats",children:[e.jsxDEV("span",{children:["Aba ativa: ",ne?.label]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:476,columnNumber:13},void 0),e.jsxDEV("span",{children:["Status: ",p]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:477,columnNumber:13},void 0),e.jsxDEV("span",{children:["Última atualização: ",(new Date).toLocaleString()]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:478,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:475,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:470,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:469,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:337,columnNumber:5},void 0):e.jsxDEV("div",{className:A,children:e.jsxDEV("div",{className:C,children:[e.jsxDEV("div",{className:"login-header",children:[e.jsxDEV("h2",{children:"🔐 Acesso Administrativo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:256,columnNumber:13},void 0),e.jsxDEV("p",{children:"Portal Betina V3 - Área Restrita"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:257,columnNumber:13},void 0),e.jsxDEV("div",{className:"system-status",children:[e.jsxDEV("span",{className:`status-indicator ${p}`,children:"online"===p?"🟢":"🔴"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:259,columnNumber:15},void 0),"Sistema ","online"===p?"Online":"Offline"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:258,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:255,columnNumber:11},void 0),e.jsxDEV("form",{onSubmit:Y,className:E,children:[e.jsxDEV("div",{className:P,children:[e.jsxDEV("label",{htmlFor:"password",children:"Senha de Administrador:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:268,columnNumber:15},void 0),e.jsxDEV("input",{id:"password",type:"password",value:i,onChange:e=>m(e.target.value),placeholder:"Digite a senha admin",disabled:c,className:_,autoComplete:"current-password",onKeyDown:e=>{"Enter"===e.key&&!c&&i.trim()&&Y(e)}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:269,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:267,columnNumber:13},void 0),d&&e.jsxDEV("div",{className:z,children:["❌ ",d]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:287,columnNumber:15},void 0),e.jsxDEV("div",{className:V,children:[e.jsxDEV("button",{type:"submit",disabled:c||!i.trim(),className:M,children:c?e.jsxDEV(e.Fragment,{children:[e.jsxDEV("span",{className:"loading-spinner",children:"🔄"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:300,columnNumber:21},void 0),"Verificando..."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:299,columnNumber:19},void 0):e.jsxDEV(e.Fragment,{children:"🔓 Entrar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:304,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:293,columnNumber:15},void 0),e.jsxDEV("button",{type:"button",onClick:a,className:I,disabled:c,children:"← Voltar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:310,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:292,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:266,columnNumber:11},void 0),e.jsxDEV("div",{className:L,children:'💡 Dica: A senha padrão é "betina2025"'},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:321,columnNumber:11},void 0),e.jsxDEV("div",{className:"login-footer",children:e.jsxDEV("small",{children:["🔒 Conexão segura • 🕒 Última atualização: ",(new Date).toLocaleTimeString()]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:326,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:325,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:254,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/admin/AdminDashboard/AdminDashboard.jsx",lineNumber:253,columnNumber:7},void 0)};function Y({onBack:o}){return e.jsxDEV(Q,{onBack:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/AdminPanel/AdminPanel.jsx",lineNumber:12,columnNumber:10},this)}export{Y as default};
