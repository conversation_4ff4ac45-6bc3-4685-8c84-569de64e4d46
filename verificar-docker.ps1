# 🐳 SCRIPT DE VERIFICAÇÃO DOCKER - Portal Betina V3 (PowerShell)
# Verifica se todos os serviços estão funcionando corretamente

Write-Host "🔍 Verificando ambiente Docker do Portal Betina V3..." -ForegroundColor Cyan

# Função para verificar se um comando existe
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Função para verificar status de um serviço
function Test-Service {
    param($ServiceName)
    
    Write-Host "🔧 Verificando $ServiceName... " -NoNewline
    
    try {
        $status = docker-compose ps | Select-String $ServiceName
        if ($status -and $status.ToString().Contains("Up")) {
            Write-Host "✅ ATIVO" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ INATIVO" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ ERRO" -ForegroundColor Red
        return $false
    }
}

# Função para verificar health check
function Test-HealthCheck {
    param($ServiceName, $Url)
    
    Write-Host "🩺 Health check $ServiceName... " -NoNewline
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ SAUDÁVEL" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ FALHA" -ForegroundColor Red
        return $false
    }
}

# Verificar se Docker está instalado
Write-Host "🐳 Verificando instalação do Docker..." -ForegroundColor Yellow

if (!(Test-Command "docker")) {
    Write-Host "❌ Docker não está instalado!" -ForegroundColor Red
    exit 1
}

if (!(Test-Command "docker-compose")) {
    Write-Host "❌ Docker Compose não está instalado!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Docker e Docker Compose instalados" -ForegroundColor Green

# Verificar se o arquivo docker-compose.yml existe
Write-Host "📄 Verificando docker-compose.yml..." -ForegroundColor Yellow
if (!(Test-Path "docker-compose.yml")) {
    Write-Host "❌ Arquivo docker-compose.yml não encontrado!" -ForegroundColor Red
    exit 1
}

Write-Host "✅ docker-compose.yml encontrado" -ForegroundColor Green

# Verificar se o arquivo .env existe
Write-Host "⚙️ Verificando arquivo .env..." -ForegroundColor Yellow
if (!(Test-Path ".env")) {
    Write-Host "⚠️ Arquivo .env não encontrado. Criando a partir do .env.example..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ Arquivo .env criado" -ForegroundColor Green
    } else {
        Write-Host "❌ Arquivo .env.example não encontrado!" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ Arquivo .env encontrado" -ForegroundColor Green
}

# Verificar status dos serviços
Write-Host ""
Write-Host "🔍 Verificando status dos serviços Docker..." -ForegroundColor Cyan

# Verificar se o docker-compose está rodando
try {
    docker-compose ps | Out-Null
} catch {
    Write-Host "⚠️ Serviços não estão rodando. Iniciando..." -ForegroundColor Yellow
    docker-compose up -d
    Start-Sleep -Seconds 10
}

# Lista de serviços para verificar
$services = @("portal-betina-v3-db", "portal-betina-v3-api", "portal-betina-v3-frontend")
$allServicesOk = $true

foreach ($service in $services) {
    if (!(Test-Service $service)) {
        $allServicesOk = $false
    }
}

# Verificar health checks
Write-Host ""
Write-Host "🩺 Verificando health checks..." -ForegroundColor Cyan

# Health check da API
if (!(Test-HealthCheck "API" "http://localhost:3000/api/public/health")) {
    $allServicesOk = $false
}

# Health check do Frontend
if (!(Test-HealthCheck "Frontend" "http://localhost:5173")) {
    $allServicesOk = $false
}

# Verificar conectividade do banco
Write-Host "🗄️ Verificando banco de dados... " -NoNewline
try {
    $dbCheck = docker-compose exec -T portal-betina-db pg_isready -U betina_user -d betina_db 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ CONECTADO" -ForegroundColor Green
    } else {
        Write-Host "❌ FALHA NA CONEXÃO" -ForegroundColor Red
        $allServicesOk = $false
    }
} catch {
    Write-Host "❌ ERRO NA VERIFICAÇÃO" -ForegroundColor Red
    $allServicesOk = $false
}

# Verificar logs recentes para erros
Write-Host ""
Write-Host "📋 Verificando logs recentes..." -ForegroundColor Cyan

# Verificar logs da API
Write-Host "📊 Logs da API... " -NoNewline
try {
    $apiLogs = docker-compose logs --tail=50 api 2>$null | Select-String -Pattern "error|failed|exception" -CaseSensitive:$false
    $apiErrorCount = ($apiLogs | Measure-Object).Count
    if ($apiErrorCount -eq 0) {
        Write-Host "✅ SEM ERROS" -ForegroundColor Green
    } else {
        Write-Host "⚠️ $apiErrorCount ERROS ENCONTRADOS" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❓ NÃO FOI POSSÍVEL VERIFICAR" -ForegroundColor Yellow
}

# Verificar logs do banco
Write-Host "🗄️ Logs do banco... " -NoNewline
try {
    $dbLogs = docker-compose logs --tail=50 portal-betina-db 2>$null | Select-String -Pattern "error|failed|fatal" -CaseSensitive:$false
    $dbErrorCount = ($dbLogs | Measure-Object).Count
    if ($dbErrorCount -eq 0) {
        Write-Host "✅ SEM ERROS" -ForegroundColor Green
    } else {
        Write-Host "⚠️ $dbErrorCount ERROS ENCONTRADOS" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❓ NÃO FOI POSSÍVEL VERIFICAR" -ForegroundColor Yellow
}

# Verificar uso de recursos
Write-Host ""
Write-Host "💾 Verificando uso de recursos..." -ForegroundColor Cyan
try {
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | Select-String "portal-betina"
} catch {
    Write-Host "❓ Não foi possível obter estatísticas de recursos" -ForegroundColor Yellow
}

# Verificar volumes
Write-Host ""
Write-Host "💿 Verificando volumes Docker..." -ForegroundColor Cyan
try {
    docker volume ls | Select-String "portal"
} catch {
    Write-Host "❓ Não foi possível listar volumes" -ForegroundColor Yellow
}

# Verificar rede
Write-Host ""
Write-Host "🌐 Verificando rede Docker..." -ForegroundColor Cyan
try {
    docker network ls | Select-String "betina"
} catch {
    Write-Host "❓ Não foi possível listar redes" -ForegroundColor Yellow
}

# Resumo final
Write-Host ""
Write-Host "==========================" -ForegroundColor White
if ($allServicesOk) {
    Write-Host "🎉 TODOS OS SERVIÇOS ESTÃO FUNCIONANDO CORRETAMENTE!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📍 URLs de Acesso:" -ForegroundColor Blue
    Write-Host "   🎨 Frontend: http://localhost:5173" -ForegroundColor White
    Write-Host "   🚀 API: http://localhost:3000" -ForegroundColor White
    Write-Host "   📊 Health Check: http://localhost:3000/api/public/health" -ForegroundColor White
    Write-Host "   📈 Monitoramento: http://localhost:9090" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 Comandos Úteis:" -ForegroundColor Blue
    Write-Host "   Ver logs: docker-compose logs -f" -ForegroundColor White
    Write-Host "   Reiniciar: docker-compose restart" -ForegroundColor White
    Write-Host "   Parar: docker-compose down" -ForegroundColor White
} else {
    Write-Host "❌ ALGUNS SERVIÇOS APRESENTAM PROBLEMAS!" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Comandos para Diagnóstico:" -ForegroundColor Yellow
    Write-Host "   Ver logs detalhados: docker-compose logs" -ForegroundColor White
    Write-Host "   Reiniciar serviços: docker-compose restart" -ForegroundColor White
    Write-Host "   Rebuild: docker-compose up -d --build" -ForegroundColor White
    Write-Host ""
    Write-Host "🆘 Se problemas persistirem:" -ForegroundColor Yellow
    Write-Host "   1. Verificar arquivo .env" -ForegroundColor White
    Write-Host "   2. Verificar portas em uso" -ForegroundColor White
    Write-Host "   3. Verificar espaço em disco" -ForegroundColor White
    Write-Host "   4. Limpar cache Docker: docker system prune" -ForegroundColor White
}

Write-Host "==========================" -ForegroundColor White
