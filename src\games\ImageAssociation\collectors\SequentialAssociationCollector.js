// ============================================================================
// SEQUENTIAL ASSOCIATION COLLECTOR - ATIVIDADE 6
// Coleta e análise de dados para associação sequencial e memória ordenada
// ============================================================================

import { BaseCollector } from '../../../utils/BaseCollector.js';

export class SequentialAssociationCollector extends BaseCollector {
  constructor() {
    super('SequentialAssociation');
    
    this.cognitiveMetrics = {
      // Métricas específicas de processamento sequencial
      sequentialMemory: [],
      orderRecognition: [],
      temporalProcessing: [],
      sequentialLogic: [],
      patternCompletion: [],
      
      // Padrões sequenciais
      sequenceTypes: [],
      orderingStrategies: [],
      sequentialErrors: [],
      chronologicalThinking: [],
      
      // Análise de memória de trabalho
      workingMemoryLoad: [],
      sequentialSpan: [],
      orderMaintenance: [],
      sequentialUpdating: []
    };

    this.sequenceTypes = {
      daily_routine: {
        category: 'temporal',
        cognitive_load: 'low',
        sequence: ['🌅', '🍳', '🚿', '👔', '🚗', '💼', '🍽️', '📺', '🛏️'],
        logic_type: 'habitual',
        dependencies: 'temporal',
        complexity: 'low',
        real_world: true
      },
      cooking_process: {
        category: 'procedural',
        cognitive_load: 'medium',
        sequence: ['🛒', '🧄', '🔪', '🍳', '🧂', '🍽️', '🧽'],
        logic_type: 'causal',
        dependencies: 'prerequisite',
        complexity: 'medium',
        real_world: true
      },
      plant_growth: {
        category: 'biological',
        cognitive_load: 'medium',
        sequence: ['🌰', '🌱', '🌿', '🌳', '🌸', '🍎', '🍂'],
        logic_type: 'natural',
        dependencies: 'biological',
        complexity: 'medium',
        real_world: true
      },
      communication_flow: {
        category: 'social',
        cognitive_load: 'medium',
        sequence: ['💭', '🗣️', '👂', '🤔', '💬', '😊', '🤝'],
        logic_type: 'interactive',
        dependencies: 'social',
        complexity: 'medium',
        real_world: true
      },
      problem_solving: {
        category: 'cognitive',
        cognitive_load: 'high',
        sequence: ['❓', '🤔', '💡', '📝', '🔬', '✅', '🎯'],
        logic_type: 'analytical',
        dependencies: 'logical',
        complexity: 'high',
        real_world: true
      },
      geometric_progression: {
        category: 'mathematical',
        cognitive_load: 'high',
        sequence: ['🔵', '🔵🔵', '🔵🔵🔵🔵', '🟦🟦🟦🟦🟦🟦🟦🟦'],
        logic_type: 'mathematical',
        dependencies: 'rule_based',
        complexity: 'high',
        real_world: false
      }
    };

    this.sessionData = {
      startTime: null,
      endTime: null,
      totalSequences: 0,
      correctSequences: 0,
      incorrectSequences: 0,
      averageSequentialProcessingTime: 0,
      sequencesByAccuracy: {},
      sequentialErrors: {},
      memorySpan: 0,
      orderingAccuracy: 0,
      difficultyLevel: 'beginner'
    };
  }

  // ========================================================================
  // COLETA DE DADOS DE ASSOCIAÇÃO SEQUENCIAL
  // ========================================================================

  collectSequentialAssociation(associationData) {
    const {
      userSequence,
      correctSequence,
      sequenceType,
      responseTime,
      timestamp,
      orderingStrategy = null,
      memoryAids = [],
      sequenceLength = 0
    } = associationData;

    const isCorrect = this.compareSequences(userSequence, correctSequence);
    const sequenceAccuracy = this.calculateSequenceAccuracy(userSequence, correctSequence);
    
    const association = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      userSequence,
      correctSequence,
      sequenceType,
      isCorrect,
      sequenceAccuracy,
      sequenceAnalysis: {
        length: correctSequence.length,
        complexity: this.sequenceTypes[sequenceType]?.complexity,
        logicType: this.sequenceTypes[sequenceType]?.logic_type,
        cognitiveLoad: this.sequenceTypes[sequenceType]?.cognitive_load,
        dependencies: this.sequenceTypes[sequenceType]?.dependencies
      },
      context: {
        orderingStrategy,
        memoryAids,
        actualSequenceLength: sequenceLength,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };

    this.interactions.push(association);

    // Análises cognitivas especializadas
    this.analyzeSequentialMemory(association);
    this.analyzeOrderRecognition(association);
    this.analyzeTemporalProcessing(association);
    this.analyzeWorkingMemoryLoad(association);
    
    // Atualizar métricas de sessão
    this.updateSessionMetrics(association);

    return association;
  }

  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================

  analyzeSequentialMemory(association) {
    const { responseTime, sequenceAccuracy, correctSequence, userSequence } = association;
    
    const memoryMetric = {
      timestamp: association.timestamp,
      sequenceType: association.sequenceType,
      sequenceLength: correctSequence.length,
      accuracy: sequenceAccuracy,
      responseTime,
      memoryLoad: this.calculateMemoryLoad(association),
      memoryStrategy: this.inferMemoryStrategy(association),
      memorySpan: this.calculateEffectiveSpan(userSequence, correctSequence)
    };

    this.cognitiveMetrics.sequentialMemory.push(memoryMetric);

    // Análise de manutenção de ordem
    const maintenanceMetric = {
      timestamp: association.timestamp,
      sequenceType: association.sequenceType,
      orderMaintained: this.assessOrderMaintenance(association),
      positionAccuracy: this.calculatePositionAccuracy(association),
      orderingStability: this.assessOrderingStability(association)
    };

    this.cognitiveMetrics.orderMaintenance.push(maintenanceMetric);
  }

  analyzeOrderRecognition(association) {
    const { sequenceType, isCorrect, userSequence, correctSequence } = association;
    
    const orderMetric = {
      timestamp: association.timestamp,
      sequenceType,
      recognized: isCorrect,
      orderLogic: this.sequenceTypes[sequenceType]?.logic_type,
      logicRecognition: this.assessLogicRecognition(association),
      orderingPatterns: this.identifyOrderingPatterns(association),
      sequentialReasoning: this.assessSequentialReasoning(association)
    };

    this.cognitiveMetrics.orderRecognition.push(orderMetric);
  }

  analyzeTemporalProcessing(association) {
    const { sequenceType, responseTime } = association;
    
    const temporalMetric = {
      timestamp: association.timestamp,
      sequenceType,
      temporalLogic: this.assessTemporalLogic(association),
      chronologicalAccuracy: this.assessChronologicalAccuracy(association),
      temporalSpan: this.calculateTemporalSpan(association),
      timeBasedReasoning: this.assessTimeBasedReasoning(association)
    };

    this.cognitiveMetrics.temporalProcessing.push(temporalMetric);
  }

  analyzeWorkingMemoryLoad(association) {
    const { correctSequence, responseTime, sequenceType } = association;
    
    const workingMemoryMetric = {
      timestamp: association.timestamp,
      sequenceLength: correctSequence.length,
      cognitiveLoad: this.sequenceTypes[sequenceType]?.cognitive_load,
      actualLoad: this.calculateActualCognitiveLoad(association),
      loadManagement: this.assessLoadManagement(association),
      capacityUtilization: this.assessCapacityUtilization(association)
    };

    this.cognitiveMetrics.workingMemoryLoad.push(workingMemoryMetric);
  }

  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS
  // ========================================================================

  compareSequences(userSequence, correctSequence) {
    if (userSequence.length !== correctSequence.length) return false;
    
    return userSequence.every((item, index) => item === correctSequence[index]);
  }

  calculateSequenceAccuracy(userSequence, correctSequence) {
    if (correctSequence.length === 0) return 0;
    
    let correctPositions = 0;
    const maxLength = Math.max(userSequence.length, correctSequence.length);
    
    for (let i = 0; i < maxLength; i++) {
      if (userSequence[i] === correctSequence[i]) {
        correctPositions++;
      }
    }
    
    return correctPositions / correctSequence.length;
  }

  calculateMemoryLoad(association) {
    const { correctSequence, sequenceType } = association;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    
    // Carga baseada no comprimento e complexidade
    let load = correctSequence.length * 0.1;
    
    // Ajustar por tipo de lógica
    const logicMultiplier = {
      'habitual': 0.8,
      'causal': 1.0,
      'natural': 1.0,
      'interactive': 1.2,
      'analytical': 1.4,
      'mathematical': 1.6
    };
    
    load *= logicMultiplier[sequenceInfo?.logic_type] || 1.0;
    
    // Ajustar por carga cognitiva
    const loadMultiplier = {
      'low': 0.8,
      'medium': 1.0,
      'high': 1.3
    };
    
    load *= loadMultiplier[sequenceInfo?.cognitive_load] || 1.0;
    
    return Math.min(2.0, load);
  }

  inferMemoryStrategy(association) {
    const { context, responseTime, sequenceAccuracy } = association;
    
    // Estratégias baseadas em pistas contextuais
    if (context.memoryAids && context.memoryAids.length > 0) {
      return 'aided_memory';
    }
    
    if (context.orderingStrategy) {
      return context.orderingStrategy;
    }
    
    // Inferir estratégia baseada no desempenho
    if (sequenceAccuracy > 0.8 && responseTime < 8000) {
      return 'automatic_recall';
    }
    
    if (sequenceAccuracy > 0.6 && responseTime > 12000) {
      return 'deliberate_reconstruction';
    }
    
    if (sequenceAccuracy < 0.5) {
      return 'guessing';
    }
    
    return 'mixed_strategy';
  }

  calculateEffectiveSpan(userSequence, correctSequence) {
    // Calcular span efetivo baseado na sequência mais longa correta
    let maxCorrectSpan = 0;
    let currentSpan = 0;
    
    const maxLength = Math.min(userSequence.length, correctSequence.length);
    
    for (let i = 0; i < maxLength; i++) {
      if (userSequence[i] === correctSequence[i]) {
        currentSpan++;
        maxCorrectSpan = Math.max(maxCorrectSpan, currentSpan);
      } else {
        currentSpan = 0;
      }
    }
    
    return maxCorrectSpan;
  }

  assessOrderMaintenance(association) {
    const { userSequence, correctSequence } = association;
    
    // Verificar quantos itens mantiveram posições relativas corretas
    let maintainedOrders = 0;
    const totalPairs = correctSequence.length - 1;
    
    for (let i = 0; i < totalPairs; i++) {
      const item1 = correctSequence[i];
      const item2 = correctSequence[i + 1];
      
      const userPos1 = userSequence.indexOf(item1);
      const userPos2 = userSequence.indexOf(item2);
      
      if (userPos1 !== -1 && userPos2 !== -1 && userPos1 < userPos2) {
        maintainedOrders++;
      }
    }
    
    return totalPairs > 0 ? maintainedOrders / totalPairs : 0;
  }

  calculatePositionAccuracy(association) {
    const { userSequence, correctSequence } = association;
    
    let correctPositions = 0;
    
    correctSequence.forEach((item, correctIndex) => {
      const userIndex = userSequence.indexOf(item);
      if (userIndex === correctIndex) {
        correctPositions++;
      }
    });
    
    return correctSequence.length > 0 ? correctPositions / correctSequence.length : 0;
  }

  assessOrderingStability(association) {
    // Avaliar estabilidade baseada na consistência de ordenação
    const { userSequence, correctSequence } = association;
    
    // Calcular inversões de ordem
    let inversions = 0;
    const n = Math.min(userSequence.length, correctSequence.length);
    
    for (let i = 0; i < n - 1; i++) {
      for (let j = i + 1; j < n; j++) {
        const correctOrder = correctSequence.indexOf(userSequence[i]) < correctSequence.indexOf(userSequence[j]);
        const userOrder = i < j;
        
        if (correctOrder !== userOrder) {
          inversions++;
        }
      }
    }
    
    const maxInversions = (n * (n - 1)) / 2;
    return maxInversions > 0 ? 1 - (inversions / maxInversions) : 1;
  }

  assessLogicRecognition(association) {
    const { sequenceType, isCorrect, userSequence, correctSequence } = association;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    
    if (!sequenceInfo) return 0.5;
    
    let logicScore = isCorrect ? 0.8 : 0.2;
    
    // Ajustar baseado no tipo de lógica
    if (sequenceInfo.logic_type === 'mathematical' && isCorrect) {
      logicScore += 0.2; // Bonus para lógica matemática
    }
    
    if (sequenceInfo.logic_type === 'causal') {
      // Verificar se respeitou dependências causais
      const causalRespected = this.assessCausalDependencies(userSequence, correctSequence);
      logicScore += causalRespected * 0.3;
    }
    
    return Math.min(1.0, logicScore);
  }

  identifyOrderingPatterns(association) {
    const { userSequence, correctSequence } = association;
    
    const patterns = {
      sequential: this.assessSequentialPattern(userSequence, correctSequence),
      reverse: this.assessReversePattern(userSequence, correctSequence),
      chunked: this.assessChunkedPattern(userSequence, correctSequence),
      random: this.assessRandomPattern(userSequence, correctSequence)
    };
    
    // Retornar o padrão predominante
    return Object.entries(patterns).reduce((max, [pattern, score]) => 
      score > max.score ? { pattern, score } : max, 
      { pattern: 'unknown', score: 0 }
    );
  }

  assessSequentialReasoning(association) {
    const { sequenceType, userSequence, correctSequence } = association;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    
    let reasoningScore = 0.3; // Score base
    
    // Raciocínio baseado no tipo de sequência
    switch (sequenceInfo?.logic_type) {
      case 'temporal':
      case 'habitual':
        reasoningScore += this.assessTemporalReasoning(userSequence, correctSequence) * 0.4;
        break;
      case 'causal':
      case 'procedural':
        reasoningScore += this.assessCausalReasoning(userSequence, correctSequence) * 0.4;
        break;
      case 'mathematical':
        reasoningScore += this.assessMathematicalReasoning(userSequence, correctSequence) * 0.4;
        break;
      case 'logical':
      case 'analytical':
        reasoningScore += this.assessLogicalReasoning(userSequence, correctSequence) * 0.4;
        break;
    }
    
    // Bonus por manter relacionamentos lógicos
    const relationshipScore = this.assessLogicalRelationships(userSequence, correctSequence);
    reasoningScore += relationshipScore * 0.3;
    
    return Math.min(1.0, reasoningScore);
  }

  assessTemporalLogic(association) {
    const { sequenceType, userSequence, correctSequence } = association;
    const sequenceInfo = this.sequenceTypes[sequenceType];
    
    if (sequenceInfo?.dependencies !== 'temporal') return 0.5;
    
    // Verificar se manteve lógica temporal
    return this.assessTemporalReasoning(userSequence, correctSequence);
  }

  assessChronologicalAccuracy(association) {
    const { userSequence, correctSequence } = association;
    
    // Verificar precisão cronológica baseada em ordem temporal esperada
    let chronologicalScore = 0;
    const temporalPairs = this.identifyTemporalPairs(correctSequence);
    
    temporalPairs.forEach(([before, after]) => {
      const userBeforeIndex = userSequence.indexOf(before);
      const userAfterIndex = userSequence.indexOf(after);
      
      if (userBeforeIndex !== -1 && userAfterIndex !== -1 && userBeforeIndex < userAfterIndex) {
        chronologicalScore++;
      }
    });
    
    return temporalPairs.length > 0 ? chronologicalScore / temporalPairs.length : 0.5;
  }

  calculateTemporalSpan(association) {
    const { userSequence, correctSequence } = association;
    
    // Calcular span temporal baseado na sequência temporal mais longa mantida
    const temporalPairs = this.identifyTemporalPairs(correctSequence);
    let maxTemporalSpan = 0;
    let currentSpan = 0;
    
    temporalPairs.forEach(([before, after]) => {
      const userBeforeIndex = userSequence.indexOf(before);
      const userAfterIndex = userSequence.indexOf(after);
      
      if (userBeforeIndex !== -1 && userAfterIndex !== -1 && userBeforeIndex < userAfterIndex) {
        currentSpan++;
        maxTemporalSpan = Math.max(maxTemporalSpan, currentSpan);
      } else {
        currentSpan = 0;
      }
    });
    
    return maxTemporalSpan;
  }

  assessTimeBasedReasoning(association) {
    const { sequenceType, responseTime, userSequence, correctSequence } = association;
    
    let timeBasedScore = 0.4; // Score base
    
    // Raciocínio temporal baseado no tempo de resposta apropriado
    const sequenceInfo = this.sequenceTypes[sequenceType];
    const expectedTime = this.getExpectedSequenceTime(sequenceInfo);
    const timeAppropriate = Math.abs(responseTime - expectedTime) < expectedTime * 0.5;
    
    if (timeAppropriate) {
      timeBasedScore += 0.3;
    }
    
    // Se a sequência tem componente temporal, verificar compreensão
    if (sequenceInfo?.dependencies === 'temporal') {
      const temporalAccuracy = this.assessTemporalReasoning(userSequence, correctSequence);
      timeBasedScore += temporalAccuracy * 0.3;
    }
    
    return Math.min(1.0, timeBasedScore);
  }

  calculateActualCognitiveLoad(association) {
    const { responseTime, sequenceAccuracy, userSequence, correctSequence } = association;
    
    // Carga cognitiva inferida baseada no desempenho
    let actualLoad = 0.5; // Base
    
    // Ajustar por tempo de resposta (carga maior = mais tempo)
    const normalizedTime = responseTime / 10000; // Normalizar para 10 segundos
    actualLoad += Math.min(0.5, normalizedTime);
    
    // Ajustar por precisão (menor precisão pode indicar maior carga)
    actualLoad += (1 - sequenceAccuracy) * 0.3;
    
    // Ajustar por comprimento da sequência
    actualLoad += (correctSequence.length / 10) * 0.2;
    
    return Math.min(2.0, actualLoad);
  }

  assessLoadManagement(association) {
    const { context, sequenceAccuracy, responseTime } = association;
    
    let managementScore = 0.4; // Score base
    
    // Se usou ajudas de memória, está gerenciando carga
    if (context.memoryAids && context.memoryAids.length > 0) {
      managementScore += 0.3;
    }
    
    // Se usou estratégia de ordenação, está gerenciando carga
    if (context.orderingStrategy && context.orderingStrategy !== 'guessing') {
      managementScore += 0.2;
    }
    
    // Relação eficiente entre tempo e precisão
    const efficiency = sequenceAccuracy / (responseTime / 10000);
    managementScore += Math.min(0.1, efficiency * 0.1);
    
    return Math.min(1.0, managementScore);
  }

  assessCapacityUtilization(association) {
    const { correctSequence, sequenceAccuracy } = association;
    const memorySpan = this.calculateEffectiveSpan(association.userSequence, correctSequence);
    
    // Utilização baseada no span efetivo vs comprimento da sequência
    const utilizationRatio = memorySpan / correctSequence.length;
    
    // Ajustar por precisão
    return utilizationRatio * sequenceAccuracy;
  }

  // ========================================================================
  // FUNÇÕES AUXILIARES ESPECIALIZADAS
  // ========================================================================

  assessCausalDependencies(userSequence, correctSequence) {
    // Identificar dependências causais na sequência correta
    const causalPairs = this.identifyCausalPairs(correctSequence);
    
    let respectfulPairs = 0;
    causalPairs.forEach(([cause, effect]) => {
      const userCauseIndex = userSequence.indexOf(cause);
      const userEffectIndex = userSequence.indexOf(effect);
      
      if (userCauseIndex !== -1 && userEffectIndex !== -1 && userCauseIndex < userEffectIndex) {
        respectfulPairs++;
      }
    });
    
    return causalPairs.length > 0 ? respectfulPairs / causalPairs.length : 0.5;
  }

  assessSequentialPattern(userSequence, correctSequence) {
    // Verificar quantos elementos estão em ordem sequencial correta
    let sequentialCount = 0;
    
    for (let i = 0; i < Math.min(userSequence.length, correctSequence.length); i++) {
      if (userSequence[i] === correctSequence[i]) {
        sequentialCount++;
      }
    }
    
    return correctSequence.length > 0 ? sequentialCount / correctSequence.length : 0;
  }

  assessReversePattern(userSequence, correctSequence) {
    // Verificar se seguiu padrão reverso
    const reversedCorrect = [...correctSequence].reverse();
    let reverseMatches = 0;
    
    for (let i = 0; i < Math.min(userSequence.length, reversedCorrect.length); i++) {
      if (userSequence[i] === reversedCorrect[i]) {
        reverseMatches++;
      }
    }
    
    return reversedCorrect.length > 0 ? reverseMatches / reversedCorrect.length : 0;
  }

  assessChunkedPattern(userSequence, correctSequence) {
    // Verificar se agrupou elementos em chunks
    const chunks = this.identifyChunks(correctSequence);
    let chunkScore = 0;
    
    chunks.forEach(chunk => {
      const chunkInUser = this.findChunkInSequence(chunk, userSequence);
      if (chunkInUser) {
        chunkScore += chunk.length / correctSequence.length;
      }
    });
    
    return chunkScore;
  }

  assessRandomPattern(userSequence, correctSequence) {
    // Verificar se o padrão parece aleatório (baixa correlação com correto)
    const orderCorrelation = this.calculateOrderCorrelation(userSequence, correctSequence);
    return 1 - orderCorrelation; // Quanto menor a correlação, mais aleatório
  }

  assessTemporalReasoning(userSequence, correctSequence) {
    const temporalPairs = this.identifyTemporalPairs(correctSequence);
    let correctTemporalOrder = 0;
    
    temporalPairs.forEach(([before, after]) => {
      const userBeforeIndex = userSequence.indexOf(before);
      const userAfterIndex = userSequence.indexOf(after);
      
      if (userBeforeIndex !== -1 && userAfterIndex !== -1 && userBeforeIndex < userAfterIndex) {
        correctTemporalOrder++;
      }
    });
    
    return temporalPairs.length > 0 ? correctTemporalOrder / temporalPairs.length : 0.5;
  }

  assessCausalReasoning(userSequence, correctSequence) {
    return this.assessCausalDependencies(userSequence, correctSequence);
  }

  assessMathematicalReasoning(userSequence, correctSequence) {
    // Para sequências matemáticas, verificar se seguiu a progressão
    if (correctSequence.length < 3) return 0.5;
    
    // Identificar padrão matemático na sequência correta
    const pattern = this.identifyMathematicalPattern(correctSequence);
    
    if (!pattern) return 0.3;
    
    // Verificar se o usuário seguiu o padrão
    return this.verifyMathematicalPattern(userSequence, pattern);
  }

  assessLogicalReasoning(userSequence, correctSequence) {
    // Verificar raciocínio lógico baseado em regras
    const logicalRules = this.identifyLogicalRules(correctSequence);
    let rulesFollowed = 0;
    
    logicalRules.forEach(rule => {
      if (this.verifyLogicalRule(userSequence, rule)) {
        rulesFollowed++;
      }
    });
    
    return logicalRules.length > 0 ? rulesFollowed / logicalRules.length : 0.5;
  }

  assessLogicalRelationships(userSequence, correctSequence) {
    // Verificar se manteve relacionamentos lógicos entre elementos
    const relationships = this.identifyLogicalRelationships(correctSequence);
    let maintainedRelationships = 0;
    
    relationships.forEach(relationship => {
      if (this.verifyRelationship(userSequence, relationship)) {
        maintainedRelationships++;
      }
    });
    
    return relationships.length > 0 ? maintainedRelationships / relationships.length : 0.5;
  }

  identifyTemporalPairs(sequence) {
    // Identificar pares temporais óbvios (simplificado)
    const temporalPairs = [];
    
    for (let i = 0; i < sequence.length - 1; i++) {
      temporalPairs.push([sequence[i], sequence[i + 1]]);
    }
    
    return temporalPairs;
  }

  identifyCausalPairs(sequence) {
    // Identificar pares causais (simplificado - consecutive elements often have causal relationship)
    const causalPairs = [];
    
    for (let i = 0; i < sequence.length - 1; i++) {
      // Assumir que elementos consecutivos têm relação causal
      causalPairs.push([sequence[i], sequence[i + 1]]);
    }
    
    return causalPairs;
  }

  identifyChunks(sequence) {
    // Identificar chunks naturais na sequência (simplificado)
    const chunks = [];
    const chunkSize = Math.min(3, Math.floor(sequence.length / 2));
    
    for (let i = 0; i < sequence.length; i += chunkSize) {
      chunks.push(sequence.slice(i, i + chunkSize));
    }
    
    return chunks.filter(chunk => chunk.length > 1);
  }

  findChunkInSequence(chunk, userSequence) {
    // Verificar se o chunk aparece em sequência no userSequence
    if (chunk.length === 0) return false;
    
    for (let i = 0; i <= userSequence.length - chunk.length; i++) {
      let matches = true;
      for (let j = 0; j < chunk.length; j++) {
        if (userSequence[i + j] !== chunk[j]) {
          matches = false;
          break;
        }
      }
      if (matches) return true;
    }
    
    return false;
  }

  calculateOrderCorrelation(userSequence, correctSequence) {
    // Calcular correlação de ordem simples
    let correlation = 0;
    const minLength = Math.min(userSequence.length, correctSequence.length);
    
    for (let i = 0; i < minLength; i++) {
      if (userSequence[i] === correctSequence[i]) {
        correlation++;
      }
    }
    
    return minLength > 0 ? correlation / minLength : 0;
  }

  identifyMathematicalPattern(sequence) {
    // Identificar padrão matemático simples (sequência geométrica)
    if (sequence.length < 3) return null;
    
    // Verificar progressão baseada no número de elementos visuais
    const counts = sequence.map(item => this.countVisualElements(item));
    
    // Verificar progressão geométrica
    if (counts.length >= 3) {
      const ratio = counts[1] / counts[0];
      let isGeometric = true;
      
      for (let i = 2; i < counts.length; i++) {
        if (Math.abs(counts[i] / counts[i-1] - ratio) > 0.1) {
          isGeometric = false;
          break;
        }
      }
      
      if (isGeometric) {
        return { type: 'geometric', ratio, base: counts[0] };
      }
    }
    
    return null;
  }

  verifyMathematicalPattern(userSequence, pattern) {
    // Verificar se o usuário seguiu o padrão matemático
    if (pattern.type === 'geometric') {
      const userCounts = userSequence.map(item => this.countVisualElements(item));
      let correctPattern = 0;
      
      for (let i = 1; i < userCounts.length; i++) {
        const expectedCount = Math.round(userCounts[0] * Math.pow(pattern.ratio, i));
        if (Math.abs(userCounts[i] - expectedCount) < 1) {
          correctPattern++;
        }
      }
      
      return userCounts.length > 1 ? correctPattern / (userCounts.length - 1) : 0;
    }
    
    return 0.5;
  }

  countVisualElements(item) {
    // Contar elementos visuais em um item (simplificado)
    if (typeof item === 'string') {
      // Contar emojis/caracteres únicos
      return Array.from(item).length;
    }
    return 1;
  }

  identifyLogicalRules(sequence) {
    // Identificar regras lógicas simples (placeholder)
    return [
      { type: 'sequential', description: 'maintain order' },
      { type: 'grouping', description: 'group similar items' }
    ];
  }

  verifyLogicalRule(userSequence, rule) {
    // Verificar se seguiu regra lógica (simplificado)
    if (rule.type === 'sequential') {
      return this.assessSequentialPattern(userSequence, userSequence) > 0.5;
    }
    
    return 0.5;
  }

  identifyLogicalRelationships(sequence) {
    // Identificar relacionamentos lógicos (simplificado)
    const relationships = [];
    
    for (let i = 0; i < sequence.length - 1; i++) {
      relationships.push({
        type: 'precedence',
        from: sequence[i],
        to: sequence[i + 1]
      });
    }
    
    return relationships;
  }

  verifyRelationship(userSequence, relationship) {
    // Verificar se manteve relacionamento
    const fromIndex = userSequence.indexOf(relationship.from);
    const toIndex = userSequence.indexOf(relationship.to);
    
    if (relationship.type === 'precedence') {
      return fromIndex !== -1 && toIndex !== -1 && fromIndex < toIndex;
    }
    
    return false;
  }

  getExpectedSequenceTime(sequenceInfo) {
    // Tempo esperado baseado na complexidade
    const baseTime = 8000; // 8 segundos base
    
    const complexityMultiplier = {
      'low': 0.8,
      'medium': 1.0,
      'high': 1.5
    };
    
    const loadMultiplier = {
      'low': 0.8,
      'medium': 1.0,
      'high': 1.3
    };
    
    return baseTime * 
           (complexityMultiplier[sequenceInfo?.complexity] || 1.0) * 
           (loadMultiplier[sequenceInfo?.cognitive_load] || 1.0);
  }

  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================

  updateSessionMetrics(association) {
    const { isCorrect, responseTime, sequenceType, sequenceAccuracy } = association;

    this.sessionData.totalSequences++;
    
    if (isCorrect) {
      this.sessionData.correctSequences++;
    } else {
      this.sessionData.incorrectSequences++;
      
      // Rastrear erros sequenciais
      const errorType = this.classifySequentialError(association);
      if (!this.sessionData.sequentialErrors[errorType]) {
        this.sessionData.sequentialErrors[errorType] = 0;
      }
      this.sessionData.sequentialErrors[errorType]++;
    }

    // Atualizar precisão por tipo de sequência
    if (!this.sessionData.sequencesByAccuracy[sequenceType]) {
      this.sessionData.sequencesByAccuracy[sequenceType] = { total: 0, correct: 0, accuracy: 0 };
    }
    
    this.sessionData.sequencesByAccuracy[sequenceType].total++;
    this.sessionData.sequencesByAccuracy[sequenceType].accuracy += sequenceAccuracy;
    if (isCorrect) {
      this.sessionData.sequencesByAccuracy[sequenceType].correct++;
    }

    // Atualizar tempo médio
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageSequentialProcessingTime = totalTime / this.interactions.length;

    // Calcular span de memória
    this.updateMemorySpan();
    
    // Calcular precisão de ordenação
    this.updateOrderingAccuracy();
  }

  classifySequentialError(association) {
    const { userSequence, correctSequence, sequenceType } = association;
    
    // Classificar tipo de erro
    if (userSequence.length !== correctSequence.length) {
      return 'length_error';
    }
    
    const orderMaintenance = this.assessOrderMaintenance(association);
    if (orderMaintenance < 0.3) {
      return 'order_error';
    }
    
    const sequenceAccuracy = this.calculateSequenceAccuracy(userSequence, correctSequence);
    if (sequenceAccuracy < 0.5) {
      return 'content_error';
    }
    
    const sequenceInfo = this.sequenceTypes[sequenceType];
    if (sequenceInfo?.logic_type === 'causal') {
      const causalRespected = this.assessCausalDependencies(userSequence, correctSequence);
      if (causalRespected < 0.5) {
        return 'causal_error';
      }
    }
    
    return 'partial_error';
  }

  updateMemorySpan() {
    const memoryMetrics = this.cognitiveMetrics.sequentialMemory;
    
    if (memoryMetrics.length === 0) {
      this.sessionData.memorySpan = 0;
      return;
    }

    const totalSpan = memoryMetrics.reduce((sum, metric) => sum + metric.memorySpan, 0);
    this.sessionData.memorySpan = totalSpan / memoryMetrics.length;
  }

  updateOrderingAccuracy() {
    const orderMetrics = this.cognitiveMetrics.orderMaintenance;
    
    if (orderMetrics.length === 0) {
      this.sessionData.orderingAccuracy = 0;
      return;
    }

    const totalOrderMaintenance = orderMetrics.reduce((sum, metric) => 
      sum + metric.orderMaintained, 0
    );

    this.sessionData.orderingAccuracy = totalOrderMaintenance / orderMetrics.length;
  }

  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================

  generateCognitiveReport() {
    return {
      sequentialMemory: this.analyzeSequentialMemoryReport(),
      orderRecognition: this.analyzeOrderRecognitionReport(),
      temporalProcessing: this.analyzeTemporalProcessingReport(),
      workingMemory: this.analyzeWorkingMemoryReport(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }

  analyzeSequentialMemoryReport() {
    return {
      memorySpan: this.sessionData.memorySpan,
      overallAccuracy: this.sessionData.correctSequences / this.sessionData.totalSequences,
      averageProcessingTime: this.sessionData.averageSequentialProcessingTime,
      memoryStrategies: this.analyzeMemoryStrategies(),
      sequenceAccuracy: this.calculateAverageSequenceAccuracy()
    };
  }

  analyzeOrderRecognitionReport() {
    return {
      orderingAccuracy: this.sessionData.orderingAccuracy,
      logicRecognition: this.calculateAverageMetric(this.cognitiveMetrics.orderRecognition, 'logicRecognition'),
      sequentialReasoning: this.calculateAverageMetric(this.cognitiveMetrics.orderRecognition, 'sequentialReasoning'),
      orderingPatterns: this.analyzeOrderingPatterns()
    };
  }

  analyzeTemporalProcessingReport() {
    return {
      temporalLogic: this.calculateAverageMetric(this.cognitiveMetrics.temporalProcessing, 'temporalLogic'),
      chronologicalAccuracy: this.calculateAverageMetric(this.cognitiveMetrics.temporalProcessing, 'chronologicalAccuracy'),
      temporalSpan: this.calculateAverageMetric(this.cognitiveMetrics.temporalProcessing, 'temporalSpan'),
      timeBasedReasoning: this.calculateAverageMetric(this.cognitiveMetrics.temporalProcessing, 'timeBasedReasoning')
    };
  }

  analyzeWorkingMemoryReport() {
    return {
      actualCognitiveLoad: this.calculateAverageMetric(this.cognitiveMetrics.workingMemoryLoad, 'actualLoad'),
      loadManagement: this.calculateAverageMetric(this.cognitiveMetrics.workingMemoryLoad, 'loadManagement'),
      capacityUtilization: this.calculateAverageMetric(this.cognitiveMetrics.workingMemoryLoad, 'capacityUtilization'),
      cognitiveEfficiency: this.calculateCognitiveEfficiency()
    };
  }

  analyzeMemoryStrategies() {
    const strategies = {};
    
    this.cognitiveMetrics.sequentialMemory.forEach(metric => {
      const strategy = metric.memoryStrategy;
      strategies[strategy] = (strategies[strategy] || 0) + 1;
    });

    // Converter para porcentagens
    const total = this.cognitiveMetrics.sequentialMemory.length;
    Object.keys(strategies).forEach(strategy => {
      strategies[strategy] = strategies[strategy] / total;
    });

    return strategies;
  }

  calculateAverageSequenceAccuracy() {
    const totalAccuracy = this.interactions.reduce((sum, interaction) => 
      sum + interaction.sequenceAccuracy, 0
    );

    return this.interactions.length > 0 ? totalAccuracy / this.interactions.length : 0;
  }

  analyzeOrderingPatterns() {
    const patterns = {};
    
    this.cognitiveMetrics.orderRecognition.forEach(metric => {
      const pattern = metric.orderingPatterns.pattern;
      patterns[pattern] = (patterns[pattern] || 0) + 1;
    });

    return patterns;
  }

  calculateCognitiveEfficiency() {
    const accuracySum = this.interactions.reduce((sum, interaction) => 
      sum + interaction.sequenceAccuracy, 0
    );
    
    const timeSum = this.interactions.reduce((sum, interaction) => 
      sum + interaction.responseTime, 0
    );

    if (this.interactions.length === 0 || timeSum === 0) return 0;

    const avgAccuracy = accuracySum / this.interactions.length;
    const avgTime = timeSum / this.interactions.length;
    
    // Eficiência = precisão / tempo normalizado
    return avgAccuracy / (avgTime / 10000); // Normalizar para 10 segundos
  }

  generateAdaptiveRecommendations() {
    const recommendations = [];
    
    // Análise por tipo de sequência
    const sequencePerformance = {};
    Object.entries(this.sessionData.sequencesByAccuracy).forEach(([type, data]) => {
      sequencePerformance[type] = data.correct / data.total;
    });

    const weakSequenceTypes = Object.entries(sequencePerformance)
      .filter(([type, accuracy]) => accuracy < 0.6)
      .map(([type]) => type);

    if (weakSequenceTypes.length > 0) {
      recommendations.push({
        type: 'sequence_training',
        recommendation: `Reforçar tipos de sequência: ${weakSequenceTypes.join(', ')}`,
        confidence: 0.8,
        details: {
          sequenceTypes: weakSequenceTypes,
          suggestedActivities: ['sequential_practice', 'pattern_recognition', 'ordering_exercises']
        }
      });
    }

    // Análise de span de memória
    if (this.sessionData.memorySpan < 4) {
      recommendations.push({
        type: 'memory_span_training',
        recommendation: 'Exercícios para ampliar span de memória sequencial',
        confidence: 0.7,
        details: {
          currentSpan: this.sessionData.memorySpan,
          suggestedActivities: ['span_building', 'chunking_practice', 'memory_strategies']
        }
      });
    }

    // Análise de precisão de ordenação
    if (this.sessionData.orderingAccuracy < 0.6) {
      recommendations.push({
        type: 'ordering_training',
        recommendation: 'Desenvolvimento de habilidades de ordenação',
        confidence: 0.7,
        details: {
          currentAccuracy: this.sessionData.orderingAccuracy,
          suggestedActivities: ['ordering_practice', 'logical_sequences', 'temporal_reasoning']
        }
      });
    }

    return recommendations;
  }

  getActivityScore() {
    if (this.sessionData.totalSequences === 0) return 0;
    
    const accuracy = this.sessionData.correctSequences / this.sessionData.totalSequences;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageSequentialProcessingTime - 8000) / 16000);
    const memoryFactor = Math.min(1, this.sessionData.memorySpan / 7); // Max span típico
    const orderingFactor = this.sessionData.orderingAccuracy;
    
    return Math.round(accuracy * speedFactor * memoryFactor * orderingFactor * 1000);
  }
}

export default SequentialAssociationCollector;
