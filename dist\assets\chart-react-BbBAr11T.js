import{r as t,R as e}from"./react-router-BtSsPy6x.js";import{C as n,L as r,B as s,R as a,P as u,D as c}from"./chart-core-CRFNBRsI.js";const o="label";function f(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function l(t,e){t.labels=e}function i(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:o;const r=[];t.datasets=e.map(e=>{const s=t.datasets.find(t=>t[n]===e[n]);return s&&e.data&&!r.includes(s)?(r.push(s),Object.assign(s,e),s):{...e}})}function d(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:o;const n={labels:[],datasets:[]};return l(n,t.labels),i(n,t.datasets,e),n}function p(r,s){const{height:a=150,width:u=300,redraw:c=!1,datasetIdKey:o,type:p,data:g,options:b,plugins:h=[],fallbackContent:m,updateMode:E,...R}=r,w=t.useRef(null),y=t.useRef(null),j=()=>{w.current&&(y.current=new n(w.current,{type:p,data:d(g,o),options:b&&{...b},plugins:h}),f(s,y.current))},v=()=>{f(s,null),y.current&&(y.current.destroy(),y.current=null)};return t.useEffect(()=>{!c&&y.current&&b&&function(t,e){const n=t.options;n&&e&&Object.assign(n,e)}(y.current,b)},[c,b]),t.useEffect(()=>{!c&&y.current&&l(y.current.config.data,g.labels)},[c,g.labels]),t.useEffect(()=>{!c&&y.current&&g.datasets&&i(y.current.config.data,g.datasets,o)},[c,g.datasets]),t.useEffect(()=>{y.current&&(c?(v(),setTimeout(j)):y.current.update(E))},[c,b,g.labels,g.datasets,E]),t.useEffect(()=>{y.current&&(v(),setTimeout(j))},[p]),t.useEffect(()=>(j(),()=>v()),[]),e.createElement("canvas",{ref:w,role:"img",height:a,width:u,...R},m)}const g=t.forwardRef(p);function b(r,s){return n.register(s),t.forwardRef((t,n)=>e.createElement(g,{...t,ref:n,type:r}))}const h=b("line",r),m=b("bar",s),E=b("radar",a),R=b("doughnut",c),w=b("pie",u);export{m as B,R as D,h as L,w as P,E as R};
