#!/usr/bin/env node

/**
 * 🎮 TESTE FUNCIONAL DOS JOGOS - Portal Betina V3
 * 
 * Script para validar se todos os jogos estão funcionando:
 * - Verificar se todos os componentes de jogos existem
 * - Validar imports e exports
 * - Verificar se configs e métricas estão corretas
 * - Testar estrutura básica dos coletores
 * 
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

console.log('🎮 TESTE FUNCIONAL DOS JOGOS - Portal Betina V3');
console.log('===============================================\n');

// Lista dos 8 jogos principais
const GAMES = [
  {
    name: 'ColorMatch',
    path: 'src/games/ColorMatch',
    component: 'ColorMatchGame.jsx',
    config: 'ColorMatchConfig.js',
    metrics: 'ColorMatchMetrics.js',
    collectors: 'collectors/index.js'
  },
  {
    name: 'MemoryGame',
    path: 'src/games/MemoryGame',
    component: 'MemoryGame.jsx',
    config: 'MemoryGameConfig.js',
    metrics: 'MemoryGameMetrics.js',
    collectors: 'collectors/index.js'
  },
  {
    name: 'LetterRecognition',
    path: 'src/games/LetterRecognition',
    component: 'LetterRecognitionGame.jsx',
    config: 'LetterRecognitionConfig.js',
    metrics: 'LetterRecognitionMetrics.js',
    collectors: 'collectors/index.js'
  },
  {
    name: 'MusicalSequence',
    path: 'src/games/MusicalSequence',
    component: 'MusicalSequenceGame.jsx',
    config: 'MusicalSequenceConfig.js',
    metrics: 'MusicalSequenceMetrics.js',
    collectors: 'collectors/index.js'
  },
  {
    name: 'ContagemNumeros',
    path: 'src/games/ContagemNumeros',
    component: 'ContagemNumerosGame.jsx',
    config: 'ContagemNumerosConfig.js',
    metrics: 'ContagemNumerosMetrics.js',
    collectors: 'collectors/index.js'
  },
  {
    name: 'PadroesVisuais',
    path: 'src/games/PadroesVisuais',
    component: 'PadroesVisuaisGame.jsx',
    config: 'PadroesVisuaisConfig.js',
    metrics: 'PadroesVisuaisMetrics.js',
    collectors: 'collectors/index.js'
  },
  {
    name: 'QuebraCabeca',
    path: 'src/games/QuebraCabeca',
    component: 'QuebraCabecaGame.jsx',
    config: 'QuebraCabecaConfig.js',
    metrics: 'QuebraCabecaMetrics.js',
    collectors: 'collectors/index.js'
  },
  {
    name: 'ImageAssociation',
    path: 'src/games/ImageAssociation',
    component: 'ImageAssociationGame.jsx',
    config: 'ImageAssociationConfig.js',
    metrics: 'ImageAssociationMetrics.js',
    collectors: 'collectors/index.js'
  }
];

/**
 * Verificar se arquivo existe
 */
function fileExists(filePath) {
  return fs.existsSync(path.join(__dirname, '..', filePath));
}

/**
 * Verificar imports de um arquivo
 */
function checkImports(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  
  if (!fs.existsSync(fullPath)) {
    return { valid: false, error: 'Arquivo não existe' };
  }
  
  try {
    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Verificar imports básicos React
    const hasReactImport = content.includes('import React') || content.includes('from \'react\'');
    
    // Verificar se tem export default
    const hasDefaultExport = content.includes('export default');
    
    // Verificar imports de hooks multissensoriais
    const hasMultisensoryHook = content.includes('useMultisensoryIntegration');
    
    // Verificar import de GameStartScreen
    const hasGameStartScreen = content.includes('GameStartScreen');
    
    // Verificar contexto de acessibilidade
    const hasAccessibilityContext = content.includes('useAccessibilityContext');
    
    return {
      valid: true,
      hasReactImport,
      hasDefaultExport,
      hasMultisensoryHook,
      hasGameStartScreen,
      hasAccessibilityContext,
      content: content.substring(0, 500) // Primeiros 500 chars para debug
    };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

/**
 * Verificar estrutura de um jogo
 */
function checkGame(game) {
  console.log(`🎯 Verificando: ${game.name}`);
  
  const results = {
    name: game.name,
    files: {},
    issues: [],
    score: 0
  };
  
  // Verificar arquivos principais
  const requiredFiles = ['component', 'config', 'metrics'];
  const optionalFiles = ['collectors'];
  
  requiredFiles.forEach(fileType => {
    const filePath = path.join(game.path, game[fileType]);
    const exists = fileExists(filePath);
    
    results.files[fileType] = {
      exists,
      path: filePath
    };
    
    if (exists) {
      results.score += 25; // 25 pontos por arquivo obrigatório
      
      // Verificar imports do componente principal
      if (fileType === 'component') {
        const importCheck = checkImports(filePath);
        results.files[fileType].imports = importCheck;
        
        if (!importCheck.valid) {
          results.issues.push(`Problema no componente: ${importCheck.error}`);
        } else {
          // Verificar imports específicos
          if (!importCheck.hasReactImport) {
            results.issues.push('Faltando import do React');
          }
          if (!importCheck.hasDefaultExport) {
            results.issues.push('Faltando export default');
          }
          if (!importCheck.hasMultisensoryHook) {
            results.issues.push('Faltando hook multissensorial');
          }
          if (!importCheck.hasGameStartScreen) {
            results.issues.push('Faltando GameStartScreen');
          }
          if (!importCheck.hasAccessibilityContext) {
            results.issues.push('Faltando contexto de acessibilidade');
          }
          
          // Pontos extras por imports corretos
          if (importCheck.hasReactImport && importCheck.hasDefaultExport) {
            results.score += 10;
          }
          if (importCheck.hasMultisensoryHook) {
            results.score += 5;
          }
          if (importCheck.hasAccessibilityContext) {
            results.score += 5;
          }
        }
      }
    } else {
      results.issues.push(`Arquivo obrigatório ausente: ${game[fileType]}`);
    }
  });
  
  // Verificar arquivos opcionais
  optionalFiles.forEach(fileType => {
    const filePath = path.join(game.path, game[fileType]);
    const exists = fileExists(filePath);
    
    results.files[fileType] = {
      exists,
      path: filePath,
      optional: true
    };
    
    if (exists) {
      results.score += 10; // 10 pontos por arquivo opcional
    }
  });
  
  // Calcular score final (máximo 115: 75 obrigatórios + 20 imports + 10 opcionais + 10 bonus)
  results.scorePercentage = Math.round((results.score / 115) * 100);
  
  // Status baseado no score
  if (results.scorePercentage >= 90) {
    results.status = '🟢 Excelente';
  } else if (results.scorePercentage >= 75) {
    results.status = '🟡 Bom';
  } else if (results.scorePercentage >= 50) {
    results.status = '🟠 Precisa melhorar';
  } else {
    results.status = '🔴 Crítico';
  }
  
  // Exibir resultado
  console.log(`   Status: ${results.status} (${results.scorePercentage}%)`);
  
  if (results.issues.length > 0) {
    console.log('   Problemas:');
    results.issues.forEach(issue => {
      console.log(`     - ${issue}`);
    });
  }
  
  console.log();
  
  return results;
}

/**
 * Executar teste em todos os jogos
 */
function runGameTests() {
  const results = [];
  let totalScore = 0;
  let gamesWithIssues = 0;
  
  GAMES.forEach(game => {
    const result = checkGame(game);
    results.push(result);
    totalScore += result.scorePercentage;
    
    if (result.issues.length > 0) {
      gamesWithIssues++;
    }
  });
  
  // Relatório final
  console.log('📊 RELATÓRIO FINAL DOS JOGOS:');
  console.log('=============================\n');
  
  const averageScore = Math.round(totalScore / GAMES.length);
  
  console.log(`🎯 Score médio: ${averageScore}%`);
  console.log(`✅ Jogos sem problemas: ${GAMES.length - gamesWithIssues}/${GAMES.length}`);
  console.log(`⚠️  Jogos com problemas: ${gamesWithIssues}/${GAMES.length}\n`);
  
  // Detalhar jogos com problemas
  if (gamesWithIssues > 0) {
    console.log('🔧 JOGOS QUE PRECISAM DE ATENÇÃO:');
    results.forEach(result => {
      if (result.issues.length > 0) {
        console.log(`❌ ${result.name} (${result.scorePercentage}%):`);
        result.issues.forEach(issue => {
          console.log(`     - ${issue}`);
        });
      }
    });
    console.log();
  }
  
  // Ranking dos jogos
  console.log('🏆 RANKING DOS JOGOS:');
  const sortedResults = [...results].sort((a, b) => b.scorePercentage - a.scorePercentage);
  sortedResults.forEach((result, index) => {
    const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '  ';
    console.log(`${medal} ${index + 1}. ${result.name}: ${result.scorePercentage}%`);
  });
  
  console.log('\n💡 PRÓXIMOS PASSOS:');
  if (averageScore >= 90) {
    console.log('🎉 Todos os jogos estão em excelente estado!');
    console.log('🔄 Execute testes funcionais no navegador');
    console.log('📈 Considere implementar melhorias de performance');
  } else if (averageScore >= 75) {
    console.log('✅ A maioria dos jogos está funcionando bem');
    console.log('🔧 Corrigir problemas identificados nos jogos com menor score');
    console.log('🧪 Executar testes no navegador');
  } else {
    console.log('⚠️  Vários jogos precisam de correções');
    console.log('🔧 Priorizar correção dos jogos com score < 75%');
    console.log('📋 Revisar imports e estrutura de arquivos');
  }
  
  return {
    averageScore,
    gamesWithIssues,
    totalGames: GAMES.length,
    results
  };
}

// Executar testes
runGameTests();
