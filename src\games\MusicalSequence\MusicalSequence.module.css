/**
 * @file MusicalSequence.module.css
 * @description Estilos modulares para o Jogo de Sequência Musical - Padrão Elegante Unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do MusicalSequence */
.musicalSequenceGame {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activitySubtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botões TTS */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton.ttsActive {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

/* Estatísticas do jogo */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* ===== ELEGANTE SISTEMA DE CARDS ===== */

/* Área da pergunta */
.questionArea {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.questionHeader {
  margin-bottom: 2rem;
}

.questionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.repeatButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.repeatButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

/* Display dos objetos principais */
.objectsDisplay {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  min-height: 120px;
}

.countingObject {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  min-width: 100px;
  min-height: 100px;
}

.countingObject::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s ease-in-out infinite;
}

.countingObject:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

/* Animações */
@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); opacity: 1; }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); }
}

/* Cards de opções de resposta */
.answerOptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.elegantCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.elegantCard::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.elegantCard:hover::before {
  animation: shimmer 1.5s ease-in-out;
  opacity: 1;
}

.elegantCard:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.elegantCard:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

/* Estados especiais dos cards */
.elegantCard.selected {
  background: var(--success-bg);
  border-color: var(--success-border);
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
}

.elegantCard.correct {
  background: var(--success-bg);
  border-color: var(--success-border);
  animation: bounceIn 0.6s ease;
}

.elegantCard.incorrect {
  background: var(--error-bg);
  border-color: var(--error-border);
  animation: pulse 0.5s ease 2;
}

/* Compatibilidade com sistema antigo */
.answerButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.answerButton::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.answerButton:hover::before {
  animation: shimmer 1.5s ease-in-out;
  opacity: 1;
}

.answerButton:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.answerButton:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

.optionNumber {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Feedback visual */
.feedbackContainer {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.feedbackContainer.correct {
  background: var(--success-bg);
  border: 1px solid var(--success-border);
  color: white;
  animation: bounceIn 0.6s ease;
}

.feedbackContainer.incorrect {
  background: var(--error-bg);
  border: 1px solid var(--error-border);
  color: white;
  animation: pulse 0.5s ease 2;
}

/* Controles do jogo */
.gameControls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.controlButton:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* TTS Indicators */
.ttsIndicator {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

/* Responsividade Mobile-First */
@media (max-width: 768px) {
  .musicalSequenceGame {
    padding: 0.5rem;
  }

  .gameHeader {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    min-height: 60px;
  }

  .gameTitle {
    font-size: 1.4rem;
  }

  .activitySubtitle {
    font-size: 0.65rem;
    padding: 0.2rem 0.5rem;
  }

  .questionArea {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .questionTitle {
    font-size: 1.2rem;
  }

  .objectsDisplay {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .countingObject {
    padding: 1rem;
    font-size: 2rem;
    min-width: 80px;
    min-height: 80px;
  }

  .answerOptions {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }

  .elegantCard, .answerButton {
    padding: 1rem;
    font-size: 1rem;
    min-height: 60px;
  }

  .optionNumber {
    font-size: 1.5rem;
  }

  .gameControls {
    gap: 0.5rem;
  }

  .controlButton {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .gameStats {
    grid-template-columns: repeat(2, 1fr);
  }

  .answerOptions {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .gameControls {
    flex-direction: column;
    align-items: center;
  }

  .controlButton {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

/* Media query adicional para celulares em landscape e telas pequenas */
@media (max-width: 640px) and (orientation: landscape) {
  .answerOptions {
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
  }
  
  .elegantCard, .answerButton {
    padding: 0.75rem;
    font-size: 0.8rem;
    min-height: 50px;
  }
}

/* Estados de acessibilidade */
.musicalSequenceGame.high-contrast {
  --card-background: rgba(0, 0, 0, 0.8);
  --card-border: 2px solid rgba(255, 255, 255, 0.8);
}

.musicalSequenceGame.reduced-motion * {
  animation: none !important;
  transition: none !important;
}

/* Tamanhos de fonte personalizáveis */
.musicalSequenceGame[data-font-size="small"] {
  font-size: 0.875rem;
}

.musicalSequenceGame[data-font-size="large"] {
  font-size: 1.25rem;
}

/* Temas de cor */
.musicalSequenceGame[data-theme="dark"] {
  --gradient-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.musicalSequenceGame[data-theme="light"] {
  --gradient-bg: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #2d3436;
}
.musicalSequenceGame {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Container principal - genérico para outros jogos */
.gameContainer {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activitySubtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Estatísticas */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* Área da pergunta */
.questionArea {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

.questionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: white;
}

.objectsDisplay {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 2rem 0;
  min-height: 150px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.countingObject {
  font-size: 3rem;
  transition: all 0.3s ease;
  animation: objectAppear 0.5s ease-out;
  cursor: default;
  user-select: none;
}

.countingObject:hover {
  transform: scale(1.1);
}

@keyframes objectAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Opções de resposta */
.answerOptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.answerButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.answerButton:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

.answerButton.correct {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: correctPulse 0.6s ease-in-out;
}

.answerButton.incorrect {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: incorrectShake 0.6s ease-in-out;
}

.answerButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes correctPulse {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes incorrectShake {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Controles do jogo */
.gameControls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.nextButton {
  background: var(--success-bg);
  border-color: var(--success-border);
}

.nextButton:hover {
  background: rgba(76, 175, 80, 0.4);
}

/* Feedback */
.feedbackMessage {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: messageSlide 3s ease-in-out;
}

.feedbackMessage.success {
  background: var(--success-bg);
  color: white;
}

.feedbackMessage.error {
  background: var(--error-bg);
  color: white;
}

@keyframes messageSlide {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Botões TTS */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

.ttsActive {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

.ttsInactive {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

.repeatButton {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.repeatButton:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

.repeatButton:active {
  transform: scale(0.95);
}

.ttsIndicator {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(74, 144, 226, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  z-index: 5;
  pointer-events: none;
  transition: all 0.2s ease;
}

.answerButton:hover .ttsIndicator {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* Menu de atividades */
.activityMenu {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.activityButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.activityButton.active {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.activeIndicator {
  color: var(--success-color);
  font-size: 0.8rem;
  margin-left: 0.5rem;
  animation: pulse 1.5s infinite;
}

.activityIcon {
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.activityName {
  font-size: 0.9rem;
  font-weight: 500;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Atividade de Som */
.soundActivity {
  text-align: center;
}

.soundIndicator {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: soundPulse 2s ease-in-out infinite;
}

@keyframes soundPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

.soundButton {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

.soundButton:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
.estimationDisplay {
  position: relative;
}

.estimationObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

.estimationObject {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

.estimationTip {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
.sequenceDisplay {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

.sequenceNumber {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

/* Grid de instrumentos musicais */
.instrumentsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.instrumentButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.instrumentButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

.instrumentButton:active {
  transform: translateY(-2px) scale(1.02);
}

.instrumentButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.instrumentButton.playing {
  background: var(--success-bg);
  border-color: var(--success-border);
  animation: instrumentPulse 0.6s ease-in-out;
  transform: scale(1.1);
}

.instrumentIcon {
  font-size: 2.5rem;
  margin-bottom: 0.25rem;
}

.instrumentName {
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
}

@keyframes instrumentPulse {
  0%, 100% { transform: scale(1.1); }
  50% { transform: scale(1.2); }
}

/* Área principal do jogo */
.gameArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

/* Controles do jogo */
.gameControls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.controlButton:active {
  transform: translateY(0);
}

/* =====================================================
   🎵 INTERFACES ESPECÍFICAS PARA CADA ATIVIDADE
   ===================================================== */

/* Interface base para atividades */
.activityInterface {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
}

.instructionPanel {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
}

.instructionPanel h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: white;
}

.instructionPanel p {
  font-size: 1rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* 🔄 REPRODUÇÃO DE SEQUÊNCIA */
.sequenceIndicator {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

.sequenceItem {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  font-size: 2rem;
  min-width: 60px;
  text-align: center;
  transition: all 0.3s ease;
}

.sequenceItem.playing {
  background: var(--success-bg);
  border-color: var(--success-border);
  transform: scale(1.2);
  animation: pulse 0.6s ease-in-out;
}

.progressArea {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  text-align: center;
}

.playerSequence {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  margin: 1rem 0;
}

.playerItem {
  background: rgba(76, 175, 80, 0.3);
  border: 2px solid rgba(76, 175, 80, 0.5);
  border-radius: 8px;
  padding: 0.5rem;
  font-size: 1.5rem;
  min-width: 40px;
  text-align: center;
}

/* 🥁 PADRÕES RÍTMICOS */
.rhythmInterface {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
}

.rhythmDisplay {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  width: 100%;
}

.rhythmPattern {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

.rhythmBeat {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.rhythmBeat.active {
  background: var(--success-bg);
  border-color: var(--success-border);
  transform: scale(1.2);
}

.rhythmControls {
  display: flex;
  gap: 1rem;
}

.rhythmButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.rhythmButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* 🎼 COMPLETAR MELODIA */
.melodyInterface {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.melodyDisplay {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

.melodyPattern {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 1rem 0;
}

.melodyNote {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  font-size: 2rem;
  min-width: 60px;
  text-align: center;
}

.melodyNote.missing {
  background: rgba(255, 193, 7, 0.3);
  border-color: rgba(255, 193, 7, 0.5);
  animation: pulse 2s infinite;
}

.melodyOptions {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

.noteOptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.noteButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.noteButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

/* 🎺 RECONHECIMENTO DE INSTRUMENTOS */
.recognitionInterface {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.soundDisplay {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

.soundIndicator {
  margin: 2rem 0;
}

.soundWave {
  font-size: 2rem;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.soundWave.playing {
  opacity: 1;
  animation: soundPulse 1s ease-in-out infinite;
}

@keyframes soundPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.playButton {
  background: var(--success-bg);
  border: 2px solid var(--success-border);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.playButton:hover {
  background: rgba(76, 175, 80, 0.5);
  transform: translateY(-2px);
}

.playButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.instrumentOptions {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

.optionsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.optionButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.optionButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.optionButton.selected {
  background: var(--success-bg);
  border-color: var(--success-border);
  transform: scale(1.05);
}

.optionIcon {
  font-size: 2rem;
}

.optionName {
  font-size: 0.9rem;
  font-weight: 600;
}

/* 🧠 MEMÓRIA MUSICAL */
.memoryInterface {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.memoryChallenge {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

.memorySequence {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin: 1rem 0;
}

.memoryItem {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 60px;
  transition: all 0.3s ease;
}

.memoryItem.playing {
  background: var(--success-bg);
  border-color: var(--success-border);
  transform: scale(1.1);
}

.memoryItem.completed {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

.itemNumber {
  font-size: 0.8rem;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.itemIcon {
  font-size: 1.5rem;
}

.memoryInput {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
}

.memoryProgress {
  margin-top: 1rem;
  text-align: center;
}

.progressBar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  height: 10px;
  margin: 0.5rem 0;
  overflow: hidden;
}

.progressFill {
  background: linear-gradient(90deg, #4CAF50, #2196F3);
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 10px;
}

/* 🎨 COMPOSIÇÃO CRIATIVA */
.compositionInterface {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.compositionArea {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
}

.compositionSequence {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin: 1rem 0;
  min-height: 80px;
  align-items: center;
}

.compositionNote {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  min-width: 60px;
}

.noteNumber {
  font-size: 0.8rem;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.noteIcon {
  font-size: 1.5rem;
}

.removeNote {
  position: absolute;
  top: -5px;
  right: -5px;
  background: rgba(244, 67, 54, 0.8);
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  color: white;
  cursor: pointer;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emptyComposition {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  padding: 2rem;
}

.compositionControls {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
}

.compositionActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.actionButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.actionButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* =====================================================
   🎵 ANIMAÇÕES PARA FUNCIONALIDADES ESPECÍFICAS
   ===================================================== */

/* Animação de pulse para elementos ativos */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Animação de bounce para elementos faltantes */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Animação de glow para elementos selecionados */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
  }
}

/* Estilos para visualizadores de frequência */
.frequencyVisualizer {
  position: relative;
  height: 60px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  margin: 1rem 0;
}

.frequencyBar {
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.frequencyBar.playing {
  animation: pulse 0.5s infinite;
}

/* Estilos para padrões visuais */
.patternElement {
  transition: all 0.3s ease;
  border-radius: 16px;
  padding: 1.5rem;
  min-width: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.patternElement.completed {
  border: 3px solid rgba(76, 175, 80, 0.6);
  background: rgba(76, 175, 80, 0.2);
}

.patternElement.missing {
  border: 4px dashed rgba(255, 193, 7, 0.8);
  background: rgba(255, 193, 7, 0.3);
  animation: pulse 2s infinite;
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.5);
}

/* Estilos para botões de gravação */
.recordingButton {
  background: rgba(244, 67, 54, 0.3);
  border: 2px solid rgba(244, 67, 54, 0.5);
  animation: pulse 1s infinite;
}

.recordingButton:hover {
  background: rgba(244, 67, 54, 0.5);
}

/* Estilos para elementos selecionados */
.selectedElement {
  border: 3px solid rgba(255, 193, 7, 0.8) !important;
  background: rgba(255, 193, 7, 0.2) !important;
  transform: scale(1.05);
  animation: glow 2s infinite;
}

.sequenceNumber:hover {
  transform: scale(1.05);
}

.sequenceArrow {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
}

.sequenceMissing {
  background: rgba(255, 255, 80, 0.3) !important;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  animation: missingPulse 2s ease-in-out infinite;
}

@keyframes missingPulse {
  0%, 100% { box-shadow: 0 0 0 rgba(255, 255, 80, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 80, 0.6); }
}

/* Atividade de Comparação */
.comparisonDisplay {
  display: flex;
  gap: 3rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.comparisonGroup {
  text-align: center;
  background: var(--card-background);
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.comparisonGroup:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

.comparisonObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  max-width: 150px;
  margin-bottom: 1rem;
}

.comparisonNumber {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  background: var(--card-background);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Atividade de Padrões */
.patternDisplay {
  text-align: center;
}

.patternDescription {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  background: rgba(156, 39, 176, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #9C27B0;
}

.patternSequence {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

.patternNumber {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.1));
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  border: 1px solid rgba(156, 39, 176, 0.4);
  transition: all 0.3s ease;
}

.patternNumber:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
  .activityMenu {
    gap: 0.25rem;
  }

  .activityButton {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }

  .comparisonDisplay {
    gap: 1.5rem;
  }

  .sequenceDisplay,
  .patternSequence {
    gap: 0.5rem;
    font-size: 1.5rem;
  }

  .sequenceNumber,
  .patternNumber {
    padding: 0.75rem;
    min-width: 50px;
  }

  .gameStats {
    grid-template-columns: repeat(2, 1fr);
  }

  .instrumentsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .instrumentButton {
    padding: 1rem;
    min-height: 80px;
  }

  .instrumentIcon {
    font-size: 2rem;
  }

  .instrumentName {
    font-size: 0.8rem;
  }

  /* Responsividade para interfaces de atividades */
  .sequenceIndicator,
  .playerSequence,
  .rhythmPattern,
  .melodyPattern,
  .memorySequence,
  .compositionSequence {
    gap: 0.5rem;
  }

  .optionsGrid,
  .noteOptions {
    grid-template-columns: repeat(2, 1fr);
  }

  .rhythmControls,
  .compositionActions {
    flex-direction: column;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .activityMenu {
    flex-direction: column;
    align-items: center;
  }

  .activityButton {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .comparisonDisplay {
    flex-direction: column;
    gap: 1rem;
  }

  .sequenceDisplay,
  .patternSequence {
    flex-direction: column;
    gap: 0.5rem;
  }

  .sequenceArrow {
    transform: rotate(90deg);
  }

  .instrumentsGrid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    max-width: 300px;
  }

  .instrumentButton {
    padding: 0.75rem;
    min-height: 70px;
  }

  .instrumentIcon {
    font-size: 1.8rem;
  }

  .instrumentName {
    font-size: 0.75rem;
  }

  .gameControls {
    flex-direction: column;
    gap: 0.5rem;
  }

  .controlButton {
    width: 100%;
    max-width: 250px;
  }

  /* Responsividade mobile para interfaces de atividades */
  .activityInterface {
    gap: 1rem;
  }

  .instructionPanel,
  .rhythmDisplay,
  .melodyDisplay,
  .melodyOptions,
  .soundDisplay,
  .instrumentOptions,
  .memoryChallenge,
  .memoryInput,
  .compositionArea,
  .compositionControls {
    padding: 1rem;
  }

  .sequenceIndicator,
  .playerSequence,
  .rhythmPattern,
  .melodyPattern,
  .memorySequence,
  .compositionSequence {
    flex-direction: column;
    gap: 0.5rem;
  }

  .optionsGrid,
  .noteOptions {
    grid-template-columns: 1fr;
  }

  .rhythmControls,
  .compositionActions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .sequenceItem,
  .playerItem,
  .rhythmBeat,
  .melodyNote,
  .memoryItem,
  .compositionNote {
    font-size: 1.2rem;
    padding: 0.75rem;
    min-width: 50px;
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
.reduced-motion {
  .answerButton, .controlButton, .countingObject, .feedbackMessage, .soundIndicator, .sequenceMissing {
    animation: none !important;
    transition: none !important;
  }
}