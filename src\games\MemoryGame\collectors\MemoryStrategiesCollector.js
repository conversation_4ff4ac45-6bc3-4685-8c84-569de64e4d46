/**
 * 🧭 MEMORY STRATEGIES COLLECTOR - Portal Betina V3
 * Coletor especializado em identificação e análise de estratégias de memória
 * Detecta padrões de estratégias utilizadas pelo usuário
 */

export class MemoryStrategiesCollector {
  constructor() {
    this.name = 'MemoryStrategiesCollector';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    this.sessionData = {
      currentSession: null,
      strategyEvents: [],
      patternDetections: [],
      strategicBehaviors: []
    };
    
    console.log('🧭 MemoryStrategiesCollector inicializado');
  }

  /**
   * Inicializar nova sessão de coleta
   */
  startSession(sessionId, gameData = {}) {
    this.sessionData.currentSession = {
      sessionId,
      startTime: Date.now(),
      difficulty: gameData.difficulty || 'unknown',
      theme: gameData.theme || 'unknown',
      totalCards: gameData.cardsCount || 0,
      gridLayout: this.calculateGridLayout(gameData.cardsCount || 0),
      strategyEvents: [],
      detectedStrategies: {
        systematicSearch: { detected: false, confidence: 0, events: [] },
        spatialMemory: { detected: false, confidence: 0, events: [] },
        visualAssociation: { detected: false, confidence: 0, events: [] },
        sequentialProcessing: { detected: false, confidence: 0, events: [] },
        clusteringStrategy: { detected: false, confidence: 0, events: [] },
        trialAndError: { detected: false, confidence: 0, events: [] },
        patternRecognition: { detected: false, confidence: 0, events: [] },
        eliminationStrategy: { detected: false, confidence: 0, events: [] }
      },
      strategicMetrics: {
        consistency: 0,
        efficiency: 0,
        adaptability: 0,
        sophistication: 0,
        persistence: 0,
        planningCapacity: 0
      },
      searchPatterns: {
        systematicness: 0,
        coverage: 0,
        redundancy: 0,
        directionality: 'none'
      }
    };
    
    console.log(`🧭 MemoryStrategiesCollector: Nova sessão iniciada - ${sessionId}`);
  }

  /**
   * Calcular layout do grid
   */
  calculateGridLayout(cardCount) {
    switch (cardCount) {
      case 4: return { rows: 2, cols: 2 };
      case 6: return { rows: 2, cols: 3 };
      case 8: return { rows: 2, cols: 4 };
      case 12: return { rows: 3, cols: 4 };
      case 16: return { rows: 4, cols: 4 };
      default: 
        const size = Math.ceil(Math.sqrt(cardCount));
        return { rows: size, cols: size };
    }
  }

  /**
   * Registrar interação para análise de estratégias
   */
  recordInteraction(interactionData) {
    if (!this.sessionData.currentSession) {
      console.warn('🧭 MemoryStrategiesCollector: Sessão não iniciada');
      return;
    }

    const timestamp = Date.now();
    const session = this.sessionData.currentSession;
    
    const strategyEvent = {
      timestamp,
      cardId: interactionData.cardId,
      position: interactionData.position || { row: 0, col: 0, index: 0 },
      isMatch: interactionData.isMatch || false,
      isFirstCard: interactionData.isFirstCard || false,
      reactionTime: interactionData.reactionTime || 0,
      attemptNumber: this.getAttemptNumber(interactionData),
      searchSequence: this.calculateSearchSequence(interactionData),
      strategicIndicators: this.detectStrategicIndicators(interactionData),
      spatialRelation: this.analyzeSpatialRelation(interactionData),
      temporalPattern: this.analyzeTemporalPattern(interactionData)
    };

    // Adicionar ao histórico da sessão
    session.strategyEvents.push(strategyEvent);
    
    // Analisar padrões de estratégia
    this.analyzeStrategyPatterns(strategyEvent);
    
    // Atualizar métricas estratégicas
    this.updateStrategicMetrics(strategyEvent);
    
    // Detectar estratégias específicas
    this.detectSpecificStrategies(strategyEvent);
    
    console.log('🧭 MemoryStrategiesCollector: Evento estratégico registrado', {
      strategicIndicators: strategyEvent.strategicIndicators,
      detectedPatterns: Object.keys(session.detectedStrategies).filter(s => 
        session.detectedStrategies[s].detected
      )
    });
  }

  /**
   * Obter número da tentativa
   */
  getAttemptNumber(interactionData) {
    const session = this.sessionData.currentSession;
    const cardId = interactionData.cardId;
    
    const cardClicks = session.strategyEvents.filter(e => e.cardId === cardId);
    return cardClicks.length + 1;
  }

  /**
   * Calcular sequência de busca
   */
  calculateSearchSequence(interactionData) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.strategyEvents.slice(-5); // Últimos 5 eventos
    
    if (recentEvents.length === 0) {
      return { 
        sequenceType: 'initial',
        direction: 'none',
        pattern: 'single_click'
      };
    }
    
    const positions = recentEvents.map(e => e.position);
    positions.push(interactionData.position);
    
    return {
      sequenceType: this.classifySequenceType(positions),
      direction: this.calculateDirection(positions),
      pattern: this.identifySearchPattern(positions)
    };
  }

  /**
   * Classificar tipo de sequência
   */
  classifySequenceType(positions) {
    if (positions.length < 2) return 'initial';
    
    const movements = [];
    for (let i = 1; i < positions.length; i++) {
      const prev = positions[i - 1];
      const curr = positions[i];
      movements.push({
        deltaRow: curr.row - prev.row,
        deltaCol: curr.col - prev.col
      });
    }
    
    // Verificar padrões sistemáticos
    const isLinear = movements.every(m => 
      (m.deltaRow === 0 && Math.abs(m.deltaCol) === 1) || 
      (m.deltaCol === 0 && Math.abs(m.deltaRow) === 1)
    );
    
    const isDiagonal = movements.every(m => 
      Math.abs(m.deltaRow) === Math.abs(m.deltaCol)
    );
    
    const isAdjacent = movements.every(m => 
      Math.abs(m.deltaRow) <= 1 && Math.abs(m.deltaCol) <= 1
    );
    
    if (isLinear) return 'linear';
    if (isDiagonal) return 'diagonal';
    if (isAdjacent) return 'adjacent';
    
    return 'random';
  }

  /**
   * Calcular direção predominante
   */
  calculateDirection(positions) {
    if (positions.length < 2) return 'none';
    
    let horizontal = 0;
    let vertical = 0;
    
    for (let i = 1; i < positions.length; i++) {
      const prev = positions[i - 1];
      const curr = positions[i];
      
      if (curr.col > prev.col) horizontal++;
      else if (curr.col < prev.col) horizontal--;
      
      if (curr.row > prev.row) vertical++;
      else if (curr.row < prev.row) vertical--;
    }
    
    if (Math.abs(horizontal) > Math.abs(vertical)) {
      return horizontal > 0 ? 'right' : 'left';
    } else if (Math.abs(vertical) > Math.abs(horizontal)) {
      return vertical > 0 ? 'down' : 'up';
    }
    
    return 'mixed';
  }

  /**
   * Identificar padrão de busca
   */
  identifySearchPattern(positions) {
    if (positions.length < 3) return 'insufficient_data';
    
    // Verificar busca sistemática (linha por linha, coluna por coluna)
    const rowPattern = this.checkRowByRowPattern(positions);
    if (rowPattern) return 'row_by_row';
    
    const colPattern = this.checkColumnByColumnPattern(positions);
    if (colPattern) return 'column_by_column';
    
    // Verificar busca em espiral
    const spiralPattern = this.checkSpiralPattern(positions);
    if (spiralPattern) return 'spiral';
    
    // Verificar busca por quadrantes
    const quadrantPattern = this.checkQuadrantPattern(positions);
    if (quadrantPattern) return 'quadrant_based';
    
    return 'unstructured';
  }

  /**
   * Verificar padrão linha por linha
   */
  checkRowByRowPattern(positions) {
    const sortedByRow = [...positions].sort((a, b) => 
      a.row !== b.row ? a.row - b.row : a.col - b.col
    );
    
    // Verificar se as posições seguem ordem sequencial por linha
    let isSequential = true;
    for (let i = 1; i < sortedByRow.length; i++) {
      const prev = sortedByRow[i - 1];
      const curr = sortedByRow[i];
      
      if (curr.row === prev.row) {
        if (curr.col !== prev.col + 1) {
          isSequential = false;
          break;
        }
      } else if (curr.row === prev.row + 1) {
        if (curr.col !== 0) {
          isSequential = false;
          break;
        }
      } else {
        isSequential = false;
        break;
      }
    }
    
    return isSequential;
  }

  /**
   * Verificar padrão coluna por coluna
   */
  checkColumnByColumnPattern(positions) {
    const sortedByCol = [...positions].sort((a, b) => 
      a.col !== b.col ? a.col - b.col : a.row - b.row
    );
    
    let isSequential = true;
    for (let i = 1; i < sortedByCol.length; i++) {
      const prev = sortedByCol[i - 1];
      const curr = sortedByCol[i];
      
      if (curr.col === prev.col) {
        if (curr.row !== prev.row + 1) {
          isSequential = false;
          break;
        }
      } else if (curr.col === prev.col + 1) {
        if (curr.row !== 0) {
          isSequential = false;
          break;
        }
      } else {
        isSequential = false;
        break;
      }
    }
    
    return isSequential;
  }

  /**
   * Verificar padrão em espiral
   */
  checkSpiralPattern(positions) {
    // Implementação simplificada - pode ser expandida
    const center = this.calculateCenter(positions);
    const distances = positions.map(pos => 
      Math.sqrt((pos.row - center.row) ** 2 + (pos.col - center.col) ** 2)
    );
    
    // Verificar se as distâncias aumentam gradualmente
    let increasing = true;
    for (let i = 1; i < distances.length; i++) {
      if (distances[i] < distances[i - 1] - 0.5) {
        increasing = false;
        break;
      }
    }
    
    return increasing;
  }

  /**
   * Calcular centro do grid
   */
  calculateCenter(positions) {
    const avgRow = positions.reduce((sum, pos) => sum + pos.row, 0) / positions.length;
    const avgCol = positions.reduce((sum, pos) => sum + pos.col, 0) / positions.length;
    
    return { row: avgRow, col: avgCol };
  }

  /**
   * Verificar padrão por quadrantes
   */
  checkQuadrantPattern(positions) {
    const session = this.sessionData.currentSession;
    const { rows, cols } = session.gridLayout;
    
    const midRow = rows / 2;
    const midCol = cols / 2;
    
    // Classificar posições por quadrante
    const quadrants = { topLeft: [], topRight: [], bottomLeft: [], bottomRight: [] };
    
    positions.forEach(pos => {
      if (pos.row < midRow) {
        if (pos.col < midCol) quadrants.topLeft.push(pos);
        else quadrants.topRight.push(pos);
      } else {
        if (pos.col < midCol) quadrants.bottomLeft.push(pos);
        else quadrants.bottomRight.push(pos);
      }
    });
    
    // Verificar se há concentração em quadrantes específicos
    const nonEmptyQuadrants = Object.values(quadrants).filter(q => q.length > 0).length;
    return nonEmptyQuadrants <= 2; // Concentração em poucos quadrantes
  }

  /**
   * Detectar indicadores estratégicos
   */
  detectStrategicIndicators(interactionData) {
    const indicators = [];
    const session = this.sessionData.currentSession;
    
    // Indicador de sistematicidade
    if (this.isSystematicMove(interactionData)) {
      indicators.push('systematic_approach');
    }
    
    // Indicador de memória espacial
    if (this.usesSpatialMemory(interactionData)) {
      indicators.push('spatial_memory_usage');
    }
    
    // Indicador de tentativa e erro
    if (this.isTrialAndError(interactionData)) {
      indicators.push('trial_and_error');
    }
    
    // Indicador de reconhecimento de padrão
    if (this.showsPatternRecognition(interactionData)) {
      indicators.push('pattern_recognition');
    }
    
    // Indicador de persistência
    if (this.showsPersistence(interactionData)) {
      indicators.push('persistence');
    }
    
    return indicators;
  }

  /**
   * Verificar movimento sistemático
   */
  isSystematicMove(interactionData) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.strategyEvents.slice(-3);
    
    if (recentEvents.length < 2) return false;
    
    const positions = recentEvents.map(e => e.position);
    positions.push(interactionData.position);
    
    return this.classifySequenceType(positions) === 'linear';
  }

  /**
   * Verificar uso de memória espacial
   */
  usesSpatialMemory(interactionData) {
    const session = this.sessionData.currentSession;
    const position = interactionData.position;
    
    // Verificar se já clicou nesta posição antes
    const previousClicks = session.strategyEvents.filter(e => 
      e.position.row === position.row && e.position.col === position.col
    );
    
    // Se clicou novamente após um intervalo, indica memória espacial
    if (previousClicks.length > 0) {
      const lastClick = previousClicks[previousClicks.length - 1];
      const timeDiff = Date.now() - lastClick.timestamp;
      return timeDiff > 5000; // Mais de 5 segundos de intervalo
    }
    
    return false;
  }

  /**
   * Verificar tentativa e erro
   */
  isTrialAndError(interactionData) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.strategyEvents.slice(-5);
    
    // Verificar se há muitos cliques sem padrão definido
    const positions = recentEvents.map(e => e.position);
    const uniquePositions = new Set(positions.map(p => `${p.row}-${p.col}`));
    
    return positions.length >= 4 && uniquePositions.size === positions.length;
  }

  /**
   * Verificar reconhecimento de padrão
   */
  showsPatternRecognition(interactionData) {
    const session = this.sessionData.currentSession;
    const recentMatches = session.strategyEvents.filter(e => 
      e.isMatch && Date.now() - e.timestamp < 30000 // Últimos 30 segundos
    );
    
    // Se conseguiu múltiplos matches recentemente, indica reconhecimento
    return recentMatches.length >= 2;
  }

  /**
   * Verificar persistência
   */
  showsPersistence(interactionData) {
    const session = this.sessionData.currentSession;
    const cardId = interactionData.cardId;
    
    // Verificar se já tentou esta carta múltiplas vezes
    const cardAttempts = session.strategyEvents.filter(e => e.cardId === cardId);
    return cardAttempts.length >= 2;
  }

  /**
   * Analisar relação espacial
   */
  analyzeSpatialRelation(interactionData) {
    const session = this.sessionData.currentSession;
    const lastEvent = session.strategyEvents[session.strategyEvents.length - 1];
    
    if (!lastEvent) {
      return { relation: 'initial', distance: 0, direction: 'none' };
    }
    
    const currPos = interactionData.position;
    const lastPos = lastEvent.position;
    
    const distance = Math.sqrt(
      (currPos.row - lastPos.row) ** 2 + 
      (currPos.col - lastPos.col) ** 2
    );
    
    const deltaRow = currPos.row - lastPos.row;
    const deltaCol = currPos.col - lastPos.col;
    
    let relation = 'distant';
    if (distance <= 1.5) relation = 'adjacent';
    else if (distance <= 2.5) relation = 'nearby';
    
    let direction = 'none';
    if (Math.abs(deltaRow) > Math.abs(deltaCol)) {
      direction = deltaRow > 0 ? 'down' : 'up';
    } else if (Math.abs(deltaCol) > Math.abs(deltaRow)) {
      direction = deltaCol > 0 ? 'right' : 'left';
    }
    
    return { relation, distance, direction };
  }

  /**
   * Analisar padrão temporal
   */
  analyzeTemporalPattern(interactionData) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.strategyEvents.slice(-3);
    
    if (recentEvents.length === 0) {
      return { pattern: 'initial', rhythm: 'none', consistency: 0 };
    }
    
    const intervals = [];
    for (let i = 1; i < recentEvents.length; i++) {
      intervals.push(recentEvents[i].timestamp - recentEvents[i - 1].timestamp);
    }
    
    if (interactionData.timestamp && recentEvents.length > 0) {
      intervals.push(interactionData.timestamp - recentEvents[recentEvents.length - 1].timestamp);
    }
    
    if (intervals.length === 0) {
      return { pattern: 'single', rhythm: 'none', consistency: 0 };
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => 
      sum + (interval - avgInterval) ** 2, 0
    ) / intervals.length;
    
    const consistency = 1 - Math.min(1, Math.sqrt(variance) / avgInterval);
    
    let rhythm = 'irregular';
    if (avgInterval < 1000) rhythm = 'fast';
    else if (avgInterval < 3000) rhythm = 'moderate';
    else rhythm = 'slow';
    
    let pattern = 'variable';
    if (consistency > 0.8) pattern = 'consistent';
    else if (consistency > 0.5) pattern = 'somewhat_consistent';
    
    return { pattern, rhythm, consistency };
  }

  /**
   * Analisar padrões de estratégia
   */
  analyzeStrategyPatterns(strategyEvent) {
    const session = this.sessionData.currentSession;
    
    // Analisar sistematicidade geral
    this.updateSystematicness();
    
    // Analisar cobertura do espaço de busca
    this.updateSearchCoverage();
    
    // Analisar redundância nas tentativas
    this.updateSearchRedundancy();
    
    // Analisar direcionalidade
    this.updateSearchDirectionality();
  }

  /**
   * Atualizar sistematicidade
   */
  updateSystematicness() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length < 3) return;
    
    const systematicMoves = events.filter(e => 
      e.strategicIndicators.includes('systematic_approach')
    ).length;
    
    session.searchPatterns.systematicness = systematicMoves / events.length;
  }

  /**
   * Atualizar cobertura de busca
   */
  updateSearchCoverage() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    const { rows, cols } = session.gridLayout;
    
    const visitedPositions = new Set(
      events.map(e => `${e.position.row}-${e.position.col}`)
    );
    
    const totalPositions = rows * cols;
    session.searchPatterns.coverage = visitedPositions.size / totalPositions;
  }

  /**
   * Atualizar redundância de busca
   */
  updateSearchRedundancy() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length === 0) return;
    
    const totalClicks = events.length;
    const uniquePositions = new Set(
      events.map(e => `${e.position.row}-${e.position.col}`)
    ).size;
    
    session.searchPatterns.redundancy = 1 - (uniquePositions / totalClicks);
  }

  /**
   * Atualizar direcionalidade
   */
  updateSearchDirectionality() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length < 2) return;
    
    const directions = { up: 0, down: 0, left: 0, right: 0 };
    
    for (let i = 1; i < events.length; i++) {
      const prev = events[i - 1].position;
      const curr = events[i].position;
      
      if (curr.row < prev.row) directions.up++;
      else if (curr.row > prev.row) directions.down++;
      
      if (curr.col < prev.col) directions.left++;
      else if (curr.col > prev.col) directions.right++;
    }
    
    const maxDirection = Object.keys(directions).reduce((a, b) => 
      directions[a] > directions[b] ? a : b
    );
    
    const maxCount = directions[maxDirection];
    const totalMoves = events.length - 1;
    
    if (maxCount / totalMoves > 0.6) {
      session.searchPatterns.directionality = maxDirection;
    } else {
      session.searchPatterns.directionality = 'mixed';
    }
  }

  /**
   * Atualizar métricas estratégicas
   */
  updateStrategicMetrics(strategyEvent) {
    const session = this.sessionData.currentSession;
    const metrics = session.strategicMetrics;
    
    // Atualizar consistência
    metrics.consistency = this.calculateConsistency();
    
    // Atualizar eficiência
    metrics.efficiency = this.calculateEfficiency();
    
    // Atualizar adaptabilidade
    metrics.adaptability = this.calculateAdaptability();
    
    // Atualizar sofisticação
    metrics.sophistication = this.calculateSophistication();
    
    // Atualizar persistência
    metrics.persistence = this.calculatePersistence();
    
    // Atualizar capacidade de planejamento
    metrics.planningCapacity = this.calculatePlanningCapacity();
  }

  /**
   * Calcular consistência
   */
  calculateConsistency() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length < 3) return 0;
    
    // Consistência baseada na repetição de padrões estratégicos
    const strategicIndicators = events.map(e => e.strategicIndicators.join(','));
    const uniquePatterns = new Set(strategicIndicators);
    
    return 1 - (uniquePatterns.size / events.length);
  }

  /**
   * Calcular eficiência
   */
  calculateEfficiency() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length === 0) return 0;
    
    const matches = events.filter(e => e.isMatch).length;
    const totalClicks = events.length;
    
    // Eficiência = matches / total de cliques
    return matches / totalClicks;
  }

  /**
   * Calcular adaptabilidade
   */
  calculateAdaptability() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length < 5) return 0;
    
    // Verificar mudanças de estratégia ao longo do tempo
    const firstHalf = events.slice(0, Math.floor(events.length / 2));
    const secondHalf = events.slice(Math.floor(events.length / 2));
    
    const firstHalfStrategies = new Set(
      firstHalf.flatMap(e => e.strategicIndicators)
    );
    const secondHalfStrategies = new Set(
      secondHalf.flatMap(e => e.strategicIndicators)
    );
    
    const intersection = new Set(
      [...firstHalfStrategies].filter(s => secondHalfStrategies.has(s))
    );
    
    const totalUnique = new Set([...firstHalfStrategies, ...secondHalfStrategies]).size;
    
    // Adaptabilidade = variedade de estratégias utilizadas
    return totalUnique > 0 ? 1 - (intersection.size / totalUnique) : 0;
  }

  /**
   * Calcular sofisticação
   */
  calculateSophistication() {
    const session = this.sessionData.currentSession;
    const searchPatterns = session.searchPatterns;
    
    // Sofisticação baseada na sistematicidade e cobertura
    const systematicness = searchPatterns.systematicness;
    const coverage = searchPatterns.coverage;
    const lowRedundancy = 1 - searchPatterns.redundancy;
    
    return (systematicness * 0.4 + coverage * 0.3 + lowRedundancy * 0.3);
  }

  /**
   * Calcular persistência
   */
  calculatePersistence() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length === 0) return 0;
    
    const persistentMoves = events.filter(e => 
      e.strategicIndicators.includes('persistence')
    ).length;
    
    return persistentMoves / events.length;
  }

  /**
   * Calcular capacidade de planejamento
   */
  calculatePlanningCapacity() {
    const session = this.sessionData.currentSession;
    const searchPatterns = session.searchPatterns;
    
    // Planejamento indicado por direcionality e baixa redundância
    const hasDirection = searchPatterns.directionality !== 'mixed' && 
                        searchPatterns.directionality !== 'none';
    const lowRedundancy = searchPatterns.redundancy < 0.3;
    const goodCoverage = searchPatterns.coverage > 0.5;
    
    let score = 0;
    if (hasDirection) score += 0.4;
    if (lowRedundancy) score += 0.3;
    if (goodCoverage) score += 0.3;
    
    return score;
  }

  /**
   * Detectar estratégias específicas
   */
  detectSpecificStrategies(strategyEvent) {
    const session = this.sessionData.currentSession;
    
    // Detectar busca sistemática
    this.detectSystematicSearch();
    
    // Detectar memória espacial
    this.detectSpatialMemory();
    
    // Detectar associação visual
    this.detectVisualAssociation();
    
    // Detectar processamento sequencial
    this.detectSequentialProcessing();
    
    // Detectar estratégia de agrupamento
    this.detectClusteringStrategy();
    
    // Detectar tentativa e erro
    this.detectTrialAndError();
    
    // Detectar reconhecimento de padrão
    this.detectPatternRecognition();
    
    // Detectar estratégia de eliminação
    this.detectEliminationStrategy();
  }

  /**
   * Detectar busca sistemática
   */
  detectSystematicSearch() {
    const session = this.sessionData.currentSession;
    const systematicness = session.searchPatterns.systematicness;
    
    const strategy = session.detectedStrategies.systematicSearch;
    
    if (systematicness > 0.6) {
      strategy.detected = true;
      strategy.confidence = systematicness;
      strategy.events = session.strategyEvents.filter(e => 
        e.strategicIndicators.includes('systematic_approach')
      );
    }
  }

  /**
   * Detectar memória espacial
   */
  detectSpatialMemory() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    const spatialEvents = events.filter(e => 
      e.strategicIndicators.includes('spatial_memory_usage')
    );
    
    const strategy = session.detectedStrategies.spatialMemory;
    
    if (spatialEvents.length >= 2) {
      strategy.detected = true;
      strategy.confidence = Math.min(1, spatialEvents.length / events.length * 2);
      strategy.events = spatialEvents;
    }
  }

  /**
   * Detectar associação visual
   */
  detectVisualAssociation() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    // Verificar se há tentativas de matching baseadas em proximidade visual
    const matchingAttempts = events.filter(e => e.isMatch);
    
    const strategy = session.detectedStrategies.visualAssociation;
    
    if (matchingAttempts.length >= 2) {
      // Análise simplificada - pode ser expandida
      strategy.detected = true;
      strategy.confidence = Math.min(1, matchingAttempts.length / 5);
      strategy.events = matchingAttempts;
    }
  }

  /**
   * Detectar processamento sequencial
   */
  detectSequentialProcessing() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    const sequentialMoves = events.filter(e => 
      e.searchSequence.sequenceType === 'linear'
    );
    
    const strategy = session.detectedStrategies.sequentialProcessing;
    
    if (sequentialMoves.length >= 3) {
      strategy.detected = true;
      strategy.confidence = sequentialMoves.length / events.length;
      strategy.events = sequentialMoves;
    }
  }

  /**
   * Detectar estratégia de agrupamento
   */
  detectClusteringStrategy() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    // Verificar se há agrupamento espacial nas tentativas
    const clusters = this.identifySpatialClusters(events);
    
    const strategy = session.detectedStrategies.clusteringStrategy;
    
    if (clusters.length >= 2) {
      strategy.detected = true;
      strategy.confidence = Math.min(1, clusters.length / 4);
      strategy.events = events; // Todos os eventos contribuem para o clustering
    }
  }

  /**
   * Identificar clusters espaciais
   */
  identifySpatialClusters(events) {
    const positions = events.map(e => e.position);
    const clusters = [];
    const visited = new Set();
    
    positions.forEach((pos, index) => {
      const key = `${pos.row}-${pos.col}`;
      if (visited.has(key)) return;
      
      const cluster = [pos];
      visited.add(key);
      
      // Buscar posições adjacentes
      positions.forEach((otherPos, otherIndex) => {
        if (otherIndex === index) return;
        
        const otherKey = `${otherPos.row}-${otherPos.col}`;
        if (visited.has(otherKey)) return;
        
        const distance = Math.sqrt(
          (pos.row - otherPos.row) ** 2 + 
          (pos.col - otherPos.col) ** 2
        );
        
        if (distance <= 1.5) { // Adjacente ou próximo
          cluster.push(otherPos);
          visited.add(otherKey);
        }
      });
      
      if (cluster.length >= 2) {
        clusters.push(cluster);
      }
    });
    
    return clusters;
  }

  /**
   * Detectar tentativa e erro
   */
  detectTrialAndError() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    const trialErrorEvents = events.filter(e => 
      e.strategicIndicators.includes('trial_and_error')
    );
    
    const strategy = session.detectedStrategies.trialAndError;
    
    if (trialErrorEvents.length >= 3) {
      strategy.detected = true;
      strategy.confidence = trialErrorEvents.length / events.length;
      strategy.events = trialErrorEvents;
    }
  }

  /**
   * Detectar reconhecimento de padrão
   */
  detectPatternRecognition() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    const patternEvents = events.filter(e => 
      e.strategicIndicators.includes('pattern_recognition')
    );
    
    const strategy = session.detectedStrategies.patternRecognition;
    
    if (patternEvents.length >= 2) {
      strategy.detected = true;
      strategy.confidence = patternEvents.length / Math.max(events.length / 2, 1);
      strategy.events = patternEvents;
    }
  }

  /**
   * Detectar estratégia de eliminação
   */
  detectEliminationStrategy() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    // Verificar se evita posições já tentadas sem sucesso
    const unsuccessfulPositions = new Set();
    const avoidanceEvents = [];
    
    events.forEach(event => {
      const posKey = `${event.position.row}-${event.position.col}`;
      
      if (!event.isMatch) {
        unsuccessfulPositions.add(posKey);
      } else {
        // Verificar se evitou posições não-sucedidas
        if (unsuccessfulPositions.size > 0) {
          avoidanceEvents.push(event);
        }
      }
    });
    
    const strategy = session.detectedStrategies.eliminationStrategy;
    
    if (avoidanceEvents.length >= 2) {
      strategy.detected = true;
      strategy.confidence = avoidanceEvents.length / events.length;
      strategy.events = avoidanceEvents;
    }
  }

  /**
   * Análise completa dos dados coletados
   */
  async analyze(gameData = {}) {
    const session = this.sessionData.currentSession;
    if (!session) {
      return this.getEmptyAnalysis();
    }

    const analysis = {
      collectorName: this.name,
      version: this.version,
      sessionId: session.sessionId,
      timestamp: Date.now(),
      
      // Dados básicos da sessão
      sessionSummary: {
        totalEvents: session.strategyEvents.length,
        sessionDuration: Date.now() - session.startTime,
        difficulty: session.difficulty,
        theme: session.theme,
        totalMatches: session.strategyEvents.filter(e => e.isMatch).length,
        completed: gameData.completed || false
      },
      
      // Estratégias detectadas
      detectedStrategies: {
        primary: this.identifyPrimaryStrategy(),
        secondary: this.identifySecondaryStrategies(),
        detailed: session.detectedStrategies,
        evolution: this.analyzeStrategyEvolution()
      },
      
      // Padrões de busca
      searchPatterns: {
        ...session.searchPatterns,
        spatialDistribution: this.analyzesSpatialDistribution(),
        temporalDistribution: this.analyzeTemporalDistribution(),
        efficiencyTrend: this.analyzeEfficiencyTrend()
      },
      
      // Métricas estratégicas
      strategicMetrics: {
        ...session.strategicMetrics,
        overallEffectiveness: this.calculateOverallEffectiveness(),
        strategicFlexibility: this.calculateStrategicFlexibility(),
        learningRate: this.calculateLearningRate()
      },
      
      // Análise comportamental
      behavioralAnalysis: {
        dominantBehaviors: this.identifyDominantBehaviors(),
        behavioralConsistency: this.calculateBehavioralConsistency(),
        adaptiveCapacity: this.calculateAdaptiveCapacity(),
        strategicMaturity: this.calculateStrategicMaturity()
      },
      
      // Recomendações terapêuticas
      therapeuticRecommendations: this.generateTherapeuticRecommendations(),
      
      // Pontuação de capacidades
      capacityScores: {
        strategicPlanning: this.calculateStrategicPlanningScore(),
        executiveControl: this.calculateExecutiveControlScore(),
        cognitiveFlexibility: this.calculateCognitiveFlexibilityScore(),
        problemSolving: this.calculateProblemSolvingScore()
      }
    };

    // Armazenar análise
    this.collectedData.push(analysis);
    
    console.log('🧭 MemoryStrategiesCollector: Análise completa gerada', {
      primaryStrategy: analysis.detectedStrategies.primary?.type,
      strategicEffectiveness: analysis.strategicMetrics.overallEffectiveness,
      therapeuticRecommendations: analysis.therapeuticRecommendations.length
    });

    return analysis;
  }

  /**
   * Identificar estratégia primária
   */
  identifyPrimaryStrategy() {
    const session = this.sessionData.currentSession;
    const strategies = session.detectedStrategies;
    
    const detectedStrategies = Object.entries(strategies)
      .filter(([_, strategy]) => strategy.detected)
      .sort(([_a, a], [_b, b]) => b.confidence - a.confidence);
    
    if (detectedStrategies.length === 0) {
      return { type: 'unidentified', confidence: 0, description: 'Nenhuma estratégia clara identificada' };
    }
    
    const [strategyName, strategyData] = detectedStrategies[0];
    
    return {
      type: strategyName,
      confidence: strategyData.confidence,
      description: this.getStrategyDescription(strategyName),
      eventCount: strategyData.events.length
    };
  }

  /**
   * Identificar estratégias secundárias
   */
  identifySecondaryStrategies() {
    const session = this.sessionData.currentSession;
    const strategies = session.detectedStrategies;
    
    const detectedStrategies = Object.entries(strategies)
      .filter(([_, strategy]) => strategy.detected)
      .sort(([_a, a], [_b, b]) => b.confidence - a.confidence)
      .slice(1, 3); // Pegar as próximas 2 estratégias
    
    return detectedStrategies.map(([strategyName, strategyData]) => ({
      type: strategyName,
      confidence: strategyData.confidence,
      description: this.getStrategyDescription(strategyName),
      eventCount: strategyData.events.length
    }));
  }

  /**
   * Obter descrição da estratégia
   */
  getStrategyDescription(strategyName) {
    const descriptions = {
      'systematicSearch': 'Busca sistemática e organizada pelo espaço de jogo',
      'spatialMemory': 'Uso efetivo da memória espacial para localizar cartas',
      'visualAssociation': 'Estratégia baseada em associações visuais',
      'sequentialProcessing': 'Processamento sequencial e linear das informações',
      'clusteringStrategy': 'Agrupamento espacial das tentativas de busca',
      'trialAndError': 'Abordagem exploratória de tentativa e erro',
      'patternRecognition': 'Reconhecimento e uso de padrões identificados',
      'eliminationStrategy': 'Eliminação sistemática de opções incorretas'
    };
    
    return descriptions[strategyName] || 'Estratégia não descrita';
  }

  /**
   * Analisar evolução da estratégia
   */
  analyzeStrategyEvolution() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length < 6) {
      return { evolution: 'insufficient_data', changes: 0 };
    }
    
    // Dividir sessão em terços
    const thirdSize = Math.floor(events.length / 3);
    const firstThird = events.slice(0, thirdSize);
    const secondThird = events.slice(thirdSize, thirdSize * 2);
    const finalThird = events.slice(thirdSize * 2);
    
    const phases = [firstThird, secondThird, finalThird].map(phase => {
      const indicators = phase.flatMap(e => e.strategicIndicators);
      const uniqueIndicators = new Set(indicators);
      return Array.from(uniqueIndicators);
    });
    
    // Contar mudanças entre fases
    let changes = 0;
    for (let i = 1; i < phases.length; i++) {
      const prev = new Set(phases[i - 1]);
      const curr = new Set(phases[i]);
      const intersection = new Set([...prev].filter(x => curr.has(x)));
      const similarity = intersection.size / Math.max(prev.size, curr.size, 1);
      
      if (similarity < 0.5) changes++; // Mudança significativa
    }
    
    return {
      evolution: changes > 1 ? 'highly_adaptive' : changes === 1 ? 'moderately_adaptive' : 'stable',
      changes,
      phases: phases.length
    };
  }

  /**
   * Analisar distribuição espacial
   */
  analyzesSpatialDistribution() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    const { rows, cols } = session.gridLayout;
    
    // Criar mapa de calor das posições
    const heatmap = Array(rows).fill().map(() => Array(cols).fill(0));
    
    events.forEach(event => {
      const { row, col } = event.position;
      if (row < rows && col < cols) {
        heatmap[row][col]++;
      }
    });
    
    // Calcular concentração
    const totalClicks = events.length;
    const variance = this.calculateSpatialVariance(heatmap, totalClicks);
    
    return {
      heatmap,
      concentration: 1 - variance, // Baixa variância = alta concentração
      uniformity: this.calculateUniformity(heatmap),
      centerBias: this.calculateCenterBias(heatmap, rows, cols)
    };
  }

  /**
   * Calcular variância espacial
   */
  calculateSpatialVariance(heatmap, totalClicks) {
    const rows = heatmap.length;
    const cols = heatmap[0].length;
    const expected = totalClicks / (rows * cols);
    
    let variance = 0;
    heatmap.forEach(row => {
      row.forEach(count => {
        variance += (count - expected) ** 2;
      });
    });
    
    return variance / (rows * cols) / (expected ** 2); // Normalizada
  }

  /**
   * Calcular uniformidade
   */
  calculateUniformity(heatmap) {
    const flat = heatmap.flat();
    const max = Math.max(...flat);
    const min = Math.min(...flat);
    
    return max > 0 ? 1 - ((max - min) / max) : 1;
  }

  /**
   * Calcular viés central
   */
  calculateCenterBias(heatmap, rows, cols) {
    const centerRow = Math.floor(rows / 2);
    const centerCol = Math.floor(cols / 2);
    
    let centerClicks = 0;
    let totalClicks = 0;
    
    heatmap.forEach((row, r) => {
      row.forEach((count, c) => {
        totalClicks += count;
        const distance = Math.sqrt((r - centerRow) ** 2 + (c - centerCol) ** 2);
        if (distance <= 1) centerClicks += count;
      });
    });
    
    return totalClicks > 0 ? centerClicks / totalClicks : 0;
  }

  /**
   * Analisar distribuição temporal
   */
  analyzeTemporalDistribution() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length < 2) {
      return { pattern: 'insufficient_data', rhythm: 'none' };
    }
    
    const intervals = [];
    for (let i = 1; i < events.length; i++) {
      intervals.push(events[i].timestamp - events[i - 1].timestamp);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => 
      sum + (interval - avgInterval) ** 2, 0
    ) / intervals.length;
    
    const consistency = 1 - Math.min(1, Math.sqrt(variance) / avgInterval);
    
    return {
      averageInterval: avgInterval,
      consistency,
      pattern: consistency > 0.7 ? 'regular' : consistency > 0.4 ? 'somewhat_regular' : 'irregular',
      rhythm: avgInterval < 1500 ? 'fast' : avgInterval < 3000 ? 'moderate' : 'slow'
    };
  }

  /**
   * Analisar tendência de eficiência
   */
  analyzeEfficiencyTrend() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length < 6) {
      return { trend: 'insufficient_data', slope: 0 };
    }
    
    // Calcular eficiência em janelas deslizantes
    const windowSize = Math.max(3, Math.floor(events.length / 5));
    const efficiencies = [];
    
    for (let i = 0; i <= events.length - windowSize; i++) {
      const window = events.slice(i, i + windowSize);
      const matches = window.filter(e => e.isMatch).length;
      efficiencies.push(matches / windowSize);
    }
    
    // Calcular tendência linear
    const slope = this.calculateLinearSlope(efficiencies);
    
    return {
      trend: slope > 0.05 ? 'improving' : slope < -0.05 ? 'declining' : 'stable',
      slope,
      dataPoints: efficiencies
    };
  }

  /**
   * Calcular inclinação linear
   */
  calculateLinearSlope(values) {
    const n = values.length;
    if (n < 2) return 0;
    
    const x = Array.from({ length: n }, (_, i) => i);
    const meanX = (n - 1) / 2;
    const meanY = values.reduce((a, b) => a + b, 0) / n;
    
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < n; i++) {
      numerator += (x[i] - meanX) * (values[i] - meanY);
      denominator += (x[i] - meanX) ** 2;
    }
    
    return denominator === 0 ? 0 : numerator / denominator;
  }

  /**
   * Calcular efetividade geral
   */
  calculateOverallEffectiveness() {
    const session = this.sessionData.currentSession;
    const metrics = session.strategicMetrics;
    
    return (
      metrics.consistency * 0.2 +
      metrics.efficiency * 0.3 +
      metrics.sophistication * 0.2 +
      metrics.planningCapacity * 0.3
    );
  }

  /**
   * Calcular flexibilidade estratégica
   */
  calculateStrategicFlexibility() {
    const session = this.sessionData.currentSession;
    const strategies = session.detectedStrategies;
    
    const detectedCount = Object.values(strategies).filter(s => s.detected).length;
    const maxStrategies = Object.keys(strategies).length;
    
    return detectedCount / maxStrategies;
  }

  /**
   * Calcular taxa de aprendizagem
   */
  calculateLearningRate() {
    const trend = this.analyzeEfficiencyTrend();
    return Math.max(0, trend.slope);
  }

  /**
   * Identificar comportamentos dominantes
   */
  identifyDominantBehaviors() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    const behaviorCounts = {};
    
    events.forEach(event => {
      event.strategicIndicators.forEach(indicator => {
        behaviorCounts[indicator] = (behaviorCounts[indicator] || 0) + 1;
      });
    });
    
    return Object.entries(behaviorCounts)
      .sort(([_a, a], [_b, b]) => b - a)
      .slice(0, 3)
      .map(([behavior, count]) => ({
        behavior,
        frequency: count / events.length,
        description: this.getBehaviorDescription(behavior)
      }));
  }

  /**
   * Obter descrição do comportamento
   */
  getBehaviorDescription(behavior) {
    const descriptions = {
      'systematic_approach': 'Abordagem sistemática e organizada',
      'spatial_memory_usage': 'Uso efetivo da memória espacial',
      'trial_and_error': 'Exploração por tentativa e erro',
      'pattern_recognition': 'Reconhecimento de padrões',
      'persistence': 'Persistência nas tentativas'
    };
    
    return descriptions[behavior] || 'Comportamento não descrito';
  }

  /**
   * Calcular consistência comportamental
   */
  calculateBehavioralConsistency() {
    const session = this.sessionData.currentSession;
    const events = session.strategyEvents;
    
    if (events.length < 3) return 0;
    
    // Analisar consistência dos indicadores estratégicos
    const allIndicators = events.flatMap(e => e.strategicIndicators);
    const uniqueIndicators = new Set(allIndicators);
    
    return 1 - (uniqueIndicators.size / allIndicators.length);
  }

  /**
   * Calcular capacidade adaptativa
   */
  calculateAdaptiveCapacity() {
    const evolution = this.analyzeStrategyEvolution();
    const flexibility = this.calculateStrategicFlexibility();
    
    return (evolution.changes / 2 * 0.6 + flexibility * 0.4);
  }

  /**
   * Calcular maturidade estratégica
   */
  calculateStrategicMaturity() {
    const session = this.sessionData.currentSession;
    const metrics = session.strategicMetrics;
    
    // Maturidade baseada em sofisticação e planejamento
    return (metrics.sophistication * 0.6 + metrics.planningCapacity * 0.4);
  }

  /**
   * Calcular pontuações de capacidade
   */
  calculateStrategicPlanningScore() {
    const session = this.sessionData.currentSession;
    const planningCapacity = session.strategicMetrics.planningCapacity;
    const sophistication = session.strategicMetrics.sophistication;
    
    return Math.min(100, Math.round((planningCapacity * 60 + sophistication * 40) * 100));
  }

  calculateExecutiveControlScore() {
    const session = this.sessionData.currentSession;
    const consistency = session.strategicMetrics.consistency;
    const efficiency = session.strategicMetrics.efficiency;
    
    return Math.min(100, Math.round((consistency * 50 + efficiency * 50) * 100));
  }

  calculateCognitiveFlexibilityScore() {
    const adaptability = this.calculateAdaptiveCapacity();
    const flexibility = this.calculateStrategicFlexibility();
    
    return Math.min(100, Math.round((adaptability * 60 + flexibility * 40) * 100));
  }

  calculateProblemSolvingScore() {
    const effectiveness = this.calculateOverallEffectiveness();
    const learningRate = this.calculateLearningRate();
    
    return Math.min(100, Math.round((effectiveness * 70 + learningRate * 30) * 100));
  }

  /**
   * Gerar recomendações terapêuticas
   */
  generateTherapeuticRecommendations() {
    const recommendations = [];
    const session = this.sessionData.currentSession;
    const primaryStrategy = this.identifyPrimaryStrategy();
    
    // Recomendações baseadas na estratégia primária
    if (primaryStrategy.confidence < 0.4) {
      recommendations.push({
        area: 'strategy_development',
        priority: 'high',
        title: 'Desenvolver Estratégias de Memória',
        description: 'Baixa confiança nas estratégias identificadas.',
        activities: [
          'Treinamento de estratégias sistemáticas',
          'Exercícios de planejamento sequencial',
          'Desenvolvimento de metacognição'
        ]
      });
    }
    
    // Recomendações baseadas na eficiência
    if (session.strategicMetrics.efficiency < 0.4) {
      recommendations.push({
        area: 'efficiency_improvement',
        priority: 'medium',
        title: 'Melhorar Eficiência Estratégica',
        description: 'Baixa eficiência na execução de estratégias.',
        activities: [
          'Treinamento de seleção estratégica',
          'Exercícios de otimização de busca',
          'Feedback sobre performance'
        ]
      });
    }
    
    // Recomendações baseadas na flexibilidade
    const flexibility = this.calculateStrategicFlexibility();
    if (flexibility < 0.3) {
      recommendations.push({
        area: 'cognitive_flexibility',
        priority: 'medium',
        title: 'Desenvolver Flexibilidade Cognitiva',
        description: 'Limitada variedade de estratégias utilizadas.',
        activities: [
          'Treinamento de mudança de estratégia',
          'Exercícios de resolução criativa',
          'Prática com múltiplos contextos'
        ]
      });
    }
    
    return recommendations;
  }

  /**
   * Obter análise vazia
   */
  getEmptyAnalysis() {
    return {
      collectorName: this.name,
      version: this.version,
      sessionId: null,
      timestamp: Date.now(),
      error: 'No session data available',
      detectedStrategies: null,
      searchPatterns: null,
      strategicMetrics: null,
      behavioralAnalysis: null,
      therapeuticRecommendations: [],
      capacityScores: {
        strategicPlanning: 0,
        executiveControl: 0,
        cognitiveFlexibility: 0,
        problemSolving: 0
      }
    };
  }

  /**
   * Finalizar sessão
   */
  endSession() {
    if (this.sessionData.currentSession) {
      console.log(`🧭 MemoryStrategiesCollector: Sessão finalizada - ${this.sessionData.currentSession.sessionId}`);
      this.sessionData.currentSession = null;
    }
  }

  /**
   * Obter dados coletados
   */
  getCollectedData() {
    return {
      collectorName: this.name,
      version: this.version,
      isActive: this.isActive,
      totalSessions: this.collectedData.length,
      lastSession: this.sessionData.currentSession,
      collectedData: this.collectedData
    };
  }

  /**
   * Limpar dados
   */
  clearData() {
    this.collectedData = [];
    this.sessionData = {
      currentSession: null,
      strategyEvents: [],
      patternDetections: [],
      strategicBehaviors: []
    };
    console.log('🧭 MemoryStrategiesCollector: Dados limpos');
  }
}
