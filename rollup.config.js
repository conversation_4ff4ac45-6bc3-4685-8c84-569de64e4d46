// rollup.config.js - Configuração adicional para otimização
import { purgeCss } from 'rollup-plugin-purgecss';

export default {
  plugins: [
    purgeCss({
      content: [
        './src/**/*.jsx',
        './src/**/*.js',
        './src/**/*.tsx',
        './src/**/*.ts',
        './index.html'
      ],
      css: [
        './src/**/*.css',
        './src/**/*.module.css'
      ],
      // Whitelist para classes que podem ser adicionadas dinamicamente
      whitelist: [
        'active',
        'disabled',
        'loading',
        'error',
        'success',
        'warning',
        'info',
        'tts-active',
        'game-started',
        'game-paused',
        'game-ended'
      ],
      // Whitelist para padrões de classes
      whitelistPatterns: [
        /^react-/,
        /^framer-/,
        /^chart-/,
        /^motion-/,
        /^game-/,
        /^activity-/,
        /^dashboard-/,
        /^control-/,
        /^btn-/,
        /^card-/,
        /^modal-/,
        /^tooltip-/,
        /^loading-/,
        /^error-/,
        /^success-/,
        /^warning-/,
        /^info-/
      ]
    })
  ]
};
