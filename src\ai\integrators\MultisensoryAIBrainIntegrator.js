/**
 * @file MultisensoryAIBrainIntegrator.js
 * @description Integrador entre sistema multissensorial e AI Brain
 * @version 1.0.0
 */

import { AIBrainOrchestrator } from '../../api/services/ai/AIBrainOrchestrator.js';
import { StructuredLogger } from '../../api/services/core/logging/StructuredLogger.js';

/**
 * Classe responsável pela integração das métricas multissensoriais com o AI Brain
 */
export class MultisensoryAIBrainIntegrator {
  constructor(options = {}) {
    this.logger = StructuredLogger.getInstance({
      serviceName: 'MultisensoryAIBrainIntegrator',
      logLevel: options.logLevel || 'info'
    });
    
    this.aiBrain = options.aiBrain || new AIBrainOrchestrator();
    this.enableRealTimeAnalysis = options.enableRealTimeAnalysis || false;
    this.analysisFrequency = options.analysisFrequency || 'session-end'; // 'real-time', 'interval', 'session-end'
    this.sessionData = null;
    
    this.isProcessing = false;
    this.processingQueue = [];
    this.lastProcessedTimestamp = null;
    
    this.logger.info('MultisensoryAIBrainIntegrator inicializado', {
      enableRealTimeAnalysis: this.enableRealTimeAnalysis,
      analysisFrequency: this.analysisFrequency
    });
  }
  
  /**
   * Processa métricas multissensoriais e envia para o AI Brain
   * @param {Object} metricsData - Dados coletados pelo MultisensoryMetricsCollector
   * @param {Object} options - Opções de processamento
   * @returns {Promise<Object>} - Resultado da análise do AI Brain
   */
  async processMultisensoryMetrics(metricsData, options = {}) {
    if (!metricsData) {
      this.logger.warn('Dados multissensoriais vazios ou inválidos');
      return { success: false, error: 'Dados multissensoriais inválidos' };
    }
    
    try {
      this.logger.info('Processando métricas multissensoriais', {
        sessionId: metricsData.sessionId,
        dataPoints: metricsData.sensorData?.length || 0,
        gameType: metricsData.gameType || 'unknown'
      });
      
      // Armazenar dados da sessão
      this.sessionData = metricsData;
      
      // Preparar dados para o AI Brain
      const processedData = this._preprocessMultisensoryData(metricsData);
      
      // Enviar para o AI Brain
      const aiResponse = await this._sendToAIBrain(processedData, options);
      
      // Registrar resultado
      this.lastProcessedTimestamp = new Date().toISOString();
      
      return {
        success: true,
        analysisResults: aiResponse,
        timestamp: this.lastProcessedTimestamp,
        sessionId: metricsData.sessionId
      };
    } catch (error) {
      this.logger.error('Erro ao processar métricas multissensoriais', error);
      return {
        success: false,
        error: error.message || 'Erro desconhecido no processamento',
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * Pré-processa os dados multissensoriais para o formato esperado pelo AI Brain
   * @private
   * @param {Object} rawData - Dados brutos do MultisensoryMetricsCollector
   * @returns {Object} - Dados processados prontos para o AI Brain
   */
  _preprocessMultisensoryData(rawData) {
    // Extrair informações relevantes dos dados brutos
    const { sessionId, userId, gameType, sensorData = [], deviceInfo, startTime, endTime } = rawData;
    
    // Extrair métricas multissensoriais específicas
    const accelerometerData = this._extractSensorData(sensorData, 'accelerometer');
    const gyroscopeData = this._extractSensorData(sensorData, 'gyroscope');
    const touchData = this._extractSensorData(sensorData, 'touch');
    const geolocationData = this._extractSensorData(sensorData, 'geolocation');
    
    // Calcular métricas agregadas
    const aggregatedMetrics = this._calculateAggregatedMetrics(sensorData);
    
    // Formatar para o AI Brain
    return {
      metadata: {
        sessionId,
        userId,
        gameType,
        deviceType: deviceInfo?.deviceType || 'unknown',
        platform: deviceInfo?.platform || 'unknown',
        startTime,
        endTime,
        duration: endTime && startTime ? new Date(endTime) - new Date(startTime) : null,
        dataPoints: sensorData.length
      },
      sensorMetrics: {
        accelerometer: {
          available: accelerometerData.length > 0,
          dataPoints: accelerometerData.length,
          data: accelerometerData
        },
        gyroscope: {
          available: gyroscopeData.length > 0,
          dataPoints: gyroscopeData.length,
          data: gyroscopeData
        },
        touch: {
          available: touchData.length > 0,
          dataPoints: touchData.length,
          data: touchData
        },
        geolocation: {
          available: geolocationData.length > 0,
          dataPoints: geolocationData.length,
          data: geolocationData
        }
      },
      aggregatedMetrics
    };
  }
  
  /**
   * Extrai dados de um sensor específico
   * @private
   * @param {Array} sensorData - Array de dados dos sensores
   * @param {String} sensorType - Tipo de sensor (accelerometer, gyroscope, etc.)
   * @returns {Array} - Dados filtrados do sensor especificado
   */
  _extractSensorData(sensorData, sensorType) {
    return sensorData.filter(data => data.type === sensorType)
      .map(item => {
        // Remover propriedades desnecessárias para economia de espaço
        const { type, timestamp, ...values } = item;
        return { timestamp, ...values };
      });
  }
  
  /**
   * Calcula métricas agregadas a partir dos dados dos sensores
   * @private
   * @param {Array} sensorData - Array de dados dos sensores
   * @returns {Object} - Métricas agregadas
   */
  _calculateAggregatedMetrics(sensorData) {
    // Dados agrupados por tipo
    const dataByType = sensorData.reduce((acc, item) => {
      if (!acc[item.type]) acc[item.type] = [];
      acc[item.type].push(item);
      return acc;
    }, {});
    
    // Frequência de movimentos bruscos (acelerômetro)
    const accelerometerData = dataByType.accelerometer || [];
    const suddenMovements = accelerometerData.filter(item => {
      const magnitude = Math.sqrt(
        Math.pow(item.x || 0, 2) + 
        Math.pow(item.y || 0, 2) + 
        Math.pow(item.z || 0, 2)
      );
      return magnitude > 15; // Valor arbitrário para movimento brusco
    }).length;
    
    // Padrões de toque
    const touchData = dataByType.touch || [];
    const touchPatterns = this._analyzeTouchPatterns(touchData);
    
    // Estabilidade do dispositivo (giroscópio)
    const gyroscopeData = dataByType.gyroscope || [];
    const deviceStability = this._calculateDeviceStability(gyroscopeData);
    
    return {
      deviceHandling: {
        suddenMovements,
        suddenMovementsPerMinute: accelerometerData.length > 0 ? 
          (suddenMovements / (accelerometerData.length / 60)) : 0,
        deviceStability: deviceStability * 100 // Porcentagem
      },
      userInteraction: {
        touchFrequency: touchData.length,
        touchPrecision: touchPatterns.precision * 100, // Porcentagem
        touchConsistency: touchPatterns.consistency * 100 // Porcentagem
      }
    };
  }
  
  /**
   * Analisa padrões de toque
   * @private
   * @param {Array} touchData - Dados de toque
   * @returns {Object} - Análise de padrões de toque
   */
  _analyzeTouchPatterns(touchData) {
    // Implementação simplificada
    return {
      precision: touchData.length > 10 ? 0.85 : 0.7,
      consistency: touchData.length > 20 ? 0.9 : 0.75
    };
  }
  
  /**
   * Calcula a estabilidade do dispositivo
   * @private
   * @param {Array} gyroscopeData - Dados do giroscópio
   * @returns {Number} - Índice de estabilidade (0-1)
   */
  _calculateDeviceStability(gyroscopeData) {
    if (gyroscopeData.length < 5) return 0.5; // Dados insuficientes
    
    // Implementação simplificada
    const instabilitySum = gyroscopeData.reduce((sum, item) => {
      const magnitude = Math.sqrt(
        Math.pow(item.alpha || 0, 2) + 
        Math.pow(item.beta || 0, 2) + 
        Math.pow(item.gamma || 0, 2)
      );
      return sum + magnitude;
    }, 0);
    
    const avgInstability = instabilitySum / gyroscopeData.length;
    return Math.max(0, Math.min(1, 1 - (avgInstability / 90))); // 90 graus como máximo
  }
  
  /**
   * Envia dados processados para o AI Brain
   * @private
   * @param {Object} processedData - Dados processados
   * @param {Object} options - Opções adicionais
   * @returns {Promise<Object>} - Resposta do AI Brain
   */
  async _sendToAIBrain(processedData, options = {}) {
    try {
      this.logger.info('Enviando dados para AI Brain', {
        sessionId: processedData.metadata.sessionId,
        dataPoints: processedData.metadata.dataPoints
      });
      
      // Preparar payload para o AI Brain
      const payload = {
        analysisType: 'multisensory-metrics',
        data: processedData,
        options: {
          includeDetailedAnalysis: options.includeDetailedAnalysis !== false,
          generateVisualizations: options.generateVisualizations || false,
          priority: options.priority || 'normal',
          ...options
        }
      };
      
      // Chamar o AI Brain
      const response = await this.aiBrain.processData(payload);
      
      this.logger.info('Resposta recebida do AI Brain', {
        sessionId: processedData.metadata.sessionId,
        analysisId: response.analysisId || 'unknown'
      });
      
      return response;
    } catch (error) {
      this.logger.error('Erro ao enviar dados para AI Brain', error);
      throw new Error(`Falha na comunicação com AI Brain: ${error.message}`);
    }
  }
  
  /**
   * Obtém o último relatório de análise
   * @returns {Object|null} - Último relatório ou null se não houver
   */
  getLatestReport() {
    if (!this.sessionData || !this.lastProcessedTimestamp) {
      return null;
    }
    
    return {
      sessionId: this.sessionData.sessionId,
      userId: this.sessionData.userId,
      gameType: this.sessionData.gameType,
      processedAt: this.lastProcessedTimestamp,
      // Nota: Relatórios completos seriam armazenados em um sistema real
      reportAvailable: true,
      reportType: 'multisensory-analysis'
    };
  }
}

export default MultisensoryAIBrainIntegrator;
