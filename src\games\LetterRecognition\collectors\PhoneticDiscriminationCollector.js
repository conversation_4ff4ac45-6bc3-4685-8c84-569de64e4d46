/**
 * 📝 PHONETIC DISCRIMINATION COLLECTOR V3
 * Coletor especializado para atividade de discriminação fonética
 * Portal Betina V3
 */

export class PhoneticDiscriminationCollector {
  constructor() {
    this.name = 'PhoneticDiscriminationCollector';
    this.version = '3.0.0';
    this.isActive = true;
    this.collectedData = [];
  }

  async collect(data) {
    try {
      const timestamp = new Date().toISOString();
      
      const analysisData = {
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: 'phonetic_discrimination',
        
        // Dados fonéticos
        targetSound: data.targetSound,
        phonePair: data.phonePair,
        selectedOption: data.selectedOption,
        expectedOption: data.expectedOption,
        isCorrect: data.selectedOption === data.expectedOption,
        responseTime: data.responseTime || 0,
        
        // Análise fonética
        phoneticDistance: this.calculatePhoneticDistance(data.phonePair),
        soundComplexity: this.assessSoundComplexity(data.targetSound),
        discriminationDifficulty: this.assessDiscriminationDifficulty(data),
        
        // Métricas auditivas
        auditoryProcessingTime: data.behavioralMetrics?.auditoryProcessingTime || data.responseTime,
        phoneticAwareness: this.assessPhoneticAwareness(data),
        auditoryDiscrimination: this.assessAuditoryDiscrimination(data),
        soundCategorization: this.assessSoundCategorization(data),
        
        // Habilidades específicas
        minimalPairRecognition: this.assessMinimalPairRecognition(data),
        acousticFeatureDetection: this.assessAcousticFeatureDetection(data),
        phoneticMemory: this.assessPhoneticMemory(data),
        
        // Padrões de erro
        errorType: this.classifyPhoneticError(data),
        errorAnalysis: this.analyzePhoneticError(data)
      };
      
      this.collectedData.push(analysisData);
      return analysisData;
      
    } catch (error) {
      console.error('Erro no PhoneticDiscriminationCollector:', error);
      return null;
    }
  }

  calculatePhoneticDistance(phonePair) {
    if (!phonePair || phonePair.length !== 2) return 0.5;
    
    const [sound1, sound2] = phonePair;
    
    // Mapeamento de características fonéticas
    const phoneticFeatures = {
      'p': { voicing: 0, place: 'bilabial', manner: 'stop' },
      'b': { voicing: 1, place: 'bilabial', manner: 'stop' },
      't': { voicing: 0, place: 'alveolar', manner: 'stop' },
      'd': { voicing: 1, place: 'alveolar', manner: 'stop' },
      'k': { voicing: 0, place: 'velar', manner: 'stop' },
      'g': { voicing: 1, place: 'velar', manner: 'stop' },
      'f': { voicing: 0, place: 'labiodental', manner: 'fricative' },
      'v': { voicing: 1, place: 'labiodental', manner: 'fricative' },
      's': { voicing: 0, place: 'alveolar', manner: 'fricative' },
      'z': { voicing: 1, place: 'alveolar', manner: 'fricative' },
      'm': { voicing: 1, place: 'bilabial', manner: 'nasal' },
      'n': { voicing: 1, place: 'alveolar', manner: 'nasal' },
      'l': { voicing: 1, place: 'alveolar', manner: 'liquid' },
      'r': { voicing: 1, place: 'alveolar', manner: 'liquid' }
    };
    
    const feat1 = phoneticFeatures[sound1.toLowerCase()] || { voicing: 0.5, place: 'unknown', manner: 'unknown' };
    const feat2 = phoneticFeatures[sound2.toLowerCase()] || { voicing: 0.5, place: 'unknown', manner: 'unknown' };
    
    let distance = 0;
    
    // Diferença de vozeamento (0.4 de peso)
    distance += Math.abs(feat1.voicing - feat2.voicing) * 0.4;
    
    // Diferença de ponto de articulação (0.3 de peso)
    if (feat1.place !== feat2.place) distance += 0.3;
    
    // Diferença de modo de articulação (0.3 de peso)
    if (feat1.manner !== feat2.manner) distance += 0.3;
    
    return Math.min(1.0, distance);
  }

  assessSoundComplexity(sound) {
    if (!sound) return 0.5;
    
    // Complexidade baseada em características fonéticas
    const complexityMap = {
      // Sons mais simples
      'a': 0.2, 'e': 0.2, 'i': 0.2, 'o': 0.2, 'u': 0.2,
      'm': 0.3, 'n': 0.3, 'p': 0.3, 'b': 0.3,
      
      // Sons intermediários
      't': 0.4, 'd': 0.4, 'k': 0.4, 'g': 0.4,
      'f': 0.5, 'v': 0.5, 'l': 0.5,
      
      // Sons mais complexos
      's': 0.6, 'z': 0.6, 'r': 0.7,
      'ch': 0.8, 'th': 0.8, 'nh': 0.8
    };
    
    return complexityMap[sound.toLowerCase()] || 0.5;
  }

  assessDiscriminationDifficulty(data) {
    let difficulty = 0.5;
    
    const phoneticDistance = this.calculatePhoneticDistance(data.phonePair);
    
    // Menor distância fonética = maior dificuldade
    difficulty += (1 - phoneticDistance) * 0.3;
    
    const soundComplexity = this.assessSoundComplexity(data.targetSound);
    difficulty += soundComplexity * 0.2;
    
    return Math.min(1.0, difficulty);
  }

  assessPhoneticAwareness(data) {
    let awareness = 0.6;
    
    if (data.selectedOption === data.expectedOption) {
      awareness += 0.3;
      
      // Bônus baseado na dificuldade da discriminação
      const difficulty = this.assessDiscriminationDifficulty(data);
      if (difficulty > 0.7) awareness += 0.1;
    }
    
    const responseTime = data.responseTime || 0;
    if (responseTime < 2000) awareness += 0.1;
    else if (responseTime > 5000) awareness -= 0.2;
    
    return Math.max(0.0, Math.min(1.0, awareness));
  }

  assessAuditoryDiscrimination(data) {
    let discrimination = 0.5;
    
    const phoneticDistance = this.calculatePhoneticDistance(data.phonePair);
    const isCorrect = data.selectedOption === data.expectedOption;
    
    if (isCorrect) {
      discrimination += 0.3;
      
      // Bônus para discriminações difíceis (sons similares)
      if (phoneticDistance < 0.4) discrimination += 0.2;
    } else {
      // Penalidade baseada na facilidade da discriminação
      if (phoneticDistance > 0.7) discrimination -= 0.3;
    }
    
    return Math.max(0.0, Math.min(1.0, discrimination));
  }

  assessSoundCategorization(data) {
    let categorization = 0.6;
    
    if (data.selectedOption === data.expectedOption) {
      categorization += 0.3;
      
      const soundComplexity = this.assessSoundComplexity(data.targetSound);
      if (soundComplexity > 0.6) categorization += 0.1;
    }
    
    return Math.min(1.0, categorization);
  }

  assessMinimalPairRecognition(data) {
    let recognition = 0.5;
    
    const phoneticDistance = this.calculatePhoneticDistance(data.phonePair);
    
    // Par mínimo: sons com distância pequena
    if (phoneticDistance < 0.5) {
      recognition += 0.2; // É um par mínimo
      
      if (data.selectedOption === data.expectedOption) {
        recognition += 0.3; // Acertou o par mínimo
      }
    }
    
    return Math.min(1.0, recognition);
  }

  assessAcousticFeatureDetection(data) {
    let detection = 0.6;
    
    if (data.selectedOption === data.expectedOption) {
      detection += 0.3;
      
      // Analisa que tipo de característica foi detectada
      const phoneticDistance = this.calculatePhoneticDistance(data.phonePair);
      if (phoneticDistance < 0.3) detection += 0.1; // Detectou diferença sutil
    }
    
    return Math.min(1.0, detection);
  }

  assessPhoneticMemory(data) {
    let memory = 0.6;
    
    // Memória fonética baseada no tempo de resposta e precisão
    if (data.selectedOption === data.expectedOption) {
      memory += 0.3;
      
      const responseTime = data.responseTime || 0;
      if (responseTime < 3000) memory += 0.1; // Memória rápida
    }
    
    return Math.min(1.0, memory);
  }

  classifyPhoneticError(data) {
    if (data.selectedOption === data.expectedOption) return 'no_error';
    
    const phoneticDistance = this.calculatePhoneticDistance(data.phonePair);
    
    if (phoneticDistance < 0.3) return 'minimal_pair_confusion';
    if (phoneticDistance < 0.5) return 'similar_sound_confusion';
    if (phoneticDistance > 0.8) return 'random_selection';
    
    return 'moderate_confusion';
  }

  analyzePhoneticError(data) {
    if (data.selectedOption === data.expectedOption) return null;
    
    const phoneticDistance = this.calculatePhoneticDistance(data.phonePair);
    const difficulty = this.assessDiscriminationDifficulty(data);
    
    return {
      errorType: this.classifyPhoneticError(data),
      phoneticDistance,
      discriminationDifficulty: difficulty,
      soundComplexity: this.assessSoundComplexity(data.targetSound),
      possibleCauses: this.identifyPhoneticErrorCauses(data)
    };
  }

  identifyPhoneticErrorCauses(data) {
    const causes = [];
    
    const responseTime = data.responseTime || 0;
    if (responseTime < 1000) causes.push('hasty_response');
    if (responseTime > 6000) causes.push('auditory_processing_difficulty');
    
    const phoneticDistance = this.calculatePhoneticDistance(data.phonePair);
    if (phoneticDistance < 0.3) causes.push('minimal_pair_difficulty');
    
    const soundComplexity = this.assessSoundComplexity(data.targetSound);
    if (soundComplexity > 0.7) causes.push('complex_sound_structure');
    
    return causes;
  }

  generateSummary() {
    if (this.collectedData.length === 0) return null;
    
    const total = this.collectedData.length;
    const correct = this.collectedData.filter(d => d.isCorrect).length;
    const accuracy = correct / total;
    
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / total;
    const avgPhoneticDistance = this.collectedData.reduce((sum, d) => sum + d.phoneticDistance, 0) / total;
    const avgAwareness = this.collectedData.reduce((sum, d) => sum + d.phoneticAwareness, 0) / total;
    
    // Análise por tipo de erro
    const errorTypes = {};
    this.collectedData.forEach(d => {
      if (!errorTypes[d.errorType]) errorTypes[d.errorType] = 0;
      errorTypes[d.errorType]++;
    });
    
    // Performance em diferentes distâncias fonéticas
    const distancePerformance = {
      'very_close': this.getPerformanceByDistance(0, 0.3),
      'close': this.getPerformanceByDistance(0.3, 0.6),
      'distant': this.getPerformanceByDistance(0.6, 1.0)
    };
    
    return {
      collector: this.name,
      version: this.version,
      total,
      accuracy,
      avgResponseTime,
      avgPhoneticDistance,
      avgPhoneticAwareness: avgAwareness,
      errorTypes,
      distancePerformance,
      recommendations: this.generateRecommendations()
    };
  }

  getPerformanceByDistance(minDist, maxDist) {
    const data = this.collectedData.filter(d => 
      d.phoneticDistance >= minDist && d.phoneticDistance < maxDist
    );
    
    if (data.length === 0) return null;
    
    return {
      count: data.length,
      accuracy: data.filter(d => d.isCorrect).length / data.length,
      avgTime: data.reduce((sum, d) => sum + d.responseTime, 0) / data.length
    };
  }

  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    
    const recommendations = [];
    
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: 'phonetic_training',
        priority: 'high',
        message: 'Pratique discriminação de sons básicos antes de avançar.'
      });
    }
    
    if (summary.avgResponseTime > 5000) {
      recommendations.push({
        type: 'auditory_processing',
        priority: 'medium',
        message: 'Trabalhe velocidade de processamento auditivo.'
      });
    }
    
    // Recomendações baseadas em performance por distância
    if (summary.distancePerformance.very_close?.accuracy < 0.5) {
      recommendations.push({
        type: 'minimal_pairs',
        priority: 'high',
        message: 'Foque em exercícios de pares mínimos.'
      });
    }
    
    return recommendations;
  }

  reset() {
    this.collectedData = [];
  }

  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: new Date().toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
