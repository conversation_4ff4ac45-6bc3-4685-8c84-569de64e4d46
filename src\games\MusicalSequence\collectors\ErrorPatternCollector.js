/**
 * 🎵 MUSICAL SEQUENCE ERROR PATTERN COLLECTOR
 * Algoritmo especializado para coleta e análise de padrões de erro no jogo de sequência musical
 */

export class ErrorPatternCollector {
  constructor() {
    this.name = 'MusicalSequenceErrorPatternCollector';
    this.description = 'Coleta padrões de erros no MusicalSequence';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    
    this.errorData = {
      sequenceErrors: {},
      rhythmErrors: [],
      pitchErrors: [],
      timingErrors: [],
      auditoryProcessingErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      musicalPatterns: {}
    };
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3,
      cluster: 5,
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    console.log(`🎵 ${this.name} v${this.version} inicializado`);
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("MusicalSequenceErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }

    console.log(`📊 MusicalSequenceErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || 'sem ID'}`);
    
    try {
      // Extrair e categorizar erros dos dados do jogo
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      const errors = [];
      
      // Analisar erros de sequência musical
      if (gameData.attemptHistory && Array.isArray(gameData.attemptHistory)) {
        gameData.attemptHistory.forEach((attempt, index) => {
          if (!attempt.isCorrect && attempt.targetSequence && attempt.playedSequence) {
            const sequenceError = this.collectSequenceError(
              attempt.targetSequence,
              attempt.playedSequence,
              { 
                difficulty: gameData.difficulty || 'medium',
                responseTime: attempt.responseTime || 0,
                attemptNumber: index
              }
            );
            if (sequenceError) errors.push(sequenceError);
          }
        });
      }
      
      // Analisar erros rítmicos
      if (gameData.rhythmHistory && Array.isArray(gameData.rhythmHistory)) {
        gameData.rhythmHistory.forEach((rhythm) => {
          if (rhythm.target && rhythm.actual && rhythm.target !== rhythm.actual) {
            const rhythmError = this.collectRhythmError(
              rhythm.target,
              rhythm.actual,
              {
                rhythmType: rhythm.type || 'standard',
                difficulty: gameData.difficulty || 'medium'
              }
            );
            if (rhythmError) errors.push(rhythmError);
          }
        });
      }
      
      // Salvar dados coletados para análise futura
      const collectedMetric = {
        timestamp: Date.now(),
        type: 'error_pattern',
        gameType: 'MusicalSequence',
        data: errorMetrics,
        errors: errors,
        sessionData: {
          sessionId: gameData.sessionId,
          level: gameData.level || 1,
          attempt: gameData.attempt || 1
        }
      };

      this.collectedData.push(collectedMetric);
      this.categorizeErrors(errorMetrics);
      
      return {
        errors,
        patterns: errorMetrics,
        metrics: this.generateErrorMetrics(gameData)
      };
    } catch (error) {
      console.error('❌ Erro ao coletar padrões de erro (MusicalSequence):', error);
      return { errors: [], patterns: [], metrics: {}, error: error.message };
    }
  }

  analyzeErrorPatterns(gameData) {
    const patterns = {
      sequenceErrors: this.detectSequenceErrors(gameData),
      rhythmErrors: this.detectRhythmErrors(gameData),
      pitchErrors: this.detectPitchErrors(gameData),
      timingErrors: this.detectTimingErrors(gameData),
      severity: this.calculateOverallSeverity(gameData)
    };

    return patterns;
  }

  detectSequenceErrors(gameData) {
    return [];
  }

  detectRhythmErrors(gameData) {
    return [];
  }

  detectPitchErrors(gameData) {
    return [];
  }

  detectTimingErrors(gameData) {
    return [];
  }

  calculateOverallSeverity(gameData) {
    return 'low';
  }

  categorizeErrors(errorMetrics) {
    // Categorizar erros por tipo
  }

  /**
   * Coleta erros de sequência musical
   */
  collectSequenceError(targetSequence, playedSequence, context) {
    const errorKey = `seq_${targetSequence.length}`;
    
    const sequenceError = {
      timestamp: new Date().toISOString(),
      targetSequence: targetSequence,
      playedSequence: playedSequence,
      errorType: this.identifySequenceErrorType(targetSequence, playedSequence),
      context: {
        difficulty: context.difficulty || 'medium',
        responseTime: context.responseTime || 0,
        attemptNumber: context.attemptNumber || 0
      },
      severity: this.calculateSequenceErrorSeverity(targetSequence, playedSequence, context),
      matchAccuracy: this.calculateSequenceMatchAccuracy(targetSequence, playedSequence)
    };

    if (!this.errorData.sequenceErrors[errorKey]) {
      this.errorData.sequenceErrors[errorKey] = [];
    }
    this.errorData.sequenceErrors[errorKey].push(sequenceError);

    return sequenceError;
  }

  /**
   * Coleta erros rítmicos
   */
  collectRhythmError(targetRhythm, actualRhythm, context) {
    const rhythmError = {
      timestamp: new Date().toISOString(),
      targetRhythm: targetRhythm,
      actualRhythm: actualRhythm,
      errorType: this.identifyRhythmErrorType(targetRhythm, actualRhythm),
      context: {
        rhythmType: context.rhythmType || 'standard',
        difficulty: context.difficulty || 'medium'
      },
      severity: this.calculateRhythmErrorSeverity(targetRhythm, actualRhythm, context),
      timingAccuracy: this.calculateTimingAccuracy(targetRhythm, actualRhythm)
    };

    this.errorData.rhythmErrors.push(rhythmError);
    return rhythmError;
  }

  /**
   * Identifica o tipo de erro de sequência musical
   */
  identifySequenceErrorType(target, played) {
    if (!played || played.length === 0) return 'no_response';
    
    // Erro de comprimento (sequências de tamanhos diferentes)
    if (target.length !== played.length) {
      return target.length > played.length ? 'incomplete_sequence' : 'added_notes';
    }
    
    // Erro de ordem (notas corretas, ordem errada)
    if (this.hasSameElements(target, played) && !this.areSequencesEqual(target, played)) {
      return 'order_error';
    }
    
    // Erro de nota (algumas notas incorretas)
    if (!this.hasSameElements(target, played)) {
      return 'note_substitution';
    }
    
    // Erro de timing (correto, mas timing errado)
    if (this.hasTimingIssues(target, played)) {
      return 'timing_error';
    }
    
    return 'general_sequence_error';
  }

  /**
   * Identifica o tipo de erro rítmico
   */
  identifyRhythmErrorType(target, actual) {
    // Implementação simplificada
    return 'rhythm_pattern_error';
  }

  /**
   * Verifica se duas sequências têm os mesmos elementos (independente da ordem)
   */
  hasSameElements(seq1, seq2) {
    if (seq1.length !== seq2.length) return false;
    
    const count1 = this.countElements(seq1);
    const count2 = this.countElements(seq2);
    
    for (const key in count1) {
      if (count1[key] !== count2[key]) return false;
    }
    
    return true;
  }

  /**
   * Conta a ocorrência de cada elemento em uma sequência
   */
  countElements(seq) {
    const counts = {};
    seq.forEach(elem => {
      counts[elem] = (counts[elem] || 0) + 1;
    });
    return counts;
  }

  /**
   * Verifica se duas sequências são iguais (mesmos elementos na mesma ordem)
   */
  areSequencesEqual(seq1, seq2) {
    if (seq1.length !== seq2.length) return false;
    
    for (let i = 0; i < seq1.length; i++) {
      if (seq1[i] !== seq2[i]) return false;
    }
    
    return true;
  }

  /**
   * Verifica se há problemas de timing na sequência
   */
  hasTimingIssues(target, played) {
    // Implementação simplificada
    return false;
  }

  /**
   * Calcula a precisão de correspondência entre duas sequências
   */
  calculateSequenceMatchAccuracy(target, played) {
    if (!played || played.length === 0) return 0;
    if (target.length === 0) return 0;
    
    let correctCount = 0;
    const maxLength = Math.max(target.length, played.length);
    
    for (let i = 0; i < maxLength; i++) {
      if (i < target.length && i < played.length && target[i] === played[i]) {
        correctCount++;
      }
    }
    
    return correctCount / maxLength;
  }

  /**
   * Calcula a precisão de timing
   */
  calculateTimingAccuracy(target, actual) {
    // Implementação simplificada
    return 0.7;
  }

  /**
   * Calcula a severidade do erro de sequência
   */
  calculateSequenceErrorSeverity(target, played, context) {
    let severity = 0.5; // Base
    
    // Ajuste por precisão de correspondência
    const accuracy = this.calculateSequenceMatchAccuracy(target, played);
    severity += (1 - accuracy) * 0.3;
    
    // Ajuste por tamanho de sequência
    if (target.length > 5) {
      severity -= 0.1; // Mais compreensível errar em sequências longas
    }
    
    // Ajuste por tempo de resposta
    if (context.responseTime > 5000) severity += 0.1; // Muito lento
    if (context.responseTime < 500) severity += 0.1; // Muito rápido, pode indicar impulsividade
    
    // Ajuste por dificuldade
    if (context.difficulty === 'hard') severity -= 0.1; // Mais compreensível errar em níveis difíceis
    
    return Math.min(Math.max(severity, 0), 1); // Limitar entre 0 e 1
  }

  /**
   * Calcula a severidade do erro rítmico
   */
  calculateRhythmErrorSeverity(target, actual, context) {
    // Implementação simplificada
    return 0.6;
  }

  /**
   * Gera métricas de erro com base nos dados coletados
   */
  generateErrorMetrics(gameData) {
    const sequenceErrorCount = Object.values(this.errorData.sequenceErrors).reduce(
      (total, errors) => total + errors.length, 0
    );
    
    return {
      totalErrors: sequenceErrorCount + this.errorData.rhythmErrors.length,
      uniqueSequenceErrors: Object.keys(this.errorData.sequenceErrors).length,
      mostCommonError: this.findMostCommonError(),
      averageSeverity: this.calculateAverageSeverity(),
      auditoryProcessingScore: this.calculateAuditoryProcessingScore(gameData),
      sequentialMemoryScore: this.calculateSequentialMemoryScore(gameData),
      improvement: this.calculateImprovementMetric(gameData)
    };
  }

  /**
   * Encontra o erro mais comum
   */
  findMostCommonError() {
    let maxCount = 0;
    let mostCommonError = null;
    
    Object.entries(this.errorData.sequenceErrors).forEach(([errorKey, errors]) => {
      if (errors.length > maxCount) {
        maxCount = errors.length;
        mostCommonError = errorKey;
      }
    });
    
    return {
      error: mostCommonError,
      count: maxCount
    };
  }

  /**
   * Calcula a severidade média dos erros
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let errorCount = 0;
    
    Object.values(this.errorData.sequenceErrors).forEach(errors => {
      errors.forEach(error => {
        totalSeverity += error.severity;
        errorCount++;
      });
    });
    
    this.errorData.rhythmErrors.forEach(error => {
      totalSeverity += error.severity;
      errorCount++;
    });
    
    return errorCount > 0 ? totalSeverity / errorCount : 0;
  }

  /**
   * Calcula pontuação de processamento auditivo
   */
  calculateAuditoryProcessingScore(gameData) {
    // Implementação simplificada
    return 0.7;
  }

  /**
   * Calcula pontuação de memória sequencial
   */
  calculateSequentialMemoryScore(gameData) {
    // Implementação simplificada
    return 0.6;
  }

  /**
   * Calcula métrica de melhoria ao longo do tempo
   */
  calculateImprovementMetric(gameData) {
    // Implementação simplificada
    return 0.5;
  }

  /**
   * Método de análise para compatibilidade com outros coletores
   */
  analyze(gameData) {
    return this.collect(gameData);
  }
}

export default ErrorPatternCollector;
