/**
 * 🌈 COLOR DISCRIMINATION COLLECTOR
 * Coletor especializado em análise de discriminação e percepção de cores
 * Portal Betina V3 - FASE 2.1
 */

export class ColorDiscriminationCollector {
  constructor() {
    this.colorCategories = {
      primary: ['red', 'blue', 'yellow'],
      secondary: ['green', 'orange', 'purple'],
      warm: ['red', 'orange', 'yellow'],
      cool: ['blue', 'green', 'purple'],
      light: ['lightblue', 'lightgreen', 'yellow'],
      dark: ['darkblue', 'darkgreen', 'purple']
    };
    
    this.confusionPairs = [
      ['red', 'orange'],
      ['blue', 'purple'],
      ['green', 'blue'],
      ['yellow', 'orange'],
      ['purple', 'red'],
      ['lightblue', 'blue'],
      ['lightgreen', 'green']
    ];
    
    this.colorComplexity = {
      red: 1,
      blue: 1,
      yellow: 1,
      green: 2,
      orange: 2,
      purple: 2,
      lightblue: 3,
      lightgreen: 3
    };
    
    this.therapeuticTargets = {
      visual_discrimination: 'Discriminação Visual',
      color_recognition: 'Reconhecimento de Cores',
      pattern_recognition: 'Reconhecimento de Padrões',
      visual_attention: 'Atenção Visual',
      cognitive_flexibility: 'Flexibilidade Cognitiva'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Análise principal da discriminação de cores
   */
  async analyze(data) {
    try {
      console.log('🌈 ColorDiscriminationCollector: Iniciando análise de discriminação...');
      
      if (!this.validateColorData(data)) {
        return this.generateFallbackAnalysis('Dados de cor inválidos');
      }

      const colorData = this.extractColorData(data);
      
      // Análises principais
      const discriminationProfile = this.analyzeDiscriminationProfile(colorData);
      const confusionPatterns = this.analyzeConfusionPatterns(colorData);
      const categoryPerformance = this.analyzeCategoryPerformance(colorData);
      const complexityAnalysis = this.analyzeComplexityEffects(colorData);
      const adaptationPatterns = this.analyzeAdaptationPatterns(colorData);
      const perceptualConsistency = this.analyzePerceptualConsistency(colorData);

      const results = {
        // Perfil geral de discriminação
        overallAccuracy: discriminationProfile.accuracy,
        discriminationSkill: discriminationProfile.skill,
        discriminationSpeed: discriminationProfile.speed,
        colorRecognitionLevel: discriminationProfile.recognitionLevel,
        
        // Padrões de confusão
        primaryConfusions: confusionPatterns.primary,
        confusionRate: confusionPatterns.rate,
        confusionTypes: confusionPatterns.types,
        systematicErrors: confusionPatterns.systematic,
        
        // Performance por categoria
        primaryColors: categoryPerformance.primary,
        secondaryColors: categoryPerformance.secondary,
        warmColors: categoryPerformance.warm,
        coolColors: categoryPerformance.cool,
        lightColors: categoryPerformance.light,
        darkColors: categoryPerformance.dark,
        
        // Análise de complexidade
        complexityTolerance: complexityAnalysis.tolerance,
        complexityEffect: complexityAnalysis.effect,
        optimalComplexity: complexityAnalysis.optimal,
        challengeResponse: complexityAnalysis.response,
        
        // Padrões de adaptação
        learningCurve: adaptationPatterns.learning,
        adaptationRate: adaptationPatterns.rate,
        improvementTrend: adaptationPatterns.improvement,
        plateauDetection: adaptationPatterns.plateau,
        
        // Consistência perceptual
        consistencyIndex: perceptualConsistency.index,
        reliabilityScore: perceptualConsistency.reliability,
        stabilityMeasure: perceptualConsistency.stability,
        variabilityPattern: perceptualConsistency.variability,
        
        // Análises específicas por cor
        colorSpecificAnalysis: this.analyzeIndividualColors(colorData),
        distanceAnalysis: this.analyzeColorDistances(colorData),
        sequenceEffects: this.analyzeSequenceEffects(colorData),
        
        // Métricas de desenvolvimento
        visualMaturity: this.assessVisualMaturity(colorData),
        discriminationGrowth: this.assessDiscriminationGrowth(colorData),
        perceptualFlexibility: this.assessPerceptualFlexibility(colorData),
        
        // Indicadores terapêuticos
        therapeuticProgress: this.assessTherapeuticProgress(colorData),
        targetAchievement: this.assessTargetAchievement(colorData),
        interventionNeeds: this.identifyInterventionNeeds(colorData),
        
        // Comparações e benchmarks
        ageAppropriate: this.assessAgeAppropriateness(discriminationProfile, data.userAge),
        difficultyAppropriate: this.assessDifficultyAppropriateness(discriminationProfile, data.difficulty),
        progressLevel: this.determineProgressLevel(discriminationProfile),
        
        // Contexto da análise
        totalInteractions: colorData.interactions.length,
        uniqueColors: colorData.uniqueColors.length,
        colorDistribution: colorData.colorDistribution,
        sessionDuration: colorData.sessionDuration,
        
        // Recomendações específicas
        recommendations: this.generateColorRecommendations({
          discriminationProfile, confusionPatterns, categoryPerformance,
          complexityAnalysis, adaptationPatterns, perceptualConsistency
        }),
        
        // Metadados
        analysisTimestamp: new Date().toISOString(),
        collectorVersion: '2.1.0',
        dataQuality: this.assessDataQuality(colorData)
      };

      console.log('✅ ColorDiscriminationCollector: Análise concluída:', {
        accuracy: Math.round(results.overallAccuracy * 100),
        skill: results.discriminationSkill,
        confusions: results.primaryConfusions.length,
        consistency: Math.round(results.consistencyIndex * 100)
      });

      return results;

    } catch (error) {
      console.error('❌ ColorDiscriminationCollector: Erro na análise:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de cor de entrada
   */
  validateColorData(data) {
    if (!data) return false;
    
    // Verificar se há dados de interação com cores
    const hasColorData = data.selectedItems || data.interactions || data.colorHistory;
    const hasTargetColors = data.targetColors || data.gridItems || data.colors;
    
    return hasColorData && hasTargetColors;
  }

  /**
   * Extrai dados de cor dos dados de entrada
   */
  extractColorData(data) {
    let interactions = [];
    let targetColors = [];
    let selectedColors = [];
    let accuracyData = [];
    let timestamps = [];

    // Extrair das diferentes estruturas possíveis
    if (data.selectedItems && Array.isArray(data.selectedItems)) {
      interactions = data.selectedItems.map((item, index) => ({
        index,
        targetColor: item.targetColor || item.color || 'unknown',
        selectedColor: item.selectedColor || item.color || 'unknown',
        correct: item.correct || false,
        timestamp: item.timestamp || Date.now(),
        responseTime: item.responseTime || 0,
        gridPosition: item.gridPosition || item.position || index
      }));
      
      targetColors = interactions.map(i => i.targetColor);
      selectedColors = interactions.map(i => i.selectedColor);
      accuracyData = interactions.map(i => i.correct);
      timestamps = interactions.map(i => i.timestamp);
    }

    // Extrair cores do grid se disponível
    if (data.gridItems) {
      const gridColors = data.gridItems.map(item => item.color || 'unknown');
      if (targetColors.length === 0) {
        targetColors = gridColors;
      }
    }

    // Análise de distribuição de cores
    const colorDistribution = this.calculateColorDistribution(targetColors);
    const uniqueColors = [...new Set(targetColors)].filter(color => color !== 'unknown');

    return {
      interactions,
      targetColors,
      selectedColors,
      accuracyData,
      timestamps,
      colorDistribution,
      uniqueColors,
      difficulty: data.difficulty || 'medium',
      sessionDuration: data.sessionDuration || 0,
      userAge: data.userAge || null,
      totalInteractions: interactions.length
    };
  }

  /**
   * Analisa perfil geral de discriminação
   */
  analyzeDiscriminationProfile(colorData) {
    const { interactions, accuracyData } = colorData;
    
    if (interactions.length === 0) {
      return {
        accuracy: 0,
        skill: 'unknown',
        speed: 0,
        recognitionLevel: 'beginner'
      };
    }

    // Precisão geral
    const correctCount = accuracyData.filter(correct => correct).length;
    const accuracy = correctCount / accuracyData.length;

    // Velocidade de discriminação
    const responseTimes = interactions
      .map(i => i.responseTime)
      .filter(rt => rt > 0);
    const averageSpeed = responseTimes.length > 0 
      ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length
      : 0;

    // Nível de habilidade
    const skill = this.categorizeDiscriminationSkill(accuracy, averageSpeed);
    
    // Nível de reconhecimento
    const recognitionLevel = this.assessRecognitionLevel(accuracy, colorData.uniqueColors.length);

    return {
      accuracy,
      skill,
      speed: Math.max(0, 1 - (averageSpeed / 3000)), // Normalizado
      recognitionLevel,
      responseTimes,
      correctCount,
      totalAttempts: accuracyData.length
    };
  }

  /**
   * Categoriza habilidade de discriminação
   */
  categorizeDiscriminationSkill(accuracy, averageSpeed) {
    if (accuracy >= 0.9 && averageSpeed < 2000) return 'expert';
    if (accuracy >= 0.8 && averageSpeed < 2500) return 'advanced';
    if (accuracy >= 0.7 && averageSpeed < 3000) return 'intermediate';
    if (accuracy >= 0.6) return 'developing';
    return 'beginner';
  }

  /**
   * Avalia nível de reconhecimento
   */
  assessRecognitionLevel(accuracy, uniqueColorCount) {
    if (accuracy >= 0.9 && uniqueColorCount >= 7) return 'mastery';
    if (accuracy >= 0.8 && uniqueColorCount >= 5) return 'proficient';
    if (accuracy >= 0.7 && uniqueColorCount >= 4) return 'developing';
    if (accuracy >= 0.6) return 'emerging';
    return 'beginner';
  }

  /**
   * Analisa padrões de confusão
   */
  analyzeConfusionPatterns(colorData) {
    const { interactions } = colorData;
    
    const confusions = [];
    const confusionMatrix = {};
    let totalErrors = 0;

    // Construir matriz de confusão
    interactions.forEach(interaction => {
      const { targetColor, selectedColor, correct } = interaction;
      
      if (!correct && targetColor !== 'unknown' && selectedColor !== 'unknown') {
        totalErrors++;
        confusions.push({ target: targetColor, selected: selectedColor });
        
        if (!confusionMatrix[targetColor]) {
          confusionMatrix[targetColor] = {};
        }
        if (!confusionMatrix[targetColor][selectedColor]) {
          confusionMatrix[targetColor][selectedColor] = 0;
        }
        confusionMatrix[targetColor][selectedColor]++;
      }
    });

    // Identificar confusões primárias
    const primaryConfusions = this.identifyPrimaryConfusions(confusions);
    
    // Analisar tipos de confusão
    const confusionTypes = this.analyzeConfusionTypes(confusions);
    
    // Detectar erros sistemáticos
    const systematicErrors = this.detectSystematicErrors(confusions);
    
    // Taxa de confusão
    const confusionRate = totalErrors / interactions.length;

    return {
      primary: primaryConfusions,
      rate: confusionRate,
      types: confusionTypes,
      systematic: systematicErrors,
      matrix: confusionMatrix,
      totalErrors,
      patterns: this.analyzeConfusionPatterns_detailed(confusions)
    };
  }

  /**
   * Identifica confusões primárias
   */
  identifyPrimaryConfusions(confusions) {
    const confusionCounts = {};
    
    confusions.forEach(({ target, selected }) => {
      const pair = [target, selected].sort().join('-');
      if (!confusionCounts[pair]) {
        confusionCounts[pair] = { count: 0, pairs: [] };
      }
      confusionCounts[pair].count++;
      confusionCounts[pair].pairs.push({ target, selected });
    });

    return Object.entries(confusionCounts)
      .filter(([_, data]) => data.count >= 2)
      .sort(([_, a], [__, b]) => b.count - a.count)
      .slice(0, 5)
      .map(([pair, data]) => ({
        colorPair: pair,
        frequency: data.count,
        examples: data.pairs
      }));
  }

  /**
   * Analisa tipos de confusão
   */
  analyzeConfusionTypes(confusions) {
    const types = {
      similarHue: 0,
      brightnessDifference: 0,
      saturationDifference: 0,
      knownPairs: 0,
      random: 0
    };

    confusions.forEach(({ target, selected }) => {
      // Verificar se é par conhecido de confusão
      const isKnownPair = this.confusionPairs.some(pair => 
        (pair[0] === target && pair[1] === selected) ||
        (pair[1] === target && pair[0] === selected)
      );

      if (isKnownPair) {
        types.knownPairs++;
      } else if (this.areSimilarHues(target, selected)) {
        types.similarHue++;
      } else if (this.haveBrightnessDifference(target, selected)) {
        types.brightnessDifference++;
      } else if (this.haveSaturationDifference(target, selected)) {
        types.saturationDifference++;
      } else {
        types.random++;
      }
    });

    return types;
  }

  /**
   * Detecta erros sistemáticos
   */
  detectSystematicErrors(confusions) {
    const systematicThreshold = 3;
    const patterns = {};

    confusions.forEach(({ target, selected }) => {
      const pattern = `${target}->${selected}`;
      if (!patterns[pattern]) {
        patterns[pattern] = { count: 0, examples: [] };
      }
      patterns[pattern].count++;
      patterns[pattern].examples.push({ target, selected });
    });

    return Object.entries(patterns)
      .filter(([_, data]) => data.count >= systematicThreshold)
      .map(([pattern, data]) => ({
        pattern,
        frequency: data.count,
        type: this.classifySystematicError(pattern),
        examples: data.examples
      }));
  }

  /**
   * Classifica erro sistemático
   */
  classifySystematicError(pattern) {
    const [target, selected] = pattern.split('->');
    
    if (this.areSimilarHues(target, selected)) return 'hue_confusion';
    if (this.haveBrightnessDifference(target, selected)) return 'brightness_confusion';
    if (this.areSameCategory(target, selected)) return 'category_confusion';
    return 'unknown_pattern';
  }

  /**
   * Analisa performance por categoria de cor
   */
  analyzeCategoryPerformance(colorData) {
    const { interactions } = colorData;
    
    const categories = {};
    
    // Inicializar categorias
    Object.keys(this.colorCategories).forEach(category => {
      categories[category] = {
        attempted: 0,
        correct: 0,
        accuracy: 0,
        averageResponseTime: 0,
        colors: []
      };
    });

    // Analisar cada interação
    interactions.forEach(interaction => {
      const { targetColor, correct, responseTime } = interaction;
      
      // Verificar em qual categoria a cor se encaixa
      Object.entries(this.colorCategories).forEach(([categoryName, categoryColors]) => {
        if (categoryColors.includes(targetColor)) {
          const category = categories[categoryName];
          category.attempted++;
          if (correct) category.correct++;
          category.colors.push({
            color: targetColor,
            correct,
            responseTime
          });
        }
      });
    });

    // Calcular métricas finais
    Object.keys(categories).forEach(categoryName => {
      const category = categories[categoryName];
      if (category.attempted > 0) {
        category.accuracy = category.correct / category.attempted;
        
        const responseTimes = category.colors
          .map(c => c.responseTime)
          .filter(rt => rt > 0);
        
        category.averageResponseTime = responseTimes.length > 0
          ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length
          : 0;
      }
    });

    return categories;
  }

  /**
   * Analisa efeitos da complexidade
   */
  analyzeComplexityEffects(colorData) {
    const { interactions } = colorData;
    
    const complexityLevels = { 1: [], 2: [], 3: [] };
    
    // Agrupar por nível de complexidade
    interactions.forEach(interaction => {
      const complexity = this.colorComplexity[interaction.targetColor] || 2;
      complexityLevels[complexity].push(interaction);
    });

    // Analisar cada nível
    const analysis = {};
    Object.entries(complexityLevels).forEach(([level, levelInteractions]) => {
      if (levelInteractions.length > 0) {
        const correct = levelInteractions.filter(i => i.correct).length;
        const accuracy = correct / levelInteractions.length;
        
        const responseTimes = levelInteractions
          .map(i => i.responseTime)
          .filter(rt => rt > 0);
        const averageRT = responseTimes.length > 0
          ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length
          : 0;

        analysis[level] = {
          accuracy,
          averageResponseTime: averageRT,
          attempts: levelInteractions.length,
          correct
        };
      }
    });

    // Determinar tolerância à complexidade
    const tolerance = this.calculateComplexityTolerance(analysis);
    
    // Determinar efeito da complexidade
    const effect = this.determineComplexityEffect(analysis);
    
    // Nível ótimo
    const optimal = this.findOptimalComplexity(analysis);
    
    // Resposta ao desafio
    const response = this.assessChallengeResponse(analysis);

    return {
      tolerance,
      effect,
      optimal,
      response,
      levelAnalysis: analysis
    };
  }

  /**
   * Calcula tolerância à complexidade
   */
  calculateComplexityTolerance(analysis) {
    if (!analysis['1'] || !analysis['3']) return 0.5;
    
    const simpleAccuracy = analysis['1'].accuracy || 0;
    const complexAccuracy = analysis['3'].accuracy || 0;
    
    return complexAccuracy / Math.max(simpleAccuracy, 0.1);
  }

  /**
   * Determina efeito da complexidade
   */
  determineComplexityEffect(analysis) {
    const levels = Object.keys(analysis).sort();
    if (levels.length < 2) return 'unknown';
    
    const accuracies = levels.map(level => analysis[level].accuracy);
    
    // Verificar tendência
    let increasing = 0;
    let decreasing = 0;
    
    for (let i = 1; i < accuracies.length; i++) {
      if (accuracies[i] > accuracies[i-1]) increasing++;
      if (accuracies[i] < accuracies[i-1]) decreasing++;
    }
    
    if (decreasing > increasing) return 'decreasing';
    if (increasing > decreasing) return 'improving';
    return 'stable';
  }

  /**
   * Encontra complexidade ótima
   */
  findOptimalComplexity(analysis) {
    let optimal = 1;
    let bestScore = 0;
    
    Object.entries(analysis).forEach(([level, data]) => {
      // Score combinando precisão e velocidade
      const score = data.accuracy * (1 - Math.min(data.averageResponseTime / 5000, 1));
      if (score > bestScore) {
        bestScore = score;
        optimal = parseInt(level);
      }
    });
    
    return optimal;
  }

  /**
   * Avalia resposta ao desafio
   */
  assessChallengeResponse(analysis) {
    if (!analysis['3']) return 'unknown';
    
    const complexPerformance = analysis['3'];
    
    if (complexPerformance.accuracy > 0.8) return 'thrives';
    if (complexPerformance.accuracy > 0.6) return 'adapts';
    if (complexPerformance.accuracy > 0.4) return 'struggles';
    return 'overwhelmed';
  }

  /**
   * Analisa padrões de adaptação
   */
  analyzeAdaptationPatterns(colorData) {
    const { interactions } = colorData;
    
    if (interactions.length < 5) {
      return {
        learning: 'insufficient_data',
        rate: 0,
        improvement: 'unknown',
        plateau: false
      };
    }

    // Dividir em segmentos para análise temporal
    const segmentSize = Math.max(3, Math.floor(interactions.length / 4));
    const segments = [];
    
    for (let i = 0; i < interactions.length; i += segmentSize) {
      const segment = interactions.slice(i, i + segmentSize);
      const correct = segment.filter(int => int.correct).length;
      const accuracy = correct / segment.length;
      
      segments.push({
        startIndex: i,
        endIndex: Math.min(i + segmentSize, interactions.length),
        accuracy,
        size: segment.length
      });
    }

    // Analisar curva de aprendizado
    const learningCurve = this.analyzeLearningCurve(segments);
    
    // Taxa de adaptação
    const adaptationRate = this.calculateAdaptationRate(segments);
    
    // Tendência de melhoria
    const improvementTrend = this.analyzeImprovementTrend(segments);
    
    // Detecção de plateau
    const plateauDetection = this.detectPlateauPattern(segments);

    return {
      learning: learningCurve,
      rate: adaptationRate,
      improvement: improvementTrend,
      plateau: plateauDetection,
      segments
    };
  }

  /**
   * Analisa curva de aprendizado
   */
  analyzeLearningCurve(segments) {
    if (segments.length < 3) return 'insufficient_data';
    
    const accuracies = segments.map(s => s.accuracy);
    const trend = this.calculateLinearTrend(accuracies);
    
    if (trend.slope > 0.1) return 'improving';
    if (trend.slope > 0.05) return 'gradual_improvement';
    if (trend.slope > -0.05) return 'stable';
    if (trend.slope > -0.1) return 'slight_decline';
    return 'declining';
  }

  /**
   * Calcula taxa de adaptação
   */
  calculateAdaptationRate(segments) {
    if (segments.length < 2) return 0;
    
    const firstAccuracy = segments[0].accuracy;
    const lastAccuracy = segments[segments.length - 1].accuracy;
    
    return (lastAccuracy - firstAccuracy) / segments.length;
  }

  /**
   * Analisa tendência de melhoria
   */
  analyzeImprovementTrend(segments) {
    const accuracies = segments.map(s => s.accuracy);
    
    let improving = 0;
    let declining = 0;
    
    for (let i = 1; i < accuracies.length; i++) {
      if (accuracies[i] > accuracies[i-1]) improving++;
      else if (accuracies[i] < accuracies[i-1]) declining++;
    }
    
    if (improving > declining) return 'improving';
    if (declining > improving) return 'declining';
    return 'stable';
  }

  /**
   * Detecta padrão de plateau
   */
  detectPlateauPattern(segments) {
    if (segments.length < 4) return false;
    
    // Verificar se as últimas 3 seções têm variação mínima
    const lastThree = segments.slice(-3).map(s => s.accuracy);
    const variance = this.calculateVariance(lastThree);
    
    return variance < 0.01; // Muito pouca variação
  }

  /**
   * Analisa consistência perceptual
   */
  analyzePerceptualConsistency(colorData) {
    const { interactions } = colorData;
    
    // Agrupar por cor
    const colorGroups = {};
    interactions.forEach(interaction => {
      const color = interaction.targetColor;
      if (!colorGroups[color]) {
        colorGroups[color] = [];
      }
      colorGroups[color].push(interaction);
    });

    // Calcular consistência por cor
    const colorConsistencies = {};
    let totalConsistency = 0;
    let colorCount = 0;

    Object.entries(colorGroups).forEach(([color, colorInteractions]) => {
      if (colorInteractions.length >= 2) {
        const correct = colorInteractions.filter(i => i.correct).length;
        const consistency = correct / colorInteractions.length;
        colorConsistencies[color] = consistency;
        totalConsistency += consistency;
        colorCount++;
      }
    });

    // Índice de consistência geral
    const consistencyIndex = colorCount > 0 ? totalConsistency / colorCount : 0;

    // Score de confiabilidade
    const reliabilityScore = this.calculateReliabilityScore(colorConsistencies);

    // Medida de estabilidade
    const stabilityMeasure = this.calculateStabilityMeasure(interactions);

    // Padrão de variabilidade
    const variabilityPattern = this.analyzeVariabilityPattern(colorConsistencies);

    return {
      index: consistencyIndex,
      reliability: reliabilityScore,
      stability: stabilityMeasure,
      variability: variabilityPattern,
      colorConsistencies
    };
  }

  /**
   * Calcula score de confiabilidade
   */
  calculateReliabilityScore(colorConsistencies) {
    const consistencies = Object.values(colorConsistencies);
    if (consistencies.length === 0) return 0;
    
    const average = consistencies.reduce((sum, c) => sum + c, 0) / consistencies.length;
    const variance = this.calculateVariance(consistencies);
    
    return Math.max(0, average - variance); // Penalizar alta variação
  }

  /**
   * Calcula medida de estabilidade
   */
  calculateStabilityMeasure(interactions) {
    if (interactions.length < 4) return 0.5;
    
    // Dividir em janelas deslizantes
    const windowSize = Math.min(5, Math.floor(interactions.length / 3));
    const accuracies = [];
    
    for (let i = 0; i <= interactions.length - windowSize; i++) {
      const window = interactions.slice(i, i + windowSize);
      const correct = window.filter(int => int.correct).length;
      accuracies.push(correct / window.length);
    }
    
    // Estabilidade como inverso da variação
    const variance = this.calculateVariance(accuracies);
    return Math.max(0, 1 - variance);
  }

  /**
   * Analisa padrão de variabilidade
   */
  analyzeVariabilityPattern(colorConsistencies) {
    const consistencies = Object.values(colorConsistencies);
    if (consistencies.length < 3) return 'insufficient_data';
    
    const variance = this.calculateVariance(consistencies);
    
    if (variance < 0.05) return 'very_stable';
    if (variance < 0.1) return 'stable';
    if (variance < 0.2) return 'moderate';
    if (variance < 0.3) return 'variable';
    return 'highly_variable';
  }

  /**
   * Analisa cores individuais
   */
  analyzeIndividualColors(colorData) {
    const { interactions } = colorData;
    
    const colorAnalysis = {};
    
    // Agrupar por cor
    const colorGroups = {};
    interactions.forEach(interaction => {
      const color = interaction.targetColor;
      if (!colorGroups[color]) {
        colorGroups[color] = [];
      }
      colorGroups[color].push(interaction);
    });

    // Analisar cada cor
    Object.entries(colorGroups).forEach(([color, colorInteractions]) => {
      const correct = colorInteractions.filter(i => i.correct).length;
      const accuracy = correct / colorInteractions.length;
      
      const responseTimes = colorInteractions
        .map(i => i.responseTime)
        .filter(rt => rt > 0);
      const averageRT = responseTimes.length > 0
        ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length
        : 0;

      // Análise de progresso para esta cor
      const progress = this.analyzeColorProgress(colorInteractions);
      
      // Dificuldade percebida
      const difficulty = this.assessColorDifficulty(color, accuracy, averageRT);

      colorAnalysis[color] = {
        accuracy,
        attempts: colorInteractions.length,
        correct,
        averageResponseTime: averageRT,
        difficulty,
        progress,
        consistency: this.calculateColorConsistency(colorInteractions),
        category: this.getColorCategory(color),
        complexity: this.colorComplexity[color] || 2
      };
    });

    return colorAnalysis;
  }

  /**
   * Analisa progresso de uma cor específica
   */
  analyzeColorProgress(colorInteractions) {
    if (colorInteractions.length < 3) return 'insufficient_data';
    
    const accuracies = colorInteractions.map(i => i.correct ? 1 : 0);
    const trend = this.calculateLinearTrend(accuracies);
    
    if (trend.slope > 0.2) return 'improving';
    if (trend.slope > -0.2) return 'stable';
    return 'declining';
  }

  /**
   * Avalia dificuldade da cor
   */
  assessColorDifficulty(color, accuracy, averageRT) {
    const complexityPenalty = (this.colorComplexity[color] || 2) - 1;
    const accuracyScore = accuracy;
    const speedScore = Math.max(0, 1 - (averageRT / 3000));
    
    const difficultyScore = 1 - (accuracyScore * 0.7 + speedScore * 0.3) + complexityPenalty * 0.2;
    
    if (difficultyScore > 0.7) return 'very_difficult';
    if (difficultyScore > 0.5) return 'difficult';
    if (difficultyScore > 0.3) return 'moderate';
    if (difficultyScore > 0.1) return 'easy';
    return 'very_easy';
  }

  /**
   * Calcula consistência de uma cor
   */
  calculateColorConsistency(colorInteractions) {
    if (colorInteractions.length < 2) return 0.5;
    
    const accuracies = colorInteractions.map(i => i.correct ? 1 : 0);
    const variance = this.calculateVariance(accuracies);
    
    return Math.max(0, 1 - variance);
  }

  /**
   * Obtém categoria da cor
   */
  getColorCategory(color) {
    for (const [category, colors] of Object.entries(this.colorCategories)) {
      if (colors.includes(color)) {
        return category;
      }
    }
    return 'other';
  }

  /**
   * Analisa distâncias entre cores
   */
  analyzeColorDistances(colorData) {
    const { interactions } = colorData;
    
    const distanceAnalysis = {
      close_pairs: 0,
      medium_pairs: 0,
      far_pairs: 0,
      accuracy_by_distance: {}
    };

    interactions.forEach(interaction => {
      if (!interaction.correct) {
        const distance = this.calculateColorDistance(
          interaction.targetColor, 
          interaction.selectedColor
        );
        
        if (distance < 0.3) {
          distanceAnalysis.close_pairs++;
        } else if (distance < 0.7) {
          distanceAnalysis.medium_pairs++;
        } else {
          distanceAnalysis.far_pairs++;
        }
      }
    });

    return distanceAnalysis;
  }

  /**
   * Calcula distância entre cores (simplificado)
   */
  calculateColorDistance(color1, color2) {
    // Implementação simplificada baseada em categorias
    if (color1 === color2) return 0;
    
    const cat1 = this.getColorCategory(color1);
    const cat2 = this.getColorCategory(color2);
    
    if (cat1 === cat2) return 0.2;
    
    // Verificar se são cores similares conhecidas
    const isKnownPair = this.confusionPairs.some(pair => 
      (pair[0] === color1 && pair[1] === color2) ||
      (pair[1] === color1 && pair[0] === color2)
    );
    
    if (isKnownPair) return 0.3;
    
    return 1.0; // Cores muito diferentes
  }

  /**
   * Analisa efeitos de sequência
   */
  analyzeSequenceEffects(colorData) {
    const { interactions } = colorData;
    
    if (interactions.length < 3) {
      return { effect: 'insufficient_data' };
    }

    let priming_effects = 0;
    let interference_effects = 0;
    
    for (let i = 1; i < interactions.length; i++) {
      const current = interactions[i];
      const previous = interactions[i - 1];
      
      // Efeito de priming (cor anterior facilita)
      if (current.targetColor === previous.targetColor && current.correct) {
        priming_effects++;
      }
      
      // Efeito de interferência (cor anterior confunde)
      if (current.targetColor !== previous.targetColor && 
          current.selectedColor === previous.targetColor && 
          !current.correct) {
        interference_effects++;
      }
    }

    return {
      effect: 'analyzed',
      priming: priming_effects,
      interference: interference_effects,
      ratio: priming_effects / Math.max(interference_effects, 1)
    };
  }

  // Continua com métodos auxiliares...

  /**
   * Funções auxiliares
   */
  calculateColorDistribution(colors) {
    const distribution = {};
    colors.forEach(color => {
      distribution[color] = (distribution[color] || 0) + 1;
    });
    return distribution;
  }

  areSimilarHues(color1, color2) {
    const similarGroups = [
      ['red', 'orange'],
      ['blue', 'purple', 'lightblue'],
      ['green', 'lightgreen'],
      ['yellow', 'orange']
    ];
    
    return similarGroups.some(group => 
      group.includes(color1) && group.includes(color2)
    );
  }

  haveBrightnessDifference(color1, color2) {
    const lightColors = ['yellow', 'lightblue', 'lightgreen'];
    const darkColors = ['purple', 'darkblue', 'darkgreen'];
    
    return (lightColors.includes(color1) && darkColors.includes(color2)) ||
           (lightColors.includes(color2) && darkColors.includes(color1));
  }

  haveSaturationDifference(color1, color2) {
    const saturated = ['red', 'blue', 'green'];
    const desaturated = ['lightblue', 'lightgreen'];
    
    return (saturated.includes(color1) && desaturated.includes(color2)) ||
           (saturated.includes(color2) && desaturated.includes(color1));
  }

  areSameCategory(color1, color2) {
    return this.getColorCategory(color1) === this.getColorCategory(color2);
  }

  calculateLinearTrend(values) {
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;
    
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    
    return { slope };
  }

  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  analyzeConfusionPatterns_detailed(confusions) {
    // Implementação detalhada dos padrões de confusão
    return {
      frequency_patterns: this.analyzeFrequencyPatterns(confusions),
      temporal_patterns: this.analyzeTemporalConfusionPatterns(confusions),
      categorical_patterns: this.analyzeCategoricalConfusionPatterns(confusions)
    };
  }

  analyzeFrequencyPatterns(confusions) {
    // Análise de frequência das confusões
    return confusions.length > 0 ? 'analyzed' : 'no_data';
  }

  analyzeTemporalConfusionPatterns(confusions) {
    // Análise temporal das confusões
    return confusions.length > 0 ? 'analyzed' : 'no_data';
  }

  analyzeCategoricalConfusionPatterns(confusions) {
    // Análise categórica das confusões
    return confusions.length > 0 ? 'analyzed' : 'no_data';
  }

  // Métodos de avaliação...

  assessVisualMaturity(colorData) {
    const { interactions, uniqueColors } = colorData;
    
    const accuracy = interactions.filter(i => i.correct).length / interactions.length;
    const colorRange = uniqueColors.length;
    
    return Math.min(1, (accuracy * 0.7 + (colorRange / 8) * 0.3));
  }

  assessDiscriminationGrowth(colorData) {
    const adaptationPatterns = this.analyzeAdaptationPatterns(colorData);
    
    if (adaptationPatterns.improvement === 'improving') return 0.8;
    if (adaptationPatterns.improvement === 'stable') return 0.6;
    return 0.4;
  }

  assessPerceptualFlexibility(colorData) {
    const categoryPerformance = this.analyzeCategoryPerformance(colorData);
    
    const categories = Object.values(categoryPerformance);
    const validCategories = categories.filter(cat => cat.attempted > 0);
    
    if (validCategories.length === 0) return 0.5;
    
    const avgAccuracy = validCategories.reduce((sum, cat) => sum + cat.accuracy, 0) / validCategories.length;
    const variance = this.calculateVariance(validCategories.map(cat => cat.accuracy));
    
    return Math.max(0, avgAccuracy - variance);
  }

  assessTherapeuticProgress(colorData) {
    const discriminationProfile = this.analyzeDiscriminationProfile(colorData);
    
    return {
      overall_progress: discriminationProfile.skill,
      accuracy_level: discriminationProfile.accuracy,
      speed_level: discriminationProfile.speed,
      consistency_level: this.analyzePerceptualConsistency(colorData).index
    };
  }

  assessTargetAchievement(colorData) {
    const targets = Object.values(this.therapeuticTargets);
    const achievements = {};
    
    targets.forEach(target => {
      achievements[target] = this.assessSpecificTarget(target, colorData);
    });
    
    return achievements;
  }

  assessSpecificTarget(target, colorData) {
    // Implementação específica para cada alvo terapêutico
    switch (target) {
      case 'Discriminação Visual':
        return this.analyzeDiscriminationProfile(colorData).accuracy;
      case 'Reconhecimento de Cores':
        return this.assessColorRecognition(colorData);
      case 'Atenção Visual':
        return this.assessVisualAttention(colorData);
      default:
        return 0.5;
    }
  }

  assessColorRecognition(colorData) {
    const { uniqueColors, interactions } = colorData;
    const accuracy = interactions.filter(i => i.correct).length / interactions.length;
    
    return Math.min(1, accuracy * (uniqueColors.length / 8));
  }

  assessVisualAttention(colorData) {
    const consistency = this.analyzePerceptualConsistency(colorData);
    return consistency.index;
  }

  identifyInterventionNeeds(colorData) {
    const needs = [];
    
    const discriminationProfile = this.analyzeDiscriminationProfile(colorData);
    if (discriminationProfile.accuracy < 0.6) {
      needs.push('basic_color_discrimination');
    }
    
    const confusionPatterns = this.analyzeConfusionPatterns(colorData);
    if (confusionPatterns.rate > 0.3) {
      needs.push('confusion_reduction');
    }
    
    const consistency = this.analyzePerceptualConsistency(colorData);
    if (consistency.index < 0.6) {
      needs.push('consistency_improvement');
    }
    
    return needs.length > 0 ? needs : ['maintenance'];
  }

  assessAgeAppropriateness(discriminationProfile, userAge) {
    if (!userAge) return { appropriate: 'unknown' };
    
    const expectedAccuracy = Math.max(0.5, Math.min(0.9, 0.5 + (userAge - 4) * 0.1));
    const appropriate = discriminationProfile.accuracy >= expectedAccuracy * 0.8;
    
    return {
      appropriate,
      expected: expectedAccuracy,
      actual: discriminationProfile.accuracy
    };
  }

  assessDifficultyAppropriateness(discriminationProfile, difficulty) {
    const expectedAccuracies = {
      easy: 0.8,
      medium: 0.7,
      hard: 0.6
    };
    
    const expected = expectedAccuracies[difficulty] || 0.7;
    const appropriate = discriminationProfile.accuracy >= expected * 0.8;
    
    return {
      appropriate,
      expected,
      actual: discriminationProfile.accuracy
    };
  }

  determineProgressLevel(discriminationProfile) {
    const { accuracy, skill } = discriminationProfile;
    
    if (accuracy >= 0.9 && skill === 'expert') return 'mastery';
    if (accuracy >= 0.8 && skill === 'advanced') return 'proficient';
    if (accuracy >= 0.7) return 'developing';
    if (accuracy >= 0.6) return 'emerging';
    return 'beginning';
  }

  assessDataQuality(colorData) {
    let qualityScore = 1.0;
    const issues = [];

    if (colorData.interactions.length < 5) {
      qualityScore -= 0.3;
      issues.push('Poucos dados para análise robusta');
    }

    if (colorData.uniqueColors.length < 3) {
      qualityScore -= 0.2;
      issues.push('Variedade limitada de cores');
    }

    const unknownColors = colorData.interactions.filter(i => 
      i.targetColor === 'unknown' || i.selectedColor === 'unknown'
    ).length;
    
    if (unknownColors > 0) {
      qualityScore -= 0.2;
      issues.push(`${unknownColors} cores não identificadas`);
    }

    return {
      score: Math.max(0, qualityScore),
      issues,
      level: qualityScore > 0.8 ? 'high' : qualityScore > 0.5 ? 'medium' : 'low'
    };
  }

  generateColorRecommendations(analysisResults) {
    const recommendations = [];
    const { 
      discriminationProfile, confusionPatterns, categoryPerformance,
      complexityAnalysis, adaptationPatterns, perceptualConsistency 
    } = analysisResults;

    // Recomendações baseadas na precisão geral
    if (discriminationProfile.accuracy < 0.6) {
      recommendations.push({
        category: 'basic_discrimination',
        priority: 'high',
        recommendation: 'Focar em exercícios básicos de discriminação com cores primárias',
        rationale: `Precisão geral baixa (${Math.round(discriminationProfile.accuracy * 100)}%)`
      });
    }

    // Recomendações baseadas em confusões
    if (confusionPatterns.primary.length > 0) {
      const topConfusion = confusionPatterns.primary[0];
      recommendations.push({
        category: 'confusion_resolution',
        priority: 'high',
        recommendation: `Trabalhar especificamente a diferenciação entre ${topConfusion.colorPair}`,
        rationale: `Confusão frequente detectada (${topConfusion.frequency} vezes)`
      });
    }

    // Recomendações baseadas em categorias
    const worstCategory = Object.entries(categoryPerformance)
      .filter(([_, cat]) => cat.attempted > 0)
      .sort(([_, a], [__, b]) => a.accuracy - b.accuracy)[0];
    
    if (worstCategory && worstCategory[1].accuracy < 0.6) {
      recommendations.push({
        category: 'category_improvement',
        priority: 'medium',
        recommendation: `Intensificar treino com cores ${worstCategory[0]}`,
        rationale: `Performance baixa nesta categoria (${Math.round(worstCategory[1].accuracy * 100)}%)`
      });
    }

    // Recomendações baseadas em complexidade
    if (complexityAnalysis.tolerance < 0.5) {
      recommendations.push({
        category: 'complexity_tolerance',
        priority: 'medium',
        recommendation: 'Aumentar gradualmente a complexidade das cores apresentadas',
        rationale: 'Dificuldade com cores mais complexas detectada'
      });
    }

    // Recomendações baseadas em adaptação
    if (adaptationPatterns.improvement === 'declining') {
      recommendations.push({
        category: 'adaptation_support',
        priority: 'high',
        recommendation: 'Implementar estratégias de motivação e pausas regulares',
        rationale: 'Declínio na performance ao longo da sessão'
      });
    }

    // Recomendações baseadas em consistência
    if (perceptualConsistency.index < 0.6) {
      recommendations.push({
        category: 'consistency_improvement',
        priority: 'medium',
        recommendation: 'Trabalhar regulação emocional e técnicas de foco',
        rationale: `Baixa consistência perceptual (${Math.round(perceptualConsistency.index * 100)}%)`
      });
    }

    return recommendations.length > 0 ? recommendations : [{
      category: 'maintenance',
      priority: 'low',
      recommendation: 'Manter nível atual e explorar cores mais desafiadoras',
      rationale: 'Performance de discriminação de cores adequada'
    }];
  }

  generateFallbackAnalysis(errorMessage) {
    return {
      overallAccuracy: 0,
      discriminationSkill: 'unknown',
      discriminationSpeed: 0,
      colorRecognitionLevel: 'unknown',
      primaryConfusions: [],
      confusionRate: 0,
      confusionTypes: {},
      systematicErrors: [],
      primaryColors: { attempted: 0, correct: 0, accuracy: 0 },
      secondaryColors: { attempted: 0, correct: 0, accuracy: 0 },
      warmColors: { attempted: 0, correct: 0, accuracy: 0 },
      coolColors: { attempted: 0, correct: 0, accuracy: 0 },
      lightColors: { attempted: 0, correct: 0, accuracy: 0 },
      darkColors: { attempted: 0, correct: 0, accuracy: 0 },
      complexityTolerance: 0.5,
      complexityEffect: 'unknown',
      optimalComplexity: 1,
      challengeResponse: 'unknown',
      learningCurve: 'unknown',
      adaptationRate: 0,
      improvementTrend: 'unknown',
      plateauDetection: false,
      consistencyIndex: 0.5,
      reliabilityScore: 0.5,
      stabilityMeasure: 0.5,
      variabilityPattern: 'unknown',
      colorSpecificAnalysis: {},
      distanceAnalysis: {},
      sequenceEffects: { effect: 'unknown' },
      visualMaturity: 0.5,
      discriminationGrowth: 0.5,
      perceptualFlexibility: 0.5,
      therapeuticProgress: {},
      targetAchievement: {},
      interventionNeeds: ['data_collection_improvement'],
      ageAppropriate: { appropriate: 'unknown' },
      difficultyAppropriate: { appropriate: false },
      progressLevel: 'unknown',
      totalInteractions: 0,
      uniqueColors: 0,
      colorDistribution: {},
      sessionDuration: 0,
      recommendations: [{
        category: 'data_collection',
        priority: 'high',
        recommendation: 'Melhorar coleta de dados de cor',
        rationale: `Erro na análise: ${errorMessage}`
      }],
      analysisTimestamp: new Date().toISOString(),
      collectorVersion: '2.1.0',
      dataQuality: { score: 0, issues: [errorMessage], level: 'error' },
      status: 'fallback',
      error: errorMessage
    };
  }
}
