/**
 * 📝 SOUND MATCHING COLLECTOR V3
 * Coletor especializado em análise de habilidades de correspondência som-número
 * Portal Betina V3
 */

export class SoundMatchingCollector {
  constructor() {
    this.soundThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.70,
      poor: 0.50,
      critical: 0.30
    };
    
    this.soundTypes = {
      quantity: 'Correspondência quantidade-som',
      sequence: 'Sequência sonora',
      pattern: 'Padrão rítmico',
      recognition: 'Reconhecimento auditivo',
      memory: 'Memória auditiva'
    };
    
    this.cognitiveSkills = {
      auditoryProcessing: 'Processamento auditivo',
      auditoryMemory: 'Memória auditiva',
      audioVisualIntegration: 'Integração audiovisual',
      sequentialProcessing: 'Processamento sequencial',
      rhythmicPatterns: 'Padrões rítmicos'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  
  /**
   * Análise principal das habilidades de correspondência som-número
   */
  async analyze(data) {
    if (!data || !data.soundMatching) {
      console.warn('SoundMatchingCollector: Dados de correspondência sonora não encontrados');
      return this.getDefaultAnalysis();
    }

    const soundData = data.soundMatching;
    
    // Analisar precisão por tipo de som
    const accuracyBySoundType = this.analyzeAccuracyBySoundType(soundData);
    
    // Avaliar capacidade de memória auditiva
    const auditoryMemory = this.assessAuditoryMemory(soundData);
    
    // Analisar integração audiovisual
    const audioVisualIntegration = this.analyzeAudioVisualIntegration(soundData);
    
    // Avaliar velocidade de processamento auditivo
    const auditoryProcessingSpeed = this.assessAuditoryProcessingSpeed(soundData);
    
    // Analisar progresso temporal
    const temporalProgress = this.analyzeTemporalProgress(soundData);
    
    // Detectar padrões de erro auditivo
    const auditoryErrorPatterns = this.analyzeAuditoryErrorPatterns(soundData);
    
    // Calcular índice de habilidade auditiva
    const auditorySkillIndex = this.calculateAuditorySkillIndex(soundData);

    const analysis = {
      timestamp: new Date().toISOString(),
      collector: 'SoundMatchingCollector',
      version: '3.0.0',
      
      // Métricas principais
      overallAccuracy: this.calculateOverallAccuracy(soundData),
      auditorySkillIndex,
      auditoryProcessingSpeed,
      
      // Análises detalhadas
      accuracyBySoundType,
      auditoryMemory,
      audioVisualIntegration,
      temporalProgress,
      auditoryErrorPatterns,
      
      // Habilidades cognitivas específicas
      cognitiveSkills: {
        auditoryProcessing: this.assessAuditoryProcessing(soundData),
        auditoryMemory: this.assessAuditoryMemorySkill(soundData),
        audioVisualIntegration: this.assessAudioVisualIntegrationSkill(soundData),
        sequentialProcessing: this.assessSequentialProcessing(soundData),
        rhythmicPatterns: this.assessRhythmicPatterns(soundData)
      },
      
      // Análise de dificuldade auditiva
      auditoryDifficultyAnalysis: this.analyzeAuditoryDifficultyProgression(soundData),
      
      // Análise de latência de resposta
      responseLatency: this.analyzeResponseLatency(soundData),
      
      // Recomendações
      recommendations: this.generateRecommendations(soundData, auditorySkillIndex),
      
      // Metadados
      metadata: {
        totalSounds: soundData.length || 0,
        uniqueSoundTypes: this.countUniqueSoundTypes(soundData),
        averageSoundDuration: this.calculateAverageSoundDuration(soundData),
        soundComplexityDistribution: this.analyzeSoundComplexityDistribution(soundData)
      }
    };

    return analysis;
  }

  /**
   * Analisar precisão por tipo de som
   */
  analyzeAccuracyBySoundType(data) {
    const accuracyBySoundType = {};
    
    // Agrupar por tipo de som
    const groupedData = this.groupBySoundType(data);
    
    Object.keys(groupedData).forEach(type => {
      const typeData = groupedData[type];
      const correct = typeData.filter(attempt => attempt.isCorrect).length;
      accuracyBySoundType[type] = {
        accuracy: typeData.length > 0 ? correct / typeData.length : 0,
        attempts: typeData.length,
        averageResponseTime: this.calculateAverageTime(typeData),
        averageLatency: this.calculateAverageLatency(typeData),
        difficulty: this.assessSoundTypeDifficulty(typeData)
      };
    });
    
    return accuracyBySoundType;
  }

  /**
   * Avaliar capacidade de memória auditiva
   */
  assessAuditoryMemory(data) {
    // Analisar desempenho baseado no delay entre som e resposta
    const memoryLevels = {
      immediate: [],    // resposta imediata (< 2s após som)
      shortTerm: [],    // resposta rápida (2-5s)
      mediumTerm: [],   // resposta normal (5-10s)
      longTerm: []      // resposta demorada (> 10s)
    };

    data.forEach(attempt => {
      const latency = attempt.responseTime - (attempt.soundDuration || 1000);
      
      if (latency < 2000) memoryLevels.immediate.push(attempt);
      else if (latency < 5000) memoryLevels.shortTerm.push(attempt);
      else if (latency < 10000) memoryLevels.mediumTerm.push(attempt);
      else memoryLevels.longTerm.push(attempt);
    });

    const memoryAnalysis = {};
    Object.keys(memoryLevels).forEach(level => {
      const levelData = memoryLevels[level];
      memoryAnalysis[level] = {
        accuracy: this.calculateAccuracy(levelData),
        averageTime: this.calculateAverageTime(levelData),
        attempts: levelData.length
      };
    });

    return {
      byLatency: memoryAnalysis,
      memorySpan: this.calculateMemorySpan(data),
      retentionRate: this.calculateRetentionRate(data),
      decayPattern: this.analyzeDecayPattern(data)
    };
  }

  /**
   * Analisar integração audiovisual
   */
  analyzeAudioVisualIntegration(data) {
    const integration = {
      synchronization: 0,        // Sincronização som-visual
      crossModalAccuracy: 0,     // Precisão cross-modal
      interferenceResistance: 0,  // Resistência a interferência
      modalityPreference: null    // Preferência de modalidade
    };

    // Analisar desempenho quando há estímulos visuais simultâneos
    const audioOnlyTrials = data.filter(attempt => !attempt.hasVisualStimulus);
    const audioVisualTrials = data.filter(attempt => attempt.hasVisualStimulus);

    const audioOnlyAccuracy = this.calculateAccuracy(audioOnlyTrials);
    const audioVisualAccuracy = this.calculateAccuracy(audioVisualTrials);

    integration.crossModalAccuracy = audioVisualAccuracy;
    integration.interferenceResistance = audioVisualAccuracy / Math.max(audioOnlyAccuracy, 0.1);
    integration.modalityPreference = audioOnlyAccuracy > audioVisualAccuracy ? 'auditory' : 'visual';

    // Analisar sincronização temporal
    integration.synchronization = this.analyzeSynchronization(data);

    return integration;
  }

  /**
   * Avaliar velocidade de processamento auditivo
   */
  assessAuditoryProcessingSpeed(data) {
    const responseTimes = data.map(attempt => attempt.responseTime).filter(Boolean);
    const soundDurations = data.map(attempt => attempt.soundDuration || 1000).filter(Boolean);
    
    if (responseTimes.length === 0) return { speed: 'unknown', score: 0.5 };
    
    // Calcular latência real (tempo após fim do som)
    const latencies = data.map((attempt, index) => 
      attempt.responseTime - soundDurations[index]
    ).filter(latency => latency > 0);
    
    const averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    const medianLatency = this.calculateMedian(latencies);
    
    let speedCategory = 'average';
    let speedScore = 0.5;
    
    if (averageLatency < 1000) {
      speedCategory = 'very_fast';
      speedScore = 0.9;
    } else if (averageLatency < 2000) {
      speedCategory = 'fast';
      speedScore = 0.8;
    } else if (averageLatency < 4000) {
      speedCategory = 'average';
      speedScore = 0.6;
    } else {
      speedCategory = 'slow';
      speedScore = 0.4;
    }
    
    return {
      averageLatency,
      medianLatency,
      speedCategory,
      speedScore,
      consistency: this.calculateLatencyConsistency(latencies),
      processingEfficiency: this.calculateProcessingEfficiency(data)
    };
  }

  /**
   * Analisar progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (data.length < 3) return { trend: 'insufficient_data', improvement: 0 };
    
    const segments = this.divideIntoSegments(data, 3);
    const segmentAccuracies = segments.map(segment => this.calculateAccuracy(segment));
    const segmentLatencies = segments.map(segment => this.calculateAverageLatency(segment));
    
    const accuracyTrend = this.calculateTrend(segmentAccuracies);
    const speedTrend = this.calculateTrend(segmentLatencies.map(latency => 1/latency));
    
    return {
      accuracyTrend,
      speedTrend,
      improvement: segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0],
      segmentAccuracies,
      segmentLatencies,
      auditoryLearningRate: this.calculateLearningRate(segmentAccuracies),
      auditoryStability: this.calculateStability(segmentAccuracies)
    };
  }

  /**
   * Analisar padrões de erro auditivo
   */
  analyzeAuditoryErrorPatterns(data) {
    const errors = data.filter(attempt => !attempt.isCorrect);
    
    const errorTypes = {
      quantityMiscount: 0,      // Erro na contagem de sons
      sequenceConfusion: 0,     // Confusão na sequência
      memoryFailure: 0,         // Falha de memória auditiva
      attentionLapse: 0,        // Lapso de atenção
      processingDelay: 0        // Atraso no processamento
    };

    errors.forEach(error => {
      const latency = error.responseTime - (error.soundDuration || 1000);
      
      if (latency < 500) {
        errorTypes.attentionLapse++;
      } else if (latency > 10000) {
        errorTypes.memoryFailure++;
      } else if (error.soundType === 'sequence') {
        errorTypes.sequenceConfusion++;
      } else if (error.soundType === 'quantity') {
        errorTypes.quantityMiscount++;
      } else {
        errorTypes.processingDelay++;
      }
    });

    return {
      totalErrors: errors.length,
      errorTypes,
      errorRate: data.length > 0 ? errors.length / data.length : 0,
      criticalErrors: this.identifyCriticalAuditoryErrors(errors),
      errorProgression: this.analyzeErrorProgression(data),
      auditoryErrorsByComplexity: this.analyzeErrorsByComplexity(data)
    };
  }

  /**
   * Calcular índice de habilidade auditiva
   */
  calculateAuditorySkillIndex(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speed = this.assessAuditoryProcessingSpeed(data).speedScore;
    const memory = this.assessAuditoryMemory(data);
    const memoryScore = this.calculateMemoryScore(memory);
    const integration = this.analyzeAudioVisualIntegration(data);
    const integrationScore = integration.crossModalAccuracy;
    const consistency = this.calculateConsistency(data);
    
    return (accuracy * 0.3) + (speed * 0.2) + (memoryScore * 0.2) + (integrationScore * 0.15) + (consistency * 0.15);
  }

  /**
   * Avaliar habilidades cognitivas específicas
   */
  assessAuditoryProcessing(data) {
    // Capacidade geral de processar informação auditiva
    const overallAccuracy = this.calculateOverallAccuracy(data);
    const speedScore = this.assessAuditoryProcessingSpeed(data).speedScore;
    
    return (overallAccuracy * 0.7) + (speedScore * 0.3);
  }

  assessAuditoryMemorySkill(data) {
    // Capacidade de reter informação auditiva
    const memory = this.assessAuditoryMemory(data);
    return this.calculateMemoryScore(memory);
  }

  assessAudioVisualIntegrationSkill(data) {
    // Capacidade de integrar informação auditiva e visual
    const integration = this.analyzeAudioVisualIntegration(data);
    return integration.crossModalAccuracy;
  }

  assessSequentialProcessing(data) {
    // Capacidade de processar sequências auditivas
    const sequenceTrials = data.filter(attempt => 
      attempt.soundType && attempt.soundType.includes('sequence')
    );
    
    return this.calculateAccuracy(sequenceTrials);
  }

  assessRhythmicPatterns(data) {
    // Capacidade de reconhecer padrões rítmicos
    const rhythmTrials = data.filter(attempt => 
      attempt.soundType && attempt.soundType.includes('rhythm')
    );
    
    return this.calculateAccuracy(rhythmTrials);
  }

  /**
   * Analisar latência de resposta
   */
  analyzeResponseLatency(data) {
    const latencies = data.map(attempt => 
      attempt.responseTime - (attempt.soundDuration || 1000)
    ).filter(latency => latency > 0);

    if (latencies.length === 0) return { status: 'no_data' };

    return {
      averageLatency: latencies.reduce((a, b) => a + b, 0) / latencies.length,
      medianLatency: this.calculateMedian(latencies),
      latencyVariability: this.calculateVariance(latencies),
      optimalRange: this.calculateOptimalLatencyRange(data),
      latencyDistribution: this.analyzeLatencyDistribution(latencies)
    };
  }

  /**
   * Gerar recomendações
   */
  generateRecommendations(data, skillIndex) {
    const recommendations = [];
    
    if (skillIndex < 0.6) {
      recommendations.push({
        type: 'improvement',
        priority: 'high',
        message: 'Pratique exercícios básicos de correspondência som-número',
        activities: ['simple_sound_counting', 'audio_visual_matching']
      });
    }
    
    const memory = this.assessAuditoryMemory(data);
    if (this.calculateMemoryScore(memory) < 0.5) {
      recommendations.push({
        type: 'memory',
        priority: 'high',
        message: 'Trabalhe memória auditiva com sequências progressivamente mais longas',
        activities: ['memory_sequences', 'auditory_span_games']
      });
    }
    
    const processing = this.assessAuditoryProcessingSpeed(data);
    if (processing.speedCategory === 'slow') {
      recommendations.push({
        type: 'speed',
        priority: 'medium',
        message: 'Pratique respostas mais rápidas a estímulos auditivos',
        activities: ['speed_sound_games', 'reaction_time_training']
      });
    }
    
    const integration = this.analyzeAudioVisualIntegration(data);
    if (integration.crossModalAccuracy < 0.6) {
      recommendations.push({
        type: 'integration',
        priority: 'medium',
        message: 'Desenvolva integração audiovisual com exercícios multimodais',
        activities: ['cross_modal_games', 'synchronization_training']
      });
    }
    
    return recommendations;
  }

  /**
   * Funções auxiliares
   */
  calculateOverallAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter(attempt => attempt.isCorrect).length;
    return correct / data.length;
  }

  groupBySoundType(data) {
    const grouped = {};
    data.forEach(attempt => {
      const type = attempt.soundType || 'quantity';
      if (!grouped[type]) grouped[type] = [];
      grouped[type].push(attempt);
    });
    return grouped;
  }

  calculateAverageTime(data) {
    const times = data.map(attempt => attempt.responseTime).filter(Boolean);
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }

  calculateAverageLatency(data) {
    const latencies = data.map(attempt => 
      attempt.responseTime - (attempt.soundDuration || 1000)
    ).filter(latency => latency > 0);
    return latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0;
  }

  calculateAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter(attempt => attempt.isCorrect).length;
    return correct / data.length;
  }

  calculateMedian(numbers) {
    const sorted = numbers.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? 
      (sorted[middle - 1] + sorted[middle]) / 2 : 
      sorted[middle];
  }

  calculateMemoryScore(memory) {
    const scores = Object.values(memory.byLatency).map(level => level.accuracy);
    return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0.5;
  }

  calculateConsistency(data) {
    if (data.length < 2) return 0.5;
    
    const accuracies = this.divideIntoSegments(data, 3).map(segment => this.calculateAccuracy(segment));
    const variance = this.calculateVariance(accuracies);
    
    return Math.max(0, 1 - variance);
  }

  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
  }

  divideIntoSegments(data, numSegments) {
    const segmentSize = Math.floor(data.length / numSegments);
    const segments = [];
    
    for (let i = 0; i < numSegments; i++) {
      const start = i * segmentSize;
      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;
      segments.push(data.slice(start, end));
    }
    
    return segments.filter(segment => segment.length > 0);
  }

  getDefaultAnalysis() {
    return {
      timestamp: new Date().toISOString(),
      collector: 'SoundMatchingCollector',
      version: '3.0.0',
      overallAccuracy: 0.5,
      auditorySkillIndex: 0.5,
      auditoryProcessingSpeed: { speed: 'unknown', score: 0.5 },
      accuracyBySoundType: {},
      auditoryMemory: {},
      audioVisualIntegration: {},
      temporalProgress: { trend: 'insufficient_data', improvement: 0 },
      auditoryErrorPatterns: { totalErrors: 0, errorRate: 0 },
      cognitiveSkills: {},
      recommendations: [],
      metadata: { totalSounds: 0, uniqueSoundTypes: 0 }
    };
  }
}
