const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/NeuropedagogicalDashboard-CAsMfhwI.js","assets/index-BIwBZl_j.js","assets/react-BQG6_13O.js","assets/react-router-BtSsPy6x.js","assets/react-query-CDommIwN.js","assets/helmet-CSX2cyrn.js","assets/framer-motion-DA-GaQt2.js","assets/prop-types-D_3gT01v.js","assets/index-DnDa0SVZ.css","assets/chart-core-CRFNBRsI.js","assets/chart-react-BbBAr11T.js","assets/NeuropedagogicalDashboard-glAX7q-r.css"])))=>i.map(i=>d[i]);
import{P as e,_ as o,j as a,a as r,b as n,u as s}from"./index-BIwBZl_j.js";import{R as i,r as t}from"./react-router-BtSsPy6x.js";import{P as m,A as d,N as c}from"./NeuropedagogicalDashboard-CAsMfhwI.js";import"./RelatorioADashboard-BivtyRN_.js";import l from"./BackupExportDashboard-DmZCQITt.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";import"./chart-core-CRFNBRsI.js";import"./chart-react-BbBAr11T.js";const b={dashboardContainer:"_dashboardContainer_1b3w6_15",dashboardTabs:"_dashboardTabs_1b3w6_213",dashboardContent:"_dashboardContent_1b3w6_255",dashboardTab:"_dashboardTab_1b3w6_213",active:"_active_1b3w6_321",dashboardWrapper:"_dashboardWrapper_1b3w6_383",loginContainer:"_loginContainer_1b3w6_541",loginBox:"_loginBox_1b3w6_563",loginInput:"_loginInput_1b3w6_601",loginButton:"_loginButton_1b3w6_643"};e.string.isRequired,e.string,e.string,e.bool,e.bool,e.node.isRequired,e.node,e.func,i.lazy(()=>o(()=>import("./NeuropedagogicalDashboard-CAsMfhwI.js").then(e=>e.a),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])).catch(()=>({default:()=>a.jsxDEV("div",{children:"Erro ao carregar Performance Dashboard"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardPremiumContainer.jsx",lineNumber:16,columnNumber:20},void 0)}))),i.lazy(()=>o(()=>import("./NeuropedagogicalDashboard-CAsMfhwI.js").then(e=>e.c),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])).catch(()=>({default:()=>a.jsxDEV("div",{children:"Erro ao carregar Neuropedagogical Dashboard"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardPremiumContainer.jsx",lineNumber:21,columnNumber:20},void 0)}))),i.lazy(()=>o(()=>import("./NeuropedagogicalDashboard-CAsMfhwI.js").then(e=>e.b),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])).catch(()=>({default:()=>a.jsxDEV("div",{children:"Erro ao carregar AI Report"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardPremiumContainer.jsx",lineNumber:26,columnNumber:20},void 0)})));const u={performance:{component:"PerformanceDashboard",title:"Performance Dashboard",description:"Métricas avançadas de performance e uso",access:"premium",icon:"📊",features:["Accuracy detalhada","Tempo de sessão","Pontuação avançada","Progresso completo"]},relatorioA:{component:"AdvancedAIReport",title:"Relatório A - Análise IA",description:"Análise avançada com Inteligência Artificial",access:"premium",icon:"🤖",features:["Análise cognitiva IA","Padrões comportamentais","Previsões desenvolvimento","Mapeamento neural"]},neuropedagogical:{component:"NeuropedagogicalDashboard",title:"Dashboard Neuropedagógico",description:"Métricas especializadas para terapeutas",access:"premium",icon:"🧠",features:["Função executiva","Atenção sustentada","Processamento sensorial","Relatórios profissionais"]},backupEsporte:{component:"BackupEsporteDashboard",title:"Backup e Exportação",description:"Backup e exportação completa dos dados esportivos",access:"premium",icon:"💾",features:["Backup completo","Exportação avançada","Restauração","Sincronização"]}},p=e=>"admin"===u[e]?.access,v=["performance","relatorioA","neuropedagogical","backupEsporte"],h="_adminGate_1p3zc_13",N="_backButton_1p3zc_37",j="_gateContent_1p3zc_83",x="_gateIcon_1p3zc_107",f="_gateTitle_1p3zc_141",D="_gateMessage_1p3zc_161",g="_gateInfo_1p3zc_175",C="_accessRequirements_1p3zc_217",E="_contactInfo_1p3zc_269",P=({title:e="Acesso Restrito",message:o="Este recurso está disponível apenas para administradores do sistema.",onBack:r})=>a.jsxDEV("div",{className:h,children:[r&&a.jsxDEV("button",{className:N,onClick:r,"aria-label":"Voltar para a página anterior",children:"← Voltar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:19,columnNumber:9},void 0),a.jsxDEV("div",{className:j,children:[a.jsxDEV("div",{className:x,children:"🔒"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:29,columnNumber:9},void 0),a.jsxDEV("h2",{className:f,children:e},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:33,columnNumber:9},void 0),a.jsxDEV("p",{className:D,children:o},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:37,columnNumber:9},void 0),a.jsxDEV("div",{className:g,children:[a.jsxDEV("h3",{children:"🛡️ Sistema Integrado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:42,columnNumber:11},void 0),a.jsxDEV("p",{children:"O Sistema Integrado contém informações sensíveis do sistema e métricas avançadas que requerem privilégios administrativos para visualização."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:43,columnNumber:11},void 0),a.jsxDEV("div",{className:C,children:[a.jsxDEV("h4",{children:"Requisitos de Acesso:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:49,columnNumber:13},void 0),a.jsxDEV("ul",{children:[a.jsxDEV("li",{children:"✅ Credenciais administrativas válidas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:51,columnNumber:15},void 0),a.jsxDEV("li",{children:"✅ Permissões de sistema integrado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:52,columnNumber:15},void 0),a.jsxDEV("li",{children:"✅ Sessão administrativa ativa"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:53,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:50,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:48,columnNumber:11},void 0),a.jsxDEV("div",{className:E,children:a.jsxDEV("p",{children:[a.jsxDEV("strong",{children:"📞 Precisa de acesso?"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:59,columnNumber:15},void 0),a.jsxDEV("br",{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:59,columnNumber:53},void 0),"Entre em contato com o administrador do sistema para solicitar as permissões necessárias."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:58,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:57,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:41,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:28,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/AdminGate/AdminGate.jsx",lineNumber:17,columnNumber:5},void 0);P.propTypes={title:e.string,message:e.string,onBack:e.func},e.string,e.object,e.number,e.number,e.oneOf(["small","medium","large"]),e.string,e.func,e.func,e.func,e.node.isRequired,e.oneOf(["primary","secondary","success","danger","warning","info","light","dark","link"]),e.oneOf(["small","medium","large"]),e.oneOf(["default","rounded","pill","circle","square"]),e.bool,e.bool,e.bool,e.node,e.oneOf(["left","right"]),e.string,e.func,e.oneOf(["button","submit","reset"]),e.string;const _=({initialTab:e="performance"})=>{const[o,i]=t.useState(e),[h,N]=t.useState([]),[j,x]=t.useState(!1),[f,D]=t.useState({email:"",password:""}),[g,C]=t.useState(""),[E,_]=t.useState(!1),{isPremium:A,canAccessDashboard:V}=r(),{isAdmin:G,canAccessIntegratedDashboard:y}=n(),{settings:S}=s();t.useEffect(()=>{const o=A?"premium":"public",a=((e,o=!1)=>Object.entries(u).filter(([a,r])=>"admin"===r.access?o:"premium"===e&&"premium"===r.access))(o,G);N(a),"performance"===e||((e,o,a=!1)=>{const r=u[e];return!!r&&("admin"===r.access?a:"premium"!==r.access||"premium"===o)})(e,o,G)||i("performance")},[A,G,e]);const I=async e=>{e.preventDefault(),_(!0),C("");try{const e=await fetch("/api/auth/dashboard/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:f.email,password:f.password,rememberMe:!0})}),o=await e.json();o.success&&o.token?(localStorage.setItem("authToken",o.token),localStorage.setItem("userData",JSON.stringify(o.user||{email:f.email})),x(!0),C(""),_(!1),k()):(C(o.message||"Erro ao fazer login"),_(!1))}catch(o){C("Erro de conexão. Tente novamente."),_(!1)}},k=async()=>{try{const e=localStorage.getItem("authToken");if(!e)return;const o=await fetch("/api/premium/auth/status",{headers:{Authorization:`Bearer ${e}`}}),a=await o.json();o.ok&&a.success}catch(e){}};t.useEffect(()=>{const e=localStorage.getItem("authToken"),o=localStorage.getItem("userData");if(e&&o)try{JSON.parse(o);x(!0),k()}catch(a){localStorage.removeItem("authToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("userData"),x(!1)}else x(!1)},[]);const z=(e,o)=>{D(a=>({...a,[e]:o})),C("")},T=[...h].sort((e,o)=>v.indexOf(e[0])-v.indexOf(o[0]));return a.jsxDEV("div",{className:b.dashboardContainer,children:[a.jsxDEV("div",{className:b.dashboardTabs,children:T.map(([e,r])=>a.jsxDEV("button",{className:`${b.dashboardTab} ${o===e?b.active:""}`,onClick:()=>i(e),"aria-pressed":o===e,children:[a.jsxDEV("span",{"aria-hidden":"true",children:r.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:337,columnNumber:13},void 0)," ",r.title]},e,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:331,columnNumber:11},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:329,columnNumber:7},void 0),a.jsxDEV("div",{className:b.dashboardContent,children:a.jsxDEV("div",{className:b.dashboardWrapper,children:(()=>{if(!j)return a.jsxDEV("div",{className:b.loginContainer,children:a.jsxDEV("div",{className:b.loginBox,children:["          ",a.jsxDEV("div",{style:{textAlign:"center",marginBottom:"1.5rem"},children:[a.jsxDEV("h1",{style:{fontSize:"1.8rem",marginBottom:"0.5rem"},children:p(o)?"🔐 Admin":"🔐 Premium"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:190,columnNumber:13},void 0),a.jsxDEV("p",{style:{opacity:.9,lineHeight:1.4,fontSize:"0.9rem"},children:p(o)?"Credenciais administrativas":"Entre para acessar os dashboards"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:193,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:189,columnNumber:52},void 0),a.jsxDEV("form",{onSubmit:I,style:{display:"flex",flexDirection:"column",gap:"1rem"},children:[a.jsxDEV("div",{children:[a.jsxDEV("label",{style:{display:"block",marginBottom:"0.3rem",fontWeight:"500",fontSize:"0.9rem"},children:"Email:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:203,columnNumber:15},void 0),a.jsxDEV("input",{type:"email",value:f.email,onChange:e=>z("email",e.target.value),className:b.loginInput,placeholder:"Digite seu email",required:!0},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:206,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:202,columnNumber:13},void 0),a.jsxDEV("div",{children:[a.jsxDEV("label",{style:{display:"block",marginBottom:"0.3rem",fontWeight:"500",fontSize:"0.9rem"},children:"Senha:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:217,columnNumber:15},void 0),a.jsxDEV("input",{type:"password",value:f.password,onChange:e=>z("password",e.target.value),className:b.loginInput,placeholder:"Digite sua senha",required:!0},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:220,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:216,columnNumber:13},void 0),g&&a.jsxDEV("div",{style:{background:"rgba(239, 68, 68, 0.2)",color:"#fecaca",padding:"0.8rem",borderRadius:"10px",border:"2px solid #ef4444",textAlign:"center",fontSize:"0.85rem"},children:g},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:231,columnNumber:15},void 0),a.jsxDEV("button",{type:"submit",disabled:E,className:b.loginButton,children:E?"🔄 Entrando...":"🔓 Entrar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:244,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:201,columnNumber:11},void 0),a.jsxDEV("div",{style:{marginTop:"1.5rem",padding:"0.8rem",background:"rgba(59, 130, 246, 0.2)",borderRadius:"10px",border:"2px solid #3b82f6",fontSize:"0.8rem",textAlign:"center"},children:[a.jsxDEV("strong",{children:"💡 Demo:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:262,columnNumber:13},void 0),a.jsxDEV("br",{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:262,columnNumber:38},void 0),a.jsxDEV("code",{children:"<EMAIL>"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:263,columnNumber:13},void 0)," / ",a.jsxDEV("code",{children:"admin123"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:263,columnNumber:45},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:253,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:189,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:188,columnNumber:7},void 0);if(p(o)&&(!G||!y()))return a.jsxDEV(P,{title:"Sistema Integrado - Acesso Restrito",message:"Este dashboard contém informações administrativas sensíveis e está disponível apenas para administradores autorizados do sistema."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:288,columnNumber:23},void 0);switch(o){case"performance":return a.jsxDEV(m,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:298,columnNumber:16},void 0);case"neuropedagogical":return a.jsxDEV(c,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:300,columnNumber:16},void 0);case"backupEsporte":return a.jsxDEV(l,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:302,columnNumber:16},void 0);case"integrated":return a.jsxDEV("div",{className:b.placeholderDashboard,children:[a.jsxDEV("h3",{children:"🔄 Sistema Integrado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:306,columnNumber:13},void 0),a.jsxDEV("p",{children:"Dashboard em desenvolvimento. Use o Relatório A para análises avançadas."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:307,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:305,columnNumber:11},void 0);case"relatorioA":return a.jsxDEV(d,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:311,columnNumber:16},void 0);default:return a.jsxDEV(m,{},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:313,columnNumber:16},void 0)}})()},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:343,columnNumber:9},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:342,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/DashboardContainer.jsx",lineNumber:327,columnNumber:5},void 0)};_.propTypes={initialTab:e.string};export{_ as default};
