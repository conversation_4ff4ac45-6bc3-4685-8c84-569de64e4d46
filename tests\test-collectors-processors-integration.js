/**
 * 🔧 TESTE DE INTEGRAÇÃO COLETORES + PROCESSADORES
 * Valida a integração completa entre coletores e processadores
 * Portal Betina V3
 */

import { GameSpecificProcessors } from './src/api/services/processors/GameSpecificProcessors.js';

// Dados de teste para diferentes jogos
const testGameData = {
  CreativePainting: {
    sessionId: 'test-session-integration-creative',
    userId: 'test-user-123',
    gameType: 'CreativePainting',
    startTime: Date.now() - 300000,
    endTime: Date.now(),
    
    // Dados específicos do Creative Painting
    brushStrokes: [
      { x: 100, y: 150, pressure: 0.8, size: 5, color: '#FF0000', timestamp: Date.now() - 200000 },
      { x: 120, y: 170, pressure: 0.6, size: 8, color: '#00FF00', timestamp: Date.now() - 180000 },
      { x: 140, y: 190, pressure: 0.9, size: 3, color: '#0000FF', timestamp: Date.now() - 160000 }
    ],
    colorMixings: [
      { targetColor: '#FF8800', resultColor: '#FF7700', isCorrect: true, responseTime: 2500 },
      { targetColor: '#00FFFF', resultColor: '#00AAFF', isCorrect: false, responseTime: 4000 }
    ],
    toolSelections: [
      { toolId: 'brush', correctTool: 'brush', isInappropriate: false, responseTime: 1500 },
      { toolId: 'pencil', correctTool: 'brush', isInappropriate: true, responseTime: 3000 }
    ],
    actions: [
      { type: 'brush_stroke', success: true, timestamp: Date.now() - 200000 },
      { type: 'color_mix', success: false, timestamp: Date.now() - 180000 }
    ],
    metrics: {
      creativityScore: 75,
      coordinationScore: 68,
      engagementTime: 280,
      colorsUsed: 8,
      toolChanges: 4
    }
  },

  LetterRecognition: {
    sessionId: 'test-session-integration-letter',
    userId: 'test-user-123',
    gameType: 'LetterRecognition',
    startTime: Date.now() - 240000,
    endTime: Date.now(),
    
    // Dados específicos do Letter Recognition
    attempts: [
      {
        targetLetter: 'A',
        selectedLetter: 'A',
        isCorrect: true,
        position: 'center',
        responseTime: 1200,
        distractors: ['B', 'H', 'R']
      },
      {
        targetLetter: 'B',
        selectedLetter: 'D',
        isCorrect: false,
        position: 'right',
        responseTime: 2500,
        distractors: ['P', 'R', 'D']
      }
    ],
    letterForm: 'B',
    recognitionSpeed: 2250,
    context: 'isolated',
    visualErrors: [
      { type: 'mirror_confusion', orientation: 'horizontal', mirrorConfusion: true }
    ],
    auditoryErrors: [
      { type: 'similar_sound', phoneticSimilarity: 0.7 }
    ],
    readingSpeed: 12,
    visualAttentionScores: { focus: 0.7, sustain: 0.6, selective: 0.8 },
    linguisticMetrics: { phonologicalAwareness: 0.65, orthographicProcessing: 0.72 }
  },

  PadroesVisuais: {
    sessionId: 'test-session-integration-patterns',
    userId: 'test-user-123',
    gameType: 'PadroesVisuais',
    startTime: Date.now() - 180000,
    endTime: Date.now(),
    
    // Dados específicos dos Padrões Visuais
    patternSequences: [
      {
        pattern: ['red', 'blue', 'red', 'blue'],
        response: ['red', 'blue', 'red', 'blue'],
        isCorrect: true,
        responseTime: 2500
      },
      {
        pattern: ['circle', 'square', 'triangle'],
        response: ['circle', 'square', 'circle'],
        isCorrect: false,
        responseTime: 3800
      }
    ],
    attemptHistory: [
      {
        timestamp: Date.now() - 150000,
        isCorrect: true,
        responseTime: 1200,
        targetItem: { shape: 'circle', color: 'red' },
        selectedItem: { shape: 'circle', color: 'red' }
      },
      {
        timestamp: Date.now() - 120000,
        isCorrect: false,
        responseTime: 2500,
        targetItem: { shape: 'square', color: 'blue' },
        selectedItem: { shape: 'triangle', color: 'blue' }
      }
    ],
    visualDiscrimination: { score: 75, errorRate: 0.25 },
    spatialRelations: { accuracy: 0.8, responseTime: 2200 },
    patternComplexity: 'medium'
  }
};

async function testCollectorsProcessorsIntegration() {
  console.log('\n🔧 === TESTE DE INTEGRAÇÃO COLETORES + PROCESSADORES ===');
  console.log(`📅 Iniciado em: ${new Date().toISOString()}\n`);

  try {
    // Inicializar processadores
    console.log('🚀 Inicializando GameSpecificProcessors...');
    const processors = new GameSpecificProcessors();
    console.log('✅ Processadores inicializados com sucesso\n');

    // Testar cada jogo
    for (const [gameType, gameData] of Object.entries(testGameData)) {
      console.log(`\n🎮 TESTANDO INTEGRAÇÃO: ${gameType}`);
      console.log('='.repeat(50));
      
      try {
        // Verificar se coletores estão disponíveis
        const collectorsInfo = processors.gameCollectors[gameType];
        if (collectorsInfo && collectorsInfo.hub) {
          console.log(`✅ Hub de coletores disponível para ${gameType}`);
          console.log(`📊 Coletores ativos:`, Object.keys(collectorsInfo.collectors || {}));
          
          // Testar coleta de dados individual dos coletores
          console.log(`\n🔍 Testando coletores individuais...`);
          for (const [collectorName, collector] of Object.entries(collectorsInfo.collectors || {})) {
            try {
              if (collector && typeof collector.collect === 'function') {
                const collectorResult = await collector.collect(gameData);
                console.log(`  ✅ ${collectorName}: dados coletados`, typeof collectorResult);
              } else {
                console.log(`  ⚠️ ${collectorName}: método collect não disponível`);
              }
            } catch (error) {
              console.log(`  ❌ ${collectorName}: erro na coleta -`, error.message);
            }
          }
        } else {
          console.log(`⚠️ Hub de coletores não disponível para ${gameType}`);
        }

        // Processar dados do jogo
        console.log(`\n🔄 Processando dados do jogo...`);
        const result = await processors.processGameData(gameType, gameData);
        
        if (result && result.specificAnalysis) {
          console.log(`✅ Processamento bem-sucedido para ${gameType}`);
          console.log(`📊 Análise específica:`, Object.keys(result.specificAnalysis));
          console.log(`🏥 Análise terapêutica:`, Object.keys(result.therapeuticAnalysis || {}));
          console.log(`📈 Coletores utilizados:`, result.metadata?.collectorsUsed || []);
          
          // Verificar se dados dos coletores foram integrados
          if (result.specificAnalysis.metrics) {
            console.log(`💫 Métricas integradas encontradas`);
          }
          
          if (result.therapeuticAnalysis) {
            console.log(`🎯 Análise terapêutica gerada com sucesso`);
          }
          
        } else {
          console.log(`❌ Falha no processamento de ${gameType}`);
        }
        
      } catch (error) {
        console.error(`❌ Erro ao testar ${gameType}:`, error.message);
      }
    }

    // Teste de integração completa
    console.log(`\n\n🔗 === TESTE DE INTEGRAÇÃO COMPLETA ===`);
    console.log('Verificando se todos os coletores estão sendo usados pelos processadores...\n');
    
    for (const [gameType, collectorsInfo] of Object.entries(processors.gameCollectors)) {
      console.log(`🎮 ${gameType}:`);
      
      if (collectorsInfo.hub && collectorsInfo.collectors) {
        const collectorCount = Object.keys(collectorsInfo.collectors).length;
        console.log(`  📊 ${collectorCount} coletores disponíveis`);
        console.log(`  🔗 Hub integrado: ${collectorsInfo.hub.constructor.name}`);
        
        // Verificar se o processador existe
        if (processors.processors[gameType]) {
          console.log(`  ✅ Processador correspondente disponível`);
        } else {
          console.log(`  ⚠️ Processador não encontrado`);
        }
      } else {
        console.log(`  ❌ Coletores não inicializados`);
      }
    }

    console.log(`\n\n📈 === RESUMO DA INTEGRAÇÃO ===`);
    
    // Contar jogos com integração completa (hub + processador)
    let gamesWithCompleteIntegration = 0;
    for (const [gameType, collectorsInfo] of Object.entries(processors.gameCollectors)) {
      if (collectorsInfo.hub && collectorsInfo.collectors && processors.processors[gameType]) {
        gamesWithCompleteIntegration++;
      }
    }
    
    console.log(`✅ Jogos com integração completa: ${gamesWithCompleteIntegration}`);
    console.log(`🔗 Total de hubs de coletores: ${Object.keys(processors.gameCollectors).length}`);
    console.log(`⚙️ Total de processadores: ${Object.keys(processors.processors).length}`);
    console.log(`🎮 Jogos testados com dados: ${Object.keys(testGameData).length}`);
    console.log(`✅ Teste de integração concluído com sucesso!\n`);

  } catch (error) {
    console.error('❌ Erro geral no teste de integração:', error);
    console.error('Stack:', error.stack);
  }
}

// Executar o teste
testCollectorsProcessorsIntegration()
  .then(() => {
    console.log('🏁 Teste de integração finalizado!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Falha crítica no teste:', error);
    process.exit(1);
  });
