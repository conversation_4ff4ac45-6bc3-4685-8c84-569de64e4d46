/**
 * @file AdvancedAIReport.module.css
 * @description Estilos modulares para Dashboard de Relatório de IA Avançado
 * @version 3.0.0
 */

/* Container principal do dashboard */
.dashboardContainer {
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

/* Loading e Error States */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: #f8fafc;
  border-radius: 12px;
}

.errorState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  text-align: center;
  background-color: #f8fafc;
  border-radius: 12px;
  color: #e53e3e;
}

/* Header do dashboard */
.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
  gap: 20px;
}

.headerLeft {
  flex: 1;
}

.dashboardTitle {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboardSubtitle {
  margin: 0;
  font-size: 16px;
  color: #64748b;
  line-height: 1.4;
}

.titleIcon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 8px;
  border-radius: 8px;
  color: white;
  font-size: 20px;
}

/* Controles do dashboard */
.dashboardControls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.analysisSelector,
.timeframeSelector,
.chatButton,
.mcpButton,
.refreshButton {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.analysisSelector:hover,
.timeframeSelector:hover,
.chatButton:hover,
.mcpButton:hover,
.refreshButton:hover {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.analysisSelector:focus,
.timeframeSelector:focus,
.chatButton:focus,
.mcpButton:focus,
.refreshButton:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.chatButton.active,
.mcpButton.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: transparent;
}

/* Status Bar */
.statusBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  padding: 12px 20px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #e2e8f0;
}

.statusItem {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
}

.statusIcon {
  font-size: 16px;
}

.statusIcon.connected {
  color: #10b981;
}

.statusIcon.disconnected {
  color: #ef4444;
}

/* Seções dos componentes */
.mcpSection {
  margin-bottom: 32px;
}

.ieBrandSection {
  margin-bottom: 32px;
}

.aiChatComponent {
  z-index: 1001;
}

.analysisSelector:hover,
.timeframeSelector:hover,
.analysisSelector:focus,
.timeframeSelector:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  outline: none;
}

.refreshButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Dashboard Type Controls */
.dashboardTypeControls {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 16px;
}

.typeButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 14px;
  border-radius: 8px;
  border: none;
  background: #f3f4f6;
  color: #4b5563;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.typeButton:hover {
  background: #e5e7eb;
}

.typeButton.active {
  background: #3b82f6;
  color: white;
}

.typeIcon {
  font-size: 18px;
}

/* Dashboard Mode Controls */
.dashboardModeControls {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
}

.modeButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 6px;
  border: none;
  background: #f3f4f6;
  color: #4b5563;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modeButton:hover {
  background: #e5e7eb;
}

.modeButton.active {
  background: #4b5563;
  color: white;
}

.modeIcon {
  font-size: 16px;
}

/* Dashboard Container and Placeholders */
.unifiedDashboardWrapper {
  margin-bottom: 24px;
}

.dashboardContainer {
  background: #ffffff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.dashboardTitle {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px;
  color: #111827;
}

.dashboardInfo {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 16px;
}

.dashboardPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #f9fafb;
  border-radius: 8px;
  text-align: center;
}

.placeholderIcon {
  font-size: 32px;
  margin-bottom: 12px;
}

.dashboardComponent {
  margin-top: 16px;
}

/* Grid de métricas */
.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.metricCard {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metricCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.metricHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.metricTitle {
  font-size: 14px;
  font-weight: 500;
  color: #718096;
  margin: 0;
}

.metricIcon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.metricIcon.style {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metricIcon.strengths {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metricIcon.milestone {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metricIcon.recommendations {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.metricValue {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  line-height: 1;
}

.metricTrend {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.metricTrend.trendPositive {
  color: #38a169;
}

/* Grid de gráficos */
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chartCard {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.chartCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.chartTitle {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chartContainer {
  height: 300px;
  position: relative;
}

/* Seção de Insights */
.insightsSection {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.insightsTitle {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.insightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.insightCard {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.insightCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.insightTitle {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.insightContent {
  color: #4a5568;
  line-height: 1.6;
}

.insightContent p {
  margin: 8px 0;
  font-size: 14px;
}

/* Seção de Recomendações */
.recommendationsSection {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.recommendationsTitle {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommendationsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 16px;
}

.recommendationCard {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border: 1px solid #feb2b2;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transition: all 0.3s ease;
}

.recommendationCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #f56565;
}

.recommendationIcon {
  font-size: 20px;
  color: #e53e3e;
  flex-shrink: 0;
}

.recommendationContent {
  flex: 1;
}

.recommendationContent p {
  margin: 0;
  color: #2d3748;
  font-size: 14px;
  line-height: 1.5;
}

/* Seção de Insights da IA */
.aiInsightsSection {
  background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
  border: 1px solid #81e6d9;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.aiInsightsTitle {
  font-size: 20px;
  font-weight: 600;
  color: #234e52;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.aiInsightsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.aiInsightCard {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid #81e6d9;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  transition: all 0.3s ease;
}

.aiInsightCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #4fd1c7;
  background: rgba(255, 255, 255, 0.9);
}

.aiInsightIcon {
  font-size: 20px;
  color: #319795;
  flex-shrink: 0;
}

.aiInsightContent {
  flex: 1;
}

.aiInsightContent p {
  margin: 0;
  color: #234e52;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 16px;
  }

  .dashboardHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .dashboardControls {
    flex-wrap: wrap;
    justify-content: flex-start;
  }

  .metricsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chartsGrid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .insightsGrid,
  .recommendationsGrid,
  .aiInsightsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chartContainer {
    height: 250px;
  }

  .dashboardTitle {
    font-size: 24px;
  }

  .metricValue {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .dashboardContainer {
    padding: 12px;
  }

  .chartsGrid {
    grid-template-columns: 1fr;
  }

  .chartContainer {
    height: 200px;
  }

  .metricCard {
    padding: 16px;
  }

  .insightsSection,
  .recommendationsSection,
  .aiInsightsSection {
    padding: 16px;
  }
}
