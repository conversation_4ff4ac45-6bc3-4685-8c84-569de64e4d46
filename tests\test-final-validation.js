/**
 * @file test-final-validation.js
 * @description Teste final de validação completa do Portal Betina V3
 * @version 1.0.0
 */

import SystemOrchestrator from './src/api/services/core/SystemOrchestrator.js';
import { getAnalysisOrchestrator } from './src/api/services/analysis/AnalysisOrchestrator.js';
import { logger } from './src/api/services/core/logging/StructuredLogger.js';

console.log('🎊 INICIANDO VALIDAÇÃO FINAL DO PORTAL BETINA V3');
console.log('================================================================================');

const GAMES_TO_TEST = [
  'ColorMatch',
  'MemoryGame', 
  'MusicalSequence',
  'ImageAssociation',
  'PadroesVisuais',
  'ContagemNumeros'
];

async function runFinalValidation() {
  const startTime = Date.now();
  const results = {
    systemInitialization: false,
    healthChecks: false,
    gameProcessing: { total: 0, successful: 0, games: {} },
    analysis: { total: 0, successful: 0, orchestrations: {} },
    performance: { avgProcessingTime: 0, cacheHitRate: 0 },
    overallSuccess: false
  };

  try {
    console.log('\n🔧 FASE 1: INICIALIZAÇÃO DO SISTEMA');
    console.log('================================================================================');
    
    // Inicializar SystemOrchestrator
    console.log('🏗️ Inicializando SystemOrchestrator...');
    const systemOrchestrator = new SystemOrchestrator({
      mode: 'production',
      enableGameSpecificProcessors: true,
      enableMultisensoryIntegration: true,
      logLevel: 'info'
    });
    console.log('✅ SystemOrchestrator inicializado');
    
    // Inicializar AnalysisOrchestrator
    console.log('🎭 Inicializando AnalysisOrchestrator...');
    const analysisOrchestrator = getAnalysisOrchestrator({
      enableParallelAnalysis: true,
      analysisTimeout: 10000
    });
    console.log('✅ AnalysisOrchestrator inicializado');
    
    results.systemInitialization = true;
    
    console.log('\n🏥 FASE 2: VALIDAÇÃO DE HEALTH CHECKS');
    console.log('================================================================================');
    
    // Aguardar um pouco para sistemas se estabilizarem
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Verificar health do sistema
    console.log('🏥 Verificando saúde dos componentes...');
    const healthStatus = await systemOrchestrator.getSystemHealth();
    
    if (healthStatus && healthStatus.overall === 'healthy') {
      console.log('✅ Todos os componentes estão saudáveis');
      results.healthChecks = true;
    } else {
      console.log('⚠️ Alguns componentes podem ter problemas');
      console.log('📊 Status:', healthStatus?.overall || 'unknown');
    }
    
    console.log('\n🎮 FASE 3: VALIDAÇÃO DE PROCESSAMENTO DE JOGOS');
    console.log('================================================================================');
    
    for (const gameName of GAMES_TO_TEST) {
      console.log(`\n🎯 Testando ${gameName}...`);
      results.gameProcessing.total++;
      
      try {
        // Criar dados de sessão simulada
        const sessionData = {
          childId: 'validation_child_001',
          sessionId: `validation_session_${gameName}_${Date.now()}`,
          gameName: gameName,
          timestamp: new Date().toISOString(),
          duration: 45000 + Math.random() * 30000, // 45-75 segundos
          accuracy: 0.6 + Math.random() * 0.3, // 60-90%
          responseTime: 1000 + Math.random() * 2000, // 1-3 segundos
          interactions: Array.from({ length: 8 + Math.floor(Math.random() * 8) }, (_, i) => ({
            type: 'interaction',
            timestamp: Date.now() + i * 1000,
            success: Math.random() > 0.3,
            responseTime: 800 + Math.random() * 1500
          })),
          completed: true,
          difficulty: 'medium'
        };
        
        // Processar no sistema
        const processingStart = Date.now();
        const processedMetrics = await systemOrchestrator.processGameMetrics(
          sessionData.childId, 
          sessionData.gameName, 
          sessionData
        );
        const processingTime = Date.now() - processingStart;
        
        console.log(`  ✅ Processamento: ${processingTime}ms`);
        console.log(`  📊 Precisão: ${(sessionData.accuracy * 100).toFixed(1)}%`);
        console.log(`  ⚡ Tempo de resposta: ${sessionData.responseTime}ms`);
        
        results.gameProcessing.successful++;
        results.gameProcessing.games[gameName] = {
          success: true,
          processingTime,
          sessionData: {
            accuracy: sessionData.accuracy,
            responseTime: sessionData.responseTime,
            interactions: sessionData.interactions.length
          }
        };
        
      } catch (error) {
        console.log(`  ❌ Erro: ${error.message}`);
        results.gameProcessing.games[gameName] = {
          success: false,
          error: error.message
        };
      }
    }
    
    console.log('\n🔬 FASE 4: VALIDAÇÃO DE ANÁLISES ESPECIALIZADAS');
    console.log('================================================================================');
    
    // Testar análises com os dados processados
    const analysisTestGames = Object.keys(results.gameProcessing.games)
      .filter(game => results.gameProcessing.games[game].success)
      .slice(0, 3); // Testar 3 jogos
    
    for (const gameName of analysisTestGames) {
      console.log(`\n🧠 Analisando ${gameName}...`);
      results.analysis.total++;
      
      try {
        // Criar sessão para análise
        const sessionForAnalysis = {
          id: `analysis_session_${gameName}_${Date.now()}`,
          childId: 'validation_child_001',
          gameName: gameName,
          timestamp: new Date().toISOString(),
          duration: 60000,
          accuracy: 0.75,
          responseTime: 1500,
          interactions: [
            { type: 'click', success: true, responseTime: 1200 },
            { type: 'click', success: false, responseTime: 1800 },
            { type: 'click', success: true, responseTime: 1100 }
          ]
        };
        
        // Executar análise completa
        const analysisStart = Date.now();
        const completeAnalysis = await analysisOrchestrator.orchestrateCompleteAnalysis(
          sessionForAnalysis,
          { forceRefresh: true }
        );
        const analysisTime = Date.now() - analysisStart;
        
        const overallScore = completeAnalysis.consolidated?.overallScore || 0;
        const analysesExecuted = Object.keys(completeAnalysis.analyses || {}).length;
        
        console.log(`  ✅ Análise: ${analysisTime}ms`);
        console.log(`  📊 Score geral: ${(overallScore * 100).toFixed(1)}%`);
        console.log(`  🔬 Analisadores: ${analysesExecuted}`);
        
        results.analysis.successful++;
        results.analysis.orchestrations[gameName] = {
          success: true,
          analysisTime,
          overallScore,
          analysesExecuted,
          orchestrationId: completeAnalysis.orchestrationId
        };
        
      } catch (error) {
        console.log(`  ❌ Erro: ${error.message}`);
        results.analysis.orchestrations[gameName] = {
          success: false,
          error: error.message
        };
      }
    }
    
    console.log('\n📊 FASE 5: COLETA DE MÉTRICAS DE PERFORMANCE');
    console.log('================================================================================');
    
    // Coletar métricas de performance
    const orchestratorMetrics = analysisOrchestrator.getOrchestratorMetrics();
    const systemMetrics = systemOrchestrator.getSystemMetrics ? 
      await systemOrchestrator.getSystemMetrics() : {};
    
    results.performance = {
      avgProcessingTime: Object.values(results.gameProcessing.games)
        .filter(g => g.success && g.processingTime)
        .reduce((sum, g) => sum + g.processingTime, 0) / results.gameProcessing.successful || 0,
      
      avgAnalysisTime: Object.values(results.analysis.orchestrations)
        .filter(a => a.success && a.analysisTime)
        .reduce((sum, a) => sum + a.analysisTime, 0) / results.analysis.successful || 0,
      
      cacheHitRate: orchestratorMetrics?.cache?.hitRate || 0,
      
      successRates: {
        gameProcessing: (results.gameProcessing.successful / results.gameProcessing.total) * 100,
        analysis: (results.analysis.successful / results.analysis.total) * 100
      }
    };
    
    console.log(`⚡ Tempo médio de processamento: ${results.performance.avgProcessingTime.toFixed(0)}ms`);
    console.log(`🧠 Tempo médio de análise: ${results.performance.avgAnalysisTime.toFixed(0)}ms`);
    console.log(`💾 Taxa de cache hit: ${(results.performance.cacheHitRate * 100).toFixed(1)}%`);
    console.log(`🎮 Taxa de sucesso (jogos): ${results.performance.successRates.gameProcessing.toFixed(1)}%`);
    console.log(`🔬 Taxa de sucesso (análises): ${results.performance.successRates.analysis.toFixed(1)}%`);
    
    // Determinar sucesso geral
    results.overallSuccess = 
      results.systemInitialization &&
      results.healthChecks &&
      results.performance.successRates.gameProcessing >= 80 &&
      results.performance.successRates.analysis >= 70;
    
    const totalTime = Date.now() - startTime;
    
    console.log('\n🎊 RESULTADOS FINAIS DA VALIDAÇÃO');
    console.log('================================================================================');
    
    console.log(`\n📊 RESUMO EXECUTIVO:`);
    console.log(`✅ Inicialização do sistema: ${results.systemInitialization ? 'SUCESSO' : 'FALHA'}`);
    console.log(`🏥 Health checks: ${results.healthChecks ? 'SUCESSO' : 'FALHA'}`);
    console.log(`🎮 Processamento de jogos: ${results.gameProcessing.successful}/${results.gameProcessing.total} (${results.performance.successRates.gameProcessing.toFixed(1)}%)`);
    console.log(`🔬 Análises especializadas: ${results.analysis.successful}/${results.analysis.total} (${results.performance.successRates.analysis.toFixed(1)}%)`);
    console.log(`⏱️ Tempo total de validação: ${Math.round(totalTime / 1000)}s`);
    
    console.log(`\n📈 PERFORMANCE:`);
    console.log(`• Processamento médio: ${results.performance.avgProcessingTime.toFixed(0)}ms`);
    console.log(`• Análise média: ${results.performance.avgAnalysisTime.toFixed(0)}ms`);
    console.log(`• Cache efficiency: ${(results.performance.cacheHitRate * 100).toFixed(1)}%`);
    
    if (results.overallSuccess) {
      console.log(`\n🎉 VALIDAÇÃO FINAL: ✅ SUCESSO COMPLETO!`);
      console.log(`\n🌟 O Portal Betina V3 está totalmente funcional e pronto para produção!`);
    } else {
      console.log(`\n⚠️ VALIDAÇÃO FINAL: ❌ ALGUNS PROBLEMAS IDENTIFICADOS`);
      console.log(`\n🔧 Recomenda-se revisar os componentes com falhas antes da produção.`);
    }
    
    logger.info('🎊 Validação final concluída', {
      type: 'final_validation_completed',
      results: {
        overallSuccess: results.overallSuccess,
        systemInitialization: results.systemInitialization,
        healthChecks: results.healthChecks,
        gameProcessingRate: results.performance.successRates.gameProcessing,
        analysisRate: results.performance.successRates.analysis,
        totalTime: totalTime
      }
    });
    
    return results;
    
  } catch (error) {
    console.error('\n💥 ERRO CRÍTICO NA VALIDAÇÃO:', error.message);
    console.error('Stack trace:', error.stack);
    
    logger.error('❌ Falha crítica na validação final', {
      type: 'final_validation_error',
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
}

// Executar validação
runFinalValidation()
  .then(results => {
    console.log('\n================================================================================');
    console.log('🎊 VALIDAÇÃO FINAL CONCLUÍDA!');
    console.log('================================================================================');
    process.exit(results.overallSuccess ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 Validação falhou:', error.message);
    process.exit(1);
  });
