/**
 * Validador de sintaxe específico para user-backup.js
 */

import { readFileSync } from 'fs'

try {
  console.log('🔍 Validando sintaxe do user-backup.js...')
  
  const filePath = './src/api/routes/backup/user-backup.js'
  const content = readFileSync(filePath, 'utf8')
  
  // Procurar por parênteses duplos problemáticos
  const lines = content.split('\n')
  const issues = []
  
  lines.forEach((line, index) => {
    const lineNum = index + 1
    
    // Procurar por )) problemáticos
    if (line.includes('))') && !line.includes(')))')) {
      // Verificar se é um caso válido
      const validCases = [
        'map(', 'filter(', 'reduce(', 'forEach(', 'sort(', 'slice(',
        'Math.', 'JSON.', 'Object.', 'Array.', 'Date.',
        '.includes(', '.indexOf(', '.match(', '.replace('
      ]
      
      const isValid = validCases.some(validCase => line.includes(validCase))
      
      if (!isValid && line.trim().endsWith('))')) {
        issues.push(`Linha ${lineNum}: Possível parênteses duplo problemático - "${line.trim()}"`)
      }
    }
    
    // Procurar por estruturas incompletas
    if (line.includes('}') && line.includes('))') && line.split('}').length !== line.split('))').length) {
      issues.push(`Linha ${lineNum}: Possível estrutura incompleta - "${line.trim()}"`)
    }
  })
  
  if (issues.length === 0) {
    console.log('✅ Nenhum problema de sintaxe óbvio encontrado!')
  } else {
    console.log('❌ Possíveis problemas encontrados:')
    issues.forEach(issue => console.log(`   ${issue}`))
  }
  
  // Verificar balanceamento geral
  const openParens = (content.match(/\(/g) || []).length
  const closeParens = (content.match(/\)/g) || []).length
  const openBraces = (content.match(/\{/g) || []).length
  const closeBraces = (content.match(/\}/g) || []).length
  
  console.log(`\n📊 Contadores:`)
  console.log(`   Parênteses: ${openParens} abertos, ${closeParens} fechados (${openParens === closeParens ? '✅' : '❌'})`)
  console.log(`   Chaves: ${openBraces} abertas, ${closeBraces} fechadas (${openBraces === closeBraces ? '✅' : '❌'})`)
  
} catch (error) {
  console.error('❌ Erro ao validar arquivo:', error.message)
}
