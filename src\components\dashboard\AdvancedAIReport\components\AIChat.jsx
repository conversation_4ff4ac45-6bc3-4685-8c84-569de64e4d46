/**
 * @file AIChat.jsx
 * @description Componente de Chat IA para Dashboard A - Integração IE Brand
 * @version 3.0.0
 * @premium true
 */

import React, { useState, useRef, useEffect } from 'react'
import styles from './AIChat.module.css'

const AIChat = ({ className, onClose, isVisible, dashboardData }) => {
  const [messages, setMessages] = useState([
    {
      id: 'welcome',
      type: 'ai',
      content: 'Olá! Sou a IA da IE Brand. Como posso ajudá-lo hoje com a evolução do seu filho(a)? Posso responder sobre autismo, TDAH, desenvolvimento neurodivergente e interpretar os dados do dashboard.',
      timestamp: new Date().toISOString(),
    }
  ])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [mcpConnected, setMcpConnected] = useState(false)
  const messagesEndRef = useRef(null)
  const inputRef = useRef(null)

  // Configurações MCP via environment variables
  const mcpConfig = {
    endpoint: process.env.REACT_APP_MCP_ENDPOINT,
    apiKey: process.env.REACT_APP_MCP_API_KEY,
    enabled: process.env.REACT_APP_MCP_ENABLED === 'true'
  }

  // Configurações Multi-MCP para profissionais
  const multiMcpConfig = {
    psicopedagogico: {
      endpoint: process.env.REACT_APP_MCP_PSICOPEDAGOGICO_ENDPOINT,
      enabled: process.env.REACT_APP_MCP_PSICOPEDAGOGICO_ENABLED === 'true',
      name: 'Psicopedagógico',
      icon: '🧠',
      description: 'Estratégias para TEA/TDAH'
    },
    terapeutico: {
      endpoint: process.env.REACT_APP_MCP_TERAPEUTICO_ENDPOINT,
      enabled: process.env.REACT_APP_MCP_TERAPEUTICO_ENABLED === 'true',
      name: 'Terapêutico',
      icon: '🏥',
      description: 'Planos de intervenção'
    },
    educacional: {
      endpoint: process.env.REACT_APP_MCP_EDUCACIONAL_ENDPOINT,
      enabled: process.env.REACT_APP_MCP_EDUCACIONAL_ENABLED === 'true',
      name: 'Educacional',
      icon: '📚',
      description: 'Adaptações curriculares'
    },
    familiar: {
      endpoint: process.env.REACT_APP_MCP_FAMILIAR_ENDPOINT,
      enabled: process.env.REACT_APP_MCP_FAMILIAR_ENABLED === 'true',
      name: 'Familiar',
      icon: '👨‍👩‍👧‍👦',
      description: 'Orientações para pais'
    }
  }

  const [selectedMcp, setSelectedMcp] = useState('geral')
  const [chatHistory, setChatHistory] = useState({})
  const [isExpanded, setIsExpanded] = useState(false)

  // Auto-scroll para última mensagem
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Focus no input quando o chat abre
  useEffect(() => {
    if (isVisible) {
      inputRef.current?.focus()
    }
  }, [isVisible])

  // Inicializar mensagem de boas-vindas
  useEffect(() => {
    if (isVisible && !chatHistory[selectedMcp]) {
      handleMcpChange(selectedMcp)
    }
  }, [isVisible])

  // Atualizar mensagens quando trocar de MCP
  useEffect(() => {
    const history = chatHistory[selectedMcp] || []
    setMessages(history)
  }, [selectedMcp, chatHistory])

  // Verificar conexão MCP
  useEffect(() => {
    const checkMcpConnection = async () => {
      if (mcpConfig.enabled && mcpConfig.endpoint) {
        try {
          // Aqui será feita a verificação real da conexão MCP/N8n
          // const response = await fetch(mcpConfig.endpoint + '/health')
          // setMcpConnected(response.ok)
          setMcpConnected(true) // Simulação por enquanto
        } catch (error) {
          console.error('Erro na conexão MCP:', error)
          setMcpConnected(false)
        }
      } else {
        setMcpConnected(false)
      }
    }
    
    if (isVisible) {
      setTimeout(checkMcpConnection, 1000)
    }
  }, [isVisible, mcpConfig])

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return

    const userMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date().toISOString(),
      mcpType: selectedMcp
    }

    // Salvar no histórico do MCP selecionado
    const currentHistory = chatHistory[selectedMcp] || []
    const newHistory = [...currentHistory, userMessage]
    setChatHistory(prev => ({ ...prev, [selectedMcp]: newHistory }))
    setMessages(newHistory)
    setInputMessage('')
    setIsLoading(true)

    try {
      let aiResponse
      let selectedEndpoint = mcpConfig.endpoint

      // Selecionar endpoint baseado no MCP escolhido
      if (selectedMcp !== 'geral' && multiMcpConfig[selectedMcp]?.enabled) {
        selectedEndpoint = multiMcpConfig[selectedMcp].endpoint
      }

      // Usar MCP real se disponível
      if (mcpConnected && selectedEndpoint) {
        try {
          const response = await fetch(selectedEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              ...(mcpConfig.apiKey && { 'Authorization': `Bearer ${mcpConfig.apiKey}` })
            },
            body: JSON.stringify({
              message: userMessage.content,
              mcpType: selectedMcp,
              context: {
                dashboardData,
                userId: 'current-user',
                timestamp: userMessage.timestamp,
                previousMessages: currentHistory.slice(-5) // Últimas 5 mensagens para contexto
              }
            })
          })

          if (response.ok) {
            const data = await response.json()
            aiResponse = data.response || data.message || 'Resposta recebida do MCP'
          } else {
            throw new Error('Erro na resposta do MCP')
          }
        } catch (mcpError) {
          console.error('Erro MCP, usando fallback:', mcpError)
          aiResponse = await generateContextualResponse(userMessage.content, selectedMcp, dashboardData)
        }
      } else {
        // Usar simulação local contextualizada
        aiResponse = await generateContextualResponse(userMessage.content, selectedMcp, dashboardData)
      }
      
      setTimeout(() => {
        const aiMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: aiResponse,
          timestamp: new Date().toISOString(),
          mcpType: selectedMcp,
          // Marcar mensagem como usando AIBrain se a resposta contiver indicação disso
          usedAIBrain: !!dashboardData?.aiBrain && (
            aiResponse.includes('[via AIBrain]') || 
            aiResponse.includes('AIBrain') || 
            aiResponse.includes('análise avançada')
          )
        }
        
        // Limpar o marcador [via AIBrain] da mensagem final
        if (aiMessage.usedAIBrain && aiMessage.content.includes('[via AIBrain]')) {
          aiMessage.content = aiMessage.content.replace('[via AIBrain]', '');
        }
        
        const updatedHistory = [...newHistory, aiMessage]
        setChatHistory(prev => ({ ...prev, [selectedMcp]: updatedHistory }))
        setMessages(updatedHistory)
        setIsLoading(false)
      }, mcpConnected ? 800 : 1500)
    } catch (error) {
      console.error('Erro ao gerar resposta da IA:', error)
      const errorMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: 'Desculpe, ocorreu um erro. Por favor, tente novamente.',
        timestamp: new Date().toISOString(),
      }
      setMessages(prev => [...prev, errorMessage])
      setIsLoading(false)
    }
  }

  // Função para verificar se temos acesso ao AIBrain
  const hasAIBrainAccess = (data) => {
    return data && data.aiBrain && typeof data.aiBrain === 'object';
  }
  
  // Função para gerar respostas contextualizadas por tipo de MCP
  const generateContextualResponse = async (message, mcpType, data) => {
    // Verificar se temos acesso à instância do AIBrain
    const aiBrain = data?.aiBrain;
    const multisensoryData = data?.multisensoryAnalysis;
    const gameMetricsData = data?.gameMetricsAnalysis;
    const adaptationData = data?.adaptationReport;
    
    // Log para debug
    console.log('💬 Gerando resposta contextualizada com:', {
      hasAIBrain: !!aiBrain,
      hasMultisensoryData: !!multisensoryData,
      hasGameMetricsData: !!gameMetricsData,
      hasAdaptationData: !!adaptationData,
      mcpType
    });
    
    const responses = {
      geral: {
        greeting: `Olá! Sou a assistente IA da IE Brand${aiBrain ? ' potencializada pelo AIBrain' : ''}. Como posso ajudar com questões sobre desenvolvimento, TEA, TDAH ou neurodivergência?`,
        responses: [
          'Com base nos dados do dashboard, vejo progressos interessantes. Como posso ajudar a interpretá-los?',
          'Analisando o histórico de atividades, posso sugerir algumas estratégias personalizadas.',
          'Os dados mostram padrões únicos de desenvolvimento. Vamos explorar juntos?'
        ]
      },
      psicopedagogico: {
        greeting: `Olá! Sou especialista em estratégias psicopedagógicas para TEA/TDAH. Como posso ajudar hoje?`,
        responses: [
          'Baseado nos padrões de aprendizagem observados, sugiro focar em estratégias multissensoriais.',
          'Os dados indicam força em processamento visual. Podemos aproveitar isso para fortalecer outras áreas.',
          'Vejo oportunidades para implementar técnicas de andaimento cognitivo específicas.',
          'As métricas de atenção sugerem que estratégias de autorregulação seriam benéficas.'
        ]
      },
      terapeutico: {
        greeting: `Olá! Sou especialista em planos terapêuticos. Vamos analisar o progresso e definir intervenções?`,
        responses: [
          'Com base no perfil sensorial, recomendo intervenções de integração sensorial específicas.',
          'O progresso motor fino indica que atividades de coordenação bilateral seriam eficazes.',
          'As métricas emocionais sugerem trabalhar regulação através de técnicas de mindfulness adaptadas.',
          'Vejo necessidade de ajustes no plano terapêutico baseado nos últimos resultados.'
        ]
      },
      educacional: {
        greeting: `Olá! Sou especialista em adaptações educacionais. Como posso ajudar com o planejamento pedagógico?`,
        responses: [
          'Baseado no perfil de aprendizagem, sugiro adaptações curriculares em linguagem e matemática.',
          'Os dados indicam que metodologias visuais e estruturadas serão mais eficazes.',
          'Recomendo implementar pausas sensoriais e ambientes de baixa estimulação.',
          'As métricas sugerem que estratégias de ensino estruturado aumentarão o engajamento.'
        ]
      },
      familiar: {
        greeting: `Olá! Sou especialista em orientação familiar. Como posso ajudar pais e cuidadores hoje?`,
        responses: [
          'Com base no progresso, sugiro estratégias simples para implementar em casa.',
          'Os dados mostram que rotinas estruturadas em casa potencializarão o desenvolvimento.',
          'Recomendo atividades familiares que reforcem as habilidades trabalhadas na terapia.',
          'Vejo oportunidades para envolver toda a família no processo terapêutico.'
        ]
      }
    }

    const context = responses[mcpType] || responses.geral
    const randomResponse = context.responses[Math.floor(Math.random() * context.responses.length)]
    
    return randomResponse + ` (Modo ${mcpType === 'geral' ? 'Geral' : multiMcpConfig[mcpType]?.name || 'Simulação'})`
  }

  // Função para trocar de MCP e carregar histórico
  const handleMcpChange = (newMcp) => {
    setSelectedMcp(newMcp)
    const history = chatHistory[newMcp] || []
    
    if (history.length === 0) {
      // Mensagem inicial para o MCP selecionado
      const greeting = generateContextualResponse('', newMcp, dashboardData)
      greeting.then(greetingText => {
        const initialMessage = {
          id: '1',
          type: 'ai',
          content: greetingText,
          timestamp: new Date().toISOString(),
          mcpType: newMcp
        }
        setChatHistory(prev => ({ ...prev, [newMcp]: [initialMessage] }))
        setMessages([initialMessage])
      })
    } else {
      setMessages(history)
    }
  }

  // Gerar resposta IA baseada nos dados do dashboard e do AIBrain
  const generateAIResponse = async (question, data) => {
    const lowerQuestion = question.toLowerCase()
    const aiBrain = data?.aiBrain;
    const multisensoryData = data?.multisensoryAnalysis;
    const gameMetricsData = data?.gameMetricsAnalysis;
    const adaptationData = data?.adaptationReport;
    const hasAdvancedData = !!multisensoryData || !!gameMetricsData || !!adaptationData;
    
    // Tentar usar AIBrain diretamente para gerar resposta personalizada se disponível
    if (aiBrain && typeof aiBrain.generateChatResponse === 'function') {
      try {
        console.log('🧠 Tentando usar AIBrain.generateChatResponse diretamente');
        const response = await aiBrain.generateChatResponse(question, data);
        if (response) {
          return response + " [via AIBrain]";
        }
      } catch (err) {
        console.error('❌ Erro ao usar AIBrain.generateChatResponse:', err);
        // Continuar com fallback
      }
    }
    
    // Respostas baseadas nos dados do dashboard com análise avançada do AIBrain
    if (lowerQuestion.includes('progresso') || lowerQuestion.includes('evolução')) {
      const avgAccuracy = data?.avgAccuracy || 0;
      
      if (gameMetricsData && gameMetricsData.trends) {
        // Usar análise avançada dos padrões de progresso
        const trends = gameMetricsData.trends;
        const recentTrend = trends.recent || 'estável';
        const improvementRate = trends.improvementRate || 0;
        
        return `Com base na análise avançada do AIBrain, observo que a precisão média está em ${Math.round(avgAccuracy)}% com tendência ${recentTrend} (taxa de melhoria: ${improvementRate}%). ${
          recentTrend === 'crescente' 
            ? 'O progresso está acelerando de forma muito positiva! As estratégias atuais estão funcionando bem.' 
            : recentTrend === 'decrescente'
              ? 'Identifico algumas dificuldades recentes. Podemos ajustar as atividades para retomar o progresso.'
              : 'O progresso está constante. Podemos introduzir novos desafios para estimular o desenvolvimento.'
        }`;
      }
      
      // Fallback para análise básica
      return `Com base nos dados atuais, observo que a precisão média está em ${Math.round(avgAccuracy)}%. ${
        avgAccuracy > 70 
          ? 'Isso indica um progresso muito positivo! Continue com as atividades atuais.' 
          : 'Há espaço para melhoria. Recomendo focar em atividades básicas para fortalecer os fundamentos.'
      }`;
    }
    
    if (lowerQuestion.includes('autismo') || lowerQuestion.includes('tea')) {
      // Verificar se temos dados avançados do AIBrain sobre perfil sensorial
      if (multisensoryData && multisensoryData.sensoryProfile) {
        const profile = multisensoryData.sensoryProfile;
        return `Baseado na análise multissensorial do AIBrain, identifiquei um perfil TEA com ${
          profile.description || 'características específicas'
        }. Os padrões sensoriais indicam ${
          profile.sensitivities ? `sensibilidade em ${profile.sensitivities.join(', ')}` : 'padrões diversos'
        }. Posso sugerir estratégias específicas para comunicação, socialização ou autorregulação com base nesses dados. Em qual área você gostaria de focar?`;
      }
      
      // Fallback para resposta básica
      return 'O Transtorno do Espectro Autista (TEA) apresenta diferentes características. Baseado nos dados coletados, posso identificar padrões de comportamento e sugerir estratégias específicas. Gostaria de saber sobre algum aspecto específico como comunicação, socialização ou comportamentos repetitivos?';
    }
    
    if (lowerQuestion.includes('tdah') || lowerQuestion.includes('atenção')) {
      // Verificar se temos dados de atenção do AIBrain
      if (gameMetricsData && gameMetricsData.attentionMetrics) {
        const attention = gameMetricsData.attentionMetrics;
        const sustainedAttention = attention.sustained || 'moderada';
        const distractions = attention.distractions || 'moderadas';
        
        return `Com base na análise avançada do AIBrain, o perfil de atenção mostra capacidade de atenção sustentada ${sustainedAttention} com distrações ${distractions}. ${
          sustainedAttention === 'alta'
            ? 'Isso é um ponto forte que podemos aproveitar nas atividades!'
            : 'Podemos trabalhar com estratégias específicas para fortalecer essa habilidade.'
        } Posso sugerir técnicas como ${attention.recommendedTechniques?.join(', ') || 'temporizadores visuais, pausas estruturadas e feedback imediato'} para melhorar o foco e a organização. Que aspecto específico do TDAH você gostaria de trabalhar?`;
      }
      
      // Fallback para resposta básica
      return 'O TDAH afeta a atenção, hiperatividade e impulsividade. Nas atividades realizadas, monitoro o tempo de resposta e padrões de concentração. Posso sugerir estratégias para melhorar o foco e a organização. Que aspecto específico te preocupa mais?';
    }
    
    if (lowerQuestion.includes('recomendação') || lowerQuestion.includes('sugestão')) {
      const totalSessions = data?.totalSessions || 0;
      
      // Verificar se temos recomendações do AIBrain
      if (adaptationData && adaptationData.recommendations) {
        const recommendations = adaptationData.recommendations;
        const personalizedRecs = recommendations.personalized || [];
        const priorityRecs = recommendations.priority || [];
        
        const formattedRecs = [...(priorityRecs.length ? priorityRecs : personalizedRecs)]
          .slice(0, 3)
          .map((rec, i) => `${i+1}) ${rec}`)
          .join(', ');
          
        return `Com base na análise avançada do AIBrain e em ${totalSessions} sessões realizadas, recomendo: ${formattedRecs}. ${
          recommendations.rationale 
            ? `\nEstas recomendações são baseadas em ${recommendations.rationale}.` 
            : ''
        } Gostaria de orientações mais específicas sobre alguma dessas recomendações?`;
      }
      
      // Fallback para recomendações básicas
      return `Com base em ${totalSessions} sessões realizadas, recomendo: 1) Manter consistência nas atividades diárias, 2) Variar os tipos de jogos para estimular diferentes áreas cognitivas, 3) Celebrar pequenas conquistas para manter a motivação. Gostaria de orientações mais específicas sobre alguma área?`;
    }
    
    if (lowerQuestion.includes('dificuldade') || lowerQuestion.includes('desafio')) {
      // Verificar se temos dados do AIBrain sobre dificuldades
      if (gameMetricsData && gameMetricsData.challenges) {
        const challenges = gameMetricsData.challenges;
        const mainChallenge = challenges.main || 'não especificado';
        const strategies = challenges.strategies || [];
        
        return `Baseado na análise do AIBrain, o principal desafio identificado está na área de "${mainChallenge}". ${
          strategies.length 
            ? `Posso sugerir estratégias específicas como: ${strategies.slice(0, 2).join(' e ')}. ` 
            : ''
        }Ajustar a dificuldade é fundamental para manter o engajamento sem causar frustração. Em qual atividade específica você está encontrando dificuldades?`;
      }
      
      // Fallback para resposta básica
      return 'Ajustar a dificuldade é fundamental para manter o engajamento sem causar frustração. Baseado no desempenho atual, posso sugerir atividades mais desafiadoras ou estratégias para superar obstáculos específicos. Qual área está sendo mais desafiadora?';
    }
    
    // Verificar outros tópicos específicos
    if (lowerQuestion.includes('força') || lowerQuestion.includes('ponto forte') || lowerQuestion.includes('habilidade')) {
      if (gameMetricsData && gameMetricsData.strengths) {
        const strengths = gameMetricsData.strengths;
        const topStrengths = strengths.top || [];
        
        return `De acordo com a análise do AIBrain, as principais forças identificadas são: ${
          topStrengths.length 
            ? topStrengths.slice(0, 3).join(', ') 
            : 'ainda em avaliação'
        }. ${strengths.recommendations || 'Recomendo continuar desenvolvendo essas áreas enquanto trabalhamos nas oportunidades de melhoria.'} Gostaria de saber como potencializar alguma dessas habilidades específicas?`;
      }
    }
    
    if (lowerQuestion.includes('multisensor') || lowerQuestion.includes('sensorial')) {
      if (multisensoryData) {
        const sensorySummary = multisensoryData.summary || 'perfil multissensorial em desenvolvimento';
        
        return `A análise multissensorial do AIBrain indica ${sensorySummary}. ${
          multisensoryData.recommendations 
            ? `Para otimizar o aprendizado, recomendo: ${multisensoryData.recommendations}.` 
            : 'As experiências multissensoriais podem ajudar significativamente no desenvolvimento.'
        } Gostaria de saber mais sobre como aplicar estratégias multissensoriais nas atividades diárias?`;
      }
    }
    
    // Se temos AIBrain mas nenhuma condição específica foi atendida, usar informações avançadas genéricas
    if (hasAdvancedData) {
      return `Entendi sua pergunta sobre "${question}". Com base na análise avançada do AIBrain e nos dados do dashboard, posso identificar padrões específicos de desenvolvimento e oferecer insights personalizados. ${
        gameMetricsData?.summary || multisensoryData?.summary || adaptationData?.summary || 'Os dados mostram um perfil único com oportunidades específicas de desenvolvimento.'
      } Gostaria que eu analise algum aspecto específico como padrões cognitivos, perfil sensorial ou recomendações personalizadas?`;
    }
    
    // Resposta padrão com contexto dos dados
    return `Entendi sua pergunta sobre "${question}". Com base nos dados do dashboard, posso fornecer insights personalizados sobre desenvolvimento neurodivergente. Você gostaria que eu analise algum aspecto específico dos dados coletados ou tem alguma preocupação particular sobre o desenvolvimento?`;
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  if (!isVisible) return null

  return (
    <div className={`${styles.chatContainer} ${isExpanded ? styles.expanded : ''} ${className || ''}`}>
      {/* Header do Chat */}
      <div className={styles.chatHeader}>
        <div className={styles.chatHeaderInfo}>
          <div className={styles.aiAvatar}>🤖</div>
          <div className={styles.chatHeaderText}>
            <h3 className={styles.chatTitle}>
              IE Brand AI Assistant
              {dashboardData?.aiBrain && (
                <span className={styles.aiBrainBadge} title="Potencializado pelo AIBrain">🧠</span>
              )}
            </h3>
            <div className={styles.chatStatus}>
              <span className={`${styles.statusIndicator} ${mcpConnected ? styles.connected : styles.disconnected}`}></span>
              {mcpConnected ? 'Conectado ao MCP' : 'Carregando...'}
              {dashboardData?.aiBrain && (
                <>
                  <span className={`${styles.statusIndicator} ${styles.aiBrainConnected}`}></span>
                  <span className={styles.aiBrainStatus}>AIBrain Ativo</span>
                </>
              )}
            </div>
          </div>
        </div>
        <div className={styles.headerActions}>
          <button 
            className={styles.expandButton} 
            onClick={() => setIsExpanded(!isExpanded)}
            aria-label={isExpanded ? 'Recolher chat' : 'Expandir chat'}
          >
            {isExpanded ? '🗗' : '🗖'}
          </button>
          <button className={styles.closeButton} onClick={onClose} aria-label="Fechar chat">
            ✕
          </button>
        </div>
      </div>

      {/* Seletor de MCP */}
      <div className={styles.mcpSelector}>
        <div className={styles.mcpSelectorTitle}>Especialidade:</div>
        <div className={styles.mcpTabs}>
          <button
            className={`${styles.mcpTab} ${selectedMcp === 'geral' ? styles.active : ''}`}
            onClick={() => handleMcpChange('geral')}
          >
            <span className={styles.mcpIcon}>🧠</span>
            <span className={styles.mcpName}>Geral</span>
          </button>
          {Object.entries(multiMcpConfig).map(([key, config]) => (
            config.enabled && (
              <button
                key={key}
                className={`${styles.mcpTab} ${selectedMcp === key ? styles.active : ''}`}
                onClick={() => handleMcpChange(key)}
                title={config.description}
              >
                <span className={styles.mcpIcon}>{config.icon}</span>
                <span className={styles.mcpName}>{config.name}</span>
              </button>
            )
          ))}
        </div>
      </div>

      {/* Área de Mensagens */}
      <div className={styles.messagesContainer}>
        {messages.map((message) => (
          <div 
            key={message.id} 
            className={`${styles.messageWrapper} ${styles[message.type]}`}
          >
            <div className={`${styles.messageContent} ${message.usedAIBrain ? styles.aiBrainEnhanced : ''}`}>
              {message.type === 'ai' && (
                <div className={styles.messageAvatar}>
                  {message.usedAIBrain ? '🧠' : '🤖'}
                </div>
              )}
              <div className={styles.messageText}>
                {message.usedAIBrain && dashboardData?.aiBrain && (
                  <span className={styles.aiBrainIcon} title="Resposta aprimorada pelo AIBrain">🧠</span>
                )}
                {message.content}
              </div>
              {message.type === 'user' && (
                <div className={styles.messageAvatar}>👤</div>
              )}
            </div>
            <div className={styles.messageTime}>
              {new Date(message.timestamp).toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className={`${styles.messageWrapper} ${styles.ai}`}>
            <div className={styles.messageContent}>
              <div className={styles.messageAvatar}>🤖</div>
              <div className={styles.typingIndicator}>
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Input de Mensagem */}
      <div className={styles.inputContainer}>
        <div className={styles.inputWrapper}>
          <textarea
            ref={inputRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Pergunte sobre autismo, TDAH, desenvolvimento ou os dados do dashboard..."
            className={styles.messageInput}
            rows={1}
            disabled={isLoading}
          />
          <button 
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className={styles.sendButton}
            aria-label="Enviar mensagem"
          >
            📤
          </button>
        </div>
        <div className={styles.inputHint}>
          💡 Dica: Pergunte sobre estratégias para autismo, TDAH ou análise dos dados coletados
        </div>
      </div>
    </div>
  )
}

export default AIChat
