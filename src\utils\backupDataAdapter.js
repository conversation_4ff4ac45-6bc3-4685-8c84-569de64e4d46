/**
 * @file backupDataAdapter.js
 * @description Adaptador para formatos de backup exportado - Portal Betina V3
 * @version 1.0.0
 */

/**
 * Verifica se um objeto é um backup exportado válido no formato padrão
 * @param {Object} data - O objeto para verificação
 * @returns {boolean} - Verdadeiro se for um backup exportado válido
 */
export const isValidBackupFormat = (data) => {
  // Verificar propriedades essenciais do backup
  return data && 
    data.version && 
    data.exportDate && 
    data.data && 
    typeof data.data === 'object' && 
    data.metadata && 
    typeof data.metadata === 'object';
}

/**
 * Extrai dados de jogo a partir de um backup
 * @param {Object} backupData - Os dados do backup
 * @returns {Object} - Os dados de jogo extraídos e normalizados
 */
export const extractGameDataFromBackup = (backupData) => {
  if (!isValidBackupFormat(backupData)) {
    console.error('Formato de backup inválido', backupData);
    return null;
  }

  // Extrair informações relevantes
  const { data, metadata } = backupData;
  const { userProfiles, gameProgress, gameMetrics, accessibilitySettings } = data;

  // Verificar se temos dados de progresso de jogos
  if (!gameProgress || Object.keys(gameProgress).length === 0) {
    console.warn('Backup sem dados de progresso de jogos');
  }

  // Verificar se temos métricas de jogos
  if (!gameMetrics || Object.keys(gameMetrics).length === 0) {
    console.warn('Backup sem métricas de jogos');
  }

  // Estrutura de retorno normalizada para todos os dashboards usarem
  return {
    // Dados para performance dashboard
    performance: {
      gameMetrics: gameMetrics || {},
      gameProgress: gameProgress || {},
      userProfiles: userProfiles || [],
      sessionCount: calculateSessionCount(gameProgress),
      avgAccuracy: calculateAverageAccuracy(gameProgress),
      timeSpent: calculateTotalTimeSpent(gameProgress),
      completionRate: calculateCompletionRate(gameProgress)
    },

    // Dados para dashboard neuropedagógico
    neuroPedagogical: {
      cognitiveProfile: extractCognitiveProfile(gameProgress),
      recommendedActivities: generateRecommendations(gameProgress),
      progressIndicators: calculateProgressIndicators(gameProgress),
      skillsDistribution: calculateSkillsDistribution(gameProgress)
    },

    // Dados para relatório A
    aiReport: {
      gameProgress: gameProgress || {},
      userProfiles: userProfiles || [],
      gameMetrics: gameMetrics || {},
      learningPatterns: analyzeLearningPatterns(gameProgress),
      preferredTimeframes: analyzeTimeframes(gameProgress),
      skillsGrowth: analyzeSkillsGrowth(gameProgress),
      emergingPatterns: detectEmergingPatterns(gameProgress)
    },

    // Dados para dashboard multissensorial
    multisensory: {
      visualData: extractVisualData(gameProgress),
      auditoryData: extractAuditoryData(gameProgress), 
      tactileData: extractTactileData(gameProgress),
      sensoryIntegration: calculateSensoryIntegration(gameProgress),
      crossModalTransfer: analyzeModalTransfer(gameProgress)
    },
    
    // Metadados e informações de erro
    metadata: {
      lastUpdate: new Date().toISOString(),
      source: 'backup_adapter',
      originalMetadata: metadata,
      serverError: metadata.serverError || null,
      version: backupData.version
    }
  };
}

// Funções auxiliares para calcular métricas a partir de dados de progresso

/**
 * Calcula o número total de sessões a partir de dados de progresso
 */
function calculateSessionCount(gameProgress) {
  if (!gameProgress) return 0;
  
  let sessionCount = 0;
  Object.values(gameProgress).forEach(sessions => {
    if (Array.isArray(sessions)) {
      sessionCount += sessions.length;
    }
  });
  
  return sessionCount;
}

/**
 * Calcula a precisão média a partir de dados de progresso
 */
function calculateAverageAccuracy(gameProgress) {
  if (!gameProgress) return 0;
  
  let totalAccuracy = 0;
  let sessionCount = 0;
  
  Object.values(gameProgress).forEach(sessions => {
    if (Array.isArray(sessions)) {
      sessions.forEach(session => {
        // Usar accuracy diretamente, ou calcular a partir de correctCount/moveCount
        const accuracy = session.accuracy || 
          (session.correctCount && session.moveCount ? 
            (session.correctCount / session.moveCount * 100) : null) ||
          session.score || 0;
          
        totalAccuracy += accuracy;
        sessionCount++;
      });
    }
  });
  
  return sessionCount > 0 ? Math.round(totalAccuracy / sessionCount) : 0;
}

/**
 * Calcula o tempo total gasto a partir de dados de progresso
 */
function calculateTotalTimeSpent(gameProgress) {
  if (!gameProgress) return 0;
  
  let totalTime = 0;
  
  Object.values(gameProgress).forEach(sessions => {
    if (Array.isArray(sessions)) {
      sessions.forEach(session => {
        totalTime += session.timeSpent || session.duration || 0;
      });
    }
  });
  
  return totalTime;
}

/**
 * Calcula a taxa de conclusão a partir de dados de progresso
 */
function calculateCompletionRate(gameProgress) {
  if (!gameProgress) return 0;
  
  let totalCompleted = 0;
  let sessionCount = 0;
  
  Object.values(gameProgress).forEach(sessions => {
    if (Array.isArray(sessions)) {
      sessions.forEach(session => {
        if (session.completed || session.completed === true) {
          totalCompleted++;
        }
        sessionCount++;
      });
    }
  });
  
  return sessionCount > 0 ? Math.round((totalCompleted / sessionCount) * 100) : 0;
}

/**
 * Extrai o perfil cognitivo a partir de dados de progresso
 */
function extractCognitiveProfile(gameProgress) {
  // Mapeamento de jogos para habilidades cognitivas
  const gameSkillMap = {
    'number-counting': ['matemática', 'raciocínio', 'atenção'],
    'visual-patterns': ['percepção visual', 'atenção', 'memória visual'],
    'memory-game': ['memória de trabalho', 'concentração'],
    'color-match': ['atenção visual', 'tomada de decisão', 'velocidade de processamento']
  };
  
  // Inicializar perfil cognitivo
  const profile = {
    'atenção': 0,
    'memória': 0,
    'raciocínio': 0,
    'percepção': 0,
    'velocidade': 0,
    'concentração': 0
  };
  
  if (!gameProgress) return profile;
  
  let skillCounts = {};
  
  // Calcular pontuação para cada habilidade
  Object.entries(gameProgress).forEach(([gameId, sessions]) => {
    if (Array.isArray(sessions) && sessions.length > 0) {
      // Extrair o tipo de jogo da chave
      let gameType = gameId.replace('betina_', '').replace('_history', '');
      
      // Buscar habilidades associadas a este jogo
      const skills = gameSkillMap[gameType] || ['atenção'];
      
      // Calcular pontuação média para este jogo
      const avgScore = sessions.reduce((sum, s) => sum + (s.score || s.accuracy || 0), 0) / sessions.length;
      
      // Atribuir pontuação a cada habilidade associada ao jogo
      skills.forEach(skill => {
        const mainSkill = mapSkillToMainCategory(skill);
        if (profile[mainSkill] !== undefined) {
          if (!skillCounts[mainSkill]) skillCounts[mainSkill] = 0;
          profile[mainSkill] += avgScore;
          skillCounts[mainSkill]++;
        }
      });
    }
  });
  
  // Calcular média para cada habilidade
  Object.keys(profile).forEach(skill => {
    if (skillCounts[skill] && skillCounts[skill] > 0) {
      profile[skill] = Math.round(profile[skill] / skillCounts[skill]);
    }
  });
  
  return profile;
}

/**
 * Mapeia subcategorias de habilidades para categorias principais
 */
function mapSkillToMainCategory(skill) {
  const skillMap = {
    'atenção visual': 'atenção',
    'atenção auditiva': 'atenção',
    'memória visual': 'memória',
    'memória auditiva': 'memória',
    'memória de trabalho': 'memória',
    'raciocínio lógico': 'raciocínio',
    'raciocínio espacial': 'raciocínio',
    'percepção visual': 'percepção',
    'percepção auditiva': 'percepção',
    'velocidade de processamento': 'velocidade',
    'tomada de decisão': 'velocidade',
    'concentração': 'concentração',
    'foco': 'concentração',
    'matemática': 'raciocínio'
  };
  
  return skillMap[skill.toLowerCase()] || skill.toLowerCase();
}

/**
 * Gera recomendações baseadas em dados de progresso
 */
function generateRecommendations(gameProgress) {
  if (!gameProgress) return [];
  
  const recommendations = [];
  const profile = extractCognitiveProfile(gameProgress);
  
  // Identificar pontos fracos (habilidades com pontuação abaixo de 70)
  const weakPoints = Object.entries(profile)
    .filter(([_, score]) => score < 70 && score > 0)
    .map(([skill, _]) => skill);
  
  // Gerar recomendações baseadas em pontos fracos
  weakPoints.forEach(skill => {
    switch(skill) {
      case 'atenção':
        recommendations.push('Recomendado: Atividades focadas em atenção visual e sustentada');
        break;
      case 'memória':
        recommendations.push('Recomendado: Exercícios de memória de trabalho');
        break;
      case 'raciocínio':
        recommendations.push('Recomendado: Atividades de raciocínio lógico e sequencial');
        break;
      case 'percepção':
        recommendations.push('Recomendado: Exercícios de discriminação visual e espacial');
        break;
      case 'velocidade':
        recommendations.push('Recomendado: Atividades para melhorar tempo de resposta');
        break;
      case 'concentração':
        recommendations.push('Recomendado: Exercícios para desenvolvimento do foco atencional');
        break;
    }
  });
  
  // Garantir pelo menos uma recomendação
  if (recommendations.length === 0) {
    recommendations.push('Continue com a rotina atual de atividades');
  }
  
  return recommendations;
}

/**
 * Calcula indicadores de progresso a partir de dados de progresso
 */
function calculateProgressIndicators(gameProgress) {
  if (!gameProgress) return {};
  
  const indicators = {};
  
  // Para cada tipo de jogo, calcular tendência de melhoria
  Object.entries(gameProgress).forEach(([gameId, sessions]) => {
    if (Array.isArray(sessions) && sessions.length >= 2) {
      // Ordenar sessões por data
      const sortedSessions = [...sessions].sort((a, b) => 
        new Date(a.timestamp) - new Date(b.timestamp)
      );
      
      // Comparar primeiras e últimas sessões
      const firstHalf = sortedSessions.slice(0, Math.floor(sortedSessions.length / 2));
      const secondHalf = sortedSessions.slice(Math.floor(sortedSessions.length / 2));
      
      const firstAvg = firstHalf.reduce((sum, s) => sum + (s.score || s.accuracy || 0), 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((sum, s) => sum + (s.score || s.accuracy || 0), 0) / secondHalf.length;
      
      // Calcular porcentagem de melhoria
      const improvement = secondAvg > 0 ? 
        Math.round((secondAvg - firstAvg) / firstAvg * 100) : 0;
      
      // Extrair nome legível do jogo
      const gameName = gameId.replace('betina_', '')
        .replace('_history', '')
        .replace(/-/g, ' ')
        .replace(/(^|\s)\S/g, l => l.toUpperCase());
      
      indicators[gameName] = {
        sessions: sessions.length,
        improvement: improvement,
        trend: improvement > 10 ? 'crescimento' : 
               improvement < -10 ? 'declínio' : 'estável',
        lastScore: sortedSessions[sortedSessions.length - 1].score || 
                  sortedSessions[sortedSessions.length - 1].accuracy || 0
      };
    }
  });
  
  return indicators;
}

/**
 * Calcula distribuição de habilidades a partir de dados de progresso
 */
function calculateSkillsDistribution(gameProgress) {
  if (!gameProgress) return {};
  
  // Usar o perfil cognitivo para distribuição de habilidades
  const profile = extractCognitiveProfile(gameProgress);
  
  // Garantir que todas as habilidades tenham pelo menos valor 1 para visualização
  Object.keys(profile).forEach(skill => {
    profile[skill] = Math.max(1, profile[skill]);
  });
  
  return profile;
}

/**
 * Analisa padrões de aprendizado a partir de dados de progresso
 */
function analyzeLearningPatterns(gameProgress) {
  if (!gameProgress) return {};
  
  // Inicializar contadores
  const patterns = {
    consistencyScore: 0,
    preferredGameType: '',
    learningCurve: 'estável',
    bestPerformanceMetric: '',
    challengeAreas: []
  };
  
  // Analisar sessões por jogo para identificar padrões
  const gameStats = {};
  let totalSessions = 0;
  
  Object.entries(gameProgress).forEach(([gameId, sessions]) => {
    if (Array.isArray(sessions) && sessions.length > 0) {
      const gameName = gameId.replace('betina_', '')
        .replace('_history', '')
        .replace(/-/g, ' ')
        .replace(/(^|\s)\S/g, l => l.toUpperCase());
        
      gameStats[gameName] = {
        count: sessions.length,
        avgScore: sessions.reduce((sum, s) => sum + (s.score || s.accuracy || 0), 0) / sessions.length
      };
      
      totalSessions += sessions.length;
    }
  });
  
  // Identificar jogo preferido (mais jogado)
  if (Object.keys(gameStats).length > 0) {
    const sortedGames = Object.entries(gameStats)
      .sort(([,a], [,b]) => b.count - a.count);
      
    patterns.preferredGameType = sortedGames[0][0];
    
    // Calcular score de consistência
    const sessionsPerGame = Object.values(gameStats).map(g => g.count);
    const avgSessionsPerGame = totalSessions / Object.keys(gameStats).length;
    const variance = sessionsPerGame.reduce((v, s) => v + Math.pow(s - avgSessionsPerGame, 2), 0) / sessionsPerGame.length;
    
    // Consistência é inversamente proporcional à variância
    patterns.consistencyScore = Math.min(100, Math.max(0, 100 - (variance / avgSessionsPerGame) * 10));
    
    // Identificar áreas de desafio (jogos com menor pontuação)
    const lowScoreGames = Object.entries(gameStats)
      .filter(([,stats]) => stats.avgScore < 70)
      .map(([game,]) => game);
      
    if (lowScoreGames.length > 0) {
      patterns.challengeAreas = lowScoreGames;
    }
    
    // Identificar melhor métrica
    const highestScore = Object.entries(gameStats)
      .sort(([,a], [,b]) => b.avgScore - a.avgScore)[0];
      
    if (highestScore) {
      patterns.bestPerformanceMetric = highestScore[0];
    }
  }
  
  return patterns;
}

/**
 * Analisa horários preferidos a partir de dados de progresso
 */
function analyzeTimeframes(gameProgress) {
  if (!gameProgress) return {};
  
  // Inicializar contadores de horários
  const hourCounts = {
    morning: 0,   // 6h-12h
    afternoon: 0, // 12h-18h
    evening: 0,   // 18h-24h
    night: 0      // 0h-6h
  };
  
  // Contar sessões por horário
  let totalSessions = 0;
  
  Object.values(gameProgress).forEach(sessions => {
    if (Array.isArray(sessions)) {
      sessions.forEach(session => {
        if (session.timestamp) {
          const hour = new Date(session.timestamp).getHours();
          
          if (hour >= 6 && hour < 12) hourCounts.morning++;
          else if (hour >= 12 && hour < 18) hourCounts.afternoon++;
          else if (hour >= 18) hourCounts.evening++;
          else hourCounts.night++;
          
          totalSessions++;
        }
      });
    }
  });
  
  // Calcular preferências percentuais
  const timePreferences = {};
  
  if (totalSessions > 0) {
    Object.entries(hourCounts).forEach(([timeframe, count]) => {
      timePreferences[timeframe] = Math.round((count / totalSessions) * 100);
    });
    
    // Identificar horário preferido
    const preferredTimeframe = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'afternoon';
      
    timePreferences.preferred = preferredTimeframe;
  } else {
    timePreferences.morning = 25;
    timePreferences.afternoon = 25;
    timePreferences.evening = 25;
    timePreferences.night = 25;
    timePreferences.preferred = 'afternoon';
  }
  
  return timePreferences;
}

/**
 * Analisa crescimento de habilidades a partir de dados de progresso
 */
function analyzeSkillsGrowth(gameProgress) {
  if (!gameProgress) return {};
  
  // Ordenar sessões por data para cada jogo
  const sessionsByDate = [];
  
  Object.entries(gameProgress).forEach(([gameId, sessions]) => {
    if (Array.isArray(sessions)) {
      sessions.forEach(session => {
        if (session.timestamp) {
          sessionsByDate.push({
            game: gameId.replace('betina_', '').replace('_history', ''),
            timestamp: new Date(session.timestamp),
            score: session.score || session.accuracy || 0
          });
        }
      });
    }
  });
  
  // Ordenar todas as sessões por data
  sessionsByDate.sort((a, b) => a.timestamp - b.timestamp);
  
  // Agrupar por mês
  const monthlyData = {};
  
  sessionsByDate.forEach(session => {
    const month = session.timestamp.toISOString().slice(0, 7); // YYYY-MM
    
    if (!monthlyData[month]) {
      monthlyData[month] = {
        scores: [],
        games: new Set()
      };
    }
    
    monthlyData[month].scores.push(session.score);
    monthlyData[month].games.add(session.game);
  });
  
  // Calcular médias mensais e diversidade de jogos
  const growthData = {
    labels: [],
    avgScores: [],
    diversity: [],
    trend: 'estável'
  };
  
  Object.entries(monthlyData).forEach(([month, data]) => {
    growthData.labels.push(month);
    
    const avgScore = data.scores.reduce((sum, score) => sum + score, 0) / data.scores.length;
    growthData.avgScores.push(Math.round(avgScore));
    
    growthData.diversity.push(data.games.size);
  });
  
  // Determinar tendência de crescimento
  if (growthData.avgScores.length >= 2) {
    const firstHalf = growthData.avgScores.slice(0, Math.floor(growthData.avgScores.length / 2));
    const secondHalf = growthData.avgScores.slice(Math.floor(growthData.avgScores.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;
    
    const growthRate = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    if (growthRate > 10) growthData.trend = 'crescimento';
    else if (growthRate < -10) growthData.trend = 'declínio';
    else growthData.trend = 'estável';
  }
  
  return growthData;
}

/**
 * Detecta padrões emergentes a partir de dados de progresso
 */
function detectEmergingPatterns(gameProgress) {
  // Implementação simplificada
  return {
    patterns: [],
    suggestions: [
      'Explorar novas atividades para estimular diferentes habilidades',
      'Continuar com a rotina atual para consolidar aprendizados'
    ],
    confidenceScore: 75
  };
}

/**
 * Extrai dados visuais a partir de dados de progresso
 */
function extractVisualData(gameProgress) {
  // Implementação simplificada para dados multissensoriais
  return {
    visualScore: 75,
    colorDiscrimination: 80,
    spatialPerception: 70,
    visualTracking: 75
  };
}

/**
 * Extrai dados auditivos a partir de dados de progresso
 */
function extractAuditoryData(gameProgress) {
  // Implementação simplificada para dados multissensoriais
  return {
    auditoryScore: 70,
    soundDiscrimination: 75,
    rhythmicPerception: 65,
    sequentialProcessing: 70
  };
}

/**
 * Extrai dados táteis a partir de dados de progresso
 */
function extractTactileData(gameProgress) {
  // Implementação simplificada para dados multissensoriais
  return {
    tactileScore: 65,
    pressureSensitivity: 70,
    textureDiscrimination: 65,
    fineMotor: 60
  };
}

/**
 * Calcula integração sensorial a partir de dados de progresso
 */
function calculateSensoryIntegration(gameProgress) {
  // Implementação simplificada para dados multissensoriais
  return {
    overallScore: 70,
    visualAuditory: 75,
    visualTactile: 65,
    auditoryTactile: 70
  };
}

/**
 * Analisa transferência modal a partir de dados de progresso
 */
function analyzeModalTransfer(gameProgress) {
  // Implementação simplificada para dados multissensoriais
  return {
    transferScore: 65,
    primaryModality: 'visual',
    transferEfficiency: 'moderada',
    recommendations: [
      'Exercícios de integração visual-auditiva',
      'Atividades multissensoriais variadas'
    ]
  };
}
