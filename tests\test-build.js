#!/usr/bin/env node

/**
 * 🧪 TESTE AUTOMÁTICO DE BUILD - Portal Betina V3
 * 
 * Script para executar testes automáticos e validações:
 * - Build de produção
 * - Verificação de warnings
 * - Análise de bundle size
 * - Verificação de imports quebrados
 * 
 * @version 1.0.0
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

console.log('🧪 TESTE AUTOMÁTICO DE BUILD - Portal Betina V3');
console.log('===============================================\n');

/**
 * Executar comando e capturar output
 */
function runCommand(command, description) {
  console.log(`🔄 ${description}...`);
  
  try {
    const output = execSync(command, { 
      cwd: path.join(__dirname, '..'),
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    console.log(`✅ ${description} - Sucesso\n`);
    return { success: true, output };
  } catch (error) {
    console.log(`❌ ${description} - Falhou`);
    console.log(`Erro: ${error.message}\n`);
    return { success: false, error: error.message, output: error.stdout };
  }
}

/**
 * Verificar tamanho do bundle
 */
function checkBundleSize() {
  console.log('📊 Verificando tamanho do bundle...');
  
  const distPath = path.join(__dirname, '..', 'dist');
  
  if (!fs.existsSync(distPath)) {
    console.log('❌ Pasta dist não encontrada - Build não foi executado\n');
    return false;
  }
  
  const files = fs.readdirSync(distPath, { recursive: true });
  let totalSize = 0;
  const jsFiles = [];
  const cssFiles = [];
  
  files.forEach(file => {
    const filePath = path.join(distPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isFile()) {
      totalSize += stats.size;
      
      if (file.endsWith('.js')) {
        jsFiles.push({ name: file, size: stats.size });
      } else if (file.endsWith('.css')) {
        cssFiles.push({ name: file, size: stats.size });
      }
    }
  });
  
  console.log(`📦 Tamanho total do bundle: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
  
  // Verificar arquivos JS grandes
  const largeJsFiles = jsFiles.filter(f => f.size > 800 * 1024); // > 800KB
  if (largeJsFiles.length > 0) {
    console.log('⚠️  Arquivos JS grandes detectados:');
    largeJsFiles.forEach(file => {
      console.log(`   - ${file.name}: ${(file.size / 1024).toFixed(0)} KB`);
    });
  }
  
  console.log(`✅ Build gerou ${jsFiles.length} arquivos JS e ${cssFiles.length} arquivos CSS\n`);
  return true;
}

/**
 * Verificar imports quebrados
 */
function checkBrokenImports() {
  console.log('🔍 Verificando imports quebrados...');
  
  const srcPath = path.join(__dirname, '..', 'src');
  let brokenImports = [];
  
  function checkFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      const importMatch = line.match(/import.*from\s+['"]([^'"]+)['"]/);
      if (importMatch) {
        const importPath = importMatch[1];
        
        // Verificar imports relativos
        if (importPath.startsWith('./') || importPath.startsWith('../')) {
          const resolvedPath = path.resolve(path.dirname(filePath), importPath);
          const extensions = ['', '.js', '.jsx', '.ts', '.tsx'];
          
          let exists = false;
          for (const ext of extensions) {
            if (fs.existsSync(resolvedPath + ext)) {
              exists = true;
              break;
            }
          }
          
          if (!exists) {
            brokenImports.push({
              file: path.relative(srcPath, filePath),
              line: index + 1,
              import: importPath
            });
          }
        }
      }
    });
  }
  
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isDirectory()) {
        walkDir(filePath);
      } else if (file.endsWith('.jsx') || file.endsWith('.js')) {
        checkFile(filePath);
      }
    });
  }
  
  walkDir(srcPath);
  
  if (brokenImports.length > 0) {
    console.log('❌ Imports quebrados encontrados:');
    brokenImports.forEach(broken => {
      console.log(`   ${broken.file}:${broken.line} - ${broken.import}`);
    });
    console.log();
    return false;
  } else {
    console.log('✅ Nenhum import quebrado encontrado\n');
    return true;
  }
}

/**
 * Executar todos os testes
 */
async function runAllTests() {
  const results = {
    build: false,
    bundleSize: false,
    imports: false
  };
  
  // 1. Verificar imports quebrados primeiro
  results.imports = checkBrokenImports();
  
  // 2. Tentar build de produção
  const buildResult = runCommand('npm run build', 'Build de produção');
  results.build = buildResult.success;
  
  // 3. Verificar tamanho do bundle se build foi bem-sucedido
  if (results.build) {
    results.bundleSize = checkBundleSize();
  }
  
  // 4. Relatório final
  console.log('📋 RELATÓRIO FINAL:');
  console.log('===================');
  console.log(`🔗 Imports: ${results.imports ? '✅ OK' : '❌ Problemas'}`);
  console.log(`🏗️  Build: ${results.build ? '✅ OK' : '❌ Falhou'}`);
  console.log(`📦 Bundle: ${results.bundleSize ? '✅ OK' : '❌ Problemas'}`);
  
  const allPassed = results.imports && results.build && results.bundleSize;
  
  if (allPassed) {
    console.log('\n🎉 Todos os testes passaram! Sistema está pronto para produção.');
  } else {
    console.log('\n⚠️  Alguns testes falharam. Revise os problemas acima.');
  }
  
  return allPassed;
}

// Executar testes
runAllTests().catch(console.error);
