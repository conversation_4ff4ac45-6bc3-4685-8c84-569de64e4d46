/**
 * 🎮 TESTE COMPLETO DO FLUXO END-TO-END
 * Testa: Usuário joga → <PERSON><PERSON> métricas → Salva no banco de dados
 * Para todos os 9 jogos funcionais do Portal Betina V3
 */

import { getSystemOrchestrator } from '../api/services/core/SystemOrchestrator.js';

// Mock do banco de dados para capturar as operações
class MockDatabase {
  constructor() {
    this.sessions = [];
    this.metrics = [];
    this.reports = [];
    this.operations = [];
  }

  async save(data) {
    this.operations.push({ type: 'save', data, timestamp: new Date() });
    const id = Date.now() + Math.random();
    return { id, ...data };
  }

  async find(query) {
    this.operations.push({ type: 'find', query, timestamp: new Date() });
    return [];
  }

  async update(id, data) {
    this.operations.push({ type: 'update', id, data, timestamp: new Date() });
    return { id, ...data };
  }

  async saveCompleteSession(userId, gameId, sessionData) {
    this.sessions.push({ userId, gameId, sessionData, timestamp: new Date() });
    this.operations.push({ type: 'saveCompleteSession', userId, gameId, sessionData });
    console.log(`💾 Sessão salva: ${userId} - ${gameId}`);
    return { id: Date.now(), userId, gameId, ...sessionData };
  }

  async saveMetrics(userId, gameId, metrics, analysis) {
    this.metrics.push({ userId, gameId, metrics, analysis, timestamp: new Date() });
    this.operations.push({ type: 'saveMetrics', userId, gameId, metrics, analysis });
    console.log(`📊 Métricas salvas: ${userId} - ${gameId}`);
    return { id: Date.now(), userId, gameId, metrics, analysis };
  }

  async saveSessionReport(report) {
    this.reports.push({ report, timestamp: new Date() });
    this.operations.push({ type: 'saveSessionReport', report });
    console.log(`📋 Relatório salvo: ${report.sessionId}`);
    return { id: Date.now(), ...report };
  }

  getStats() {
    return {
      totalOperations: this.operations.length,
      sessions: this.sessions.length,
      metrics: this.metrics.length,
      reports: this.reports.length,
      operationTypes: this.operations.reduce((acc, op) => {
        acc[op.type] = (acc[op.type] || 0) + 1;
        return acc;
      }, {})
    };
  }
}

// Dados de teste para cada jogo
const gameTestData = {
  ColorMatch: {
    sessionId: 'colorMatch_test_001',
    userId: 'user_colorMatch',
    gameId: 'ColorMatch',
    sessionDuration: 120000,
    difficulty: 'medium',
    totalColors: 10,
    correctMatches: 8,
    accuracy: 0.8,
    selectedItems: [
      { id: 1, color: 'red', correct: true, responseTime: 1200 },
      { id: 2, color: 'green', correct: false, responseTime: 2100 },
      { id: 3, color: 'yellow', correct: true, responseTime: 900 }
    ],
    targetColors: ['red', 'blue', 'yellow'],
    gameGrid: [
      [{ color: 'red', id: 1 }, { color: 'blue', id: 2 }],
      [{ color: 'yellow', id: 3 }, { color: 'green', id: 4 }]
    ],
    colorData: {
      interactions: [
        { correct: true, targetColor: 'red', selectedColor: 'red', responseTime: 1200 },
        { correct: false, targetColor: 'blue', selectedColor: 'green', responseTime: 2100 },
        { correct: true, targetColor: 'yellow', selectedColor: 'yellow', responseTime: 900 }
      ]
    }
  },

  ContagemNumeros: {
    sessionId: 'counting_test_001',
    userId: 'user_counting',
    gameId: 'ContagemNumeros',
    sessionDuration: 180000,
    difficulty: 'easy',
    totalNumbers: 15,
    correctCounts: 12,
    accuracy: 0.8,
    numberCounting: {
      sequences: [
        { numbers: [1, 2, 3, 4, 5], correct: true, time: 5000 },
        { numbers: [6, 7, 8], correct: false, time: 3000 }
      ],
      totalAttempts: 15,
      correctAttempts: 12
    }
  },

  ImageAssociation: {
    sessionId: 'imageAssoc_test_001',
    userId: 'user_imageAssoc',
    gameId: 'ImageAssociation',
    sessionDuration: 150000,
    difficulty: 'medium',
    totalAssociations: 20,
    correctAssociations: 16,
    accuracy: 0.8,
    imageAssociations: {
      pairs: [
        { image1: 'cat', image2: 'animal', correct: true, responseTime: 2000 },
        { image1: 'car', image2: 'vehicle', correct: true, responseTime: 1800 }
      ]
    }
  },

  MemoryGame: {
    sessionId: 'memory_test_001',
    userId: 'user_memory',
    gameId: 'MemoryGame',
    sessionDuration: 200000,
    difficulty: 'hard',
    totalPairs: 12,
    correctPairs: 9,
    accuracy: 0.75,
    memorySequences: [
      { sequence: ['A', 'B', 'C'], userResponse: ['A', 'B', 'C'], correct: true },
      { sequence: ['X', 'Y'], userResponse: ['X', 'Z'], correct: false }
    ]
  },

  MusicalSequence: {
    sessionId: 'music_test_001',
    userId: 'user_music',
    gameId: 'MusicalSequence',
    sessionDuration: 160000,
    difficulty: 'medium',
    totalSequences: 8,
    correctSequences: 6,
    accuracy: 0.75,
    musicalData: {
      sequences: [
        { notes: ['C', 'D', 'E'], correct: true, tempo: 120 },
        { notes: ['F', 'G'], correct: false, tempo: 100 }
      ]
    }
  },

  PadroesVisuais: {
    sessionId: 'patterns_test_001',
    userId: 'user_patterns',
    gameId: 'PadroesVisuais',
    sessionDuration: 140000,
    difficulty: 'medium',
    totalPatterns: 10,
    correctPatterns: 7,
    accuracy: 0.7,
    visualPatterns: {
      patterns: [
        { type: 'geometric', completed: true, time: 15000 },
        { type: 'color', completed: false, time: 20000 }
      ]
    }
  },

  QuebraCabeca: {
    sessionId: 'puzzle_test_001',
    userId: 'user_puzzle',
    gameId: 'QuebraCabeca',
    sessionDuration: 300000,
    difficulty: 'hard',
    totalPieces: 24,
    correctPlacements: 20,
    accuracy: 0.83,
    puzzleData: {
      pieces: [
        { id: 1, position: { x: 0, y: 0 }, correct: true },
        { id: 2, position: { x: 1, y: 0 }, correct: false }
      ]
    }
  },

  CreativePainting: {
    sessionId: 'painting_test_001',
    userId: 'user_painting',
    gameId: 'CreativePainting',
    sessionDuration: 250000,
    difficulty: 'free',
    totalStrokes: 150,
    completionRate: 0.9,
    accuracy: 0.85,
    paintingData: {
      strokes: [
        { color: 'red', pressure: 0.8, duration: 1000 },
        { color: 'blue', pressure: 0.6, duration: 800 }
      ],
      canvas: { width: 800, height: 600 }
    }
  },

  LetterRecognition: {
    sessionId: 'letters_test_001',
    userId: 'user_letters',
    gameId: 'LetterRecognition',
    sessionDuration: 180000,
    difficulty: 'medium',
    totalLetters: 26,
    correctRecognitions: 22,
    accuracy: 0.85,
    letterData: {
      recognitions: [
        { letter: 'A', recognized: 'A', correct: true, time: 1500 },
        { letter: 'B', recognized: 'D', correct: false, time: 2200 }
      ]
    }
  }
};

/**
 * Testa o fluxo completo para todos os jogos
 */
async function testCompleteFlowAllGames() {
  console.log('🎮 INICIANDO TESTE COMPLETO DO FLUXO END-TO-END');
  console.log('='.repeat(60));

  const mockDb = new MockDatabase();
  const systemOrchestrator = await getSystemOrchestrator(mockDb);

  const results = {
    totalGames: 0,
    successfulGames: 0,
    failedGames: 0,
    gameResults: {},
    databaseStats: null
  };

  const games = Object.keys(gameTestData);
  console.log(`\n🎯 Testando ${games.length} jogos funcionais...\n`);

  for (const gameId of games) {
    results.totalGames++;
    console.log(`\n${'='.repeat(50)}`);
    console.log(`🎮 TESTANDO: ${gameId}`);
    console.log(`${'='.repeat(50)}`);

    try {
      const gameData = gameTestData[gameId];
      
      console.log(`1️⃣ Processando métricas do jogo...`);
      const metricsResult = await systemOrchestrator.processGameMetrics(
        gameData.userId,
        gameId,
        gameData
      );

      console.log(`2️⃣ Verificando resultado das métricas...`);
      // ✅ CORRIGIDO: Verificar estrutura correta do resultado
      const metricsSuccess = !!(metricsResult && metricsResult.success && metricsResult.therapeuticMetrics);

      console.log(`   📊 Resultado das métricas:`, {
        success: metricsResult?.success,
        hasTherapeuticMetrics: !!metricsResult?.therapeuticMetrics,
        hasTherapeuticAnalysis: !!metricsResult?.therapeuticAnalysis,
        hasProcessedData: !!metricsResult?.processedData
      });

      console.log(`3️⃣ Verificando salvamento no banco...`);
      const dbStats = mockDb.getStats();
      const dbSuccess = dbStats.totalOperations > 0;

      const gameSuccess = metricsSuccess && dbSuccess;
      
      results.gameResults[gameId] = {
        success: gameSuccess,
        metricsGenerated: metricsSuccess,
        databaseSaved: dbSuccess,
        error: null
      };

      if (gameSuccess) {
        results.successfulGames++;
        console.log(`✅ ${gameId}: SUCESSO COMPLETO`);
      } else {
        results.failedGames++;
        console.log(`❌ ${gameId}: FALHOU`);
      }

    } catch (error) {
      results.failedGames++;
      results.gameResults[gameId] = {
        success: false,
        metricsGenerated: false,
        databaseSaved: false,
        error: error.message
      };
      console.log(`❌ ${gameId}: ERRO - ${error.message}`);
    }
  }

  results.databaseStats = mockDb.getStats();

  return results;
}

// Executar teste se chamado diretamente
if (typeof window === 'undefined') {
  const isMainModule = process.argv[1] && process.argv[1].includes('test-complete-flow-all-games.js');
  
  if (isMainModule) {
    testCompleteFlowAllGames()
      .then(results => {
        console.log('\n' + '='.repeat(60));
        console.log('📊 RELATÓRIO FINAL DO TESTE COMPLETO');
        console.log('='.repeat(60));
        
        console.log(`\n🎮 JOGOS TESTADOS: ${results.totalGames}`);
        console.log(`✅ SUCESSOS: ${results.successfulGames}`);
        console.log(`❌ FALHAS: ${results.failedGames}`);
        console.log(`📈 TAXA DE SUCESSO: ${((results.successfulGames / results.totalGames) * 100).toFixed(1)}%`);
        
        console.log('\n📋 DETALHES POR JOGO:');
        Object.entries(results.gameResults).forEach(([game, result]) => {
          const status = result.success ? '✅' : '❌';
          console.log(`${status} ${game}: Métricas=${result.metricsGenerated ? '✅' : '❌'} | DB=${result.databaseSaved ? '✅' : '❌'}`);
          if (result.error) console.log(`   Erro: ${result.error}`);
        });
        
        console.log('\n💾 ESTATÍSTICAS DO BANCO DE DADOS:');
        const dbStats = results.databaseStats;
        console.log(`Total de operações: ${dbStats.totalOperations}`);
        console.log(`Sessões salvas: ${dbStats.sessions}`);
        console.log(`Métricas salvas: ${dbStats.metrics}`);
        console.log(`Relatórios salvos: ${dbStats.reports}`);
        
        console.log('\n🔍 TIPOS DE OPERAÇÕES:');
        Object.entries(dbStats.operationTypes).forEach(([type, count]) => {
          console.log(`  ${type}: ${count}`);
        });

        const allSuccess = results.failedGames === 0;
        console.log(`\n🎯 RESULTADO GERAL: ${allSuccess ? '✅ TODOS OS JOGOS FUNCIONANDO' : '⚠️ ALGUNS JOGOS COM PROBLEMAS'}`);
        
        process.exit(allSuccess ? 0 : 1);
      })
      .catch(error => {
        console.error('💥 ERRO CRÍTICO NO TESTE:', error);
        process.exit(1);
      });
  }
}

export { testCompleteFlowAllGames };
