/**
 * 🎵 AUDITORY PATTERN COLLECTOR V3
 * Coletor especializado para análise de padrões sonoros
 * Localização: src/games/MemoryGame/collectors/AuditoryPatternCollector.js
 */

export class AuditoryPatternCollector {
  constructor() {
    this.id = 'auditory_pattern_memory';
    this.name = 'Memória de Padrões Sonoros';
    this.version = '3.0.0';
    this.category = 'auditory_processing';
    
    this.data = {
      soundPatterns: [],
      sequenceAccuracy: [],
      responseLatency: [],
      auditoryDiscrimination: [],
      patternComplexity: [],
      frequencyProcessing: [],
      rhythmicMemory: [],
      auditoryWorkingMemory: []
    };
  }

  // Coletar dados de tentativas de padrões sonoros
  collect(data) {
    const {
      soundSequence,
      userSequence,
      responseTime,
      accuracy,
      patternLength,
      frequency,
      difficulty,
      timestamp
    } = data;

    // Análise de padrão sonoro
    const patternAnalysis = this.analyzeSoundPattern(soundSequence, userSequence);
    
    this.data.soundPatterns.push({
      original: soundSequence,
      response: userSequence,
      accuracy,
      patternLength,
      analysis: patternAnalysis,
      timestamp
    });

    // Precisão sequencial
    this.data.sequenceAccuracy.push({
      accuracy,
      patternLength,
      difficulty,
      timestamp
    });

    // Latência de resposta
    this.data.responseLatency.push({
      responseTime,
      patternLength,
      complexity: this.calculatePatternComplexity(soundSequence),
      timestamp
    });

    // Discriminação auditiva
    const discrimination = this.analyzeAuditoryDiscrimination(soundSequence, userSequence);
    this.data.auditoryDiscrimination.push(discrimination);

    // Processamento de frequência
    if (frequency) {
      this.data.frequencyProcessing.push({
        frequency,
        accuracy,
        responseTime,
        timestamp
      });
    }

    // Memória rítmica
    const rhythmicAnalysis = this.analyzeRhythmicPattern(soundSequence, userSequence);
    this.data.rhythmicMemory.push(rhythmicAnalysis);

    // Memória de trabalho auditiva
    const workingMemoryLoad = this.calculateAuditoryWorkingMemoryLoad(patternLength, accuracy);
    this.data.auditoryWorkingMemory.push({
      load: workingMemoryLoad,
      performance: accuracy,
      capacity: this.estimateAuditoryCapacity(),
      timestamp
    });
  }

  analyzeSoundPattern(original, response) {
    if (!original || !response) return { similarity: 0, errors: [] };

    const similarity = this.calculateSequenceSimilarity(original, response);
    const errors = this.identifyPatternErrors(original, response);
    const positionErrors = this.analyzePositionErrors(original, response);

    return {
      similarity,
      errors,
      positionErrors,
      totalErrors: errors.length,
      errorRate: errors.length / original.length
    };
  }

  calculateSequenceSimilarity(seq1, seq2) {
    if (!seq1 || !seq2) return 0;
    
    const maxLength = Math.max(seq1.length, seq2.length);
    let matches = 0;

    for (let i = 0; i < maxLength; i++) {
      if (seq1[i] === seq2[i]) matches++;
    }

    return matches / maxLength;
  }

  identifyPatternErrors(original, response) {
    const errors = [];
    
    for (let i = 0; i < Math.max(original.length, response.length); i++) {
      if (original[i] !== response[i]) {
        errors.push({
          position: i,
          expected: original[i],
          actual: response[i],
          type: this.categorizeAuditoryError(original[i], response[i])
        });
      }
    }

    return errors;
  }

  categorizeAuditoryError(expected, actual) {
    if (!actual) return 'omission';
    if (!expected) return 'insertion';
    
    // Categorizar por similaridade sonora
    const soundCategories = {
      low_frequency: ['bell', 'gong'],
      medium_frequency: ['chime', 'ding'],
      high_frequency: ['ping', 'pong']
    };

    for (const [category, sounds] of Object.entries(soundCategories)) {
      if (sounds.includes(expected) && sounds.includes(actual)) {
        return 'within_category_confusion';
      }
    }

    return 'cross_category_confusion';
  }

  analyzePositionErrors(original, response) {
    const positionAnalysis = {
      beginning: 0,
      middle: 0,
      end: 0
    };

    const length = original.length;
    
    for (let i = 0; i < length; i++) {
      if (original[i] !== response[i]) {
        if (i < length * 0.33) {
          positionAnalysis.beginning++;
        } else if (i > length * 0.67) {
          positionAnalysis.end++;
        } else {
          positionAnalysis.middle++;
        }
      }
    }

    return positionAnalysis;
  }

  calculatePatternComplexity(pattern) {
    if (!pattern || pattern.length === 0) return 0;

    // Fatores de complexidade
    const length = pattern.length;
    const uniqueSounds = new Set(pattern).size;
    const repetitions = this.countRepetitions(pattern);
    
    // Análise de transições
    const transitions = this.analyzeTransitions(pattern);
    
    return {
      length,
      uniqueSounds,
      repetitions,
      transitions,
      complexityScore: (length * 0.3) + (uniqueSounds * 0.4) + (transitions.difficulty * 0.3)
    };
  }

  countRepetitions(pattern) {
    const soundCounts = {};
    pattern.forEach(sound => {
      soundCounts[sound] = (soundCounts[sound] || 0) + 1;
    });
    
    return {
      totalRepeats: Object.values(soundCounts).reduce((sum, count) => sum + Math.max(0, count - 1), 0),
      maxRepeats: Math.max(...Object.values(soundCounts))
    };
  }

  analyzeTransitions(pattern) {
    let difficultTransitions = 0;
    let totalTransitions = pattern.length - 1;

    for (let i = 0; i < pattern.length - 1; i++) {
      const current = pattern[i];
      const next = pattern[i + 1];
      
      // Transições difíceis: entre categorias similares
      if (this.isSimilarSound(current, next)) {
        difficultTransitions++;
      }
    }

    return {
      total: totalTransitions,
      difficult: difficultTransitions,
      difficulty: totalTransitions > 0 ? difficultTransitions / totalTransitions : 0
    };
  }

  isSimilarSound(sound1, sound2) {
    const similarPairs = [
      ['bell', 'chime'],
      ['ding', 'ping'],
      ['gong', 'pong']
    ];

    return similarPairs.some(pair => 
      (pair.includes(sound1) && pair.includes(sound2))
    );
  }

  analyzeRhythmicPattern(original, response) {
    // Análise básica de ritmo (placeholder para implementação futura)
    return {
      rhythmAccuracy: this.calculateSequenceSimilarity(original, response),
      timingConsistency: Math.random() * 0.2 + 0.8, // Placeholder
      rhythmicMemoryScore: this.calculateSequenceSimilarity(original, response) * 100
    };
  }

  calculateAuditoryWorkingMemoryLoad(patternLength, accuracy) {
    // Carga baseada no comprimento do padrão e performance
    const baseLoad = Math.min(100, patternLength * 15);
    const performanceModifier = (1 - accuracy) * 20;
    
    return Math.min(100, baseLoad + performanceModifier);
  }

  estimateAuditoryCapacity() {
    if (this.data.sequenceAccuracy.length < 3) return 50;

    const recentAccuracy = this.data.sequenceAccuracy.slice(-5);
    const avgAccuracy = recentAccuracy.reduce((sum, item) => sum + item.accuracy, 0) / recentAccuracy.length;
    const maxPatternLength = Math.max(...recentAccuracy.map(item => item.patternLength));

    return Math.min(100, (avgAccuracy * 70) + (maxPatternLength * 5));
  }

  // Gerar relatório de análise auditiva
  generateReport() {
    const totalAttempts = this.data.soundPatterns.length;
    if (totalAttempts === 0) return null;

    const avgAccuracy = this.data.sequenceAccuracy.reduce((sum, item) => sum + item.accuracy, 0) / totalAttempts;
    const avgResponseTime = this.data.responseLatency.reduce((sum, item) => sum + item.responseTime, 0) / totalAttempts;

    // Análise de erros mais comuns
    const errorAnalysis = this.analyzeErrorPatterns();
    
    // Capacidade auditiva estimada
    const auditoryCapacity = this.estimateAuditoryCapacity();

    return {
      collectorId: this.id,
      totalAttempts,
      avgAccuracy: Math.round(avgAccuracy * 100) / 100,
      avgResponseTime: Math.round(avgResponseTime),
      auditoryCapacity,
      errorAnalysis,
      recommendations: this.generateAuditoryRecommendations(avgAccuracy, errorAnalysis),
      cognitiveInsights: {
        auditoryProcessingSpeed: this.calculateProcessingSpeed(),
        sequentialMemoryCapacity: auditoryCapacity,
        auditoryDiscrimination: this.calculateDiscriminationScore(),
        workingMemoryEfficiency: this.calculateWorkingMemoryEfficiency()
      }
    };
  }

  analyzeErrorPatterns() {
    const errorTypes = {};
    
    this.data.soundPatterns.forEach(pattern => {
      if (pattern.analysis && pattern.analysis.errors) {
        pattern.analysis.errors.forEach(error => {
          const type = error.type;
          if (!errorTypes[type]) errorTypes[type] = 0;
          errorTypes[type]++;
        });
      }
    });

    return errorTypes;
  }

  calculateProcessingSpeed() {
    if (this.data.responseLatency.length === 0) return 50;

    const avgLatency = this.data.responseLatency.reduce((sum, item) => sum + item.responseTime, 0) / this.data.responseLatency.length;
    
    // Velocidade baseada na latência (menor latência = maior velocidade)
    return Math.max(0, 100 - (avgLatency / 50));
  }

  calculateDiscriminationScore() {
    if (this.data.auditoryDiscrimination.length === 0) return 50;

    const avgSimilarity = this.data.auditoryDiscrimination.reduce((sum, item) => sum + (item.similarity || 0), 0) / this.data.auditoryDiscrimination.length;
    
    return avgSimilarity * 100;
  }

  calculateWorkingMemoryEfficiency() {
    if (this.data.auditoryWorkingMemory.length === 0) return 50;

    const recentMemory = this.data.auditoryWorkingMemory.slice(-5);
    const avgPerformance = recentMemory.reduce((sum, item) => sum + item.performance, 0) / recentMemory.length;
    
    return avgPerformance * 100;
  }

  generateAuditoryRecommendations(accuracy, errorAnalysis) {
    const recommendations = [];

    if (accuracy < 0.7) {
      recommendations.push('Praticar com padrões sonoros mais simples');
    }

    if (errorAnalysis.within_category_confusion > 2) {
      recommendations.push('Exercícios de discriminação auditiva fina');
    }

    if (errorAnalysis.omission > 3) {
      recommendations.push('Treino de atenção auditiva sustentada');
    }

    return recommendations;
  }

  reset() {
    this.data = {
      soundPatterns: [],
      sequenceAccuracy: [],
      responseLatency: [],
      auditoryDiscrimination: [],
      patternComplexity: [],
      frequencyProcessing: [],
      rhythmicMemory: [],
      auditoryWorkingMemory: []
    };
  }
}

export default AuditoryPatternCollector;
