# 🎯 Portal Betina V3 - Exemplos Práticos de Uso da Arquitetura

## 📚 Exemplos Reais de Interação entre Módulos

### **Exemplo 1: Fluxo Completo de uma Sessão de Jogo**

#### **1.1 - <PERSON><PERSON><PERSON> (SISTEMA coordena)**
```javascript
// 🔴 ARQUIVO DE SISTEMA: SystemOrchestrator.js
export class SystemOrchestrator {
  async startGameSession(userId, gameId) {
    // 1. Sistema coordena início da sessão
    this.logger.info('🎮 Iniciando sessão de jogo')
    
    // 2. Sistema pede para MÉTRICAS começarem a coletar
    const metricsService = this.therapeuticSystems.metricsService
    await metricsService.startSessionTracking(userId, gameId)
    
    // 3. Sistema pede para ANÁLISE preparar processamento
    const analysisOrchestrator = this.therapeuticSystems.advancedAnalysisOrchestrator
    await analysisOrchestrator.prepareSessionAnalysis(userId)
    
    // 4. Sistema coordena retorno
    return { sessionId: Date.now(), status: 'active' }
  }
}
```

#### **1.2 - <PERSON><PERSON> (MÉTRICAS processam)**
```javascript
// 🔵 ARQUIVO DE MÉTRICAS: MetricsService.js
class MetricsService {
  async collectGameMetrics(gameId, userActions) {
    // 1. MÉTRICAS coletam dados brutos
    const rawMetrics = {
      timestamp: Date.now(),
      gameId,
      userActions,
      responseTime: userActions.map(a => a.responseTime),
      accuracy: this.calculateAccuracy(userActions)
    }
    
    // 2. MÉTRICAS usam ALGORITMOS para processar
    const cognitiveAnalysis = await this.cognitiveEngine.analyze(rawMetrics)
    const prediction = await this.predictiveEngine.predict(rawMetrics)
    
    // 3. MÉTRICAS retornam dados processados
    return {
      rawMetrics,
      cognitiveAnalysis,
      prediction,
      insights: this.generateInsights(cognitiveAnalysis, prediction)
    }
  }
}
```

#### **1.3 - Uso de Algoritmos (PROCESSAMENTO executa)**
```javascript
// 🔵 ARQUIVO DE ALGORITMO: CognitiveAssociationEngine.js
export class CognitiveAssociationEngine {
  async analyze(userData) {
    // ALGORITMO processa dados puros
    const cognitivePatterns = this.extractCognitivePatterns(userData)
    const associationStrength = this.calculateAssociationStrength(cognitivePatterns)
    const developmentLevel = this.assessDevelopmentLevel(userData)
    
    return {
      patterns: cognitivePatterns,
      strength: associationStrength,
      level: developmentLevel,
      recommendations: this.generateRecommendations(developmentLevel)
    }
  }
}
```

---

### **Exemplo 2: Sistema de Análise Terapêutica**

#### **2.1 - Coordenação Terapêutica (SISTEMA orquestra)**
```javascript
// 🔴 ARQUIVO DE SISTEMA: SystemOrchestrator.js
async runTherapeuticAnalysis(sessionData) {
  // Sistema coordena múltiplos módulos de processamento
  
  // 1. Análise Cognitiva
  const cognitiveResults = await this.therapeuticSystems.cognitiveAnalyzer.analyze(sessionData)
  
  // 2. Análise Comportamental  
  const behavioralResults = await this.therapeuticSystems.behavioralEngagementAnalyzer.analyze(sessionData)
  
  // 3. Análise Preditiva
  const predictiveResults = await this.therapeuticSystems.predictiveAnalysisEngine.predict(sessionData)
  
  // 4. Sistema consolida tudo
  return this.consolidateTherapeuticInsights({
    cognitive: cognitiveResults,
    behavioral: behavioralResults,
    predictive: predictiveResults
  })
}
```

#### **2.2 - Processamento Especializado (ANÁLISE executa)**
```javascript
// 🔵 ARQUIVO DE ANÁLISE: CognitiveAnalyzer.js
export class CognitiveAnalyzer {
  async analyze(sessionData) {
    // ANÁLISE processa dados específicos
    const attentionLevel = this.assessAttention(sessionData.interactions)
    const memoryPerformance = this.assessMemory(sessionData.responses)
    const processingSpeed = this.assessProcessingSpeed(sessionData.timings)
    
    // ANÁLISE usa algoritmos especializados
    const cognitiveLoad = this.calculateCognitiveLoad(attentionLevel, processingSpeed)
    
    return {
      attention: attentionLevel,
      memory: memoryPerformance,
      processing: processingSpeed,
      cognitiveLoad,
      therapeuticRecommendations: this.generateTherapeuticPlan(cognitiveLoad)
    }
  }
}
```

---

### **Exemplo 3: Integração de Coletores de Jogos**

#### **3.1 - Hub de Coletores (SISTEMA organiza)**
```javascript
// 🔴 ARQUIVO DE SISTEMA: game/index.js
export const GAME_COLLECTORS_MAP = {
  'ColorMatch': {
    collectors: [VisualProcessingCollector, ColorMatchCollector],
    algorithms: ['CognitiveAssociationEngine', 'PredictiveAnalysisEngine']
  },
  'ContagemNumeros': {
    collectors: [NumericalProcessingCollector, ContagemNumerosCollector],
    algorithms: ['CognitiveAssociationEngine']
  }
}

export async function getGameCollectors(gameId) {
  // Sistema busca coletores apropriados para o jogo
  const gameConfig = GAME_COLLECTORS_MAP[gameId]
  if (!gameConfig) return []
  
  // Sistema instancia coletores
  const collectors = gameConfig.collectors.map(CollectorClass => new CollectorClass())
  
  return collectors
}
```

#### **3.2 - Coletor Específico (PROCESSAMENTO coleta)**
```javascript
// 🔵 ARQUIVO DE PROCESSAMENTO: VisualProcessingCollector.js
export class VisualProcessingCollector {
  async collect(gameSession) {
    // PROCESSAMENTO coleta dados específicos do jogo
    const visualData = {
      colorAccuracy: this.analyzeColorAccuracy(gameSession.moves),
      patternRecognition: this.analyzePatternRecognition(gameSession.sequences),
      visualAttention: this.analyzeVisualAttention(gameSession.focusPoints),
      reactionTime: this.analyzeReactionTime(gameSession.timings)
    }
    
    // PROCESSAMENTO retorna dados estruturados
    return {
      type: 'visual_processing',
      data: visualData,
      confidence: this.calculateConfidence(visualData),
      timestamp: Date.now()
    }
  }
}
```

---

### **Exemplo 4: Sistema de Métricas Avançadas**

#### **4.1 - Orquestração de Métricas (SISTEMA coordena)**
```javascript
// 🔴 ARQUIVO DE SISTEMA: SystemOrchestrator.js
async processAdvancedMetrics(rawData) {
  // Sistema coordena processamento avançado
  
  // 1. Métricas básicas
  const basicMetrics = await this.therapeuticSystems.metricsService.collectBasicMetrics(rawData)
  
  // 2. Métricas avançadas
  const advancedMetrics = await this.therapeuticSystems.advancedMetricsEngine.processAdvanced(basicMetrics)
  
  // 3. Análise preditiva
  const predictions = await this.therapeuticSystems.predictiveAnalysisEngine.predict(advancedMetrics)
  
  // Sistema coordena resultado final
  return {
    basic: basicMetrics,
    advanced: advancedMetrics,
    predictions,
    insights: this.generateSystemInsights(basicMetrics, advancedMetrics, predictions)
  }
}
```

#### **4.2 - Engine de Métricas Avançadas (PROCESSAMENTO calcula)**
```javascript
// 🔵 ARQUIVO DE ALGORITMO: AdvancedMetricsEngine.js
export class AdvancedMetricsEngine {
  async processAdvanced(basicMetrics) {
    // ALGORITMO calcula métricas complexas
    const neuroplasticityIndex = this.calculateNeuroplasticityIndex(basicMetrics)
    const cognitiveLoadIndex = this.calculateCognitiveLoadIndex(basicMetrics)
    const therapeuticProgressIndex = this.calculateTherapeuticProgress(basicMetrics)
    
    // ALGORITMO aplica modelos matemáticos
    const adaptiveRecommendations = this.generateAdaptiveRecommendations({
      neuroplasticity: neuroplasticityIndex,
      cognitiveLoad: cognitiveLoadIndex,
      progress: therapeuticProgressIndex
    })
    
    return {
      neuroplasticityIndex,
      cognitiveLoadIndex,
      therapeuticProgressIndex,
      adaptiveRecommendations,
      confidence: this.calculateMetricsConfidence()
    }
  }
}
```

---

## 🔄 **Mapa de Dependências em Ação**

### **Fluxo Real do Sistema:**

```
🎮 USUÁRIO JOGA
    ↓
🔴 SystemOrchestrator.startGameSession()
    ↓
🔵 MetricsService.collectMetrics()
    ↓
🔵 CognitiveAssociationEngine.analyze()
🔵 PredictiveAnalysisEngine.predict()
    ↓
🔴 SystemOrchestrator.processResults()
    ↓
🔵 AnalysisOrchestrator.generateInsights()
    ↓
🔴 SystemOrchestrator.storeResults()
    ↓
📊 DASHBOARD ATUALIZADO
```

### **Quem Chama Quem:**

| **Tipo** | **Arquivo** | **Chama** | **Tipo** |
|----------|-------------|-----------|----------|
| 🔴 Sistema | SystemOrchestrator | MetricsService | 🔵 Métricas |
| 🔵 Métricas | MetricsService | CognitiveAssociationEngine | 🔵 Algoritmo |
| 🔵 Métricas | MetricsService | PredictiveAnalysisEngine | 🔵 Algoritmo |
| 🔴 Sistema | SystemOrchestrator | AnalysisOrchestrator | 🔵 Análise |
| 🔵 Análise | CognitiveAnalyzer | CognitiveAssociationEngine | 🔵 Algoritmo |
| 🔴 Sistema | game/index.js | VisualProcessingCollector | 🔵 Processamento |

---

## 🎯 **Resumo dos Padrões**

### **🔴 ARQUIVOS DE SISTEMA fazem:**
- ✅ **Coordenam** fluxos entre módulos
- ✅ **Inicializam** componentes
- ✅ **Orquestram** processos complexos  
- ✅ **Gerenciam** estado do sistema
- ✅ **Consolidam** resultados finais

### **🔵 ARQUIVOS DE PROCESSAMENTO fazem:**
- ✅ **Coletam** dados específicos
- ✅ **Processam** informações
- ✅ **Executam** algoritmos
- ✅ **Calculam** métricas
- ✅ **Retornam** insights estruturados

### **⚡ INTERAÇÕES TÍPICAS:**
1. **Sistema** → chama → **Processamento**
2. **Processamento** → usa → **Algoritmo**  
3. **Algoritmo** → retorna → **Resultado**
4. **Sistema** → consolida → **Resultado Final**

Esta arquitetura garante separação clara de responsabilidades e facilita manutenção e evolução do sistema.
