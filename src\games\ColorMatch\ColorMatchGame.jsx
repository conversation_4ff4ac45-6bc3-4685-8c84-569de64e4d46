/**
 * 🎨 COLOR MATCH V3 - <PERSON>OGO DE CORES COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useCallback, useRef, useContext } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import { ColorMatchCollectorsHub } from './collectors/index.js';
import { ColorMatchConfig } from './ColorMatchConfig.js';
import { ColorMatchMetrics } from './ColorMatchMetrics.js';
import styles from './ColorMatch.module.css';

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - COLOR MATCH
const ACTIVITY_TYPES = {
  SPEED_CHALLENGE: {
    id: 'speed_challenge',
    name: 'Desafio de Velocidade',
    icon: '⚡',
    description: 'Identifique cores rapidamente',
    component: 'SpeedChallengeActivity'
  },
  COLOR_MEMORY: {
    id: 'color_memory',
    name: 'Memória de Cores',
    icon: '🧠',
    description: 'Lembre-se da sequência de cores',
    component: 'ColorMemoryActivity'
  },
  SHADE_DISCRIMINATION: {
    id: 'shade_discrimination',
    name: 'Discriminação de Tons',
    icon: '🔍',
    description: 'Diferencie tons similares',
    component: 'ShadeDiscriminationActivity'
  },
  SEQUENCE_COLORS: {
    id: 'sequence_colors',
    name: 'Sequência de Cores',
    icon: '📝',
    description: 'Complete sequências coloridas',
    component: 'SequenceColorsActivity'
  },
  GRADIENT_MATCHING: {
    id: 'gradient_matching',
    name: 'Combinação de Gradientes',
    icon: '🌈',
    description: 'Combine gradientes e transições',
    component: 'GradientMatchingActivity'
  }
};

// 🎯 FUNÇÕES DE GERAÇÃO DE ATIVIDADES INLINE
const generateFindTheColor = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
  const colorNames = ['Vermelho', 'Verde', 'Azul', 'Amarelo', 'Magenta', 'Ciano'];
  const targetIndex = Math.floor(Math.random() * colors.length);
  const targetColor = colors[targetIndex];
  const colorName = colorNames[targetIndex];
  
  // Criar opções de cores diferentes
  const wrongColors = colors.filter(color => color !== targetColor);
  const shuffledWrong = wrongColors.sort(() => Math.random() - 0.5).slice(0, 3);
  const allOptions = [targetColor, ...shuffledWrong].sort(() => Math.random() - 0.5);
  
  return {
    targetColor,
    targetColorName: colorName,
    colorName,
    correctAnswer: targetColor,
    options: allOptions,
    instruction: `Clique na cor ${colorName}`,
    type: 'color_matching'
  };
};

const generateNameTheColor = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
  const colorNames = ['Vermelho', 'Verde', 'Azul', 'Amarelo', 'Magenta', 'Ciano'];
  const targetIndex = Math.floor(Math.random() * colors.length);
  const targetColor = colors[targetIndex];
  const correctName = colorNames[targetIndex];
  
  // Criar opções diferentes para tornar única
  const wrongNames = colorNames.filter(name => name !== correctName);
  const shuffledWrong = wrongNames.sort(() => Math.random() - 0.5).slice(0, 3);
  const allOptions = [correctName, ...shuffledWrong].sort(() => Math.random() - 0.5);
  
  return {
    targetColor,
    colorName: correctName,
    correctAnswer: correctName,
    options: allOptions,
    instruction: 'Qual é o nome desta cor?',
    type: 'name_challenge'
  };
};

const generateShadeDiscrimination = (config) => {
  const baseColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#800080', '#FFA500'];
  const baseColor = baseColors[Math.floor(Math.random() * baseColors.length)];
  
  // Gerar tons similares da mesma cor
  const shades = [];
  const targetShade = baseColor;
  shades.push(targetShade);
  
  // Adicionar tons mais claros e mais escuros
  for (let i = 1; i <= 3; i++) {
    const lighterShade = adjustColorBrightness(baseColor, 0.3 * i);
    const darkerShade = adjustColorBrightness(baseColor, -0.3 * i);
    shades.push(lighterShade, darkerShade);
  }
  
  return {
    shades: shuffleArray(shades).slice(0, 6),
    target: targetShade,
    instruction: 'Encontre o tom de cor mais próximo do original',
    type: 'shade_discrimination'
  };
};

// Função auxiliar para ajustar brilho das cores
const adjustColorBrightness = (hex, factor) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  
  const adjust = (color) => Math.max(0, Math.min(255, Math.round(color * (1 + factor))));
  
  return `#${adjust(r).toString(16).padStart(2, '0')}${adjust(g).toString(16).padStart(2, '0')}${adjust(b).toString(16).padStart(2, '0')}`;
};

const generateMatchTheName = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#800080', '#FFA500'];
  const colorNames = ['Vermelho', 'Verde', 'Azul', 'Amarelo', 'Roxo', 'Laranja'];
  const targetIndex = Math.floor(Math.random() * colors.length);
  
  // Criar múltiplas cores para combinação
  const selectedColors = [];
  const selectedNames = [];
  for (let i = 0; i < 3; i++) {
    const index = (targetIndex + i) % colors.length;
    selectedColors.push(colors[index]);
    selectedNames.push(colorNames[index]);
  }
  
  return {
    colors: selectedColors,
    names: selectedNames,
    correctAnswer: selectedNames[0],
    options: selectedNames,
    instruction: 'Combine cada cor com seu nome correto',
    type: 'color_name_matching'
  };
};

const generateMemoryMatch = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
  const sequenceLength = Math.floor(Math.random() * 2) + 3;
  const sequence = [];
  
  for (let i = 0; i < sequenceLength; i++) {
    sequence.push(colors[Math.floor(Math.random() * colors.length)]);
  }
  
  // Perguntar sobre uma posição específica na sequência
  const questionIndex = Math.floor(Math.random() * sequenceLength);
  const correctColor = sequence[questionIndex];
  
  const wrongColors = colors.filter(color => color !== correctColor);
  const shuffledWrong = wrongColors.sort(() => Math.random() - 0.5).slice(0, 3);
  const allOptions = [correctColor, ...shuffledWrong].sort(() => Math.random() - 0.5);
  
  return {
    sequence,
    questionIndex,
    correctAnswer: correctColor,
    options: allOptions,
    instruction: `Qual foi a cor na posição ${questionIndex + 1}?`,
    type: 'memory_sequence'
  };
};

const generateSequenceRepeat = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00'];
  const pattern = [colors[0], colors[1], colors[0]];
  const nextColor = colors[1];
  
  return {
    pattern,
    correctAnswer: nextColor,
    options: colors,
    instruction: 'Continue o padrão de cores'
  };
};

const generateColorMixing = (config) => {
  const gradients = [
    'linear-gradient(90deg, #FF0000, #FFFF00)',
    'linear-gradient(90deg, #00FF00, #0000FF)',
    'linear-gradient(90deg, #FF00FF, #00FFFF)'
  ];
  const names = ['Vermelho para Amarelo', 'Verde para Azul', 'Magenta para Ciano'];
  const targetIndex = Math.floor(Math.random() * gradients.length);
  
  return {
    gradient: gradients[targetIndex],
    correctAnswer: names[targetIndex],
    options: names,
    instruction: 'Que transição de cores é esta?'
  };
};

// Geração de dados para reconhecimento de padrões
const generatePatternRecognitionData = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00'];
  const pattern = [colors[0], colors[1], colors[2]];
  const repeat = [colors[0], colors[1]];
  const correctNext = colors[2];
  
  return {
    pattern,
    repeat,
    correctAnswer: 'ABC',
    options: ['ABC', 'ABB', 'AAB', 'BAC'],
    instruction: 'Qual padrão se repete?'
  };
};

// Geração de dados para gradiente de cores
const generateColorGradientData = (config) => {
  const baseColors = [
    { color: '#FF0000', name: 'Vermelho' },
    { color: '#00FF00', name: 'Verde' },
    { color: '#0000FF', name: 'Azul' }
  ];
  const base = baseColors[Math.floor(Math.random() * baseColors.length)];
  
  // Gerar tons diferentes
  const colors = [
    base.color,
    base.color + '99',
    base.color + '66',
    base.color + '33'
  ];
  
  return {
    colors,
    correctAnswer: '1,2,3,4',
    options: ['1,2,3,4', '4,3,2,1', '2,1,4,3', '3,4,1,2'],
    instruction: `Ordene os tons de ${base.name} do mais escuro para o mais claro`
  };
};

// Geração de dados para sequência temporal
const generateSequenceData = (config) => {
  const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00'];
  const sequence = [colors[0], colors[1], colors[2]];
  
  return {
    sequence,
    showSequence: true,
    correctAnswer: colors[0],
    options: colors,
    instruction: 'Reproduza a sequência: clique na primeira cor mostrada'
  };
};

// 🎯 FUNÇÃO DE VERIFICAÇÃO DE RESPOSTA
const checkAnswer = (activityId, answer, activityData) => {
  return answer === activityData.correctAnswer;
};

function ColorMatchGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();
  const metricsRef = useRef(new ColorMatchMetrics());
  const sessionIdRef = useRef(uuidv4());
  const [collectorsHub] = useState(() => new ColorMatchCollectorsHub());
  
  // 🎮 HOOKS DE LÓGICA UNIFICADA - DEVE VIR PRIMEIRO PARA FORNECER sessionId
  const { startUnifiedSession, endUnifiedSession, recordInteraction, updateMetrics, sessionId, isSessionActive } = useUnifiedGameLogic('colormatch');
  
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized,
    multisensoryIntegration
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'colormatch',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  const shouldUseTherapeutic = user?.id && user.id !== 'anonymous' && user.id !== '';
  const therapeuticOrchestrator = useTherapeuticOrchestrator({
    userId: shouldUseTherapeutic ? user.id : null
  });

  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO V3
  // =====================================================
  
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('colorMatch_ttsActive');
    return saved !== null ? JSON.parse(saved) : ttsEnabled;
  });

  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('colorMatch_ttsActive', JSON.stringify(newState));
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      return newState;
    });
  }, []);

  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) return;
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // 🎯 ESTADOS PRINCIPAIS DO JOGO V3 COM SISTEMA DE ATIVIDADES
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [attemptCount, setAttemptCount] = useState(0);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    score: 0,
    round: 1,
    targetColor: null,
    selectedAnswer: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: 'easy',
    roundStartTime: null,
    
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.SPEED_CHALLENGE.id,
    activityCycle: [
      ACTIVITY_TYPES.SPEED_CHALLENGE.id,
      ACTIVITY_TYPES.COLOR_MEMORY.id,
      ACTIVITY_TYPES.SHADE_DISCRIMINATION.id,
      ACTIVITY_TYPES.SEQUENCE_COLORS.id,
      ACTIVITY_TYPES.GRADIENT_MATCHING.id
    ],
    activityIndex: 0,
    roundsPerActivity: 10,
    activityRoundCount: 1,
    minRoundsPerActivity: 4,
    maxRoundsPerActivity: 7,
    canSwitchActivity: true, // 🔥 Começar permitindo a primeira escolha livre
    isFirstActivityChoice: true, // 🔥 Flag para permitir primeira escolha
    
    // 🎯 Dados específicos de atividades
    activityData: {
      basicMatching: {
        currentQuestion: null,
        colors: [],
        options: []
      },
      speedChallenge: {
        targetColor: null,
        timeLimit: 5000,
        attempts: 0
      },
      colorMemory: {
        sequence: [],
        userSequence: [],
        showSequence: false
      },
      shadeDiscrimination: {
        baseColor: null,
        shades: [],
        target: null
      },
      sequenceColors: {
        pattern: [],
        userProgress: []
      },
      gradientMatching: {
        gradient: null,
        options: [],
        correctAnswer: null
      }
    },
    
    // 🎯 Métricas comportamentais avançadas
    behavioralMetrics: {
      activityPreferences: {},
      responsePatterns: [],
      adaptiveAdjustments: 0,
      engagementLevel: 1.0,
      difficultyProgression: [],
      errorPatterns: {},
      recoveryRate: 1.0,
      attentionSpan: 0,
      cognitiveLoad: 0.5
    },
    
    // 📊 Estatísticas detalhadas por atividade
    activityStats: {
      speed_challenge: { attempts: 0, correct: 0, averageTime: 0 },
      color_memory: { attempts: 0, correct: 0, averageTime: 0 },
      shade_discrimination: { attempts: 0, correct: 0, averageTime: 0 },
      sequence_colors: { attempts: 0, correct: 0, averageTime: 0 },
      gradient_matching: { attempts: 0, correct: 0, averageTime: 0 }
    }
  });

  // 🧠 Estados para análise cognitiva avançada
  const [gameStartTime, setGameStartTime] = useState(null);
  const [attemptStartTime, setAttemptStartTime] = useState(null);
  const [gameAttempts, setGameAttempts] = useState([]);
  const [cognitiveAnalysis, setCognitiveAnalysis] = useState(null);
  const [analysisInProgress, setAnalysisInProgress] = useState(false);

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({ recordInteraction, updateMetrics });
    }
  }, [recordInteraction, updateMetrics]);

  // 🔧 FUNÇÃO UTILITÁRIA - Embaralhar arrays
  const shuffleArray = useCallback((array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }, []);

  // 🧠 FUNÇÃO DE ANÁLISE COGNITIVA AVANÇADA
  const runCognitiveAnalysis = useCallback(async (attempts) => {
    if (analysisInProgress || attempts.length < 3) return;
    setAnalysisInProgress(true);
    try {
      const gameData = {
        sessionId: sessionId || `session_${Date.now()}`,
        attempts,
        difficulty: gameState.difficulty,
        gameMode: 'standard',
        sessionDuration: Date.now() - gameStartTime,
        totalScore: gameState.score,
        currentLevel: gameState.round
      };
      const analysis = await collectorsHub.runCompleteAnalysis(gameData);
      setCognitiveAnalysis(analysis);
      if (updateMetrics && analysis && !analysis.error) {
        await updateMetrics({
          type: 'cognitive_analysis',
          gameId: 'colormatch',
          analysis: analysis.integratedAnalysis,
          synthesisMetrics: analysis.synthesisMetrics,
          developmentProfile: analysis.developmentProfile
        });
      }
    } catch (error) {
      console.error('❌ Erro na análise cognitiva:', error);
    } finally {
      setAnalysisInProgress(false);
    }
  }, [analysisInProgress, sessionId, gameState, gameStartTime, collectorsHub, updateMetrics]);

  useEffect(() => {
    return () => {
      if (gameAttempts.length >= 3 && !analysisInProgress) {
        runCognitiveAnalysis(gameAttempts);
      }
    };
  }, [gameAttempts, analysisInProgress, runCognitiveAnalysis]);

  // 🎯 FUNÇÕES DE GERAÇÃO DE ATIVIDADES V3
  const generateActivityContent = useCallback((activityId, difficulty) => {
    const config = ColorMatchConfig.difficulties[difficulty] || ColorMatchConfig.difficulties.easy;
    switch (activityId) {
      case ACTIVITY_TYPES.SPEED_CHALLENGE.id:
        return generateNameTheColor(config);
      case ACTIVITY_TYPES.COLOR_MEMORY.id:
        return generateMemoryMatch(config);
      case ACTIVITY_TYPES.SHADE_DISCRIMINATION.id:
        const baseColors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#800080', '#FFA500'];
        const baseColor = baseColors[Math.floor(Math.random() * baseColors.length)];
        
        const shades = [];
        const targetShade = baseColor;
        shades.push(targetShade);
        
        // Criar variações mais claras e mais escuras
        for (let i = 1; i <= 5; i++) {
          const lighterShade = adjustColorBrightness(baseColor, 0.2 * i);
          const darkerShade = adjustColorBrightness(baseColor, -0.2 * i);
          shades.push(lighterShade, darkerShade);
        }
        
        // Embaralhar e pegar apenas 6 tons diferentes
        const shuffledShades = shuffleArray(shades).slice(0, 6);
        
        return {
          shades: shuffledShades,
          target: targetShade,
          correctAnswer: targetShade,
          instruction: 'Encontre o tom de cor mais próximo do original',
          type: 'shade_discrimination'
        };
      case ACTIVITY_TYPES.SEQUENCE_COLORS.id:
        const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00'];
        const patternTypes = [
          { pattern: [colors[0], colors[1], colors[2]], next: colors[0] },
          { pattern: [colors[0], colors[1]], next: colors[0] },
          { pattern: [colors[0], colors[1], colors[0]], next: colors[1] }
        ];
        const selectedPattern = patternTypes[Math.floor(Math.random() * patternTypes.length)];
        
        return {
          pattern: selectedPattern.pattern,
          correctAnswer: selectedPattern.next,
          options: colors,
          instruction: 'Continue o padrão de cores',
          type: 'sequence_colors'
        };
      case ACTIVITY_TYPES.GRADIENT_MATCHING.id:
        const gradients = [
          {
            gradient: 'linear-gradient(90deg, #FF0000, #FFFF00)',
            name: 'Vermelho para Amarelo',
            description: 'Transição quente'
          },
          {
            gradient: 'linear-gradient(90deg, #00FF00, #0000FF)',
            name: 'Verde para Azul',
            description: 'Transição fria'
          },
          {
            gradient: 'linear-gradient(90deg, #FF00FF, #00FFFF)',
            name: 'Magenta para Ciano',
            description: 'Transição complementar'
          },
          {
            gradient: 'linear-gradient(90deg, #800080, #FFA500)',
            name: 'Roxo para Laranja',
            description: 'Transição contrastante'
          }
        ];
        
        const selectedGradient = gradients[Math.floor(Math.random() * gradients.length)];
        const wrongOptions = gradients.filter(g => g.name !== selectedGradient.name).map(g => g.name);
        const shuffledWrong = wrongOptions.sort(() => Math.random() - 0.5).slice(0, 3);
        const allOptions = [selectedGradient.name, ...shuffledWrong].sort(() => Math.random() - 0.5);
        
        return {
          gradient: selectedGradient.gradient,
          correctAnswer: selectedGradient.name,
          options: allOptions,
          instruction: 'Que transição de cores é esta?',
          type: 'gradient_matching'
        };
      case 'pattern_recognition':
        return generatePatternRecognitionData(config);
      case 'color_gradient':
        return generateColorGradientData(config);
      case 'sequence':
        return generateSequenceData(config);
      default:
        return generateNameTheColor(config);
    }
  }, [shuffleArray]);

  const startGame = useCallback((difficulty) => {
    setShowStartScreen(false);
    setGameStarted(true);
    setGameStartTime(Date.now());
    
    const initialActivity = ACTIVITY_TYPES.SPEED_CHALLENGE.id;
    const activityContent = generateActivityContent(initialActivity, difficulty);
    
    setGameState(prev => ({
      ...prev,
      difficulty,
      roundStartTime: Date.now(),
      currentActivity: initialActivity,
      activityData: {
        ...prev.activityData,
        [initialActivity.replace('_', '')]: activityContent
      }
    }));
    
    metricsRef.current.startSession(sessionIdRef.current, user?.id || 'anonymous', difficulty);
    startUnifiedSession(difficulty);
    
    setTimeout(() => {
      const activityName = ACTIVITY_TYPES.SPEED_CHALLENGE.name;
      speak(`Bem-vindo ao Color Match! Vamos começar com ${activityName}. ${activityContent.instruction}`);
    }, 1000);
  }, [generateActivityContent, speak, startUnifiedSession, user?.id]);

  const handleAnswer = useCallback((answer) => {
    setGameState(prev => {
      const currentActivityData = prev.activityData[prev.currentActivity.replace('_', '')];
      const isCorrect = checkAnswer(prev.currentActivity, answer, currentActivityData);
      const responseTime = Date.now() - prev.roundStartTime;
      
      const attemptData = {
        answer,
        isCorrect,
        responseTime,
        difficulty: prev.difficulty,
        round: prev.round,
        activity: prev.currentActivity
      };
      
      setGameAttempts(prevAttempts => [...prevAttempts, attemptData]);
      
      if (metricsRef.current) {
        metricsRef.current.recordAnswer(isCorrect, responseTime, {
          correctAnswer: currentActivityData.correctAnswer,
          selectedAnswer: answer,
          activity: prev.currentActivity,
          difficulty: prev.difficulty,
          round: prev.round
        });
      }
      
      return {
        ...prev,
        selectedAnswer: answer,
        showFeedback: true,
        score: isCorrect ? prev.score + 10 : prev.score,
        activityStats: {
          ...prev.activityStats,
          [prev.currentActivity]: {
            ...prev.activityStats[prev.currentActivity],
            attempts: prev.activityStats[prev.currentActivity].attempts + 1,
            correct: isCorrect ? prev.activityStats[prev.currentActivity].correct + 1 : prev.activityStats[prev.currentActivity].correct
          }
        }
      };
    });
  }, []);

  const generateNewRound = useCallback(() => {
    setGameState(prev => {
      const newRound = prev.round + 1;
      const newActivityRoundCount = prev.activityRoundCount + 1;
      const newActivityContent = generateActivityContent(prev.currentActivity, prev.difficulty);
      
      return {
        ...prev,
        round: newRound,
        activityRoundCount: newActivityRoundCount,
        canSwitchActivity: newActivityRoundCount >= prev.minRoundsPerActivity,
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [prev.currentActivity.replace('_', '')]: newActivityContent
        }
      };
    });
  }, [generateActivityContent]);

  const switchActivity = useCallback((activityId) => {
    setGameState(prev => {
      // Se for a mesma atividade, não faz nada
      if (prev.currentActivity === activityId) {
        return prev;
      }
      
      const newActivityContent = generateActivityContent(activityId, prev.difficulty);
      const newActivityIndex = prev.activityCycle.indexOf(activityId);
      
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction('activity_switch', {
          from: prev.currentActivity,
          to: activityId,
          manual: true,
          round: prev.round
        });
      }
      
      return {
        ...prev,
        currentActivity: activityId,
        activityIndex: newActivityIndex !== -1 ? newActivityIndex : 0,
        activityRoundCount: 1, // 🔥 Resetar contador para nova atividade
        canSwitchActivity: true, // 🔥 Manter permissão sempre ativa para permitir voltar
        isFirstActivityChoice: false, // 🔥 Não é mais a primeira escolha
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [activityId.replace('_', '')]: newActivityContent
        }
      };
    });
  }, [generateActivityContent, multisensoryIntegration]);

  const rotateToNextActivity = useCallback(() => {
    setGameState(prev => {
      const newActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const newCurrentActivity = prev.activityCycle[newActivityIndex];
      const newActivityContent = generateActivityContent(newCurrentActivity, prev.difficulty);
      
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction('activity_rotation', {
          from: prev.currentActivity,
          to: newCurrentActivity,
          automatic: true,
          round: prev.round
        });
      }
      
      return {
        ...prev,
        activityIndex: newActivityIndex,
        currentActivity: newCurrentActivity,
        activityRoundCount: 0,
        roundStartTime: Date.now(),
        showFeedback: false,
        selectedAnswer: null,
        activityData: {
          ...prev.activityData,
          [newCurrentActivity.replace('_', '')]: newActivityContent
        }
      };
    });
  }, [generateActivityContent, multisensoryIntegration]);

  const explainGame = useCallback(() => {
    const currentActivityInfo = ACTIVITY_TYPES[gameState.currentActivity.toUpperCase().replace('_', '_')];
    if (currentActivityInfo) {
      speak(`Atividade atual: ${currentActivityInfo.name}. ${currentActivityInfo.description}`, { rate: 0.8 });
    }
  }, [gameState.currentActivity, speak]);

  const getAccuracy = useCallback(() => {
    const totalAttempts = Object.values(gameState.activityStats).reduce((sum, stat) => sum + stat.attempts, 0);
    const totalCorrect = Object.values(gameState.activityStats).reduce((sum, stat) => sum + stat.correct, 0);
    return totalAttempts > 0 ? Math.round((totalCorrect / totalAttempts) * 100) : 100;
  }, [gameState.activityStats]);

  // Renderizar conteúdo da atividade atual
  const renderActivityContent = () => {
    const currentActivityData = gameState.activityData[gameState.currentActivity.replace('_', '')];
    if (!currentActivityData) return null;

    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.SPEED_CHALLENGE.id:
        return renderSpeedChallengeActivity(currentActivityData);
      case ACTIVITY_TYPES.COLOR_MEMORY.id:
        return renderColorMemoryActivity(currentActivityData);
      case ACTIVITY_TYPES.SHADE_DISCRIMINATION.id:
        return renderShadeDiscriminationActivity(currentActivityData);
      case ACTIVITY_TYPES.SEQUENCE_COLORS.id:
        return renderSequenceColorsActivity(currentActivityData);
      case ACTIVITY_TYPES.GRADIENT_MATCHING.id:
        return renderGradientMatchingActivity(currentActivityData);
      default:
        return renderBasicMatchingActivity(currentActivityData);
    }
  };

  // Renderizar atividade de combinação básica
  const renderBasicMatchingActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div 
          className={styles.countingObject}
          style={{ 
            backgroundColor: data.targetColor,
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto',
            border: '3px solid rgba(255,255,255,0.3)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.2)'
          }}
        />
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Escolher ${option}`}
          >
            <span className={styles.optionNumber}>{option}</span>
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de desafio de velocidade
  const renderSpeedChallengeActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          ⏱️ Responda rapidamente!
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div 
          className={styles.countingObject}
          style={{ 
            backgroundColor: data.targetColor,
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto',
            border: '3px solid rgba(255,255,255,0.3)',
            boxShadow: '0 4px 20px rgba(0,0,0,0.2)',
            animation: 'pulse 1.5s infinite'
          }}
        />
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Escolher ${option}`}
          >
            <span className={styles.optionNumber}>{option}</span>
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de memória de cores
  const renderColorMemoryActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
      </div>
      
      <div className={styles.objectsDisplay}>
        {data.sequence.map((color, index) => (
          <div
            key={index}
            className={styles.countingObject}
            style={{ 
              backgroundColor: color,
              width: '60px',
              height: '60px',
              borderRadius: '50%',
              margin: '0.5rem',
              border: '2px solid rgba(255,255,255,0.3)',
              animationDelay: `${index * 0.1}s`
            }}
          />
        ))}
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Escolher ${option}`}
          >
            <div 
              style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                backgroundColor: option,
                margin: '0 auto',
                border: '2px solid rgba(255,255,255,0.3)'
              }}
            />
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de discriminação de tons
  const renderShadeDiscriminationActivity = (data) => {
    // 🔥 Verificação de segurança
    if (!data || !data.shades || !Array.isArray(data.shades)) {
      return <div>Carregando atividade...</div>;
    }
    
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
          <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
            Cor de referência:
          </div>
          <div style={{ 
            backgroundColor: data.target,
            width: '80px',
            height: '80px',
            borderRadius: '50%',
            margin: '1rem auto',
            border: '3px solid yellow',
            boxShadow: '0 4px 20px rgba(255, 255, 0, 0.5)'
          }} />
        </div>
        
        <div className={styles.objectsDisplay}>
          <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginBottom: '1rem' }}>
            Escolha o tom mais próximo:
          </div>
          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap', justifyContent: 'center' }}>
            {data.shades.map((shade, index) => (
              <button
                key={index}
                className={styles.answerButton}
                onClick={() => handleAnswer(shade)}
                style={{ 
                  backgroundColor: shade,
                  width: '60px',
                  height: '60px',
                  borderRadius: '50%',
                  border: '2px solid rgba(255,255,255,0.3)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  transform: 'scale(1)',
                  ':hover': {
                    transform: 'scale(1.1)',
                    border: '3px solid white'
                  }
                }}
                title={`Tom ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Renderizar atividade de sequência de cores
  const renderSequenceColorsActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          Padrão: Complete a sequência
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', justifyContent: 'center' }}>
          {data.pattern.map((color, index) => (
            <React.Fragment key={index}>
              <div style={{ 
                backgroundColor: color,
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                border: '2px solid rgba(255,255,255,0.3)'
              }} />
              {index < data.pattern.length - 1 && <span style={{ color: 'white' }}>→</span>}
            </React.Fragment>
          ))}
          <span style={{ color: 'white' }}>→</span>
          <div style={{ 
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            border: '2px dashed rgba(255, 255, 255, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '1.5rem',
            fontWeight: 'bold'
          }}>
            ?
          </div>
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Continuar com ${option}`}
          >
            <div 
              style={{
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                backgroundColor: option,
                margin: '0 auto',
                border: '2px solid rgba(255,255,255,0.3)'
              }}
            />
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de combinação de gradientes
  const renderGradientMatchingActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ 
          width: '200px',
          height: '80px',
          background: data.gradient,
          borderRadius: '20px',
          border: '3px solid rgba(255, 255, 255, 0.3)',
          margin: '0 auto',
          boxShadow: '0 4px 20px rgba(0,0,0,0.2)'
        }} />
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Escolher ${option}`}
          >
            <span className={styles.optionNumber}>{option}</span>
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de reconhecimento de padrões
  const renderPatternRecognitionActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          Identifique o padrão de repetição
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center', justifyContent: 'center', flexWrap: 'wrap' }}>
          {data.pattern.map((color, index) => (
            <div
              key={index}
              style={{ 
                backgroundColor: color,
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                border: '2px solid rgba(255,255,255,0.3)',
                animation: `shimmer 2s ease-in-out infinite`,
                animationDelay: `${index * 0.1}s`
              }}
            />
          ))}
          <span style={{ color: 'white', fontSize: '1.2rem', margin: '0 0.5rem' }}>|</span>
          {data.repeat.map((color, index) => (
            <div
              key={`repeat-${index}`}
              style={{ 
                backgroundColor: color,
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                border: '2px solid rgba(255,255,255,0.3)',
                opacity: 0.7
              }}
            />
          ))}
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Escolher padrão ${option}`}
          >
            <span className={styles.optionNumber}>{option}</span>
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de gradiente de cores
  const renderColorGradientActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          Ordenar do mais claro ao mais escuro
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', justifyContent: 'center', flexWrap: 'wrap' }}>
          {data.colors.map((color, index) => (
            <div
              key={index}
              style={{ 
                backgroundColor: color,
                width: '60px',
                height: '60px',
                borderRadius: '50%',
                border: '2px solid rgba(255,255,255,0.3)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '1.2rem',
                textShadow: '0 0 10px rgba(0,0,0,0.8)',
                boxShadow: '0 4px 15px rgba(0,0,0,0.3)'
              }}
            >
              {index + 1}
            </div>
          ))}
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Ordenação ${option}`}
          >
            <span className={styles.optionNumber}>{option}</span>
          </button>
        ))}
      </div>
    </div>
  );

  // Renderizar atividade de sequência temporal
  const renderSequenceActivity = (data) => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>{data.instruction}</h2>
        <div style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.8)', marginTop: '0.5rem' }}>
          Memorize a sequência e reproduza
        </div>
      </div>
      
      <div className={styles.objectsDisplay}>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>
          <div style={{ fontSize: '1.1rem', color: 'rgba(255, 255, 255, 0.9)', marginBottom: '1rem' }}>
            Sequência Mostrada:
          </div>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            {data.sequence.map((color, index) => (
              <div
                key={index}
                style={{ 
                  backgroundColor: color,
                  width: '50px',
                  height: '50px',
                  borderRadius: '50%',
                  border: '2px solid rgba(255,255,255,0.3)',
                  animation: data.showSequence ? `pulse 1s ease-in-out infinite` : 'none',
                  animationDelay: `${index * 0.2}s`,
                  opacity: data.showSequence ? 1 : 0.3
                }}
              />
            ))}
          </div>
        </div>
      </div>

      <div className={styles.answerOptions}>
        {data.options.map((option) => (
          <button
            key={option}
            className={styles.answerButton}
            onClick={() => handleAnswer(option)}
            aria-label={`Reproduzir cor ${option}`}
          >
            <div 
              style={{
                width: '50px',
                height: '50px',
                borderRadius: '50%',
                backgroundColor: option,
                margin: '0 auto',
                border: '2px solid rgba(255,255,255,0.3)'
              }}
            />
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <div 
      className={`${styles.colorMatchGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      {showStartScreen ? (
        <GameStartScreen
          gameTitle="Color Match"
          gameDescription="Desenvolva sua percepção visual e reconhecimento de cores"
          gameIcon="🎨"
          difficulties={[
            { id: 'easy', name: 'Fácil', description: 'Cores básicas e simples', icon: '🟢' },
            { id: 'medium', name: 'Médio', description: 'Mais cores e tons', icon: '🟡' },
            { id: 'hard', name: 'Avançado', description: 'Tons sutis e gradientes', icon: '🔴' }
          ]}
          onStart={startGame}
          onBack={onBack}
        />
      ) : (
        <div className={styles.gameContent}>
          {/* Header do jogo - padrão LetterRecognition */}
          <div className={styles.gameHeader}>
            <h1 className={styles.gameTitle}>
              🎨 Color Match V3
              <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
                {ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || 'Desafio de Velocidade'}
              </div>
            </h1>
            <button
              className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
              onClick={toggleTTS}
              title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
              aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            >
              {ttsActive ? '🔊' : '🔇'}
            </button>
          </div>

          {/* Header com estatísticas - padrão LetterRecognition */}
          <div className={styles.gameStats}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.score}</div>
              <div className={styles.statLabel}>Pontos</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.round}</div>
              <div className={styles.statLabel}>Rodada</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{gameState.accuracy}%</div>
              <div className={styles.statLabel}>Precisão</div>
            </div>
          </div>

          {/* Menu de atividades - padrão LetterRecognition */}
          <div className={styles.activityMenu}>
            {Object.values(ACTIVITY_TYPES).map((activity) => (
              <button
                key={activity.id}
                className={`${styles.activityButton} ${
                  gameState.currentActivity === activity.id ? styles.active : ''
                }`}
                onClick={() => switchActivity(activity.id)}
              >
                <span>{activity.icon}</span>
                <span>{activity.name}</span>
              </button>
            ))}
          </div>

          {/* Renderização da atividade atual */}
          {renderActivityContent()}

          {/* Controles do jogo - padrão LetterRecognition */}
          <div className={styles.gameControls}>
            <button className={styles.controlButton} onClick={explainGame}>
              🔊 Explicar
            </button>
            <button className={styles.controlButton} onClick={generateNewRound}>
              🔄 Nova Rodada
            </button>
            <button className={styles.controlButton} onClick={onBack}>
              ⬅️ Voltar
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default ColorMatchGame;
