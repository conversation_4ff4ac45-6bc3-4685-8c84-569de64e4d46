import{_ as a}from"./index-BIwBZl_j.js";import"./react-BQG6_13O.js";import"./react-router-BtSsPy6x.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";let e=null,t=!1;class i{constructor(){this.instance=null,this.isInitializing=!1,this.logger=this.createLogger(),this.supportedGames=["ImageAssociation","MemoryGame","MusicalSequence","PadroesVisuais","ContagemNumeros","PatternMatching","SequenceLearning","CreativePainting","QuebraCabeca","LetterRecognition","ColorMatch"]}createLogger(){return{info:(...a)=>{},error:(...a)=>{},warn:(...a)=>{}}}async loadDatabaseIntegrator(){if(e)return e;if(t){for(;t;)await new Promise(a=>setTimeout(a,10));return e}t=!0;try{const t=await a(()=>import("./DatabaseIntegrator-DCcGdmP_.js"),[]);return e=t.default||t.DatabaseIntegrator,e}catch(i){throw this.logger.error("❌ Erro ao carregar DatabaseIntegrator:",i),i}finally{t=!1}}async getInstance(){if(this.instance)return this.logger.info("✅ Retornando instância existente do DatabaseIntegrator"),this.instance;if(this.isInitializing){for(this.logger.info("⏳ Aguardando inicialização do DatabaseIntegrator");this.isInitializing;)await new Promise(a=>setTimeout(a,10));return this.instance}this.isInitializing=!0;try{const a=await this.loadDatabaseIntegrator(),e={resilience:{enabled:!0,monitoringEnabled:!0,retryAttempts:3,retryDelay:1e3},cache:{memoryMaxSize:2e3,memoryTTL:3e5},metrics:{enabled:!0,detailedLogging:!0,persistInterval:6e4}};return this.instance=new a(e),this.logger.info("🔄 DatabaseIntegrator inicializado com sucesso"),this.instance}catch(a){throw this.logger.error("❌ Erro ao inicializar DatabaseIntegrator:",a),a}finally{this.isInitializing=!1}}validateSchema(a,e){try{return e&&"object"==typeof e?"game_specific_analysis"!==a||(e.gameName&&this.supportedGames.includes(e.gameName)?e.sessionId&&e.childId&&e.timestamp?this.validateGameSpecificData(e):(this.logger.error("❌ Campos obrigatórios ausentes",{sessionId:e.sessionId,childId:e.childId,timestamp:e.timestamp}),!1):(this.logger.error("❌ gameName inválido ou não suportado",{gameName:e.gameName,supportedGames:this.supportedGames}),!1)):(this.logger.error("❌ Dados inválidos: deve ser um objeto",{collection:a}),!1)}catch(t){return this.logger.error("❌ Erro ao validar esquema:",t),!1}}validateGameSpecificData(a){switch(a.gameName){case"ImageAssociation":return this.validateImageAssociationData(a);case"MemoryGame":return this.validateMemoryGameData(a);case"MusicalSequence":return this.validateMusicalSequenceData(a);case"PadroesVisuais":return this.validatePadroesVisuaisData(a);case"ContagemNumeros":return this.validateContagemNumerosData(a);case"PatternMatching":return this.validatePatternMatchingData(a);case"SequenceLearning":return this.validateSequenceLearningData(a);case"CreativePainting":return this.validateCreativePaintingData(a);case"QuebraCabeca":return this.validateQuebraCabecaData(a);case"LetterRecognition":return this.validateLetterRecognitionData(a);case"ColorMatch":return this.validateColorMatchData(a);default:return this.logger.error("❌ Jogo não reconhecido:",a.gameName),!1}}validateImageAssociationData(a){return!(!a.analysisResult?.categoricalThinking||!a.analysisResult?.semanticUnderstanding)}validateMemoryGameData(a){return!(!a.analysisResult?.workingMemory||!a.analysisResult?.visualMemory)}validateMusicalSequenceData(a){return!(!a.analysisResult?.sequentialProcessing||!a.analysisResult?.rhythmPerception)}validatePadroesVisuaisData(a){return!(!a.analysisResult?.patternRecognition||!a.analysisResult?.spatialProcessing)}validateContagemNumerosData(a){return!(!a.analysisResult?.numberRecognition||!a.analysisResult?.countingAbility)}validatePatternMatchingData(a){return!(!a.analysisResult?.patternRecognition||!a.analysisResult?.visualProcessing)}validateSequenceLearningData(a){return!(!a.analysisResult?.sequentialMemory||!a.analysisResult?.auditoryProcessing)}validateCreativePaintingData(a){return!(!a.analysisResult?.creativityAnalysis||!a.analysisResult?.motorSkills)}validateQuebraCabecaData(a){return!(!a.analysisResult?.spatialReasoning||!a.analysisResult?.problemSolving)}validateLetterRecognitionData(a){return!(!a.analysisResult?.letterRecognition||!a.analysisResult?.visualProcessing)}validateColorMatchData(a){return!(!a.analysisResult?.colorRecognition||!a.analysisResult?.visualDiscrimination)}async store(a,e){try{if(!this.validateSchema(a,e))throw new Error(`Validação de esquema falhou para coleção ${a}`);const t=await this.getInstance();await t.store(a,{...e,storedAt:(new Date).toISOString()}),this.logger.info("💾 Dados armazenados com sucesso",{collection:a,sessionId:e.sessionId,gameName:e.gameName})}catch(t){throw this.logger.error("❌ Erro ao armazenar dados:",t),t}}async query(a,e){try{const t=await this.getInstance(),i=await t.query(a,e);return this.logger.info("🔍 Consulta realizada com sucesso",{collection:a,query:e}),i||[]}catch(t){return this.logger.error("❌ Erro ao consultar dados:",t),[]}}async update(a,e,t){try{const i=await this.getInstance(),s=await i.update(a,e,t);return this.logger.info("🔄 Dados atualizados com sucesso",{collection:a,query:e}),s}catch(i){throw this.logger.error("❌ Erro ao atualizar dados:",i),i}}async delete(a,e){try{const t=await this.getInstance(),i=await t.delete(a,e);return this.logger.info("🗑️ Dados removidos com sucesso",{collection:a,query:e}),i}catch(t){throw this.logger.error("❌ Erro ao remover dados:",t),t}}async reset(){try{this.logger.info("🔄 Reiniciando DatabaseSingleton..."),this.instance&&this.instance.clearCache&&await this.instance.clearCache(),this.instance=null,this.isInitializing=!1,this.logger.info("✅ DatabaseSingleton reiniciado")}catch(a){this.logger.error("❌ Erro ao reiniciar DatabaseSingleton:",a)}}async getStats(){try{const a=await this.getInstance();return a&&a.getStats?await a.getStats():null}catch(a){return this.logger.error("❌ Erro ao obter estatísticas:",a),null}}}export{i as default};
