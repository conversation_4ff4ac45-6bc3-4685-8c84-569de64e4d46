import{j as t}from"./react-query-CDommIwN.js";import{r as e}from"./react-router-BtSsPy6x.js";const n=e.createContext({});const i="undefined"!=typeof window,s=i?e.useLayoutEffect:e.useEffect,o=e.createContext(null);function r(t,e){-1===t.indexOf(e)&&t.push(e)}function a(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const l=(t,e,n)=>n>e?e:n<t?t:n;let u=()=>{},h=()=>{};u=(t,e)=>{},h=(t,e)=>{if(!t)throw new Error(e)};const c={},d=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function p(t){return"object"==typeof t&&null!==t}const m=t=>/^0[^.\s]+$/u.test(t);function f(t){let e;return()=>(void 0===e&&(e=t()),e)}const y=t=>t,g=(t,e)=>n=>e(t(n)),v=(...t)=>t.reduce(g),x=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class T{constructor(){this.subscriptions=[]}add(t){return r(this.subscriptions,t),()=>a(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const w=t=>1e3*t,P=t=>t/1e3;function S(t,e){return e?t*(1e3/e):0}const b=new Set;function A(t,e,n){t||b.has(e)||b.add(e)}const E=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function V(t,e,n,i){if(t===e&&n===i)return y;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=E(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:E(s(t),e,i)}const M=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,C=t=>e=>1-t(1-e),D=V(.33,1.53,.69,.99),k=C(D),R=M(k),L=t=>(t*=2)<1?.5*k(t):.5*(2-Math.pow(2,-10*(t-1))),j=t=>1-Math.sin(Math.acos(t)),B=C(j),F=M(j),O=V(.42,0,1,1),I=V(0,0,.58,1),U=V(.42,0,.58,1),N=t=>Array.isArray(t)&&"number"==typeof t[0],W={linear:y,easeIn:O,easeInOut:U,easeOut:I,circIn:j,circInOut:F,circOut:B,backIn:k,backInOut:R,backOut:D,anticipate:L},$=t=>{if(N(t)){h(4===t.length,"Cubic bezier arrays must contain four numerical values.");const[e,n,i,s]=t;return V(e,n,i,s)}return"string"==typeof t?(h(void 0!==W[t],`Invalid easing type '${t}'`),W[t]):t},Y=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],X={value:null};function K(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=Y.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(h.schedule(e),t()),l++,e(a)}const h={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&X.value&&X.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,h.process(t)))}};return h}(o,e?n:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:p,render:m,postRender:f}=r,y=()=>{const o=c.useManualTiming?s.timestamp:performance.now();n=!1,c.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),h.process(s),d.process(s),p.process(s),m.process(s),f.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(y))};return{schedule:Y.reduce((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(y)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<Y.length;e++)r[Y[e]].cancel(t)},state:s,steps:r}}const{schedule:z,cancel:H,state:q,steps:G}=K("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:y,!0);let Z;function _(){Z=void 0}const J={now:()=>(void 0===Z&&J.set(q.isProcessing||c.useManualTiming?q.timestamp:performance.now()),Z),set:t=>{Z=t,queueMicrotask(_)}},Q=t=>e=>"string"==typeof e&&e.startsWith(t),tt=Q("--"),et=Q("var(--"),nt=t=>!!et(t)&&it.test(t.split("/*")[0].trim()),it=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,st={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},ot={...st,transform:t=>l(0,1,t)},rt={...st,default:1},at=t=>Math.round(1e5*t)/1e5,lt=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const ut=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ht=(t,e)=>n=>Boolean("string"==typeof n&&ut.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),ct=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(lt);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},dt={...st,transform:t=>Math.round((t=>l(0,255,t))(t))},pt={test:ht("rgb","red"),parse:ct("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+dt.transform(t)+", "+dt.transform(e)+", "+dt.transform(n)+", "+at(ot.transform(i))+")"};const mt={test:ht("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:pt.transform},ft=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),yt=ft("deg"),gt=ft("%"),vt=ft("px"),xt=ft("vh"),Tt=ft("vw"),wt=(()=>({...gt,parse:t=>gt.parse(t)/100,transform:t=>gt.transform(100*t)}))(),Pt={test:ht("hsl","hue"),parse:ct("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+gt.transform(at(e))+", "+gt.transform(at(n))+", "+at(ot.transform(i))+")"},St={test:t=>pt.test(t)||mt.test(t)||Pt.test(t),parse:t=>pt.test(t)?pt.parse(t):Pt.test(t)?Pt.parse(t):mt.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?pt.transform(t):Pt.transform(t),getAnimatableNone:t=>{const e=St.parse(t);return e.alpha=0,St.transform(e)}},bt=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const At="number",Et="color",Vt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Mt(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Vt,t=>(St.test(t)?(i.color.push(o),s.push(Et),n.push(St.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(At),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:r,indexes:i,types:s}}function Ct(t){return Mt(t).values}function Dt(t){const{split:e,types:n}=Mt(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===At?at(t[o]):e===Et?St.transform(t[o]):t[o]}return s}}const kt=t=>"number"==typeof t?0:St.test(t)?St.getAnimatableNone(t):t;const Rt={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(lt)?.length||0)+(t.match(bt)?.length||0)>0},parse:Ct,createTransformer:Dt,getAnimatableNone:function(t){const e=Ct(t);return Dt(t)(e.map(kt))}};function Lt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function jt(t,e){return n=>n>0?e:t}const Bt=(t,e,n)=>t+(e-t)*n,Ft=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},Ot=[mt,pt,Pt];function It(t){const e=(n=t,Ot.find(t=>t.test(n)));var n;if(Boolean(e),!Boolean(e))return!1;let i=e.parse(t);return e===Pt&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=Lt(a,i,t+1/3),o=Lt(a,i,t),r=Lt(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const Ut=(t,e)=>{const n=It(t),i=It(e);if(!n||!i)return jt(t,e);const s={...n};return t=>(s.red=Ft(n.red,i.red,t),s.green=Ft(n.green,i.green,t),s.blue=Ft(n.blue,i.blue,t),s.alpha=Bt(n.alpha,i.alpha,t),pt.transform(s))},Nt=new Set(["none","hidden"]);function Wt(t,e){return n=>Bt(t,e,n)}function $t(t){return"number"==typeof t?Wt:"string"==typeof t?nt(t)?jt:St.test(t)?Ut:Kt:Array.isArray(t)?Yt:"object"==typeof t?St.test(t)?Ut:Xt:jt}function Yt(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>$t(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Xt(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=$t(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const Kt=(t,e)=>{const n=Rt.createTransformer(e),i=Mt(t),s=Mt(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?Nt.has(t)&&!s.values.length||Nt.has(e)&&!i.values.length?function(t,e){return Nt.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):v(Yt(function(t,e){const n=[],i={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){const o=e.types[s],r=t.indexes[o][i[o]],a=t.values[r]??0;n[s]=a,i[o]++}return n}(i,s),s.values),n):jt(t,e)};function zt(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Bt(t,e,n);return $t(t)(t,e)}const Ht=t=>{const e=({timestamp:e})=>t(e);return{start:(t=!0)=>z.update(e,t),stop:()=>H(e),now:()=>q.isProcessing?q.timestamp:J.now()}},qt=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let o=0;o<s;o++)i+=Math.round(1e4*t(o/(s-1)))/1e4+", ";return`linear(${i.substring(0,i.length-2)})`},Gt=2e4;function Zt(t){let e=0;let n=t.next(e);for(;!n.done&&e<Gt;)e+=50,n=t.next(e);return e>=Gt?1/0:e}function _t(t,e,n){const i=Math.max(e-5,0);return S(n-t(i),e-i)}const Jt=100,Qt=10,te=1,ee=0,ne=800,ie=.3,se=.3,oe={granular:.01,default:2},re={granular:.005,default:.5},ae=.01,le=10,ue=.05,he=1,ce=.001;function de({duration:t=ne,bounce:e=ie,velocity:n=ee,mass:i=te}){let s,o,r=1-e;r=l(ue,he,r),t=l(ae,le,P(t)),r<1?(s=e=>{const i=e*r,s=i*t,o=i-n,a=me(e,r),l=Math.exp(-s);return ce-o/a*l},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=me(Math.pow(e,2),r);return(-s(e)+ce>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let s=1;s<pe;s++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=w(t),isNaN(a))return{stiffness:Jt,damping:Qt,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}const pe=12;function me(t,e){return t*Math.sqrt(1-e*e)}const fe=["duration","bounce"],ye=["stiffness","damping","mass"];function ge(t,e){return e.some(e=>void 0!==t[e])}function ve(t=se,e=ie){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:d,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:ee,stiffness:Jt,damping:Qt,mass:te,isResolvedFromDuration:!1,...t};if(!ge(t,ye)&&ge(t,fe))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*l(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:te,stiffness:s,damping:o}}else{const n=de(t);e={...e,...n,mass:te},e.isResolvedFromDuration=!0}return e}({...n,velocity:-P(n.velocity||0)}),f=p||0,y=h/(2*Math.sqrt(u*c)),g=r-o,v=P(Math.sqrt(u/c)),x=Math.abs(g)<5;let T;if(i||(i=x?oe.granular:oe.default),s||(s=x?re.granular:re.default),y<1){const t=me(v,y);T=e=>{const n=Math.exp(-y*v*e);return r-n*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===y)T=t=>r-Math.exp(-v*t)*(g+(f+v*g)*t);else{const t=v*Math.sqrt(y*y-1);T=e=>{const n=Math.exp(-y*v*e),i=Math.min(t*e,300);return r-n*((f+y*v*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const S={calculatedDuration:m&&d||null,next:t=>{const e=T(t);if(m)a.done=t>=d;else{let n=0===t?f:0;y<1&&(n=0===t?w(f):_t(T,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(Zt(S),Gt),e=qt(e=>S.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return S}function xe({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:h}){const c=t[0],d={done:!1,value:c},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=c+m,y=void 0===r?f:r(f);y!==f&&(m=y-c);const g=t=>-m*Math.exp(-t/i),v=t=>y+g(t),x=t=>{const e=g(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?y:n};let T,w;const P=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(T=t,w=ve({keyframes:[d.value,p(d.value)],velocity:_t(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==T||(e=!0,x(t),P(t)),void 0!==T&&t>=T?w.next(t-T):(!e&&x(t),d)}}}function Te(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(h(o===e.length,"Both input and output ranges must be the same length"),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||c.mix||zt,o=t.length-1;for(let r=0;r<o;r++){let n=s(t[r],t[r+1]);if(e){const t=Array.isArray(e)?e[r]||y:e;n=v(t,n)}i.push(n)}return i}(e,i,s),u=a.length,d=n=>{if(r&&n<t[0])return e[0];let i=0;if(u>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=x(t[i],t[i+1],n);return a[i](s)};return n?e=>d(l(t[0],t[o-1],e)):d}function we(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=x(0,e,i);t.push(Bt(n,1,s))}}(e,t.length-1),e}function Pe({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map($):$(i),o={done:!1,value:e[0]},r=function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:we(e),t),a=Te(r,e,{ease:Array.isArray(s)?s:(l=e,u=s,l.map(()=>u||U).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}ve.applyToOptions=t=>{const e=function(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(Zt(i),Gt);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:P(s)}}(t,100,ve);return t.ease=e.ease,t.duration=w(e.duration),t.type="keyframes",t};const Se=t=>null!==t;function be(t,{repeat:e,repeatType:n="loop"},i,s=1){const o=t.filter(Se),r=s<0||e&&"loop"!==n&&e%2==1?0:o.length-1;return r&&void 0!==i?i:o[r]}const Ae={decay:xe,inertia:xe,tween:Pe,keyframes:Pe,spring:ve};function Ee(t){"string"==typeof t.type&&(t.type=Ae[t.type])}class Ve{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Me=t=>t/100;class Ce extends Ve{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;t&&t.updatedAt!==J.now()&&this.tick(J.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ee(t);const{type:e=Pe,repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=t;let{keyframes:r}=t;const a=e||Pe;a!==Pe&&h(r.length<=2,`Only two keyframes currently supported with spring and inertia animations. Trying to animate ${r}`),a!==Pe&&"number"!=typeof r[0]&&(this.mixKeyframes=v(Me,zt(r[0],r[1])),r=[0,100]);const l=a({...t,keyframes:r});"mirror"===s&&(this.mirroredGenerator=a({...t,keyframes:[...r].reverse(),velocity:-o})),null===l.calculatedDuration&&(l.calculatedDuration=Zt(l));const{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(n+1)-i,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:n,totalDuration:i,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:r,calculatedDuration:a}=this;if(null===this.startTime)return n.next(0);const{delay:u=0,keyframes:h,repeat:c,repeatType:d,repeatDelay:p,type:m,onUpdate:f,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-i/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=i);let x=this.currentTime,T=n;if(c){const t=Math.min(this.currentTime,i)/r;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,c+1);Boolean(e%2)&&("reverse"===d?(n=1-n,p&&(n-=p/r)):"mirror"===d&&(T=o)),x=l(0,1,n)*r}const w=v?{done:!1,value:h[0]}:T.next(x);s&&(w.value=s(w.value));let{done:P}=w;v||null===a||(P=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&m!==xe&&(w.value=be(h,this.options,y,this.speed)),f&&f(w.value),S&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return P(this.calculatedDuration)}get time(){return P(this.currentTime)}set time(t){t=w(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(J.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=P(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=Ht,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=e??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(J.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}const De=t=>180*t/Math.PI,ke=t=>{const e=De(Math.atan2(t[1],t[0]));return Le(e)},Re={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ke,rotateZ:ke,skewX:t=>De(Math.atan(t[1])),skewY:t=>De(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Le=t=>((t%=360)<0&&(t+=360),t),je=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Be=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),Fe={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:je,scaleY:Be,scale:t=>(je(t)+Be(t))/2,rotateX:t=>Le(De(Math.atan2(t[6],t[5]))),rotateY:t=>Le(De(Math.atan2(-t[2],t[0]))),rotateZ:ke,rotate:ke,skewX:t=>De(Math.atan(t[4])),skewY:t=>De(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Oe(t){return t.includes("scale")?1:0}function Ie(t,e){if(!t||"none"===t)return Oe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=Fe,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=Re,s=e}if(!s)return Oe(e);const o=i[e],r=s[1].split(",").map(Ue);return"function"==typeof o?o(r):r[o]}function Ue(t){return parseFloat(t.trim())}const Ne=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],We=(()=>new Set(Ne))(),$e=t=>t===st||t===vt,Ye=new Set(["x","y","z"]),Xe=Ne.filter(t=>!Ye.has(t));const Ke={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Ie(e,"x"),y:(t,{transform:e})=>Ie(e,"y")};Ke.translateX=Ke.x,Ke.translateY=Ke.y;const ze=new Set;let He=!1,qe=!1,Ge=!1;function Ze(){if(qe){const t=Array.from(ze).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return Xe.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{t.getValue(e)?.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}qe=!1,He=!1,ze.forEach(t=>t.complete(Ge)),ze.clear()}function _e(){ze.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(qe=!0)})}class Je{constructor(t,e,n,i,s,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(ze.add(this),He||(He=!0,z.read(_e),z.resolveKeyframes(Ze))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;if(null===t[0]){const s=i?.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),ze.delete(this)}cancel(){"scheduled"===this.state&&(ze.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}const Qe=f(()=>void 0!==window.ScrollTimeline),tn={};function en(t,e){const n=f(t);return()=>tn[e]??n()}const nn=en(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),sn=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,on={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:sn([0,.65,.55,1]),circOut:sn([.55,0,1,.45]),backIn:sn([.31,.01,.66,-.59]),backOut:sn([.33,1.53,.69,.99])};function rn(t,e){return t?"function"==typeof t?nn()?qt(t,e):"ease-out":N(t)?sn(t):Array.isArray(t)?t.map(t=>rn(t,e)||on.easeOut):on[t]:void 0}function an(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},u=void 0){const h={[e]:n};l&&(h.offset=l);const c=rn(a,s);Array.isArray(c)&&(h.easing=c);const d={delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"};u&&(d.pseudoElement=u);return t.animate(h,d)}function ln(t){return"function"==typeof t&&"applyToOptions"in t}class un extends Ve{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:n,keyframes:i,pseudoElement:s,allowFlatten:o=!1,finalKeyframe:r,onComplete:a}=t;this.isPseudoElement=Boolean(s),this.allowFlatten=o,this.options=t,h("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');const l=function({type:t,...e}){return ln(t)&&nn()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=an(e,n,i,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){const t=be(i,this.options,r,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,n){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,n):t.style[e]=n}(e,n,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return P(Number(t))}get time(){return P(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=w(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&Qe()?(this.animation.timeline=t,y):e(this)}}const hn={anticipate:L,backInOut:R,circInOut:F};function cn(t){"string"==typeof t.ease&&t.ease in hn&&(t.ease=hn[t.ease])}class dn extends un{constructor(t){cn(t),Ee(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:n,onComplete:i,element:s,...o}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const r=new Ce({...o,autoplay:!1}),a=w(this.finishedTime??this.time);e.setWithVelocity(r.sample(a-10).value,r.sample(a).value,10),r.stop()}}const pn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Rt.test(t)&&"0"!==t||t.startsWith("url(")));function mn(t){return p(t)&&"offsetHeight"in t}const fn=new Set(["opacity","clipPath","filter","transform"]),yn=f(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class gn extends Ve{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",keyframes:r,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=J.now();const c={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,name:a,motionValue:l,element:u,...h},d=u?.KeyframeResolver||Je;this.keyframeResolver=new d(r,(t,e,n)=>this.onKeyframesResolved(t,e,c,!n),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,n,i){this.keyframeResolver=void 0;const{name:s,type:o,velocity:r,delay:a,isHandoff:l,onUpdate:u}=n;this.resolvedAt=J.now(),function(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=pn(s,e),a=pn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||ln(n))&&i)}(t,s,o,r)||(!c.instantAnimations&&a||u?.(be(t,n,e)),t[0]=t[t.length-1],n.duration=0,n.repeat=0);const h={startTime:i?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...n,keyframes:t},d=!l&&function(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!mn(e?.owner?.current))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return yn()&&n&&fn.has(n)&&("transform"!==n||!l)&&!a&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}(h)?new dn({...h,element:h.motionValue.owner.current}):new Ce(h);d.finished.then(()=>this.notifyFinished()).catch(y),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Ge=!0,_e(),Ze(),Ge=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const vn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function xn(t,e,n=1){h(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);const[i,s]=function(t){const e=vn.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${n??i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return d(t)?parseFloat(t):t}return nt(s)?xn(s,e,n+1):s}function Tn(t,e){return t?.[e]??t?.default??t}const wn=new Set(["width","height","top","left","right","bottom",...Ne]),Pn=t=>e=>e.test(t),Sn=[st,vt,gt,yt,Tt,xt,{test:t=>"auto"===t,parse:t=>t}],bn=t=>Sn.find(Pn(t));function An(t){return"number"==typeof t?0===t:null===t||("none"===t||"0"===t||m(t))}const En=new Set(["brightness","contrast","saturate","opacity"]);function Vn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(lt)||[];if(!i)return t;const s=n.replace(i,"");let o=En.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Mn=/\b([a-z-]*)\(.*?\)/gu,Cn={...Rt,getAnimatableNone:t=>{const e=t.match(Mn);return e?e.map(Vn).join(" "):t}},Dn={...st,transform:Math.round},kn={borderWidth:vt,borderTopWidth:vt,borderRightWidth:vt,borderBottomWidth:vt,borderLeftWidth:vt,borderRadius:vt,radius:vt,borderTopLeftRadius:vt,borderTopRightRadius:vt,borderBottomRightRadius:vt,borderBottomLeftRadius:vt,width:vt,maxWidth:vt,height:vt,maxHeight:vt,top:vt,right:vt,bottom:vt,left:vt,padding:vt,paddingTop:vt,paddingRight:vt,paddingBottom:vt,paddingLeft:vt,margin:vt,marginTop:vt,marginRight:vt,marginBottom:vt,marginLeft:vt,backgroundPositionX:vt,backgroundPositionY:vt,...{rotate:yt,rotateX:yt,rotateY:yt,rotateZ:yt,scale:rt,scaleX:rt,scaleY:rt,scaleZ:rt,skew:yt,skewX:yt,skewY:yt,distance:vt,translateX:vt,translateY:vt,translateZ:vt,x:vt,y:vt,z:vt,perspective:vt,transformPerspective:vt,opacity:ot,originX:wt,originY:wt,originZ:vt},zIndex:Dn,fillOpacity:ot,strokeOpacity:ot,numOctaves:Dn},Rn={...kn,color:St,backgroundColor:St,outlineColor:St,fill:St,stroke:St,borderColor:St,borderTopColor:St,borderRightColor:St,borderBottomColor:St,borderLeftColor:St,filter:Cn,WebkitFilter:Cn},Ln=t=>Rn[t];function jn(t,e){let n=Ln(t);return n!==Cn&&(n=Rt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Bn=new Set(["auto","none","0"]);class Fn extends Je{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let n=t[a];if("string"==typeof n&&(n=n.trim(),nt(n))){const i=xn(n,e.current);void 0!==i&&(t[a]=i),a===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!wn.has(n)||2!==t.length)return;const[i,s]=t,o=bn(i),r=bn(s);if(o!==r)if($e(o)&&$e(r))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else Ke[n]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let i=0;i<t.length;i++)(null===t[i]||An(t[i]))&&n.push(i);n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Bn.has(e)&&Mt(e).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=jn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ke[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const i=t.getValue(e);i&&i.jump(this.measuredOrigin,!1);const s=n.length-1,o=n[s];n[s]=Ke[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}const On=(t,e)=>e&&"number"==typeof t?e.transform(t):t;class In{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=J.now();if(this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const i of this.dependents)i.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=J.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return A(!1,'value.onChange(callback) is deprecated. Switch to value.on("change", callback).'),this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);const n=this.events[t].add(e);return"change"===t?()=>{n(),z.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=J.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return S(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Un(t,e){return new In(t,e)}const{schedule:Nn}=K(queueMicrotask,!1),Wn={x:!1,y:!1};function $n(){return Wn.x||Wn.y}function Yn(t,e){const n=function(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document;const i=n?.[t]??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function Xn(t){return!("touch"===t.pointerType||$n())}const Kn=(t,e)=>!!e&&(t===e||Kn(t,e.parentElement)),zn=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,Hn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const qn=new WeakSet;function Gn(t){return e=>{"Enter"===e.key&&t(e)}}function Zn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function _n(t){return zn(t)&&!$n()}function Jn(t,e,n={}){const[i,s,o]=Yn(t,n),r=t=>{const i=t.currentTarget;if(!_n(t))return;qn.add(i);const o=e(i,t),r=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),qn.has(i)&&qn.delete(i),_n(t)&&"function"==typeof o&&o(t,{success:e})},a=t=>{r(t,i===window||i===document||n.useGlobalTarget||Kn(i,t.target))},l=t=>{r(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return i.forEach(t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",r,s),mn(t)&&(t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=Gn(()=>{if(qn.has(n))return;Zn(n,"down");const t=Gn(()=>{Zn(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>Zn(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)})(t,s)),e=t,Hn.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),o}function Qn(t){return p(t)&&"ownerSVGElement"in t}const ti=t=>Boolean(t&&t.getVelocity),ei=[...Sn,St,Rt],ni=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});const ii=e.createContext({strict:!1}),si={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},oi={};for(const ia in si)oi[ia]={isEnabled:t=>si[ia].some(e=>!!t[e])};const ri=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ai(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ri.has(t)}let li=t=>!ai(t);try{"function"==typeof(ui=require("@emotion/is-prop-valid").default)&&(li=t=>t.startsWith("on")?!ai(t):ui(t))}catch{}var ui;function hi(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy((...e)=>(A(!1,"motion() is deprecated. Use motion.create() instead."),t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const ci=e.createContext({});function di(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function pi(t){return"string"==typeof t||Array.isArray(t)}const mi=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],fi=["initial",...mi];function yi(t){return di(t.animate)||fi.some(e=>pi(t[e]))}function gi(t){return Boolean(yi(t)||t.variants)}function vi(t){const{initial:n,animate:i}=function(t,e){if(yi(t)){const{initial:e,animate:n}=t;return{initial:!1===e||pi(e)?e:void 0,animate:pi(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(ci));return e.useMemo(()=>({initial:n,animate:i}),[xi(n),xi(i)])}function xi(t){return Array.isArray(t)?t.join(" "):t}const Ti=Symbol.for("motionComponentSymbol");function wi(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function Pi(t,n,i){return e.useCallback(e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):wi(i)&&(i.current=e))},[n])}const Si=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),bi="data-"+Si("framerAppearId"),Ai=e.createContext({});function Ei(t,n,i,r,a){const{visualElement:l}=e.useContext(ci),u=e.useContext(ii),h=e.useContext(o),c=e.useContext(ni).reducedMotion,d=e.useRef(null);r=r||u.renderer,!d.current&&r&&(d.current=r(t,{visualState:n,parent:l,props:i,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:c}));const p=d.current,m=e.useContext(Ai);!p||p.projection||!a||"html"!==p.type&&"svg"!==p.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Vi(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&wi(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,crossfade:h,layoutScroll:l,layoutRoot:u})}(d.current,i,a,m);const f=e.useRef(!1);e.useInsertionEffect(()=>{p&&f.current&&p.update(i,h)});const y=i[bi],g=e.useRef(Boolean(y)&&!window.MotionHandoffIsComplete?.(y)&&window.MotionHasOptimisedAnimation?.(y));return s(()=>{p&&(f.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),Nn.render(p.render),g.current&&p.animationState&&p.animationState.animateChanges())}),e.useEffect(()=>{p&&(!g.current&&p.animationState&&p.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(y)}),g.current=!1))}),p}function Vi(t){if(t)return!1!==t.options.allowProjection?t.projection:Vi(t.parent)}function Mi({preloadedFeatures:n,createVisualElement:s,useRender:o,useVisualState:r,Component:a}){function l(l,u){let c;const d={...e.useContext(ni),...l,layoutId:Ci(l)},{isStatic:p}=d,m=vi(l),f=r(l,p);if(!p&&i){!function(t,n){const i=e.useContext(ii).strict;if(n&&i){const e="You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.";t.ignoreStrict||h(!1,e)}}(d,n);const t=function(t){const{drag:e,layout:n}=oi;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(d);c=t.MeasureLayout,m.visualElement=Ei(a,f,d,s,t.ProjectionNode)}return t.jsxs(ci.Provider,{value:m,children:[c&&m.visualElement?t.jsx(c,{visualElement:m.visualElement,...d}):null,o(a,l,Pi(f,m.visualElement,u),f,p,m.visualElement)]})}n&&function(t){for(const e in t)oi[e]={...oi[e],...t[e]}}(n),l.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;const u=e.forwardRef(l);return u[Ti]=a,u}function Ci({layoutId:t}){const i=e.useContext(n).id;return i&&void 0!==t?i+"-"+t:t}const Di={};function ki(t,{layout:e,layoutId:n}){return We.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Di[t]||"opacity"===t)}const Ri={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Li=Ne.length;function ji(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const t=e[l];if(We.has(l))r=!0;else if(tt(l))s[l]=t;else{const e=On(t,kn[l]);l.startsWith("origin")?(a=!0,o[l]=e):i[l]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<Li;o++){const r=Ne[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=On(a,kn[r]);l||(s=!1,i+=`${Ri[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const Bi=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Fi(t,e,n){for(const i in e)ti(e[i])||ki(i,n)||(t[i]=e[i])}function Oi(t,n){const i={};return Fi(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return ji(e,n,t),Object.assign({},e.vars,e.style)},[n])}(t,n)),i}function Ii(t,e){const n={},i=Oi(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const Ui={offset:"stroke-dashoffset",array:"stroke-dasharray"},Ni={offset:"strokeDashoffset",array:"strokeDasharray"};function Wi(t,{attrX:e,attrY:n,attrScale:i,pathLength:s,pathSpacing:o=1,pathOffset:r=0,...a},l,u,h){if(ji(t,a,u),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==n&&(c.y=n),void 0!==i&&(c.scale=i),void 0!==s&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?Ui:Ni;t[o.offset]=vt.transform(-i);const r=vt.transform(e),a=vt.transform(n);t[o.array]=`${r} ${a}`}(c,s,o,r,!1)}const $i=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Yi=t=>"string"==typeof t&&"svg"===t.toLowerCase();function Xi(t,n,i,s){const o=e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Wi(e,n,Yi(s),t.transformTemplate,t.style),{...e.attrs,style:{...e.style}}},[n]);if(t.style){const e={};Fi(e,t.style,t),o.style={...e,...o.style}}return o}const Ki=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function zi(t){return"string"==typeof t&&!t.includes("-")&&!!(Ki.indexOf(t)>-1||/[A-Z]/u.test(t))}function Hi(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(zi(n)?Xi:Ii)(i,o,r,n),l=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(li(s)||!0===n&&ai(s)||!e&&!ai(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:h}=i,c=e.useMemo(()=>ti(h)?h.get():h,[h]);return e.createElement(n,{...u,children:c})}}function qi(t){const e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function Gi(t,e,n,i){if("function"==typeof e){const[s,o]=qi(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=qi(i);e=e(void 0!==n?n:t.custom,s,o)}return e}function Zi(t){return ti(t)?t.get():t}const _i=t=>(n,i)=>{const s=e.useContext(ci),r=e.useContext(o),a=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,i,s){return{latestValues:Ji(n,i,s,t),renderState:e()}}(t,n,s,r);return i?a():function(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}(a)};function Ji(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=Zi(o[d]);let{initial:r,animate:a}=t;const l=yi(t),u=gi(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let h=!!n&&!1===n.initial;h=h||!1===r;const c=h?a:r;if(c&&"boolean"!=typeof c&&!di(c)){const e=Array.isArray(c)?c:[c];for(let n=0;n<e.length;n++){const i=Gi(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const i in n){let t=n[i];if(Array.isArray(t)){t=t[h?t.length-1:0]}null!==t&&(s[i]=t)}for(const i in t)s[i]=t[i]}}}return s}function Qi(t,e,n){const{style:i}=t,s={};for(const o in i)(ti(i[o])||e.style&&ti(e.style[o])||ki(o,t)||void 0!==n?.getValue(o)?.liveStyle)&&(s[o]=i[o]);return s}const ts={useVisualState:_i({scrapeMotionValuesFromProps:Qi,createRenderState:Bi})};function es(t,e,n){const i=Qi(t,e,n);for(const s in t)if(ti(t[s])||ti(e[s])){i[-1!==Ne.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=t[s]}return i}const ns={useVisualState:_i({scrapeMotionValuesFromProps:es,createRenderState:$i})};function is(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return Mi({...zi(n)?ns:ts,preloadedFeatures:t,useRender:Hi(i),createVisualElement:e,Component:n})}}function ss(t,e,n){const i=t.getProps();return Gi(i,e,void 0!==n?n:i.custom,t)}const os=t=>Array.isArray(t);function rs(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Un(n))}function as(t){return os(t)?t[t.length-1]||0:t}function ls(t,e){const n=t.getValue("willChange");if(i=n,Boolean(ti(i)&&i.add))return n.add(e);if(!n&&c.WillChange){const n=new c.WillChange("auto");t.addValue("willChange",n),n.add(e)}var i}function us(t){return t.props[bi]}const hs=t=>null!==t;const cs={type:"spring",stiffness:500,damping:25,restSpeed:10},ds={type:"keyframes",duration:.8},ps={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ms=(t,{keyframes:e})=>e.length>2?ds:We.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:cs:ps;const fs=(t,e,n,i={},s,o)=>r=>{const a=Tn(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=w(l);const h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length})(a)||Object.assign(h,ms(t,h)),h.duration&&(h.duration=w(h.duration)),h.repeatDelay&&(h.repeatDelay=w(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(d=!0)),(c.instantAnimations||c.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,d&&!o&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:n="loop"}){const i=t.filter(hs);return i[e&&"loop"!==n&&e%2==1?0:i.length-1]}(h.keyframes,a);if(void 0!==t)return void z.update(()=>{h.onUpdate(t),h.onComplete()})}return a.isSync?new Ce(h):new gn(h)};function ys({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function gs(t,e,{delay:n=0,transitionOverride:i,type:s}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;i&&(o=i);const l=[],u=s&&t.animationState&&t.animationState.getState()[s];for(const h in a){const e=t.getValue(h,t.latestValues[h]??null),i=a[h];if(void 0===i||u&&ys(u,h))continue;const s={delay:n,...Tn(o||{},h)},r=e.get();if(void 0!==r&&!e.isAnimating&&!Array.isArray(i)&&i===r&&!s.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){const e=us(t);if(e){const t=window.MotionHandoffAnimation(e,h,z);null!==t&&(s.startTime=t,c=!0)}}ls(t,h),e.start(fs(h,e,i,t.shouldReduceMotion&&wn.has(h)?{type:!1}:s,t,c));const d=e.animation;d&&l.push(d)}return r&&Promise.all(l).then(()=>{z.update(()=>{r&&function(t,e){const n=ss(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const r in o)rs(t,r,as(o[r]))}(t,r)})}),l}function vs(t,e,n={}){const i=ss(t,e,"exit"===n.type?t.presenceContext?.custom:void 0);let{transition:s=t.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(gs(t,i,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:o=0,staggerChildren:r,staggerDirection:a}=s;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(xs).forEach((t,i)=>{t.notify("AnimationStart",e),r.push(vs(t,e,{...o,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,o+i,r,a,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[t,e]="beforeChildren"===a?[o,r]:[r,o];return t().then(()=>e())}return Promise.all([o(),r(n.delay)])}function xs(t,e){return t.sortNodePosition(e)}function Ts(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const ws=fi.length;function Ps(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&Ps(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<ws;n++){const i=fi[n],s=t.props[i];(pi(s)||!1===s)&&(e[i]=s)}return e}const Ss=[...mi].reverse(),bs=mi.length;function As(t){return e=>Promise.all(e.map(({animation:e,options:n})=>function(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>vs(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=vs(t,e,n);else{const s="function"==typeof e?ss(t,e,n.custom):e;i=Promise.all(gs(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}(t,e,n)))}function Es(t){let e=As(t),n=Cs(),i=!0;const s=e=>(n,i)=>{const s=ss(t,i,"exit"===e?t.presenceContext?.custom:void 0);if(s){const{transition:t,transitionEnd:e,...i}=s;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=Ps(t.parent)||{},l=[],u=new Set;let h={},c=1/0;for(let e=0;e<bs;e++){const d=Ss[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=pi(m),y=d===o?p.isActive:null;!1===y&&(c=e);let g=m===a[d]&&m!==r[d]&&f;if(g&&i&&t.manuallyAnimateOnMount&&(g=!1),p.protectedKeys={...h},!p.isActive&&null===y||!m&&!p.prevProp||di(m)||"boolean"==typeof m)continue;const v=Vs(p.prevProp,m);let x=v||d===o&&p.isActive&&!g&&f||e>c&&f,T=!1;const w=Array.isArray(m)?m:[m];let P=w.reduce(s(d),{});!1===y&&(P={});const{prevResolvedValues:S={}}=p,b={...S,...P},A=e=>{x=!0,u.has(e)&&(T=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=P[t],n=S[t];if(h.hasOwnProperty(t))continue;let i=!1;i=os(e)&&os(n)?!Ts(e,n):e!==n,i?null!=e?A(t):u.add(t):void 0!==e&&u.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=P,p.isActive&&(h={...h,...P}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(g&&v)||T)&&l.push(...w.map(t=>({animation:t,options:{type:d}})))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=ss(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach(n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=i??null}),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){if(n[e].isActive===i)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,i)),n[e].isActive=i;const s=o(e);for(const t in n)n[t].protectedKeys={};return s},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=Cs(),i=!0}}}function Vs(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Ts(e,t)}function Ms(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Cs(){return{animate:Ms(!0),whileInView:Ms(),whileHover:Ms(),whileTap:Ms(),whileDrag:Ms(),whileFocus:Ms(),exit:Ms()}}class Ds{constructor(t){this.isMounted=!1,this.node=t}update(){}}let ks=0;const Rs={animation:{Feature:class extends Ds{constructor(t){super(t),t.animationState||(t.animationState=Es(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();di(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Ds{constructor(){super(...arguments),this.id=ks++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function Ls(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function js(t){return{point:{x:t.pageX,y:t.pageY}}}function Bs(t,e,n,i){return Ls(t,e,(t=>e=>zn(e)&&t(e,js(e)))(n),i)}function Fs({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Os(t){return t.max-t.min}function Is(t,e,n,i=.5){t.origin=i,t.originPoint=Bt(e.min,e.max,t.origin),t.scale=Os(n)/Os(e),t.translate=Bt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Us(t,e,n,i){Is(t.x,e.x,n.x,i?i.originX:void 0),Is(t.y,e.y,n.y,i?i.originY:void 0)}function Ns(t,e,n){t.min=n.min+e.min,t.max=t.min+Os(e)}function Ws(t,e,n){t.min=e.min-n.min,t.max=t.min+Os(e)}function $s(t,e,n){Ws(t.x,e.x,n.x),Ws(t.y,e.y,n.y)}const Ys=()=>({x:{min:0,max:0},y:{min:0,max:0}});function Xs(t){return[t("x"),t("y")]}function Ks(t){return void 0===t||1===t}function zs({scale:t,scaleX:e,scaleY:n}){return!Ks(t)||!Ks(e)||!Ks(n)}function Hs(t){return zs(t)||qs(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function qs(t){return Gs(t.x)||Gs(t.y)}function Gs(t){return t&&"0%"!==t}function Zs(t,e,n){return n+e*(t-n)}function _s(t,e,n,i,s){return void 0!==s&&(t=Zs(t,s,i)),Zs(t,n,i)+e}function Js(t,e=0,n=1,i,s){t.min=_s(t.min,e,n,i,s),t.max=_s(t.max,e,n,i,s)}function Qs(t,{x:e,y:n}){Js(t.x,e.translate,e.scale,e.originPoint),Js(t.y,n.translate,n.scale,n.originPoint)}const to=.999999999999,eo=1.0000000000001;function no(t,e){t.min=t.min+e,t.max=t.max+e}function io(t,e,n,i,s=.5){Js(t,e,n,Bt(t.min,t.max,s),i)}function so(t,e){io(t.x,e.x,e.scaleX,e.scale,e.originX),io(t.y,e.y,e.scaleY,e.scale,e.originY)}function oo(t,e){return Fs(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const ro=({current:t})=>t?t.ownerDocument.defaultView:null,ao=(t,e)=>Math.abs(t-e);class lo{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=co(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=ao(t.x,e.x),i=ao(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=q;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=uo(e,this.transformPagePoint),z.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=co("pointercancel"===t.type?this.lastMoveEventInfo:uo(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!zn(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=uo(js(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=q;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,co(o,this.history)),this.removeListeners=v(Bs(this.contextWindow,"pointermove",this.handlePointerMove),Bs(this.contextWindow,"pointerup",this.handlePointerUp),Bs(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),H(this.updatePoint)}}function uo(t,e){return e?{point:e(t.point)}:t}function ho(t,e){return{x:t.x-e.x,y:t.y-e.y}}function co({point:t},e){return{point:t,delta:ho(t,mo(e)),offset:ho(t,po(e)),velocity:fo(e,.1)}}function po(t){return t[0]}function mo(t){return t[t.length-1]}function fo(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=mo(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>w(e)));)n--;if(!i)return{x:0,y:0};const o=P(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function yo(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function go(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const vo=.35;function xo(t,e,n){return{min:To(t,e),max:To(t,n)}}function To(t,e){return"number"==typeof t?t:t[e]||0}const wo=new WeakMap;class Po{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new lo(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(js(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?Wn[o]?null:(Wn[o]=!0,()=>{Wn[o]=!1}):Wn.x||Wn.y?null:(Wn.x=Wn.y=!0,()=>{Wn.x=Wn.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Xs(t=>{let e=this.getAxisMotionValue(t).get()||0;if(gt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Os(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&z.postRender(()=>s(t,e)),ls(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Xs(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:ro(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&z.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!So(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Bt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Bt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,i=this.constraints;t&&wi(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!n)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:yo(t.x,n,s),y:yo(t.y,e,i)}}(n.layoutBox,t),this.elastic=function(t=vo){return!1===t?t=0:!0===t&&(t=vo),{x:xo(t,"left","right"),y:xo(t,"top","bottom")}}(e),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&Xs(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!wi(t))return!1;const n=t.current;h(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");const{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=oo(t,n),{scroll:s}=e;return s&&(no(i.x,s.offset.x),no(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:go(t.x,e.x),y:go(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Fs(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Xs(r=>{if(!So(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,h=i?40:1e7,c={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,c)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return ls(this.visualElement,t),n.start(fs(t,n,0,e,this.visualElement,!1))}stopAnimation(){Xs(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Xs(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Xs(e=>{const{drag:n}=this.getProps();if(!So(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Bt(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!wi(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Xs(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Os(t),s=Os(e);return s>i?n=x(e.min,e.max-i,t.min):i>s&&(n=x(t.min,t.max-s,e.min)),l(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Xs(e=>{if(!So(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Bt(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;wo.set(this.visualElement,this);const t=Bs(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();wi(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),z.read(e);const s=Ls(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Xs(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=vo,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function So(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const bo=t=>(e,n)=>{t&&z.postRender(()=>t(e,n))};const Ao={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Eo(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Vo={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!vt.test(t))return t;t=parseFloat(t)}return`${Eo(t,e.target.x)}% ${Eo(t,e.target.y)}%`}},Mo={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Rt.parse(t);if(s.length>5)return i;const o=Rt.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Bt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};class Co extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;!function(t){for(const e in t)Di[e]=t[e],tt(e)&&(Di[e].isCSSVariable=!0)}(ko),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Ao.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,{projection:o}=n;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||z.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Nn.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Do(i){const[s,r]=function(t=!0){const n=e.useContext(o);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:r}=n,a=e.useId();e.useEffect(()=>{if(t)return r(a)},[t]);const l=e.useCallback(()=>t&&s&&s(a),[a,s,t]);return!i&&s?[!1,l]:[!0]}(),a=e.useContext(n);return t.jsx(Co,{...i,layoutGroup:a,switchLayoutGroup:e.useContext(Ai),isPresent:s,safeToRemove:r})}const ko={borderRadius:{...Vo,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Vo,borderTopRightRadius:Vo,borderBottomLeftRadius:Vo,borderBottomRightRadius:Vo,boxShadow:Mo};const Ro=(t,e)=>t.depth-e.depth;class Lo{constructor(){this.children=[],this.isDirty=!1}add(t){r(this.children,t),this.isDirty=!0}remove(t){a(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Ro),this.isDirty=!1,this.children.forEach(t)}}const jo=["TopLeft","TopRight","BottomLeft","BottomRight"],Bo=jo.length,Fo=t=>"string"==typeof t?parseFloat(t):t,Oo=t=>"number"==typeof t||vt.test(t);function Io(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Uo=Wo(0,.5,B),No=Wo(.5,.95,y);function Wo(t,e,n){return i=>i<t?0:i>e?1:n(x(t,e,i))}function $o(t,e){t.min=e.min,t.max=e.max}function Yo(t,e){$o(t.x,e.x),$o(t.y,e.y)}function Xo(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function Ko(t,e,n,i,s){return t=Zs(t-=e,1/n,i),void 0!==s&&(t=Zs(t,1/s,i)),t}function zo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){gt.test(e)&&(e=parseFloat(e),e=Bt(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Bt(o.min,o.max,i);t===o&&(a-=e),t.min=Ko(t.min,e,n,a,s),t.max=Ko(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const Ho=["x","scaleX","originX"],qo=["y","scaleY","originY"];function Go(t,e,n,i){zo(t.x,e,Ho,n?n.x:void 0,i?i.x:void 0),zo(t.y,e,qo,n?n.y:void 0,i?i.y:void 0)}function Zo(t){return 0===t.translate&&1===t.scale}function _o(t){return Zo(t.x)&&Zo(t.y)}function Jo(t,e){return t.min===e.min&&t.max===e.max}function Qo(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function tr(t,e){return Qo(t.x,e.x)&&Qo(t.y,e.y)}function er(t){return Os(t.x)/Os(t.y)}function nr(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class ir{constructor(){this.members=[]}add(t){r(this.members,t),t.scheduleRender()}remove(t){if(a(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const sr=["","X","Y","Z"],or={visibility:"hidden"};let rr=0;function ar(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function lr(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=us(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",z,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&lr(i)}function ur({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=e?.()){this.id=rr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(dr),this.nodes.forEach(xr),this.nodes.forEach(Tr),this.nodes.forEach(pr)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Lo)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;var n;this.isSVG=Qn(e)&&!(Qn(n=e)&&"svg"===n.tagName),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=J.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(H(i),t(o-e))};return z.setup(i,!0),()=>H(i)}(i,250),Ao.hasAnimatedSinceResize&&(Ao.hasAnimatedSinceResize=!1,this.nodes.forEach(vr))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Er,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!tr(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const e={...Tn(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||vr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),H(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(wr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&lr(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let s=0;s<this.path.length;s++){const t=this.path[s];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(fr);this.isUpdating||this.nodes.forEach(yr),this.isUpdating=!1,this.nodes.forEach(gr),this.nodes.forEach(hr),this.nodes.forEach(cr),this.clearAllSnapshots();const t=J.now();q.delta=l(0,1e3/60,t-q.timestamp),q.timestamp=t,q.isProcessing=!0,G.update.process(q),G.preRender.process(q),G.render.process(q),q.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Nn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(mr),this.sharedNodes.forEach(Pr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,z.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){z.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||Os(this.snapshot.measuredBox.x)||Os(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!_o(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&this.instance&&(e||Hs(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),Cr((i=n).x),Cr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(kr))){const{scroll:t}=this.root;t&&(no(e.x,t.offset.x),no(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(Yo(e,t),this.scroll?.wasRoot)return e;for(let n=0;n<this.path.length;n++){const i=this.path[n],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Yo(e,t),no(e.x,s.offset.x),no(e.y,s.offset.y))}return e}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Yo(n,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&so(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),Hs(t.latestValues)&&so(n,t.latestValues)}return Hs(this.latestValues)&&so(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Yo(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!Hs(t.latestValues))continue;zs(t.latestValues)&&t.updateSnapshot();const i=Ys();Yo(i,t.measurePageBox()),Go(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return Hs(this.latestValues)&&Go(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==q.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const n=Boolean(this.resumingFrom)||this!==e;if(!(t||n&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:i,layoutId:s}=this.options;if(this.layout&&(i||s)){if(this.resolvedRelativeTargetAt=q.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},$s(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Yo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var o,r,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),o=this.target,r=this.relativeTarget,a=this.relativeParent.target,Ns(o.x,r.x,a.x),Ns(o.y,r.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Yo(this.target,this.layout.layoutBox),Qs(this.target,this.targetDelta)):Yo(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},$s(this.relativeTargetOrigin,this.target,t.target),Yo(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!zs(this.parent.latestValues)&&!qs(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let n=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(n=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===q.timestamp&&(n=!1),n)return;const{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!s)return;Yo(this.layoutCorrected,this.layout.layoutBox);const o=this.treeScale.x,r=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&so(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Qs(t,r)),i&&Hs(o.latestValues)&&so(t,o.latestValues))}e.x<eo&&e.x>to&&(e.x=1),e.y<eo&&e.y>to&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(Xo(this.prevProjectionDelta.x,this.projectionDelta.x),Xo(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Us(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===o&&this.treeScale.y===r&&nr(this.projectionDelta.x,this.prevProjectionDelta.x)&&nr(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,h=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Ar));let c;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,y;Sr(o.x,t.x,n),Sr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&($s(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,y=n,br(p.x,m.x,f.x,y),br(p.y,m.y,f.y,y),c&&(l=this.relativeTarget,d=c,Jo(l.x,d.x)&&Jo(l.y,d.y))&&(this.isProjectionDirty=!1),c||(c={x:{min:0,max:0},y:{min:0,max:0}}),Yo(c,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Bt(0,n.opacity??1,Uo(i)),t.opacityExit=Bt(e.opacity??1,0,No(i))):o&&(t.opacity=Bt(e.opacity??1,n.opacity??1,i));for(let r=0;r<Bo;r++){const s=`border${jo[r]}Radius`;let o=Io(e,s),a=Io(n,s);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||Oo(o)===Oo(a)?(t[s]=Math.max(Bt(Fo(o),Fo(a),i),0),(gt.test(a)||gt.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=Bt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,h,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(H(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=z.update(()=>{Ao.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=Un(0)),this.currentAnimation=function(t,e,n){const i=ti(t)?t:Un(t);return i.start(fs("",i,e,n)),i.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&Dr(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Os(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Os(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Yo(e,n),so(e,s),Us(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new ir);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&ar("z",t,i,this.animationValues);for(let s=0;s<sr.length;s++)ar(`rotate${sr[s]}`,t,i,this.animationValues),ar(`skew${sr[s]}`,t,i,this.animationValues);t.render();for(const s in i)t.setStaticValue(s,i[s]),this.animationValues&&(this.animationValues[s]=i[s]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return or;const e={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=Zi(t?.pointerEvents)||"",e.transform=n?n(this.latestValues,""):"none",e;const i=this.getLead();if(!this.projectionDelta||!this.layout||!i.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Zi(t?.pointerEvents)||""),this.hasProjected&&!Hs(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}const s=i.animationValues||i.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=n?.z||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),n&&(e.transform=n(s,e.transform));const{x:o,y:r}=this.projectionDelta;e.transformOrigin=`${100*o.origin}% ${100*r.origin}% 0`,i.animationValues?e.opacity=i===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=i===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const a in Di){if(void 0===s[a])continue;const{correct:t,applyTo:n,isCSSVariable:o}=Di[a],r="none"===e.transform?s[a]:t(s[a],i);if(n){const t=n.length;for(let i=0;i<t;i++)e[n[i]]=r}else o?this.options.visualElement.renderState.vars[a]=r:e[a]=r}return this.options.layoutId&&(e.pointerEvents=i===this?Zi(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(fr),this.root.sharedNodes.clear()}}}function hr(t){t.updateLayout()}function cr(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:n,measuredBox:i}=t.layout,{animationType:s}=t.options,o=e.source!==t.layout.source;"size"===s?Xs(t=>{const i=o?e.measuredBox[t]:e.layoutBox[t],s=Os(i);i.min=n[t].min,i.max=i.min+s}):Dr(s,e.layoutBox,n)&&Xs(i=>{const s=o?e.measuredBox[i]:e.layoutBox[i],r=Os(n[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Us(r,n,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Us(a,t.applyTransform(i,!0),e.measuredBox):Us(a,n,e.layoutBox);const l=!_o(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};$s(r,e.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};$s(a,n,o.layoutBox),tr(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:n,snapshot:e,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function dr(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function pr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function mr(t){t.clearSnapshot()}function fr(t){t.clearMeasurements()}function yr(t){t.isLayoutDirty=!1}function gr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function vr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function xr(t){t.resolveTargetDelta()}function Tr(t){t.calcProjection()}function wr(t){t.resetSkewAndRotation()}function Pr(t){t.removeLeadSnapshot()}function Sr(t,e,n){t.translate=Bt(e.translate,0,n),t.scale=Bt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function br(t,e,n,i){t.min=Bt(e.min,n.min,i),t.max=Bt(e.max,n.max,i)}function Ar(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Er={duration:.45,ease:[.4,0,.1,1]},Vr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Mr=Vr("applewebkit/")&&!Vr("chrome/")?Math.round:y;function Cr(t){t.min=Mr(t.min),t.max=Mr(t.max)}function Dr(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=er(e),s=er(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function kr(t){return t!==t.root&&t.scroll?.wasRoot}const Rr=ur({attachResizeListener:(t,e)=>Ls(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Lr={current:void 0},jr=ur({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Lr.current){const t=new Rr({});t.mount(window),t.setOptions({layoutScroll:!0}),Lr.current=t}return Lr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),Br={pan:{Feature:class extends Ds{constructor(){super(...arguments),this.removePointerDownListener=y}onPointerDown(t){this.session=new lo(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ro(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:bo(t),onStart:bo(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&z.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=Bs(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Ds{constructor(t){super(t),this.removeGroupControls=y,this.removeListeners=y,this.controls=new Po(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||y}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:jr,MeasureLayout:Do}};function Fr(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&z.postRender(()=>s(e,js(e)))}function Or(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&z.postRender(()=>s(e,js(e)))}const Ir=new WeakMap,Ur=new WeakMap,Nr=t=>{const e=Ir.get(t.target);e&&e(t)},Wr=t=>{t.forEach(Nr)};function $r(t,e,n){const i=function({root:t,...e}){const n=t||document;Ur.has(n)||Ur.set(n,{});const i=Ur.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Wr,{root:t,...e})),i[s]}(e);return Ir.set(t,n),i.observe(t),()=>{Ir.delete(t),i.unobserve(t)}}const Yr={some:0,all:1};const Xr={inView:{Feature:class extends Ds{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:Yr[i]};return $r(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Ds{mount(){const{current:t}=this.node;t&&(this.unmount=Jn(t,(t,e)=>(Or(this.node,e,"Start"),(t,{success:e})=>Or(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Ds{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=v(Ls(this.node.current,"focus",()=>this.onFocus()),Ls(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Ds{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,n={}){const[i,s,o]=Yn(t,n),r=t=>{if(!Xn(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{Xn(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach(t=>{t.addEventListener("pointerenter",r,s)}),o}(t,(t,e)=>(Fr(this.node,e,"Start"),t=>Fr(this.node,t,"End"))))}unmount(){}}}},Kr={layout:{ProjectionNode:jr,MeasureLayout:Do}},zr={current:null},Hr={current:!1};const qr=new WeakMap;const Gr=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Zr{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Je,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=J.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,z.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=yi(e),this.isVariantNode=gi(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const c in h){const t=h[c];void 0!==a[c]&&ti(t)&&t.set(a[c],!1)}}mount(t){this.current=t,qr.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),Hr.current||function(){if(Hr.current=!0,i)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>zr.current=t.matches;t.addListener(e),e()}else zr.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||zr.current),A(!0!==this.shouldReduceMotion,"You have Reduced Motion enabled on your device. Animations may not appear as expected."),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),H(this.notifyUpdate),H(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=We.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&z.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in oi){const e=oi[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<Gr.length;n++){const e=Gr[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(ti(s))t.addValue(i,s);else if(ti(o))t.addValue(i,Un(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Un(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Un(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var i;return null!=n&&("string"==typeof n&&(d(n)||m(n))?n=parseFloat(n):(i=n,!ei.find(Pn(i))&&Rt.test(e)&&(n=jn(t,e))),this.setBaseTarget(t,ti(n)?n.get():n)),ti(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const i=Gi(this.props,e,this.presenceContext?.custom);i&&(n=i[t])}if(e&&void 0!==n)return n;const i=this.getBaseTargetFromProps(this.props,t);return void 0===i||ti(i)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:i}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class _r extends Zr{constructor(){super(...arguments),this.KeyframeResolver=Fn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ti(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function Jr(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}class Qr extends _r{constructor(){super(...arguments),this.type="html",this.renderInstance=Jr}readValueFromInstance(t,e){if(We.has(e))return this.projection?.isProjecting?Oe(e):((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Ie(n,e)})(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(tt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return oo(t,e)}build(t,e,n){ji(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Qi(t,e,n)}}const ta=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ea extends _r{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ys}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(We.has(e)){const t=Ln(e);return t&&t.default||0}return e=ta.has(e)?e:Si(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return es(t,e,n)}build(t,e,n){Wi(t,e,this.isSVGTag,n.transformTemplate,n.style)}renderInstance(t,e,n,i){!function(t,e,n,i){Jr(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(ta.has(s)?s:Si(s),e.attrs[s])}(t,e,0,i)}mount(t){this.isSVGTag=Yi(t.tagName),super.mount(t)}}const na=hi(is({...Rs,...Xr,...Br,...Kr},(t,n)=>zi(t)?new ea(n):new Qr(n,{allowProjection:t!==e.Fragment})));export{na as m};
