/**
 * 🧠 TESTE DE INTEGRAÇÃO COMPLETA - MEMORY GAME
 * Teste para verificar se todos os 11 coletores estão integrados corretamente
 */

import { MemoryGameCollectorsHub } from './src/games/MemoryGame/collectors/index.js';

async function testarIntegracaoMemoryGame() {
  console.log('🧠 TESTE DE INTEGRAÇÃO - MEMORY GAME');
  console.log('=====================================');
  
  try {
    // 1. Inicializar hub
    console.log('\n1. Inicializando MemoryGameCollectorsHub...');
    const hub = new MemoryGameCollectorsHub();
    
    console.log(`✅ Hub inicializado com ${Object.keys(hub.collectors).length} coletores`);
    console.log('📋 Coletores disponíveis:', Object.keys(hub.collectors));
    
    // 2. Dados de teste do MemoryGame
    const gameDataTeste = {
      sessionId: 'test_memory_session_001',
      userId: 'test_user',
      gameType: 'MemoryGame',
      difficulty: 'medium',
      theme: 'space_stars',
      totalCards: 12,
      completedMatches: 6,
      sessionDuration: 120000,
      cards: [
        { id: 1, type: 'star', matched: true },
        { id: 2, type: 'moon', matched: true },
        { id: 3, type: 'rocket', matched: false }
      ],
      flips: [
        { cardId: 1, timestamp: 1000, matched: true, position: { row: 0, col: 0 } },
        { cardId: 2, timestamp: 2000, matched: true, position: { row: 0, col: 1 } },
        { cardId: 3, timestamp: 3000, matched: false, position: { row: 1, col: 0 } }
      ],
      matches: [
        { cardIds: [1, 4], timestamp: 1500, reactionTime: 800 },
        { cardIds: [2, 5], timestamp: 2500, reactionTime: 900 }
      ]
    };
    
    // 3. Testar análise completa
    console.log('\n2. Executando análise completa...');
    const startTime = Date.now();
    
    const resultado = await hub.runCompleteAnalysis(gameDataTeste);
    
    const tempoExecucao = Date.now() - startTime;
    console.log(`✅ Análise completa executada em ${tempoExecucao}ms`);
    
    // 4. Verificar resultados
    console.log('\n3. Verificando resultados dos coletores...');
    const coletoresExecutados = Object.keys(resultado.collectorsResults || {});
    console.log(`📊 Coletores que retornaram resultados: ${coletoresExecutados.length}`);
    
    coletoresExecutados.forEach(coletor => {
      console.log(`   ✅ ${coletor}: OK`);
    });
    
    // 5. Verificar métricas integradas
    console.log('\n4. Verificando métricas integradas...');
    if (resultado.integratedMetrics) {
      console.log('✅ Métricas integradas geradas');
      console.log('📈 Métricas disponíveis:', Object.keys(resultado.integratedMetrics));
    }
    
    // 6. Verificar recomendações
    console.log('\n5. Verificando recomendações terapêuticas...');
    if (resultado.recommendations && Array.isArray(resultado.recommendations)) {
      console.log(`✅ ${resultado.recommendations.length} recomendações geradas`);
    }
    
    // 7. Estatísticas finais
    console.log('\n🎯 RESUMO DA INTEGRAÇÃO:');
    console.log('========================');
    console.log(`✅ Coletores integrados: ${Object.keys(hub.collectors).length}/11`);
    console.log(`✅ Coletores executados: ${coletoresExecutados.length}/11`);
    console.log(`✅ Tempo de execução: ${tempoExecucao}ms`);
    console.log(`✅ Métricas integradas: ${resultado.integratedMetrics ? 'SIM' : 'NÃO'}`);
    console.log(`✅ Recomendações: ${resultado.recommendations?.length || 0}`);
    
    if (coletoresExecutados.length === 11) {
      console.log('\n🎉 SUCESSO: Todos os 11 coletores do MemoryGame estão integrados e funcionando!');
      console.log('🧠 Sistema de análise multissensorial 300% expandido - ATIVO');
    } else {
      console.log(`\n⚠️  ATENÇÃO: ${11 - coletoresExecutados.length} coletores não executaram corretamente`);
    }
    
    return true;
    
  } catch (error) {
    console.error('\n❌ ERRO NO TESTE DE INTEGRAÇÃO:', error.message);
    console.error('Stack:', error.stack);
    return false;
  }
}

// Executar teste
testarIntegracaoMemoryGame()
  .then(sucesso => {
    if (sucesso) {
      console.log('\n✅ Teste de integração finalizado com SUCESSO');
      process.exit(0);
    } else {
      console.log('\n❌ Teste de integração FALHOU');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 ERRO CRÍTICO:', error);
    process.exit(1);
  });
