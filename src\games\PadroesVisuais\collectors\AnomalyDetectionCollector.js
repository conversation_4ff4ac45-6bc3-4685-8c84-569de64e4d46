/**
 * @file AnomalyDetectionCollector.js
 * @description Coletor especializado para análise de detecção de anomalias visuais
 * @version 3.0.0
 */

export class AnomalyDetectionCollector {
  constructor() {
    this.name = 'AnomalyDetectionCollector';
    this.version = '3.0.0';
    this.description = 'Analisa habilidades de detecção e identificação de anomalias em padrões visuais';
  }

  async collect(gameState) {
    try {
      const patternElements = gameState.patternElements || [];
      const detectedAnomalies = gameState.detectedAnomalies || [];
      const actualAnomalies = gameState.actualAnomalies || [];
      const detectionTime = gameState.detectionTime || 0;

      return {
        // Métricas de detecção
        detectionAccuracy: this.calculateDetectionAccuracy(actualAnomalies, detectedAnomalies),
        anomalyIdentificationScore: this.assessAnomalyIdentification(actualAnomalies, detectedAnomalies),
        falsePositiveRate: this.calculateFalsePositiveRate(patternElements, detectedAnomalies, actualAnomalies),
        detectionSensitivity: this.calculateDetectionSensitivity(actualAnomalies, detectedAnomalies),
        
        // Análise visual
        visualDiscriminationAccuracy: this.assessVisualDiscrimination(patternElements, actualAnomalies, detectedAnomalies),
        patternDeviationRecognition: this.evaluatePatternDeviationRecognition(gameState),
        visualAttentionScope: this.assessVisualAttentionScope(patternElements, detectedAnomalies),
        perceptualSensitivity: this.evaluatePerceptualSensitivity(actualAnomalies, detectedAnomalies),
        
        // Processamento cognitivo
        anomalyCategorizationAbility: this.assessAnomalyCategorization(detectedAnomalies, actualAnomalies),
        patternComprehensionIndex: this.evaluatePatternComprehension(gameState),
        cognitiveFlexibilityInDetection: this.assessCognitiveFlexibility(gameState.sessionAttempts || []),
        
        // Eficiência e estratégia
        detectionEfficiency: this.calculateDetectionEfficiency(detectionTime, actualAnomalies.length),
        systematicSearchStrategy: this.assessSearchStrategy(gameState.searchSequence || []),
        attentionalFocusControl: this.evaluateAttentionalFocus(gameState),
        
        timestamp: Date.now(),
        sessionId: gameState.sessionId || 'unknown'
      };
    } catch (error) {
      console.error('Erro no AnomalyDetectionCollector:', error);
      return null;
    }
  }

  calculateDetectionAccuracy(actual, detected) {
    if (!actual.length && !detected.length) return 100;
    if (!actual.length) return detected.length === 0 ? 100 : 0;
    
    let correctDetections = 0;
    
    for (const actualAnomaly of actual) {
      const wasDetected = detected.some(detectedAnomaly => 
        this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (wasDetected) correctDetections++;
    }
    
    // Penaliza detecções incorretas
    const falseDetections = this.countFalseDetections(actual, detected);
    const accuracy = (correctDetections / actual.length) * 100;
    const penalty = (falseDetections / Math.max(detected.length, 1)) * 20;
    
    return Math.max(0, accuracy - penalty);
  }

  anomaliesMatch(anomaly1, anomaly2) {
    // Verifica se duas anomalias se referem ao mesmo elemento
    if (anomaly1.elementId && anomaly2.elementId) {
      return anomaly1.elementId === anomaly2.elementId;
    }
    
    if (anomaly1.position && anomaly2.position) {
      const distance = Math.sqrt(
        Math.pow(anomaly1.position.x - anomaly2.position.x, 2) +
        Math.pow(anomaly1.position.y - anomaly2.position.y, 2)
      );
      return distance < 50; // Tolerância de 50 pixels
    }
    
    return false;
  }

  countFalseDetections(actual, detected) {
    let falseDetections = 0;
    
    for (const detectedAnomaly of detected) {
      const isActualAnomaly = actual.some(actualAnomaly => 
        this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (!isActualAnomaly) falseDetections++;
    }
    
    return falseDetections;
  }

  assessAnomalyIdentification(actual, detected) {
    if (!actual.length) return detected.length === 0 ? 100 : 0;
    
    let identificationScore = 0;
    
    for (const actualAnomaly of actual) {
      const matchingDetection = detected.find(detectedAnomaly => 
        this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      
      if (matchingDetection) {
        const identificationAccuracy = this.assessAnomalyTypeIdentification(actualAnomaly, matchingDetection);
        identificationScore += identificationAccuracy;
      }
    }
    
    return actual.length > 0 ? identificationScore / actual.length : 0;
  }

  assessAnomalyTypeIdentification(actual, detected) {
    // Avalia se o tipo de anomalia foi identificado corretamente
    const typeScore = actual.type === detected.type ? 50 : 0;
    const severityScore = this.assessSeverityIdentification(actual.severity, detected.severity);
    const descriptionScore = this.assessDescriptionAccuracy(actual.description, detected.description);
    
    return typeScore + severityScore + descriptionScore;
  }

  assessSeverityIdentification(actualSeverity, detectedSeverity) {
    if (!actualSeverity || !detectedSeverity) return 25;
    
    const severityLevels = { low: 1, medium: 2, high: 3 };
    const actualLevel = severityLevels[actualSeverity] || 2;
    const detectedLevel = severityLevels[detectedSeverity] || 2;
    
    const difference = Math.abs(actualLevel - detectedLevel);
    return Math.max(0, 25 - difference * 8);
  }

  assessDescriptionAccuracy(actualDescription, detectedDescription) {
    if (!actualDescription || !detectedDescription) return 25;
    
    // Análise simples de similaridade textual
    const actualWords = actualDescription.toLowerCase().split(' ');
    const detectedWords = detectedDescription.toLowerCase().split(' ');
    
    const commonWords = actualWords.filter(word => detectedWords.includes(word));
    const similarity = commonWords.length / Math.max(actualWords.length, detectedWords.length);
    
    return similarity * 25;
  }

  calculateFalsePositiveRate(patternElements, detected, actual) {
    const totalElements = patternElements.length;
    const falsePositives = this.countFalseDetections(actual, detected);
    
    if (totalElements === 0) return 0;
    
    const falsePositiveRate = (falsePositives / totalElements) * 100;
    return Math.min(100, falsePositiveRate);
  }

  calculateDetectionSensitivity(actual, detected) {
    if (!actual.length) return 100;
    
    let detectedCount = 0;
    
    for (const actualAnomaly of actual) {
      const wasDetected = detected.some(detectedAnomaly => 
        this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      if (wasDetected) detectedCount++;
    }
    
    return (detectedCount / actual.length) * 100;
  }

  assessVisualDiscrimination(patternElements, actualAnomalies, detectedAnomalies) {
    if (!patternElements.length) return 50;
    
    const discriminationTasks = this.identifyDiscriminationTasks(patternElements, actualAnomalies);
    let discriminationScore = 0;
    
    for (const task of discriminationTasks) {
      const taskScore = this.evaluateDiscriminationTask(task, detectedAnomalies);
      discriminationScore += taskScore;
    }
    
    return discriminationTasks.length > 0 ? discriminationScore / discriminationTasks.length : 50;
  }

  identifyDiscriminationTasks(patternElements, actualAnomalies) {
    const tasks = [];
    
    for (const anomaly of actualAnomalies) {
      const anomalyElement = this.findElementById(patternElements, anomaly.elementId);
      if (!anomalyElement) continue;
      
      // Encontra elementos similares para determinar dificuldade de discriminação
      const similarElements = patternElements.filter(element => 
        element.id !== anomaly.elementId && 
        this.calculateVisualSimilarity(element, anomalyElement) > 0.6
      );
      
      tasks.push({
        anomaly: anomaly,
        element: anomalyElement,
        similarElements: similarElements,
        difficulty: this.calculateDiscriminationDifficulty(anomalyElement, similarElements, anomaly)
      });
    }
    
    return tasks;
  }

  findElementById(elements, elementId) {
    return elements.find(element => element.id === elementId);
  }

  calculateVisualSimilarity(element1, element2) {
    let similarity = 0;
    const features = ['shape', 'color', 'size', 'pattern'];
    
    for (const feature of features) {
      if (element1[feature] === element2[feature]) {
        similarity += 0.25;
      }
    }
    
    return similarity;
  }

  calculateDiscriminationDifficulty(anomalyElement, similarElements, anomaly) {
    // Mais elementos similares = maior dificuldade
    const similarityDifficulty = Math.min(similarElements.length / 5, 1) * 30;
    
    // Tipo de anomalia afeta dificuldade
    const anomalyTypeDifficulty = this.getAnomalyTypeDifficulty(anomaly.type);
    
    // Severidade da anomalia afeta dificuldade (menos severa = mais difícil)
    const severityDifficulty = this.getSeverityDifficulty(anomaly.severity);
    
    return similarityDifficulty + anomalyTypeDifficulty + severityDifficulty;
  }

  getAnomalyTypeDifficulty(anomalyType) {
    const difficultyMap = {
      color: 20,
      shape: 30,
      size: 25,
      position: 40,
      orientation: 35,
      pattern: 45,
      texture: 50
    };
    
    return difficultyMap[anomalyType] || 30;
  }

  getSeverityDifficulty(severity) {
    const difficultyMap = {
      high: 10,   // Fácil de detectar
      medium: 25, // Moderadamente difícil
      low: 40     // Difícil de detectar
    };
    
    return difficultyMap[severity] || 25;
  }

  evaluateDiscriminationTask(task, detectedAnomalies) {
    // Verifica se a anomalia foi detectada corretamente
    const wasDetected = detectedAnomalies.some(detected => 
      this.anomaliesMatch(task.anomaly, detected)
    );
    
    if (wasDetected) {
      // Bônus baseado na dificuldade da tarefa
      return Math.min(100, 100 - task.difficulty);
    } else {
      // Penalidade baseada na facilidade da tarefa
      return Math.max(0, task.difficulty - 50);
    }
  }

  evaluatePatternDeviationRecognition(gameState) {
    const patternRules = gameState.patternRules || [];
    const detectedAnomalies = gameState.detectedAnomalies || [];
    
    if (!patternRules.length) return 50;
    
    let recognitionScore = 0;
    
    for (const rule of patternRules) {
      const ruleViolations = this.findRuleViolations(rule, gameState.patternElements || []);
      const detectedViolations = detectedAnomalies.filter(anomaly => 
        anomaly.violatedRule === rule.id
      );
      
      const ruleRecognitionScore = this.calculateRuleRecognitionScore(ruleViolations, detectedViolations);
      recognitionScore += ruleRecognitionScore;
    }
    
    return recognitionScore / patternRules.length;
  }

  findRuleViolations(rule, patternElements) {
    const violations = [];
    
    switch (rule.type) {
      case 'color_sequence':
        violations.push(...this.findColorSequenceViolations(rule, patternElements));
        break;
      case 'size_progression':
        violations.push(...this.findSizeProgressionViolations(rule, patternElements));
        break;
      case 'shape_pattern':
        violations.push(...this.findShapePatternViolations(rule, patternElements));
        break;
      case 'spatial_arrangement':
        violations.push(...this.findSpatialArrangementViolations(rule, patternElements));
        break;
      default:
        break;
    }
    
    return violations;
  }

  findColorSequenceViolations(rule, elements) {
    const violations = [];
    const expectedSequence = rule.sequence || [];
    
    for (let i = 0; i < elements.length; i++) {
      const expectedColor = expectedSequence[i % expectedSequence.length];
      if (elements[i].color !== expectedColor) {
        violations.push({
          elementId: elements[i].id,
          type: 'color_violation',
          expected: expectedColor,
          actual: elements[i].color
        });
      }
    }
    
    return violations;
  }

  findSizeProgressionViolations(rule, elements) {
    const violations = [];
    const sizeValues = { small: 1, medium: 2, large: 3 };
    
    for (let i = 1; i < elements.length; i++) {
      const currentSize = sizeValues[elements[i].size] || 2;
      const previousSize = sizeValues[elements[i - 1].size] || 2;
      
      const expectedProgression = rule.direction === 'ascending' ? 1 : -1;
      const actualProgression = Math.sign(currentSize - previousSize);
      
      if (actualProgression !== expectedProgression && actualProgression !== 0) {
        violations.push({
          elementId: elements[i].id,
          type: 'size_violation',
          expected: rule.direction,
          actual: actualProgression > 0 ? 'ascending' : 'descending'
        });
      }
    }
    
    return violations;
  }

  findShapePatternViolations(rule, elements) {
    const violations = [];
    const expectedPattern = rule.pattern || [];
    
    for (let i = 0; i < elements.length; i++) {
      const expectedShape = expectedPattern[i % expectedPattern.length];
      if (elements[i].shape !== expectedShape) {
        violations.push({
          elementId: elements[i].id,
          type: 'shape_violation',
          expected: expectedShape,
          actual: elements[i].shape
        });
      }
    }
    
    return violations;
  }

  findSpatialArrangementViolations(rule, elements) {
    const violations = [];
    
    // Verifica alinhamento esperado
    if (rule.alignment === 'horizontal') {
      violations.push(...this.findHorizontalAlignmentViolations(elements));
    } else if (rule.alignment === 'vertical') {
      violations.push(...this.findVerticalAlignmentViolations(elements));
    }
    
    return violations;
  }

  findHorizontalAlignmentViolations(elements) {
    const violations = [];
    const positions = elements.map(e => e.position).filter(pos => pos);
    
    if (positions.length < 2) return violations;
    
    const averageY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    const tolerance = 20; // 20 pixels de tolerância
    
    for (const element of elements) {
      if (element.position && Math.abs(element.position.y - averageY) > tolerance) {
        violations.push({
          elementId: element.id,
          type: 'alignment_violation',
          expected: 'horizontal',
          deviation: Math.abs(element.position.y - averageY)
        });
      }
    }
    
    return violations;
  }

  findVerticalAlignmentViolations(elements) {
    const violations = [];
    const positions = elements.map(e => e.position).filter(pos => pos);
    
    if (positions.length < 2) return violations;
    
    const averageX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const tolerance = 20; // 20 pixels de tolerância
    
    for (const element of elements) {
      if (element.position && Math.abs(element.position.x - averageX) > tolerance) {
        violations.push({
          elementId: element.id,
          type: 'alignment_violation',
          expected: 'vertical',
          deviation: Math.abs(element.position.x - averageX)
        });
      }
    }
    
    return violations;
  }

  calculateRuleRecognitionScore(actualViolations, detectedViolations) {
    if (!actualViolations.length && !detectedViolations.length) return 100;
    if (!actualViolations.length) return detectedViolations.length === 0 ? 100 : 0;
    
    let recognizedViolations = 0;
    
    for (const actualViolation of actualViolations) {
      const wasRecognized = detectedViolations.some(detected => 
        detected.elementId === actualViolation.elementId &&
        detected.type === actualViolation.type
      );
      if (wasRecognized) recognizedViolations++;
    }
    
    return (recognizedViolations / actualViolations.length) * 100;
  }

  assessVisualAttentionScope(patternElements, detectedAnomalies) {
    if (!patternElements.length) return 50;
    
    const attentionMetrics = {
      spatialCoverage: this.assessSpatialCoverage(patternElements, detectedAnomalies),
      focusDistribution: this.assessFocusDistribution(detectedAnomalies),
      attentionPersistence: this.assessAttentionPersistence(detectedAnomalies)
    };
    
    return (
      attentionMetrics.spatialCoverage * 0.5 +
      attentionMetrics.focusDistribution * 0.3 +
      attentionMetrics.attentionPersistence * 0.2
    );
  }

  assessSpatialCoverage(patternElements, detectedAnomalies) {
    // Avalia cobertura espacial da atenção visual
    const patternBounds = this.calculatePatternBounds(patternElements);
    const detectionBounds = this.calculateDetectionBounds(detectedAnomalies);
    
    if (!patternBounds || !detectionBounds) return 50;
    
    const coverageRatio = this.calculateBoundsCoverage(patternBounds, detectionBounds);
    return coverageRatio * 100;
  }

  calculatePatternBounds(elements) {
    const positions = elements.map(e => e.position).filter(pos => pos);
    if (positions.length === 0) return null;
    
    return {
      minX: Math.min(...positions.map(pos => pos.x)),
      maxX: Math.max(...positions.map(pos => pos.x)),
      minY: Math.min(...positions.map(pos => pos.y)),
      maxY: Math.max(...positions.map(pos => pos.y))
    };
  }

  calculateDetectionBounds(detections) {
    const positions = detections.map(d => d.position).filter(pos => pos);
    if (positions.length === 0) return null;
    
    return {
      minX: Math.min(...positions.map(pos => pos.x)),
      maxX: Math.max(...positions.map(pos => pos.x)),
      minY: Math.min(...positions.map(pos => pos.y)),
      maxY: Math.max(...positions.map(pos => pos.y))
    };
  }

  calculateBoundsCoverage(patternBounds, detectionBounds) {
    // Calcula sobreposição entre bounds do padrão e detecções
    const overlapX = Math.max(0, Math.min(patternBounds.maxX, detectionBounds.maxX) - 
                                 Math.max(patternBounds.minX, detectionBounds.minX));
    const overlapY = Math.max(0, Math.min(patternBounds.maxY, detectionBounds.maxY) - 
                                 Math.max(patternBounds.minY, detectionBounds.minY));
    
    const overlapArea = overlapX * overlapY;
    const patternArea = (patternBounds.maxX - patternBounds.minX) * (patternBounds.maxY - patternBounds.minY);
    
    return patternArea > 0 ? overlapArea / patternArea : 0;
  }

  assessFocusDistribution(detectedAnomalies) {
    // Avalia distribuição do foco atencional
    if (detectedAnomalies.length < 2) return 100;
    
    const positions = detectedAnomalies.map(d => d.position).filter(pos => pos);
    if (positions.length < 2) return 50;
    
    // Calcula dispersão das detecções
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    
    let totalDispersion = 0;
    for (const pos of positions) {
      const distance = Math.sqrt(
        Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
      );
      totalDispersion += distance;
    }
    
    const averageDispersion = totalDispersion / positions.length;
    
    // Dispersão moderada indica boa distribuição
    const optimalDispersion = 150; // pixels
    const distributionScore = Math.max(0, 100 - Math.abs(averageDispersion - optimalDispersion) / 2);
    
    return distributionScore;
  }

  assessAttentionPersistence(detectedAnomalies) {
    // Avalia persistência da atenção (detecções múltiplas na mesma área)
    if (detectedAnomalies.length < 2) return 100;
    
    const clusters = this.clusterDetections(detectedAnomalies);
    let persistenceScore = 0;
    
    for (const cluster of clusters) {
      if (cluster.length > 1) {
        // Múltiplas detecções na mesma área podem indicar boa persistência
        persistenceScore += Math.min(cluster.length * 20, 100);
      } else {
        persistenceScore += 50; // Detecção única
      }
    }
    
    return clusters.length > 0 ? persistenceScore / clusters.length : 100;
  }

  clusterDetections(detections) {
    const clusters = [];
    const visited = new Set();
    const clusterRadius = 50; // pixels
    
    for (let i = 0; i < detections.length; i++) {
      if (visited.has(i) || !detections[i].position) continue;
      
      const cluster = [i];
      visited.add(i);
      
      for (let j = i + 1; j < detections.length; j++) {
        if (visited.has(j) || !detections[j].position) continue;
        
        const distance = Math.sqrt(
          Math.pow(detections[j].position.x - detections[i].position.x, 2) +
          Math.pow(detections[j].position.y - detections[i].position.y, 2)
        );
        
        if (distance <= clusterRadius) {
          cluster.push(j);
          visited.add(j);
        }
      }
      
      clusters.push(cluster);
    }
    
    return clusters;
  }

  evaluatePerceptualSensitivity(actualAnomalies, detectedAnomalies) {
    if (!actualAnomalies.length) return 50;
    
    let sensitivityScore = 0;
    
    for (const actualAnomaly of actualAnomalies) {
      const sensitivityLevel = this.calculateAnomalySensitivity(actualAnomaly);
      const wasDetected = detectedAnomalies.some(detected => 
        this.anomaliesMatch(actualAnomaly, detected)
      );
      
      if (wasDetected) {
        // Bônus baseado na dificuldade de detecção
        sensitivityScore += Math.min(100, 50 + sensitivityLevel);
      } else {
        // Penalidade baseada na facilidade de detecção
        sensitivityScore += Math.max(0, 50 - sensitivityLevel);
      }
    }
    
    return sensitivityScore / actualAnomalies.length;
  }

  calculateAnomalySensitivity(anomaly) {
    // Calcula sensibilidade necessária para detectar anomalia
    const severityScore = this.getSeverityDifficulty(anomaly.severity);
    const typeScore = this.getAnomalyTypeDifficulty(anomaly.type);
    const contrastScore = this.calculateContrastScore(anomaly);
    
    return (severityScore + typeScore + contrastScore) / 3;
  }

  calculateContrastScore(anomaly) {
    // Calcula score de contraste (menor contraste = maior dificuldade)
    const contrastLevel = anomaly.contrast || 'medium';
    const contrastScores = {
      high: 10,
      medium: 30,
      low: 50
    };
    
    return contrastScores[contrastLevel] || 30;
  }

  assessAnomalyCategorization(detectedAnomalies, actualAnomalies) {
    if (!detectedAnomalies.length) return actualAnomalies.length === 0 ? 100 : 0;
    
    const categorizationMetrics = {
      typeAccuracy: this.assessTypeCategorizationAccuracy(detectedAnomalies, actualAnomalies),
      severityAccuracy: this.assessSeverityCategorization(detectedAnomalies, actualAnomalies),
      categoryConsistency: this.assessCategoryConsistency(detectedAnomalies)
    };
    
    return (
      categorizationMetrics.typeAccuracy * 0.5 +
      categorizationMetrics.severityAccuracy * 0.3 +
      categorizationMetrics.categoryConsistency * 0.2
    );
  }

  assessTypeCategorizationAccuracy(detected, actual) {
    if (!actual.length) return 50;
    
    let accurateTyping = 0;
    
    for (const actualAnomaly of actual) {
      const matchingDetection = detected.find(detectedAnomaly => 
        this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      
      if (matchingDetection && matchingDetection.type === actualAnomaly.type) {
        accurateTyping++;
      }
    }
    
    return (accurateTyping / actual.length) * 100;
  }

  assessSeverityCategorization(detected, actual) {
    if (!actual.length) return 50;
    
    let accurateSeverity = 0;
    
    for (const actualAnomaly of actual) {
      const matchingDetection = detected.find(detectedAnomaly => 
        this.anomaliesMatch(actualAnomaly, detectedAnomaly)
      );
      
      if (matchingDetection) {
        const severityAccuracy = this.assessSeverityIdentification(
          actualAnomaly.severity, 
          matchingDetection.severity
        );
        accurateSeverity += severityAccuracy;
      }
    }
    
    return actual.length > 0 ? accurateSeverity / actual.length : 50;
  }

  assessCategoryConsistency(detectedAnomalies) {
    // Avalia consistência na categorização
    if (detectedAnomalies.length < 2) return 100;
    
    const typeGroups = this.groupAnomaliesByType(detectedAnomalies);
    let consistencyScore = 0;
    
    for (const [type, anomalies] of Object.entries(typeGroups)) {
      const groupConsistency = this.calculateGroupConsistency(anomalies);
      consistencyScore += groupConsistency;
    }
    
    const typeCount = Object.keys(typeGroups).length;
    return typeCount > 0 ? consistencyScore / typeCount : 100;
  }

  groupAnomaliesByType(anomalies) {
    const groups = {};
    
    for (const anomaly of anomalies) {
      const type = anomaly.type || 'unknown';
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(anomaly);
    }
    
    return groups;
  }

  calculateGroupConsistency(anomalies) {
    // Calcula consistência dentro de um grupo de anomalias do mesmo tipo
    if (anomalies.length < 2) return 100;
    
    const severities = anomalies.map(a => a.severity).filter(s => s);
    const uniqueSeverities = new Set(severities);
    
    // Consistência baseada na variação de severidade
    const severityConsistency = severities.length > 0 ? 
      100 - ((uniqueSeverities.size - 1) / severities.length) * 50 : 100;
    
    return Math.max(0, severityConsistency);
  }

  evaluatePatternComprehension(gameState) {
    const patternRules = gameState.patternRules || [];
    const detectedAnomalies = gameState.detectedAnomalies || [];
    const patternElements = gameState.patternElements || [];
    
    if (!patternRules.length) return 50;
    
    const comprehensionMetrics = {
      ruleUnderstanding: this.assessRuleUnderstanding(patternRules, detectedAnomalies),
      patternRecognition: this.assessPatternRecognition(patternElements, gameState),
      contextualUnderstanding: this.assessContextualUnderstanding(gameState)
    };
    
    return (
      comprehensionMetrics.ruleUnderstanding * 0.5 +
      comprehensionMetrics.patternRecognition * 0.3 +
      comprehensionMetrics.contextualUnderstanding * 0.2
    );
  }

  assessRuleUnderstanding(patternRules, detectedAnomalies) {
    let understandingScore = 0;
    
    for (const rule of patternRules) {
      const ruleViolations = detectedAnomalies.filter(anomaly => 
        anomaly.violatedRule === rule.id
      );
      
      const ruleComplexity = this.calculateRuleComplexity(rule);
      const detectionQuality = this.assessRuleDetectionQuality(rule, ruleViolations);
      
      // Bonus por detectar violações de regras complexas
      const complexityBonus = (ruleComplexity / 100) * detectionQuality;
      understandingScore += detectionQuality + complexityBonus;
    }
    
    return patternRules.length > 0 ? understandingScore / patternRules.length : 50;
  }

  calculateRuleComplexity(rule) {
    const complexityScores = {
      color_sequence: 20,
      size_progression: 30,
      shape_pattern: 40,
      spatial_arrangement: 50,
      composite: 60
    };
    
    return complexityScores[rule.type] || 30;
  }

  assessRuleDetectionQuality(rule, detectedViolations) {
    // Avalia qualidade da detecção de violações de regra
    if (!detectedViolations.length) return 30; // Sem detecções
    
    let qualityScore = 0;
    
    for (const violation of detectedViolations) {
      const accuracy = this.assessViolationAccuracy(rule, violation);
      qualityScore += accuracy;
    }
    
    return detectedViolations.length > 0 ? qualityScore / detectedViolations.length : 30;
  }

  assessViolationAccuracy(rule, violation) {
    // Avalia precisão na identificação de violação de regra
    const baseScore = 50;
    
    // Bônus por especificar detalhes corretos
    if (violation.expected && violation.actual) {
      return baseScore + 30; // Análise detalhada
    } else if (violation.type) {
      return baseScore + 20; // Tipo identificado
    } else {
      return baseScore; // Detecção básica
    }
  }

  assessPatternRecognition(patternElements, gameState) {
    // Avalia reconhecimento do padrão geral
    const patternType = gameState.patternType || 'unknown';
    const identifiedPattern = gameState.identifiedPattern || 'unknown';
    
    if (patternType === identifiedPattern) {
      return 100; // Padrão identificado corretamente
    } else if (identifiedPattern !== 'unknown') {
      return 60; // Tentativa de identificação, mas incorreta
    } else {
      return 30; // Padrão não identificado
    }
  }

  assessContextualUnderstanding(gameState) {
    // Avalia compreensão contextual da tarefa
    const difficulty = gameState.difficulty || 'medium';
    const detectionStrategy = gameState.detectionStrategy || 'unknown';
    const detectedAnomalies = gameState.detectedAnomalies || [];
    
    let contextScore = 50; // Base
    
    // Bônus por estratégia apropriada à dificuldade
    if (this.isStrategyAppropriate(detectionStrategy, difficulty)) {
      contextScore += 25;
    }
    
    // Bônus por número apropriado de detecções
    if (this.isDetectionCountAppropriate(detectedAnomalies.length, difficulty)) {
      contextScore += 25;
    }
    
    return Math.min(100, contextScore);
  }

  isStrategyAppropriate(strategy, difficulty) {
    const appropriateStrategies = {
      easy: ['systematic', 'quick'],
      medium: ['systematic', 'focused'],
      hard: ['systematic', 'analytical']
    };
    
    return appropriateStrategies[difficulty]?.includes(strategy) || false;
  }

  isDetectionCountAppropriate(detectionCount, difficulty) {
    const expectedRanges = {
      easy: { min: 1, max: 3 },
      medium: { min: 2, max: 5 },
      hard: { min: 3, max: 8 }
    };
    
    const range = expectedRanges[difficulty] || { min: 1, max: 5 };
    return detectionCount >= range.min && detectionCount <= range.max;
  }

  assessCognitiveFlexibility(sessionAttempts) {
    if (sessionAttempts.length < 3) return 50;
    
    const flexibilityMetrics = {
      strategyAdaptation: this.assessStrategyAdaptation(sessionAttempts),
      errorLearning: this.assessErrorLearning(sessionAttempts),
      difficultyAdjustment: this.assessDifficultyAdjustment(sessionAttempts)
    };
    
    return (
      flexibilityMetrics.strategyAdaptation * 0.4 +
      flexibilityMetrics.errorLearning * 0.3 +
      flexibilityMetrics.difficultyAdjustment * 0.3
    );
  }

  assessStrategyAdaptation(attempts) {
    const strategies = attempts.map(attempt => attempt.detectionStrategy || 'unknown');
    const uniqueStrategies = new Set(strategies);
    
    // Mais estratégias = maior flexibilidade
    const adaptationScore = (uniqueStrategies.size / attempts.length) * 100;
    return Math.min(100, adaptationScore);
  }

  assessErrorLearning(attempts) {
    // Avalia aprendizagem com erros
    let learningScore = 0;
    
    for (let i = 1; i < attempts.length; i++) {
      const currentAccuracy = attempts[i].detectionAccuracy || 0;
      const previousAccuracy = attempts[i - 1].detectionAccuracy || 0;
      
      if (currentAccuracy > previousAccuracy) {
        learningScore += 20; // Melhoria
      } else if (currentAccuracy === previousAccuracy) {
        learningScore += 10; // Manutenção
      }
    }
    
    return Math.min(100, learningScore);
  }

  assessDifficultyAdjustment(attempts) {
    // Avalia adaptação a diferentes níveis de dificuldade
    const difficulties = attempts.map(attempt => attempt.difficulty).filter(d => d);
    const uniqueDifficulties = new Set(difficulties);
    
    if (uniqueDifficulties.size < 2) return 50; // Sem variação de dificuldade
    
    let adjustmentScore = 0;
    
    for (let i = 1; i < attempts.length; i++) {
      const currentDiff = attempts[i].difficulty;
      const previousDiff = attempts[i - 1].difficulty;
      
      if (currentDiff !== previousDiff) {
        const currentAccuracy = attempts[i].detectionAccuracy || 0;
        if (currentAccuracy > 60) { // Adaptação bem-sucedida
          adjustmentScore += 25;
        }
      }
    }
    
    return Math.min(100, adjustmentScore);
  }

  calculateDetectionEfficiency(detectionTime, anomalyCount) {
    if (anomalyCount === 0) return 100;
    
    const expectedTimePerAnomaly = 3000; // 3 segundos por anomalia
    const totalExpectedTime = anomalyCount * expectedTimePerAnomaly;
    
    const efficiency = totalExpectedTime / Math.max(detectionTime, 1000);
    return Math.min(100, efficiency * 100);
  }

  assessSearchStrategy(searchSequence) {
    if (!searchSequence.length) return 50;
    
    const strategyMetrics = {
      systematicness: this.assessSystematicSearch(searchSequence),
      coverage: this.assessSearchCoverage(searchSequence),
      efficiency: this.assessSearchEfficiency(searchSequence)
    };
    
    return (
      strategyMetrics.systematicness * 0.4 +
      strategyMetrics.coverage * 0.3 +
      strategyMetrics.efficiency * 0.3
    );
  }

  assessSystematicSearch(searchSequence) {
    // Avalia se a busca foi sistemática
    if (searchSequence.length < 3) return 50;
    
    const positions = searchSequence.map(step => step.position).filter(pos => pos);
    if (positions.length < 3) return 50;
    
    // Verifica padrão de busca (linha por linha, coluna por coluna, etc.)
    const searchPattern = this.identifySearchPattern(positions);
    
    const systematicScores = {
      linear: 90,
      grid: 95,
      spiral: 85,
      random: 30,
      clustered: 60
    };
    
    return systematicScores[searchPattern] || 50;
  }

  identifySearchPattern(positions) {
    // Identifica padrão de busca visual
    if (this.isLinearSearch(positions)) return 'linear';
    if (this.isGridSearch(positions)) return 'grid';
    if (this.isSpiralSearch(positions)) return 'spiral';
    if (this.isClusteredSearch(positions)) return 'clustered';
    
    return 'random';
  }

  isLinearSearch(positions) {
    // Verifica se a busca seguiu um padrão linear
    if (positions.length < 3) return false;
    
    let linearX = true;
    let linearY = true;
    
    for (let i = 2; i < positions.length; i++) {
      const deltaX1 = positions[i - 1].x - positions[i - 2].x;
      const deltaX2 = positions[i].x - positions[i - 1].x;
      const deltaY1 = positions[i - 1].y - positions[i - 2].y;
      const deltaY2 = positions[i].y - positions[i - 1].y;
      
      if (Math.abs(deltaX1 - deltaX2) > 50) linearX = false;
      if (Math.abs(deltaY1 - deltaY2) > 50) linearY = false;
    }
    
    return linearX || linearY;
  }

  isGridSearch(positions) {
    // Verifica se a busca seguiu um padrão de grade
    const tolerance = 50;
    const xPositions = [...new Set(positions.map(p => Math.round(p.x / tolerance) * tolerance))];
    const yPositions = [...new Set(positions.map(p => Math.round(p.y / tolerance) * tolerance))];
    
    // Se há múltiplas posições X e Y distintas, pode ser busca em grade
    return xPositions.length >= 2 && yPositions.length >= 2;
  }

  isSpiralSearch(positions) {
    // Verifica se a busca seguiu um padrão espiral
    if (positions.length < 5) return false;
    
    // Calcula centro da busca
    const centerX = positions.reduce((sum, p) => sum + p.x, 0) / positions.length;
    const centerY = positions.reduce((sum, p) => sum + p.y, 0) / positions.length;
    
    // Verifica se as distâncias do centro aumentam progressivamente
    const distances = positions.map(p => 
      Math.sqrt(Math.pow(p.x - centerX, 2) + Math.pow(p.y - centerY, 2))
    );
    
    let increasing = 0;
    for (let i = 1; i < distances.length; i++) {
      if (distances[i] >= distances[i - 1] * 0.8) increasing++;
    }
    
    return increasing / (distances.length - 1) > 0.7;
  }

  isClusteredSearch(positions) {
    // Verifica se a busca foi agrupada em clusters
    const clusters = this.identifySearchClusters(positions);
    return clusters.length > 1 && clusters.length < positions.length * 0.7;
  }

  identifySearchClusters(positions) {
    const clusters = [];
    const visited = new Set();
    const clusterRadius = 100;
    
    for (let i = 0; i < positions.length; i++) {
      if (visited.has(i)) continue;
      
      const cluster = [i];
      visited.add(i);
      
      for (let j = i + 1; j < positions.length; j++) {
        if (visited.has(j)) continue;
        
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) +
          Math.pow(positions[j].y - positions[i].y, 2)
        );
        
        if (distance <= clusterRadius) {
          cluster.push(j);
          visited.add(j);
        }
      }
      
      clusters.push(cluster);
    }
    
    return clusters;
  }

  assessSearchCoverage(searchSequence) {
    // Avalia cobertura da busca visual
    const positions = searchSequence.map(step => step.position).filter(pos => pos);
    
    if (positions.length < 2) return 50;
    
    const searchBounds = this.calculateDetectionBounds(positions.map(pos => ({ position: pos })));
    const searchArea = (searchBounds.maxX - searchBounds.minX) * (searchBounds.maxY - searchBounds.minY);
    
    // Assume área total de 800x600 pixels
    const totalArea = 800 * 600;
    const coverageRatio = Math.min(1, searchArea / totalArea);
    
    return coverageRatio * 100;
  }

  assessSearchEfficiency(searchSequence) {
    // Avalia eficiência da busca (sem redundâncias)
    const positions = searchSequence.map(step => step.position).filter(pos => pos);
    
    if (positions.length < 2) return 100;
    
    // Conta revisitas a áreas já exploradas
    let redundantSearches = 0;
    const exploredAreas = [];
    
    for (const position of positions) {
      const isRedundant = exploredAreas.some(area => 
        Math.sqrt(Math.pow(position.x - area.x, 2) + Math.pow(position.y - area.y, 2)) < 75
      );
      
      if (isRedundant) {
        redundantSearches++;
      } else {
        exploredAreas.push(position);
      }
    }
    
    const efficiency = Math.max(0, 100 - (redundantSearches / positions.length) * 100);
    return efficiency;
  }

  evaluateAttentionalFocus(gameState) {
    const detectedAnomalies = gameState.detectedAnomalies || [];
    const searchSequence = gameState.searchSequence || [];
    const responseTime = gameState.responseTime || 0;
    
    const focusMetrics = {
      selectionAccuracy: this.assessFocusSelectionAccuracy(detectedAnomalies, gameState.actualAnomalies || []),
      distractorResistance: this.assessDistractorResistance(gameState),
      focusMaintenance: this.assessFocusMaintenance(searchSequence, responseTime)
    };
    
    return (
      focusMetrics.selectionAccuracy * 0.4 +
      focusMetrics.distractorResistance * 0.3 +
      focusMetrics.focusMaintenance * 0.3
    );
  }

  assessFocusSelectionAccuracy(detected, actual) {
    // Avalia precisão na seleção do foco atencional
    if (!actual.length) return detected.length === 0 ? 100 : 50;
    
    const correctSelections = detected.filter(detectedAnomaly => 
      actual.some(actualAnomaly => this.anomaliesMatch(actualAnomaly, detectedAnomaly))
    );
    
    return (correctSelections.length / actual.length) * 100;
  }

  assessDistractorResistance(gameState) {
    // Avalia resistência a distratores
    const distractors = gameState.distractors || [];
    const detectedAnomalies = gameState.detectedAnomalies || [];
    
    if (!distractors.length) return 100;
    
    const distractorDetections = detectedAnomalies.filter(detected => 
      distractors.some(distractor => this.anomaliesMatch(distractor, detected))
    );
    
    const resistanceScore = Math.max(0, 100 - (distractorDetections.length / distractors.length) * 50);
    return resistanceScore;
  }

  assessFocusMaintenance(searchSequence, responseTime) {
    // Avalia manutenção do foco durante a tarefa
    if (!searchSequence.length) return 50;
    
    const focusStability = this.calculateFocusStability(searchSequence);
    const timeEfficiency = this.calculateTimeEfficiency(responseTime, searchSequence.length);
    
    return (focusStability + timeEfficiency) / 2;
  }

  calculateFocusStability(searchSequence) {
    // Calcula estabilidade do foco (menor variação = maior estabilidade)
    if (searchSequence.length < 3) return 100;
    
    const dwellTimes = [];
    for (let i = 1; i < searchSequence.length; i++) {
      const dwellTime = searchSequence[i].timestamp - searchSequence[i - 1].timestamp;
      dwellTimes.push(dwellTime);
    }
    
    if (dwellTimes.length === 0) return 100;
    
    const meanDwellTime = dwellTimes.reduce((sum, time) => sum + time, 0) / dwellTimes.length;
    const variance = dwellTimes.reduce((sum, time) => sum + Math.pow(time - meanDwellTime, 2), 0) / dwellTimes.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Menor desvio padrão = maior estabilidade
    const stability = Math.max(0, 100 - (standardDeviation / meanDwellTime) * 100);
    return stability;
  }

  calculateTimeEfficiency(responseTime, searchSteps) {
    // Calcula eficiência temporal
    const expectedTimePerStep = 1500; // 1.5 segundos por passo de busca
    const expectedTotalTime = searchSteps * expectedTimePerStep;
    
    const efficiency = expectedTotalTime / Math.max(responseTime, 1000);
    return Math.min(100, efficiency * 100);
  }
}
