/**
 * ⏱️ TIME RESPONSE COLLECTOR
 * Coletor especializado em análise temporal e padrões de tempo de resposta
 * Portal Betina V3 - FASE 2.1
 */

export class TimeResponseCollector {
  constructor() {
    this.temporalCategories = {
      impulsive: 'Impulsivo (< 1s)',
      quick: '<PERSON><PERSON><PERSON><PERSON> (1-2s)',
      normal: 'Normal (2-3s)',
      deliberate: 'Deliberado (3-5s)',
      slow: 'Lento (> 5s)'
    };
    
    this.responsePatterns = {
      accelerating: 'Acelerando',
      decelerating: 'Desacelerando',
      stable: 'Estável',
      oscillating: 'Oscilante',
      fatiguing: 'Fadiga'
    };
    
    this.contextualFactors = {
      difficulty: 'Dific<PERSON>ade da Tarefa',
      position: 'Posição no Grid',
      sequence: 'Ordem Sequencial',
      accuracy: 'Precisão da Resposta'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Análise principal dos padrões temporais
   */
  async analyze(data) {
    try {
      console.log('⏱️ TimeResponseCollector: Iniciando análise temporal...');
      
      if (!this.validateTemporalData(data)) {
        return this.generateFallbackAnalysis('Dados temporais inválidos');
      }

      const temporalData = this.extractTemporalData(data);
      
      // Análises principais
      const responseTimeProfile = this.analyzeResponseTimeProfile(temporalData);
      const temporalPatterns = this.analyzeTemporalPatterns(temporalData);
      const contextualAnalysis = this.analyzeContextualFactors(temporalData);
      const cognitiveProcessing = this.analyzeCognitiveProcessing(temporalData);
      const fatigueAssessment = this.assessFatiguePatterns(temporalData);
      const consistencyMetrics = this.calculateConsistencyMetrics(temporalData);

      const results = {
        // Perfil básico de tempo de resposta
        averageResponseTime: responseTimeProfile.average,
        medianResponseTime: responseTimeProfile.median,
        responseTimeRange: responseTimeProfile.range,
        responseTimeCategory: responseTimeProfile.category,
        
        // Padrões temporais
        temporalPattern: temporalPatterns.primaryPattern,
        patternStability: temporalPatterns.stability,
        trendDirection: temporalPatterns.trend,
        changeRate: temporalPatterns.changeRate,
        
        // Variabilidade e consistência
        responseTimeVariability: consistencyMetrics.variability,
        consistencyIndex: consistencyMetrics.consistency,
        stabilityScore: consistencyMetrics.stability,
        outlierRate: consistencyMetrics.outliers,
        
        // Análise contextual
        difficultyImpact: contextualAnalysis.difficultyEffect,
        positionImpact: contextualAnalysis.positionEffect,
        sequenceImpact: contextualAnalysis.sequenceEffect,
        accuracyCorrelation: contextualAnalysis.accuracyCorrelation,
        
        // Processamento cognitivo
        processingSpeed: cognitiveProcessing.speed,
        decisionTime: cognitiveProcessing.decisionTime,
        executionTime: cognitiveProcessing.executionTime,
        cognitiveLoad: cognitiveProcessing.load,
        
        // Fadiga e resistência
        fatigueIndex: fatigueAssessment.fatigueIndex,
        enduranceScore: fatigueAssessment.endurance,
        performanceDecline: fatigueAssessment.decline,
        recoveryCapacity: fatigueAssessment.recovery,
        
        // Métricas de desenvolvimento
        attentionSustainability: this.assessAttentionSustainability(temporalData),
        impulseControl: this.assessImpulseControl(temporalData),
        adaptiveSpeed: this.assessAdaptiveSpeed(temporalData),
        
        // Comparações e benchmarks
        percentileRanking: this.calculatePercentileRanking(responseTimeProfile.average, data.difficulty),
        ageAppropriate: this.assessAgeAppropriateness(responseTimeProfile.average, data.userAge),
        difficultyAppropriate: this.assessDifficultyAppropriateness(responseTimeProfile, data.difficulty),
        
        // Contexto da análise
        totalResponses: temporalData.responseTimes.length,
        sessionDuration: temporalData.sessionDuration,
        difficulty: temporalData.difficulty,
        
        // Recomendações específicas
        recommendations: this.generateTemporalRecommendations({
          responseTimeProfile, temporalPatterns, contextualAnalysis, 
          cognitiveProcessing, fatigueAssessment, consistencyMetrics
        }),
        
        // Metadados
        analysisTimestamp: new Date().toISOString(),
        collectorVersion: '2.1.0',
        dataQuality: this.assessDataQuality(temporalData)
      };

      console.log('✅ TimeResponseCollector: Análise concluída:', {
        averageRT: Math.round(results.averageResponseTime),
        category: results.responseTimeCategory,
        pattern: results.temporalPattern,
        consistency: Math.round(results.consistencyIndex * 100)
      });

      return results;

    } catch (error) {
      console.error('❌ TimeResponseCollector: Erro na análise:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados temporais de entrada
   */
  validateTemporalData(data) {
    if (!data) return false;
    
    // Verificar se há dados de tempo de resposta
    const hasResponseTimes = data.selectedItems || data.interactions || data.responseTimes;
    const hasTimestamps = data.timestamps || data.selectedItems;
    
    return hasResponseTimes && hasTimestamps;
  }

  /**
   * Extrai dados temporais dos dados de entrada
   */
  extractTemporalData(data) {
    let responseTimes = [];
    let timestamps = [];
    let accuracyData = [];
    let positionData = [];

    // Extrair dados das diferentes estruturas possíveis
    if (data.selectedItems && Array.isArray(data.selectedItems)) {
      responseTimes = data.selectedItems
        .map(item => item.responseTime)
        .filter(rt => rt && rt > 0);
      
      timestamps = data.selectedItems
        .map(item => item.timestamp)
        .filter(ts => ts && ts > 0);
      
      accuracyData = data.selectedItems.map(item => item.correct || false);
      positionData = data.selectedItems.map((item, index) => item.itemIndex || index);
    } else if (data.responseTimes) {
      responseTimes = data.responseTimes.filter(rt => rt > 0);
    }

    // Calcular intervalos entre respostas
    const intervals = [];
    for (let i = 1; i < timestamps.length; i++) {
      intervals.push(timestamps[i] - timestamps[i - 1]);
    }

    return {
      responseTimes,
      timestamps,
      intervals,
      accuracyData,
      positionData,
      difficulty: data.difficulty || 'medium',
      sessionDuration: data.sessionDuration || 0,
      userAge: data.userAge || null,
      totalInteractions: responseTimes.length
    };
  }

  /**
   * Analisa perfil de tempo de resposta
   */
  analyzeResponseTimeProfile(temporalData) {
    const { responseTimes } = temporalData;
    
    if (responseTimes.length === 0) {
      return {
        average: 0,
        median: 0,
        range: { min: 0, max: 0 },
        category: 'unknown'
      };
    }

    // Estatísticas básicas
    const sorted = [...responseTimes].sort((a, b) => a - b);
    const average = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    const median = sorted[Math.floor(sorted.length / 2)];
    const min = sorted[0];
    const max = sorted[sorted.length - 1];
    
    // Quartis
    const q1 = sorted[Math.floor(sorted.length * 0.25)];
    const q3 = sorted[Math.floor(sorted.length * 0.75)];
    
    // Categorização
    const category = this.categorizeResponseTime(average);
    
    return {
      average,
      median,
      range: { min, max },
      quartiles: { q1, q3 },
      standardDeviation: this.calculateStandardDeviation(responseTimes),
      category,
      distribution: this.analyzeDistribution(responseTimes)
    };
  }

  /**
   * Categoriza tempo de resposta
   */
  categorizeResponseTime(averageTime) {
    if (averageTime < 1000) return 'impulsive';
    if (averageTime < 2000) return 'quick';
    if (averageTime < 3000) return 'normal';
    if (averageTime < 5000) return 'deliberate';
    return 'slow';
  }

  /**
   * Analisa padrões temporais ao longo da sessão
   */
  analyzeTemporalPatterns(temporalData) {
    const { responseTimes } = temporalData;
    
    if (responseTimes.length < 3) {
      return {
        primaryPattern: 'insufficient_data',
        stability: 0,
        trend: 'unknown',
        changeRate: 0
      };
    }

    // Análise de tendência
    const trend = this.calculateTrend(responseTimes);
    const changeRate = this.calculateChangeRate(responseTimes);
    
    // Identificar padrão principal
    const pattern = this.identifyPattern(responseTimes, trend);
    
    // Calcular estabilidade
    const stability = this.calculatePatternStability(responseTimes);
    
    return {
      primaryPattern: pattern,
      stability,
      trend: trend.direction,
      changeRate,
      trendStrength: trend.strength,
      cycles: this.detectCycles(responseTimes)
    };
  }

  /**
   * Calcula tendência dos tempos de resposta
   */
  calculateTrend(responseTimes) {
    const n = responseTimes.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = responseTimes;
    
    // Regressão linear simples
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    // Calcular força da correlação
    const meanX = sumX / n;
    const meanY = sumY / n;
    
    const ssXY = x.reduce((sum, val, i) => sum + (val - meanX) * (y[i] - meanY), 0);
    const ssXX = x.reduce((sum, val) => sum + (val - meanX) ** 2, 0);
    const ssYY = y.reduce((sum, val) => sum + (val - meanY) ** 2, 0);
    
    const correlation = ssXY / Math.sqrt(ssXX * ssYY);
    
    return {
      slope,
      intercept,
      correlation,
      strength: Math.abs(correlation),
      direction: slope > 100 ? 'increasing' : slope < -100 ? 'decreasing' : 'stable'
    };
  }

  /**
   * Identifica padrão principal
   */
  identifyPattern(responseTimes, trend) {
    const { correlation, slope } = trend;
    
    if (Math.abs(correlation) > 0.7) {
      return slope > 0 ? 'decelerating' : 'accelerating';
    }
    
    if (Math.abs(correlation) < 0.3) {
      // Verificar se é oscilante
      const oscillations = this.countOscillations(responseTimes);
      if (oscillations > responseTimes.length * 0.3) {
        return 'oscillating';
      }
      return 'stable';
    }
    
    // Verificar fadiga (aumento no final)
    const lastQuarter = responseTimes.slice(-Math.floor(responseTimes.length / 4));
    const firstQuarter = responseTimes.slice(0, Math.floor(responseTimes.length / 4));
    
    if (lastQuarter.length > 0 && firstQuarter.length > 0) {
      const lastAvg = lastQuarter.reduce((sum, rt) => sum + rt, 0) / lastQuarter.length;
      const firstAvg = firstQuarter.reduce((sum, rt) => sum + rt, 0) / firstQuarter.length;
      
      if (lastAvg > firstAvg * 1.5) {
        return 'fatiguing';
      }
    }
    
    return 'stable';
  }

  /**
   * Conta oscilações nos tempos de resposta
   */
  countOscillations(responseTimes) {
    let oscillations = 0;
    
    for (let i = 2; i < responseTimes.length; i++) {
      const prev = responseTimes[i - 2];
      const curr = responseTimes[i - 1];
      const next = responseTimes[i];
      
      // Detectar pico ou vale
      if ((curr > prev && curr > next) || (curr < prev && curr < next)) {
        oscillations++;
      }
    }
    
    return oscillations;
  }

  /**
   * Calcula taxa de mudança
   */
  calculateChangeRate(responseTimes) {
    if (responseTimes.length < 2) return 0;
    
    let totalChange = 0;
    for (let i = 1; i < responseTimes.length; i++) {
      totalChange += Math.abs(responseTimes[i] - responseTimes[i - 1]);
    }
    
    return totalChange / (responseTimes.length - 1);
  }

  /**
   * Calcula estabilidade do padrão
   */
  calculatePatternStability(responseTimes) {
    const variability = this.calculateCoefficientOfVariation(responseTimes);
    return Math.max(0, 1 - variability);
  }

  /**
   * Detecta ciclos nos tempos de resposta
   */
  detectCycles(responseTimes) {
    // Implementação simplificada de detecção de ciclos
    const cycles = [];
    const windowSize = Math.min(5, Math.floor(responseTimes.length / 3));
    
    for (let i = 0; i <= responseTimes.length - windowSize * 2; i++) {
      const pattern1 = responseTimes.slice(i, i + windowSize);
      const pattern2 = responseTimes.slice(i + windowSize, i + windowSize * 2);
      
      if (this.patternsAreSimilar(pattern1, pattern2)) {
        cycles.push({
          start: i,
          length: windowSize,
          pattern: pattern1
        });
      }
    }
    
    return cycles;
  }

  /**
   * Verifica se dois padrões são similares
   */
  patternsAreSimilar(pattern1, pattern2, threshold = 0.8) {
    if (pattern1.length !== pattern2.length) return false;
    
    const correlation = this.calculateCorrelation(pattern1, pattern2);
    return Math.abs(correlation) > threshold;
  }

  /**
   * Analisa fatores contextuais
   */
  analyzeContextualFactors(temporalData) {
    const { responseTimes, accuracyData, positionData, difficulty } = temporalData;
    
    return {
      difficultyEffect: this.analyzeDifficultyEffect(responseTimes, difficulty),
      positionEffect: this.analyzePositionEffect(responseTimes, positionData),
      sequenceEffect: this.analyzeSequenceEffect(responseTimes),
      accuracyCorrelation: this.analyzeAccuracyCorrelation(responseTimes, accuracyData)
    };
  }

  /**
   * Analisa efeito da dificuldade
   */
  analyzeDifficultyEffect(responseTimes, difficulty) {
    const averageRT = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    
    const expectedRT = {
      easy: 2000,
      medium: 2500,
      hard: 3500
    }[difficulty] || 2500;
    
    const ratio = averageRT / expectedRT;
    
    return {
      expectedTime: expectedRT,
      actualTime: averageRT,
      ratio,
      appropriate: ratio > 0.7 && ratio < 1.5
    };
  }

  /**
   * Analisa efeito da posição
   */
  analyzePositionEffect(responseTimes, positionData) {
    if (!positionData || positionData.length !== responseTimes.length) {
      return { effect: 'unknown', positions: {} };
    }

    const positionTimes = {};
    positionData.forEach((pos, index) => {
      if (!positionTimes[pos]) positionTimes[pos] = [];
      positionTimes[pos].push(responseTimes[index]);
    });

    // Calcular média por posição
    const positionAverages = {};
    Object.keys(positionTimes).forEach(pos => {
      const times = positionTimes[pos];
      positionAverages[pos] = times.reduce((sum, rt) => sum + rt, 0) / times.length;
    });

    return {
      effect: 'analyzed',
      positions: positionAverages,
      variation: this.calculatePositionVariation(positionAverages)
    };
  }

  /**
   * Analisa efeito sequencial
   */
  analyzeSequenceEffect(responseTimes) {
    if (responseTimes.length < 5) {
      return { effect: 'insufficient_data', trend: 'unknown' };
    }

    const segments = this.segmentResponseTimes(responseTimes, 3);
    const segmentAverages = segments.map(segment => 
      segment.reduce((sum, rt) => sum + rt, 0) / segment.length
    );

    const trend = this.calculateTrend(segmentAverages);
    
    return {
      effect: 'analyzed',
      segments: segmentAverages,
      trend: trend.direction,
      strength: trend.strength
    };
  }

  /**
   * Analisa correlação com precisão
   */
  analyzeAccuracyCorrelation(responseTimes, accuracyData) {
    if (!accuracyData || accuracyData.length !== responseTimes.length) {
      return { correlation: 0, effect: 'unknown' };
    }

    const correctTimes = [];
    const incorrectTimes = [];

    responseTimes.forEach((rt, index) => {
      if (accuracyData[index]) {
        correctTimes.push(rt);
      } else {
        incorrectTimes.push(rt);
      }
    });

    if (correctTimes.length === 0 || incorrectTimes.length === 0) {
      return { correlation: 0, effect: 'insufficient_data' };
    }

    const correctAvg = correctTimes.reduce((sum, rt) => sum + rt, 0) / correctTimes.length;
    const incorrectAvg = incorrectTimes.reduce((sum, rt) => sum + rt, 0) / incorrectTimes.length;

    return {
      correlation: (incorrectAvg - correctAvg) / correctAvg,
      correctAverage: correctAvg,
      incorrectAverage: incorrectAvg,
      effect: incorrectAvg > correctAvg ? 'slower_when_wrong' : 'faster_when_wrong'
    };
  }

  /**
   * Analisa processamento cognitivo
   */
  analyzeCognitiveProcessing(temporalData) {
    const { responseTimes } = temporalData;
    
    const averageRT = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    
    // Estimar componentes do tempo de resposta
    const decisionTime = Math.min(averageRT * 0.7, 2000); // Tempo de decisão
    const executionTime = averageRT - decisionTime; // Tempo de execução
    
    // Velocidade de processamento (inverso do tempo médio)
    const processingSpeed = Math.max(0, Math.min(1, (3000 - averageRT) / 3000));
    
    // Carga cognitiva baseada na variabilidade
    const variability = this.calculateCoefficientOfVariation(responseTimes);
    const cognitiveLoad = Math.min(1, variability);
    
    return {
      speed: processingSpeed,
      decisionTime,
      executionTime,
      load: cognitiveLoad,
      efficiency: processingSpeed * (1 - cognitiveLoad)
    };
  }

  /**
   * Avalia padrões de fadiga
   */
  assessFatiguePatterns(temporalData) {
    const { responseTimes } = temporalData;
    
    if (responseTimes.length < 6) {
      return {
        fatigueIndex: 0,
        endurance: 0.5,
        decline: 0,
        recovery: 0.5
      };
    }

    // Dividir em segmentos para análise
    const segmentSize = Math.floor(responseTimes.length / 3);
    const beginning = responseTimes.slice(0, segmentSize);
    const middle = responseTimes.slice(segmentSize, segmentSize * 2);
    const end = responseTimes.slice(segmentSize * 2);

    const beginningAvg = beginning.reduce((sum, rt) => sum + rt, 0) / beginning.length;
    const middleAvg = middle.reduce((sum, rt) => sum + rt, 0) / middle.length;
    const endAvg = end.reduce((sum, rt) => sum + rt, 0) / end.length;

    // Índice de fadiga (aumento do início para o fim)
    const fatigueIndex = Math.max(0, (endAvg - beginningAvg) / beginningAvg);
    
    // Resistência (capacidade de manter performance)
    const endurance = Math.max(0, 1 - fatigueIndex);
    
    // Declínio de performance
    const decline = (endAvg - beginningAvg) / beginningAvg;
    
    // Capacidade de recuperação (melhoria do meio para o fim)
    const recovery = middleAvg > endAvg ? (middleAvg - endAvg) / middleAvg : 0;

    return {
      fatigueIndex,
      endurance,
      decline,
      recovery,
      segments: {
        beginning: beginningAvg,
        middle: middleAvg,
        end: endAvg
      }
    };
  }

  /**
   * Calcula métricas de consistência
   */
  calculateConsistencyMetrics(temporalData) {
    const { responseTimes } = temporalData;
    
    if (responseTimes.length === 0) {
      return {
        variability: 0,
        consistency: 0.5,
        stability: 0.5,
        outliers: 0
      };
    }

    const mean = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    const stdDev = this.calculateStandardDeviation(responseTimes);
    
    // Coeficiente de variação
    const variability = mean > 0 ? stdDev / mean : 0;
    
    // Índice de consistência (inverso da variabilidade)
    const consistency = Math.max(0, 1 - Math.min(1, variability));
    
    // Estabilidade temporal
    const stability = this.calculateTemporalStability(responseTimes);
    
    // Taxa de outliers (valores > 2 desvios padrão)
    const outlierThreshold = mean + 2 * stdDev;
    const outlierCount = responseTimes.filter(rt => rt > outlierThreshold).length;
    const outlierRate = outlierCount / responseTimes.length;

    return {
      variability,
      consistency,
      stability,
      outliers: outlierRate,
      standardDeviation: stdDev,
      outlierThreshold
    };
  }

  /**
   * Avalia sustentabilidade da atenção
   */
  assessAttentionSustainability(temporalData) {
    const { responseTimes } = temporalData;
    
    // Análise da variabilidade ao longo do tempo
    const windowSize = Math.max(3, Math.floor(responseTimes.length / 5));
    const variabilities = [];
    
    for (let i = 0; i <= responseTimes.length - windowSize; i++) {
      const window = responseTimes.slice(i, i + windowSize);
      const variability = this.calculateCoefficientOfVariation(window);
      variabilities.push(variability);
    }
    
    // Sustentabilidade baseada na estabilidade da variabilidade
    const variabilityTrend = this.calculateTrend(variabilities);
    const sustainability = Math.max(0, 1 - Math.abs(variabilityTrend.slope));
    
    return sustainability;
  }

  /**
   * Avalia controle de impulso
   */
  assessImpulseControl(temporalData) {
    const { responseTimes } = temporalData;
    
    // Contar respostas muito rápidas (< 1 segundo)
    const impulsiveResponses = responseTimes.filter(rt => rt < 1000).length;
    const impulsiveRate = impulsiveResponses / responseTimes.length;
    
    // Controle de impulso (inverso da taxa de respostas impulsivas)
    return Math.max(0, 1 - impulsiveRate);
  }

  /**
   * Avalia velocidade adaptativa
   */
  assessAdaptiveSpeed(temporalData) {
    const { responseTimes, accuracyData } = temporalData;
    
    if (!accuracyData || accuracyData.length !== responseTimes.length) {
      return 0.5; // Valor neutro se não há dados de precisão
    }

    // Analisar se ajusta velocidade baseado na precisão
    let adaptiveAdjustments = 0;
    
    for (let i = 1; i < responseTimes.length; i++) {
      const prevAccurate = accuracyData[i - 1];
      const currentRT = responseTimes[i];
      const prevRT = responseTimes[i - 1];
      
      // Comportamento adaptativo: mais devagar após erro, mais rápido após acerto
      if (!prevAccurate && currentRT > prevRT * 1.1) {
        adaptiveAdjustments++;
      } else if (prevAccurate && currentRT < prevRT * 0.9) {
        adaptiveAdjustments++;
      }
    }
    
    return adaptiveAdjustments / (responseTimes.length - 1);
  }

  /**
   * Calcula ranking percentual
   */
  calculatePercentileRanking(averageRT, difficulty) {
    const benchmarks = {
      easy: { p25: 1500, p50: 2000, p75: 2800, p90: 3500 },
      medium: { p25: 1800, p50: 2500, p75: 3200, p90: 4000 },
      hard: { p25: 2200, p50: 3000, p75: 3800, p90: 4800 }
    };
    
    const benchmark = benchmarks[difficulty] || benchmarks.medium;
    
    if (averageRT <= benchmark.p25) return 75;
    if (averageRT <= benchmark.p50) return 50;
    if (averageRT <= benchmark.p75) return 25;
    if (averageRT <= benchmark.p90) return 10;
    return 5;
  }

  /**
   * Avalia adequação à idade
   */
  assessAgeAppropriateness(averageRT, userAge) {
    if (!userAge) return { appropriate: 'unknown', reason: 'Idade não informada' };
    
    // Benchmarks aproximados por idade (valores simplificados)
    const ageBenchmarks = {
      4: { min: 2000, max: 5000 },
      5: { min: 1800, max: 4500 },
      6: { min: 1600, max: 4000 },
      7: { min: 1400, max: 3500 },
      8: { min: 1200, max: 3000 },
      9: { min: 1000, max: 2800 },
      10: { min: 900, max: 2500 }
    };
    
    const benchmark = ageBenchmarks[userAge] || ageBenchmarks[8];
    const appropriate = averageRT >= benchmark.min && averageRT <= benchmark.max;
    
    return {
      appropriate,
      expectedRange: benchmark,
      actualTime: averageRT,
      reason: appropriate ? 'Dentro do esperado para a idade' : 
              averageRT < benchmark.min ? 'Mais rápido que esperado' : 'Mais lento que esperado'
    };
  }

  /**
   * Avalia adequação à dificuldade
   */
  assessDifficultyAppropriateness(responseTimeProfile, difficulty) {
    const { average } = responseTimeProfile;
    const appropriateness = this.analyzeDifficultyEffect([average], difficulty);
    
    return {
      appropriate: appropriateness.appropriate,
      ratio: appropriateness.ratio,
      recommendation: appropriateness.ratio < 0.7 ? 'Aumentar dificuldade' :
                     appropriateness.ratio > 1.5 ? 'Reduzir dificuldade' : 'Manter dificuldade'
    };
  }

  /**
   * Funções auxiliares
   */
  calculateStandardDeviation(values) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  calculateCoefficientOfVariation(values) {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const stdDev = this.calculateStandardDeviation(values);
    return mean > 0 ? stdDev / mean : 0;
  }

  calculateCorrelation(array1, array2) {
    const n = array1.length;
    const sum1 = array1.reduce((sum, val) => sum + val, 0);
    const sum2 = array2.reduce((sum, val) => sum + val, 0);
    const sum1Sq = array1.reduce((sum, val) => sum + val * val, 0);
    const sum2Sq = array2.reduce((sum, val) => sum + val * val, 0);
    const pSum = array1.reduce((sum, val, i) => sum + val * array2[i], 0);
    
    const num = pSum - (sum1 * sum2 / n);
    const den = Math.sqrt((sum1Sq - sum1 * sum1 / n) * (sum2Sq - sum2 * sum2 / n));
    
    return den === 0 ? 0 : num / den;
  }

  calculateTemporalStability(responseTimes) {
    if (responseTimes.length < 4) return 0.5;
    
    // Calcular mudanças consecutivas
    const changes = [];
    for (let i = 1; i < responseTimes.length; i++) {
      changes.push(Math.abs(responseTimes[i] - responseTimes[i - 1]));
    }
    
    const avgChange = changes.reduce((sum, change) => sum + change, 0) / changes.length;
    const avgRT = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    
    // Estabilidade como inverso da taxa de mudança relativa
    return Math.max(0, 1 - (avgChange / avgRT));
  }

  segmentResponseTimes(responseTimes, numSegments) {
    const segmentSize = Math.floor(responseTimes.length / numSegments);
    const segments = [];
    
    for (let i = 0; i < numSegments; i++) {
      const start = i * segmentSize;
      const end = i === numSegments - 1 ? responseTimes.length : start + segmentSize;
      segments.push(responseTimes.slice(start, end));
    }
    
    return segments;
  }

  calculatePositionVariation(positionAverages) {
    const values = Object.values(positionAverages);
    if (values.length < 2) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return mean > 0 ? Math.sqrt(variance) / mean : 0;
  }

  analyzeDistribution(responseTimes) {
    const sorted = [...responseTimes].sort((a, b) => a - b);
    const n = sorted.length;
    
    return {
      skewness: this.calculateSkewness(responseTimes),
      kurtosis: this.calculateKurtosis(responseTimes),
      isNormal: this.testNormality(responseTimes),
      percentiles: {
        p10: sorted[Math.floor(n * 0.1)],
        p25: sorted[Math.floor(n * 0.25)],
        p75: sorted[Math.floor(n * 0.75)],
        p90: sorted[Math.floor(n * 0.9)]
      }
    };
  }

  calculateSkewness(values) {
    const n = values.length;
    const mean = values.reduce((sum, val) => sum + val, 0) / n;
    const stdDev = this.calculateStandardDeviation(values);
    
    if (stdDev === 0) return 0;
    
    const skewness = values.reduce((sum, val) => {
      return sum + Math.pow((val - mean) / stdDev, 3);
    }, 0) / n;
    
    return skewness;
  }

  calculateKurtosis(values) {
    const n = values.length;
    const mean = values.reduce((sum, val) => sum + val, 0) / n;
    const stdDev = this.calculateStandardDeviation(values);
    
    if (stdDev === 0) return 0;
    
    const kurtosis = values.reduce((sum, val) => {
      return sum + Math.pow((val - mean) / stdDev, 4);
    }, 0) / n;
    
    return kurtosis - 3; // Excess kurtosis
  }

  testNormality(values) {
    // Teste simplificado de normalidade baseado em skewness e kurtosis
    const skewness = this.calculateSkewness(values);
    const kurtosis = this.calculateKurtosis(values);
    
    return Math.abs(skewness) < 1 && Math.abs(kurtosis) < 1;
  }

  /**
   * Avalia qualidade dos dados
   */
  assessDataQuality(temporalData) {
    let qualityScore = 1.0;
    const issues = [];

    // Verificar quantidade de dados
    if (temporalData.responseTimes.length < 5) {
      qualityScore -= 0.3;
      issues.push('Poucos tempos de resposta para análise robusta');
    }

    // Verificar valores válidos
    const invalidTimes = temporalData.responseTimes.filter(rt => rt <= 0 || rt > 30000);
    if (invalidTimes.length > 0) {
      qualityScore -= 0.2;
      issues.push(`${invalidTimes.length} tempos de resposta inválidos`);
    }

    // Verificar consistência temporal
    if (temporalData.timestamps.length !== temporalData.responseTimes.length) {
      qualityScore -= 0.2;
      issues.push('Inconsistência entre timestamps e tempos de resposta');
    }

    return {
      score: Math.max(0, qualityScore),
      issues,
      level: qualityScore > 0.8 ? 'high' : qualityScore > 0.5 ? 'medium' : 'low'
    };
  }

  /**
   * Gera recomendações específicas temporais
   */
  generateTemporalRecommendations(analysisResults) {
    const recommendations = [];
    const { 
      responseTimeProfile, temporalPatterns, contextualAnalysis, 
      cognitiveProcessing, fatigueAssessment, consistencyMetrics 
    } = analysisResults;

    // Recomendações baseadas na categoria de tempo
    if (responseTimeProfile.category === 'impulsive') {
      recommendations.push({
        category: 'impulse_control',
        priority: 'high',
        recommendation: 'Desenvolver estratégias de pausa e reflexão antes de responder',
        rationale: 'Respostas muito rápidas podem indicar impulsividade excessiva'
      });
    }

    if (responseTimeProfile.category === 'slow') {
      recommendations.push({
        category: 'processing_speed',
        priority: 'medium',
        recommendation: 'Praticar exercícios de velocidade de processamento visual',
        rationale: 'Tempos de resposta elevados podem indicar lentidão no processamento'
      });
    }

    // Recomendações baseadas em padrões temporais
    if (temporalPatterns.primaryPattern === 'fatiguing') {
      recommendations.push({
        category: 'fatigue_management',
        priority: 'high',
        recommendation: 'Implementar pausas regulares e sessões mais curtas',
        rationale: 'Padrão de fadiga detectado - performance declina ao longo da sessão'
      });
    }

    if (temporalPatterns.stability < 0.5) {
      recommendations.push({
        category: 'consistency',
        priority: 'medium',
        recommendation: 'Trabalhar regulação emocional e estratégias de foco',
        rationale: 'Alta variabilidade temporal pode indicar dificuldades de regulação'
      });
    }

    // Recomendações baseadas em processamento cognitivo
    if (cognitiveProcessing.speed < 0.4) {
      recommendations.push({
        category: 'cognitive_processing',
        priority: 'high',
        recommendation: 'Implementar exercícios específicos de velocidade de processamento',
        rationale: 'Velocidade de processamento abaixo do esperado'
      });
    }

    if (cognitiveProcessing.load > 0.7) {
      recommendations.push({
        category: 'cognitive_load',
        priority: 'medium',
        recommendation: 'Reduzir complexidade da tarefa e aumentar gradualmente',
        rationale: 'Alta carga cognitiva pode comprometer performance'
      });
    }

    // Recomendações baseadas em fadiga
    if (fatigueAssessment.fatigueIndex > 0.3) {
      recommendations.push({
        category: 'endurance',
        priority: 'high',
        recommendation: 'Desenvolver resistência através de sessões progressivamente mais longas',
        rationale: `Índice de fadiga elevado (${Math.round(fatigueAssessment.fatigueIndex * 100)}%)`
      });
    }

    // Recomendações baseadas em consistência
    if (consistencyMetrics.consistency < 0.6) {
      recommendations.push({
        category: 'stability',
        priority: 'medium',
        recommendation: 'Praticar técnicas de autorregulação e mindfulness',
        rationale: 'Baixa consistência temporal indica necessidade de maior controle'
      });
    }

    return recommendations.length > 0 ? recommendations : [{
      category: 'maintenance',
      priority: 'low',
      recommendation: 'Manter padrão temporal atual e continuar monitoramento',
      rationale: 'Performance temporal dentro dos parâmetros esperados'
    }];
  }

  /**
   * Gera análise de fallback em caso de erro
   */
  generateFallbackAnalysis(errorMessage) {
    return {
      averageResponseTime: 0,
      medianResponseTime: 0,
      responseTimeRange: { min: 0, max: 0 },
      responseTimeCategory: 'unknown',
      temporalPattern: 'unknown',
      patternStability: 0.5,
      trendDirection: 'unknown',
      changeRate: 0,
      responseTimeVariability: 0,
      consistencyIndex: 0.5,
      stabilityScore: 0.5,
      outlierRate: 0,
      difficultyImpact: { appropriate: false, ratio: 1 },
      positionImpact: { effect: 'unknown' },
      sequenceImpact: { effect: 'unknown' },
      accuracyCorrelation: { correlation: 0 },
      processingSpeed: 0.5,
      decisionTime: 0,
      executionTime: 0,
      cognitiveLoad: 0.5,
      fatigueIndex: 0,
      enduranceScore: 0.5,
      performanceDecline: 0,
      recoveryCapacity: 0.5,
      attentionSustainability: 0.5,
      impulseControl: 0.5,
      adaptiveSpeed: 0.5,
      percentileRanking: 50,
      ageAppropriate: { appropriate: 'unknown' },
      difficultyAppropriate: { appropriate: false },
      totalResponses: 0,
      sessionDuration: 0,
      difficulty: 'unknown',
      recommendations: [{
        category: 'data_collection',
        priority: 'high',
        recommendation: 'Melhorar coleta de dados temporais',
        rationale: `Erro na análise: ${errorMessage}`
      }],
      analysisTimestamp: new Date().toISOString(),
      collectorVersion: '2.1.0',
      dataQuality: { score: 0, issues: [errorMessage], level: 'error' },
      status: 'fallback',
      error: errorMessage
    };
  }
}
