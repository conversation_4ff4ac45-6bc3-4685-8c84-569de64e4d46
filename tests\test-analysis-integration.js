/**
 * @file test-analysis-integration.js
 * @description Teste de integração completa dos analisadores
 * @version 1.0.0
 */

import { getAnalysisOrchestrator } from './src/api/services/analysis/AnalysisOrchestrator.js';
import { getBehavioralAnalyzer } from './src/api/services/analysis/BehavioralAnalyzer.js';
import { logger } from './src/api/services/core/logging/StructuredLogger.js';

console.log('🧪 Iniciando teste de integração dos analisadores...\n');

async function testAnalysisIntegration() {
  try {
    // 1. Testar inicialização do AnalysisOrchestrator
    console.log('📋 1. Testando AnalysisOrchestrator...');
    const orchestrator = getAnalysisOrchestrator({
      enableParallelAnalysis: true,
      analysisTimeout: 10000
    });
    
    console.log('✅ AnalysisOrchestrator inicializado');
    
    // 2. Testar BehavioralAnalyzer individual
    console.log('\n🧠 2. Testando BehavioralAnalyzer...');
    const behavioralAnalyzer = getBehavioralAnalyzer();
    
    const testSession = {
      id: 'test_session_001',
      childId: 'child_123',
      gameName: 'MemoryGame',
      duration: 120000, // 2 minutos
      accuracy: 0.85,
      interactions: [
        { type: 'card_flip', timestamp: Date.now() - 5000, success: true },
        { type: 'card_flip', timestamp: Date.now() - 3000, success: false },
        { type: 'card_match', timestamp: Date.now() - 1000, success: true }
      ],
      patterns: {
        attention: { focused: true, duration: 45000 },
        persistence: { attempts: 8, giveUp: false },
        strategy: { systematic: true, random: false }
      }
    };
    
    const behavioralResult = await behavioralAnalyzer.analyzeGameBehavior(testSession);
    console.log('✅ Análise comportamental:', {
      success: behavioralResult.success,
      score: behavioralResult.overallScore,
      patterns: behavioralResult.patterns?.length || 0,
      recommendations: behavioralResult.recommendations?.length || 0
    });
    
    // 3. Testar orquestração completa
    console.log('\n🎭 3. Testando orquestração completa...');
    const completeAnalysis = await orchestrator.orchestrateCompleteAnalysis(testSession);
    
    console.log('✅ Análise completa:', {
      orchestrationId: completeAnalysis.orchestrationId,
      overallScore: completeAnalysis.consolidated?.overallScore,
      analyzersUsed: completeAnalysis.metadata?.analyzersUsed,
      orchestrationTime: completeAnalysis.metadata?.orchestrationTime,
      cacheUsed: completeAnalysis.metadata?.cacheUsed
    });
    
    // 4. Testar cache (segunda execução)
    console.log('\n💾 4. Testando cache (segunda execução)...');
    const startTime = Date.now();
    const cachedAnalysis = await orchestrator.orchestrateCompleteAnalysis(testSession);
    const cacheTime = Date.now() - startTime;
    
    console.log('✅ Análise do cache:', {
      cacheUsed: cachedAnalysis.metadata?.cacheUsed,
      cacheTime: cacheTime,
      improvement: `${Math.round((1 - cacheTime / completeAnalysis.metadata.orchestrationTime) * 100)}% mais rápido`
    });
    
    // 5. Testar métricas do orquestrador
    console.log('\n📊 5. Métricas do sistema:');
    const metrics = orchestrator.getOrchestratorMetrics();
    console.log('✅ Métricas:', {
      totalOrchestrations: metrics.totalOrchestrations,
      successfulAnalyses: metrics.successfulAnalyses,
      averageTime: `${Math.round(metrics.averageOrchestrationTime)}ms`,
      cacheHitRate: metrics.cache?.hitRate || 0
    });
    
    // 6. Testar invalidação de cache
    console.log('\n🗑️  6. Testando invalidação de cache...');
    const behavioralCache = behavioralAnalyzer.cache;
    const initialSize = behavioralCache.getMetrics().size;
    
    behavioralAnalyzer.invalidateChildCache('child_123');
    const finalSize = behavioralCache.getMetrics().size;
    
    console.log('✅ Cache invalidado:', {
      entriesRemoved: initialSize - finalSize,
      initialSize,
      finalSize
    });
    
    console.log('\n🎉 TESTE DE INTEGRAÇÃO CONCLUÍDO COM SUCESSO! 🎉');
    console.log('\n📋 RESUMO:');
    console.log('✅ AnalysisOrchestrator funcionando');
    console.log('✅ BehavioralAnalyzer integrado');
    console.log('✅ Cache inteligente ativo');
    console.log('✅ Orquestração paralela/sequencial');
    console.log('✅ Métricas consolidadas');
    console.log('✅ Invalidação inteligente');
    
  } catch (error) {
    console.error('❌ Erro no teste de integração:', error);
    console.error('Stack:', error.stack);
  }
}

// Executar teste
testAnalysisIntegration().catch(console.error);
