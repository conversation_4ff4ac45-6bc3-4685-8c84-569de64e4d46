/**
 * 🧪 TESTE DE FUNCIONALIDADE DO DASHBOARD DE MÉTRICAS
 * Verifica se o dashboard consegue acessar e exibir métricas corretamente
 */

import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000';
const FRONTEND_URL = 'http://localhost:5173';

async function testDashboardMetrics() {
  console.log('🔍 Testando funcionalidade do dashboard de métricas...\n');

  try {
    // 1. Testar se frontend está acessível
    console.log('1️⃣ Testando acesso ao frontend...');
    try {
      const frontendResponse = await axios.get(FRONTEND_URL, { timeout: 5000 });
      console.log('✅ Frontend acessível:', frontendResponse.status === 200 ? 'OK' : 'ERRO');
    } catch (error) {
      console.log('❌ Frontend não acessível:', error.message);
      return;
    }

    // 2. Testar endpoint público de métricas
    console.log('\n2️⃣ Testando endpoint público de métricas...');
    try {
      const metricsResponse = await axios.get(`${API_BASE_URL}/api/public/metrics`);
      console.log('✅ Endpoint de métricas públicas:', metricsResponse.status === 200 ? 'OK' : 'ERRO');
      console.log('📊 Dados recebidos:', JSON.stringify(metricsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Erro no endpoint de métricas:', error.message);
    }

    // 3. Testar endpoint de health
    console.log('\n3️⃣ Testando health check...');
    try {
      const healthResponse = await axios.get(`${API_BASE_URL}/api/public/health`);
      console.log('✅ Health check:', healthResponse.status === 200 ? 'OK' : 'ERRO');
      console.log('🏥 Status do sistema:', JSON.stringify(healthResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Erro no health check:', error.message);
    }

    // 4. Testar se existem dados de sessões de jogos
    console.log('\n4️⃣ Verificando dados de sessões...');
    try {
      // Primeiro, fazer login para obter token
      const loginResponse = await axios.post(`${API_BASE_URL}/api/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      if (loginResponse.data.token) {
        console.log('✅ Login realizado com sucesso');
        
        // Tentar acessar sessões com token
        const sessionsResponse = await axios.get(`${API_BASE_URL}/api/metrics/sessions`, {
          headers: {
            'Authorization': `Bearer ${loginResponse.data.token}`
          }
        });
        
        console.log('✅ Sessões acessíveis:', sessionsResponse.status === 200 ? 'OK' : 'ERRO');
        console.log('📈 Dados de sessões:', JSON.stringify(sessionsResponse.data, null, 2));
      }
    } catch (error) {
      console.log('❌ Erro ao acessar sessões:', error.message);
    }

    // 5. Verificar métricas específicas por jogo
    console.log('\n5️⃣ Testando métricas por jogo...');
    const jogos = ['ColorMatch', 'MemoryGame', 'MusicalSequence', 'ContagemNumeros'];
    
    for (const jogo of jogos) {
      try {
        const jogoMetricsResponse = await axios.get(`${API_BASE_URL}/api/public/metrics/${jogo.toLowerCase()}`);
        console.log(`✅ Métricas ${jogo}:`, jogoMetricsResponse.status === 200 ? 'OK' : 'ERRO');
      } catch (error) {
        console.log(`❌ Métricas ${jogo}: endpoint não encontrado (esperado)`);
      }
    }

    console.log('\n🎉 Teste de funcionalidade do dashboard concluído!');
    
  } catch (error) {
    console.error('❌ Erro geral no teste:', error.message);
  }
}

// Executar teste
await testDashboardMetrics();
