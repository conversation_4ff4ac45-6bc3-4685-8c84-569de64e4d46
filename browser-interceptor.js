/**
 * Script para interceptar e monitorar todas as requisições HTTP
 * Execute este script no console do browser para capturar requisições que retornam 404
 */

// <PERSON><PERSON> fetch original
const originalFetch = window.fetch;

// Interceptar todas as requisições fetch
window.fetch = async function(...args) {
  const [url, options = {}] = args;
  
  console.log('🌐 Requisição interceptada:', {
    url: url,
    method: options.method || 'GET',
    timestamp: new Date().toISOString()
  });
  
  try {
    const response = await originalFetch(...args);
    
    if (response.status === 404) {
      console.error('❌ 404 DETECTADO:', {
        url: url,
        status: response.status,
        statusText: response.statusText,
        timestamp: new Date().toISOString()
      });
      
      // Tentar ler a resposta para ver a mensagem de erro
      try {
        const clonedResponse = response.clone();
        const responseData = await clonedResponse.json();
        console.error('📝 Resposta 404:', responseData);
      } catch (e) {
        console.error('⚠️ Não foi possível ler resposta 404');
      }
    } else if (response.ok) {
      console.log('✅ Sucesso:', {
        url: url,
        status: response.status,
        timestamp: new Date().toISOString()
      });
    } else {
      console.warn('⚠️ Resposta não OK:', {
        url: url,
        status: response.status,
        statusText: response.statusText,
        timestamp: new Date().toISOString()
      });
    }
    
    return response;
  } catch (error) {
    console.error('💥 Erro na requisição:', {
      url: url,
      error: error.message,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};

// Interceptar XMLHttpRequest também
const originalXMLHttpRequest = window.XMLHttpRequest;
const originalOpen = originalXMLHttpRequest.prototype.open;
const originalSend = originalXMLHttpRequest.prototype.send;

originalXMLHttpRequest.prototype.open = function(method, url, ...args) {
  this._interceptedURL = url;
  this._interceptedMethod = method;
  console.log('🌐 XMLHttpRequest interceptado:', {
    method: method,
    url: url,
    timestamp: new Date().toISOString()
  });
  
  return originalOpen.call(this, method, url, ...args);
};

originalXMLHttpRequest.prototype.send = function(...args) {
  this.addEventListener('load', function() {
    if (this.status === 404) {
      console.error('❌ XMLHttpRequest 404 DETECTADO:', {
        method: this._interceptedMethod,
        url: this._interceptedURL,
        status: this.status,
        statusText: this.statusText,
        response: this.responseText,
        timestamp: new Date().toISOString()
      });
    }
  });
  
  return originalSend.call(this, ...args);
};

console.log('🔍 Interceptador de requisições HTTP ativado!');
console.log('📍 Monitore o console para ver todas as requisições HTTP');
console.log('❌ Requisições 404 serão destacadas em vermelho');

// Instruções para o usuário
console.log(`
🎯 INSTRUÇÕES:
1. Navegue pelo sistema normalmente
2. Interaja com os jogos
3. Observe os logs no console
4. Todas as requisições 404 serão capturadas automaticamente

🔧 Para restaurar o fetch original:
window.fetch = originalFetch;
`);

// Também interceptar navegação
window.addEventListener('beforeunload', function() {
  console.log('🔄 Página sendo recarregada/fechada');
});

// Monitorar erros de script também
window.addEventListener('error', function(event) {
  if (event.error && event.error.message && event.error.message.includes('404')) {
    console.error('❌ Erro de script relacionado a 404:', event.error);
  }
});
