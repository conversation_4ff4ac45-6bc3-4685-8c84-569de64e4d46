/**
 * @file test-therapy-plan-generator.js
 * @description Teste para validar a integração do gerador de planos terapêuticos 
 * com o sistema de filtragem de jogos ativos vs. "warms"
 */

import { getProgressAnalyzer } from './src/api/services/analysis/ProgressAnalyzer.js';
import therapyPlanGenerator from './src/api/services/therapy/therapyPlanGenerator.js';

// Mock de jogos para teste
const mockGames = [
  { id: 'MemoryGame', name: 'MemoryGame', status: 'active' },
  { id: 'CreativePainting', name: 'CreativePainting', status: 'active' },
  { id: 'LetterRecognition', name: 'LetterRecognition', status: 'active' },
  { id: 'SpatialPuzzle', name: 'SpatialPuzzle', status: 'active' },
  { id: 'MathQuest', name: 'MathQuest', status: 'active' },
  { id: 'AttentionTracker', name: 'AttentionTracker', status: 'active' },
  { id: 'SoundMatch', name: '<PERSON><PERSON><PERSON>', status: 'active' },
  { id: 'StoryCreator', name: 'StoryCreator', status: 'active' },
  { id: 'EmotionRecognition', name: 'EmotionR<PERSON>ognition', status: 'active' },
  { id: 'PatternMatching', name: 'PatternMatching', status: 'warm' },
  { id: 'SequenceLearning', name: 'SequenceLearning', status: 'warm' }
];

// Mock de um perfil de criança para teste
const mockChildProfile = {
  id: 'child-123',
  name: 'Teste',
  age: 8,
  preferences: {
    activities: ['desenho', 'música', 'jogos'],
    themes: ['animais', 'espaço', 'natureza']
  }
};

// Mock de uma avaliação para teste
const mockAssessment = {
  id: 'assessment-123',
  childId: 'child-123',
  date: new Date().toISOString(),
  domains: {
    communication: {
      level: 'moderate',
      strengths: ['compreensão verbal', 'vocabulário'],
      challenges: ['expressão verbal', 'pragmática']
    },
    cognitive: {
      level: 'mild',
      strengths: ['memória visual', 'classificação'],
      challenges: ['atenção sustentada', 'sequenciamento']
    },
    social: {
      level: 'severe',
      strengths: ['interesse em pares'],
      challenges: ['interação recíproca', 'compreensão social']
    }
  }
};

// Mock de dados de análise
const mockAnalysisData = {
  trends: [
    { metric: 'engagement', trend: 'improving', strength: 0.7, significance: 'medium' },
    { metric: 'performance', trend: 'stable', strength: 0.3, significance: 'low' }
  ],
  milestones: {
    recent: [
      { id: 'm1', title: 'Completou 10 sessões', achievedDate: new Date().toISOString() }
    ]
  },
  overview: {
    keyAchievements: [
      { type: 'milestone', title: 'Primeiro jogo concluído', date: new Date().toISOString() }
    ]
  },
  recommendations: [
    'Manter frequência atual de sessões',
    'Aumentar gradualmente a dificuldade'
  ]
};

async function runTest() {
  console.log('=== TESTE DE INTEGRAÇÃO DO GERADOR DE PLANOS TERAPÊUTICOS ===');

  try {
    // 1. Testar filtragem de jogos ativos
    console.log('1. Testando filtro de jogos ativos para terapia...');
    const activeGames = therapyPlanGenerator.filterActiveGamesForTherapy(mockGames);
    
    console.log(`Total de jogos: ${mockGames.length}`);
    console.log(`Jogos ativos: ${activeGames.length}`);
    console.log(`Jogos inativos ("warms"): ${mockGames.length - activeGames.length}`);
    console.log('Jogos ativos identificados:', activeGames.map(g => g.id).join(', '));

    // 2. Testar geração de plano terapêutico
    console.log('\n2. Testando geração de plano terapêutico...');
    const plan = therapyPlanGenerator.generateTherapeuticPlan(
      mockChildProfile, 
      mockAssessment,
      { duration: 12 }
    );
    
    console.log(`Plano terapêutico gerado com ID: ${plan.id}`);
    console.log(`Abordagem terapêutica selecionada: ${plan.approach}`);
    console.log(`Duração do plano: ${plan.duration}`);
    
    // 3. Testar sincronização do plano com jogos ativos
    console.log('\n3. Testando sincronização do plano com jogos ativos...');
    
    // Criar uma cópia do plano e adicionar jogos "warms" para testar a sincronização
    const planToSync = JSON.parse(JSON.stringify(plan));
    if (!planToSync.interventions) planToSync.interventions = {};
    planToSync.interventions.gameBasedIntervention = {
      name: 'Intervenções baseadas em jogos',
      description: 'Uso de jogos específicos para desenvolver habilidades',
      games: [
        { id: 'MemoryGame', name: 'MemoryGame' },
        { id: 'PatternMatching', name: 'PatternMatching' }, // Jogo "warm"
        { id: 'CreativePainting', name: 'CreativePainting' },
        { id: 'SequenceLearning', name: 'SequenceLearning' } // Jogo "warm"
      ]
    };
    
    // Registrar os jogos antes da sincronização
    const gamesBeforeSync = planToSync.interventions.gameBasedIntervention?.games?.length || 0;
    console.log('Jogos antes da sincronização:', planToSync.interventions.gameBasedIntervention?.games.map(g => g.id).join(', '));
    
    // Sincronizar o plano (remover jogos warm)
    const syncedPlan = therapyPlanGenerator.syncPlanWithActiveGames(planToSync, mockGames);
    
    // Verificar se a propriedade games existe e contar jogos após sincronização
    const gamesAfterSync = syncedPlan.interventions?.gameBasedIntervention?.games?.length || 0;
    
    console.log(`Jogos antes da sincronização: ${gamesBeforeSync}`);
    console.log(`Jogos após sincronização: ${gamesAfterSync}`);
    
    // Verificar quais jogos restaram após a sincronização
    if (syncedPlan.interventions?.gameBasedIntervention?.games) {
      console.log('Jogos após sincronização:', syncedPlan.interventions.gameBasedIntervention.games.map(g => g.id).join(', '));
    } else {
      console.log('Não há jogos definidos nas intervenções após sincronização');
    }
    
    // Validar se os jogos foram filtrados corretamente (só restando os ativos)
    let warmsRemoved = true; // Assumimos sucesso até que uma verificação falhe
    
    // Se temos jogos após a sincronização, verificamos se todos são ativos
    if (syncedPlan.interventions?.gameBasedIntervention?.games) {
        // Verificar se algum dos jogos restantes é um jogo "warm"
        const warmGames = ['PatternMatching', 'SequenceLearning'];
        const containsWarm = syncedPlan.interventions.gameBasedIntervention.games.some(
            game => warmGames.includes(game.id)
        );
        
        // Se contiver algum jogo "warm", a filtragem falhou
        warmsRemoved = !containsWarm;
    }
    console.log(`Jogos "warms" removidos ou plano processado corretamente: ${warmsRemoved ? 'SIM ✅' : 'NÃO ❌'}`);
    
    // 4. Testar integração com sistema de análise
    console.log('\n4. Testando integração com sistema de análise...');
    const enhancedPlan = therapyPlanGenerator.integrateWithAnalysisSystem(syncedPlan, mockAnalysisData);
    
    console.log('Plano integrado com dados de análise:');
    console.log(`- Tendências adicionadas: ${enhancedPlan.analyticalInsights.progressTrends.length}`);
    console.log(`- Marcos recentes adicionados: ${enhancedPlan.analyticalInsights.recentMilestones.length}`);
    console.log(`- Conquistas principais adicionadas: ${enhancedPlan.analyticalInsights.keyAchievements.length}`);
    
    // 5. Testar exportação do plano
    console.log('\n5. Testando exportação do plano terapêutico...');
    const exportedPlan = therapyPlanGenerator.exportTherapeuticPlan(enhancedPlan, {
      activeGamesOnly: true,
      includeMetadata: true
    });
    
    if (exportedPlan) {
      console.log(`Plano exportado com título: ${exportedPlan.title || 'Não definido'}`);
      console.log(`Total de objetivos exportados: ${exportedPlan.goals?.length || 0}`);
      console.log(`Metadados incluídos: ${exportedPlan.metadata ? 'SIM ✅' : 'NÃO ❌'}`);
    } else {
      console.log('ERRO: Não foi possível exportar o plano');
    }
    
    console.log('\n=== RESULTADO ===');
    const isIntegrationSuccessful = warmsRemoved && 
      enhancedPlan?.analyticalInsights && 
      exportedPlan?.metadata;
      
    console.log(`Integração do gerador de planos terapêuticos funcionando corretamente: ${isIntegrationSuccessful ? 'SIM ✅' : 'NÃO ❌'}`);
    
    if (isIntegrationSuccessful) {
      console.log('Validação concluída: O gerador de planos terapêuticos está corretamente integrado!');
      console.log('Apenas jogos ativos estão sendo incluídos nos planos terapêuticos.');
    } else {
      console.log('ALERTA: Verificar a implementação, a integração não está completa!');
    }
    
  } catch (error) {
    console.error('Erro durante o teste:', error);
  }
}

// Executar o teste
runTest().catch(console.error);
