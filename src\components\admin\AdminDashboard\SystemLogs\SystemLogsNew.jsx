/**
 * @file SystemLogs.jsx
 * @description Visualizador de Logs do Sistema - Área Administrativa
 * @version 3.0.0
 * @admin true
 * @datasource API Real + LocalStorage Fallback
 */

import React, { useState, useEffect } from 'react';
import adminApiService from '../../../../services/adminApiService';
import styles from './SystemLogs.module.css';

const SystemLogs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterLevel, setFilterLevel] = useState('all');
  const [filterService, setFilterService] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [dataSource, setDataSource] = useState('loading');
  const [lastUpdate, setLastUpdate] = useState(null);

  // Carregar logs reais do sistema
  const loadSystemLogs = async () => {
    try {
      setLoading(true);
      const apiLogs = await adminApiService.getSystemLogs();
      const localLogs = getLocalStorageLogs();
      const allLogs = [...(apiLogs || []), ...localLogs]
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 200);

      setLogs(allLogs);
      setDataSource(apiLogs ? 'api_real' : 'localStorage');
      setLastUpdate(new Date());

      console.log('✅ Logs do sistema carregados:', {
        total: allLogs.length,
        source: apiLogs ? 'api_real' : 'localStorage',
        apiLogs: apiLogs?.length || 0,
        localLogs: localLogs.length,
      });
    } catch (error) {
      console.error('❌ Erro ao carregar logs, usando localStorage:', error);
      const localLogs = getLocalStorageLogs();
      setLogs(localLogs);
      setDataSource('localStorage_fallback');
    } finally {
      setLoading(false);
    }
  };

  // Função para buscar logs do localStorage
  const getLocalStorageLogs = () => {
    try {
      const systemLogs = JSON.parse(localStorage.getItem('system_logs') || '[]');
      const errorLogs = JSON.parse(localStorage.getItem('error_logs') || '[]');

      const allLocalLogs = [
        ...systemLogs.map(log => ({ ...log, source: 'localStorage' })),
        ...errorLogs.map(log => ({ ...log, level: 'error', source: 'localStorage' })),
      ];

      return allLocalLogs
        .filter(log => log.timestamp)
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 100);
    } catch (error) {
      console.warn('Erro ao buscar logs locais:', error);
      return [];
    }
  };

  useEffect(() => {
    loadSystemLogs();

    const interval = autoRefresh
      ? setInterval(() => {
          loadSystemLogs();
        }, 30000)
      : null;

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const refreshLogs = () => {
    adminApiService.clearCache();
    loadSystemLogs();
  };

  const getDataSourceInfo = () => {
    switch (dataSource) {
      case 'api_real':
        return { icon: '🟢', text: 'API + LocalStorage', color: '#4CAF50' };
      case 'localStorage':
        return { icon: '🟡', text: 'Apenas LocalStorage', color: '#FF9800' };
      case 'localStorage_fallback':
        return { icon: '🟠', text: 'Fallback LocalStorage', color: '#FF5722' };
      case 'loading':
        return { icon: '🔄', text: 'Carregando...', color: '#2196F3' };
      default:
        return { icon: '🔴', text: 'Erro nos Dados', color: '#F44336' };
    }
  };

  const getLevelColor = level => {
    switch (level) {
      case 'error':
        return '#F44336';
      case 'warn':
        return '#FF9800';
      case 'info':
        return '#2196F3';
      case 'debug':
        return '#9E9E9E';
      default:
        return '#000000';
    }
  };

  const getLevelIcon = level => {
    switch (level) {
      case 'error':
        return '❌';
      case 'warn':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      case 'debug':
        return '🔍';
      default:
        return '📝';
    }
  };

  const formatTimestamp = timestamp => {
    return new Date(timestamp).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const filteredLogs = logs.filter(log => {
    const matchesLevel = filterLevel === 'all' || log.level === filterLevel;
    const matchesService = filterService === 'all' || log.service === filterService;
    const matchesSearch =
      searchTerm === '' ||
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (log.type && log.type.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesLevel && matchesService && matchesSearch;
  });

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Carregando logs do sistema...</p>
      </div>
    );
  }

  return (
    <div className={styles.systemLogs}>
      {/* Header com informações da fonte dos dados */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        padding: '12px 16px',
        background: 'rgba(255, 255, 255, 0.08)',
        borderRadius: '10px',
        border: '1px solid rgba(255, 255, 255, 0.12)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <span style={{ fontSize: '20px' }}>📋</span>
          <div>
            <h2 style={{ margin: 0, fontSize: '18px', color: '#fff', fontWeight: 'bold' }}>
              System Logs
            </h2>
            <p style={{ margin: 0, fontSize: '12px', color: '#ccc' }}>
              Logs em tempo real do sistema
            </p>
          </div>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <div style={{ 
            display: 'flex', 
            alignItems: 'center', 
            gap: '6px',
            padding: '6px 12px',
            background: 'rgba(0, 0, 0, 0.2)',
            borderRadius: '8px',
            border: `1px solid ${getDataSourceInfo().color}33`
          }}>
            <span style={{ fontSize: '14px' }}>{getDataSourceInfo().icon}</span>
            <span style={{ 
              fontSize: '12px', 
              color: getDataSourceInfo().color,
              fontWeight: '600'
            }}>
              {getDataSourceInfo().text}
            </span>
          </div>
          
          {lastUpdate && (
            <div style={{ 
              fontSize: '11px', 
              color: '#999',
              textAlign: 'right'
            }}>
              <div>Última atualização:</div>
              <div style={{ fontWeight: 'bold', color: '#ccc' }}>
                {lastUpdate.toLocaleTimeString()}
              </div>
            </div>
          )}
          
          <button
            onClick={refreshLogs}
            style={{
              background: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '8px',
              padding: '8px 12px',
              color: '#fff',
              fontSize: '12px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'all 0.2s ease'
            }}
          >
            🔄 Atualizar
          </button>
        </div>
      </div>

      {/* Filters */}
      <div style={{
        display: 'flex',
        gap: '16px',
        marginBottom: '20px',
        padding: '16px',
        background: 'rgba(255, 255, 255, 0.05)',
        borderRadius: '8px',
        border: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        <input
          type="text"
          placeholder="🔍 Buscar nos logs..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          style={{
            flex: 1,
            padding: '8px 12px',
            background: 'rgba(255, 255, 255, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '6px',
            color: '#fff',
            fontSize: '14px'
          }}
        />

        <select
          value={filterLevel}
          onChange={e => setFilterLevel(e.target.value)}
          style={{
            padding: '8px 12px',
            background: 'rgba(255, 255, 255, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '6px',
            color: '#fff',
            fontSize: '14px'
          }}
        >
          <option value="all">Todos os Níveis</option>
          <option value="error">Erros</option>
          <option value="warn">Avisos</option>
          <option value="info">Informações</option>
          <option value="debug">Debug</option>
        </select>

        <select
          value={filterService}
          onChange={e => setFilterService(e.target.value)}
          style={{
            padding: '8px 12px',
            background: 'rgba(255, 255, 255, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '6px',
            color: '#fff',
            fontSize: '14px'
          }}
        >
          <option value="all">Todos os Serviços</option>
          <option value="SystemOrchestrator">System Orchestrator</option>
          <option value="AIBrainOrchestrator">AI Brain</option>
          <option value="BehavioralAnalyzer">Behavioral Analyzer</option>
          <option value="CognitiveAnalyzer">Cognitive Analyzer</option>
          <option value="HealthCheckService">Health Check</option>
        </select>
      </div>

      {/* Stats */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '12px',
        padding: '20px',
        margin: '20px 0',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        backdropFilter: 'blur(10px)',
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          gap: '20px',
        }}>
          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>📝</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#fff', marginBottom: '2px' }}>
              {filteredLogs.length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Total de Logs</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>❌</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#ef4444', marginBottom: '2px' }}>
              {filteredLogs.filter(l => l.level === 'error').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Erros</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>⚠️</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#f59e0b', marginBottom: '2px' }}>
              {filteredLogs.filter(l => l.level === 'warn').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Avisos</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>ℹ️</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', marginBottom: '2px' }}>
              {filteredLogs.filter(l => l.level === 'info').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Informações</div>
          </div>
        </div>
      </div>

      {/* Logs List */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.05)',
        borderRadius: '8px',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        maxHeight: '600px',
        overflowY: 'auto'
      }}>
        {filteredLogs.map((log, index) => (
          <div key={log.id || index} style={{
            padding: '12px 16px',
            borderBottom: index < filteredLogs.length - 1 ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
            fontSize: '14px'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '4px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <span style={{ color: getLevelColor(log.level), fontWeight: 'bold' }}>
                  {getLevelIcon(log.level)} {log.level.toUpperCase()}
                </span>
                <span style={{ color: '#ccc', fontSize: '13px' }}>
                  {log.service || 'Unknown Service'}
                </span>
              </div>
              <span style={{ color: '#999', fontSize: '12px' }}>
                {formatTimestamp(log.timestamp)}
              </span>
            </div>

            <div style={{ color: '#fff', lineHeight: '1.4' }}>
              {log.message}
            </div>

            {log.metadata && Object.keys(log.metadata).filter(key => !['timestamp', 'service', 'type'].includes(key)).length > 0 && (
              <div style={{ 
                marginTop: '8px', 
                padding: '8px',
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#ccc'
              }}>
                {Object.entries(log.metadata)
                  .filter(([key]) => !['timestamp', 'service', 'type'].includes(key))
                  .map(([key, value]) => (
                    <span key={key} style={{ marginRight: '12px' }}>
                      <strong>{key}:</strong> {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                    </span>
                  ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {filteredLogs.length === 0 && (
        <div style={{
          textAlign: 'center',
          padding: '40px',
          color: '#999'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>📋</div>
          <div style={{ fontSize: '16px' }}>Nenhum log encontrado com os filtros aplicados</div>
        </div>
      )}
    </div>
  );
};

export default SystemLogs;
