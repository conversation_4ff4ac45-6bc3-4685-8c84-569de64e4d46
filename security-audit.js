/**
 * 🔒 AUDITORIA DE SEGURANÇA - Endpoints Públicos
 * Portal Betina V3 - Verificação de exposição de dados sensíveis
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3000';

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Endpoints públicos que devem estar seguros
const publicEndpoints = [
  {
    url: '/api/public/health',
    description: 'Health check básico',
    shouldContain: ['status', 'version'],
    shouldNotContain: ['user', 'session', 'password', 'token']
  },
  {
    url: '/api/public/games',
    description: 'Lista de jogos disponíveis',
    shouldContain: ['games', 'name', 'description'],
    shouldNotContain: ['score', 'user_data', 'metrics', 'personal']
  },
  {
    url: '/api/public/activities',
    description: 'Lista de atividades disponíveis',
    shouldContain: ['activities', 'category'],
    shouldNotContain: ['progress', 'user_data', 'therapeutic_data']
  },
  {
    url: '/api/public/metrics',
    description: 'Informações básicas sobre métricas',
    shouldContain: ['availableCategories', 'authEndpoint'],
    shouldNotContain: ['game_sessions', 'user_engagement', 'performance_data', 'scores']
  }
];

// Endpoints que DEVEM ser protegidos (401/403)
const protectedEndpoints = [
  {
    url: '/api/metrics/sessions',
    description: 'Dados de sessões (SENSÍVEL)'
  },
  {
    url: '/api/metrics/dashboard',
    description: 'Métricas do dashboard (SENSÍVEL)'
  },
  {
    url: '/api/dashboard/overview',
    description: 'Visão geral do dashboard (SENSÍVEL)'
  },
  {
    url: '/api/sessions',
    description: 'Endpoint de compatibilidade (DEVE PEDIR AUTH)'
  }
];

async function auditPublicEndpoint(endpoint) {
  try {
    console.log(`${colors.cyan}🔍 Auditando: ${endpoint.url}${colors.reset}`);
    
    const response = await fetch(`${API_BASE}${endpoint.url}`);
    const data = await response.json();
    
    if (response.status !== 200) {
      console.log(`${colors.yellow}⚠️ Status ${response.status} - ${endpoint.description}${colors.reset}`);
      return false;
    }
    
    const dataString = JSON.stringify(data).toLowerCase();
    
    // Verificar se contém dados que deveria conter
    let hasRequiredData = true;
    for (const required of endpoint.shouldContain) {
      if (!dataString.includes(required.toLowerCase())) {
        console.log(`${colors.yellow}⚠️ Não contém dados obrigatórios: ${required}${colors.reset}`);
        hasRequiredData = false;
      }
    }
    
    // Verificar se NÃO contém dados sensíveis
    let hasSensitiveData = false;
    for (const forbidden of endpoint.shouldNotContain) {
      if (dataString.includes(forbidden.toLowerCase())) {
        console.log(`${colors.red}❌ DADOS SENSÍVEIS EXPOSTOS: ${forbidden}${colors.reset}`);
        hasSensitiveData = true;
      }
    }
    
    if (!hasSensitiveData && hasRequiredData) {
      console.log(`${colors.green}✅ Endpoint seguro - ${endpoint.description}${colors.reset}`);
      return true;
    } else {
      console.log(`${colors.red}❌ Problemas de segurança detectados${colors.reset}`);
      return false;
    }
    
  } catch (error) {
    console.log(`${colors.red}💥 Erro ao auditar: ${error.message}${colors.reset}`);
    return false;
  }
}

async function auditProtectedEndpoint(endpoint) {
  try {
    console.log(`${colors.cyan}🔒 Verificando proteção: ${endpoint.url}${colors.reset}`);
    
    const response = await fetch(`${API_BASE}${endpoint.url}`);
    const data = await response.json();
    
    if (response.status === 401 || response.status === 403) {
      console.log(`${colors.green}✅ Protegido corretamente (${response.status}) - ${endpoint.description}${colors.reset}`);
      return true;
    } else if (response.status === 200) {
      // Verificar se retorna dados sensíveis
      const dataString = JSON.stringify(data).toLowerCase();
      const sensitiveKeywords = ['user_data', 'session_data', 'score', 'metrics', 'therapeutic', 'personal'];
      
      let hasSensitiveData = false;
      for (const keyword of sensitiveKeywords) {
        if (dataString.includes(keyword)) {
          hasSensitiveData = true;
          break;
        }
      }
      
      if (hasSensitiveData) {
        console.log(`${colors.red}❌ CRÍTICO: Dados sensíveis expostos sem autenticação!${colors.reset}`);
        return false;
      } else {
        console.log(`${colors.yellow}⚠️ Acessível sem auth, mas sem dados sensíveis detectados${colors.reset}`);
        return true;
      }
    } else {
      console.log(`${colors.yellow}⚠️ Status inesperado: ${response.status}${colors.reset}`);
      return false;
    }
    
  } catch (error) {
    console.log(`${colors.red}💥 Erro ao verificar proteção: ${error.message}${colors.reset}`);
    return false;
  }
}

async function checkServerHealth() {
  try {
    console.log(`${colors.blue}🏥 Verificando se o servidor está online...${colors.reset}`);
    const response = await fetch(`${API_BASE}/api/public/health`);
    
    if (response.status === 200) {
      console.log(`${colors.green}✅ Servidor online${colors.reset}`);
      return true;
    } else {
      console.log(`${colors.red}❌ Servidor com problemas (${response.status})${colors.reset}`);
      return false;
    }
  } catch (error) {
    console.log(`${colors.red}❌ Servidor offline: ${error.message}${colors.reset}`);
    return false;
  }
}

async function runSecurityAudit() {
  console.log(`${colors.magenta}🔒 AUDITORIA DE SEGURANÇA - Portal Betina V3${colors.reset}`);
  console.log('='.repeat(60));
  
  // Verificar servidor
  const serverOnline = await checkServerHealth();
  if (!serverOnline) {
    console.log(`${colors.red}❌ Não é possível auditar sem o servidor online${colors.reset}`);
    process.exit(1);
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`${colors.magenta}🔍 AUDITANDO ENDPOINTS PÚBLICOS${colors.reset}`);
  console.log('='.repeat(60));
  
  let publicSecure = 0;
  for (const endpoint of publicEndpoints) {
    const isSecure = await auditPublicEndpoint(endpoint);
    if (isSecure) publicSecure++;
    console.log(); // Linha em branco
  }
  
  console.log('='.repeat(60));
  console.log(`${colors.magenta}🔒 VERIFICANDO ENDPOINTS PROTEGIDOS${colors.reset}`);
  console.log('='.repeat(60));
  
  let protectedSecure = 0;
  for (const endpoint of protectedEndpoints) {
    const isProtected = await auditProtectedEndpoint(endpoint);
    if (isProtected) protectedSecure++;
    console.log(); // Linha em branco
  }
  
  console.log('='.repeat(60));
  console.log(`${colors.magenta}📊 RESUMO DA AUDITORIA${colors.reset}`);
  console.log('='.repeat(60));
  console.log(`${colors.green}✅ Endpoints públicos seguros: ${publicSecure}/${publicEndpoints.length}${colors.reset}`);
  console.log(`${colors.blue}🔒 Endpoints protegidos: ${protectedSecure}/${protectedEndpoints.length}${colors.reset}`);
  
  const totalSecure = publicSecure + protectedSecure;
  const totalEndpoints = publicEndpoints.length + protectedEndpoints.length;
  const securityScore = Math.round((totalSecure / totalEndpoints) * 100);
  
  console.log(`${colors.cyan}📊 Score de Segurança: ${securityScore}%${colors.reset}`);
  
  if (securityScore >= 95) {
    console.log(`${colors.green}🛡️ EXCELENTE! Sistema muito seguro${colors.reset}`);
  } else if (securityScore >= 80) {
    console.log(`${colors.yellow}⚠️ BOM, mas há melhorias necessárias${colors.reset}`);
  } else {
    console.log(`${colors.red}🚨 CRÍTICO! Problemas de segurança encontrados${colors.reset}`);
  }
  
  console.log('='.repeat(60));
  console.log(`${colors.blue}💡 Recomendações:${colors.reset}`);
  console.log('- Nunca exponha dados de usuários em endpoints públicos');
  console.log('- Sempre exija autenticação para dados sensíveis');
  console.log('- Use rate limiting em todos os endpoints');
  console.log('- Valide e sanitize todos os inputs');
  console.log('- Monitore logs para tentativas de acesso não autorizado');
  console.log('='.repeat(60));
}

// Executar auditoria
runSecurityAudit().catch(error => {
  console.error(`${colors.red}❌ Erro fatal na auditoria: ${error.message}${colors.reset}`);
  process.exit(1);
});
