/**
 * 🎨 ADMIN DASHBOARD V3 - UI/UX MODERNO
 * Design System: Material Design 3 + Glassmorphism
 * Paleta: Dark Mode + Accent Colors
 */

/* ===== VARIÁVEIS CSS ===== */
:root {
  /* Cores Principais */
  --admin-primary: #6366f1;
  --admin-primary-dark: #4f46e5;
  --admin-primary-light: #8b5cf6;

  /* Cores de Fundo */
  --admin-bg-primary: #0f172a;
  --admin-bg-secondary: #1e293b;
  --admin-bg-tertiary: #334155;
  --admin-bg-glass: rgba(255, 255, 255, 0.1);

  /* Cores de Texto */
  --admin-text-primary: #f8fafc;
  --admin-text-secondary: #cbd5e1;
  --admin-text-muted: #94a3b8;

  /* Cores de Status */
  --admin-success: #10b981;
  --admin-warning: #f59e0b;
  --admin-error: #ef4444;
  --admin-info: #3b82f6;

  /* Sombras */
  --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Bordas */
  --admin-border-radius: 12px;
  --admin-border-radius-lg: 16px;
  --admin-border-color: rgba(255, 255, 255, 0.1);
}

/* ===== LOGIN CONTAINER ===== */
.loginContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg,
    var(--admin-bg-primary) 0%,
    var(--admin-bg-secondary) 50%,
    var(--admin-primary-dark) 100%
  );
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.loginContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(10px, 10px); }
}

.loginBox {
  background: var(--admin-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius-lg);
  padding: 48px;
  box-shadow: var(--admin-shadow-xl);
  max-width: 450px;
  width: 100%;
  text-align: center;
  position: relative;
  z-index: 1;
  animation: loginBoxAppear 0.6s ease-out;
}

@keyframes loginBoxAppear {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.loginBox h2 {
  color: var(--admin-text-primary);
  margin-bottom: 12px;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loginBox p {
  color: var(--admin-text-secondary);
  margin-bottom: 32px;
  font-size: 16px;
  font-weight: 400;
}

/* ===== FORMULÁRIO DE LOGIN ===== */
.loginForm {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.inputGroup {
  text-align: left;
  position: relative;
}

.inputGroup label {
  display: block;
  margin-bottom: 12px;
  color: var(--admin-text-primary);
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.passwordInput {
  width: 100%;
  padding: 16px 20px;
  background: var(--admin-bg-glass);
  border: 2px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  font-size: 16px;
  color: var(--admin-text-primary);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.passwordInput::placeholder {
  color: var(--admin-text-muted);
}

.passwordInput:focus {
  outline: none;
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.passwordInput:disabled {
  background: rgba(255, 255, 255, 0.05);
  cursor: not-allowed;
  opacity: 0.6;
}

.buttonGroup {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.loginButton {
  flex: 1;
  padding: 16px 24px;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-light));
  color: white;
  border: none;
  border-radius: var(--admin-border-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.loginButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.loginButton:hover:not(:disabled)::before {
  left: 100%;
}

.loginButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-lg);
}

.loginButton:active {
  transform: translateY(0);
}

.loginButton:disabled {
  background: var(--admin-bg-tertiary);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.backButton {
  flex: 1;
  padding: 16px 24px;
  background: var(--admin-bg-tertiary);
  color: var(--admin-text-primary);
  border: 2px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backButton:hover {
  background: var(--admin-bg-glass);
  border-color: var(--admin-primary);
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-md);
}

.error {
  color: var(--admin-error);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  padding: 16px;
  border-radius: var(--admin-border-radius);
  font-size: 14px;
  font-weight: 500;
  animation: errorShake 0.5s ease-in-out;
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.loginHint {
  margin-top: 24px;
  padding: 16px;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--admin-border-radius);
  color: var(--admin-info);
  font-size: 14px;
  font-weight: 500;
}

/* ===== CONTAINER PRINCIPAL ===== */
.adminContainer {
  min-height: 100vh;
  background: var(--admin-bg-primary);
  display: flex;
  flex-direction: column;
  position: relative;
}

.adminContainer::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* ===== HEADER MODERNO ===== */
.adminHeader {
  background: var(--admin-bg-glass);
  backdrop-filter: blur(20px);
  padding: 24px 32px;
  border-bottom: 1px solid var(--admin-border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--admin-shadow-md);
  position: relative;
  z-index: 10;
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 16px;
}

.headerLeft h1 {
  margin: 0;
  color: var(--admin-text-primary);
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.version {
  color: var(--admin-text-muted);
  font-size: 14px;
  font-weight: 500;
  padding: 4px 12px;
  background: var(--admin-bg-tertiary);
  border-radius: 20px;
  border: 1px solid var(--admin-border-color);
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 24px;
}

.adminUser {
  color: var(--admin-text-secondary);
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--admin-bg-tertiary);
  border-radius: var(--admin-border-radius);
  border: 1px solid var(--admin-border-color);
}

.logoutButton {
  padding: 12px 20px;
  background: linear-gradient(135deg, var(--admin-error), #dc2626);
  color: white;
  border: none;
  border-radius: var(--admin-border-radius);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.logoutButton:hover {
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-lg);
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* ===== NAVEGAÇÃO POR ABAS MODERNA ===== */
.tabNavigation {
  background: var(--admin-bg-secondary);
  padding: 8px 32px;
  border-bottom: 1px solid var(--admin-border-color);
  display: flex;
  gap: 8px;
  overflow-x: auto;
  position: relative;
  z-index: 5;
}

.tabNavigation::-webkit-scrollbar {
  height: 4px;
}

.tabNavigation::-webkit-scrollbar-track {
  background: var(--admin-bg-tertiary);
  border-radius: 2px;
}

.tabNavigation::-webkit-scrollbar-thumb {
  background: var(--admin-primary);
  border-radius: 2px;
}

.tabButton {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  color: var(--admin-text-secondary);
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.tabButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left 0.5s;
}

.tabButton:hover::before {
  left: 100%;
}

.tabButton:hover {
  background: var(--admin-bg-glass);
  color: var(--admin-text-primary);
  transform: translateY(-2px);
  box-shadow: var(--admin-shadow-md);
  border-color: var(--admin-primary);
}

.tabButton.active {
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-light));
  color: white;
  border-color: var(--admin-primary);
  box-shadow: var(--admin-shadow-lg);
  transform: translateY(-2px);
}

.tabButton.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid var(--admin-bg-primary);
}

.tabIcon {
  font-size: 20px;
  transition: transform 0.3s ease;
}

.tabButton:hover .tabIcon {
  transform: scale(1.1);
}

.tabButton.active .tabIcon {
  transform: scale(1.1) rotate(5deg);
}

.tabLabel {
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== ÁREA DE CONTEÚDO ===== */
.adminContent {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

.adminContent::-webkit-scrollbar {
  width: 8px;
}

.adminContent::-webkit-scrollbar-track {
  background: var(--admin-bg-secondary);
  border-radius: 4px;
}

.adminContent::-webkit-scrollbar-thumb {
  background: var(--admin-primary);
  border-radius: 4px;
}

.tabContent {
  max-width: 1400px;
  margin: 0 auto;
  animation: contentFadeIn 0.5s ease-out;
}

@keyframes contentFadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.tabContent h2 {
  margin: 0 0 32px 0;
  color: var(--admin-text-primary);
  font-size: 32px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px 32px;
  background: var(--admin-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius-lg);
  box-shadow: var(--admin-shadow-md);
}

.tabContent h2::after {
  content: '';
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, var(--admin-primary), transparent);
  border-radius: 1px;
}

/* ===== FOOTER MODERNO ===== */
.adminFooter {
  background: var(--admin-bg-glass);
  backdrop-filter: blur(20px);
  padding: 24px 32px;
  border-top: 1px solid var(--admin-border-color);
  margin-top: auto;
  position: relative;
  z-index: 5;
}

.footerInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--admin-text-muted);
  font-size: 14px;
  font-weight: 500;
}

.footerInfo span:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footerInfo span:first-child::before {
  content: '⚡';
  font-size: 16px;
}

.footerInfo span:last-child {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--admin-text-secondary);
}

.footerInfo span:last-child::before {
  content: '🕒';
  font-size: 14px;
}

/* ===== RESPONSIVIDADE MODERNA ===== */

/* Tablet */
@media (max-width: 1024px) {
  .adminHeader {
    padding: 20px 24px;
  }

  .headerLeft h1 {
    font-size: 24px;
  }

  .tabNavigation {
    padding: 8px 24px;
  }

  .adminContent {
    padding: 24px;
  }

  .tabContent h2 {
    font-size: 28px;
    padding: 20px 24px;
  }
}

/* Mobile Large */
@media (max-width: 768px) {
  .adminHeader {
    padding: 16px 20px;
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .headerLeft {
    flex-direction: column;
    gap: 8px;
  }

  .headerLeft h1 {
    font-size: 22px;
  }

  .headerRight {
    gap: 16px;
  }

  .tabNavigation {
    padding: 8px 20px;
    gap: 4px;
  }

  .tabButton {
    padding: 12px 16px;
    gap: 8px;
  }

  .tabLabel {
    display: none;
  }

  .tabIcon {
    font-size: 18px;
  }

  .adminContent {
    padding: 20px;
  }

  .tabContent h2 {
    font-size: 24px;
    padding: 16px 20px;
    margin-bottom: 24px;
  }

  .footerInfo {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .loginBox {
    padding: 32px 24px;
  }

  .buttonGroup {
    flex-direction: column;
    gap: 12px;
  }

  .loginButton,
  .backButton {
    padding: 14px 20px;
  }
}

/* Mobile Small */
@media (max-width: 480px) {
  .loginContainer {
    padding: 16px;
  }

  .loginBox {
    padding: 24px 20px;
  }

  .loginBox h2 {
    font-size: 24px;
  }

  .adminHeader {
    padding: 12px 16px;
  }

  .headerLeft h1 {
    font-size: 20px;
  }

  .adminContent {
    padding: 16px;
  }

  .tabNavigation {
    padding: 6px 16px;
  }

  .tabButton {
    padding: 10px 12px;
    min-width: 48px;
  }

  .tabContent h2 {
    font-size: 20px;
    padding: 12px 16px;
    margin-bottom: 20px;
  }

  .adminFooter {
    padding: 16px 20px;
  }

  .version {
    font-size: 12px;
    padding: 2px 8px;
  }

  .adminUser {
    font-size: 14px;
    padding: 6px 12px;
  }

  .logoutButton {
    padding: 8px 16px;
    font-size: 12px;
  }
}

/* Ultra Small */
@media (max-width: 320px) {
  .loginBox {
    padding: 20px 16px;
  }

  .adminHeader {
    padding: 10px 12px;
  }

  .adminContent {
    padding: 12px;
  }

  .tabNavigation {
    padding: 4px 12px;
  }

  .tabContent h2 {
    font-size: 18px;
    padding: 10px 12px;
  }
}

/* ===== ANIMAÇÕES E EFEITOS ESPECIAIS ===== */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px var(--admin-primary); }
  50% { box-shadow: 0 0 20px var(--admin-primary), 0 0 30px var(--admin-primary); }
}

.tabButton.active {
  animation: glow 2s ease-in-out infinite;
}

/* ===== ELEMENTOS ADICIONAIS ===== */

/* Status indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
}

.status-badge.online {
  background: rgba(16, 185, 129, 0.1);
  border-color: var(--admin-success);
  color: var(--admin-success);
}

.status-badge.offline {
  background: rgba(239, 68, 68, 0.1);
  border-color: var(--admin-error);
  color: var(--admin-error);
}

/* Header controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-button {
  padding: 8px;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
  border-radius: 8px;
  color: var(--admin-text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.control-button:hover {
  background: var(--admin-bg-glass);
  border-color: var(--admin-primary);
  color: var(--admin-text-primary);
  transform: translateY(-1px);
}

.notifications-badge {
  position: relative;
  padding: 8px;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
  border-radius: 8px;
  color: var(--admin-text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.notifications-badge:hover {
  background: var(--admin-bg-glass);
  border-color: var(--admin-primary);
  color: var(--admin-text-primary);
}

.notification-count {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--admin-error);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Admin user info */
.adminUser {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.adminUser small {
  font-size: 11px;
  color: var(--admin-text-muted);
  margin-top: 2px;
}

/* Tab indicator */
.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 3px;
  background: var(--active-color, var(--admin-primary));
  border-radius: 2px 2px 0 0;
  transition: all 0.3s ease;
}

.active-indicator {
  margin-left: 8px;
  color: currentColor;
  animation: pulse 2s ease-in-out infinite;
}

/* Breadcrumb */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  padding: 12px 20px;
  background: var(--admin-bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  color: var(--admin-text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.breadcrumb span:first-child {
  color: var(--admin-text-primary);
}

/* Tab description */
.tab-description {
  display: block;
  font-size: 16px;
  font-weight: 400;
  color: var(--admin-text-secondary);
  margin-top: 8px;
}

/* Footer stats */
.footer-stats {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.footer-stats span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Login enhancements */
.login-header {
  margin-bottom: 32px;
}

.login-header .system-status {
  margin-top: 16px;
  padding: 8px 16px;
  background: var(--admin-bg-tertiary);
  border: 1px solid var(--admin-border-color);
  border-radius: var(--admin-border-radius);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.login-footer {
  margin-top: 24px;
  text-align: center;
  color: var(--admin-text-muted);
}

.loading-spinner {
  display: inline-block;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4);
    --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5);
    --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.6);
  }
}
