export const QuebraCabecaMetrics = {
  // Registrar início do jogo
  startGame: (difficulty) => {
    const metrics = {
      gameId: 'quebra-cabeca',
      sessionId: `puzzle_${Date.now()}`,
      startTime: new Date().toISOString(),
      difficulty,
      userId: 'anonymous',
      device: navigator.userAgent
    }

    console.log('Quebra-Cabeça Game Started:', metrics)
    return metrics
  },

  // Registrar tentativa de quebra-cabeça
  recordPuzzleAttempt: (attempt) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      puzzleNumber: attempt.puzzleNumber,
      emotion: attempt.emotion,
      emotionId: attempt.emotionId,
      difficulty: attempt.difficulty,
      piecesUsed: attempt.piecesUsed,
      completionTime: attempt.completionTime,
      isCompleted: attempt.isCompleted,
      moves: attempt.moves,
      hintsUsed: attempt.hintsUsed || 0
    }

    console.log('Quebra-Cabeça Attempt:', metrics)
    return metrics
  },

  // Registrar movimento de peça
  recordPieceMove: (moveData) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'piece_moved',
      pieceId: moveData.pieceId,
      fromPosition: moveData.fromPosition,
      toPosition: moveData.toPosition,
      isCorrectPosition: moveData.isCorrectPosition,
      moveNumber: moveData.moveNumber
    }

    console.log('Quebra-Cabeça Piece Move:', metrics)
    return metrics
  },

  // Registrar conclusão de quebra-cabeça
  recordPuzzleCompletion: (completionData) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'puzzle_completed',
      emotion: completionData.emotion,
      difficulty: completionData.difficulty,
      totalTime: completionData.totalTime,
      totalMoves: completionData.totalMoves,
      efficiency: completionData.efficiency,
      score: completionData.score
    }

    console.log('Quebra-Cabeça Completed:', metrics)
    return metrics
  },

  // Registrar final do jogo
  endGame: (gameData) => {
    const metrics = {
      sessionId: gameData.sessionId,
      endTime: new Date().toISOString(),
      totalTime: gameData.totalTime,
      totalPuzzles: gameData.totalPuzzles,
      completedPuzzles: gameData.completedPuzzles,
      totalScore: gameData.totalScore,
      accuracy: gameData.accuracy,
      averageCompletionTime: gameData.averageCompletionTime,
      difficulty: gameData.difficulty,
      emotionsExplored: gameData.emotionsExplored,
      completed: gameData.completed
    }

    console.log('Quebra-Cabeça Game Ended:', metrics)
    return metrics
  },

  // Calcular estatísticas do jogo
  calculateStats: (attempts) => {
    if (!attempts || attempts.length === 0) {
      return {
        accuracy: 0,
        averageTime: 0,
        totalAttempts: 0,
        completedPuzzles: 0
      }
    }

    const completedAttempts = attempts.filter(attempt => attempt.isCompleted)
    const totalTime = completedAttempts.reduce((sum, attempt) => sum + attempt.completionTime, 0)

    return {
      accuracy: (completedAttempts.length / attempts.length) * 100,
      averageTime: completedAttempts.length > 0 ? totalTime / completedAttempts.length : 0,
      totalAttempts: attempts.length,
      completedPuzzles: completedAttempts.length,
      emotionsExplored: [...new Set(attempts.map(a => a.emotionId))].length,
      difficultyProgression: calculateDifficultyProgression(attempts),
      emotionalGrowth: calculateEmotionalGrowth(attempts),
      averageMoves: completedAttempts.length > 0
        ? completedAttempts.reduce((sum, a) => sum + a.moves, 0) / completedAttempts.length
        : 0
    }
  },

  // Analisar padrões emocionais
  analyzeEmotionalPatterns: (attempts) => {
    const emotionPerformance = {}
    const emotionAttempts = attempts.reduce((acc, attempt) => {
      if (!acc[attempt.emotionId]) {
        acc[attempt.emotionId] = []
      }
      acc[attempt.emotionId].push(attempt)
      return acc
    }, {})

    Object.entries(emotionAttempts).forEach(([emotionId, emotionData]) => {
      const completed = emotionData.filter(a => a.isCompleted).length
      const avgTime = emotionData.filter(a => a.isCompleted)
        .reduce((sum, a) => sum + a.completionTime, 0) / completed || 0

      emotionPerformance[emotionId] = {
        attempts: emotionData.length,
        completed,
        accuracy: (completed / emotionData.length) * 100,
        averageTime: avgTime,
        averageMoves: completed > 0
          ? emotionData.filter(a => a.isCompleted)
            .reduce((sum, a) => sum + a.moves, 0) / completed
          : 0
      }
    })

    return {
      emotionPerformance,
      strongestEmotions: getStrongestEmotions(emotionPerformance),
      challengingEmotions: getChallengingEmotions(emotionPerformance),
      suggestions: generateEmotionalSuggestions(attempts, emotionPerformance)
    }
  }
}

// Funções auxiliares para análise
function calculateDifficultyProgression (attempts) {
  const difficulties = attempts.map(a => a.difficulty)
  return {
    easy: difficulties.filter(d => d === 'easy').length,
    medium: difficulties.filter(d => d === 'medium').length,
    hard: difficulties.filter(d => d === 'hard').length
  }
}

function calculateEmotionalGrowth (attempts) {
  if (attempts.length < 4) return 0

  const firstHalf = attempts.slice(0, Math.floor(attempts.length / 2))
  const secondHalf = attempts.slice(Math.floor(attempts.length / 2))

  const firstAccuracy = firstHalf.filter(a => a.isCompleted).length / firstHalf.length
  const secondAccuracy = secondHalf.filter(a => a.isCompleted).length / secondHalf.length

  return ((secondAccuracy - firstAccuracy) / firstAccuracy) * 100
}

function getStrongestEmotions (emotionPerformance) {
  return Object.entries(emotionPerformance)
    .filter(([, performance]) => performance.accuracy >= 80)
    .sort(([, a], [, b]) => b.accuracy - a.accuracy)
    .slice(0, 3)
    .map(([emotion, performance]) => ({ emotion, accuracy: performance.accuracy }))
}

function getChallengingEmotions (emotionPerformance) {
  return Object.entries(emotionPerformance)
    .filter(([, performance]) => performance.accuracy < 60)
    .sort(([, a], [, b]) => a.accuracy - b.accuracy)
    .slice(0, 3)
    .map(([emotion, performance]) => ({ emotion, accuracy: performance.accuracy }))
}

function generateEmotionalSuggestions (attempts, emotionPerformance) {
  const suggestions = []

  if (attempts.length === 0) {
    suggestions.push('Comece montando quebra-cabeças para desenvolver reconhecimento emocional!')
    return suggestions
  }

  const accuracy = attempts.filter(a => a.isCompleted).length / attempts.length

  if (accuracy < 0.5) {
    suggestions.push('Vá devagar e observe bem cada peça antes de posicioná-la')
    suggestions.push('Pense na relação entre as peças e a emoção que representam')
  } else if (accuracy < 0.7) {
    suggestions.push('Você está progredindo! Observe os contextos emocionais')
    suggestions.push('Tente identificar padrões nas emoções e suas representações')
  } else if (accuracy > 0.9) {
    suggestions.push('Excelente! Você tem ótimo reconhecimento emocional')
    suggestions.push('Tente níveis mais difíceis para novos desafios')
  }

  const challengingEmotions = getChallengingEmotions(emotionPerformance)
  if (challengingEmotions.length > 0) {
    suggestions.push(`Pratique mais as emoções: ${challengingEmotions.map(e => e.emotion).join(', ')}`)
  }

  const strongEmotions = getStrongestEmotions(emotionPerformance)
  if (strongEmotions.length > 0) {
    suggestions.push(`Você se destaca em: ${strongEmotions.map(e => e.emotion).join(', ')}`)
  }

  return suggestions.length > 0 ? suggestions : ['Continue praticando para fortalecer seu reconhecimento emocional!']
}
