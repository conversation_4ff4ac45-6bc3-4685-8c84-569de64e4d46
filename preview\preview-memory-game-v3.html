<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Game V3 - Preview das 6 Atividades</title>YPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Memory Game V3 - Preview Interativo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            color: white;
        }
        
        .game-container {
            max-width: 1000px;
            margin: 0 auto;
            width: 100%;
        }
        
        .header {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem 3rem 1rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
        }
        
        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            text-align: center;
            flex: 1;
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            min-width: 100px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.25rem;
        }
        
        .activity-selector {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .activity-selector h3 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .activity-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .activity-btn {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .activity-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-1px);
        }
        
        .activity-btn.active {
            background: rgba(76, 175, 80, 0.4);
            border-color: rgba(76, 175, 80, 0.6);
        }
        
        .activity {
            display: none;
        }
        
        .activity.active {
            display: block;
        }
        
        .instruction {
            text-align: center;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .instruction h3 {
            color: white;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }
        
        .instruction p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
        }
        
        /* Visual Memory Styles */
        .sequence-display {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .memory-item {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .memory-item.highlighted {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(255, 255, 255, 0.3);
        }
        
        .memory-item.selectable {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .memory-item.selectable:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .memory-item.correct {
            background: rgba(76, 175, 80, 0.4);
            border-color: rgba(76, 175, 80, 0.7);
        }
        
        .memory-item.incorrect {
            background: rgba(244, 67, 54, 0.4);
            border-color: rgba(244, 67, 54, 0.7);
        }
        
        /* Pattern Memory Styles */
        .pattern-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            max-width: 400px;
            margin: 2rem auto;
        }
        
        .pattern-cell {
            aspect-ratio: 1;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .pattern-cell.active {
            background: rgba(33, 150, 243, 0.4);
            border-color: rgba(33, 150, 243, 0.7);
        }
        
        .pattern-cell:hover {
            transform: scale(1.05);
        }
        
        /* Working Memory Styles */
        .nback-display {
            text-align: center;
            margin: 2rem 0;
        }
        
        .nback-item {
            width: 150px;
            height: 150px;
            margin: 0 auto 2rem;
            border: 3px solid rgba(255, 255, 255, 0.4);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .nback-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        
        .nback-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.4);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .nback-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* Spatial Memory Styles */
        .spatial-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.5rem;
            max-width: 300px;
            margin: 2rem auto;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
        }
        
        .spatial-cell {
            aspect-ratio: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .spatial-cell.highlighted {
            background: rgba(255, 193, 7, 0.6);
            border-color: rgba(255, 193, 7, 0.8);
        }
        
        .spatial-cell.selected {
            background: rgba(76, 175, 80, 0.6);
            border-color: rgba(76, 175, 80, 0.8);
        }
        
        .spatial-cell:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .controls {
            text-align: center;
            margin-top: 2rem;
        }
        
        .control-btn {
            background: rgba(33, 150, 243, 0.4);
            border: 1px solid rgba(33, 150, 243, 0.6);
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.1rem;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: rgba(33, 150, 243, 0.6);
            transform: translateY(-1px);
        }
        
        .control-btn:disabled {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-indicator {
            text-align: center;
            margin: 1rem 0;
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .difficulty-selector {
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .difficulty-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
        }
        
        .difficulty-btn.active {
            background: rgba(255, 193, 7, 0.4);
            border-color: rgba(255, 193, 7, 0.6);
        }
        
        @media (max-width: 600px) {
            .header h1 {
                font-size: 1.5rem;
            }
            
            .stats {
                gap: 0.5rem;
            }
            
            .stat-item {
                min-width: 80px;
                padding: 0.75rem;
            }
            
            .activity-btn {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }
            
            .memory-item {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }
            
            .pattern-grid {
                max-width: 300px;
            }
            
            .spatial-grid {
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <div class="header">
            <h1>🧠 Memory Game V3</h1>
        </div>
        
        <!-- Stats -->
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">7</div>
                <div class="stat-label">Span Visual</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">95%</div>
                <div class="stat-label">Precisão</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">1.2s</div>
                <div class="stat-label">Tempo Médio</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">N3</div>
                <div class="stat-label">Nível</div>
            </div>
        </div>
        
        <!-- Activity Selector -->
        <div class="activity-selector">
            <h3>🎮 <strong>PREVIEW:</strong> Escolha uma modalidade de memória:</h3>
            <div class="activity-buttons">
                <button class="activity-btn active" onclick="showActivity('visual-memory')">
                    📱 <span>Visual</span>
                </button>
                <button class="activity-btn" onclick="showActivity('pattern-memory')">
                    🧩 <span>Padrões</span>
                </button>
                <button class="activity-btn" onclick="showActivity('working-memory')">
                    🔄 <span>Trabalho</span>
                </button>
                <button class="activity-btn" onclick="showActivity('spatial-memory')">
                    🗺️ <span>Espacial</span>
                </button>
            </div>
        </div>
        
        <!-- Game Area -->
        <div class="game-area">
            
            <!-- 1. Visual Memory -->
            <div id="visual-memory" class="activity active">
                <div class="instruction">
                    <h3>📱 Memória Visual</h3>
                    <p>Memorize a sequência de cores e símbolos</p>
                </div>
                
                <div class="difficulty-selector">
                    <span>Dificuldade: </span>
                    <button class="difficulty-btn" onclick="setDifficulty('easy')">Fácil (4)</button>
                    <button class="difficulty-btn active" onclick="setDifficulty('medium')">Médio (6)</button>
                    <button class="difficulty-btn" onclick="setDifficulty('hard')">Difícil (8)</button>
                </div>
                
                <div class="sequence-display" id="visual-sequence">
                    <div class="memory-item" style="background: rgba(244, 67, 54, 0.6);">🔴</div>
                    <div class="memory-item" style="background: rgba(33, 150, 243, 0.6);">🔵</div>
                    <div class="memory-item" style="background: rgba(76, 175, 80, 0.6);">🟢</div>
                    <div class="memory-item" style="background: rgba(255, 193, 7, 0.6);">🟡</div>
                    <div class="memory-item" style="background: rgba(156, 39, 176, 0.6);">🟣</div>
                    <div class="memory-item" style="background: rgba(255, 87, 34, 0.6);">🟠</div>
                </div>
                
                <div class="progress-indicator">
                    <span>Fase: Memorização (3s restantes)</span>
                </div>
                
                <div class="controls">
                    <button class="control-btn" onclick="startVisualMemory()">▶️ Iniciar Sequência</button>
                    <button class="control-btn" onclick="showVisualTest()" disabled>✅ Testar Memória</button>
                </div>
            </div>
            
            <!-- 2. Pattern Memory -->
            <div id="pattern-memory" class="activity">
                <div class="instruction">
                    <h3>🧩 Memória de Padrões</h3>
                    <p>Encontre o padrão lógico nas formas</p>
                </div>
                
                <div class="pattern-grid" id="pattern-grid">
                    <div class="pattern-cell active">🔺</div>
                    <div class="pattern-cell">🔵</div>
                    <div class="pattern-cell active">🔺</div>
                    <div class="pattern-cell">🔵</div>
                    <div class="pattern-cell active">🔺</div>
                    <div class="pattern-cell">🔵</div>
                    <div class="pattern-cell active">🔺</div>
                    <div class="pattern-cell" style="background: rgba(255, 193, 7, 0.3);">❓</div>
                </div>
                
                <div class="progress-indicator">
                    <span>Padrão: Alternância triangular-circular</span>
                </div>
                
                <div class="controls">
                    <button class="control-btn" onclick="checkPattern('circle')">🔵 Círculo</button>
                    <button class="control-btn" onclick="checkPattern('triangle')">🔺 Triângulo</button>
                    <button class="control-btn" onclick="checkPattern('square')">🔲 Quadrado</button>
                </div>
            </div>
            
            <!-- 3. Working Memory -->
            <div id="working-memory" class="activity">
                <div class="instruction">
                    <h3>🔄 Memória de Trabalho</h3>
                    <p>N-Back Test: Identifique se o símbolo é igual ao de 2 posições atrás</p>
                </div>
                
                <div class="nback-display">
                    <div class="nback-item">🌟</div>
                </div>
                
                <div class="progress-indicator">
                    <span>Posição: 5/20 | N-Back: 2 | Acertos: 4/4</span>
                </div>
                
                <div class="nback-buttons">
                    <button class="nback-btn" onclick="nbackResponse(true)">✅ IGUAL</button>
                    <button class="nback-btn" onclick="nbackResponse(false)">❌ DIFERENTE</button>
                </div>
                
                <div class="controls" style="margin-top: 1rem;">
                    <button class="control-btn" onclick="startNBack()">🎯 Novo N-Back</button>
                    <button class="control-btn" onclick="pauseNBack()">⏸️ Pausar</button>
                </div>
            </div>
            
            <!-- 4. Spatial Memory -->
            <div id="spatial-memory" class="activity">
                <div class="instruction">
                    <h3>🗺️ Memória Espacial</h3>
                    <p>Clique nas posições na mesma ordem que foram destacadas</p>
                </div>
                
                <div class="spatial-grid" id="spatial-grid">
                    <div class="spatial-cell" onclick="selectSpatial(0)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(1)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(2)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(3)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(4)"></div>
                    <div class="spatial-cell highlighted" onclick="selectSpatial(5)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(6)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(7)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(8)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(9)"></div>
                    <div class="spatial-cell highlighted" onclick="selectSpatial(10)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(11)"></div>
                    <div class="spatial-cell highlighted" onclick="selectSpatial(12)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(13)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(14)"></div>
                    <div class="spatial-cell" onclick="selectSpatial(15)"></div>
                </div>
                
                <div class="progress-indicator">
                    <span>Sequência: 3 posições | Sua vez: 1/3</span>
                </div>
                
                <div class="controls">
                    <button class="control-btn" onclick="showSpatialSequence()">👁️ Ver Sequência</button>
                    <button class="control-btn" onclick="resetSpatial()">🔄 Resetar</button>
                    <button class="control-btn" onclick="nextSpatialLevel()">⬆️ Próximo Nível</button>
                </div>
            </div>
            
        </div>
    </div>

    <script>
        // Game State
        let currentActivity = 'visual-memory';
        let visualSequence = [];
        let spatialSequence = [];
        let spatialUserSequence = [];
        let nbackSequence = [];
        let nbackPosition = 0;
        let nbackLevel = 2;
        
        // Activity Management
        function showActivity(activityId) {
            // Hide all activities
            document.querySelectorAll('.activity').forEach(el => {
                el.classList.remove('active');
            });
            
            // Show selected activity
            document.getElementById(activityId).classList.add('active');
            
            // Update button states
            document.querySelectorAll('.activity-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.closest('.activity-btn').classList.add('active');
            
            currentActivity = activityId;
            console.log(`🎮 Atividade alterada para: ${activityId}`);
        }
        
        // Visual Memory Functions
        function startVisualMemory() {
            const items = document.querySelectorAll('#visual-sequence .memory-item');
            let index = 0;
            
            const interval = setInterval(() => {
                if (index > 0) {
                    items[index - 1].classList.remove('highlighted');
                }
                if (index < items.length) {
                    items[index].classList.add('highlighted');
                    index++;
                } else {
                    clearInterval(interval);
                    setTimeout(() => {
                        items[items.length - 1].classList.remove('highlighted');
                        document.querySelector('[onclick="showVisualTest()"]').disabled = false;
                        document.querySelector('.progress-indicator span').textContent = 'Fase: Agora reproduza a sequência!';
                    }, 500);
                }
            }, 800);
        }
        
        function showVisualTest() {
            alert('🎯 Agora clique nos itens na ordem correta!');
        }
        
        function setDifficulty(level) {
            document.querySelectorAll('.difficulty-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            console.log(`🎯 Dificuldade: ${level}`);
        }
        
        // Pattern Memory Functions
        function checkPattern(answer) {
            const isCorrect = answer === 'circle';
            const lastCell = document.querySelector('#pattern-grid .pattern-cell:last-child');
            
            if (isCorrect) {
                lastCell.innerHTML = '🔵';
                lastCell.style.background = 'rgba(76, 175, 80, 0.4)';
                setTimeout(() => {
                    alert('✅ Correto! Você identificou o padrão!');
                }, 500);
            } else {
                lastCell.style.background = 'rgba(244, 67, 54, 0.4)';
                setTimeout(() => {
                    lastCell.style.background = 'rgba(255, 193, 7, 0.3)';
                    alert('❌ Tente novamente! Observe o padrão...');
                }, 1000);
            }
        }
        
        // Working Memory Functions (N-Back)
        function nbackResponse(isEqual) {
            // Simulate N-Back logic
            const correct = Math.random() > 0.3; // 70% chance of being correct
            
            if (correct) {
                alert('✅ Correto!');
                updateNBackStats(true);
            } else {
                alert('❌ Incorreto!');
                updateNBackStats(false);
            }
            
            // Next item
            setTimeout(() => {
                const symbols = ['🌟', '🔥', '⚡', '💎', '🎯', '🏆'];
                const randomSymbol = symbols[Math.floor(Math.random() * symbols.length)];
                document.querySelector('.nback-item').textContent = randomSymbol;
                nbackPosition++;
                document.querySelector('.progress-indicator span').textContent = 
                    `Posição: ${nbackPosition}/20 | N-Back: ${nbackLevel} | Acertos: ${Math.floor(Math.random() * nbackPosition)}//${nbackPosition}`;
            }, 1000);
        }
        
        function updateNBackStats(correct) {
            // Update statistics display
            console.log(`N-Back response: ${correct ? 'Correct' : 'Incorrect'}`);
        }
        
        function startNBack() {
            nbackPosition = 1;
            alert('🎯 Novo teste N-Back iniciado!');
        }
        
        function pauseNBack() {
            alert('⏸️ Teste pausado');
        }
        
        // Spatial Memory Functions
        function selectSpatial(index) {
            const cell = document.querySelectorAll('.spatial-cell')[index];
            
            if (cell.classList.contains('highlighted')) {
                cell.classList.remove('highlighted');
                cell.classList.add('selected');
                spatialUserSequence.push(index);
                
                // Check if sequence is complete
                if (spatialUserSequence.length === 3) {
                    setTimeout(() => {
                        alert('✅ Sequência completa! Próximo nível...');
                        resetSpatial();
                    }, 500);
                } else {
                    document.querySelector('.progress-indicator span').textContent = 
                        `Sequência: 3 posições | Sua vez: ${spatialUserSequence.length + 1}/3`;
                }
            } else if (!cell.classList.contains('selected')) {
                cell.style.background = 'rgba(244, 67, 54, 0.6)';
                setTimeout(() => {
                    cell.style.background = 'rgba(255, 255, 255, 0.1)';
                }, 500);
                alert('❌ Posição incorreta!');
            }
        }
        
        function showSpatialSequence() {
            // Simulate showing sequence again
            alert('👁️ Sequência será mostrada novamente...');
            resetSpatial();
            
            setTimeout(() => {
                const positions = [5, 10, 12];
                positions.forEach((pos, index) => {
                    setTimeout(() => {
                        document.querySelectorAll('.spatial-cell')[pos].classList.add('highlighted');
                    }, index * 600);
                });
            }, 1000);
        }
        
        function resetSpatial() {
            spatialUserSequence = [];
            document.querySelectorAll('.spatial-cell').forEach(cell => {
                cell.classList.remove('selected');
                cell.style.background = 'rgba(255, 255, 255, 0.1)';
            });
            document.querySelector('.progress-indicator span').textContent = 
                'Sequência: 3 posições | Sua vez: 1/3';
        }
        
        function nextSpatialLevel() {
            alert('⬆️ Avançando para o próximo nível! +1 posição na sequência');
            resetSpatial();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧠 Memory Game V3 Preview carregado!');
            
            // Start with highlighted spatial sequence
            setTimeout(() => {
                const positions = [5, 10, 12];
                positions.forEach(pos => {
                    document.querySelectorAll('.spatial-cell')[pos].classList.add('highlighted');
                });
            }, 1000);
        });
    </script>
</body>
</html>
