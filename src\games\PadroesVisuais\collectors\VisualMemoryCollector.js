/**
 * 🧠 VISUAL MEMORY COLLECTOR
 * Coletor especializado em análise de memória visual para PadroesVisuais
 * Portal Betina V3
 */

export class VisualMemoryCollector {
  constructor() {
    this.memoryTypes = {
      shortTerm: 'memória de curto prazo',
      workingMemory: 'memória de trabalho',
      visualBuffer: 'buffer visual',
      spatialMemory: 'memória espacial',
      sequentialMemory: 'memória sequencial'
    };
    
    this.memoryCapacity = {
      low: { threshold: 3, description: 'capacidade limitada' },
      medium: { threshold: 5, description: 'capacidade moderada' },
      high: { threshold: 7, description: 'capacidade alta' },
      exceptional: { threshold: 10, description: 'capacidade excepcional' }
    };
    
    this.retentionPeriods = {
      immediate: { max: 1000, weight: 1.0 },
      short: { max: 5000, weight: 1.2 },
      medium: { max: 15000, weight: 1.4 },
      long: { max: 30000, weight: 1.6 }
    };
  }

  /**
   * <PERSON>é<PERSON>do padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    // Wrapper para manter compatibilidade com a interface padrão
    return this.analyze(data);
  }

  async analyze(data) {
    if (!data || !data.memoryData) {
      console.warn('VisualMemoryCollector: Dados inválidos recebidos', data);
      return {
        shortTermMemory: 0.7,
        workingMemory: 0.7,
        visualBuffer: 0.7,
        spatialMemory: 0.7,
        sequentialMemory: 0.7,
        memoryCapacity: 0.7,
        retentionQuality: 0.7,
        interferenceResistance: 0.7
      };
    }

    return {
      shortTermMemory: this.assessShortTermMemory(data),
      workingMemory: this.assessWorkingMemory(data),
      visualBuffer: this.assessVisualBuffer(data),
      spatialMemory: this.assessSpatialMemory(data),
      sequentialMemory: this.assessSequentialMemory(data),
      memoryCapacity: this.assessMemoryCapacity(data),
      retentionQuality: this.assessRetentionQuality(data),
      interferenceResistance: this.assessInterferenceResistance(data),
      memoryStrategies: this.identifyMemoryStrategies(data),
      memoryInsights: this.generateMemoryInsights(data)
    };
  }

  assessShortTermMemory(data) {
    const sequences = data.memoryData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    // Focar em sequências curtas (3-4 elementos) com retenção imediata
    const shortSequences = sequences.filter(s => {
      const length = s.targetSequence ? s.targetSequence.length : 0;
      const retentionTime = s.showTime || 5000;
      return length <= 4 && retentionTime <= 10000;
    });
    
    if (shortSequences.length === 0) return 0.7;
    
    let memoryScore = 0;
    
    shortSequences.forEach(sequence => {
      const accuracy = this.calculateSequenceAccuracy(sequence);
      const retentionTime = sequence.showTime || 5000;
      const memoryLoad = sequence.targetSequence ? sequence.targetSequence.length : 3;
      
      // Avaliar eficácia da memória de curto prazo
      const retentionFactor = this.getRetentionFactor(retentionTime);
      const capacityFactor = Math.min(1, memoryLoad / 4);
      
      const shortTermScore = accuracy * retentionFactor * capacityFactor;
      memoryScore += shortTermScore;
    });
    
    const avgMemoryScore = memoryScore / shortSequences.length;
    
    // Bonus para consistência na memória de curto prazo
    const consistencyBonus = this.calculateMemoryConsistency(shortSequences);
    
    return Math.max(0, Math.min(1, avgMemoryScore + consistencyBonus));
  }

  assessWorkingMemory(data) {
    const sequences = data.memoryData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    // Memória de trabalho: sequências que requerem manipulação mental
    const workingMemoryTasks = sequences.filter(s => {
      const length = s.targetSequence ? s.targetSequence.length : 0;
      const complexity = this.determineSequenceComplexity(s);
      return length >= 4 || complexity > 1;
    });
    
    if (workingMemoryTasks.length === 0) return 0.7;
    
    let workingScore = 0;
    
    workingMemoryTasks.forEach(sequence => {
      const accuracy = this.calculateSequenceAccuracy(sequence);
      const processingTime = sequence.responseTime || 2000;
      const memoryLoad = sequence.targetSequence ? sequence.targetSequence.length : 3;
      
      // Avaliar capacidade de trabalho mental
      const processingEfficiency = this.calculateProcessingEfficiency(processingTime, memoryLoad);
      const workingCapacity = this.assessWorkingCapacity(memoryLoad, accuracy);
      
      const workingMemoryScore = (accuracy * 0.5) + (processingEfficiency * 0.3) + (workingCapacity * 0.2);
      workingScore += workingMemoryScore;
    });
    
    const avgWorkingScore = workingScore / workingMemoryTasks.length;
    
    return Math.max(0, Math.min(1, avgWorkingScore));
  }

  assessVisualBuffer(data) {
    const interactions = data.memoryData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Buffer visual: capacidade de manter informação visual ativa
    let bufferScore = 0;
    let validInteractions = 0;
    
    interactions.forEach(interaction => {
      if (interaction.visualProcessing) {
        const visualComplexity = this.getVisualComplexity(interaction.shapeId);
        const processingTime = interaction.responseTime || 2000;
        const accuracy = interaction.isCorrect ? 1 : 0;
        
        // Avaliar eficiência do buffer visual
        const bufferEfficiency = this.calculateBufferEfficiency(processingTime, visualComplexity, accuracy);
        bufferScore += bufferEfficiency;
        validInteractions++;
      }
    });
    
    if (validInteractions === 0) return 0.7;
    
    const avgBufferScore = bufferScore / validInteractions;
    
    // Avaliar degradação do buffer ao longo da sessão
    const degradationFactor = this.assessBufferDegradation(interactions);
    
    return Math.max(0, Math.min(1, avgBufferScore * degradationFactor));
  }

  assessSpatialMemory(data) {
    const sequences = data.memoryData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    // Memória espacial: posições e relações espaciais entre elementos
    let spatialScore = 0;
    let spatialTasks = 0;
    
    sequences.forEach(sequence => {
      if (sequence.spatialData) {
        const positionAccuracy = this.calculatePositionAccuracy(sequence.spatialData);
        const spatialComplexity = this.getSpatialComplexity(sequence.spatialData);
        const spatialMemoryScore = positionAccuracy * spatialComplexity;
        
        spatialScore += spatialMemoryScore;
        spatialTasks++;
      } else if (sequence.targetSequence) {
        // Inferir componente espacial da sequência
        const inferredSpatial = this.inferSpatialComponent(sequence);
        spatialScore += inferredSpatial;
        spatialTasks++;
      }
    });
    
    if (spatialTasks === 0) return 0.7;
    
    const avgSpatialScore = spatialScore / spatialTasks;
    
    return Math.max(0, Math.min(1, avgSpatialScore));
  }

  assessSequentialMemory(data) {
    const sequences = data.memoryData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    // Memória sequencial: ordem temporal dos elementos
    let sequentialScore = 0;
    
    sequences.forEach(sequence => {
      const orderAccuracy = this.calculateOrderAccuracy(sequence);
      const sequenceLength = sequence.targetSequence ? sequence.targetSequence.length : 3;
      const temporalComplexity = this.getTemporalComplexity(sequence);
      
      // Avaliar qualidade da memória sequencial
      const lengthFactor = Math.min(1, sequenceLength / 5);
      const sequentialMemoryScore = orderAccuracy * lengthFactor * temporalComplexity;
      
      sequentialScore += sequentialMemoryScore;
    });
    
    const avgSequentialScore = sequentialScore / sequences.length;
    
    // Bonus para manutenção de ordem em sequências longas
    const longSequences = sequences.filter(s => s.targetSequence && s.targetSequence.length >= 5);
    const longSequenceBonus = longSequences.length > 0 ? 0.1 : 0;
    
    return Math.max(0, Math.min(1, avgSequentialScore + longSequenceBonus));
  }

  assessMemoryCapacity(data) {
    const sequences = data.memoryData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    // Determinar capacidade máxima de memória
    const successfulLengths = sequences
      .filter(s => s.isCorrect && s.targetSequence)
      .map(s => s.targetSequence.length);
    
    if (successfulLengths.length === 0) return 0.7;
    
    const maxSuccessfulLength = Math.max(...successfulLengths);
    const avgSuccessfulLength = successfulLengths.reduce((sum, len) => sum + len, 0) / successfulLengths.length;
    
    // Normalizar capacidade
    const capacityScore = Math.min(1, avgSuccessfulLength / 7); // Normalizado para 7 elementos
    const maxCapacityBonus = Math.min(0.2, maxSuccessfulLength / 10); // Bonus para capacidade máxima
    
    return Math.max(0, Math.min(1, capacityScore + maxCapacityBonus));
  }

  assessRetentionQuality(data) {
    const sequences = data.memoryData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    // Avaliar qualidade da retenção ao longo do tempo
    let retentionScore = 0;
    
    sequences.forEach(sequence => {
      const showTime = sequence.showTime || 5000;
      const accuracy = this.calculateSequenceAccuracy(sequence);
      
      // Determinar período de retenção
      const retentionPeriod = this.determineRetentionPeriod(showTime);
      const retentionWeight = this.retentionPeriods[retentionPeriod].weight;
      
      const qualityScore = accuracy * retentionWeight;
      retentionScore += qualityScore;
    });
    
    const avgRetentionScore = retentionScore / sequences.length;
    
    // Normalizar pelo peso máximo
    const maxWeight = Math.max(...Object.values(this.retentionPeriods).map(p => p.weight));
    const normalizedScore = avgRetentionScore / maxWeight;
    
    return Math.max(0, Math.min(1, normalizedScore));
  }

  assessInterferenceResistance(data) {
    const sequences = data.memoryData.sequences || [];
    
    if (sequences.length < 3) return 0.7;
    
    // Avaliar resistência à interferência entre sequências
    let interferenceScores = [];
    
    for (let i = 1; i < sequences.length; i++) {
      const current = sequences[i];
      const previous = sequences[i - 1];
      
      const interference = this.calculateInterference(previous, current);
      
      if (interference > 0) {
        const resistanceScore = current.isCorrect ? 1 : 0;
        const weightedResistance = resistanceScore * (1 + interference); // Bonus para resistir a alta interferência
        
        interferenceScores.push(weightedResistance);
      }
    }
    
    if (interferenceScores.length === 0) return 0.8; // Sem interferência detectada
    
    const avgResistance = interferenceScores.reduce((sum, score) => sum + score, 0) / interferenceScores.length;
    
    return Math.max(0, Math.min(1, avgResistance));
  }

  // Métodos auxiliares

  calculateSequenceAccuracy(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    const minLength = Math.min(target.length, player.length);
    
    let matches = 0;
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        matches++;
      }
    }
    
    return matches / target.length;
  }

  getRetentionFactor(retentionTime) {
    // Fator baseado no tempo de retenção
    if (retentionTime <= 3000) return 1.0;
    if (retentionTime <= 8000) return 0.9;
    if (retentionTime <= 15000) return 0.8;
    return 0.7;
  }

  calculateMemoryConsistency(sequences) {
    if (sequences.length < 3) return 0;
    
    const accuracies = sequences.map(s => this.calculateSequenceAccuracy(s));
    const variance = this.calculateVariance(accuracies);
    
    // Menos variância = mais consistência
    return Math.max(0, (1 - variance) * 0.15);
  }

  determineSequenceComplexity(sequence) {
    const length = sequence.targetSequence ? sequence.targetSequence.length : 3;
    const uniqueShapes = sequence.targetSequence ? new Set(sequence.targetSequence).size : 1;
    
    // Complexidade baseada em comprimento e variedade
    const lengthComplexity = Math.min(2, length / 3);
    const varietyComplexity = Math.min(1.5, uniqueShapes / 2);
    
    return lengthComplexity + varietyComplexity;
  }

  calculateProcessingEfficiency(processingTime, memoryLoad) {
    // Eficiência = capacidade de processar rapidamente cargas de memória maiores
    const expectedTime = 1000 + (memoryLoad * 500); // Tempo base + tempo por item
    const efficiency = Math.max(0, Math.min(1, expectedTime / processingTime));
    
    return efficiency;
  }

  assessWorkingCapacity(memoryLoad, accuracy) {
    // Capacidade = habilidade de lidar com cargas altas mantendo precisão
    const loadFactor = Math.min(1, memoryLoad / 6);
    const capacityScore = accuracy * loadFactor;
    
    return capacityScore;
  }

  getVisualComplexity(shapeId) {
    const complexities = {
      circle: 1.0,
      square: 1.1,
      triangle: 1.2,
      star: 1.4,
      diamond: 1.3,
      heart: 1.5
    };
    
    return complexities[shapeId] || 1.0;
  }

  calculateBufferEfficiency(processingTime, visualComplexity, accuracy) {
    const timeScore = Math.max(0, Math.min(1, (3000 - processingTime) / 2000));
    const complexityBonus = visualComplexity * 0.1;
    
    return (accuracy * 0.6) + (timeScore * 0.4) + complexityBonus;
  }

  assessBufferDegradation(interactions) {
    if (interactions.length < 5) return 1.0;
    
    const firstQuarter = interactions.slice(0, Math.floor(interactions.length / 4));
    const lastQuarter = interactions.slice(-Math.floor(interactions.length / 4));
    
    const firstAccuracy = firstQuarter.filter(i => i.isCorrect).length / firstQuarter.length;
    const lastAccuracy = lastQuarter.filter(i => i.isCorrect).length / lastQuarter.length;
    
    // Se não houve degradação significativa, manter score alto
    const degradation = firstAccuracy - lastAccuracy;
    const degradationFactor = Math.max(0.7, 1 - Math.max(0, degradation));
    
    return degradationFactor;
  }

  calculatePositionAccuracy(spatialData) {
    // Simplificado - calcularia precisão das posições espaciais
    if (!spatialData.targetPositions || !spatialData.playerPositions) return 0.7;
    
    let matches = 0;
    const minLength = Math.min(spatialData.targetPositions.length, spatialData.playerPositions.length);
    
    for (let i = 0; i < minLength; i++) {
      const targetPos = spatialData.targetPositions[i];
      const playerPos = spatialData.playerPositions[i];
      
      // Calcular distância espacial
      const distance = Math.sqrt(
        Math.pow(targetPos.x - playerPos.x, 2) + 
        Math.pow(targetPos.y - playerPos.y, 2)
      );
      
      // Considerar correto se estiver dentro de um threshold
      if (distance <= 50) { // 50 pixels de tolerância
        matches++;
      }
    }
    
    return matches / spatialData.targetPositions.length;
  }

  getSpatialComplexity(spatialData) {
    // Complexidade baseada na distribuição espacial
    if (!spatialData.targetPositions) return 1.0;
    
    const positions = spatialData.targetPositions;
    const spread = this.calculateSpatialSpread(positions);
    const density = this.calculateSpatialDensity(positions);
    
    return Math.min(2, spread + density);
  }

  inferSpatialComponent(sequence) {
    // Inferir componente espacial baseado na sequência
    if (!sequence.targetSequence) return 0.7;
    
    const uniqueShapes = new Set(sequence.targetSequence).size;
    const totalShapes = sequence.targetSequence.length;
    
    // Mais variedade espacial = maior demanda espacial
    const spatialDemand = uniqueShapes / totalShapes;
    const accuracy = this.calculateSequenceAccuracy(sequence);
    
    return accuracy * spatialDemand;
  }

  calculateOrderAccuracy(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    
    // Avaliar ordem correta
    let correctOrder = 0;
    const minLength = Math.min(target.length, player.length);
    
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        correctOrder++;
      } else {
        break; // Para na primeira ordem incorreta
      }
    }
    
    return correctOrder / target.length;
  }

  getTemporalComplexity(sequence) {
    const length = sequence.targetSequence ? sequence.targetSequence.length : 3;
    const showTime = sequence.showTime || 5000;
    
    // Complexidade temporal baseada em comprimento e tempo disponível
    const timePerItem = showTime / length;
    const timeComplexity = Math.min(1.5, 5000 / timePerItem);
    
    return timeComplexity;
  }

  determineRetentionPeriod(showTime) {
    if (showTime <= this.retentionPeriods.immediate.max) return 'immediate';
    if (showTime <= this.retentionPeriods.short.max) return 'short';
    if (showTime <= this.retentionPeriods.medium.max) return 'medium';
    return 'long';
  }

  calculateInterference(previous, current) {
    if (!previous.targetSequence || !current.targetSequence) return 0;
    
    // Calcular similaridade entre sequências
    const prevSet = new Set(previous.targetSequence);
    const currSet = new Set(current.targetSequence);
    
    let commonElements = 0;
    prevSet.forEach(element => {
      if (currSet.has(element)) {
        commonElements++;
      }
    });
    
    const totalElements = new Set([...previous.targetSequence, ...current.targetSequence]).size;
    const similarity = commonElements / totalElements;
    
    // Maior similaridade = maior interferência
    return similarity;
  }

  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  calculateSpatialSpread(positions) {
    if (positions.length < 2) return 1.0;
    
    const xCoords = positions.map(p => p.x);
    const yCoords = positions.map(p => p.y);
    
    const xRange = Math.max(...xCoords) - Math.min(...xCoords);
    const yRange = Math.max(...yCoords) - Math.min(...yCoords);
    
    // Normalizar spread
    return Math.min(1.5, (xRange + yRange) / 1000);
  }

  calculateSpatialDensity(positions) {
    if (positions.length < 2) return 1.0;
    
    // Calcular densidade baseada na distância média entre pontos
    let totalDistance = 0;
    let pairs = 0;
    
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const distance = Math.sqrt(
          Math.pow(positions[i].x - positions[j].x, 2) + 
          Math.pow(positions[i].y - positions[j].y, 2)
        );
        totalDistance += distance;
        pairs++;
      }
    }
    
    const avgDistance = pairs > 0 ? totalDistance / pairs : 100;
    
    // Menor distância média = maior densidade = maior complexidade
    return Math.min(1.5, 200 / avgDistance);
  }

  identifyMemoryStrategies(data) {
    const sequences = data.memoryData.sequences || [];
    const strategies = [];
    
    // Identificar estratégias baseadas em padrões de desempenho
    const accuracyProgression = sequences.map(s => this.calculateSequenceAccuracy(s));
    
    // Estratégia de chunking
    if (this.detectChunkingStrategy(sequences)) {
      strategies.push('chunking');
    }
    
    // Estratégia de repetição
    if (this.detectRehearsalStrategy(sequences)) {
      strategies.push('rehearsal');
    }
    
    // Estratégia visual
    if (this.detectVisualStrategy(sequences)) {
      strategies.push('visual_encoding');
    }
    
    // Estratégia sequencial
    if (this.detectSequentialStrategy(sequences)) {
      strategies.push('sequential_organization');
    }
    
    return strategies;
  }

  detectChunkingStrategy(sequences) {
    // Detectar se há melhoria em sequências longas (indicativo de chunking)
    const longSequences = sequences.filter(s => s.targetSequence && s.targetSequence.length >= 4);
    if (longSequences.length < 2) return false;
    
    const firstHalf = longSequences.slice(0, Math.floor(longSequences.length / 2));
    const secondHalf = longSequences.slice(Math.floor(longSequences.length / 2));
    
    const firstAccuracy = firstHalf.reduce((sum, s) => sum + this.calculateSequenceAccuracy(s), 0) / firstHalf.length;
    const secondAccuracy = secondHalf.reduce((sum, s) => sum + this.calculateSequenceAccuracy(s), 0) / secondHalf.length;
    
    return secondAccuracy > firstAccuracy + 0.1; // Melhoria de 10%
  }

  detectRehearsalStrategy(sequences) {
    // Detectar padrões de tempo que indicam repetição mental
    const responseTimes = sequences
      .filter(s => s.responseTime)
      .map(s => s.responseTime);
    
    if (responseTimes.length < 3) return false;
    
    // Verificar se há tempos consistentemente maiores (indicativo de rehearsal)
    const avgTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const longTimes = responseTimes.filter(time => time > avgTime * 1.2);
    
    return longTimes.length > responseTimes.length * 0.3; // 30% dos tempos são longos
  }

  detectVisualStrategy(sequences) {
    // Detectar se há melhor desempenho com formas visualmente distintas
    let visuallyDistinctCorrect = 0;
    let visuallySimilarCorrect = 0;
    let distinctTotal = 0;
    let similarTotal = 0;
    
    sequences.forEach(sequence => {
      if (sequence.targetSequence) {
        const uniqueShapes = new Set(sequence.targetSequence).size;
        const isVisuallyDistinct = uniqueShapes / sequence.targetSequence.length > 0.7;
        
        if (isVisuallyDistinct) {
          distinctTotal++;
          if (sequence.isCorrect) visuallyDistinctCorrect++;
        } else {
          similarTotal++;
          if (sequence.isCorrect) visuallySimilarCorrect++;
        }
      }
    });
    
    if (distinctTotal === 0 || similarTotal === 0) return false;
    
    const distinctAccuracy = visuallyDistinctCorrect / distinctTotal;
    const similarAccuracy = visuallySimilarCorrect / similarTotal;
    
    return distinctAccuracy > similarAccuracy + 0.15; // 15% melhor com elementos distintos
  }

  detectSequentialStrategy(sequences) {
    // Detectar se há melhor desempenho mantendo ordem sequencial
    let sequentialErrors = 0;
    let totalSequences = 0;
    
    sequences.forEach(sequence => {
      if (sequence.targetSequence && sequence.playerSequence) {
        totalSequences++;
        
        // Verificar se os erros são principalmente de ordem
        const orderAccuracy = this.calculateOrderAccuracy(sequence);
        const totalAccuracy = this.calculateSequenceAccuracy(sequence);
        
        if (orderAccuracy < totalAccuracy - 0.2) {
          sequentialErrors++;
        }
      }
    });
    
    if (totalSequences === 0) return false;
    
    // Baixa taxa de erros sequenciais indica uso de estratégia sequencial
    return sequentialErrors / totalSequences < 0.3;
  }

  generateMemoryInsights(data) {
    const insights = [];
    const sequences = data.memoryData.sequences || [];
    
    if (sequences.length === 0) return insights;
    
    // Analisar capacidade de memória
    const maxLength = Math.max(...sequences.filter(s => s.isCorrect && s.targetSequence).map(s => s.targetSequence.length));
    
    if (maxLength <= 3) {
      insights.push('Capacidade de memória visual limitada a sequências curtas');
    } else if (maxLength >= 6) {
      insights.push('Excelente capacidade de memória visual para sequências longas');
    }
    
    // Analisar consistência
    const accuracies = sequences.map(s => this.calculateSequenceAccuracy(s));
    const variance = this.calculateVariance(accuracies);
    
    if (variance > 0.3) {
      insights.push('Desempenho inconsistente na memória visual');
    } else if (variance < 0.1) {
      insights.push('Desempenho muito consistente na memória visual');
    }
    
    // Analisar estratégias identificadas
    const strategies = this.identifyMemoryStrategies(data);
    if (strategies.length > 0) {
      insights.push(`Estratégias de memória identificadas: ${strategies.join(', ')}`);
    }
    
    return insights;
  }
}
