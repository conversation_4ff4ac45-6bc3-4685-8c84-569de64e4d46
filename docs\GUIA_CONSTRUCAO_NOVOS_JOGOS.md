# 🎮 Guia para Construção de Novos Jogos - Portal Betina V3

## 📋 Índice

1. [<PERSON>isão Geral](#visão-geral)
2. [Estrutura Básica](#estrutura-básica)
3. [Implementação Passo a Passo](#implementação-passo-a-passo)
4. [Integração Multissensorial](#integração-multissensorial)
5. [Sistema de Métricas](#sistema-de-métricas)
6. [Acessibilidade](#acessibilidade)
7. [Boas Práticas](#boas-práticas)
8. [Templates e Exemplos](#templates-e-exemplos)
9. [Validação e Testes](#validação-e-testes)
10. [Deploy e Integração](#deploy-e-integração)

---

## 🎯 Visão Geral

O Portal Betina V3 é uma plataforma terapêutica que utiliza jogos para auxiliar no desenvolvimento cognitivo e terapêutico de crianças. Cada jogo deve seguir padrões específicos para garantir:

- **Integração multissensorial** completa
- **Coleta de métricas** terapêuticas avançadas
- **Acessibilidade** total (WCAG 2.1)
- **Experiência** consistente entre jogos
- **Performance** otimizada

### 🧠 Objetivos Terapêuticos

Cada jogo deve focar em pelo menos uma área:
- 🧠 **Memória** (visual, auditiva, sequencial)
- 🎯 **Atenção** (concentração, foco)
- 🔢 **Raciocínio** (lógico, matemático)
- 🎨 **Criatividade** (expressão, imaginação)
- 👥 **Social** (comunicação, cooperação)
- ⚡ **Motor** (coordenação, precisão)

---

## 🏗️ Estrutura Básica

### 📁 Estrutura de Pastas

```
src/games/NomeDoJogo/
├── NomeDoJogoGame.jsx          # Componente principal
├── NomeDoJogo.module.css       # Estilos modulares
├── NomeDoJogoConfig.js         # Configurações do jogo
├── NomeDoJogoMetrics.js        # Sistema de métricas
├── collectors/                 # Coletores multissensoriais
│   ├── index.js               # Hub principal
│   ├── SpecificCollector1.js  # Coletor específico 1
│   └── SpecificCollector2.js  # Coletor específico 2
├── components/                 # Componentes específicos
│   ├── GameBoard.jsx          # Tabuleiro/área de jogo
│   ├── GamePiece.jsx          # Peças/elementos
│   └── GameControls.jsx       # Controles específicos
├── utils/                      # Utilitários
│   ├── gameLogic.js           # Lógica do jogo
│   ├── soundManager.js        # Gerenciamento de áudio
│   └── animationHelpers.js    # Helpers de animação
└── assets/                     # Recursos específicos
    ├── sounds/                # Arquivos de áudio
    ├── images/                # Imagens específicas
    └── animations/            # Animações
```

### 🔧 Dependências Básicas

```javascript
// Dependências obrigatórias
import React, { useState, useEffect, useRef, useCallback, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { v4 as uuidv4 } from 'uuid';

// Contextos do sistema
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';

// Componentes padrão
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// Hooks obrigatórios
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';

// Configurações específicas
import { NomeDoJogoConfig } from './NomeDoJogoConfig.js';
import { NomeDoJogoMetrics } from './NomeDoJogoMetrics.js';
import { NomeDoJogoCollectorsHub } from './collectors/index.js';

// Estilos
import styles from './NomeDoJogo.module.css';
```

---

## 🛠️ Implementação Passo a Passo

### 1️⃣ Criação do Arquivo de Configuração

```javascript
// src/games/NomeDoJogo/NomeDoJogoConfig.js
export const NOME_DO_JOGO_CONFIG = {
  // Configurações básicas
  gameId: 'nome-do-jogo',
  gameName: 'Nome do Jogo',
  gameType: 'cognitive', // cognitive, motor, creative, social
  
  // Dificuldades
  difficulties: [
    {
      id: 'easy',
      name: 'Fácil',
      description: 'Descrição da dificuldade fácil',
      icon: '😊',
      config: {
        // Parâmetros específicos da dificuldade
        timeLimit: 60000,
        maxAttempts: 5,
        targetScore: 100
      }
    },
    {
      id: 'medium',
      name: 'Médio',
      description: 'Descrição da dificuldade média',
      icon: '🎯',
      config: {
        timeLimit: 45000,
        maxAttempts: 3,
        targetScore: 150
      }
    },
    {
      id: 'hard',
      name: 'Avançado',
      description: 'Descrição da dificuldade avançada',
      icon: '🚀',
      config: {
        timeLimit: 30000,
        maxAttempts: 2,
        targetScore: 200
      }
    }
  ],
  
  // Configurações de gameplay
  gameplay: {
    maxLives: 3,
    timeBonus: true,
    streakBonus: true,
    adaptiveDifficulty: true
  },
  
  // Configurações de áudio
  audio: {
    enableSoundEffects: true,
    enableBackgroundMusic: false,
    enableTTS: true
  },
  
  // Configurações de animação
  animation: {
    enableParticles: true,
    transitionDuration: 300,
    feedbackDuration: 1500
  }
};

// Mensagens de encorajamento
export const ENCOURAGEMENT_MESSAGES = {
  success: [
    'Excelente trabalho!',
    'Muito bem!',
    'Fantástico!',
    'Continue assim!'
  ],
  partial: [
    'Quase lá!',
    'Você está no caminho certo!',
    'Tente mais uma vez!'
  ],
  error: [
    'Não desista!',
    'Vamos tentar novamente!',
    'Você consegue!'
  ]
};
```

### 2️⃣ Criação do Sistema de Métricas

```javascript
// src/games/NomeDoJogo/NomeDoJogoMetrics.js
import { MetricsCollector } from '../../api/services/MetricsCollector.js';

export class NomeDoJogoMetrics extends MetricsCollector {
  constructor() {
    super('NomeDoJogo');
    this.collectorsHub = null;
    this.gameData = {
      startTime: null,
      endTime: null,
      interactions: [],
      achievements: []
    };
  }

  // Conectar com hub de coletores
  static initializeAdvancedCollectors(config) {
    try {
      const { NomeDoJogoCollectorsHub } = require('./collectors/index.js');
      const hub = new NomeDoJogoCollectorsHub();
      
      const result = hub.startSession({
        sessionId: config.sessionId || `nomejogo_${Date.now()}`,
        startTime: Date.now(),
        gameConfig: config.gameConfig || {},
        difficulty: config.difficulty || 'medium',
        playerProfile: config.playerProfile || {}
      });
      
      this.collectorsHub = hub;
      
      return {
        success: true,
        sessionId: result.sessionId,
        activeCollectors: result.activeCollectors || []
      };
    } catch (error) {
      console.error('Erro ao inicializar coletores avançados:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Registrar interação avançada
  static recordAdvancedInteraction(interactionData) {
    try {
      if (this.collectorsHub) {
        return this.collectorsHub.processInteraction(interactionData);
      }
      
      // Fallback para coleta básica
      console.log('📊 Interação registrada:', interactionData);
      return { success: true, fallback: true };
    } catch (error) {
      console.error('Erro ao registrar interação:', error);
      return { success: false, error: error.message };
    }
  }

  // Obter hub de coletores
  static getCollectorsHub() {
    return this.collectorsHub;
  }

  // Métodos específicos do jogo
  startGame(config) {
    this.gameData.startTime = Date.now();
    this.recordEvent('game_start', {
      gameType: 'NomeDoJogo',
      difficulty: config.difficulty,
      timestamp: this.gameData.startTime
    });
  }

  recordInteraction(action, data) {
    const interaction = {
      timestamp: Date.now(),
      action,
      ...data
    };
    
    this.gameData.interactions.push(interaction);
    this.recordEvent('interaction', interaction);
    
    // Registrar também no sistema avançado
    NomeDoJogoMetrics.recordAdvancedInteraction({
      type: 'game_interaction',
      action,
      ...data,
      timestamp: Date.now()
    });
  }

  endGame(finalData) {
    this.gameData.endTime = Date.now();
    const sessionDuration = this.gameData.endTime - this.gameData.startTime;
    
    const gameResults = {
      ...finalData,
      sessionDuration,
      totalInteractions: this.gameData.interactions.length,
      timestamp: this.gameData.endTime
    };
    
    this.recordEvent('game_end', gameResults);
    return gameResults;
  }
}
```

### 3️⃣ Criação do Hub de Coletores

```javascript
// src/games/NomeDoJogo/collectors/index.js
import { BaseCollectorHub } from '../../../api/services/collectors/BaseCollectorHub.js';
import { ColetoEspecifico1 } from './ColetoEspecifico1.js';
import { ColetoEspecifico2 } from './ColetoEspecifico2.js';

export class NomeDoJogoCollectorsHub extends BaseCollectorHub {
  constructor() {
    super('NomeDoJogo');
    
    // Inicializar coletores específicos
    this.collectors = {
      especifico1: new ColetoEspecifico1(),
      especifico2: new ColetoEspecifico2()
    };
    
    console.log('🎮 NomeDoJogo CollectorsHub inicializado');
  }

  // Validar dados específicos do jogo
  validateGameData(gameData) {
    const requiredFields = ['action', 'timestamp'];
    const hasBasicData = requiredFields.every(field => 
      gameData.hasOwnProperty(field) && gameData[field] !== undefined
    );

    if (!hasBasicData) {
      console.warn('Dados básicos ausentes:', { 
        received: Object.keys(gameData), 
        required: requiredFields 
      });
      return false;
    }

    return true;
  }

  // Processar interação específica do jogo
  async processInteraction(interactionData) {
    try {
      if (!this.validateGameData(interactionData)) {
        throw new Error('Dados de interação inválidos');
      }

      const results = {};
      const insights = [];

      // Processar com cada coletor
      for (const [name, collector] of Object.entries(this.collectors)) {
        try {
          const result = await collector.process(interactionData);
          results[name] = result;
          
          if (result.insights) {
            insights.push(...result.insights);
          }
        } catch (error) {
          console.warn(`Erro no coletor ${name}:`, error);
          results[name] = { error: error.message };
        }
      }

      // Integrar insights de todos os coletores
      const integratedInsights = this.integrateInsights(insights);

      console.log('🎮 NomeDoJogo - Interação processada:', {
        collectors: Object.keys(results),
        integratedInsights: integratedInsights.slice(0, 5) // Limitado para log
      });

      return {
        success: true,
        collectors: results,
        insights: integratedInsights
      };
    } catch (error) {
      console.error('❌ Erro ao processar interação:', error);
      return { success: false, error: error.message };
    }
  }

  // Integrar insights de múltiplos coletores
  integrateInsights(insights) {
    const integrated = [];
    
    // Agrupar por tipo de insight
    const groupedInsights = insights.reduce((groups, insight) => {
      const type = insight.type || 'general';
      if (!groups[type]) groups[type] = [];
      groups[type].push(insight);
      return groups;
    }, {});

    // Processar cada grupo
    Object.entries(groupedInsights).forEach(([type, typeInsights]) => {
      if (typeInsights.length > 1) {
        // Múltiplos insights do mesmo tipo - criar insight consolidado
        integrated.push({
          type: `consolidated_${type}`,
          confidence: Math.max(...typeInsights.map(i => i.confidence || 0.5)),
          data: typeInsights.map(i => i.data),
          timestamp: Date.now()
        });
      } else {
        // Insight único - adicionar diretamente
        integrated.push(typeInsights[0]);
      }
    });

    return integrated;
  }

  // Gerar relatório detalhado
  generateDetailedReport() {
    const reports = {};
    
    Object.entries(this.collectors).forEach(([name, collector]) => {
      if (collector.generateReport) {
        reports[name] = collector.generateReport();
      }
    });

    return {
      sessionId: this.sessionId,
      gameType: 'NomeDoJogo',
      collectorReports: reports,
      sessionSummary: this.getSessionSummary(),
      timestamp: Date.now()
    };
  }

  // Gerar recomendações terapêuticas
  generateRecommendations() {
    const recommendations = [];
    
    Object.entries(this.collectors).forEach(([name, collector]) => {
      if (collector.generateRecommendations) {
        const collectorRecs = collector.generateRecommendations();
        recommendations.push(...collectorRecs);
      }
    });

    return recommendations;
  }
}
```

### 4️⃣ Componente Principal do Jogo

```javascript
// src/games/NomeDoJogo/NomeDoJogoGame.jsx
const NomeDoJogoGame = ({ onBack }) => {
  // 🏠 Contextos obrigatórios
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();
  
  // 🎮 Inicialização de coletores e hooks
  const [collectorsHub] = useState(() => new NomeDoJogoCollectorsHub());
  
  const multisensoryIntegration = useMultisensoryIntegration('nome-do-jogo', collectorsHub, {
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info'
  });

  const {
    startSession,
    endSession,
    recordInteraction,
    updateMetrics,
    sessionId,
    isSessionActive
  } = useUnifiedGameLogic('nome-do-jogo');

  // 🔊 Sistema TTS padronizado
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('nomeDoJogo_ttsActive');
    return saved !== null ? JSON.parse(saved) : true;
  });

  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) return;
    
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // 🎯 Estados do jogo
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameState, setGameState] = useState({
    status: 'waiting', // waiting, playing, completed
    difficulty: NOME_DO_JOGO_CONFIG.difficulties[0].id,
    score: 0,
    level: 1,
    lives: NOME_DO_JOGO_CONFIG.gameplay.maxLives,
    startTime: null,
    // Estados específicos do seu jogo aqui
  });

  // 🚀 Inicializar jogo
  const initializeGame = useCallback(async (difficulty) => {
    try {
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty,
        startTime: Date.now()
      }));

      setShowStartScreen(false);

      // Inicializar métricas
      NomeDoJogoMetrics.prototype.startGame({
        gameType: 'NomeDoJogo',
        difficulty,
        timestamp: Date.now()
      });

      // Inicializar integração multissensorial
      await multisensoryIntegration.initializeSession(`session_${Date.now()}`, {
        difficulty,
        gameMode: 'nome_do_jogo_mode',
        userId: user?.id || 'anonymous'
      });

      // Conectar métricas ao backend
      if (startSession) {
        await startSession({
          gameType: 'nome-do-jogo',
          difficulty,
          userId: user?.id || 'anonymous'
        });
      }

      // TTS de boas-vindas
      speak(`Bem-vindo ao Nome do Jogo! Dificuldade: ${difficulty}. Vamos começar!`, {
        rate: 0.8
      });

    } catch (error) {
      console.error('Erro ao inicializar jogo:', error);
    }
  }, [multisensoryIntegration, startSession, speak, user]);

  // 🎮 Lógica principal do jogo
  const handleGameAction = useCallback(async (action, data) => {
    try {
      // Registrar interação
      await recordInteraction?.(action, {
        ...data,
        timestamp: Date.now(),
        gameState: gameState.status
      });

      // Processar com coletores avançados
      await multisensoryIntegration.recordInteraction(action, data);

      // Atualizar estado do jogo baseado na ação
      // (implementar lógica específica do seu jogo aqui)
      
    } catch (error) {
      console.error('Erro ao processar ação do jogo:', error);
    }
  }, [recordInteraction, multisensoryIntegration, gameState]);

  // 🏁 Finalizar jogo
  const finishGame = useCallback(async () => {
    try {
      const sessionDuration = Date.now() - gameState.startTime;
      
      // Finalizar sessão multissensorial
      await multisensoryIntegration.finalizeSession({
        finalScore: gameState.score,
        sessionDuration,
        difficulty: gameState.difficulty
      });

      // Finalizar sessão unificada
      if (endSession) {
        await endSession({
          finalScore: gameState.score,
          sessionDuration,
          success: true
        });
      }

      // Feedback final
      speak(`Jogo finalizado! Sua pontuação foi ${gameState.score} pontos. Parabéns!`);

    } catch (error) {
      console.error('Erro ao finalizar jogo:', error);
    }
  }, [gameState, multisensoryIntegration, endSession, speak]);

  // 🔄 Cleanup effect
  useEffect(() => {
    return () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  return (
    <div 
      className={`${styles.container} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
    >
      {showStartScreen ? (
        <GameStartScreen
          gameTitle="Nome do Jogo"
          gameSubtitle="Descrição breve do objetivo terapêutico"
          gameInstruction="Instruções claras de como jogar"
          gameIcon="🎮"
          difficulties={NOME_DO_JOGO_CONFIG.difficulties}
          onStart={initializeGame}
          onBack={onBack}
          customContent={
            // Conteúdo personalizado opcional
            <div className={styles.gamePreview}>
              {/* Prévia visual do jogo */}
            </div>
          }
        />
      ) : (
        <div className={styles.gameContent}>
          {/* Interface principal do jogo */}
          <div className={styles.gameHeader}>
            <h1>{NOME_DO_JOGO_CONFIG.gameName}</h1>
            <button 
              className={`${styles.ttsButton} ${ttsActive ? styles.active : ''}`}
              onClick={() => setTtsActive(!ttsActive)}
            >
              {ttsActive ? '🔊' : '🔇'}
            </button>
          </div>

          <div className={styles.gameStats}>
            <div className={styles.stat}>
              <span>Pontos: {gameState.score}</span>
            </div>
            <div className={styles.stat}>
              <span>Nível: {gameState.level}</span>
            </div>
            <div className={styles.stat}>
              <span>Vidas: {gameState.lives}</span>
            </div>
          </div>

          <div className={styles.gameArea}>
            {/* Área principal do jogo - implementar de acordo com o jogo */}
          </div>

          <div className={styles.gameControls}>
            <button onClick={finishGame}>
              🏠 Finalizar
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NomeDoJogoGame;
```

---

## 🔄 Integração Multissensorial

### Implementação Obrigatória

Todo jogo deve implementar o hook `useMultisensoryIntegration`:

```javascript
const multisensoryIntegration = useMultisensoryIntegration('nome-do-jogo', collectorsHub, {
  autoUpdate: true,
  enablePatternAnalysis: true,
  logLevel: 'info'
});

// Inicializar sessão
await multisensoryIntegration.initializeSession(sessionId, {
  difficulty: selectedDifficulty,
  gameMode: 'specific_mode',
  userId: user?.id || 'anonymous'
});

// Registrar interações
await multisensoryIntegration.recordInteraction('action_type', {
  // dados da interação
});

// Finalizar sessão
await multisensoryIntegration.finalizeSession({
  finalScore: score,
  sessionDuration: duration,
  difficulty: difficulty
});
```

### Tipos de Dados Coletados

- **Visuais**: Movimentos oculares, tempo de fixação, padrões de varredura
- **Auditivos**: Reação a sons, tempo de resposta auditiva
- **Táteis**: Pressão de toque, gestos, coordenação motora
- **Movimento**: Acelerômetro, giroscópio, posição do dispositivo

---

## 📊 Sistema de Métricas

### Métricas Obrigatórias

```javascript
// Eventos básicos obrigatórios
recordInteraction('game_start', {
  difficulty: 'easy',
  timestamp: Date.now()
});

recordInteraction('user_action', {
  action: 'click',
  element: 'button_id',
  correct: true,
  responseTime: 1500,
  timestamp: Date.now()
});

recordInteraction('game_end', {
  score: 150,
  duration: 45000,
  accuracy: 85,
  timestamp: Date.now()
});
```

### Métricas Específicas do Jogo

Cada jogo deve definir suas próprias métricas relevantes:

```javascript
// Exemplo para jogo de memória
recordInteraction('card_flip', {
  cardId: 'card_1',
  pairFound: false,
  attempts: 3,
  timeElapsed: 2500
});

// Exemplo para jogo musical
recordInteraction('note_played', {
  note: 'C4',
  expected: 'C4',
  correct: true,
  sequencePosition: 2
});
```

---

## ♿ Acessibilidade

### Requisitos Obrigatórios

1. **ARIA Labels**: Todos os elementos interativos devem ter labels descritivos
2. **Navegação por Teclado**: Suporte completo para navegação sem mouse
3. **Contraste**: Mínimo 4.5:1 para texto normal, 3:1 para texto grande
4. **TTS**: Sistema de text-to-speech integrado
5. **Tamanhos de Fonte**: Suporte a diferentes tamanhos
6. **Movimento Reduzido**: Opção para reduzir animações

### Implementação

```javascript
// Contexto de acessibilidade
const { settings } = useAccessibilityContext();

// Aplicar configurações
<div 
  className={`${styles.container} ${settings.reducedMotion ? 'reduced-motion' : ''}`}
  data-font-size={settings.fontSize}
  data-theme={settings.colorScheme}
>
  {/* Conteúdo do jogo */}
</div>

// TTS obrigatório
const speak = useCallback((text, options = {}) => {
  if (!ttsActive || !('speechSynthesis' in window)) return;
  
  window.speechSynthesis.cancel();
  const utterance = new SpeechSynthesisUtterance(text);
  utterance.lang = 'pt-BR';
  window.speechSynthesis.speak(utterance);
}, [ttsActive]);

// Navegação por teclado
useEffect(() => {
  const handleKeyDown = (event) => {
    switch (event.key) {
      case 'Enter':
      case ' ':
        // Ativar elemento focado
        break;
      case 'Escape':
        // Voltar/cancelar
        break;
      case 'Tab':
        // Navegação (padrão do browser)
        break;
    }
  };

  document.addEventListener('keydown', handleKeyDown);
  return () => document.removeEventListener('keydown', handleKeyDown);
}, []);
```

---

## 💡 Boas Práticas

### 🏗️ Arquitetura

1. **Separação de Responsabilidades**: Lógica, apresentação e dados separados
2. **Hooks Customizados**: Para lógica reutilizável
3. **Componentes Modulares**: Pequenos e focados em uma função
4. **Error Boundaries**: Para capturar e tratar erros
5. **Loading States**: Feedback visual para operações assíncronas

### 🎨 UI/UX

1. **Consistência Visual**: Seguir o design system do portal
2. **Feedback Imediato**: Resposta visual/sonora para todas as ações
3. **Progressão Clara**: O usuário deve saber onde está no jogo
4. **Animações Suaves**: Transições naturais entre estados
5. **Cores Terapêuticas**: Paleta calma e acolhedora

### ⚡ Performance

1. **Lazy Loading**: Carregar recursos apenas quando necessário
2. **Otimização de Imagens**: Formatos modernos (WebP) e tamanhos adequados
3. **Debounce**: Para ações frequentes como digitação
4. **Memoização**: React.memo, useMemo, useCallback quando apropriado
5. **Bundle Size**: Monitorar e otimizar o tamanho dos arquivos

### 🔒 Segurança

1. **Validação de Entrada**: Sempre validar dados do usuário
2. **Sanitização**: Limpar dados antes de usar
3. **Rate Limiting**: Evitar spam de ações
4. **Dados Sensíveis**: Nunca expor informações pessoais
5. **HTTPS**: Sempre usar conexões seguras

---

## 📝 Templates e Exemplos

### Template Básico

```javascript
// Template básico para um novo jogo
import React, { useState, useEffect, useCallback, useContext } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import styles from './MeuJogo.module.css';

const MeuJogoGame = ({ onBack }) => {
  // Contextos
  const { user } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();
  
  // Estados básicos
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameState, setGameState] = useState('waiting');
  const [score, setScore] = useState(0);
  
  // Hooks obrigatórios
  const { startSession, endSession, recordInteraction } = useUnifiedGameLogic('meu-jogo');
  const multisensoryIntegration = useMultisensoryIntegration('meu-jogo', null);
  
  // TTS
  const [ttsActive, setTtsActive] = useState(true);
  const speak = useCallback((text) => {
    if (!ttsActive) return;
    window.speechSynthesis.cancel();
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);
  
  // Inicializar jogo
  const startGame = async (difficulty) => {
    setShowStartScreen(false);
    setGameState('playing');
    
    await multisensoryIntegration.initializeSession(`session_${Date.now()}`, {
      difficulty,
      userId: user?.id || 'anonymous'
    });
    
    speak(`Bem-vindo ao meu jogo! Dificuldade: ${difficulty}`);
  };
  
  return (
    <div className={styles.container}>
      {showStartScreen ? (
        <GameStartScreen
          gameTitle="Meu Jogo"
          gameSubtitle="Descrição do jogo"
          gameInstruction="Como jogar"
          gameIcon="🎮"
          difficulties={[
            { id: 'easy', name: 'Fácil', description: 'Para iniciantes', icon: '😊' }
          ]}
          onStart={startGame}
          onBack={onBack}
        />
      ) : (
        <div className={styles.gameContent}>
          {/* Conteúdo do jogo aqui */}
          <h1>Meu Jogo</h1>
          <p>Pontuação: {score}</p>
        </div>
      )}
    </div>
  );
};

export default MeuJogoGame;
```

### Exemplos de Coletores

```javascript
// Exemplo de coletor específico
export class AtencaoVisualCollector {
  constructor() {
    this.data = {
      fixationPoints: [],
      scanPatterns: [],
      reactionTimes: []
    };
  }

  async process(interactionData) {
    const { action, timestamp, position, duration } = interactionData;
    
    if (action === 'visual_fixation') {
      this.data.fixationPoints.push({
        x: position.x,
        y: position.y,
        duration,
        timestamp
      });
      
      return {
        type: 'attention',
        insight: this.analyzeAttentionPattern(),
        confidence: 0.8
      };
    }
    
    return { type: 'unknown', confidence: 0 };
  }

  analyzeAttentionPattern() {
    // Análise específica do padrão de atenção
    const avgDuration = this.data.fixationPoints.reduce((sum, point) => 
      sum + point.duration, 0) / this.data.fixationPoints.length;
    
    return {
      averageFixationDuration: avgDuration,
      totalFixations: this.data.fixationPoints.length,
      attentionStability: avgDuration > 1000 ? 'stable' : 'scattered'
    };
  }
}
```

---

## ✅ Validação e Testes

### Checklist de Validação

#### 🎮 Funcionalidade
- [ ] Jogo inicia corretamente
- [ ] Todas as dificuldades funcionam
- [ ] Pontuação é calculada corretamente
- [ ] Jogo termina adequadamente
- [ ] Estados são gerenciados corretamente

#### 🔄 Integração
- [ ] Multisensory integration ativo
- [ ] Métricas sendo coletadas
- [ ] Backend recebendo dados
- [ ] Coletores funcionando
- [ ] Relatórios sendo gerados

#### ♿ Acessibilidade
- [ ] Navegação por teclado
- [ ] ARIA labels apropriados
- [ ] TTS funcionando
- [ ] Contraste adequado
- [ ] Suporte a diferentes tamanhos

#### ⚡ Performance
- [ ] Carregamento rápido (< 3s)
- [ ] Animações suaves (60fps)
- [ ] Sem vazamentos de memória
- [ ] Bundle otimizado
- [ ] Recursos carregados sob demanda

### Testes Automatizados

```javascript
// Exemplo de teste básico
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import MeuJogoGame from './MeuJogoGame';

describe('MeuJogoGame', () => {
  test('deve renderizar tela inicial', () => {
    render(<MeuJogoGame onBack={() => {}} />);
    expect(screen.getByText('Meu Jogo')).toBeInTheDocument();
  });

  test('deve iniciar jogo ao selecionar dificuldade', async () => {
    render(<MeuJogoGame onBack={() => {}} />);
    
    fireEvent.click(screen.getByText('Fácil'));
    fireEvent.click(screen.getByText('Começar'));
    
    await waitFor(() => {
      expect(screen.queryByText('Escolha a dificuldade')).not.toBeInTheDocument();
    });
  });

  test('deve registrar interações', async () => {
    const mockRecordInteraction = jest.fn();
    // Mock do hook useUnifiedGameLogic
    
    render(<MeuJogoGame onBack={() => {}} />);
    // Simular interação
    
    expect(mockRecordInteraction).toHaveBeenCalled();
  });
});
```

---

## 🚀 Deploy e Integração

### 1️⃣ Adicionar ao Sistema

```javascript
// src/games/index.js - Adicionar exportação
export { default as MeuJogoGame } from './MeuJogo/MeuJogoGame.jsx';

// src/config/games.js - Adicionar configuração
export const GAMES_CONFIG = {
  // ... outros jogos
  'meu-jogo': {
    id: 'meu-jogo',
    name: 'Meu Jogo',
    description: 'Descrição do jogo',
    category: 'cognitive',
    targetAge: [6, 12],
    difficulty: ['easy', 'medium', 'hard'],
    duration: 10, // minutos
    skills: ['attention', 'memory'],
    component: 'MeuJogoGame',
    icon: '🎮',
    color: '#4CAF50'
  }
};
```

### 2️⃣ Registrar Rotas

```javascript
// src/components/pages/GamePage.jsx
const gameComponents = {
  // ... outros jogos
  'meu-jogo': React.lazy(() => import('../../games/MeuJogo/MeuJogoGame.jsx'))
};
```

### 3️⃣ Adicionar ao Menu

```javascript
// src/components/common/GameSelector/GameSelector.jsx
// O jogo será automaticamente incluído se estiver em GAMES_CONFIG
```

### 4️⃣ Testes de Integração

```bash
# Executar testes
npm test

# Build de produção
npm run build

# Verificar bundle size
npm run analyze
```

---

## 📚 Recursos Adicionais

### 🔗 Links Úteis

- [React Hooks Guide](https://reactjs.org/docs/hooks-intro.html)
- [Framer Motion Docs](https://www.framer.com/motion/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Web Audio API](https://developer.mozilla.org/en-US/docs/Web/API/Web_Audio_API)

### 📖 Documentação Interna

- `src/hooks/README.md` - Documentação dos hooks
- `src/components/README.md` - Componentes disponíveis
- `src/api/README.md` - APIs e serviços
- `ARCHITECTURE.md` - Arquitetura do sistema

### 🎯 Exemplos de Referência

- `src/games/ColorMatch/` - Jogo visual simples
- `src/games/MemoryGame/` - Jogo de memória completo
- `src/games/MusicalSequence/` - Jogo com áudio complexo
- `src/games/ImageAssociation/` - Jogo educativo

---

## 🤝 Suporte

### 📞 Contatos

- **Arquitetura**: <EMAIL>
- **UX/UI**: <EMAIL>
- **Acessibilidade**: <EMAIL>
- **QA**: <EMAIL>

### 🐛 Reportar Problemas

1. Criar issue no repositório
2. Incluir logs de erro
3. Descrever passos para reproduzir
4. Anexar screenshots se relevante

### 💡 Sugestões

1. Discussões no GitHub
2. Proposals via RFC
3. Feedback via formulário interno

---

**✨ Lembre-se: Cada jogo é uma oportunidade de transformar a vida de uma criança através da tecnologia. Desenvolva com carinho e propósito! 💖**
