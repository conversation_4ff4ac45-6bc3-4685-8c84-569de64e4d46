{"version": "3.1.0", "exportDate": "2025-07-16T18:42:24.698Z", "userId": "user_demo", "userDetails": {"birthDate": "2022-07-16", "guardian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "therapeuticGoals": ["attention", "memory", "coordination", "social_skills"], "specialNeeds": [], "language": "pt-BR", "registrationDate": "2025-06-21T22:31:13.090Z"}, "data": {"userProfiles": [{"id": "1750545073090", "name": "<PERSON><PERSON>", "age": "3", "avatar": "👧", "preferences": {"theme": "default", "difficulty": "easy", "soundEnabled": true, "animationsEnabled": true, "autoAdvance": false, "hapticFeedback": true}, "createdAt": "2025-06-21T22:31:13.090Z", "lastUsed": "2025-06-30T20:49:31.309Z", "gamesPlayed": 45, "totalTime": 3600000, "level": 5, "totalPoints": 1250}], "gameProgress": {"betina_number-counting_history": [{"difficulty": "easy", "correctCount": 1, "moveCount": 0, "timeSpent": 3, "accuracy": 100, "score": 21, "timestamp": "2025-06-15T15:15:02.932Z"}, {"difficulty": "easy", "correctCount": 5, "moveCount": 1, "timeSpent": 16, "accuracy": 100, "score": 34, "timestamp": "2025-06-15T15:15:15.777Z"}], "betina_visual-patterns_history": [{"userId": "session_1749950090532_mfbqjm5xh", "activityType": "visual-patterns", "difficulty": "MEDIUM", "level": 3, "score": 40, "timeSpent": 14, "errorsCount": 0, "hintsUsed": 0, "completed": true, "performance": {"accuracy": 100, "speed": "normal", "sequenceLength": 3, "shapeTypes": ["triangle", "star", "circle"]}, "timestamp": "2025-06-15T01:15:05.156Z"}], "betina_colormatch_history": [{"difficulty": "easy", "level": 3, "score": 150, "timeSpent": 45, "accuracy": 90, "completed": true, "timestamp": "2025-07-10T14:30:00.000Z"}], "betina_memorygame_history": [{"difficulty": "medium", "level": 2, "score": 120, "timeSpent": 60, "accuracy": 85, "completed": false, "timestamp": "2025-07-12T16:45:00.000Z"}], "betina_imageassociation_history": [{"difficulty": "easy", "activity": "basic_association", "score": 80, "timeSpent": 35, "accuracy": 80, "completed": true, "timestamp": "2025-07-14T10:15:00.000Z"}]}, "gameMetrics": {"totalGamesPlayed": 45, "favoriteGame": "QuebraCabeca", "averageSessionTime": 120000, "improvementTrend": "positive", "lastWeekActivity": 7, "streakDays": 5, "longestStreak": 12, "weeklyGoal": 30, "weeklyProgress": 25, "cognitiveProgress": {"attention": 75, "memory": 85, "coordination": 60, "problemSolving": 70}}, "accessibilitySettings": {"textToSpeech": true, "highContrast": false, "reducedMotion": false, "colorScheme": "default", "dyslexiaFriendly": false, "fontSize": "medium", "soundEnabled": true, "autoRead": false, "voiceSpeed": "normal", "keyboardNavigation": false, "focusIndicators": true}, "sessionData": {"currentStreak": 5, "longestStreak": 12, "weeklyGoal": 30, "weeklyProgress": 25, "lastLoginDate": "2025-07-16T18:42:24.698Z", "totalSessionsThisWeek": 7, "averageSessionQuality": "good", "preferredPlayTime": "afternoon"}, "therapeuticAnalysis": {"cognitiveAreas": {"attention": {"level": "developing", "progress": 75, "trend": "improving", "recommendations": ["Continue with focus games", "Increase session duration gradually"]}, "memory": {"level": "good", "progress": 85, "trend": "stable", "recommendations": ["Challenge with harder levels", "Introduce sequence games"]}, "coordination": {"level": "needs_work", "progress": 60, "trend": "slowly_improving", "recommendations": ["Focus on drag-and-drop games", "Practice fine motor skills"]}, "problemSolving": {"level": "developing", "progress": 70, "trend": "improving", "recommendations": ["Introduce puzzle games", "Use hint system sparingly"]}}, "overallProgress": "positive", "nextMilestones": ["Reach 80% in coordination", "Complete medium difficulty consistently"], "recommendedGames": ["QuebraCabeca", "ImageAssociation", "PadroesVisuais"]}, "serverData": {"userProfiles": {"id": "user_demo", "name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "preferences": {"notifications": true, "dataSharing": false, "parentalControls": true}, "createdAt": "2025-07-16T18:42:24.990Z"}, "gameMetrics": {"ColorMatch": {"sessions": 15, "avgScore": 85, "lastPlayed": "2025-07-16T18:42:24.990Z", "bestScore": 150, "completionRate": 90}, "MemoryGame": {"sessions": 22, "avgScore": 78, "lastPlayed": "2025-07-16T18:42:24.990Z", "bestScore": 120, "completionRate": 75}, "QuebraCabeca": {"sessions": 8, "avgScore": 92, "lastPlayed": "2025-07-16T18:42:24.990Z", "bestScore": 200, "completionRate": 95}, "ImageAssociation": {"sessions": 5, "avgScore": 80, "lastPlayed": "2025-07-14T10:15:00.000Z", "bestScore": 100, "completionRate": 80}, "MusicalSequence": {"sessions": 3, "avgScore": 70, "lastPlayed": "2025-07-13T15:30:00.000Z", "bestScore": 85, "completionRate": 65}}, "sessionData": {"totalSessions": 45, "totalPlayTime": 3600000, "averageSessionDuration": 80000, "lastSession": "2025-07-16T18:42:24.990Z", "therapeuticGoals": ["attention", "memory", "coordination"], "sessionsThisWeek": 7, "sessionsThisMonth": 25, "engagement": {"level": "high", "consistency": "good", "motivation": "positive"}}, "gameProgress": {"ColorMatch": {"level": 5, "completed": true, "achievements": ["first_win", "perfect_score", "speed_demon"], "currentDifficulty": "medium", "unlockedFeatures": ["custom_colors", "timed_mode"]}, "MemoryGame": {"level": 3, "completed": false, "achievements": ["memory_master"], "currentDifficulty": "easy", "unlockedFeatures": ["4x4_grid"]}, "QuebraCabeca": {"level": 7, "completed": true, "achievements": ["puzzle_solver", "speed_demon", "perfectionist"], "currentDifficulty": "hard", "unlockedFeatures": ["custom_pieces", "rotation_mode", "collaborative_mode"]}, "ImageAssociation": {"level": 2, "completed": false, "achievements": ["first_association"], "currentDifficulty": "easy", "unlockedFeatures": ["basic_association", "category_matching"]}, "MusicalSequence": {"level": 1, "completed": false, "achievements": [], "currentDifficulty": "easy", "unlockedFeatures": ["simple_melody"]}}, "achievements": {"global": ["early_adopter", "consistent_player", "improver"], "weekly": ["daily_player", "goal_achiever"], "special": ["birthday_player", "holiday_spirit"]}}}, "metadata": {"source": "premium_dashboard", "totalItems": 18, "categories": ["userProfiles", "gameProgress", "gameMetrics", "accessibilitySettings", "sessionData", "therapeuticAnalysis", "serverData", "achievements"], "backupQuality": "complete", "dataIntegrity": "verified", "compressionRatio": 0.75, "encryptionLevel": "standard"}}