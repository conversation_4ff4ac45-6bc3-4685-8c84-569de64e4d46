<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔤 Letter Recognition V3 - Preview Interativo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            color: white;
        }
        
        .game-container {
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        
        .header {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem 3rem 1rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            min-height: 60px;
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
            color: white;
            text-align: center;
            flex: 1;
        }
        
        .header-tts-button {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            color: white;
            z-index: 10;
        }
        
        .header-tts-button:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            min-width: 80px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.25rem;
        }
        
        
        .activity-selector {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .activity-selector h3 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .activity-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .activity-btn {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .activity-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-1px);
        }
        
        .activity-btn.active {
            background: rgba(76, 175, 80, 0.4);
            border-color: rgba(76, 175, 80, 0.6);
        }
        
        .game-area {
            padding: 0;
        }
        
        .activity {
            display: none;
        }
        
        .activity.active {
            display: block;
        }
        
        .instruction {
            text-align: center;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .instruction h3 {
            color: white;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }
        
        .instruction p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
        }
        
        .options-grid {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .letter-option {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
            text-align: center;
        }
        
        .letter-option:hover {
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .letter-option .letter {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            display: block;
        }
        
        .letter-option .example {
            font-size: 1.5rem;
            margin-top: 0.5rem;
        }
        
        .sound-text {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.5rem;
        }
        
        .word-builder {
            text-align: center;
            margin: 2rem 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .word-slots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .letter-slot {
            width: 60px;
            height: 60px;
            border: 3px dashed rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            font-weight: bold;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .letter-slot.filled {
            border: 3px solid #4CAF50;
            background: rgba(76, 175, 80, 0.3);
            color: white;
        }
        
        .available-letters {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .letter-tile {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.4);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .letter-tile:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }
        
        .letter-tile:disabled {
            background: rgba(255, 255, 255, 0.1);
            cursor: not-allowed;
            transform: none;
            opacity: 0.5;
        }
        
        
        .sound-wave {
            font-size: 2rem;
            animation: wave 1s ease-in-out infinite;
            color: white;
        }
        
        @keyframes wave {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        .sequence-display {
            text-align: center;
            margin: 2rem 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .sequence-letters {
            font-size: 3rem;
            font-weight: bold;
            color: white;
            letter-spacing: 1rem;
        }
        
        .missing-letter {
            color: #ff6b6b;
        }
        
        .letter-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            max-width: 300px;
            margin: 2rem auto;
        }
        
        .letter-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }
        
        .letter-item.target {
            background: rgba(33, 150, 243, 0.2);
            border-color: rgba(33, 150, 243, 0.5);
        }
        
        .letter-item.selected {
            background: rgba(76, 175, 80, 0.4);
            color: white;
            border-color: rgba(76, 175, 80, 0.7);
        }
        
        .progress {
            text-align: center;
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 1rem;
        }
        
        .play-sound-btn {
            background: rgba(255, 107, 107, 0.9);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 1rem 2rem;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.2rem;
            margin: 1rem;
            transition: all 0.3s ease;
        }
        
        .play-sound-btn:hover {
            background: rgba(255, 82, 82, 1);
            transform: scale(1.05);
        }
        
        .repeat-btn {
            background: rgba(255, 193, 7, 0.9);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .repeat-btn:hover {
            background: rgba(255, 193, 7, 1);
        }
        
        @media (max-width: 600px) {
            .stats {
                gap: 0.5rem;
            }
            
            .stat-item {
                min-width: 60px;
                padding: 0.75rem;
            }
            
            .options-grid {
                flex-direction: column;
                align-items: center;
            }
            
            .letter-option {
                min-width: 120px;
            }
            
            .sequence-letters {
                font-size: 2rem;
                letter-spacing: 0.5rem;
            }
            
            .activity-buttons {
                gap: 0.25rem;
            }
            
            .activity-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <div class="header">
            <h1>📚 Reconhecimento de Letras V3</h1>
            <button class="header-tts-button" onclick="toggleTTS()" title="Ativar/Desativar TTS">
                🔊
            </button>
        </div>
        
        <!-- Stats -->
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">5</div>
                <div class="stat-label">Rodada</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">40</div>
                <div class="stat-label">Pontos</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">85%</div>
                <div class="stat-label">Precisão</div>
            </div>
        </div>
        
        <!-- Activity Selector (for demo) -->
        <div class="activity-selector">
            <h3>🎮 Clique para testar cada atividade:</h3>
            <div class="activity-buttons">
                <button class="activity-btn active" onclick="showActivity('letter-selection')">🔤 Seleção</button>
                <button class="activity-btn" onclick="showActivity('sound-matching')">🎵 Sons</button>
                <button class="activity-btn" onclick="showActivity('word-formation')">🔗 Palavras</button>
                <button class="activity-btn" onclick="showActivity('sequence-recognition')">📝 Sequência</button>
                <button class="activity-btn" onclick="showActivity('phonetic-discrimination')">🎧 Fonética</button>
                <button class="activity-btn" onclick="showActivity('visual-discrimination')">👁️ Visual</button>
            </div>
        </div>
        
        <!-- Game Area -->
        <div class="game-area">
            
            <!-- 1. Letter Selection (Current) -->
            <div id="letter-selection" class="activity active">
                <div class="instruction">
                    <h3>Encontre a letra: <span style="color: #ff6b6b;">A</span></h3>
                    <p>🐝 Abelha</p>
                    <button class="repeat-btn" onclick="playSound('A')">🔊 Repetir</button>
                </div>
                <div class="options-grid">
                    <div class="letter-option" onclick="selectLetter('A')">
                        <span class="letter">A</span>
                    </div>
                    <div class="letter-option" onclick="selectLetter('B')">
                        <span class="letter">B</span>
                    </div>
                    <div class="letter-option" onclick="selectLetter('C')">
                        <span class="letter">C</span>
                    </div>
                </div>
            </div>
            
            <!-- 2. Sound Matching -->
            <div id="sound-matching" class="activity">
                <div class="instruction">
                    <h3>Que letra faz este som?</h3>
                    <button class="play-sound-btn" onclick="playTargetSound()">
                        ▶️ Tocar Som
                    </button>
                    <div class="sound-wave" id="sound-wave" style="display: none;">🎵 ~~~</div>
                </div>
                <div class="options-grid">
                    <div class="letter-option" onclick="selectSound('A')">
                        <span class="letter">A</span>
                        <span class="example">🐝</span>
                        <div class="sound-text">/a/</div>
                    </div>
                    <div class="letter-option" onclick="selectSound('E')">
                        <span class="letter">E</span>
                        <span class="example">⭐</span>
                        <div class="sound-text">/e/</div>
                    </div>
                    <div class="letter-option" onclick="selectSound('I')">
                        <span class="letter">I</span>
                        <span class="example">⛪</span>
                        <div class="sound-text">/i/</div>
                    </div>
                </div>
            </div>
            
            <!-- 3. Word Formation -->
            <div id="word-formation" class="activity">
                <div class="instruction">
                    <h3>Monte a palavra: <span style="color: #28a745;">MAMA</span></h3>
                    <p>👩 Mamãe</p>
                </div>
                <div class="word-builder">
                    <div class="word-slots">
                        <div class="letter-slot" id="slot-0"></div>
                        <div class="letter-slot" id="slot-1"></div>
                        <div class="letter-slot" id="slot-2"></div>
                        <div class="letter-slot" id="slot-3"></div>
                    </div>
                    <div class="available-letters">
                        <button class="letter-tile" onclick="addLetterToWord('M', 0)">M</button>
                        <button class="letter-tile" onclick="addLetterToWord('A', 1)">A</button>
                        <button class="letter-tile" onclick="addLetterToWord('T', 2)">T</button>
                        <button class="letter-tile" onclick="addLetterToWord('A', 3)">A</button>
                        <button class="letter-tile" onclick="addLetterToWord('M', 4)">M</button>
                    </div>
                </div>
            </div>
            
            <!-- 4. Sequence Recognition -->
            <div id="sequence-recognition" class="activity">
                <div class="instruction">
                    <h3>Complete a sequência:</h3>
                    <p>Que letra vem depois?</p>
                </div>
                <div class="sequence-display">
                    <div class="sequence-letters">
                        A → B → C → <span class="missing-letter">?</span>
                    </div>
                </div>
                <div class="options-grid">
                    <div class="letter-option" onclick="selectSequence('D')">
                        <span class="letter">D</span>
                    </div>
                    <div class="letter-option" onclick="selectSequence('E')">
                        <span class="letter">E</span>
                    </div>
                    <div class="letter-option" onclick="selectSequence('F')">
                        <span class="letter">F</span>
                    </div>
                </div>
            </div>
            
            <!-- 5. Phonetic Discrimination -->
            <div id="phonetic-discrimination" class="activity">
                <div class="instruction">
                    <h3>Qual letra tem som diferente?</h3>
                    <p>Ouça com atenção!</p>
                </div>
                <div class="options-grid">
                    <div class="letter-option" onclick="selectPhonetic('B1')">
                        <span class="letter">B</span>
                        <div style="font-size: 1rem; margin-top: 0.5rem;">🔊</div>
                        <div class="sound-text">/ba/</div>
                    </div>
                    <div class="letter-option" onclick="selectPhonetic('D')">
                        <span class="letter">D</span>
                        <div style="font-size: 1rem; margin-top: 0.5rem;">🔊</div>
                        <div class="sound-text">/da/</div>
                    </div>
                    <div class="letter-option" onclick="selectPhonetic('B2')">
                        <span class="letter">B</span>
                        <div style="font-size: 1rem; margin-top: 0.5rem;">🔊</div>
                        <div class="sound-text">/ba/</div>
                    </div>
                </div>
            </div>
            
            <!-- 6. Visual Discrimination -->
            <div id="visual-discrimination" class="activity">
                <div class="instruction">
                    <h3>Encontre todas as letras: <span style="color: #007bff;">b</span></h3>
                    <p>Cuidado com as parecidas!</p>
                </div>
                <div class="letter-grid">
                    <div class="letter-item target" onclick="toggleDiscrimination(this, 'b')">b</div>
                    <div class="letter-item" onclick="toggleDiscrimination(this, 'd')">d</div>
                    <div class="letter-item target" onclick="toggleDiscrimination(this, 'b')">b</div>
                    <div class="letter-item" onclick="toggleDiscrimination(this, 'p')">p</div>
                    <div class="letter-item" onclick="toggleDiscrimination(this, 'q')">q</div>
                    <div class="letter-item target" onclick="toggleDiscrimination(this, 'b')">b</div>
                </div>
                <div class="progress">
                    Encontradas: <span id="found-count">0</span>/3
                </div>
            </div>
            
        </div>
    </div>

    <script>
        // Activity Management
        const activities = {
            'letter-selection': 'Seleção de Letras',
            'sound-matching': 'Combinação de Sons',
            'word-formation': 'Formação de Palavras',
            'sequence-recognition': 'Reconhecimento de Sequência',
            'phonetic-discrimination': 'Discriminação Fonética',
            'visual-discrimination': 'Discriminação Visual'
        };
        
        let currentActivity = 'letter-selection';
        let wordSlots = ['', '', '', ''];
        let foundCount = 0;
        let ttsActive = true;
        
        function toggleTTS() {
            ttsActive = !ttsActive;
            const btn = document.querySelector('.header-tts-button');
            btn.textContent = ttsActive ? '🔊' : '🔇';
            btn.style.background = ttsActive ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)';
            btn.style.borderColor = ttsActive ? 'rgba(76, 175, 80, 0.5)' : 'rgba(244, 67, 54, 0.5)';
        }
        
        function showActivity(activityId) {
            // Hide all activities
            document.querySelectorAll('.activity').forEach(el => {
                el.classList.remove('active');
            });
            
            // Show selected activity
            document.getElementById(activityId).classList.add('active');
            
            // Update button states
            document.querySelectorAll('.activity-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update activity indicator
            const activities = {
                'letter-selection': 'Seleção de Letras',
                'sound-matching': 'Combinação de Sons',
                'word-formation': 'Formação de Palavras',
                'sequence-recognition': 'Reconhecimento de Sequência',
                'phonetic-discrimination': 'Discriminação Fonética',
                'visual-discrimination': 'Discriminação Visual'
            };
            
            if (ttsActive) {
                const utterance = new SpeechSynthesisUtterance(`Atividade alterada para: ${activities[activityId]}`);
                utterance.lang = 'pt-BR';
                speechSynthesis.speak(utterance);
            }
            
            // Reset activity states
            if (activityId === 'word-formation') {
                resetWordBuilder();
            } else if (activityId === 'visual-discrimination') {
                resetDiscrimination();
            }
            
            currentActivity = activityId;
        }
        
        // Letter Selection
        function selectLetter(letter) {
            const options = document.querySelectorAll('#letter-selection .letter-option');
            options.forEach(opt => {
                opt.style.background = letter === 'A' ? '#28a745' : '#dc3545';
                opt.style.color = 'white';
                opt.style.pointerEvents = 'none';
            });
            
            setTimeout(() => {
                options.forEach(opt => {
                    opt.style.background = '#f8f9fa';
                    opt.style.color = '#333';
                    opt.style.pointerEvents = 'auto';
                });
            }, 2000);
        }
        
        // Sound Matching
        function playTargetSound() {
            document.getElementById('sound-wave').style.display = 'block';
            setTimeout(() => {
                document.getElementById('sound-wave').style.display = 'none';
            }, 2000);
        }
        
        function selectSound(letter) {
            alert(`Som selecionado: ${letter}`);
        }
        
        // Word Formation
        function addLetterToWord(letter, tileIndex) {
            const targetWord = 'MAMA';
            let placed = false;
            
            for (let i = 0; i < 4; i++) {
                if (wordSlots[i] === '' && targetWord[i] === letter) {
                    wordSlots[i] = letter;
                    document.getElementById(`slot-${i}`).textContent = letter;
                    document.getElementById(`slot-${i}`).classList.add('filled');
                    
                    // Disable the tile
                    const tiles = document.querySelectorAll('#word-formation .letter-tile');
                    tiles[tileIndex].disabled = true;
                    
                    placed = true;
                    break;
                }
            }
            
            if (!placed) {
                alert('Esta letra não vai nesta posição!');
            }
        }
        
        function resetWordBuilder() {
            wordSlots = ['', '', '', ''];
            for (let i = 0; i < 4; i++) {
                document.getElementById(`slot-${i}`).textContent = '';
                document.getElementById(`slot-${i}`).classList.remove('filled');
            }
            document.querySelectorAll('#word-formation .letter-tile').forEach(tile => {
                tile.disabled = false;
            });
        }
        
        // Sequence Recognition
        function selectSequence(letter) {
            const options = document.querySelectorAll('#sequence-recognition .letter-option');
            options.forEach(opt => {
                const isCorrect = opt.querySelector('.letter').textContent === 'D';
                opt.style.background = (letter === 'D' && isCorrect) ? '#28a745' : '#dc3545';
                opt.style.color = 'white';
            });
        }
        
        // Phonetic Discrimination
        function selectPhonetic(option) {
            alert(`Opção selecionada: ${option}`);
        }
        
        // Visual Discrimination
        function toggleDiscrimination(element, letter) {
            if (letter === 'b') {
                element.classList.toggle('selected');
                if (element.classList.contains('selected')) {
                    foundCount++;
                } else {
                    foundCount--;
                }
                document.getElementById('found-count').textContent = foundCount;
            } else {
                element.style.background = '#dc3545';
                element.style.color = 'white';
                setTimeout(() => {
                    element.style.background = '#f8f9fa';
                    element.style.color = '#333';
                }, 1000);
            }
        }
        
        function resetDiscrimination() {
            foundCount = 0;
            document.getElementById('found-count').textContent = '0';
            document.querySelectorAll('#visual-discrimination .letter-item').forEach(item => {
                item.classList.remove('selected');
                item.style.background = item.classList.contains('target') ? '#e3f2fd' : '#f8f9fa';
                item.style.color = '#333';
            });
        }
        
        // Sound effects with TTS check
        function playSound(letter) {
            if (ttsActive) {
                const utterance = new SpeechSynthesisUtterance(`Som da letra: ${letter}`);
                utterance.lang = 'pt-BR';
                speechSynthesis.speak(utterance);
            }
        }
        
        // Auto-advance demo (optional)
        let autoAdvanceInterval;
        function startAutoAdvance() {
            const activityKeys = Object.keys(activities);
            let currentIndex = 0;
            
            autoAdvanceInterval = setInterval(() => {
                currentIndex = (currentIndex + 1) % activityKeys.length;
                showActivity(activityKeys[currentIndex]);
            }, 5000);
        }
        
        function stopAutoAdvance() {
            clearInterval(autoAdvanceInterval);
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎮 Letter Recognition V3 Preview carregado!');
        });
    </script>
</body>
</html>
