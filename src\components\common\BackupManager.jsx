import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  Box,
  Typography,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Tooltip
} from '@material-ui/core';
import CloudDownloadIcon from '@material-ui/icons/CloudDownload';
import DeleteIcon from '@material-ui/icons/Delete';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import GetAppIcon from '@material-ui/icons/GetApp';
import BackupImportDialog from './BackupImportDialog';
import { hasImportedBackup, getImportedBackup, clearImportedBackup } from '../../utils/backupExportSupport';

/**
 * Componente de gerenciamento de backup para dashboards
 * @param {Object} props - Propriedades do componente
 * @returns {React.Component}
 */
const BackupManager = ({ onImportSuccess, onClearBackup }) => {
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [hasBackup, setHasBackup] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  
  useEffect(() => {
    // Verificar se existe backup importado
    setHasBackup(hasImportedBackup());
  }, []);
  
  const handleImportClick = () => {
    setImportDialogOpen(true);
  };
  
  const handleDialogClose = () => {
    setImportDialogOpen(false);
  };
  
  const handleImportSuccess = (result) => {
    setHasBackup(true);
    if (onImportSuccess) {
      onImportSuccess(result);
    }
  };
  
  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  
  const handleClearBackup = () => {
    const cleared = clearImportedBackup();
    if (cleared) {
      setHasBackup(false);
      if (onClearBackup) {
        onClearBackup();
      }
    }
    handleMenuClose();
  };
  
  const handleExportBackup = () => {
    const backupData = getImportedBackup();
    if (backupData) {
      const dataStr = JSON.stringify(backupData, null, 2);
      const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
      
      const exportFileDefaultName = `betina_backup_${new Date().toISOString().slice(0, 10)}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    }
    handleMenuClose();
  };
  
  return (
    <>
      <Box display="flex" alignItems="center">
        {hasBackup ? (
          <>
            <Typography variant="body2" color="textSecondary" style={{ marginRight: '8px' }}>
              Backup carregado
            </Typography>
            <Tooltip title="Opções de backup">
              <IconButton size="small" onClick={handleMenuClick}>
                <MoreVertIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={handleImportClick}>
                <ListItemIcon>
                  <CloudDownloadIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Importar outro backup" />
              </MenuItem>
              <MenuItem onClick={handleExportBackup}>
                <ListItemIcon>
                  <GetAppIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Re-exportar backup" />
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleClearBackup}>
                <ListItemIcon>
                  <DeleteIcon fontSize="small" />
                </ListItemIcon>
                <ListItemText primary="Remover backup" />
              </MenuItem>
            </Menu>
          </>
        ) : (
          <Button
            size="small"
            startIcon={<CloudDownloadIcon />}
            onClick={handleImportClick}
            variant="outlined"
            color="primary"
          >
            Importar Backup
          </Button>
        )}
      </Box>
      
      <BackupImportDialog
        open={importDialogOpen}
        onClose={handleDialogClose}
        onImportSuccess={handleImportSuccess}
      />
    </>
  );
};

BackupManager.propTypes = {
  onImportSuccess: PropTypes.func,
  onClearBackup: PropTypes.func
};

export default BackupManager;
