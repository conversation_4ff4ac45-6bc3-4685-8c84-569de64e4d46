<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview dos Jogos - Portal Betina V3</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(550px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .game-preview {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .game-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .game-description {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 20px;
            text-align: center;
        }

        .game-board {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        /* Sequence Learning Styles */
        .sequence-display {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .sequence-item {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sequence-item:hover {
            transform: scale(1.1);
        }

        .sequence-item.active {
            transform: scale(1.2);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .sequence-item.color-1 { background: #e74c3c; }
        .sequence-item.color-2 { background: #3498db; }
        .sequence-item.color-3 { background: #2ecc71; }
        .sequence-item.color-4 { background: #f39c12; }
        .sequence-item.color-5 { background: #9b59b6; }

        /* Pattern Matching Styles */
        .pattern-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            margin-bottom: 20px;
            max-width: 250px;
        }

        .pattern-cell {
            width: 50px;
            height: 50px;
            border: 2px solid #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .pattern-cell:hover {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .pattern-cell.filled {
            background: #667eea;
            color: white;
        }

        .pattern-cell.correct {
            background: #2ecc71;
            color: white;
        }

        .pattern-cell.incorrect {
            background: #e74c3c;
            color: white;
        }

        .pattern-options {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .pattern-option {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 3px;
            border: 3px solid #ddd;
            border-radius: 8px;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pattern-option:hover {
            border-color: #667eea;
            transform: scale(1.05);
        }

        .pattern-option.selected {
            border-color: #667eea;
            background: #f8f9fa;
        }

        .pattern-option-cell {
            width: 20px;
            height: 20px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        .pattern-option-cell.filled {
            background: #667eea;
        }

        .game-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .game-stats {
            display: flex;
            justify-content: space-around;
            font-size: 0.9em;
            color: #666;
        }

        .feedback {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: bold;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .feedback.show {
            opacity: 1;
            transform: translateY(0);
        }

        .feedback.success {
            background: #d4edda;
            color: #155724;
        }

        .feedback.error {
            background: #f8d7da;
            color: #721c24;
        }

        .feedback.info {
            background: #cce7ff;
            color: #004085;
        }

        @media (max-width: 768px) {
            .games-grid {
                grid-template-columns: 1fr;
            }
            
            .sequence-item {
                width: 50px;
                height: 50px;
                font-size: 1.2em;
            }
            
            .pattern-cell {
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Preview dos Jogos</h1>
            <p>Conheça os jogos SequenceLearning e PatternMatching</p>
        </div>

        <div class="games-grid">
            <!-- Sequence Learning Game -->
            <div class="game-preview">
                <h2 class="game-title">🔢 Sequence Learning</h2>
                <p class="game-description">Memorize e reproduza sequências de cores na ordem correta</p>
                
                <div class="game-board">
                    <div class="feedback" id="sequence-feedback"></div>
                    
                    <div>
                        <h3 style="margin-bottom: 10px; color: #333;">Sequência para memorizar:</h3>
                        <div class="sequence-display" id="sequence-display">
                            <!-- Sequência será gerada aqui -->
                        </div>
                    </div>
                    
                    <div>
                        <h3 style="margin-bottom: 10px; color: #333;">Sua resposta:</h3>
                        <div class="sequence-display" id="player-sequence">
                            <!-- Resposta do jogador aqui -->
                        </div>
                    </div>
                </div>
                
                <div class="game-controls">
                    <button class="btn btn-primary" id="start-sequence">Iniciar</button>
                    <button class="btn btn-secondary" id="show-sequence">Mostrar Sequência</button>
                </div>
                
                <div class="game-stats">
                    <div>Nível: <span id="sequence-level">1</span></div>
                    <div>Pontos: <span id="sequence-score">0</span></div>
                    <div>Acertos: <span id="sequence-streak">0</span></div>
                </div>
            </div>

            <!-- Pattern Matching Game -->
            <div class="game-preview">
                <h2 class="game-title">🧩 Pattern Matching</h2>
                <p class="game-description">Complete o padrão faltante analisando a sequência lógica</p>
                
                <div class="game-board">
                    <div class="feedback" id="pattern-feedback"></div>
                    
                    <div>
                        <h3 style="margin-bottom: 10px; color: #333;">Complete o padrão:</h3>
                        <div class="pattern-grid" id="pattern-grid">
                            <!-- Padrão será gerado aqui -->
                        </div>
                    </div>
                    
                    <div>
                        <h3 style="margin-bottom: 10px; color: #333;">Escolha a opção:</h3>
                        <div class="pattern-options" id="pattern-options">
                            <!-- Opções serão geradas aqui -->
                        </div>
                    </div>
                </div>
                
                <div class="game-controls">
                    <button class="btn btn-primary" id="start-pattern">Novo Padrão</button>
                    <button class="btn btn-secondary" id="check-pattern">Verificar</button>
                </div>
                
                <div class="game-stats">
                    <div>Nível: <span id="pattern-level">1</span></div>
                    <div>Pontos: <span id="pattern-score">0</span></div>
                    <div>Acertos: <span id="pattern-streak">0</span></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sequence Learning Game
        class SequenceGame {
            constructor() {
                this.level = 1;
                this.score = 0;
                this.streak = 0;
                this.currentSequence = [];
                this.playerSequence = [];
                this.isShowingSequence = false;
                this.colors = ['color-1', 'color-2', 'color-3', 'color-4', 'color-5'];
                this.init();
            }

            init() {
                document.getElementById('start-sequence').onclick = () => this.startGame();
                document.getElementById('show-sequence').onclick = () => this.showSequence();
                this.showFeedback('sequence-feedback', 'Clique em "Iniciar" para começar!', 'info');
            }

            startGame() {
                this.generateSequence();
                this.renderSequence();
                this.showSequence();
            }

            generateSequence() {
                const length = Math.min(3 + Math.floor(this.level / 2), 8);
                this.currentSequence = [];
                this.playerSequence = [];
                
                for (let i = 0; i < length; i++) {
                    this.currentSequence.push(this.colors[Math.floor(Math.random() * this.colors.length)]);
                }
            }

            renderSequence() {
                const container = document.getElementById('sequence-display');
                container.innerHTML = '';
                
                this.currentSequence.forEach((color, index) => {
                    const item = document.createElement('div');
                    item.className = `sequence-item ${color}`;
                    item.textContent = index + 1;
                    container.appendChild(item);
                });

                this.renderPlayerSequence();
            }

            renderPlayerSequence() {
                const container = document.getElementById('player-sequence');
                container.innerHTML = '';
                
                this.currentSequence.forEach((_, index) => {
                    const item = document.createElement('div');
                    item.className = 'sequence-item color-1';
                    item.textContent = '?';
                    item.style.background = '#ddd';
                    item.style.color = '#666';
                    item.onclick = () => this.selectColor(index);
                    container.appendChild(item);
                });
            }

            selectColor(index) {
                if (this.isShowingSequence) return;
                
                // Cycle through colors
                const currentColorIndex = this.playerSequence[index] ? 
                    this.colors.indexOf(this.playerSequence[index]) : -1;
                const nextColorIndex = (currentColorIndex + 1) % this.colors.length;
                
                this.playerSequence[index] = this.colors[nextColorIndex];
                this.updatePlayerDisplay();
                
                if (this.playerSequence.length === this.currentSequence.length &&
                    this.playerSequence.every(color => color !== undefined)) {
                    this.checkSequence();
                }
            }

            updatePlayerDisplay() {
                const items = document.querySelectorAll('#player-sequence .sequence-item');
                items.forEach((item, index) => {
                    if (this.playerSequence[index]) {
                        item.className = `sequence-item ${this.playerSequence[index]}`;
                        item.textContent = index + 1;
                    }
                });
            }

            showSequence() {
                this.isShowingSequence = true;
                const items = document.querySelectorAll('#sequence-display .sequence-item');
                
                let index = 0;
                const highlight = () => {
                    if (index < items.length) {
                        items[index].classList.add('active');
                        setTimeout(() => {
                            items[index].classList.remove('active');
                            index++;
                            setTimeout(highlight, 200);
                        }, 600);
                    } else {
                        this.isShowingSequence = false;
                        this.showFeedback('sequence-feedback', 'Agora reproduza a sequência clicando nas cores!', 'info');
                    }
                };
                
                setTimeout(highlight, 500);
            }

            checkSequence() {
                const correct = this.currentSequence.every((color, index) => 
                    color === this.playerSequence[index]
                );
                
                if (correct) {
                    this.score += this.level * 10;
                    this.streak++;
                    this.level++;
                    this.showFeedback('sequence-feedback', `Correto! +${this.level * 10} pontos`, 'success');
                    setTimeout(() => this.startGame(), 1500);
                } else {
                    this.streak = 0;
                    this.showFeedback('sequence-feedback', 'Incorreto! Tente novamente.', 'error');
                }
                
                this.updateStats();
            }

            updateStats() {
                document.getElementById('sequence-level').textContent = this.level;
                document.getElementById('sequence-score').textContent = this.score;
                document.getElementById('sequence-streak').textContent = this.streak;
            }

            showFeedback(id, message, type) {
                const feedback = document.getElementById(id);
                feedback.textContent = message;
                feedback.className = `feedback ${type} show`;
                setTimeout(() => feedback.classList.remove('show'), 3000);
            }
        }

        // Pattern Matching Game
        class PatternGame {
            constructor() {
                this.level = 1;
                this.score = 0;
                this.streak = 0;
                this.currentPattern = [];
                this.correctAnswer = 0;
                this.selectedAnswer = null;
                this.init();
            }

            init() {
                document.getElementById('start-pattern').onclick = () => this.startGame();
                document.getElementById('check-pattern').onclick = () => this.checkAnswer();
                this.showFeedback('pattern-feedback', 'Clique em "Novo Padrão" para começar!', 'info');
            }

            startGame() {
                this.generatePattern();
                this.renderPattern();
                this.generateOptions();
                this.selectedAnswer = null;
            }

            generatePattern() {
                // Gerar um padrão simples de 4x4 com um quadrado faltando
                this.currentPattern = [];
                const patterns = [
                    [1,0,1,0,0,1,0,1,1,0,1,0,0,1,0,1], // Padrão xadrez
                    [1,1,0,0,1,1,0,0,0,0,1,1,0,0,1,1], // Padrão de blocos
                    [1,0,0,1,0,1,1,0,0,1,1,0,1,0,0,1], // Padrão diagonal
                    [1,1,1,0,1,0,0,1,1,0,0,1,0,1,1,1], // Padrão circular
                ];
                
                const selectedPattern = patterns[Math.floor(Math.random() * patterns.length)];
                this.currentPattern = [...selectedPattern];
                
                // Remover um elemento aleatório (criar o "buraco")
                this.missingIndex = Math.floor(Math.random() * 16);
                this.correctAnswer = this.currentPattern[this.missingIndex];
                this.currentPattern[this.missingIndex] = 2; // 2 = vazio
            }

            renderPattern() {
                const container = document.getElementById('pattern-grid');
                container.innerHTML = '';
                
                this.currentPattern.forEach((cell, index) => {
                    const cellElement = document.createElement('div');
                    cellElement.className = 'pattern-cell';
                    
                    if (cell === 1) {
                        cellElement.classList.add('filled');
                        cellElement.textContent = '●';
                    } else if (cell === 0) {
                        cellElement.textContent = '○';
                    } else {
                        cellElement.textContent = '?';
                        cellElement.style.background = '#ffeb3b';
                        cellElement.style.color = '#333';
                    }
                    
                    container.appendChild(cellElement);
                });
            }

            generateOptions() {
                const container = document.getElementById('pattern-options');
                container.innerHTML = '';
                
                const options = [0, 1]; // Vazio ou preenchido
                
                options.forEach((option, index) => {
                    const optionElement = document.createElement('div');
                    optionElement.className = 'pattern-option';
                    optionElement.onclick = () => this.selectOption(index, option);
                    
                    // Criar mini padrão 2x2 para mostrar a opção
                    for (let i = 0; i < 4; i++) {
                        const cell = document.createElement('div');
                        cell.className = 'pattern-option-cell';
                        if (i === 1) { // Mostrar a opção no centro
                            if (option === 1) {
                                cell.classList.add('filled');
                            }
                        }
                        optionElement.appendChild(cell);
                    }
                    
                    container.appendChild(optionElement);
                });
            }

            selectOption(index, option) {
                // Remover seleção anterior
                document.querySelectorAll('.pattern-option').forEach(el => 
                    el.classList.remove('selected')
                );
                
                // Adicionar seleção atual
                document.querySelectorAll('.pattern-option')[index].classList.add('selected');
                this.selectedAnswer = option;
            }

            checkAnswer() {
                if (this.selectedAnswer === null) {
                    this.showFeedback('pattern-feedback', 'Selecione uma opção primeiro!', 'error');
                    return;
                }
                
                if (this.selectedAnswer === this.correctAnswer) {
                    this.score += this.level * 15;
                    this.streak++;
                    this.level++;
                    this.showFeedback('pattern-feedback', `Correto! +${this.level * 15} pontos`, 'success');
                    
                    // Mostrar resposta correta
                    const cells = document.querySelectorAll('.pattern-cell');
                    cells[this.missingIndex].classList.add('correct');
                    cells[this.missingIndex].textContent = this.correctAnswer === 1 ? '●' : '○';
                    
                    setTimeout(() => this.startGame(), 2000);
                } else {
                    this.streak = 0;
                    this.showFeedback('pattern-feedback', 'Incorreto! Tente novamente.', 'error');
                    
                    // Mostrar resposta incorreta
                    const cells = document.querySelectorAll('.pattern-cell');
                    cells[this.missingIndex].classList.add('incorrect');
                    cells[this.missingIndex].textContent = this.selectedAnswer === 1 ? '●' : '○';
                    
                    setTimeout(() => {
                        cells[this.missingIndex].classList.remove('incorrect');
                        cells[this.missingIndex].textContent = '?';
                        cells[this.missingIndex].style.background = '#ffeb3b';
                    }, 1500);
                }
                
                this.updateStats();
            }

            updateStats() {
                document.getElementById('pattern-level').textContent = this.level;
                document.getElementById('pattern-score').textContent = this.score;
                document.getElementById('pattern-streak').textContent = this.streak;
            }

            showFeedback(id, message, type) {
                const feedback = document.getElementById(id);
                feedback.textContent = message;
                feedback.className = `feedback ${type} show`;
                setTimeout(() => feedback.classList.remove('show'), 3000);
            }
        }

        // Inicializar os jogos
        window.addEventListener('load', () => {
            new SequenceGame();
            new PatternGame();
        });
    </script>
</body>
</html>
