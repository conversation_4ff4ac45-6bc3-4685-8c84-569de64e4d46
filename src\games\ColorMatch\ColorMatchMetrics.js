/**
 * @file ColorMatchMetrics.js
 * @description Sistema de métricas e analytics para o Jogo de Combinação de Cores V3
 * @version 3.1.0
 */

/**
 * Classe base para métricas de jogos
 */
export class GameMetrics {
  constructor(backendConnector = null) {
    this.backendConnector = backendConnector;
    this.sessionData = this.createDefaultSessionData();
  }

  createDefaultSessionData() {
    return {
      startTime: Date.now(),
      totalQuestions: 0,
      correctAnswers: 0,
      wrongAnswers: 0,
      currentStreak: 0,
      maxStreak: 0,
      averageResponseTime: 0,
      responseTimes: [],
      questionsHistory: [],
      categoriesUsed: new Set(),
      difficultiesPlayed: new Set(),
      activityMetrics: {},
      behavioralAnalysis: {
        activityPreferences: {},
        learningProgression: [],
        difficultyAdaptation: [],
        errorPatterns: {},
        recoveryRate: 1.0,
        engagementLevel: 1.0,
        cognitiveLoad: 0.5,
        attentionSpan: 0,
        frustrationLevel: 0.0,
        motivationLevel: 1.0
      },
      temporalAnalysis: {
        peakPerformanceTimes: [],
        performanceFluctuations: [],
        sessionDurations: [],
        breakPatterns: [],
        fatigueIndicators: []
      }
    };
  }

  connectToBackend(backendConnector) {
    this.backendConnector = backendConnector;
  }

  async syncWithBackend(data) {
    if (this.backendConnector?.updateMetrics) {
      try {
        await this.backendConnector.updateMetrics(data);
      } catch (error) {
        console.warn('Erro ao sincronizar métricas com backend:', error);
      }
    }
  }

  recordAnswer(isCorrect, responseTime, questionData) {
    this.ensureValidSessionData();
    this.sessionData.totalQuestions++;
    this.sessionData.responseTimes.push(responseTime);

    if (isCorrect) {
      this.sessionData.correctAnswers++;
      this.sessionData.currentStreak++;
      this.sessionData.maxStreak = Math.max(this.sessionData.maxStreak, this.sessionData.currentStreak);
    } else {
      this.sessionData.wrongAnswers++;
      this.sessionData.currentStreak = 0;
    }

    this.sessionData.averageResponseTime =
      this.sessionData.responseTimes.reduce((a, b) => a + b, 0) / this.sessionData.responseTimes.length;

    if (questionData.category) this.sessionData.categoriesUsed.add(questionData.category);
    if (questionData.difficulty) this.sessionData.difficultiesPlayed.add(questionData.difficulty);
  }

  getAccuracy() {
    if (this.sessionData.totalQuestions === 0) return 100;
    return Math.round((this.sessionData.correctAnswers / this.sessionData.totalQuestions) * 100);
  }

  getSessionStats() {
    return {
      ...this.sessionData,
      accuracy: this.getAccuracy(),
      sessionDuration: Date.now() - this.sessionData.startTime,
      categoriesUsed: Array.from(this.sessionData.categoriesUsed),
      difficultiesPlayed: Array.from(this.sessionData.difficultiesPlayed)
    };
  }

  getLearningInsights() {
    const stats = this.getSessionStats();
    const insights = [];

    if (stats.accuracy >= 90) {
      insights.push({ type: 'success', category: 'overall', message: 'Excelente precisão geral!' });
    } else if (stats.accuracy >= 70) {
      insights.push({ type: 'good', category: 'overall', message: 'Boa precisão geral! Continue praticando!' });
    } else {
      insights.push({ type: 'improvement', category: 'overall', message: 'Pratique mais para melhorar suas habilidades!' });
    }

    if (stats.averageResponseTime < 3000) {
      insights.push({ type: 'success', category: 'speed', message: 'Você responde muito rapidamente!' });
    } else if (stats.averageResponseTime > 8000) {
      insights.push({ type: 'tip', category: 'speed', message: 'Não tenha pressa, pense com calma antes de responder!' });
    }

    if (stats.maxStreak >= 5) {
      insights.push({ type: 'achievement', category: 'consistency', message: `Sequência incrível de ${stats.maxStreak} acertos consecutivos!` });
    }

    return insights;
  }

  ensureValidSessionData() {
    if (!this.sessionData) {
      console.warn('sessionData corrompido, recriando...');
      this.sessionData = this.createDefaultSessionData();
    }
  }

  resetSession() {
    this.sessionData = this.createDefaultSessionData();
  }

  startSession(sessionId, userId, difficulty = 'medium') {
    this.sessionData.sessionId = sessionId;
    this.sessionData.userId = userId;
    this.sessionData.difficulty = difficulty;
    this.sessionData.startTime = Date.now();
    console.log(`📊 Sessão de métricas iniciada: ${sessionId}`);
  }

  getSessionSummary() {
    return {
      sessionId: this.sessionData.sessionId,
      userId: this.sessionData.userId,
      gameType: 'GenericGame',
      ...this.getSessionStats(),
      insights: this.getLearningInsights(),
      completed: true
    };
  }
}

export class ColorMatchMetrics extends GameMetrics {
  constructor(backendConnector = null) {
    super(backendConnector);
    this.sessionData.gameVersion = '3.1.0';
    this.sessionData.errorTypes = { colorConfusion: 0, shapeMismatch: 0, patternError: 0 };
    this.sessionData.repetitions = 0;
    this.initializeActivityMetrics();
  }

  initializeActivityMetrics() {
    const activities = [
      'find_the_color',
      'name_the_color',
      'match_the_name',
      'memory_match',
      'sequence_repeat',
      'color_mixing'
    ];

    activities.forEach(activity => {
      this.sessionData.activityMetrics[activity] = {
        attempts: 0,
        correct: 0,
        wrong: 0,
        averageTime: 0,
        responseTimes: [],
        accuracy: 100,
        difficulty: null,
        bestStreak: 0,
        currentStreak: 0
      };

      switch (activity) {
        case 'name_the_color':
          this.sessionData.activityMetrics[activity].namingErrors = [];
          break;
        case 'memory_match':
          this.sessionData.activityMetrics[activity].pairMatches = 0;
          break;
        case 'sequence_repeat':
          this.sessionData.activityMetrics[activity].sequenceTypes = [];
          break;
        case 'color_mixing':
          this.sessionData.activityMetrics[activity].mixingAccuracy = 100;
          break;
      }
    });
  }

  recordAnswer(isCorrect, responseTime, questionData) {
    super.recordAnswer(isCorrect, responseTime, questionData);

    const activity = questionData.activity || 'find_the_color';
    
    // 🔥 Verificação de segurança - inicializar atividade se não existir
    if (!this.sessionData.activityMetrics[activity]) {
      this.sessionData.activityMetrics[activity] = {
        attempts: 0,
        correct: 0,
        responseTimes: [],
        difficulty: questionData.difficulty || 'medium',
        currentStreak: 0,
        bestStreak: 0,
        averageResponseTime: 0
      };
    }
    
    const activityMetric = this.sessionData.activityMetrics[activity];

    activityMetric.attempts++;
    activityMetric.responseTimes.push(responseTime);
    activityMetric.difficulty = questionData.difficulty;

    if (isCorrect) {
      activityMetric.correct++;
      activityMetric.currentStreak++;
      activityMetric.bestStreak = Math.max(activityMetric.bestStreak, activityMetric.currentStreak);
    } else {
      activityMetric.wrong++;
      activityMetric.currentStreak = 0;
    }

    activityMetric.accuracy = activityMetric.attempts > 0 ?
      Math.round((activityMetric.correct / activityMetric.attempts) * 100) : 100;
    activityMetric.averageTime = activityMetric.responseTimes.reduce((a, b) => a + b, 0) / activityMetric.responseTimes.length;

    this.recordActivitySpecificData(activity, questionData, isCorrect, responseTime);
    this.sessionData.questionsHistory.push({
      timestamp: Date.now(),
      activity,
      correct: isCorrect,
      responseTime,
      ...questionData
    });

    this.updateBehavioralAnalysis(activity, isCorrect, responseTime, questionData);
  }

  recordActivitySpecificData(activity, questionData, isCorrect, responseTime) {
    const activityMetric = this.sessionData.activityMetrics[activity];

    switch (activity) {
      case 'name_the_color':
        if (!isCorrect && questionData.userInput) {
          activityMetric.namingErrors.push({
            expected: questionData.correctAnswer,
            input: questionData.userInput
          });
        }
        break;
      case 'memory_match':
        if (isCorrect) {
          activityMetric.pairMatches++;
        }
        break;
      case 'sequence_repeat':
        if (questionData.sequenceType) {
          activityMetric.sequenceTypes.push(questionData.sequenceType);
        }
        break;
      case 'color_mixing':
        if (questionData.mixingResult) {
          const accuracy = questionData.mixingResult.accuracy || 100;
          activityMetric.mixingAccuracy = ((activityMetric.mixingAccuracy * (activityMetric.attempts - 1)) + accuracy) / activityMetric.attempts;
        }
        break;
    }

    if (!isCorrect && questionData.element === 'color') {
      this.sessionData.errorTypes.colorConfusion++;
    } else if (!isCorrect && questionData.element === 'shape') {
      this.sessionData.errorTypes.shapeMismatch++;
    } else if (!isCorrect && questionData.element === 'pattern') {
      this.sessionData.errorTypes.patternError++;
    }
  }

  recordRepetition() {
    this.sessionData.repetitions++;
  }

  updateBehavioralAnalysis(activity, isCorrect, responseTime, questionData) {
    const behavioral = this.sessionData.behavioralAnalysis;
    if (!behavioral.activityPreferences[activity]) {
      behavioral.activityPreferences[activity] = { score: 0, attempts: 0 };
    }

    const activityPref = behavioral.activityPreferences[activity];
    activityPref.attempts++;
    const speedScore = Math.max(0, (10000 - responseTime) / 1000);
    const accuracyScore = isCorrect ? 10 : 0;
    activityPref.score = ((activityPref.score * (activityPref.attempts - 1)) + (speedScore + accuracyScore)) / activityPref.attempts;

    behavioral.learningProgression.push({
      activity,
      timestamp: Date.now(),
      correct: isCorrect,
      responseTime,
      difficulty: questionData.difficulty
    });

    const recentAttempts = behavioral.learningProgression.slice(-10);
    const consistency = recentAttempts.filter(attempt => attempt.correct).length / recentAttempts.length;
    behavioral.engagementLevel = Math.max(0.1, Math.min(1.0, consistency * 1.2));
    behavioral.cognitiveLoad = Math.min(1.0, responseTime / 8000);

    if (!isCorrect) {
      if (!behavioral.errorPatterns[activity]) {
        behavioral.errorPatterns[activity] = [];
      }
      behavioral.errorPatterns[activity].push({
        timestamp: Date.now(),
        questionData,
        responseTime
      });
    }
  }

  getActivityAccuracy(activity) {
    const activityMetric = this.sessionData.activityMetrics[activity];
    if (!activityMetric || activityMetric.attempts === 0) return 100;
    return activityMetric.accuracy;
  }

  getPreferredActivity() {
    const preferences = this.sessionData.behavioralAnalysis.activityPreferences;
    let bestActivity = null;
    let bestScore = -1;

    for (const [activity, data] of Object.entries(preferences)) {
      if (data.attempts >= 3 && data.score > bestScore) {
        bestScore = data.score;
        bestActivity = activity;
      }
    }

    return bestActivity;
  }

  getNeedsImprovementActivity() {
    const activities = this.sessionData.activityMetrics;
    let worstActivity = null;
    let worstAccuracy = 101;

    for (const [activity, data] of Object.entries(activities)) {
      if (data.attempts >= 3 && data.accuracy < worstAccuracy) {
        worstAccuracy = data.accuracy;
        worstActivity = activity;
      }
    }

    return worstActivity;
  }

  getLearningInsights() {
    const insights = super.getLearningInsights();
    const activityNames = {
      'find_the_color': 'Encontrar a Cor',
      'name_the_color': 'Nomear a Cor',
      'match_the_name': 'Combinar o Nome',
      'memory_match': 'Jogo da Memória',
      'sequence_repeat': 'Repetir Sequência',
      'color_mixing': 'Mistura de Cores'
    };

    for (const [activity, data] of Object.entries(this.sessionData.activityMetrics)) {
      if (data.attempts >= 3) {
        const name = activityNames[activity] || activity;
        if (data.accuracy >= 90) {
          insights.push({
            type: 'success',
            category: 'activity',
            activity,
            message: `Excelente em ${name}! Precisão de ${data.accuracy}%`
          });
        } else if (data.accuracy < 60) {
          insights.push({
            type: 'improvement',
            category: 'activity',
            activity,
            message: `${name} precisa de mais prática. Precisão: ${data.accuracy}%`
          });
        }

        if (data.averageTime < 3000) {
          insights.push({
            type: 'success',
            category: 'speed',
            activity,
            message: `Muito rápido em ${name}! Tempo médio: ${(data.averageTime/1000).toFixed(1)}s`
          });
        }
      }
    }

    const preferred = this.getPreferredActivity();
    if (preferred) {
      insights.push({
        type: 'info',
        category: 'preference',
        message: `Você parece gostar mais de ${activityNames[preferred] || preferred}!`
      });
    }

    const needsWork = this.getNeedsImprovementActivity();
    if (needsWork && needsWork !== preferred) {
      insights.push({
        type: 'suggestion',
        category: 'improvement',
        message: `Tente praticar mais ${activityNames[needsWork] || needsWork} para equilibrar suas habilidades!`
      });
    }

    return insights;
  }

  getColorConfusionMatrix() {
    const confusions = {};
    this.sessionData.questionsHistory
      .filter(q => !q.correct && q.activity === 'find_the_color')
      .forEach(q => {
        const key = `${q.correctAnswer}->${q.answer}`;
        confusions[key] = (confusions[key] || 0) + 1;
      });
    return confusions;
  }

  checkColorBlindnessPatterns() {
    const incorrectColors = this.sessionData.questionsHistory
      .filter(q => !q.correct && q.activity === 'find_the_color')
      .map(q => ({ correct: q.correctAnswer, selected: q.answer }));
    return {
      protanopia: incorrectColors.some(c => c.correct === 'Verde' && c.selected === 'Vermelho') ||
                   incorrectColors.some(c => c.correct === 'Vermelho' && c.selected === 'Verde'),
      deuteranopia: incorrectColors.some(c => c.correct === 'Verde' && c.selected === 'Vermelho') ||
                    incorrectColors.some(c => c.correct === 'Vermelho' && c.selected === 'Verde'),
      tritanopia: incorrectColors.some(c => c.correct === 'Azul' && c.selected === 'Amarelo') ||
                  incorrectColors.some(c => c.correct === 'Amarelo' && c.selected === 'Azul')
    };
  }

  exportData() {
    return {
      gameType: 'ColorMatch',
      version: '3.1.0',
      sessionData: this.getSessionStats(),
      exportTime: Date.now()
    };
  }

  getSessionSummary() {
    const summary = super.getSessionSummary();
    summary.gameType = 'ColorMatch';
    summary.colorConfusionMatrix = this.getColorConfusionMatrix();
    summary.colorBlindnessPatterns = this.checkColorBlindnessPatterns();
    return summary;
  }
}