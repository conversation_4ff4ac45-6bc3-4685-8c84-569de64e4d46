/**
 * 🧠 COLOR COGNITION COLLECTOR
 * Coletor especializado em análise cognitiva relacionada à cor para ColorMatch
 * Portal Betina V3
 */

export class ColorCognitionCollector {
  constructor() {
    this.cognitiveDomains = {
      categorization: 'categorização cromática',
      semantics: 'semântica de cores',
      memory: 'memória cromática',
      processing: 'processamento cognitivo',
      naming: 'nomeação de cores',
      association: 'associação cromática'
    };
    
    this.cognitiveComplexity = {
      basic: { level: 1, description: 'reconhecimento básico' },
      intermediate: { level: 2, description: 'discriminação avançada' },
      complex: { level: 3, description: 'processamento multi-dimensional' },
      expert: { level: 4, description: 'análise especializada' }
    };
    
    this.colorKnowledgeDomains = {
      basic: ['vermelho', 'azul', 'amarelo', 'verde'],
      advanced: ['laranja', 'roxo', 'rosa'],
      complex: ['marrom', 'cinza', 'bege', 'creme']
    };
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise cognitiva relacionada à cor
   */
  collect(data) {
    return this.analyze(data);
  }
  
  async analyze(data) {
    if (!data || !data.cognitiveData) {
      console.warn('ColorCognitionCollector: Dados inválidos recebidos', data);
      return {
        colorCategorization: 0.7,
        colorSemantics: 0.7,
        colorMemory: 0.7,
        processingSpeed: 0.7,
        colorNaming: 0.7,
        colorAssociation: 0.7,
        conceptualFlexibility: 0.7,
        cognitiveLoad: 0.7
      };
    }

    return {
      colorCategorization: this.assessColorCategorization(data),
      colorSemantics: this.assessColorSemantics(data),
      colorMemory: this.assessColorMemory(data),
      processingSpeed: this.assessProcessingSpeed(data),
      colorNaming: this.assessColorNaming(data),
      colorAssociation: this.assessColorAssociation(data),
      conceptualFlexibility: this.assessConceptualFlexibility(data),
      cognitiveLoad: this.assessCognitiveLoad(data),
      colorKnowledgeBase: this.evaluateColorKnowledgeBase(data),
      cognitiveStrategies: this.identifyCognitiveStrategies(data)
    };
  }

  assessColorCategorization(data) {
    const interactions = data.cognitiveData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Avaliar capacidade de categorizar cores corretamente
    const categorizationTasks = interactions.filter(i => i.taskType === 'categorization');
    
    if (categorizationTasks.length === 0) {
      // Estimar baseado na precisão geral por categoria de cor
      return this.estimateCategorizationFromGeneral(interactions);
    }
    
    let categorizationScore = 0;
    
    // Avaliar por nível de complexidade
    Object.keys(this.cognitiveComplexity).forEach(complexity => {
      const complexityTasks = categorizationTasks.filter(t => t.complexity === complexity);
      
      if (complexityTasks.length > 0) {
        const accuracy = complexityTasks.filter(t => t.correct).length / complexityTasks.length;
        const complexityWeight = this.cognitiveComplexity[complexity].level;
        
        // Ponderação por complexidade
        categorizationScore += accuracy * complexityWeight;
      }
    });
    
    // Normalizar pelo número total de níveis testados
    const testedComplexities = Object.keys(this.cognitiveComplexity).filter(c => 
      categorizationTasks.some(t => t.complexity === c)
    ).length;
    
    const finalScore = testedComplexities > 0 ? categorizationScore / (testedComplexities * 2.5) : 0.7; // 2.5 = média dos pesos
    
    return Math.max(0, Math.min(1, finalScore));
  }

  assessColorSemantics(data) {
    const interactions = data.cognitiveData.interactions || [];
    const semanticTasks = interactions.filter(i => i.taskType === 'semantic' || i.hasSemanticComponent);
    
    if (semanticTasks.length === 0) {
      // Estimar baseado em associações cor-objeto
      return this.estimateSemanticFromAssociations(interactions);
    }
    
    // Avaliar conhecimento semântico de cores
    const semanticAccuracy = semanticTasks.filter(t => t.correct).length / semanticTasks.length;
    
    // Avaliar velocidade de acesso semântico
    const semanticSpeed = this.calculateSemanticSpeed(semanticTasks);
    
    // Avaliar riqueza semântica (variedade de associações)
    const semanticRichness = this.calculateSemanticRichness(semanticTasks);
    
    const semanticScore = (
      semanticAccuracy * 0.5 +
      semanticSpeed * 0.25 +
      semanticRichness * 0.25
    );
    
    return Math.max(0, Math.min(1, semanticScore));
  }

  assessColorMemory(data) {
    const interactions = data.cognitiveData.interactions || [];
    const memoryTasks = interactions.filter(i => i.taskType === 'memory');
    
    if (memoryTasks.length === 0) {
      // Estimar baseado em padrões de recall
      return this.estimateMemoryFromRecall(interactions);
    }
    
    // Avaliar diferentes tipos de memória cromática
    const shortTermMemory = this.assessShortTermColorMemory(memoryTasks);
    const longTermMemory = this.assessLongTermColorMemory(memoryTasks);
    const workingMemory = this.assessWorkingColorMemory(memoryTasks);
    
    const memoryScore = (
      shortTermMemory * 0.4 +
      longTermMemory * 0.3 +
      workingMemory * 0.3
    );
    
    return Math.max(0, Math.min(1, memoryScore));
  }

  assessProcessingSpeed(data) {
    const interactions = data.cognitiveData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Analisar velocidade de processamento cognitivo por tipo de tarefa
    const processingTimes = this.categorizeProcessingTimes(interactions);
    
    let speedScore = 0;
    let categoryCount = 0;
    
    Object.entries(processingTimes).forEach(([category, times]) => {
      if (times.length > 0) {
        const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
        const categorySpeed = this.calculateCategorySpeed(category, avgTime);
        
        speedScore += categorySpeed;
        categoryCount++;
      }
    });
    
    const overallSpeed = categoryCount > 0 ? speedScore / categoryCount : 0.7;
    
    // Ajustar pela consistência da velocidade
    const speedConsistency = this.calculateSpeedConsistency(interactions);
    
    return Math.max(0, Math.min(1, overallSpeed * speedConsistency));
  }

  assessColorNaming(data) {
    const interactions = data.cognitiveData.interactions || [];
    const namingTasks = interactions.filter(i => i.taskType === 'naming' || i.requiresNaming);
    
    if (namingTasks.length === 0) {
      // Estimar baseado na precisão de identificação
      return this.estimateNamingFromIdentification(interactions);
    }
    
    // Avaliar precisão na nomeação
    const namingAccuracy = namingTasks.filter(t => t.correct).length / namingTasks.length;
    
    // Avaliar velocidade de nomeação
    const namingSpeed = this.calculateNamingSpeed(namingTasks);
    
    // Avaliar consistência na nomeação
    const namingConsistency = this.calculateNamingConsistency(namingTasks);
    
    // Avaliar vocabulário cromático
    const colorVocabulary = this.assessColorVocabulary(namingTasks);
    
    const namingScore = (
      namingAccuracy * 0.4 +
      namingSpeed * 0.2 +
      namingConsistency * 0.2 +
      colorVocabulary * 0.2
    );
    
    return Math.max(0, Math.min(1, namingScore));
  }

  assessColorAssociation(data) {
    const interactions = data.cognitiveData.interactions || [];
    const associationTasks = interactions.filter(i => i.taskType === 'association');
    
    if (associationTasks.length === 0) {
      // Estimar baseado em padrões de resposta
      return this.estimateAssociationFromPatterns(interactions);
    }
    
    // Avaliar força das associações cor-conceito
    const associationStrength = this.calculateAssociationStrength(associationTasks);
    
    // Avaliar flexibilidade associativa
    const associativeFlexibility = this.calculateAssociativeFlexibility(associationTasks);
    
    // Avaliar apropriação cultural das associações
    const culturalAppropriation = this.assessCulturalAppropriation(associationTasks);
    
    const associationScore = (
      associationStrength * 0.4 +
      associativeFlexibility * 0.3 +
      culturalAppropriation * 0.3
    );
    
    return Math.max(0, Math.min(1, associationScore));
  }

  assessConceptualFlexibility(data) {
    const interactions = data.cognitiveData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Avaliar capacidade de mudar entre diferentes aspectos conceituais da cor
    const conceptualSwitches = this.identifyConceptualSwitches(interactions);
    const switchingEfficiency = this.calculateSwitchingEfficiency(conceptualSwitches);
    
    // Avaliar adaptabilidade a novos contextos cromáticos
    const contextualAdaptation = this.assessContextualAdaptation(interactions);
    
    // Avaliar pensamento divergente com cores
    const divergentThinking = this.assessDivergentColorThinking(interactions);
    
    const flexibilityScore = (
      switchingEfficiency * 0.4 +
      contextualAdaptation * 0.3 +
      divergentThinking * 0.3
    );
    
    return Math.max(0, Math.min(1, flexibilityScore));
  }

  assessCognitiveLoad(data) {
    const interactions = data.cognitiveData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Analisar como o desempenho muda com aumento da carga cognitiva
    const loadLevels = this.categorizeByLoad(interactions);
    
    let loadScore = 0;
    let loadCount = 0;
    
    Object.entries(loadLevels).forEach(([level, tasks]) => {
      if (tasks.length > 0) {
        const accuracy = tasks.filter(t => t.correct).length / tasks.length;
        const expectedAccuracy = this.getExpectedAccuracy(level);
        
        // Pontuação baseada em quão bem mantém performance sob carga
        const loadPerformance = accuracy / expectedAccuracy;
        loadScore += Math.min(1, loadPerformance);
        loadCount++;
      }
    });
    
    return loadCount > 0 ? loadScore / loadCount : 0.7;
  }

  evaluateColorKnowledgeBase(data) {
    const interactions = data.cognitiveData.interactions || [];
    
    const knowledgeEvaluation = {
      basicColors: this.evaluateBasicColorKnowledge(interactions),
      advancedColors: this.evaluateAdvancedColorKnowledge(interactions),
      complexColors: this.evaluateComplexColorKnowledge(interactions),
      colorRelationships: this.evaluateColorRelationships(interactions),
      colorTheory: this.evaluateColorTheoryKnowledge(interactions)
    };
    
    return knowledgeEvaluation;
  }

  identifyCognitiveStrategies(data) {
    const interactions = data.cognitiveData.interactions || [];
    
    const strategies = {
      verbal: this.identifyVerbalStrategies(interactions),
      visual: this.identifyVisualStrategies(interactions),
      associative: this.identifyAssociativeStrategies(interactions),
      categorical: this.identifyCategoricalStrategies(interactions),
      systematic: this.identifySystematicStrategies(interactions)
    };
    
    // Identificar estratégia dominante
    const dominantStrategy = Object.entries(strategies)
      .sort((a, b) => b[1].score - a[1].score)[0];
    
    return {
      strategies: strategies,
      dominantStrategy: dominantStrategy[0],
      strategyEffectiveness: dominantStrategy[1].score,
      strategyFlexibility: this.calculateStrategyFlexibility(strategies)
    };
  }

  // Métodos auxiliares
  estimateCategorizationFromGeneral(interactions) {
    const colorCategories = {
      primary: ['vermelho', 'azul', 'amarelo'],
      secondary: ['verde', 'laranja', 'roxo'],
      tertiary: ['rosa', 'marrom']
    };
    
    let categorizationScore = 0;
    let categoryCount = 0;
    
    Object.entries(colorCategories).forEach(([category, colors]) => {
      const categoryInteractions = interactions.filter(i => 
        colors.includes(i.targetColor)
      );
      
      if (categoryInteractions.length > 0) {
        const accuracy = categoryInteractions.filter(i => i.correct).length / categoryInteractions.length;
        categorizationScore += accuracy;
        categoryCount++;
      }
    });
    
    return categoryCount > 0 ? categorizationScore / categoryCount : 0.7;
  }

  estimateSemanticFromAssociations(interactions) {
    // Estimar conhecimento semântico baseado em associações cor-objeto implícitas
    const semanticAssociations = {
      'vermelho': ['maçã', 'morango', 'rosa', 'coração'],
      'verde': ['folha', 'grama', 'sapo', 'árvore'],
      'azul': ['céu', 'mar', 'água', 'gelo'],
      'amarelo': ['sol', 'banana', 'ouro', 'limão']
    };
    
    let semanticScore = 0;
    let semanticTests = 0;
    
    interactions.forEach(interaction => {
      const targetColor = interaction.targetColor;
      const selectedObject = interaction.selectedObject;
      
      if (semanticAssociations[targetColor] && selectedObject) {
        const isSemanticMatch = semanticAssociations[targetColor].includes(selectedObject);
        if (isSemanticMatch && interaction.correct) {
          semanticScore += 1;
        } else if (!isSemanticMatch && !interaction.correct) {
          semanticScore += 0.5; // Crédito parcial por evitar associação incorreta
        }
        semanticTests++;
      }
    });
    
    return semanticTests > 0 ? semanticScore / semanticTests : 0.7;
  }

  estimateMemoryFromRecall(interactions) {
    // Estimar memória cromática baseado em padrões de recall
    if (interactions.length < 5) return 0.7;
    
    const recallPatterns = this.analyzeRecallPatterns(interactions);
    
    // Memória boa = melhoria na segunda exposição à mesma cor
    const memoryIndicator = recallPatterns.improvementRate;
    
    return Math.max(0.3, Math.min(1, 0.5 + memoryIndicator));
  }

  assessShortTermColorMemory(memoryTasks) {
    if (memoryTasks.length === 0) return 0.7;
    
    // Avaliar capacidade de memória de curto prazo para cores
    const shortTermTasks = memoryTasks.filter(task => 
      (task.retentionTime || 0) <= 30000 // 30 segundos ou menos
    );
    
    if (shortTermTasks.length === 0) {
      // Estimar baseado em todas as tarefas
      const accuracy = memoryTasks.filter(task => task.correct).length / memoryTasks.length;
      return Math.max(0, Math.min(1, accuracy));
    }
    
    // Avaliar por diferentes aspectos da memória de curto prazo
    let shortTermScore = 0;
    
    // Capacidade (quantas cores consegue reter simultaneamente)
    const maxItemsRetained = Math.max(...shortTermTasks.map(task => task.itemsCount || 1));
    const capacityScore = Math.min(1, maxItemsRetained / 7); // 7±2 regra de Miller
    
    // Precisão (acurácia na recuperação)
    const accuracy = shortTermTasks.filter(task => task.correct).length / shortTermTasks.length;
    
    // Velocidade de acesso
    const avgAccessTime = shortTermTasks.reduce((sum, task) => sum + (task.responseTime || 2000), 0) / shortTermTasks.length;
    const speedScore = Math.max(0, Math.min(1, (3000 - avgAccessTime) / 2000));
    
    // Persistência (manutenção ao longo do tempo)
    const persistenceScore = shortTermTasks.filter(task => 
      task.retentionTime > 10000 && task.correct
    ).length / Math.max(1, shortTermTasks.filter(task => task.retentionTime > 10000).length);
    
    // Combinação ponderada
    shortTermScore = (capacityScore * 0.3) + (accuracy * 0.4) + (speedScore * 0.2) + (persistenceScore * 0.1);
    
    return Math.max(0, Math.min(1, shortTermScore));
  }

  assessLongTermColorMemory(memoryTasks) {
    if (memoryTasks.length === 0) return 0.7;
    
    // Avaliar capacidade de memória de longo prazo para cores
    const longTermTasks = memoryTasks.filter(task => 
      (task.retentionTime || 0) > 30000 // Mais de 30 segundos
    );
    
    if (longTermTasks.length === 0) {
      // Estimar baseado em tarefas de longo prazo disponíveis
      const allTasks = memoryTasks.filter(task => task.retentionTime > 5000);
      if (allTasks.length === 0) return 0.7;
      
      const accuracy = allTasks.filter(task => task.correct).length / allTasks.length;
      return Math.max(0.3, Math.min(1, accuracy * 0.9)); // Penalizar por não ser realmente longo prazo
    }
    
    let longTermScore = 0;
    
    // Consolidação (capacidade de reter informação ao longo do tempo)
    const consolidationTasks = longTermTasks.filter(task => task.retentionTime > 60000); // > 1 minuto
    const consolidationRate = consolidationTasks.length > 0 ? 
      consolidationTasks.filter(task => task.correct).length / consolidationTasks.length : 0.7;
    
    // Recuperação (capacidade de acessar informação armazenada)
    const retrievalTasks = longTermTasks.filter(task => task.taskType === 'recall' || task.requiresRecall);
    const retrievalRate = retrievalTasks.length > 0 ? 
      retrievalTasks.filter(task => task.correct).length / retrievalTasks.length : 0.7;
    
    // Reconhecimento (capacidade de identificar informação previamente aprendida)
    const recognitionTasks = longTermTasks.filter(task => task.taskType === 'recognition');
    const recognitionRate = recognitionTasks.length > 0 ? 
      recognitionTasks.filter(task => task.correct).length / recognitionTasks.length : 0.8;
    
    // Persistência (manutenção da informação sem ensaio ativo)
    const persistenceTasks = longTermTasks.filter(task => 
      task.retentionTime > 120000 && !task.hadIntermediateRehearsal
    );
    const persistenceRate = persistenceTasks.length > 0 ? 
      persistenceTasks.filter(task => task.correct).length / persistenceTasks.length : 0.6;
    
    // Interferência (resistência ao esquecimento devido a nova informação)
    const interferenceTasks = longTermTasks.filter(task => task.hadInterference);
    const interferenceResistance = interferenceTasks.length > 0 ? 
      interferenceTasks.filter(task => task.correct).length / interferenceTasks.length : 0.7;
    
    // Combinação ponderada dos aspectos de memória de longo prazo
    longTermScore = (
      consolidationRate * 0.25 +
      retrievalRate * 0.25 +
      recognitionRate * 0.20 +
      persistenceRate * 0.20 +
      interferenceResistance * 0.10
    );
    
    return Math.max(0, Math.min(1, longTermScore));
  }

  assessWorkingColorMemory(memoryTasks) {
    if (memoryTasks.length === 0) return 0.7;
    
    // Avaliar capacidade de memória de trabalho para cores
    const workingMemoryTasks = memoryTasks.filter(task => 
      (task.presentationTime || 0) <= 15000 // 15 segundos ou menos
    );
    
    if (workingMemoryTasks.length === 0) {
      // Estimar baseado em todas as tarefas de memória
      const accuracy = memoryTasks.filter(task => task.correct).length / memoryTasks.length;
      return Math.max(0, Math.min(1, accuracy * 0.8));
    }
    
    let workingMemoryScore = 0;
    
    // Avaliar por diferentes aspectos da memória de trabalho
    // Capacidade (quantas cores consegue manter na memória de trabalho)
    const maxCapacity = Math.max(...workingMemoryTasks.map(task => task.itemsCount || 1));
    const capacityScore = Math.min(1, maxCapacity / 5); // 5±2 regra de Miller
    
    // Precisão (acurácia na recuperação imediata)
    const immediateRecallAccuracy = workingMemoryTasks.filter(task => task.correct).length / workingMemoryTasks.length;
    
    // Velocidade de resposta
    const avgResponseTime = workingMemoryTasks.reduce((sum, task) => sum + (task.responseTime || 2000), 0) / workingMemoryTasks.length;
    const speedScore = Math.max(0, Math.min(1, (3000 - avgResponseTime) / 2000));
    
    // Combinação ponderada
    workingMemoryScore = (capacityScore * 0.3) + (immediateRecallAccuracy * 0.4) + (speedScore * 0.3);
    
    return Math.max(0, Math.min(1, workingMemoryScore));
  }

  assessColorRecognition(data) {
    const interactions = data.cognitiveData.interactions || [];
    const recognitionTasks = interactions.filter(i => i.taskType === 'recognition');
    
    if (recognitionTasks.length === 0) {
      // Estimar baseado em tarefas de reconhecimento de cores
      return this.estimateRecognitionFromTasks(interactions);
    }
    
    // Avaliar precisão no reconhecimento de cores
    const recognitionAccuracy = recognitionTasks.filter(t => t.correct).length / recognitionTasks.length;
    
    // Avaliar velocidade de reconhecimento
    const recognitionSpeed = this.calculateRecognitionSpeed(recognitionTasks);
    
    // Avaliar consistência no reconhecimento
    const recognitionConsistency = this.calculateRecognitionConsistency(recognitionTasks);
    
    const recognitionScore = (
      recognitionAccuracy * 0.5 +
      recognitionSpeed * 0.3 +
      recognitionConsistency * 0.2
    );
    
    return Math.max(0, Math.min(1, recognitionScore));
  }

  // Métodos auxiliares para avaliação de memória
  estimateRecognitionFromTasks(interactions) {
    // Estimar capacidade de reconhecimento baseado em tarefas de reconhecimento de cores
    const recognitionRate = interactions.filter(i => i.taskType === 'recognition' && i.correct).length / 
                            Math.max(1, interactions.filter(i => i.taskType === 'recognition').length);
    
    return Math.max(0.3, Math.min(1, recognitionRate));
  }

  calculateRecognitionSpeed(recognitionTasks) {
    const responseTimes = recognitionTasks
      .filter(t => t.correct && t.responseTime)
      .map(t => t.responseTime);
    
    if (responseTimes.length === 0) return 0.5;
    
    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    
    // Velocidade ideal de reconhecimento ~ 1000ms
    const speedScore = Math.max(0, 1 - (avgResponseTime - 1000) / 1500);
    return Math.min(1, speedScore);
  }

  calculateRecognitionConsistency(recognitionTasks) {
    const colorRecognitions = {};
    
    recognitionTasks.forEach(task => {
      const color = task.targetColor;
      const recognizedColor = task.recognizedColor || task.givenName;
      
      if (!colorRecognitions[color]) {
        colorRecognitions[color] = [];
      }
      colorRecognitions[color].push(recognizedColor);
    });
    
    // Consistência = quão consistente é o reconhecimento da mesma cor
    let consistencySum = 0;
    let colorCount = 0;
    
    Object.values(colorRecognitions).forEach(recognizedColors => {
      if (recognizedColors.length > 1) {
        const mostCommon = this.getMostCommonName(recognizedColors);
        const consistency = recognizedColors.filter(name => name === mostCommon).length / recognizedColors.length;
        consistencySum += consistency;
        colorCount++;
      }
    });
    
    return colorCount > 0 ? consistencySum / colorCount : 1;
  }

  // Métodos auxiliares
  categorizeProcessingTimes(interactions) {
    const categories = {
      simple: [],
      complex: [],
      semantic: [],
      memory: []
    };
    
    interactions.forEach(interaction => {
      if (interaction.responseTime && interaction.responseTime > 0) {
        const complexity = this.determineTaskComplexity(interaction);
        if (categories[complexity]) {
          categories[complexity].push(interaction.responseTime);
        }
      }
    });
    
    return categories;
  }

  calculateCategorySpeed(category, avgTime) {
    const speedThresholds = {
      simple: { fast: 800, normal: 1500, slow: 2500 },
      complex: { fast: 1200, normal: 2000, slow: 3500 },
      semantic: { fast: 1000, normal: 1800, slow: 3000 },
      memory: { fast: 1500, normal: 2500, slow: 4000 }
    };
    
    const thresholds = speedThresholds[category] || speedThresholds.simple;
    
    if (avgTime <= thresholds.fast) return 1.0;
    if (avgTime <= thresholds.normal) return 0.8;
    if (avgTime <= thresholds.slow) return 0.6;
    return 0.4;
  }

  calculateSpeedConsistency(interactions) {
    const times = interactions
      .filter(i => i.responseTime && i.responseTime > 0)
      .map(i => i.responseTime);
    
    if (times.length < 3) return 1;
    
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Consistência = 1 - coeficiente de variação normalizado
    const coefficientOfVariation = standardDeviation / mean;
    return Math.max(0.5, 1 - Math.min(coefficientOfVariation, 0.5));
  }

  estimateNamingFromIdentification(interactions) {
    // Estimar capacidade de nomeação baseado na precisão de identificação
    const identificationAccuracy = interactions.filter(i => i.correct).length / interactions.length;
    
    // Naming típicamente um pouco mais difícil que identificação
    const estimatedNaming = identificationAccuracy * 0.9;
    
    return Math.max(0.3, Math.min(1, estimatedNaming));
  }

  calculateNamingSpeed(namingTasks) {
    const namingTimes = namingTasks
      .filter(t => t.correct && t.responseTime)
      .map(t => t.responseTime);
    
    if (namingTimes.length === 0) return 0.5;
    
    const avgNamingTime = namingTimes.reduce((sum, time) => sum + time, 0) / namingTimes.length;
    
    // Velocidade ideal de nomeação ~ 1200ms
    const speedScore = Math.max(0, 1 - (avgNamingTime - 1200) / 2000);
    return Math.min(1, speedScore);
  }

  calculateNamingConsistency(namingTasks) {
    const colorNames = {};
    
    namingTasks.forEach(task => {
      const color = task.targetColor;
      const name = task.givenName;
      
      if (!colorNames[color]) {
        colorNames[color] = [];
      }
      colorNames[color].push(name);
    });
    
    // Consistência = quão consistente é a nomeação da mesma cor
    let consistencySum = 0;
    let colorCount = 0;
    
    Object.values(colorNames).forEach(names => {
      if (names.length > 1) {
        const mostCommon = this.getMostCommonName(names);
        const consistency = names.filter(name => name === mostCommon).length / names.length;
        consistencySum += consistency;
        colorCount++;
      }
    });
    
    return colorCount > 0 ? consistencySum / colorCount : 1;
  }

  assessColorVocabulary(namingTasks) {
    const uniqueNames = new Set(namingTasks.map(t => t.givenName)).size;
    const totalColors = new Set(namingTasks.map(t => t.targetColor)).size;
    
    // Vocabulário rico = muitos nomes únicos apropriados
    const vocabularyRichness = totalColors > 0 ? Math.min(uniqueNames / totalColors, 2) / 2 : 0.5;
    
    return vocabularyRichness;
  }

  estimateAssociationFromPatterns(interactions) {
    // Estimar capacidade de associação baseado em padrões de resposta
    const responsePatterns = this.analyzeResponsePatterns(interactions);
    
    // Associação boa = padrões consistentes e culturalmente apropriados
    const associationScore = responsePatterns.consistency * responsePatterns.appropriateness;
    
    return Math.max(0.4, Math.min(1, associationScore));
  }

  calculateAssociationStrength(associationTasks) {
    // Força das associações baseada na velocidade e precisão
    let strengthSum = 0;
    
    associationTasks.forEach(task => {
      const accuracy = task.correct ? 1 : 0;
      const speed = task.responseTime ? Math.max(0, 1 - (task.responseTime - 1000) / 2000) : 0.5;
      const strength = (accuracy * 0.7) + (speed * 0.3);
      strengthSum += strength;
    });
    
    return associationTasks.length > 0 ? strengthSum / associationTasks.length : 0.7;
  }

  calculateAssociativeFlexibility(associationTasks) {
    // Flexibilidade = capacidade de formar múltiplas associações
    const colorAssociations = {};
    
    associationTasks.forEach(task => {
      const color = task.targetColor;
      const association = task.association;
      
      if (!colorAssociations[color]) {
        colorAssociations[color] = new Set();
      }
      colorAssociations[color].add(association);
    });
    
    const avgAssociationsPerColor = Object.values(colorAssociations)
      .reduce((sum, associations) => sum + associations.size, 0) / Object.keys(colorAssociations).length;
    
    // Normalizar (3+ associações = alta flexibilidade)
    return Math.min(avgAssociationsPerColor / 3, 1);
  }

  assessCulturalAppropriation(associationTasks) {
    // Avaliar se as associações são culturalmente apropriadas
    const culturallyAppropriate = associationTasks.filter(task => 
      this.isCulturallyAppropriate(task.targetColor, task.association)
    ).length;
    
    return associationTasks.length > 0 ? culturallyAppropriate / associationTasks.length : 0.8;
  }

  identifyConceptualSwitches(interactions) {
    const switches = [];
    
    for (let i = 1; i < interactions.length; i++) {
      const prev = interactions[i - 1];
      const curr = interactions[i];
      
      if (this.isConceptualSwitch(prev, curr)) {
        switches.push({
          from: prev.conceptualAspect,
          to: curr.conceptualAspect,
          efficiency: this.calculateSwitchEfficiency(prev, curr)
        });
      }
    }
    
    return switches;
  }

  calculateSwitchingEfficiency(conceptualSwitches) {
    if (conceptualSwitches.length === 0) return 0.8;
    
    const avgEfficiency = conceptualSwitches
      .reduce((sum, sw) => sum + sw.efficiency, 0) / conceptualSwitches.length;
    
    return avgEfficiency;
  }

  assessContextualAdaptation(interactions) {
    const contexts = this.identifyContexts(interactions);
    
    if (contexts.length < 2) return 0.7;
    
    let adaptationSum = 0;
    
    contexts.forEach(context => {
      const contextAccuracy = context.interactions.filter(i => i.correct).length / context.interactions.length;
      adaptationSum += contextAccuracy;
    });
    
    return adaptationSum / contexts.length;
  }

  assessDivergentColorThinking(interactions) {
    // Avaliar pensamento divergente baseado na criatividade das respostas
    const creativityScores = interactions
      .filter(i => i.creativityRating)
      .map(i => i.creativityRating);
    
    if (creativityScores.length === 0) return 0.6;
    
    const avgCreativity = creativityScores.reduce((sum, score) => sum + score, 0) / creativityScores.length;
    return Math.min(avgCreativity / 5, 1); // Assumindo escala 1-5
  }

  categorizeByLoad(interactions) {
    const loadLevels = {
      low: [],
      medium: [],
      high: [],
      extreme: []
    };
    
    interactions.forEach(interaction => {
      const load = this.determineCognitiveLoad(interaction);
      if (loadLevels[load]) {
        loadLevels[load].push(interaction);
      }
    });
    
    return loadLevels;
  }

  getExpectedAccuracy(loadLevel) {
    const expectedAccuracies = {
      low: 0.9,
      medium: 0.8,
      high: 0.7,
      extreme: 0.6
    };
    
    return expectedAccuracies[loadLevel] || 0.8;
  }

  evaluateBasicColorKnowledge(interactions) {
    const basicColors = this.colorKnowledgeDomains.basic;
    const basicInteractions = interactions.filter(i => basicColors.includes(i.targetColor));
    
    return basicInteractions.length > 0 ? 
      basicInteractions.filter(i => i.correct).length / basicInteractions.length : 0.8;
  }

  evaluateAdvancedColorKnowledge(interactions) {
    const advancedColors = this.colorKnowledgeDomains.advanced;
    const advancedInteractions = interactions.filter(i => advancedColors.includes(i.targetColor));
    
    return advancedInteractions.length > 0 ? 
      advancedInteractions.filter(i => i.correct).length / advancedInteractions.length : 0.7;
  }

  evaluateComplexColorKnowledge(interactions) {
    const complexColors = this.colorKnowledgeDomains.complex;
    const complexInteractions = interactions.filter(i => complexColors.includes(i.targetColor));
    
    return complexInteractions.length > 0 ? 
      complexInteractions.filter(i => i.correct).length / complexInteractions.length : 0.6;
  }

  evaluateColorRelationships(interactions) {
    // Avaliar conhecimento de relações entre cores (complementares, análogas, etc.)
    const relationshipTasks = interactions.filter(i => i.taskType === 'relationship');
    
    return relationshipTasks.length > 0 ? 
      relationshipTasks.filter(i => i.correct).length / relationshipTasks.length : 0.6;
  }

  evaluateColorTheoryKnowledge(interactions) {
    // Avaliar conhecimento de teoria das cores
    const theoryTasks = interactions.filter(i => i.taskType === 'theory');
    
    return theoryTasks.length > 0 ? 
      theoryTasks.filter(i => i.correct).length / theoryTasks.length : 0.5;
  }

  identifyVerbalStrategies(interactions) {
    const verbalIndicators = interactions.filter(i => 
      i.strategy === 'verbal' || i.usedVerbalization
    ).length;
    
    return {
      score: interactions.length > 0 ? verbalIndicators / interactions.length : 0.3,
      indicators: verbalIndicators
    };
  }

  identifyVisualStrategies(interactions) {
    const visualIndicators = interactions.filter(i => 
      i.strategy === 'visual' || i.usedVisualization
    ).length;
    
    return {
      score: interactions.length > 0 ? visualIndicators / interactions.length : 0.5,
      indicators: visualIndicators
    };
  }

  identifyAssociativeStrategies(interactions) {
    const associativeIndicators = interactions.filter(i => 
      i.strategy === 'associative' || i.usedAssociation
    ).length;
    
    return {
      score: interactions.length > 0 ? associativeIndicators / interactions.length : 0.4,
      indicators: associativeIndicators
    };
  }

  identifyCategoricalStrategies(interactions) {
    const categoricalIndicators = interactions.filter(i => 
      i.strategy === 'categorical' || i.usedCategorization
    ).length;
    
    return {
      score: interactions.length > 0 ? categoricalIndicators / interactions.length : 0.6,
      indicators: categoricalIndicators
    };
  }

  identifySystematicStrategies(interactions) {
    const systematicIndicators = interactions.filter(i => 
      i.strategy === 'systematic' || i.usedSystematicApproach
    ).length;
    
    return {
      score: interactions.length > 0 ? systematicIndicators / interactions.length : 0.5,
      indicators: systematicIndicators
    };
  }

  calculateStrategyFlexibility(strategies) {
    const usedStrategies = Object.values(strategies).filter(s => s.score > 0.2).length;
    
    // Flexibilidade = uso de múltiplas estratégias
    return Math.min(usedStrategies / 3, 1); // 3+ estratégias = alta flexibilidade
  }

  // Métodos auxiliares menores
  analyzeRecallPatterns(interactions) {
    // Analisar padrões de recall para estimar memória
    const colorRecalls = {};
    
    interactions.forEach((interaction, index) => {
      const color = interaction.targetColor;
      if (!colorRecalls[color]) {
        colorRecalls[color] = [];
      }
      colorRecalls[color].push({
        attempt: index,
        correct: interaction.correct,
        time: interaction.responseTime
      });
    });
    
    let improvementSum = 0;
    let colorCount = 0;
    
    Object.values(colorRecalls).forEach(recalls => {
      if (recalls.length > 1) {
        const firstAccuracy = recalls[0].correct ? 1 : 0;
        const laterAccuracy = recalls.slice(1).filter(r => r.correct).length / (recalls.length - 1);
        const improvement = laterAccuracy - firstAccuracy;
        
        improvementSum += Math.max(0, improvement);
        colorCount++;
      }
    });
    
    return {
      improvementRate: colorCount > 0 ? improvementSum / colorCount : 0,
      totalRecalls: Object.keys(colorRecalls).length
    };
  }

  determineTaskComplexity(interaction) {
    if (interaction.complexity) return interaction.complexity;
    
    // Estimar complexidade baseado em características da tarefa
    const colorComplexity = this.getColorComplexity(interaction.targetColor);
    const taskComplexity = interaction.taskType === 'memory' ? 'complex' : 
                          interaction.taskType === 'semantic' ? 'semantic' : 'simple';
    
    return taskComplexity;
  }

  getColorComplexity(color) {
    if (this.colorKnowledgeDomains.basic.includes(color)) return 'simple';
    if (this.colorKnowledgeDomains.advanced.includes(color)) return 'complex';
    if (this.colorKnowledgeDomains.complex.includes(color)) return 'complex';
    return 'simple';
  }

  getMostCommonName(names) {
    const nameCounts = {};
    names.forEach(name => {
      nameCounts[name] = (nameCounts[name] || 0) + 1;
    });
    
    return Object.entries(nameCounts)
      .sort((a, b) => b[1] - a[1])[0][0];
  }

  analyzeResponsePatterns(interactions) {
    // Analisar padrões de resposta para estimar associação
    const patterns = {
      consistency: this.calculatePatternConsistency(interactions),
      appropriateness: this.assessPatternAppropriation(interactions)
    };
    
    return patterns;
  }

  calculatePatternConsistency(interactions) {
    // Medir consistência nos padrões de resposta
    if (interactions.length < 3) return 0.8;
    
    const responseTimes = interactions.map(i => i.responseTime || 2000);
    const mean = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / responseTimes.length;
    
    const consistency = Math.max(0, 1 - Math.sqrt(variance) / mean);
    return consistency;
  }

  assessPatternAppropriation(interactions) {
    // Avaliar se os padrões são apropriados
    const appropriateResponses = interactions.filter(i => 
      this.isAppropriateResponse(i)
    ).length;
    
    return interactions.length > 0 ? appropriateResponses / interactions.length : 0.7;
  }

  isCulturallyAppropriate(color, association) {
    const culturalAssociations = {
      'vermelho': ['amor', 'paixão', 'sangue', 'fogo', 'maçã'],
      'verde': ['natureza', 'esperança', 'dinheiro', 'folha'],
      'azul': ['céu', 'mar', 'tranquilidade', 'frio'],
      'amarelo': ['sol', 'alegria', 'ouro', 'banana'],
      'roxo': ['realeza', 'mistério', 'uva'],
      'laranja': ['laranja', 'outono', 'energia'],
      'rosa': ['feminino', 'romance', 'flores'],
      'marrom': ['terra', 'madeira', 'estabilidade']
    };
    
    return culturalAssociations[color]?.includes(association) || false;
  }

  isConceptualSwitch(prev, curr) {
    return prev.conceptualAspect !== curr.conceptualAspect;
  }

  calculateSwitchEfficiency(prev, curr) {
    // Eficiência de mudança conceitual
    const timeDiff = curr.responseTime - prev.responseTime;
    const expectedSwitchTime = 500; // 500ms esperado para mudança
    
    const efficiency = Math.max(0, 1 - Math.abs(timeDiff - expectedSwitchTime) / 1000);
    return efficiency;
  }

  identifyContexts(interactions) {
    // Identificar diferentes contextos na tarefa
    const contexts = [];
    let currentContext = null;
    
    interactions.forEach(interaction => {
      const context = interaction.context || 'default';
      
      if (currentContext !== context) {
        if (currentContext !== null) {
          contexts[contexts.length - 1].end = interaction.timestamp;
        }
        
        contexts.push({
          context: context,
          start: interaction.timestamp,
          interactions: []
        });
        currentContext = context;
      }
      
      contexts[contexts.length - 1].interactions.push(interaction);
    });
    
    return contexts;
  }

  determineCognitiveLoad(interaction) {
    const factors = [
      interaction.complexity || 'medium',
      interaction.timeConstraint ? 'high' : 'low',
      interaction.distractors ? 'high' : 'low',
      interaction.memoryRequirement || 'medium'
    ];
    
    const highFactors = factors.filter(f => f === 'high').length;
    
    if (highFactors >= 3) return 'extreme';
    if (highFactors >= 2) return 'high';
    if (highFactors >= 1) return 'medium';
    return 'low';
  }

  isAppropriateResponse(interaction) {
    // Verificar se a resposta é apropriada ao contexto
    return interaction.correct || interaction.responseAppropriation > 0.5;
  }

  generateCognitionInsights(results) {
    const insights = [];
    
    if (results.colorCategorization < 0.6) {
      insights.push('Dificuldades na categorização cromática');
    }
    
    if (results.colorSemantics < 0.6) {
      insights.push('Limitações no conhecimento semântico de cores');
    }
    
    if (results.colorMemory < 0.5) {
      insights.push('Problemas de memória cromática');
    }
    
    if (results.processingSpeed < 0.5) {
      insights.push('Velocidade de processamento cognitivo reduzida');
    }
    
    if (results.colorNaming < 0.6) {
      insights.push('Dificuldades na nomeação de cores');
    }
    
    if (results.colorAssociation < 0.6) {
      insights.push('Limitações nas associações cromáticas');
    }
    
    if (results.conceptualFlexibility < 0.5) {
      insights.push('Flexibilidade conceitual reduzida');
    }
    
    if (results.cognitiveLoad < 0.5) {
      insights.push('Sensibilidade elevada à carga cognitiva');
    }
    
    return insights;
  }

  calculateSemanticSpeed(semanticTasks) {
    if (semanticTasks.length === 0) return 0.7;
    
    const avgResponseTime = semanticTasks.reduce((sum, task) => sum + (task.responseTime || 2000), 0) / semanticTasks.length;
    
    // Normalizar: tempos menores = velocidade maior
    // 1000ms = 1.0, 3000ms = 0.33
    const speed = Math.max(0, Math.min(1, (3000 - avgResponseTime) / 2000));
    
    return speed;
  }

  calculateSemanticRichness(semanticTasks) {
    if (semanticTasks.length === 0) return 0.7;
    
    // Avaliar a variedade de associações semânticas
    const uniqueAssociations = new Set();
    
    semanticTasks.forEach(task => {
      if (task.associations) {
        task.associations.forEach(assoc => uniqueAssociations.add(assoc));
      }
      if (task.semanticCategory) {
        uniqueAssociations.add(task.semanticCategory);
      }
    });
    
    // Normalizar baseado no número esperado de associações
    const expectedAssociations = Math.min(10, semanticTasks.length * 2);
    const richness = Math.min(1, uniqueAssociations.size / expectedAssociations);
    
    return richness;
  }
}
