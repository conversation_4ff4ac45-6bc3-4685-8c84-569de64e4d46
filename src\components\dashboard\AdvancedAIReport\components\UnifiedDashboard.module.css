/**
 * @file UnifiedDashboard.module.css
 * @description Estilos para Dashboard Unificado - Todos os dashboards integrados
 * @version 3.0.0
 */

/* Container principal */
.unifiedContainer {
  background: #f8fafc;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 24px;
}

/* Tabs dos Dashboards */
.dashboardTabs {
  background: linear-gradient(135deg, #667eea, #764ba2);
  padding: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.dashboardTabs::-webkit-scrollbar {
  display: none;
}

.tabsContainer {
  display: flex;
  min-width: max-content;
  padding: 12px 16px;
  gap: 8px;
}

.dashboardTab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  backdrop-filter: blur(10px);
}

.dashboardTab:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.dashboardTab.active {
  background: white;
  color: var(--tab-color, #667eea);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tabIcon {
  font-size: 16px;
}

.tabTitle {
  font-weight: 600;
}

/* Seção de Filtros */
.filtersSection {
  display: flex;
  gap: 24px;
  padding: 20px 24px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filterLabel {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  min-width: max-content;
}

.filterSelect {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filterSelect:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Conteúdo do Dashboard */
.dashboardContent {
  padding: 24px;
  background: white;
}

.contentHeader {
  margin-bottom: 24px;
}

.contentTitle {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
}

.contentIcon {
  font-size: 28px;
}

.contentDescription {
  margin: 0;
  font-size: 16px;
  color: #64748b;
}

.contentBody {
  min-height: 400px;
}

/* Overview Content */
.overviewContent {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metricCard {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metricIcon {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

.metricValue {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.metricLabel {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* Key Metrics Section */
.keyMetricsSection {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

.sectionTitle {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 8px;
}

.keyMetricsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.keyMetricItem {
  display: grid;
  grid-template-columns: 2fr 3fr auto auto;
  gap: 16px;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.metricName {
  font-weight: 600;
  color: #2d3748;
}

.metricProgress {
  position: relative;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progressBar {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.metricValueText {
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.trendIcon {
  font-size: 16px;
}

/* Behavioral Content */
.behavioralContent {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.patternsSection,
.adaptationsSection {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

.patternsList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.patternCard {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 16px;
}

.patternType {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.patternDetails {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #64748b;
}

.adaptationsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.adaptationItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.adaptationIcon {
  font-size: 16px;
  flex-shrink: 0;
}

.adaptationText {
  color: #4a5568;
  line-height: 1.5;
}

/* Games Content */
.gamesContent {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.gamesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.gameStatsCard {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

.gameStatsCard h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

.favoriteGamesList {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.favoriteGame {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f8fafc;
  border-radius: 6px;
}

.gameIcon {
  font-size: 16px;
}

.difficultyInfo,
.achievementsInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-size: 14px;
  color: #4a5568;
}

/* Therapeutic Content */
.therapeuticContent {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.goalsSection,
.interventionsSection {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

.goalsList,
.interventionsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.goalItem,
.interventionItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #48bb78;
}

.goalIcon,
.interventionIcon {
  font-size: 16px;
  flex-shrink: 0;
}

.goalText,
.interventionText {
  color: #4a5568;
  line-height: 1.5;
}

/* Progress Content */
.progressContent {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.growthSection,
.milestonesSection {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

.growthBars {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.growthBar {
  display: grid;
  grid-template-columns: 1fr 3fr auto;
  gap: 16px;
  align-items: center;
}

.skillName {
  font-weight: 600;
  color: #2d3748;
}

.growthBarContainer {
  position: relative;
  height: 12px;
  background: #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.growthBarFill {
  height: 100%;
  background: linear-gradient(90deg, #48bb78, #38a169);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.growthValue {
  font-weight: 600;
  color: #48bb78;
  font-size: 14px;
}

.milestonesList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.milestoneItem {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: 8px;
  border-left: 4px solid #ed8936;
}

.milestoneIcon {
  font-size: 24px;
  flex-shrink: 0;
}

.milestoneInfo {
  flex: 1;
}

.milestoneSkill {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.milestoneDetails {
  font-size: 14px;
  color: #64748b;
}

/* Sensory Content */
.sensoryContent {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.sensoryProfile,
.strategiesSection {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
}

.sensoryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.sensoryItem {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.sensoryName {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.sensoryLevel {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.sensoryLevel.hiperresponsivo {
  background: rgba(245, 101, 101, 0.1);
  color: #c53030;
}

.sensoryLevel.típico {
  background: rgba(72, 187, 120, 0.1);
  color: #2f855a;
}

.sensoryLevel.hiporesponsivo {
  background: rgba(237, 137, 54, 0.1);
  color: #c05621;
}

.sensoryLevel.buscasensorial {
  background: rgba(159, 122, 234, 0.1);
  color: #6b46c1;
}

.strategiesList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.strategyItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.strategyIcon {
  font-size: 16px;
  flex-shrink: 0;
}

.strategyText {
  color: #4a5568;
  line-height: 1.5;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardTabs {
    padding: 8px;
  }
  
  .tabsContainer {
    padding: 8px;
    gap: 4px;
  }
  
  .dashboardTab {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .filtersSection {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .filterGroup {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .dashboardContent {
    padding: 16px;
  }
  
  .metricsGrid {
    grid-template-columns: 1fr;
  }
  
  .keyMetricItem {
    grid-template-columns: 1fr;
    gap: 8px;
    text-align: left;
  }
  
  .gamesGrid {
    grid-template-columns: 1fr;
  }
  
  .sensoryGrid {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .unifiedContainer {
    background: #1a202c;
    color: #e2e8f0;
  }
  
  .filtersSection,
  .dashboardContent {
    background: #2d3748;
  }
  
  .metricCard,
  .gameStatsCard,
  .goalsSection,
  .interventionsSection,
  .growthSection,
  .milestonesSection,
  .sensoryProfile,
  .strategiesSection {
    background: #4a5568;
    border-color: #718096;
  }
  
  .contentTitle,
  .sectionTitle {
    color: #e2e8f0;
  }
}
