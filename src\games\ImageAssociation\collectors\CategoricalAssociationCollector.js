// ============================================================================
// CATEGORICAL ASSOCIATION COLLECTOR - ATIVIDADE 2
// Coleta e análise de dados para categorização semântica e organização conceitual
// ============================================================================

import { BaseCollector } from '../../../utils/BaseCollector.js';

export class CategoricalAssociationCollector extends BaseCollector {
  constructor() {
    super('CategoricalAssociation');
    
    this.cognitiveMetrics = {
      // Métricas específicas de categorização
      categorizationSpeed: [],
      hierarchicalThinking: [],
      semanticOrganization: [],
      conceptualFlexibility: [],
      categoryTransfers: [],
      
      // Padrões de categorização
      categoryPreferences: {},
      crossCategoryErrors: [],
      subcategoryDiscrimination: [],
      
      // Análise de agrupamento
      clusteringBehavior: [],
      categoryCoherence: [],
      conceptualBoundaries: []
    };

    this.categoryStructure = {
      animals: {
        superCategory: 'living',
        level: 'basic',
        features: ['movement', 'biological', 'organic'],
        subcategories: ['mammals', 'amphibians', 'insects', 'birds']
      },
      fruits: {
        superCategory: 'food',
        level: 'basic',
        features: ['edible', 'organic', 'sweet'],
        subcategories: ['citrus', 'tropical', 'berries', 'stone_fruits']
      },
      vehicles: {
        superCategory: 'transportation',
        level: 'basic',
        features: ['movement', 'mechanical', 'transport'],
        subcategories: ['land', 'air', 'water', 'personal']
      },
      tools: {
        superCategory: 'artifacts',
        level: 'basic',
        features: ['functional', 'mechanical', 'purposeful'],
        subcategories: ['cutting', 'measuring', 'building', 'writing']
      },
      emotions: {
        superCategory: 'psychological',
        level: 'abstract',
        features: ['mental', 'experiential', 'subjective'],
        subcategories: ['positive', 'negative', 'complex', 'basic']
      }
    };

    this.sessionData = {
      startTime: null,
      endTime: null,
      totalCategorizations: 0,
      correctCategorizations: 0,
      incorrectCategorizations: 0,
      averageCategorizationTime: 0,
      categoriesByAccuracy: {},
      crossCategoryConfusions: {},
      categoryCompletionTimes: {},
      difficultyLevel: 'beginner'
    };
  }

  // ========================================================================
  // COLETA DE DADOS DE CATEGORIZAÇÃO
  // ========================================================================

  collectCategorization(categorizationData) {
    const {
      selectedItem,
      targetCategory,
      actualCategory,
      responseTime,
      timestamp,
      categorySequence = [],
      visualCuesEnabled = true,
      categoryLabelsVisible = true
    } = categorizationData;

    const isCorrect = targetCategory === actualCategory;
    
    const categorization = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedItem: {
        id: selectedItem.id,
        image: selectedItem.image,
        actualCategory: actualCategory,
        features: this.extractItemFeatures(selectedItem)
      },
      targetCategory,
      isCorrect,
      categorySequence,
      context: {
        visualCuesEnabled,
        categoryLabelsVisible,
        difficultyLevel: this.sessionData.difficultyLevel,
        availableCategories: this.getAvailableCategories()
      }
    };

    this.interactions.push(categorization);

    // Análises cognitivas especializadas
    this.analyzeCategorizationProcessing(categorization);
    this.analyzeHierarchicalThinking(categorization);
    this.analyzeConceptualFlexibility(categorization);
    this.analyzeCategoryBoundaries(categorization);
    
    // Atualizar métricas de sessão
    this.updateSessionMetrics(categorization);

    return categorization;
  }

  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================

  analyzeCategorizationProcessing(categorization) {
    const { responseTime, isCorrect, targetCategory, selectedItem, context } = categorization;
    
    // Velocidade de categorização
    const speedMetric = {
      timestamp: categorization.timestamp,
      responseTime,
      targetCategory,
      itemComplexity: this.calculateItemComplexity(selectedItem),
      categoryComplexity: this.calculateCategoryComplexity(targetCategory),
      isCorrect,
      adjustedSpeed: this.calculateAdjustedCategorizationSpeed(responseTime, targetCategory),
      efficiency: this.calculateCategorizationEfficiency(responseTime, isCorrect, targetCategory)
    };

    this.cognitiveMetrics.categorizationSpeed.push(speedMetric);

    // Organização semântica
    const semanticMetric = {
      timestamp: categorization.timestamp,
      category: targetCategory,
      item: selectedItem.image,
      semanticDistance: this.calculateSemanticDistance(selectedItem, targetCategory),
      prototypicality: this.calculatePrototypicality(selectedItem, targetCategory),
      categorized: isCorrect,
      responseTime
    };

    this.cognitiveMetrics.semanticOrganization.push(semanticMetric);
  }

  analyzeHierarchicalThinking(categorization) {
    const { targetCategory, selectedItem, isCorrect } = categorization;
    
    const hierarchicalMetric = {
      timestamp: categorization.timestamp,
      basicLevel: targetCategory,
      superCategory: this.categoryStructure[targetCategory]?.superCategory,
      subcategory: this.identifySubcategory(selectedItem, targetCategory),
      levelAccuracy: isCorrect,
      hierarchicalDepth: this.calculateHierarchicalDepth(targetCategory),
      crossLevelConfusion: this.detectCrossLevelConfusion(categorization)
    };

    this.cognitiveMetrics.hierarchicalThinking.push(hierarchicalMetric);
  }

  analyzeConceptualFlexibility(categorization) {
    const recentCategorizations = this.interactions.slice(-5);
    
    const flexibilityMetric = {
      timestamp: categorization.timestamp,
      categorySwitch: this.detectCategorySwitch(recentCategorizations),
      adaptationSpeed: this.calculateAdaptationSpeed(recentCategorizations),
      setShiftingAbility: this.assessSetShifting(recentCategorizations),
      cognitiveRigidity: this.detectCognitiveRigidity(recentCategorizations)
    };

    this.cognitiveMetrics.conceptualFlexibility.push(flexibilityMetric);
  }

  analyzeCategoryBoundaries(categorization) {
    const { selectedItem, targetCategory, isCorrect } = categorization;
    
    if (!isCorrect) {
      const actualCategory = selectedItem.actualCategory;
      const boundaryAnalysis = {
        timestamp: categorization.timestamp,
        confusedCategories: [targetCategory, actualCategory],
        boundaryType: this.classifyBoundaryConfusion(targetCategory, actualCategory),
        featureOverlap: this.calculateFeatureOverlap(targetCategory, actualCategory),
        prototypicalDistance: this.calculatePrototypicalDistance(selectedItem, targetCategory, actualCategory)
      };

      this.cognitiveMetrics.conceptualBoundaries.push(boundaryAnalysis);
    }
  }

  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS
  // ========================================================================

  calculateItemComplexity(item) {
    // Complexidade baseada em características visuais e semânticas
    const visualComplexity = {
      '🐱': 0.3, '🐸': 0.4, '🦋': 0.6, '🐧': 0.5,
      '🍎': 0.2, '🍌': 0.3, '🍇': 0.5, '🍊': 0.3,
      '🚗': 0.4, '✈️': 0.6, '🚲': 0.3, '🚁': 0.7,
      '🔨': 0.3, '🪚': 0.5, '🔧': 0.4, '✂️': 0.4,
      '😊': 0.3, '😢': 0.4, '😡': 0.5, '😴': 0.4
    };

    return visualComplexity[item.image] || 0.5;
  }

  calculateCategoryComplexity(category) {
    const complexityMap = {
      animals: 0.4,
      fruits: 0.3,
      vehicles: 0.5,
      tools: 0.6,
      emotions: 0.8
    };

    return complexityMap[category] || 0.5;
  }

  calculateAdjustedCategorizationSpeed(responseTime, category) {
    const baselinesByCategory = {
      animals: 3500,
      fruits: 3000,
      vehicles: 4000,
      tools: 4500,
      emotions: 6000
    };

    const baseline = baselinesByCategory[category] || 4000;
    return Math.max(0, (baseline - responseTime) / baseline);
  }

  calculateCategorizationEfficiency(responseTime, isCorrect, category) {
    if (!isCorrect) return 0;
    
    const maxTimeByCategory = {
      animals: 6000,
      fruits: 5000,
      vehicles: 7000,
      tools: 8000,
      emotions: 10000
    };

    const maxTime = maxTimeByCategory[category] || 7000;
    return Math.max(0, (maxTime - responseTime) / maxTime);
  }

  calculateSemanticDistance(item, category) {
    // Distância semântica baseada em características compartilhadas
    const itemFeatures = this.extractItemFeatures(item);
    const categoryFeatures = this.categoryStructure[category]?.features || [];
    
    const overlap = itemFeatures.filter(feature => categoryFeatures.includes(feature)).length;
    const totalFeatures = new Set([...itemFeatures, ...categoryFeatures]).size;
    
    return 1 - (overlap / totalFeatures);
  }

  calculatePrototypicality(item, category) {
    // Quão típico o item é para sua categoria
    const prototypes = {
      animals: { '🐱': 0.9, '🐸': 0.7, '🦋': 0.6, '🐧': 0.8 },
      fruits: { '🍎': 0.9, '🍌': 0.8, '🍇': 0.7, '🍊': 0.8 },
      vehicles: { '🚗': 0.9, '✈️': 0.8, '🚲': 0.7, '🚁': 0.6 },
      tools: { '🔨': 0.9, '🪚': 0.7, '🔧': 0.8, '✂️': 0.6 },
      emotions: { '😊': 0.9, '😢': 0.8, '😡': 0.7, '😴': 0.6 }
    };

    return prototypes[category]?.[item.image] || 0.5;
  }

  extractItemFeatures(item) {
    const features = {
      '🐱': ['biological', 'movement', 'mammal', 'domestic'],
      '🐸': ['biological', 'movement', 'amphibian', 'water'],
      '🦋': ['biological', 'movement', 'insect', 'colorful'],
      '🐧': ['biological', 'movement', 'bird', 'arctic'],
      '🍎': ['edible', 'organic', 'round', 'sweet'],
      '🍌': ['edible', 'organic', 'curved', 'tropical'],
      '🍇': ['edible', 'organic', 'clustered', 'sweet'],
      '🍊': ['edible', 'organic', 'round', 'citrus'],
      '🚗': ['mechanical', 'transport', 'land', 'personal'],
      '✈️': ['mechanical', 'transport', 'air', 'fast'],
      '🚲': ['mechanical', 'transport', 'land', 'manual'],
      '🚁': ['mechanical', 'transport', 'air', 'vertical'],
      '🔨': ['tool', 'construction', 'impact', 'metal'],
      '🪚': ['tool', 'cutting', 'construction', 'teeth'],
      '🔧': ['tool', 'mechanical', 'turning', 'metal'],
      '✂️': ['tool', 'cutting', 'sharp', 'precise'],
      '😊': ['emotion', 'positive', 'basic', 'social'],
      '😢': ['emotion', 'negative', 'basic', 'sadness'],
      '😡': ['emotion', 'negative', 'intense', 'anger'],
      '😴': ['emotion', 'neutral', 'state', 'sleep']
    };

    return features[item.image] || [];
  }

  identifySubcategory(item, category) {
    const subcategorization = {
      animals: {
        '🐱': 'mammals',
        '🐸': 'amphibians',
        '🦋': 'insects',
        '🐧': 'birds'
      },
      fruits: {
        '🍎': 'stone_fruits',
        '🍌': 'tropical',
        '🍇': 'berries',
        '🍊': 'citrus'
      },
      vehicles: {
        '🚗': 'land',
        '✈️': 'air',
        '🚲': 'personal',
        '🚁': 'air'
      },
      tools: {
        '🔨': 'building',
        '🪚': 'cutting',
        '🔧': 'mechanical',
        '✂️': 'cutting'
      },
      emotions: {
        '😊': 'positive',
        '😢': 'negative',
        '😡': 'negative',
        '😴': 'neutral'
      }
    };

    return subcategorization[category]?.[item.image] || 'unknown';
  }

  calculateHierarchicalDepth(category) {
    const depths = {
      animals: 3, // living -> animals -> [mammals/birds/etc]
      fruits: 3,  // food -> fruits -> [citrus/tropical/etc]
      vehicles: 3, // transportation -> vehicles -> [land/air/etc]
      tools: 3,   // artifacts -> tools -> [cutting/building/etc]
      emotions: 3 // psychological -> emotions -> [positive/negative/etc]
    };

    return depths[category] || 2;
  }

  detectCrossLevelConfusion(categorization) {
    const { targetCategory, selectedItem } = categorization;
    const actualCategory = selectedItem.actualCategory;
    
    if (targetCategory === actualCategory) return 'none';
    
    const targetSuper = this.categoryStructure[targetCategory]?.superCategory;
    const actualSuper = this.categoryStructure[actualCategory]?.superCategory;
    
    if (targetSuper === actualSuper) return 'sibling_category';
    if (this.isSubcategoryOf(targetCategory, actualCategory)) return 'sub_to_super';
    if (this.isSubcategoryOf(actualCategory, targetCategory)) return 'super_to_sub';
    
    return 'distant_category';
  }

  detectCategorySwitch(recentCategorizations) {
    if (recentCategorizations.length < 2) return false;
    
    const current = recentCategorizations[recentCategorizations.length - 1];
    const previous = recentCategorizations[recentCategorizations.length - 2];
    
    return current.targetCategory !== previous.targetCategory;
  }

  calculateAdaptationSpeed(recentCategorizations) {
    const switches = [];
    
    for (let i = 1; i < recentCategorizations.length; i++) {
      const current = recentCategorizations[i];
      const previous = recentCategorizations[i - 1];
      
      if (current.targetCategory !== previous.targetCategory) {
        switches.push({
          switchTime: current.timestamp - previous.timestamp,
          responseTime: current.responseTime,
          successful: current.isCorrect
        });
      }
    }

    if (switches.length === 0) return 1.0;
    
    const averageSwitchTime = switches.reduce((sum, s) => sum + s.responseTime, 0) / switches.length;
    const successRate = switches.filter(s => s.successful).length / switches.length;
    
    // Velocidade de adaptação normalizada
    const speedScore = Math.max(0, 1 - (averageSwitchTime - 3000) / 7000);
    
    return (speedScore + successRate) / 2;
  }

  assessSetShifting(recentCategorizations) {
    const categories = recentCategorizations.map(c => c.targetCategory);
    const uniqueCategories = new Set(categories);
    
    // Capacidade de alternar entre categorias
    if (uniqueCategories.size <= 1) return 0.2;
    if (uniqueCategories.size === 2) return 0.6;
    if (uniqueCategories.size >= 3) return 1.0;
    
    return 0.5;
  }

  detectCognitiveRigidity(recentCategorizations) {
    if (recentCategorizations.length < 4) return 0;
    
    // Perseveração em categoria incorreta
    const errors = recentCategorizations.filter(c => !c.isCorrect);
    if (errors.length < 2) return 0;
    
    const repeatedErrors = errors.filter((error, index, array) => {
      if (index === 0) return false;
      return error.targetCategory === array[index - 1].targetCategory;
    });

    return repeatedErrors.length / errors.length;
  }

  classifyBoundaryConfusion(targetCategory, actualCategory) {
    const superCategories = {
      target: this.categoryStructure[targetCategory]?.superCategory,
      actual: this.categoryStructure[actualCategory]?.superCategory
    };

    if (superCategories.target === superCategories.actual) {
      return 'within_supercategory';
    }

    const featureOverlap = this.calculateFeatureOverlap(targetCategory, actualCategory);
    
    if (featureOverlap > 0.5) return 'feature_similarity';
    if (featureOverlap > 0.3) return 'partial_similarity';
    
    return 'distant_confusion';
  }

  calculateFeatureOverlap(category1, category2) {
    const features1 = this.categoryStructure[category1]?.features || [];
    const features2 = this.categoryStructure[category2]?.features || [];
    
    const overlap = features1.filter(f => features2.includes(f)).length;
    const total = new Set([...features1, ...features2]).size;
    
    return total === 0 ? 0 : overlap / total;
  }

  calculatePrototypicalDistance(item, targetCategory, actualCategory) {
    const targetPrototypicality = this.calculatePrototypicality(item, targetCategory);
    const actualPrototypicality = this.calculatePrototypicality(item, actualCategory);
    
    return Math.abs(targetPrototypicality - actualPrototypicality);
  }

  // ========================================================================
  // HELPERS E UTILITÁRIOS
  // ========================================================================

  isSubcategoryOf(category, superCategory) {
    // Implementação simplificada - expandir conforme necessário
    return false;
  }

  getAvailableCategories() {
    return Object.keys(this.categoryStructure);
  }

  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================

  updateSessionMetrics(categorization) {
    const { isCorrect, responseTime, targetCategory } = categorization;

    this.sessionData.totalCategorizations++;
    
    if (isCorrect) {
      this.sessionData.correctCategorizations++;
    } else {
      this.sessionData.incorrectCategorizations++;
      
      // Rastrear confusões entre categorias
      const actualCategory = categorization.selectedItem.actualCategory;
      const confusionKey = `${targetCategory}->${actualCategory}`;
      
      if (!this.sessionData.crossCategoryConfusions[confusionKey]) {
        this.sessionData.crossCategoryConfusions[confusionKey] = 0;
      }
      this.sessionData.crossCategoryConfusions[confusionKey]++;
    }

    // Atualizar precisão por categoria
    if (!this.sessionData.categoriesByAccuracy[targetCategory]) {
      this.sessionData.categoriesByAccuracy[targetCategory] = { total: 0, correct: 0 };
    }
    
    this.sessionData.categoriesByAccuracy[targetCategory].total++;
    if (isCorrect) {
      this.sessionData.categoriesByAccuracy[targetCategory].correct++;
    }

    // Atualizar tempos por categoria
    if (!this.sessionData.categoryCompletionTimes[targetCategory]) {
      this.sessionData.categoryCompletionTimes[targetCategory] = [];
    }
    this.sessionData.categoryCompletionTimes[targetCategory].push(responseTime);

    // Atualizar tempo médio
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageCategorizationTime = totalTime / this.interactions.length;
  }

  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================

  generateCognitiveReport() {
    return {
      categorizationPerformance: this.analyzeCategorizationPerformance(),
      hierarchicalProcessing: this.analyzeHierarchicalProcessing(),
      conceptualFlexibility: this.analyzeConceptualFlexibility(),
      categoryBoundaries: this.analyzeCategoryBoundaries(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }

  analyzeCategorizationPerformance() {
    const speedMetrics = this.cognitiveMetrics.categorizationSpeed;
    const semanticMetrics = this.cognitiveMetrics.semanticOrganization;

    return {
      overallAccuracy: this.sessionData.correctCategorizations / this.sessionData.totalCategorizations,
      averageSpeed: this.calculateAverageMetric(speedMetrics, 'adjustedSpeed'),
      averageEfficiency: this.calculateAverageMetric(speedMetrics, 'efficiency'),
      semanticOrganization: this.calculateAverageMetric(semanticMetrics, 'prototypicality'),
      categoryPerformance: this.getCategoryPerformanceBreakdown()
    };
  }

  getCategoryPerformanceBreakdown() {
    const breakdown = {};
    
    Object.keys(this.sessionData.categoriesByAccuracy).forEach(category => {
      const data = this.sessionData.categoriesByAccuracy[category];
      const times = this.sessionData.categoryCompletionTimes[category] || [];
      
      breakdown[category] = {
        accuracy: data.correct / data.total,
        attempts: data.total,
        averageTime: times.reduce((sum, time) => sum + time, 0) / times.length || 0,
        complexity: this.calculateCategoryComplexity(category)
      };
    });

    return breakdown;
  }

  generateAdaptiveRecommendations() {
    const recommendations = [];
    
    // Análise por categoria
    const categoryPerformance = this.getCategoryPerformanceBreakdown();
    
    Object.entries(categoryPerformance).forEach(([category, performance]) => {
      if (performance.accuracy < 0.6) {
        recommendations.push({
          type: 'category_support',
          recommendation: `Reforçar categoria: ${category}`,
          confidence: 0.8,
          details: {
            category,
            currentAccuracy: performance.accuracy,
            suggestedActions: ['visual_enhancement', 'feature_highlighting', 'prototype_training']
          }
        });
      }
    });

    // Análise de flexibilidade conceitual
    const flexibilityMetrics = this.cognitiveMetrics.conceptualFlexibility;
    const avgFlexibility = this.calculateAverageMetric(flexibilityMetrics, 'adaptationSpeed');
    
    if (avgFlexibility < 0.5) {
      recommendations.push({
        type: 'flexibility_training',
        recommendation: 'Exercícios de alternância categorial',
        confidence: 0.7
      });
    }

    // Análise de confusões
    const confusions = Object.entries(this.sessionData.crossCategoryConfusions)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);

    confusions.forEach(([confusion, count]) => {
      if (count >= 2) {
        recommendations.push({
          type: 'boundary_clarification',
          recommendation: `Clarificar diferenças: ${confusion}`,
          confidence: 0.6
        });
      }
    });

    return recommendations;
  }

  getActivityScore() {
    if (this.sessionData.totalCategorizations === 0) return 0;
    
    const accuracy = this.sessionData.correctCategorizations / this.sessionData.totalCategorizations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageCategorizationTime - 4000) / 8000);
    const flexibilityFactor = this.calculateFlexibilityScore();
    
    return Math.round(accuracy * speedFactor * flexibilityFactor * 1000);
  }

  calculateFlexibilityScore() {
    const uniqueCategories = new Set(this.interactions.map(i => i.targetCategory));
    const maxCategories = Object.keys(this.categoryStructure).length;
    
    return uniqueCategories.size / maxCategories;
  }
}

export default CategoricalAssociationCollector;
