// test-middleware-syntax.js
import { createLogger } from './src/utils/logger.js';
import { errorHandler } from './src/api/middleware/monitoring/errorHandler.js';
import monitorPerformance from './src/api/middleware/monitoring/performance.js';

const logger = createLogger('test-middleware');

logger.info('Testando syntax dos middlewares');
logger.info('Middleware de tratamento de erros carregado', { 
  success: typeof errorHandler === 'function'
});
logger.info('Middleware de performance carregado', {
  success: typeof monitorPerformance === 'function'
});
logger.info('Teste finalizado com sucesso!');
