/**
 * 🔍 VISUAL SEQUENCE COLLECTOR
 * Coletor especializado em sequências visuais para Padr<PERSON><PERSON> Visuais
 * Portal Betina V3
 */

export class VisualSequenceCollector {
  constructor() {
    this.sequences = [];
    this.patterns = [];
    this.temporalData = [];
  }

  async collect(sessionData) {
    try {
      const sequenceMetrics = {
        sequenceLength: sessionData.sequence?.length || 0,
        patternType: sessionData.patternType || 'unknown',
        completionTime: sessionData.completionTime || 0,
        accuracy: sessionData.accuracy || 0,
        sequenceComplexity: this.calculateSequenceComplexity(sessionData),
        visualProcessingSpeed: this.calculateVisualProcessingSpeed(sessionData),
        sequenceMemory: this.assessSequenceMemory(sessionData),
        patternRecognition: this.evaluatePatternRecognition(sessionData)
      };

      this.sequences.push(sequenceMetrics);
      return sequenceMetrics;
    } catch (error) {
      console.error('VisualSequenceCollector error:', error);
      return {
        sequenceLength: 0,
        patternType: 'error',
        completionTime: 0,
        accuracy: 0,
        sequenceComplexity: 0,
        visualProcessingSpeed: 0,
        sequenceMemory: 0,
        patternRecognition: 0
      };
    }
  }

  calculateSequenceComplexity(data) {
    const length = data.sequence?.length || 1;
    const uniqueElements = new Set(data.sequence || []).size;
    return Math.min(10, (length * uniqueElements) / 2);
  }

  calculateVisualProcessingSpeed(data) {
    const time = data.completionTime || 1000;
    const length = data.sequence?.length || 1;
    return Math.max(1, Math.min(10, (length * 1000) / time));
  }

  assessSequenceMemory(data) {
    const accuracy = data.accuracy || 0;
    const length = data.sequence?.length || 1;
    return Math.min(10, accuracy * (length / 3));
  }

  evaluatePatternRecognition(data) {
    const accuracy = data.accuracy || 0;
    const complexity = this.calculateSequenceComplexity(data);
    return Math.min(10, accuracy * (complexity / 5));
  }

  /**
   * Método padronizado de análise de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  analyze(data) {
    return this.collect(data);
  }

  getMetrics() {
    return {
      totalSequences: this.sequences.length,
      averageAccuracy: this.sequences.reduce((acc, seq) => acc + seq.accuracy, 0) / (this.sequences.length || 1),
      averageComplexity: this.sequences.reduce((acc, seq) => acc + seq.sequenceComplexity, 0) / (this.sequences.length || 1),
      averageProcessingSpeed: this.sequences.reduce((acc, seq) => acc + seq.visualProcessingSpeed, 0) / (this.sequences.length || 1)
    };
  }
}
