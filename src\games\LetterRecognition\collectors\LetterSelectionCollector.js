/**
 * 🔤 LETTER SELECTION COLLECTOR V3
 * Coletor especializado para atividade de seleção de letras
 * Portal Betina V3
 */

export class LetterSelectionCollector {
  constructor() {
    this.name = 'LetterSelectionCollector';
    this.version = '3.0.0';
    this.isActive = true;
    this.collectedData = [];
  }

  /**
   * 🎯 Coleta dados específicos da seleção de letras
   */
  async collect(data) {
    try {
      const timestamp = new Date().toISOString();
      
      const analysisData = {
        // Dados básicos
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: 'letter_selection',
        
        // Dados da interação
        targetLetter: data.targetLetter,
        selectedLetter: data.selectedLetter,
        optionsShown: data.optionsShown || [],
        isCorrect: data.selectedLetter === data.targetLetter,
        responseTime: data.responseTime || 0,
        
        // Análise de complexidade visual
        visualComplexity: this.analyzeVisualComplexity(data),
        letterSimilarity: this.analyzeLetter Similarity(data.targetLetter, data.selectedLetter),
        optionComplexity: this.analyzeOptionComplexity(data.optionsShown),
        
        // Métricas comportamentais
        hesitationTime: data.behavioralMetrics?.hesitationTime || 0,
        cursorMovements: data.behavioralMetrics?.cursorMovements || 0,
        clickAccuracy: data.behavioralMetrics?.clickAccuracy || 1.0,
        focusLoss: data.behavioralMetrics?.focusLoss || 0,
        
        // Análise cognitiva
        cognitiveLoad: this.assessCognitiveLoad(data),
        attentionLevel: this.assessAttentionLevel(data),
        processingStrategy: this.identifyProcessingStrategy(data),
        
        // Padrões de erro
        errorType: this.classifyError(data.targetLetter, data.selectedLetter),
        errorSeverity: this.assessErrorSeverity(data),
        
        // Dados educacionais
        letterKnowledge: this.assessLetterKnowledge(data),
        visualProcessing: this.assessVisualProcessing(data),
        developmentStage: this.assessDevelopmentStage(data)
      };
      
      this.collectedData.push(analysisData);
      return analysisData;
      
    } catch (error) {
      console.error('Erro no LetterSelectionCollector:', error);
      return null;
    }
  }

  /**
   * 🎨 Analisa complexidade visual da tarefa
   */
  analyzeVisualComplexity(data) {
    let complexity = 0.3; // Base
    
    const options = data.optionsShown || [];
    if (options.length > 3) complexity += 0.2;
    
    // Analisa similaridade visual entre opções
    const similarities = this.calculateOptionSimilarities(options, data.targetLetter);
    const avgSimilarity = similarities.reduce((a, b) => a + b, 0) / similarities.length;
    complexity += avgSimilarity * 0.4;
    
    return Math.min(1.0, complexity);
  }

  /**
   * 🔍 Analisa similaridade entre letras
   */
  analyzeLetterSimilarity(target, selected) {
    if (!target || !selected) return 0;
    
    // Grupos de letras visualmente similares
    const similarGroups = [
      ['b', 'd', 'p', 'q'],
      ['m', 'n', 'h', 'r'],
      ['c', 'o', 'e'],
      ['v', 'w', 'y'],
      ['i', 'l', 'j', '1'],
      ['f', 't'],
      ['u', 'n'],
      ['s', 'z'],
      ['a', 'o']
    ];
    
    const targetLower = target.toLowerCase();
    const selectedLower = selected.toLowerCase();
    
    for (const group of similarGroups) {
      if (group.includes(targetLower) && group.includes(selectedLower)) {
        return 0.8; // Alta similaridade
      }
    }
    
    return 0.1; // Baixa similaridade
  }

  /**
   * 📊 Analisa complexidade das opções
   */
  analyzeOptionComplexity(options) {
    if (!options || options.length === 0) return 0.5;
    
    let complexity = 0.2;
    
    // Mais opções = maior complexidade
    complexity += (options.length - 2) * 0.1;
    
    // Analisa distribuição de tipos de letra
    const hasUppercase = options.some(opt => opt === opt.toUpperCase());
    const hasLowercase = options.some(opt => opt === opt.toLowerCase());
    if (hasUppercase && hasLowercase) complexity += 0.2;
    
    return Math.min(1.0, complexity);
  }

  /**
   * 🧠 Avalia carga cognitiva
   */
  assessCognitiveLoad(data) {
    let load = 0.4; // Base
    
    // Tempo de resposta indica carga cognitiva
    const responseTime = data.responseTime || 0;
    if (responseTime > 3000) load += 0.2;
    if (responseTime > 5000) load += 0.2;
    if (responseTime < 1000) load -= 0.1; // Resposta muito rápida pode indicar baixa carga
    
    // Hesitação indica maior processamento
    const hesitation = data.behavioralMetrics?.hesitationTime || 0;
    if (hesitation > 1000) load += 0.2;
    if (hesitation > 2000) load += 0.2;
    
    // Movimentos do cursor indicam indecisão
    const movements = data.behavioralMetrics?.cursorMovements || 0;
    load += Math.min(0.3, movements * 0.1);
    
    return Math.min(1.0, load);
  }

  /**
   * 👁️ Avalia nível de atenção
   */
  assessAttentionLevel(data) {
    let attention = 0.7; // Base
    
    // Perda de foco diminui atenção
    const focusLoss = data.behavioralMetrics?.focusLoss || 0;
    attention -= focusLoss * 0.2;
    
    // Precisão do click indica atenção
    const clickAccuracy = data.behavioralMetrics?.clickAccuracy || 1.0;
    attention = (attention + clickAccuracy) / 2;
    
    // Tempo de resposta muito longo pode indicar distração
    const responseTime = data.responseTime || 0;
    if (responseTime > 7000) attention -= 0.2;
    
    return Math.max(0.0, Math.min(1.0, attention));
  }

  /**
   * 🎯 Identifica estratégia de processamento
   */
  identifyProcessingStrategy(data) {
    const strategies = [];
    
    const responseTime = data.responseTime || 0;
    const hesitation = data.behavioralMetrics?.hesitationTime || 0;
    const movements = data.behavioralMetrics?.cursorMovements || 0;
    
    // Estratégias baseadas em tempo
    if (responseTime < 1500) strategies.push('impulsive');
    if (responseTime > 4000) strategies.push('deliberative');
    if (hesitation > 2000) strategies.push('hesitant');
    
    // Estratégias baseadas em movimento
    if (movements === 0) strategies.push('decisive');
    if (movements > 3) strategies.push('exploratory');
    if (movements === 1) strategies.push('targeted');
    
    // Estratégia baseada em precisão
    const isCorrect = data.selectedLetter === data.targetLetter;
    if (isCorrect && responseTime < 2000) strategies.push('confident');
    if (!isCorrect && responseTime > 3000) strategies.push('struggling');
    
    return strategies.length > 0 ? strategies : ['neutral'];
  }

  /**
   * ❌ Classifica tipo de erro
   */
  classifyError(target, selected) {
    if (!target || !selected || target === selected) return 'no_error';
    
    const similarity = this.analyzeLetterSimilarity(target, selected);
    
    if (similarity > 0.7) {
      // Verifica se é reversão específica
      if (this.isLetterReversal(target, selected)) {
        return 'reversal_error';
      }
      return 'visual_similarity_error';
    }
    
    // Verifica se é erro fonético
    if (this.isPhoneticError(target, selected)) {
      return 'phonetic_error';
    }
    
    // Verifica posição no alfabeto
    if (this.isPositionalError(target, selected)) {
      return 'positional_error';
    }
    
    return 'random_error';
  }

  /**
   * 🔄 Verifica se é reversão de letra
   */
  isLetterReversal(target, selected) {
    const reversalPairs = [
      ['b', 'd'], ['d', 'b'],
      ['p', 'q'], ['q', 'p'],
      ['u', 'n'], ['n', 'u'],
      ['m', 'w'], ['w', 'm']
    ];
    
    const targetLower = target.toLowerCase();
    const selectedLower = selected.toLowerCase();
    
    return reversalPairs.some(pair => 
      pair[0] === targetLower && pair[1] === selectedLower
    );
  }

  /**
   * 🔤 Verifica se é erro fonético
   */
  isPhoneticError(target, selected) {
    const phoneticGroups = [
      ['b', 'p', 'd', 't', 'g', 'k'], // Plosivas
      ['f', 'v', 's', 'z'], // Fricativas
      ['m', 'n'], // Nasais
      ['l', 'r'], // Líquidas
      ['a', 'e', 'i', 'o', 'u'] // Vogais
    ];
    
    const targetLower = target.toLowerCase();
    const selectedLower = selected.toLowerCase();
    
    return phoneticGroups.some(group => 
      group.includes(targetLower) && group.includes(selectedLower)
    );
  }

  /**
   * 📍 Verifica se é erro posicional
   */
  isPositionalError(target, selected) {
    const targetPos = target.toLowerCase().charCodeAt(0) - 97;
    const selectedPos = selected.toLowerCase().charCodeAt(0) - 97;
    
    // Considera erro posicional se as letras estão próximas no alfabeto
    return Math.abs(targetPos - selectedPos) <= 2;
  }

  /**
   * ⚠️ Avalia severidade do erro
   */
  assessErrorSeverity(data) {
    if (data.selectedLetter === data.targetLetter) return 0;
    
    let severity = 0.5; // Base
    
    const similarity = this.analyzeLetterSimilarity(data.targetLetter, data.selectedLetter);
    
    // Erros em letras muito similares são menos severos
    if (similarity > 0.7) severity = 0.3;
    else if (similarity > 0.4) severity = 0.6;
    else severity = 0.9;
    
    // Tempo de resposta muito curto pode indicar erro por impulso
    if (data.responseTime < 1000) severity += 0.2;
    
    return Math.min(1.0, severity);
  }

  /**
   * 📚 Avalia conhecimento da letra
   */
  assessLetterKnowledge(data) {
    let knowledge = 0.5; // Base
    
    if (data.selectedLetter === data.targetLetter) {
      knowledge = 0.8;
      
      // Resposta rápida e correta indica conhecimento sólido
      if (data.responseTime < 2000) knowledge = 0.95;
      if (data.responseTime < 1000) knowledge = 1.0;
    } else {
      // Mesmo erro, mas analisa o tipo
      const errorType = this.classifyError(data.targetLetter, data.selectedLetter);
      
      if (errorType === 'visual_similarity_error') knowledge = 0.4;
      else if (errorType === 'phonetic_error') knowledge = 0.3;
      else if (errorType === 'reversal_error') knowledge = 0.2;
      else knowledge = 0.1;
    }
    
    return knowledge;
  }

  /**
   * 👀 Avalia processamento visual
   */
  assessVisualProcessing(data) {
    let processing = 0.6; // Base
    
    const responseTime = data.responseTime || 0;
    const movements = data.behavioralMetrics?.cursorMovements || 0;
    
    // Tempo de resposta indica velocidade de processamento visual
    if (responseTime < 2000) processing += 0.2;
    else if (responseTime > 4000) processing -= 0.2;
    
    // Poucos movimentos indicam processamento eficiente
    if (movements <= 1) processing += 0.2;
    else if (movements > 3) processing -= 0.2;
    
    // Precisão na seleção
    const isCorrect = data.selectedLetter === data.targetLetter;
    if (isCorrect) processing += 0.2;
    
    return Math.max(0.0, Math.min(1.0, processing));
  }

  /**
   * 📈 Avalia estágio de desenvolvimento
   */
  assessDevelopmentStage(data) {
    const knowledge = this.assessLetterKnowledge(data);
    const processing = this.assessVisualProcessing(data);
    const cognitiveLoad = this.assessCognitiveLoad(data);
    
    const overall = (knowledge + processing + (1 - cognitiveLoad)) / 3;
    
    if (overall >= 0.8) return 'advanced';
    if (overall >= 0.6) return 'intermediate';
    if (overall >= 0.4) return 'developing';
    return 'emerging';
  }

  /**
   * 🔢 Calcula similaridades entre opções
   */
  calculateOptionSimilarities(options, target) {
    if (!options || !target) return [0];
    
    return options.map(option => 
      this.analyzeLetterSimilarity(target, option)
    );
  }

  /**
   * 📊 Gera resumo da coleta
   */
  generateSummary() {
    if (this.collectedData.length === 0) return null;
    
    const totalCollections = this.collectedData.length;
    const correctAnswers = this.collectedData.filter(d => d.isCorrect).length;
    const accuracy = correctAnswers / totalCollections;
    
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / totalCollections;
    const avgCognitiveLoad = this.collectedData.reduce((sum, d) => sum + d.cognitiveLoad, 0) / totalCollections;
    const avgAttention = this.collectedData.reduce((sum, d) => sum + d.attentionLevel, 0) / totalCollections;
    
    // Análise de padrões de erro
    const errorTypes = {};
    this.collectedData.forEach(d => {
      if (d.errorType !== 'no_error') {
        errorTypes[d.errorType] = (errorTypes[d.errorType] || 0) + 1;
      }
    });
    
    return {
      collector: this.name,
      version: this.version,
      totalCollections,
      accuracy,
      averageResponseTime: avgResponseTime,
      averageCognitiveLoad: avgCognitiveLoad,
      averageAttentionLevel: avgAttention,
      errorPatterns: errorTypes,
      developmentInsights: this.analyzeDevelopmentProgress(),
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * 📈 Analisa progresso de desenvolvimento
   */
  analyzeDevelopmentProgress() {
    if (this.collectedData.length < 3) return null;
    
    const recentData = this.collectedData.slice(-5);
    const earlierData = this.collectedData.slice(0, 5);
    
    const recentAccuracy = recentData.length > 0 ? recentData.filter(d => d.isCorrect).length / recentData.length : 0;
    const earlierAccuracy = earlierData.length > 0 ? earlierData.filter(d => d.isCorrect).length / earlierData.length : 0;
    
    const improvement = recentAccuracy - earlierAccuracy;
    
    return {
      trend: improvement > 0.1 ? 'improving' : improvement < -0.1 ? 'declining' : 'stable',
      improvementRate: improvement,
      currentLevel: recentAccuracy,
      consistency: this.calculateConsistency(recentData)
    };
  }

  /**
   * 📏 Calcula consistência
   */
  calculateConsistency(data) {
    if (data.length < 2) return 1.0;
    
    const accuracyValues = data.map(d => d.isCorrect ? 1 : 0);
    const mean = accuracyValues.reduce((a, b) => a + b) / accuracyValues.length;
    const variance = accuracyValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / accuracyValues.length;
    
    return 1 - Math.sqrt(variance); // Inverte para que maior consistência = valor maior
  }

  /**
   * 💡 Gera recomendações
   */
  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    
    const recommendations = [];
    
    // Recomendações baseadas em precisão
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: 'accuracy_improvement',
        priority: 'high',
        message: 'Foque em precisão antes de velocidade. Pratique com letras isoladas.'
      });
    }
    
    // Recomendações baseadas em tempo de resposta
    if (summary.averageResponseTime > 4000) {
      recommendations.push({
        type: 'speed_improvement',
        priority: 'medium',
        message: 'Pratique reconhecimento rápido com exercícios de tempo limitado.'
      });
    }
    
    // Recomendações baseadas em padrões de erro
    const errorTypes = summary.errorPatterns;
    if (errorTypes.reversal_error > 2) {
      recommendations.push({
        type: 'reversal_training',
        priority: 'high',
        message: 'Pratique distinção entre letras espelhadas (b/d, p/q).'
      });
    }
    
    if (errorTypes.visual_similarity_error > 3) {
      recommendations.push({
        type: 'visual_discrimination',
        priority: 'medium',
        message: 'Trabalhe discriminação visual com exercícios específicos.'
      });
    }
    
    // Recomendações baseadas em carga cognitiva
    if (summary.averageCognitiveLoad > 0.7) {
      recommendations.push({
        type: 'cognitive_load',
        priority: 'medium',
        message: 'Reduza complexidade temporariamente para consolidar aprendizado.'
      });
    }
    
    return recommendations;
  }

  /**
   * 🔄 Reset do coletor
   */
  reset() {
    this.collectedData = [];
  }

  /**
   * 📥 Exporta dados coletados
   */
  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: new Date().toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
