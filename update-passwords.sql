-- Atualizar senhas dos usuários com hashes bcrypt corretos
-- Senha: admin123 para todos os usuários

-- Gerar novo hash válido para admin123
UPDATE users SET password = '$2b$12$B3SvVy8We7WxURcdco5QueNOws7fhNMBPnBnFG4eLfh9WFXAOFn2K' WHERE email = '<EMAIL>';
UPDATE users SET password = '$2b$12$B3SvVy8We7WxURcdco5QueNOws7fhNMBPnBnFG4eLfh9WFXAOFn2K' WHERE email = '<EMAIL>';
UPDATE users SET password = '$2b$12$B3SvVy8We7WxURcdco5QueNOws7fhNMBPnBnFG4eLfh9WFXAOFn2K' WHERE email = '<EMAIL>';
UPDATE users SET password = '$2b$12$B3SvVy8We7WxURcdco5QueNOws7fhNMBPnBnFG4eLfh9WFXAOFn2K' WHERE email = '<EMAIL>';

-- Verificar se as atualizações foram feitas
SELECT email, LEFT(password, 30) || '...' as password_preview FROM users ORDER BY email;
