/**
 * COLETOR ESPECIALIZADO: MEMÓRIA ASSOCIATIVA
 * 
 * Coleta dados sobre a capacidade de formar e recuperar associações
 * entre conceitos, imagens e categorias lógicas.
 * 
 * FUNCIONALIDADES:
 * - Análise de padrões de associação
 * - Mapeamento de conexões conceituais
 * - Avaliação de força associativa
 * - Detecção de interferência entre associações
 */

export class AssociativeMemoryCollector {
  constructor() {
    this.collectorId = 'associative-memory-collector'
    this.version = '2.0.0'
    this.associationBuffer = []
    this.conceptualNetwork = new Map()
    this.interferencePatterns = []
    this.strengthMeasures = []
    this.initialized = false
  }

  /**
   * COLETA PRIMÁRIA: Registrar tentativa de associação
   */
  async collectAssociationAttempt(data) {
    const timestamp = Date.now()
    
    const associationMetrics = {
      timestamp,
      sessionId: data.sessionId || 'unknown',
      phase: data.phase,
      category: data.category,
      mainConcept: data.mainItem,
      targetAssociation: data.correctAnswer,
      userChoice: data.userAnswer,
      isCorrect: data.isCorrect,
      responseTime: data.responseTime,
      difficulty: data.difficulty,
      contextualCues: this.extractContextualCues(data),
      associationStrength: this.calculateAssociationStrength(data),
      interferenceLevel: this.detectInterference(data),
      retrievalPath: this.analyzeRetrievalPath(data)
    }

    this.associationBuffer.push(associationMetrics)
    
    // Auto-análise a cada 5 tentativas
    if (this.associationBuffer.length % 5 === 0) {
      await this.performRealTimeAnalysis()
    }

    return associationMetrics
  }

  /**
   * ANÁLISE: Padrões de memória associativa
   */
  async analyzeAssociativePatterns() {
    if (this.associationBuffer.length === 0) {
      return { status: 'insufficient_data', message: 'Dados insuficientes para análise' }
    }

    const patterns = {
      timestamp: Date.now(),
      totalAssociations: this.associationBuffer.length,
      categoryDistribution: this.analyzeCategoryDistribution(),
      associationStrengthProfile: this.buildStrengthProfile(),
      conceptualClustering: this.analyzeConceptualClusters(),
      interferenceAnalysis: this.analyzeInterferencePatterns(),
      retrievalEfficiency: this.calculateRetrievalEfficiency(),
      semanticFlexibility: this.assessSemanticFlexibility()
    }

    return {
      collectorType: 'AssociativeMemory',
      analysisType: 'comprehensive_patterns',
      data: patterns,
      insights: this.generateAssociativeInsights(patterns),
      recommendations: this.generateMemoryRecommendations(patterns)
    }
  }

  /**
   * ANÁLISE AUXILIAR: Extrair pistas contextuais
   */
  extractContextualCues(data) {
    return {
      semanticCategory: data.category,
      difficultyContext: data.difficulty,
      phaseContext: data.phase,
      temporalContext: this.getTemporalContext(),
      visualComplexity: this.assessVisualComplexity(data.mainItem),
      conceptualDistance: this.calculateConceptualDistance(data.mainItem, data.correctAnswer)
    }
  }

  /**
   * ANÁLISE AUXILIAR: Calcular força da associação
   */
  calculateAssociationStrength(data) {
    const baseStrength = data.isCorrect ? 1.0 : 0.0
    const timeBonus = Math.max(0, 1 - (data.responseTime / 10000)) // Bônus por rapidez
    const difficultyWeight = this.getDifficultyWeight(data.difficulty)
    const categoryFamiliarity = this.getCategoryFamiliarity(data.category)

    return {
      rawStrength: baseStrength,
      timeAdjusted: baseStrength * (1 + timeBonus * 0.3),
      difficultyAdjusted: baseStrength * difficultyWeight,
      familiarityAdjusted: baseStrength * categoryFamiliarity,
      finalStrength: (baseStrength + timeBonus * difficultyWeight * categoryFamiliarity) / 2
    }
  }

  /**
   * ANÁLISE AUXILIAR: Detectar interferência
   */
  detectInterference(data) {
    const recentAssociations = this.associationBuffer.slice(-5)
    const semanticOverlap = this.calculateSemanticOverlap(data, recentAssociations)
    const categorySwitch = this.detectCategorySwitch(data, recentAssociations)
    const conceptualConfusion = this.assessConceptualConfusion(data)

    return {
      semanticInterference: semanticOverlap,
      categoryInterference: categorySwitch,
      conceptualInterference: conceptualConfusion,
      totalInterference: (semanticOverlap + categorySwitch + conceptualConfusion) / 3
    }
  }

  /**
   * ANÁLISE AUXILIAR: Analisar caminho de recuperação
   */
  analyzeRetrievalPath(data) {
    return {
      directAssociation: this.isDirectAssociation(data.mainItem, data.userChoice),
      mediatedPath: this.identifyMediatedPath(data.mainItem, data.userChoice),
      categoryBased: this.isCategoryBasedRetrieval(data),
      visualSimilarity: this.assessVisualSimilarity(data.mainItem, data.userChoice),
      functionalRelation: this.assessFunctionalRelation(data.mainItem, data.userChoice)
    }
  }

  /**
   * ANÁLISE COMPLEXA: Distribuição de categorias
   */
  analyzeCategoryDistribution() {
    const distribution = {}
    const performance = {}

    this.associationBuffer.forEach(attempt => {
      const category = attempt.category
      if (!distribution[category]) {
        distribution[category] = { total: 0, correct: 0 }
        performance[category] = { accuracy: 0, avgStrength: 0, avgTime: 0 }
      }

      distribution[category].total++
      if (attempt.isCorrect) distribution[category].correct++
    })

    // Calcular performance por categoria
    Object.keys(distribution).forEach(category => {
      const categoryAttempts = this.associationBuffer.filter(a => a.category === category)
      performance[category].accuracy = distribution[category].correct / distribution[category].total
      performance[category].avgStrength = this.calculateAverageStrength(categoryAttempts)
      performance[category].avgTime = this.calculateAverageTime(categoryAttempts)
    })

    return { distribution, performance }
  }

  /**
   * ANÁLISE COMPLEXA: Perfil de força associativa
   */
  buildStrengthProfile() {
    const strengths = this.associationBuffer.map(a => a.associationStrength.finalStrength)
    
    return {
      mean: this.calculateMean(strengths),
      median: this.calculateMedian(strengths),
      standardDeviation: this.calculateStandardDeviation(strengths),
      strengthDistribution: this.categorizeStrengths(strengths),
      progressionTrend: this.analyzeStrengthProgression(),
      peakPerformance: Math.max(...strengths),
      consistencyIndex: this.calculateConsistencyIndex(strengths)
    }
  }

  /**
   * ANÁLISE COMPLEXA: Clusters conceituais
   */
  analyzeConceptualClusters() {
    const clusters = new Map()
    
    this.associationBuffer.forEach(attempt => {
      const conceptPair = `${attempt.mainConcept}-${attempt.targetAssociation}`
      if (!clusters.has(conceptPair)) {
        clusters.set(conceptPair, {
          frequency: 0,
          accuracy: 0,
          avgStrength: 0,
          concepts: [attempt.mainConcept, attempt.targetAssociation],
          category: attempt.category
        })
      }
      
      const cluster = clusters.get(conceptPair)
      cluster.frequency++
      if (attempt.isCorrect) cluster.accuracy++
      cluster.avgStrength += attempt.associationStrength.finalStrength
    })

    // Normalizar dados dos clusters
    clusters.forEach((cluster, key) => {
      cluster.accuracy = cluster.accuracy / cluster.frequency
      cluster.avgStrength = cluster.avgStrength / cluster.frequency
    })

    return Array.from(clusters.entries()).map(([pair, data]) => ({
      conceptPair: pair,
      ...data
    }))
  }

  /**
   * MÉTODOS AUXILIARES DE CÁLCULO
   */
  getDifficultyWeight(difficulty) {
    const weights = { 'EASY': 0.8, 'MEDIUM': 1.0, 'HARD': 1.3 }
    return weights[difficulty] || 1.0
  }

  getCategoryFamiliarity(category) {
    const categoryCount = this.associationBuffer.filter(a => a.category === category).length
    return Math.min(1.0, 0.5 + (categoryCount / 10) * 0.5) // Máximo 1.0
  }

  calculateSemanticOverlap(current, recent) {
    if (recent.length === 0) return 0
    
    const currentConcepts = [current.mainItem, current.correctAnswer]
    let overlapScore = 0
    
    recent.forEach(attempt => {
      const recentConcepts = [attempt.mainConcept, attempt.targetAssociation]
      const overlap = currentConcepts.filter(c => recentConcepts.includes(c)).length
      overlapScore += overlap / 2 // Normalizar pelo número de conceitos
    })
    
    return overlapScore / recent.length
  }

  detectCategorySwitch(current, recent) {
    if (recent.length === 0) return 0
    
    const lastCategory = recent[recent.length - 1]?.category
    return current.category !== lastCategory ? 1 : 0
  }

  /**
   * ANÁLISE EM TEMPO REAL
   */
  async performRealTimeAnalysis() {
    const recentPerformance = this.associationBuffer.slice(-5)
    const accuracy = recentPerformance.filter(a => a.isCorrect).length / recentPerformance.length
    
    if (accuracy < 0.4) {
      console.log('🧠 Memória Associativa: Dificuldade detectada - sugerindo estratégias')
    } else if (accuracy > 0.8) {
      console.log('🧠 Memória Associativa: Excelente performance - considerar aumento de dificuldade')
    }
  }

  /**
   * INSIGHTS E RECOMENDAÇÕES
   */
  generateAssociativeInsights(patterns) {
    const insights = []
    
    if (patterns.associationStrengthProfile.mean > 0.7) {
      insights.push('Forte capacidade de formação de associações conceituais')
    }
    
    if (patterns.semanticFlexibility > 0.8) {
      insights.push('Excelente flexibilidade semântica e adaptação categorial')
    }
    
    if (patterns.interferenceAnalysis.totalInterference < 0.3) {
      insights.push('Baixa interferência - memória associativa bem organizada')
    }
    
    return insights
  }

  generateMemoryRecommendations(patterns) {
    const recommendations = []
    
    if (patterns.retrievalEfficiency < 0.6) {
      recommendations.push('Praticar técnicas de recuperação associativa guiada')
    }
    
    if (patterns.conceptualClustering.length < 3) {
      recommendations.push('Expandir repertório de associações categóricas')
    }
    
    return recommendations
  }

  /**
   * MÉTODOS AUXILIARES MATEMÁTICOS
   */
  calculateMean(values) {
    return values.reduce((sum, val) => sum + val, 0) / values.length
  }

  calculateMedian(values) {
    const sorted = [...values].sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    return sorted.length % 2 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2
  }

  calculateStandardDeviation(values) {
    const mean = this.calculateMean(values)
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    return Math.sqrt(variance)
  }

  // Métodos auxiliares adicionais simplificados
  getTemporalContext() { return 'session_active' }
  assessVisualComplexity() { return 0.5 }
  calculateConceptualDistance() { return 0.3 }
  assessConceptualConfusion() { return 0.2 }
  isDirectAssociation() { return true }
  identifyMediatedPath() { return 'direct' }
  isCategoryBasedRetrieval() { return true }
  assessVisualSimilarity() { return 0.4 }
  assessFunctionalRelation() { return 0.6 }
  calculateAverageStrength(attempts) { 
    return attempts.reduce((sum, a) => sum + a.associationStrength.finalStrength, 0) / attempts.length 
  }
  calculateAverageTime(attempts) { 
    return attempts.reduce((sum, a) => sum + a.responseTime, 0) / attempts.length 
  }
  categorizeStrengths(strengths) {
    return {
      weak: strengths.filter(s => s < 0.4).length,
      moderate: strengths.filter(s => s >= 0.4 && s < 0.7).length,
      strong: strengths.filter(s => s >= 0.7).length
    }
  }
  analyzeStrengthProgression() {
    if (this.associationBuffer.length < 6) return 0
    const first = this.associationBuffer.slice(0, 3)
    const last = this.associationBuffer.slice(-3)
    const firstAvg = this.calculateAverageStrength(first)
    const lastAvg = this.calculateAverageStrength(last)
    return (lastAvg - firstAvg) / firstAvg
  }
  calculateConsistencyIndex(strengths) {
    const std = this.calculateStandardDeviation(strengths)
    const mean = this.calculateMean(strengths)
    return mean > 0 ? 1 - (std / mean) : 0
  }
  analyzeInterferencePatterns() {
    const interferences = this.associationBuffer.map(a => a.interferenceLevel.totalInterference)
    return {
      avgInterference: this.calculateMean(interferences),
      maxInterference: Math.max(...interferences),
      interferenceSpikes: interferences.filter(i => i > 0.7).length
    }
  }
  calculateRetrievalEfficiency() {
    const correctAttempts = this.associationBuffer.filter(a => a.isCorrect)
    if (correctAttempts.length === 0) return 0
    const avgCorrectTime = this.calculateAverageTime(correctAttempts)
    const allAvgTime = this.calculateAverageTime(this.associationBuffer)
    return allAvgTime > 0 ? 1 - (avgCorrectTime / allAvgTime) : 0
  }
  assessSemanticFlexibility() {
    const categories = [...new Set(this.associationBuffer.map(a => a.category))]
    const categorySwitches = this.associationBuffer.slice(1).filter((attempt, i) => 
      attempt.category !== this.associationBuffer[i].category
    ).length
    return categories.length > 1 ? categorySwitches / (this.associationBuffer.length - 1) : 0
  }

  /**
   * MÉTODO DE RESET
   */
  reset() {
    this.associationBuffer = []
    this.conceptualNetwork.clear()
    this.interferencePatterns = []
    this.strengthMeasures = []
    this.initialized = false
  }

  /**
   * MÉTODO DE STATUS
   */
  getStatus() {
    return {
      collectorId: this.collectorId,
      version: this.version,
      isActive: this.initialized,
      dataPoints: this.associationBuffer.length,
      lastCollection: this.associationBuffer.length > 0 ? 
        this.associationBuffer[this.associationBuffer.length - 1].timestamp : null
    }
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise da memória associativa
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("AssociativeMemoryCollector: Dados do jogo não fornecidos para análise");
      return {
        associativeCapacity: 0.5,
        conceptualConnections: [],
        strengths: {},
        interferences: [],
        analysisComplete: false
      };
    }

    try {
      // Processa tentativas de associação do jogo
      if (gameData.attempts && Array.isArray(gameData.attempts)) {
        gameData.attempts.forEach(attempt => {
          this.collectAssociationAttempt({
            sessionId: gameData.sessionId,
            phase: attempt.phase || 'unknown',
            category: attempt.category || 'general',
            mainItem: attempt.mainItem || attempt.stimulus || '',
            correctAnswer: attempt.correctAnswer || attempt.target || '',
            userAnswer: attempt.userAnswer || attempt.response || '',
            isCorrect: attempt.isCorrect === true,
            responseTime: attempt.responseTime || 0
          });
        });
      }
      
      // Analisa os dados coletados
      return this.analyze(gameData);
    }
    catch (error) {
      console.error(`❌ AssociativeMemoryCollector: Erro na coleta`, error);
      return {
        associativeCapacity: 0.5,
        conceptualConnections: [],
        strengths: {},
        interferences: [],
        analysisComplete: false,
        error: error.message
      };
    }
  }
  
  /**
   * Método padronizado de análise para integração com testes e processadores
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise da memória associativa
   */
  analyze(gameData) {
    try {
      // Se não há associações suficientes para análise
      if (this.associationBuffer.length < 3) {
        return {
          associativeCapacity: 0.5,
          conceptualConnections: Array.from(this.conceptualNetwork.keys()).slice(0, 5),
          strengths: this.calculateStrengthSummary(),
          interferences: this.interferencePatterns.slice(0, 3),
          analysisComplete: false
        };
      }

      // Calcular capacidade associativa
      const correctAssociations = this.associationBuffer.filter(a => a.isCorrect).length;
      const associativeCapacity = this.associationBuffer.length > 0 ?
        correctAssociations / this.associationBuffer.length : 0.5;

      // Ajustar com base no tempo de resposta
      const averageResponseTime = this.calculateAverageResponseTime();
      const adjustedCapacity = this.adjustCapacityByResponseTime(associativeCapacity, averageResponseTime);
      
      return {
        associativeCapacity: adjustedCapacity,
        conceptualConnections: Array.from(this.conceptualNetwork.keys()).slice(0, 10),
        strengths: this.calculateStrengthSummary(),
        interferences: this.interferencePatterns.slice(0, 5),
        responseMetrics: {
          averageTime: averageResponseTime,
          consistency: this.calculateResponseConsistency()
        },
        analysisComplete: true
      };
    }
    catch (error) {
      console.error(`❌ AssociativeMemoryCollector: Erro na análise`, error);
      return {
        associativeCapacity: 0.5,
        conceptualConnections: [],
        strengths: {},
        interferences: [],
        analysisComplete: false,
        error: error.message
      };
    }
  }
  
  /**
   * Calcula tempo médio de resposta
   */
  calculateAverageResponseTime() {
    if (this.associationBuffer.length === 0) return 0;
    
    const sum = this.associationBuffer.reduce((acc, item) => acc + (item.responseTime || 0), 0);
    return sum / this.associationBuffer.length;
  }
  
  /**
   * Calcula consistência de respostas
   */
  calculateResponseConsistency() {
    // Implementação simplificada para teste
    return 0.7;
  }
  
  /**
   * Ajusta capacidade associativa com base no tempo de resposta
   */
  adjustCapacityByResponseTime(capacity, responseTime) {
    // Tempos muito lentos reduzem ligeiramente a capacidade
    if (responseTime > 5000) {
      return Math.max(0.1, capacity * 0.9);
    }
    // Tempos muito rápidos com alta precisão indicam forte capacidade
    if (responseTime < 1500 && capacity > 0.7) {
      return Math.min(1.0, capacity * 1.1);
    }
    return capacity;
  }
  
  /**
   * Calcula resumo das forças associativas
   */
  calculateStrengthSummary() {
    // Implementação simplificada para teste
    return {
      strong: this.strengthMeasures.filter(s => s.strength > 0.7).length,
      medium: this.strengthMeasures.filter(s => s.strength > 0.4 && s.strength <= 0.7).length,
      weak: this.strengthMeasures.filter(s => s.strength <= 0.4).length
    };
  }
}
