import { SystemOrchestrator } from './src/api/services/core/SystemOrchestrator.js';
import { getHealthCheckService } from './src/api/services/core/health/HealthCheckService.js';

async function testHealthChecks() {
  console.log('🏥 Testando Health Checks...');
  
  // Inicialização correta como no teste final
  const systemOrchestrator = new SystemOrchestrator({
    mode: 'production',
    enableGameSpecificProcessors: true,
    enableMultisensoryIntegration: true,
    logLevel: 'info'
  });
  
  const healthService = getHealthCheckService();
  
  // Aguardar um pouco para os componentes estabilizarem
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  const healthStatus = await healthService.getOverallHealth();
  console.log('📊 Health Status Geral:', JSON.stringify(healthStatus, null, 2));
  
  // Testar health checks específicos
  const components = ['system_orchestrator', 'ai_brain', 'intelligent_cache', 'database'];
  for (const component of components) {
    try {
      const status = await healthService.checkComponent(component);
      console.log(`✅ ${component}: ${status.status}`);
    } catch (error) {
      console.log(`❌ ${component}: ERRO - ${error.message}`);
    }
  }
  
  // Testar health check de analisadores
  const analyzers = ['behavioral_analyzer', 'cognitive_analyzer', 'therapeutic_analyzer', 'progress_analyzer'];
  for (const analyzer of analyzers) {
    try {
      const status = await healthService.checkComponent(analyzer);
      console.log(`🔬 ${analyzer}: ${status.status}`);
    } catch (error) {
      console.log(`❌ ${analyzer}: ERRO - ${error.message}`);
    }
  }
}

testHealthChecks().catch(console.error);
