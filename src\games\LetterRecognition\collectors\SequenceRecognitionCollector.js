/**
 * 📝 SEQUENCE RECOGNITION COLLECTOR V3
 * Coletor especializado para atividade de reconhecimento de sequência
 * Portal Betina V3
 */

export class SequenceRecognitionCollector {
  constructor() {
    this.name = 'SequenceRecognitionCollector';
    this.version = '3.0.0';
    this.isActive = true;
    this.collectedData = [];
  }

  async collect(data) {
    try {
      const timestamp = new Date().toISOString();
      
      const analysisData = {
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: 'sequence_recognition',
        
        // Dados da sequência
        sequencePattern: data.sequencePattern,
        missingPosition: data.missingPosition,
        selectedLetter: data.selectedLetter,
        expectedLetter: data.expectedLetter,
        isCorrect: data.selectedLetter === data.expectedLetter,
        responseTime: data.responseTime || 0,
        
        // Análise da sequência
        sequenceLength: data.sequencePattern?.length || 0,
        patternType: this.analyzePatternType(data.sequencePattern),
        patternComplexity: this.calculatePatternComplexity(data.sequencePattern),
        positionDifficulty: this.assessPositionDifficulty(data.missingPosition, data.sequencePattern?.length),
        
        // Métricas cognitivas
        patternRecognitionTime: data.behavioralMetrics?.patternRecognitionTime || data.responseTime,
        workingMemoryLoad: this.assessWorkingMemoryLoad(data),
        sequentialProcessing: this.assessSequentialProcessing(data),
        patternAnalysis: this.assessPatternAnalysis(data),
        
        // Habilidades específicas
        alphabetKnowledge: this.assessAlphabetKnowledge(data),
        sequentialMemory: this.assessSequentialMemory(data),
        logicalReasoning: this.assessLogicalReasoning(data),
        
        // Padrões de erro
        errorType: this.classifySequenceError(data),
        errorAnalysis: this.analyzeSequenceError(data)
      };
      
      this.collectedData.push(analysisData);
      return analysisData;
      
    } catch (error) {
      console.error('Erro no SequenceRecognitionCollector:', error);
      return null;
    }
  }

  analyzePatternType(sequence) {
    if (!sequence || sequence.length < 2) return 'unknown';
    
    // Converte para códigos ASCII
    const codes = sequence.map(letter => letter.charCodeAt(0));
    
    // Verifica sequência consecutiva
    let isConsecutive = true;
    for (let i = 1; i < codes.length; i++) {
      if (codes[i] - codes[i-1] !== 1) {
        isConsecutive = false;
        break;
      }
    }
    if (isConsecutive) return 'consecutive';
    
    // Verifica padrão de salto
    if (codes.length >= 3) {
      const diff1 = codes[1] - codes[0];
      const diff2 = codes[2] - codes[1];
      if (diff1 === diff2 && diff1 > 1) return 'skip_pattern';
    }
    
    // Verifica sequência reversa
    let isReverse = true;
    for (let i = 1; i < codes.length; i++) {
      if (codes[i-1] - codes[i] !== 1) {
        isReverse = false;
        break;
      }
    }
    if (isReverse) return 'reverse';
    
    return 'irregular';
  }

  calculatePatternComplexity(sequence) {
    if (!sequence) return 0.5;
    
    let complexity = 0.3; // Base
    
    // Comprimento aumenta complexidade
    complexity += Math.min(0.3, (sequence.length - 3) * 0.1);
    
    const patternType = this.analyzePatternType(sequence);
    const complexityMap = {
      'consecutive': 0.2,
      'skip_pattern': 0.4,
      'reverse': 0.5,
      'irregular': 0.8
    };
    
    complexity += complexityMap[patternType] || 0.3;
    
    return Math.min(1.0, complexity);
  }

  assessPositionDifficulty(position, sequenceLength) {
    if (!position || !sequenceLength) return 0.5;
    
    const relativePosition = position / (sequenceLength - 1);
    
    // Posições no meio são mais difíceis
    if (relativePosition > 0.2 && relativePosition < 0.8) return 0.8;
    // Início e fim são mais fáceis
    return 0.4;
  }

  assessWorkingMemoryLoad(data) {
    let load = 0.4;
    
    const sequenceLength = data.sequencePattern?.length || 3;
    load += Math.min(0.4, (sequenceLength - 3) * 0.1);
    
    const complexity = this.calculatePatternComplexity(data.sequencePattern);
    load += complexity * 0.3;
    
    return Math.min(1.0, load);
  }

  assessSequentialProcessing(data) {
    let processing = 0.6;
    
    if (data.selectedLetter === data.expectedLetter) {
      processing += 0.3;
      
      const complexity = this.calculatePatternComplexity(data.sequencePattern);
      if (complexity > 0.6) processing += 0.1; // Bônus por padrão complexo
    }
    
    const responseTime = data.responseTime || 0;
    if (responseTime < 3000) processing += 0.1;
    else if (responseTime > 6000) processing -= 0.2;
    
    return Math.max(0.0, Math.min(1.0, processing));
  }

  assessPatternAnalysis(data) {
    let analysis = 0.5;
    
    const patternType = this.analyzePatternType(data.sequencePattern);
    const isCorrect = data.selectedLetter === data.expectedLetter;
    
    if (isCorrect) {
      analysis += 0.3;
      
      // Bônus baseado na complexidade do padrão
      const complexity = this.calculatePatternComplexity(data.sequencePattern);
      analysis += complexity * 0.2;
    }
    
    return Math.min(1.0, analysis);
  }

  assessAlphabetKnowledge(data) {
    let knowledge = 0.6;
    
    if (data.selectedLetter === data.expectedLetter) {
      knowledge += 0.3;
    } else {
      // Verifica proximidade no alfabeto
      const expectedCode = data.expectedLetter?.charCodeAt(0) || 0;
      const selectedCode = data.selectedLetter?.charCodeAt(0) || 0;
      const distance = Math.abs(expectedCode - selectedCode);
      
      if (distance <= 2) knowledge += 0.1; // Próximo no alfabeto
      else if (distance > 5) knowledge -= 0.2; // Muito distante
    }
    
    return Math.max(0.0, Math.min(1.0, knowledge));
  }

  assessSequentialMemory(data) {
    let memory = 0.6;
    
    const sequenceLength = data.sequencePattern?.length || 3;
    if (sequenceLength > 4 && data.selectedLetter === data.expectedLetter) {
      memory += 0.3; // Bônus por sequência longa
    }
    
    return Math.min(1.0, memory);
  }

  assessLogicalReasoning(data) {
    let reasoning = 0.5;
    
    const patternType = this.analyzePatternType(data.sequencePattern);
    const isCorrect = data.selectedLetter === data.expectedLetter;
    
    if (isCorrect) {
      reasoning += 0.3;
      
      // Bônus para padrões que requerem mais raciocínio
      if (patternType === 'skip_pattern') reasoning += 0.2;
      else if (patternType === 'irregular') reasoning += 0.3;
    }
    
    return Math.min(1.0, reasoning);
  }

  classifySequenceError(data) {
    if (data.selectedLetter === data.expectedLetter) return 'no_error';
    
    const expectedCode = data.expectedLetter?.charCodeAt(0) || 0;
    const selectedCode = data.selectedLetter?.charCodeAt(0) || 0;
    const distance = Math.abs(expectedCode - selectedCode);
    
    if (distance === 1) return 'adjacent_letter_error';
    if (distance <= 3) return 'close_letter_error';
    if (distance > 10) return 'random_letter_error';
    
    return 'moderate_distance_error';
  }

  analyzeSequenceError(data) {
    if (data.selectedLetter === data.expectedLetter) return null;
    
    const sequence = data.sequencePattern || [];
    const position = data.missingPosition || 0;
    
    return {
      errorType: this.classifySequenceError(data),
      distanceFromCorrect: Math.abs(
        (data.expectedLetter?.charCodeAt(0) || 0) - (data.selectedLetter?.charCodeAt(0) || 0)
      ),
      positionInSequence: position,
      sequenceComplexity: this.calculatePatternComplexity(sequence),
      possibleCauses: this.identifyErrorCauses(data)
    };
  }

  identifyErrorCauses(data) {
    const causes = [];
    
    const responseTime = data.responseTime || 0;
    if (responseTime < 1500) causes.push('impulsive_response');
    if (responseTime > 8000) causes.push('processing_difficulty');
    
    const complexity = this.calculatePatternComplexity(data.sequencePattern);
    if (complexity > 0.7) causes.push('pattern_complexity');
    
    const sequenceLength = data.sequencePattern?.length || 3;
    if (sequenceLength > 5) causes.push('memory_overload');
    
    return causes;
  }

  generateSummary() {
    if (this.collectedData.length === 0) return null;
    
    const total = this.collectedData.length;
    const correct = this.collectedData.filter(d => d.isCorrect).length;
    const accuracy = correct / total;
    
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / total;
    const avgComplexity = this.collectedData.reduce((sum, d) => sum + d.patternComplexity, 0) / total;
    
    // Análise por tipo de padrão
    const patternPerformance = {};
    ['consecutive', 'skip_pattern', 'reverse', 'irregular'].forEach(type => {
      const typeData = this.collectedData.filter(d => d.patternType === type);
      if (typeData.length > 0) {
        patternPerformance[type] = {
          count: typeData.length,
          accuracy: typeData.filter(d => d.isCorrect).length / typeData.length,
          avgTime: typeData.reduce((sum, d) => sum + d.responseTime, 0) / typeData.length
        };
      }
    });
    
    return {
      collector: this.name,
      version: this.version,
      total,
      accuracy,
      avgResponseTime,
      avgComplexity,
      patternPerformance,
      recommendations: this.generateRecommendations()
    };
  }

  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    
    const recommendations = [];
    
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: 'pattern_practice',
        priority: 'high',
        message: 'Pratique reconhecimento de padrões simples antes de avançar.'
      });
    }
    
    if (summary.avgResponseTime > 6000) {
      recommendations.push({
        type: 'processing_speed',
        priority: 'medium',
        message: 'Trabalhe velocidade de reconhecimento de padrões.'
      });
    }
    
    // Recomendações específicas por tipo de padrão
    Object.keys(summary.patternPerformance).forEach(type => {
      const perf = summary.patternPerformance[type];
      if (perf.accuracy < 0.5) {
        recommendations.push({
          type: 'pattern_specific',
          priority: 'medium',
          message: `Foque em padrões do tipo ${type} para melhorar.`
        });
      }
    });
    
    return recommendations;
  }

  reset() {
    this.collectedData = [];
  }

  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: new Date().toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
