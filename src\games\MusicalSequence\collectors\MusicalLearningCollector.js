/**
 * MusicalLearningCollector - Coleta dados sobre aprendizado e progressão musical
 * Analisa padrões de aprendizado, retenção, transferência e desenvolvimento musical
 */
export class MusicalLearningCollector {
  constructor() {
    this.learningData = [];
    this.progressionData = [];
    this.retentionData = [];
    this.transferData = [];
    this.adaptationData = [];
    this.masteryData = [];
    this.debugMode = true;
  }

  /**
   * Coleta dados sobre progressão no aprendizado
   */
  collectLearningProgression(progressionData) {
    try {
      if (!progressionData || typeof progressionData !== 'object') {
        console.warn('MusicalLearningCollector: Dados de progressão inválidos');
        return null;
      }

      const {
        sessionNumber = 0,
        difficulty = '',
        currentLevel = 1,
        previousLevel = 1,
        accuracy = 0,
        speed = 0,
        consistency = 0,
        sequencesCompleted = 0,
        errorsCommitted = 0,
        improvementRate = 0,
        challengesSolved = 0,
        timestamp = Date.now()
      } = progressionData;

      const progression = {
        timestamp,
        sessionNumber,
        difficulty,
        levelProgression: this.analyzeLevelProgression(currentLevel, previousLevel),
        performanceMetrics: {
          accuracy,
          speed,
          consistency,
          overallPerformance: this.calculateOverallPerformance(accuracy, speed, consistency)
        },
        volumeMetrics: {
          sequencesCompleted,
          errorsCommitted,
          errorRate: sequencesCompleted > 0 ? errorsCommitted / sequencesCompleted : 0
        },
        learningCurve: this.analyzeLearningCurve(sessionNumber, accuracy),
        masteryIndicators: this.assessMasteryIndicators(accuracy, consistency, currentLevel),
        adaptiveBehavior: this.analyzeAdaptiveBehavior(difficulty, accuracy),
        engagementLevel: this.calculateEngagementLevel(sequencesCompleted, challengesSolved),
        improvementRate,
        plateauDetection: this.detectLearningPlateau(this.progressionData, accuracy)
      };

      this.progressionData.push(progression);

      if (this.debugMode) {
        console.log('📈 MusicalLearningCollector - Progressão coletada:', progression);
      }

      return progression;
    } catch (error) {
      console.error('Erro no MusicalLearningCollector.collectLearningProgression:', error);
      return null;
    }
  }

  /**
   * Analisa padrões de retenção do aprendizado
   */
  analyzeRetentionPatterns(retentionData) {
    try {
      if (!retentionData || typeof retentionData !== 'object') {
        console.warn('MusicalLearningCollector: Dados de retenção inválidos');
        return null;
      }

      const {
        timeSinceLastSession = 0,
        initialPerformance = 0,
        returnPerformance = 0,
        previousMastery = 0,
        currentMastery = 0,
        forgettingCurve = [],
        practiceFrequency = 0,
        difficulty = '',
        timestamp = Date.now()
      } = retentionData;

      const retention = {
        timestamp,
        timeSinceLastSession,
        retentionRate: this.calculateRetentionRate(initialPerformance, returnPerformance),
        forgettingCurve: this.analyzeForgettingCurve(forgettingCurve, timeSinceLastSession),
        masteryRetention: this.analyzeMasteryRetention(previousMastery, currentMastery),
        consolidationLevel: this.assessConsolidationLevel(timeSinceLastSession, returnPerformance),
        practiceEffect: this.analyzePracticeEffect(practiceFrequency, retentionRate),
        memoryStrength: this.calculateMemoryStrength(initialPerformance, returnPerformance, timeSinceLastSession),
        relearningSpeed: this.assessRelearningSpeed(initialPerformance, returnPerformance),
        longTermRetention: this.assessLongTermRetention(timeSinceLastSession, returnPerformance),
        difficulty,
        retentionStrategies: this.identifyRetentionStrategies(practiceFrequency, returnPerformance)
      };

      this.retentionData.push(retention);

      if (this.debugMode) {
        console.log('🧠 MusicalLearningCollector - Retenção analisada:', retention);
      }

      return retention;
    } catch (error) {
      console.error('Erro no MusicalLearningCollector.analyzeRetentionPatterns:', error);
      return null;
    }
  }

  /**
   * Coleta dados sobre transferência de aprendizado
   */
  collectTransferLearning(transferData) {
    try {
      if (!transferData || typeof transferData !== 'object') {
        console.warn('MusicalLearningCollector: Dados de transferência inválidos');
        return null;
      }

      const {
        sourceSkill = '',
        targetSkill = '',
        sourcePerformance = 0,
        targetPerformance = 0,
        transferDistance = 0, // quão diferentes são as habilidades
        trainingTime = 0,
        transferEffectiveness = 0,
        skillSimilarity = 0,
        timestamp = Date.now()
      } = transferData;

      const transfer = {
        timestamp,
        transferType: this.classifyTransferType(sourceSkill, targetSkill),
        transferEffectiveness,
        skillMapping: {
          source: sourceSkill,
          target: targetSkill,
          similarity: skillSimilarity,
          distance: transferDistance
        },
        performanceTransfer: this.analyzePerformanceTransfer(sourcePerformance, targetPerformance),
        transferMechanisms: this.identifyTransferMechanisms(sourceSkill, targetSkill),
        generalizationLevel: this.assessGeneralizationLevel(transferEffectiveness, skillSimilarity),
        nearTransfer: this.assessNearTransfer(skillSimilarity, transferEffectiveness),
        farTransfer: this.assessFarTransfer(transferDistance, transferEffectiveness),
        transferSpeed: this.calculateTransferSpeed(trainingTime, transferEffectiveness),
        metacognitiveTransfer: this.assessMetacognitiveTransfer(sourceSkill, targetSkill),
        transferInhibition: this.detectTransferInhibition(sourcePerformance, targetPerformance)
      };

      this.transferData.push(transfer);

      if (this.debugMode) {
        console.log('🔄 MusicalLearningCollector - Transferência coletada:', transfer);
      }

      return transfer;
    } catch (error) {
      console.error('Erro no MusicalLearningCollector.collectTransferLearning:', error);
      return null;
    }
  }

  /**
   * Analisa capacidade de adaptação a novos desafios
   */
  analyzeAdaptation(adaptationData) {
    try {
      if (!adaptationData || typeof adaptationData !== 'object') {
        console.warn('MusicalLearningCollector: Dados de adaptação inválidos');
        return null;
      }

      const {
        newChallenge = '',
        challengeDifficulty = 0,
        adaptationTime = 0,
        initialPerformance = 0,
        adaptedPerformance = 0,
        strategiesUsed = [],
        flexibilityScore = 0,
        innovationLevel = 0,
        timestamp = Date.now()
      } = adaptationData;

      const adaptation = {
        timestamp,
        challengeType: newChallenge,
        adaptationMetrics: {
          adaptationSpeed: this.calculateAdaptationSpeed(adaptationTime, challengeDifficulty),
          performanceGain: adaptedPerformance - initialPerformance,
          adaptationEfficiency: this.calculateAdaptationEfficiency(adaptationTime, performanceGain),
          flexibilityScore
        },
        strategicAdaptation: {
          strategiesUsed,
          strategyDiversity: strategiesUsed.length,
          innovativeStrategies: this.identifyInnovativeStrategies(strategiesUsed),
          strategyEffectiveness: this.assessStrategyEffectiveness(strategiesUsed, adaptedPerformance)
        },
        cognitiveFlexibility: this.assessCognitiveFlexibility(flexibilityScore, adaptationTime),
        problemSolving: this.analyzeeProblemSolving(newChallenge, strategiesUsed),
        innovationCapacity: innovationLevel,
        adaptationPatterns: this.identifyAdaptationPatterns(this.adaptationData),
        learningTransfer: this.assessAdaptationTransfer(newChallenge, adaptedPerformance)
      };

      this.adaptationData.push(adaptation);

      if (this.debugMode) {
        console.log('🎯 MusicalLearningCollector - Adaptação analisada:', adaptation);
      }

      return adaptation;
    } catch (error) {
      console.error('Erro no MusicalLearningCollector.analyzeAdaptation:', error);
      return null;
    }
  }

  /**
   * Avalia níveis de maestria em diferentes aspectos musicais
   */
  assessMastery(masteryData) {
    try {
      if (!masteryData || typeof masteryData !== 'object') {
        console.warn('MusicalLearningCollector: Dados de maestria inválidos');
        return null;
      }

      const {
        skill = '',
        currentLevel = 0,
        consistency = 0,
        automaticity = 0,
        flexibility = 0,
        creativity = 0,
        transferability = 0,
        complexity = 0,
        fluency = 0,
        timestamp = Date.now()
      } = masteryData;

      const mastery = {
        timestamp,
        skill,
        masteryLevel: this.calculateMasteryLevel(currentLevel, consistency, automaticity),
        masteryDimensions: {
          proficiency: currentLevel,
          consistency,
          automaticity,
          flexibility,
          creativity,
          transferability,
          complexity,
          fluency
        },
        expertiseMarkers: this.identifyExpertiseMarkers(masteryData),
        masteryProgression: this.analyzeMasteryProgression(skill, this.masteryData),
        masteryPrediction: this.predictMasteryTrajectory(skill, currentLevel, consistency),
        competencyProfile: this.createCompetencyProfile(masteryData),
        masteryGaps: this.identifyMasteryGaps(masteryData),
        developmentPriorities: this.suggestDevelopmentPriorities(masteryData),
        masteryValidation: this.validateMasteryLevel(masteryData)
      };

      this.masteryData.push(mastery);

      if (this.debugMode) {
        console.log('🏆 MusicalLearningCollector - Maestria avaliada:', mastery);
      }

      return mastery;
    } catch (error) {
      console.error('Erro no MusicalLearningCollector.assessMastery:', error);
      return null;
    }
  }

  /**
   * Analisa eficácia de diferentes estratégias de aprendizado
   */
  analyzeLearningStrategies(strategyData) {
    try {
      if (!strategyData || typeof strategyData !== 'object') {
        console.warn('MusicalLearningCollector: Dados de estratégia inválidos');
        return null;
      }

      const {
        strategy = '',
        context = '',
        effectiveness = 0,
        usageFrequency = 0,
        outcomeImprovement = 0,
        timeInvestment = 0,
        difficulty = '',
        timestamp = Date.now()
      } = strategyData;

      const strategyAnalysis = {
        timestamp,
        strategy,
        context,
        effectiveness,
        efficiency: this.calculateStrategyEfficiency(effectiveness, timeInvestment),
        applicability: this.assessStrategyApplicability(strategy, context),
        learningAcceleration: this.measureLearningAcceleration(strategy, outcomeImprovement),
        strategicFit: this.assessStrategicFit(strategy, difficulty),
        costBenefit: this.analyzeCostBenefit(timeInvestment, outcomeImprovement),
        strategicEvolution: this.analyzeStrategicEvolution(strategy, this.learningData),
        personalizedEffectiveness: this.assessPersonalizedEffectiveness(strategy, this.learningData),
        contextualOptimization: this.suggestContextualOptimizations(strategy, context)
      };

      this.learningData.push(strategyAnalysis);

      if (this.debugMode) {
        console.log('📚 MusicalLearningCollector - Estratégia analisada:', strategyAnalysis);
      }

      return strategyAnalysis;
    } catch (error) {
      console.error('Erro no MusicalLearningCollector.analyzeLearningStrategies:', error);
      return null;
    }
  }

  // Métodos auxiliares para análise de progressão
  analyzeLevelProgression(currentLevel, previousLevel) {
    const levelGain = currentLevel - previousLevel;
    const progressionRate = previousLevel > 0 ? levelGain / previousLevel : 0;
    
    return {
      levelGain,
      progressionRate,
      progressionType: this.classifyProgressionType(levelGain),
      progressionSpeed: this.assessProgressionSpeed(levelGain)
    };
  }

  calculateOverallPerformance(accuracy, speed, consistency) {
    // Weighted performance score
    return (accuracy * 0.4 + speed * 0.3 + consistency * 0.3);
  }

  analyzeLearningCurve(sessionNumber, accuracy) {
    if (this.progressionData.length < 3) {
      return { trend: 'insufficient_data', steepness: 0, plateau: false };
    }

    const recentSessions = this.progressionData.slice(-5);
    const accuracies = recentSessions.map(s => s.performanceMetrics.accuracy);
    
    const trend = this.calculateTrend(accuracies);
    const steepness = this.calculateCurveSteepness(accuracies);
    const plateau = this.detectPlateau(accuracies);

    return { trend, steepness, plateau, curveType: this.classifyCurveType(trend, steepness) };
  }

  assessMasteryIndicators(accuracy, consistency, level) {
    const masteryThreshold = 0.8;
    const consistencyThreshold = 0.85;
    
    return {
      accuracyMastery: accuracy >= masteryThreshold,
      consistencyMastery: consistency >= consistencyThreshold,
      levelMastery: level >= 5, // Simplified level-based mastery
      overallMastery: accuracy >= masteryThreshold && consistency >= consistencyThreshold,
      masteryProgress: Math.min((accuracy + consistency) / 2, 1)
    };
  }

  analyzeAdaptiveBehavior(difficulty, accuracy) {
    return {
      difficultyHandling: this.assessDifficultyHandling(difficulty, accuracy),
      adaptiveResponse: this.measureAdaptiveResponse(accuracy),
      challengeSeeking: this.assessChallengeSeeking(difficulty),
      resilienceLevel: this.calculateResilienceLevel(accuracy)
    };
  }

  calculateEngagementLevel(sequencesCompleted, challengesSolved) {
    const engagementScore = (sequencesCompleted * 0.6 + challengesSolved * 0.4);
    return Math.min(engagementScore / 10, 1); // Normalized to 0-1
  }

  detectLearningPlateau(progressionHistory, currentAccuracy) {
    if (!Array.isArray(progressionHistory) || progressionHistory.length < 5) {
      return { plateauDetected: false, plateauDuration: 0 };
    }

    const recentAccuracies = progressionHistory.slice(-5).map(p => p.performanceMetrics.accuracy);
    const variance = this.calculateVariance(recentAccuracies);
    const plateauThreshold = 0.05; // Low variance indicates plateau
    
    return {
      plateauDetected: variance < plateauThreshold,
      plateauDuration: variance < plateauThreshold ? recentAccuracies.length : 0,
      plateauLevel: variance < plateauThreshold ? currentAccuracy : 0
    };
  }

  // Métodos auxiliares para análise de retenção
  calculateRetentionRate(initialPerformance, returnPerformance) {
    if (initialPerformance === 0) return 0;
    return Math.min(returnPerformance / initialPerformance, 1);
  }

  analyzeForgettingCurve(forgettingData, timeSinceLastSession) {
    if (!Array.isArray(forgettingData) || forgettingData.length === 0) {
      return this.estimateForgettingCurve(timeSinceLastSession);
    }

    const decayRate = this.calculateDecayRate(forgettingData);
    const memoryStrength = this.calculateMemoryStrength(forgettingData);
    
    return {
      decayRate,
      memoryStrength,
      forgettingFunction: this.modelForgettingFunction(forgettingData),
      retentionPrediction: this.predictRetention(decayRate, timeSinceLastSession)
    };
  }

  analyzeMasteryRetention(previousMastery, currentMastery) {
    const masteryRetention = currentMastery / Math.max(previousMastery, 0.1);
    
    return {
      masteryRetention,
      masteryLoss: Math.max(0, previousMastery - currentMastery),
      masteryStability: this.assessMasteryStability(masteryRetention),
      reconsolidationNeeded: masteryRetention < 0.8
    };
  }

  assessConsolidationLevel(timeSinceLastSession, returnPerformance) {
    // Model memory consolidation based on time and performance retention
    const timeDecayFactor = Math.exp(-timeSinceLastSession / (7 * 24 * 60 * 60 * 1000)); // Week half-life
    const consolidationScore = returnPerformance * timeDecayFactor;
    
    return {
      consolidationScore,
      consolidationLevel: this.classifyConsolidationLevel(consolidationScore),
      reconsolidationStrength: this.assessReconsolidationStrength(consolidationScore)
    };
  }

  analyzePracticeEffect(practiceFrequency, retentionRate) {
    return {
      practiceEffect: practiceFrequency * retentionRate,
      practiceEfficiency: this.calculatePracticeEfficiency(practiceFrequency, retentionRate),
      optimalPracticeFrequency: this.suggestOptimalPracticeFrequency(retentionRate)
    };
  }

  calculateMemoryStrength(initialPerformance, returnPerformance, timeSinceLastSession) {
    const retentionRate = this.calculateRetentionRate(initialPerformance, returnPerformance);
    const timeDecay = Math.exp(-timeSinceLastSession / (24 * 60 * 60 * 1000)); // Day decay
    
    return retentionRate * timeDecay;
  }

  assessRelearningSpeed(initialPerformance, returnPerformance) {
    const performanceDifference = initialPerformance - returnPerformance;
    if (performanceDifference <= 0) return 1; // No relearning needed
    
    // Estimate relearning speed based on retention
    const retentionRate = this.calculateRetentionRate(initialPerformance, returnPerformance);
    return Math.max(0.1, retentionRate); // Higher retention = faster relearning
  }

  assessLongTermRetention(timeSinceLastSession, returnPerformance) {
    const longTermThreshold = 7 * 24 * 60 * 60 * 1000; // 1 week
    const isLongTerm = timeSinceLastSession > longTermThreshold;
    
    return {
      isLongTermRetention: isLongTerm,
      longTermPerformance: isLongTerm ? returnPerformance : null,
      retentionCategory: this.categorizePetention(timeSinceLastSession, returnPerformance)
    };
  }

  identifyRetentionStrategies(practiceFrequency, returnPerformance) {
    const strategies = [];
    
    if (practiceFrequency > 0.7) strategies.push('distributed_practice');
    if (returnPerformance > 0.8) strategies.push('deep_learning');
    if (practiceFrequency < 0.3 && returnPerformance > 0.6) strategies.push('efficient_encoding');
    
    return strategies.length > 0 ? strategies : ['basic_repetition'];
  }

  // Métodos auxiliares para análise de transferência
  classifyTransferType(sourceSkill, targetSkill) {
    const similarityScore = this.calculateSkillSimilarity(sourceSkill, targetSkill);
    
    if (similarityScore > 0.8) return 'near_transfer';
    if (similarityScore > 0.4) return 'intermediate_transfer';
    return 'far_transfer';
  }

  analyzePerformanceTransfer(sourcePerformance, targetPerformance) {
    const transferGain = targetPerformance - sourcePerformance;
    const transferEfficiency = targetPerformance / Math.max(sourcePerformance, 0.1);
    
    return {
      transferGain,
      transferEfficiency,
      transferQuality: this.assessTransferQuality(transferGain, transferEfficiency)
    };
  }

  identifyTransferMechanisms(sourceSkill, targetSkill) {
    // Simplified mechanism identification
    const mechanisms = [];
    
    if (this.sharesCognitiveDemands(sourceSkill, targetSkill)) {
      mechanisms.push('cognitive_overlap');
    }
    
    if (this.sharesMotorComponents(sourceSkill, targetSkill)) {
      mechanisms.push('motor_transfer');
    }
    
    if (this.sharesStrategicElements(sourceSkill, targetSkill)) {
      mechanisms.push('strategic_transfer');
    }
    
    return mechanisms.length > 0 ? mechanisms : ['general_transfer'];
  }

  assessGeneralizationLevel(transferEffectiveness, skillSimilarity) {
    return {
      generalizationScore: transferEffectiveness * (1 - skillSimilarity),
      generalizationLevel: this.classifyGeneralizationLevel(transferEffectiveness, skillSimilarity)
    };
  }

  // Métodos auxiliares para análise de adaptação
  calculateAdaptationSpeed(adaptationTime, challengeDifficulty) {
    // Lower time for higher difficulty indicates faster adaptation
    const normalizedTime = adaptationTime / (challengeDifficulty * 1000); // Normalize by difficulty
    return Math.max(0, 1 - normalizedTime / 10); // Invert for speed score
  }

  calculateAdaptationEfficiency(adaptationTime, performanceGain) {
    if (adaptationTime === 0) return 0;
    return performanceGain / adaptationTime;
  }

  identifyInnovativeStrategies(strategiesUsed) {
    // Simplified innovation detection
    const commonStrategies = ['repetition', 'chunking', 'practice'];
    return strategiesUsed.filter(strategy => !commonStrategies.includes(strategy));
  }

  assessStrategyEffectiveness(strategiesUsed, adaptedPerformance) {
    return {
      effectivenessScore: adaptedPerformance,
      strategyDiversity: strategiesUsed.length,
      strategicSuccess: adaptedPerformance > 0.7
    };
  }

  assessCognitiveFlexibility(flexibilityScore, adaptationTime) {
    return {
      flexibility: flexibilityScore,
      flexibilitySpeed: this.calculateFlexibilitySpeed(adaptationTime),
      flexibilityEfficiency: flexibilityScore / Math.max(adaptationTime / 1000, 1)
    };
  }

  // Métodos auxiliares para avaliação de maestria
  calculateMasteryLevel(currentLevel, consistency, automaticity) {
    // Weighted mastery calculation
    return (currentLevel * 0.4 + consistency * 0.3 + automaticity * 0.3);
  }

  identifyExpertiseMarkers(masteryData) {
    const markers = [];
    
    if (masteryData.consistency >= 0.9) markers.push('high_consistency');
    if (masteryData.automaticity >= 0.8) markers.push('automaticity');
    if (masteryData.flexibility >= 0.8) markers.push('adaptive_expertise');
    if (masteryData.creativity >= 0.7) markers.push('creative_application');
    if (masteryData.transferability >= 0.8) markers.push('transfer_ability');
    
    return markers;
  }

  analyzeMasteryProgression(skill, masteryHistory) {
    if (!Array.isArray(masteryHistory) || masteryHistory.length < 2) {
      return { progression: 'insufficient_data', rate: 0 };
    }

    const skillHistory = masteryHistory.filter(m => m.skill === skill);
    if (skillHistory.length < 2) {
      return { progression: 'new_skill', rate: 0 };
    }

    const recent = skillHistory.slice(-3);
    const older = skillHistory.slice(0, -3);
    
    const recentMastery = recent.reduce((sum, m) => sum + m.masteryLevel, 0) / recent.length;
    const olderMastery = older.length > 0 ? 
      older.reduce((sum, m) => sum + m.masteryLevel, 0) / older.length : recentMastery;
    
    const progressionRate = (recentMastery - olderMastery) / Math.max(olderMastery, 0.1);
    
    return {
      progression: this.classifyProgressionPattern(progressionRate),
      rate: progressionRate,
      trajectory: this.analyzeTrajectory(skillHistory)
    };
  }

  predictMasteryTrajectory(skill, currentLevel, consistency) {
    // Simplified trajectory prediction
    const growthRate = consistency * 0.1; // Higher consistency = better growth
    const projectedLevel = currentLevel + growthRate;
    
    return {
      projectedLevel,
      timeToMastery: this.estimateTimeToMastery(currentLevel, growthRate),
      masteryProbability: this.calculateMasteryProbability(currentLevel, consistency)
    };
  }

  createCompetencyProfile(masteryData) {
    return {
      strengths: this.identifyStrengths(masteryData),
      weaknesses: this.identifyWeaknesses(masteryData),
      balanceScore: this.calculateBalanceScore(masteryData),
      developmentAreas: this.identifyDevelopmentAreas(masteryData)
    };
  }

  // Métodos utilitários
  calculateVariance(values) {
    if (!Array.isArray(values) || values.length === 0) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    return values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
  }

  calculateTrend(values) {
    if (!Array.isArray(values) || values.length < 2) return 'stable';
    
    let increasing = 0;
    let decreasing = 0;
    
    for (let i = 1; i < values.length; i++) {
      if (values[i] > values[i-1]) increasing++;
      else if (values[i] < values[i-1]) decreasing++;
    }
    
    const total = values.length - 1;
    if (increasing / total > 0.6) return 'increasing';
    if (decreasing / total > 0.6) return 'decreasing';
    return 'stable';
  }

  calculateCurveSteepness(values) {
    if (!Array.isArray(values) || values.length < 2) return 0;
    
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    const range = lastValue - firstValue;
    const timeSpan = values.length - 1;
    
    return Math.abs(range / timeSpan);
  }

  detectPlateau(values) {
    if (!Array.isArray(values) || values.length < 3) return false;
    
    const variance = this.calculateVariance(values);
    return variance < 0.01; // Very low variance indicates plateau
  }

  classifyCurveType(trend, steepness) {
    if (trend === 'increasing' && steepness > 0.1) return 'steep_learning';
    if (trend === 'increasing' && steepness <= 0.1) return 'gradual_learning';
    if (trend === 'stable') return 'plateau';
    if (trend === 'decreasing') return 'decline';
    return 'irregular';
  }

  classifyProgressionType(levelGain) {
    if (levelGain > 2) return 'rapid_progression';
    if (levelGain > 0) return 'steady_progression';
    if (levelGain === 0) return 'stable';
    return 'regression';
  }

  assessProgressionSpeed(levelGain) {
    if (levelGain > 3) return 'very_fast';
    if (levelGain > 1) return 'fast';
    if (levelGain > 0) return 'normal';
    return 'slow';
  }

  assessDifficultyHandling(difficulty, accuracy) {
    const difficultyMap = { easy: 0.3, medium: 0.6, hard: 0.9 };
    const expectedAccuracy = 1 - difficultyMap[difficulty] || 0.5;
    
    return accuracy / expectedAccuracy;
  }

  measureAdaptiveResponse(accuracy) {
    // Simplified adaptive response measurement
    return accuracy > 0.7 ? 'adaptive' : 'struggling';
  }

  assessChallengeSeeking(difficulty) {
    const challengeMap = { easy: 0.2, medium: 0.6, hard: 1.0 };
    return challengeMap[difficulty] || 0.5;
  }

  calculateResilienceLevel(accuracy) {
    // Resilience based on maintaining performance under difficulty
    return Math.min(accuracy * 1.2, 1);
  }

  estimateForgettingCurve(timeSinceLastSession) {
    // Simplified Ebbinghaus forgetting curve estimation
    const decayRate = 0.5; // Half-life parameter
    const timeInDays = timeSinceLastSession / (24 * 60 * 60 * 1000);
    const retention = Math.exp(-decayRate * timeInDays);
    
    return { estimatedRetention: retention, curveType: 'exponential_decay' };
  }

  calculateDecayRate(forgettingData) {
    if (!Array.isArray(forgettingData) || forgettingData.length < 2) return 0.5;
    
    // Calculate decay rate from data points
    const firstPoint = forgettingData[0];
    const lastPoint = forgettingData[forgettingData.length - 1];
    
    const timeSpan = lastPoint.time - firstPoint.time;
    const performanceDecay = firstPoint.performance - lastPoint.performance;
    
    return timeSpan > 0 ? performanceDecay / timeSpan : 0.5;
  }

  calculateSkillSimilarity(skill1, skill2) {
    // Simplified skill similarity calculation
    const skillCategories = {
      'musical_sequence': ['auditory', 'memory', 'pattern'],
      'letter_recognition': ['visual', 'linguistic', 'pattern'],
      'rhythm_pattern': ['auditory', 'temporal', 'pattern'],
      'melody_recognition': ['auditory', 'musical', 'pattern']
    };
    
    const cat1 = skillCategories[skill1] || [];
    const cat2 = skillCategories[skill2] || [];
    
    const intersection = cat1.filter(c => cat2.includes(c));
    const union = [...new Set([...cat1, ...cat2])];
    
    return union.length > 0 ? intersection.length / union.length : 0;
  }

  sharesCognitiveDemands(skill1, skill2) {
    const cognitiveSkills = ['memory', 'attention', 'pattern_recognition'];
    return this.skillsShareAttributes(skill1, skill2, cognitiveSkills);
  }

  sharesMotorComponents(skill1, skill2) {
    const motorSkills = ['timing', 'coordination', 'precision'];
    return this.skillsShareAttributes(skill1, skill2, motorSkills);
  }

  sharesStrategicElements(skill1, skill2) {
    const strategicSkills = ['planning', 'sequencing', 'organization'];
    return this.skillsShareAttributes(skill1, skill2, strategicSkills);
  }

  skillsShareAttributes(skill1, skill2, attributes) {
    // Simplified attribute sharing check
    return Math.random() > 0.5; // Placeholder logic
  }

  identifyStrengths(masteryData) {
    const threshold = 0.8;
    const strengths = [];
    
    Object.keys(masteryData.masteryDimensions || {}).forEach(dimension => {
      if (masteryData.masteryDimensions[dimension] >= threshold) {
        strengths.push(dimension);
      }
    });
    
    return strengths;
  }

  identifyWeaknesses(masteryData) {
    const threshold = 0.4;
    const weaknesses = [];
    
    Object.keys(masteryData.masteryDimensions || {}).forEach(dimension => {
      if (masteryData.masteryDimensions[dimension] <= threshold) {
        weaknesses.push(dimension);
      }
    });
    
    return weaknesses;
  }

  calculateBalanceScore(masteryData) {
    const dimensions = Object.values(masteryData.masteryDimensions || {});
    if (dimensions.length === 0) return 0;
    
    const variance = this.calculateVariance(dimensions);
    return Math.max(0, 1 - variance); // Lower variance = better balance
  }

  // Métodos de relatório e análise agregada
  getLearningReport() {
    return {
      totalSessions: this.progressionData.length,
      overallProgression: this.calculateOverallProgression(),
      retentionSummary: this.summarizeRetention(),
      transferEffectiveness: this.summarizeTransfer(),
      adaptationCapacity: this.summarizeAdaptation(),
      masteryStatus: this.summarizeMastery(),
      learningTrends: this.identifyLearningTrends(),
      recommendations: this.generateLearningRecommendations()
    };
  }

  calculateOverallProgression() {
    if (this.progressionData.length === 0) return { trend: 'no_data', rate: 0 };
    
    const accuracies = this.progressionData.map(p => p.performanceMetrics.accuracy);
    const trend = this.calculateTrend(accuracies);
    const avgImprovement = this.progressionData.reduce((sum, p) => sum + p.improvementRate, 0) / this.progressionData.length;
    
    return { trend, rate: avgImprovement, overallScore: accuracies[accuracies.length - 1] || 0 };
  }

  summarizeRetention() {
    if (this.retentionData.length === 0) return { avgRetention: 0, retentionStability: 'unknown' };
    
    const avgRetention = this.retentionData.reduce((sum, r) => sum + r.retentionRate, 0) / this.retentionData.length;
    const retentionVariance = this.calculateVariance(this.retentionData.map(r => r.retentionRate));
    
    return {
      avgRetention,
      retentionStability: retentionVariance < 0.1 ? 'stable' : 'variable',
      longTermRetention: this.assessOverallLongTermRetention()
    };
  }

  summarizeTransfer() {
    if (this.transferData.length === 0) return { transferCapacity: 0, transferTypes: [] };
    
    const avgEffectiveness = this.transferData.reduce((sum, t) => sum + t.transferEffectiveness, 0) / this.transferData.length;
    const transferTypes = [...new Set(this.transferData.map(t => t.transferType))];
    
    return { transferCapacity: avgEffectiveness, transferTypes, transferCount: this.transferData.length };
  }

  summarizeAdaptation() {
    if (this.adaptationData.length === 0) return { adaptationSpeed: 0, flexibility: 0 };
    
    const avgSpeed = this.adaptationData.reduce((sum, a) => sum + a.adaptationMetrics.adaptationSpeed, 0) / this.adaptationData.length;
    const avgFlexibility = this.adaptationData.reduce((sum, a) => sum + a.cognitiveFlexibility.flexibility, 0) / this.adaptationData.length;
    
    return { adaptationSpeed: avgSpeed, flexibility: avgFlexibility, adaptationCount: this.adaptationData.length };
  }

  summarizeMastery() {
    if (this.masteryData.length === 0) return { masteryLevel: 0, masteredSkills: [] };
    
    const avgMastery = this.masteryData.reduce((sum, m) => sum + m.masteryLevel, 0) / this.masteryData.length;
    const masteredSkills = this.masteryData.filter(m => m.masteryLevel >= 0.8).map(m => m.skill);
    
    return { masteryLevel: avgMastery, masteredSkills, totalSkills: this.masteryData.length };
  }

  identifyLearningTrends() {
    return {
      progressionTrend: this.calculateOverallProgression().trend,
      retentionTrend: this.analyzeRetentionTrend(),
      adaptationTrend: this.analyzeAdaptationTrend(),
      masteryTrend: this.analyzeMasteryTrend()
    };
  }

  generateLearningRecommendations() {
    const recommendations = [];
    
    if (this.progressionData.length > 0) {
      const latestProgression = this.progressionData[this.progressionData.length - 1];
      
      if (latestProgression.plateauDetection.plateauDetected) {
        recommendations.push('Consider increasing difficulty or trying new strategies to overcome learning plateau');
      }
      
      if (latestProgression.performanceMetrics.consistency < 0.7) {
        recommendations.push('Focus on consistent practice to improve stability');
      }
    }
    
    if (this.retentionData.length > 0) {
      const avgRetention = this.retentionData.reduce((sum, r) => sum + r.retentionRate, 0) / this.retentionData.length;
      
      if (avgRetention < 0.6) {
        recommendations.push('Implement spaced repetition to improve long-term retention');
      }
    }
    
    if (this.adaptationData.length > 0) {
      const avgFlexibility = this.adaptationData.reduce((sum, a) => sum + a.cognitiveFlexibility.flexibility, 0) / this.adaptationData.length;
      
      if (avgFlexibility < 0.6) {
        recommendations.push('Practice with varied challenges to improve cognitive flexibility');
      }
    }
    
    return recommendations.length > 0 ? recommendations : ['Continue practicing regularly to maintain progress'];
  }

  // Métodos auxiliares adicionais (implementações simplificadas)
  classifyConsolidationLevel(score) {
    if (score > 0.8) return 'strong';
    if (score > 0.5) return 'moderate';
    return 'weak';
  }

  assessReconsolidationStrength(score) {
    return score;
  }

  calculatePracticeEfficiency(frequency, retention) {
    return frequency * retention;
  }

  suggestOptimalPracticeFrequency(retentionRate) {
    if (retentionRate > 0.8) return 'maintain_current';
    if (retentionRate > 0.6) return 'increase_slightly';
    return 'increase_significantly';
  }

  categorizePetention(timeSinceLastSession, performance) {
    const hours = timeSinceLastSession / (60 * 60 * 1000);
    
    if (hours < 24) return 'short_term';
    if (hours < 168) return 'medium_term'; // 1 week
    return 'long_term';
  }

  assessTransferQuality(gain, efficiency) {
    return (gain + efficiency) / 2;
  }

  assessNearTransfer(similarity, effectiveness) {
    return similarity > 0.7 ? effectiveness : effectiveness * 0.5;
  }

  assessFarTransfer(distance, effectiveness) {
    return distance > 0.7 ? effectiveness : effectiveness * 0.5;
  }

  calculateTransferSpeed(trainingTime, effectiveness) {
    return trainingTime > 0 ? effectiveness / trainingTime : 0;
  }

  assessMetacognitiveTransfer(sourceSkill, targetSkill) {
    return this.calculateSkillSimilarity(sourceSkill, targetSkill) * 0.7;
  }

  detectTransferInhibition(sourcePerformance, targetPerformance) {
    return sourcePerformance > targetPerformance ? 'possible_inhibition' : 'no_inhibition';
  }

  classifyGeneralizationLevel(effectiveness, similarity) {
    const generalizationScore = effectiveness * (1 - similarity);
    
    if (generalizationScore > 0.7) return 'high_generalization';
    if (generalizationScore > 0.4) return 'moderate_generalization';
    return 'low_generalization';
  }

  calculateFlexibilitySpeed(adaptationTime) {
    return Math.max(0, 1 - adaptationTime / 10000); // Normalized to 10 seconds
  }

  analyzeeProblemSolving(challenge, strategies) {
    return {
      challengeComplexity: this.assessChallengeComplexity(challenge),
      solutionCreativity: this.assessSolutionCreativity(strategies),
      problemSolvingEfficiency: strategies.length > 0 ? 1 / strategies.length : 0
    };
  }

  identifyAdaptationPatterns(adaptationHistory) {
    if (!Array.isArray(adaptationHistory) || adaptationHistory.length < 2) return [];
    
    return ['pattern_recognition', 'strategy_refinement']; // Simplified
  }

  assessAdaptationTransfer(challenge, performance) {
    return { transferEvidence: performance > 0.7, transferStrength: performance };
  }

  estimateTimeToMastery(currentLevel, growthRate) {
    const masteryThreshold = 0.9;
    const timeToMastery = growthRate > 0 ? (masteryThreshold - currentLevel) / growthRate : Infinity;
    return Math.max(0, timeToMastery);
  }

  calculateMasteryProbability(currentLevel, consistency) {
    return Math.min((currentLevel + consistency) / 2, 1);
  }

  identifyMasteryGaps(masteryData) {
    const threshold = 0.6;
    const gaps = [];
    
    Object.keys(masteryData.masteryDimensions || {}).forEach(dimension => {
      if (masteryData.masteryDimensions[dimension] < threshold) {
        gaps.push({
          dimension,
          gap: threshold - masteryData.masteryDimensions[dimension],
          priority: this.calculateGapPriority(dimension, masteryData.masteryDimensions[dimension])
        });
      }
    });
    
    return gaps.sort((a, b) => b.priority - a.priority);
  }

  suggestDevelopmentPriorities(masteryData) {
    const gaps = this.identifyMasteryGaps(masteryData);
    return gaps.slice(0, 3).map(gap => gap.dimension); // Top 3 priorities
  }

  validateMasteryLevel(masteryData) {
    const dimensions = Object.values(masteryData.masteryDimensions || {});
    const avgScore = dimensions.reduce((a, b) => a + b, 0) / dimensions.length;
    const calculatedMastery = this.calculateMasteryLevel(avgScore, masteryData.consistency || 0, masteryData.automaticity || 0);
    
    return {
      validated: Math.abs(calculatedMastery - masteryData.currentLevel) < 0.1,
      confidence: 1 - Math.abs(calculatedMastery - masteryData.currentLevel),
      recommendedLevel: calculatedMastery
    };
  }

  identifyDevelopmentAreas(masteryData) {
    return this.identifyWeaknesses(masteryData);
  }

  calculateStrategyEfficiency(effectiveness, timeInvestment) {
    return timeInvestment > 0 ? effectiveness / timeInvestment : 0;
  }

  assessStrategyApplicability(strategy, context) {
    // Simplified applicability assessment
    const strategyContextMap = {
      'repetition': ['basic', 'memorization'],
      'chunking': ['complex', 'sequence'],
      'elaboration': ['understanding', 'concept'],
      'practice': ['skill', 'motor']
    };
    
    const applicableContexts = strategyContextMap[strategy] || [];
    return applicableContexts.includes(context) ? 1 : 0.5;
  }

  measureLearningAcceleration(strategy, improvement) {
    return improvement; // Simplified acceleration measure
  }

  assessStrategicFit(strategy, difficulty) {
    const strategyDifficultyFit = {
      'repetition': { easy: 0.8, medium: 0.6, hard: 0.4 },
      'chunking': { easy: 0.5, medium: 0.8, hard: 0.9 },
      'elaboration': { easy: 0.6, medium: 0.8, hard: 0.7 }
    };
    
    return strategyDifficultyFit[strategy]?.[difficulty] || 0.5;
  }

  analyzeCostBenefit(timeInvestment, improvement) {
    return {
      costBenefitRatio: timeInvestment > 0 ? improvement / timeInvestment : 0,
      efficiency: this.calculateStrategyEfficiency(improvement, timeInvestment),
      recommendation: improvement > timeInvestment ? 'continue' : 'reconsider'
    };
  }

  analyzeStrategicEvolution(strategy, learningHistory) {
    // Simplified strategy evolution analysis
    return { evolutionPattern: 'stable', adaptationLevel: 0.5 };
  }

  assessPersonalizedEffectiveness(strategy, learningHistory) {
    if (!Array.isArray(learningHistory) || learningHistory.length === 0) return 0.5;
    
    const strategyUses = learningHistory.filter(l => l.strategy === strategy);
    if (strategyUses.length === 0) return 0.5;
    
    const avgEffectiveness = strategyUses.reduce((sum, s) => sum + s.effectiveness, 0) / strategyUses.length;
    return avgEffectiveness;
  }

  suggestContextualOptimizations(strategy, context) {
    return [`Optimize ${strategy} for ${context} context`];
  }

  analyzeRetentionTrend() {
    if (this.retentionData.length < 2) return 'insufficient_data';
    
    const retentionRates = this.retentionData.map(r => r.retentionRate);
    return this.calculateTrend(retentionRates);
  }

  analyzeAdaptationTrend() {
    if (this.adaptationData.length < 2) return 'insufficient_data';
    
    const adaptationSpeeds = this.adaptationData.map(a => a.adaptationMetrics.adaptationSpeed);
    return this.calculateTrend(adaptationSpeeds);
  }

  analyzeMasteryTrend() {
    if (this.masteryData.length < 2) return 'insufficient_data';
    
    const masteryLevels = this.masteryData.map(m => m.masteryLevel);
    return this.calculateTrend(masteryLevels);
  }

  assessOverallLongTermRetention() {
    const longTermRetentions = this.retentionData.filter(r => r.longTermRetention.isLongTermRetention);
    if (longTermRetentions.length === 0) return 'no_long_term_data';
    
    const avgLongTermRetention = longTermRetentions.reduce((sum, r) => sum + r.retentionRate, 0) / longTermRetentions.length;
    return avgLongTermRetention;
  }

  assessChallengeComplexity(challenge) {
    // Simplified complexity assessment
    const complexityMap = {
      'sequence_extension': 0.6,
      'tempo_change': 0.4,
      'instrument_change': 0.5,
      'pattern_variation': 0.7,
      'difficulty_increase': 0.8
    };
    
    return complexityMap[challenge] || 0.5;
  }

  assessSolutionCreativity(strategies) {
    const uniqueStrategies = new Set(strategies);
    const creativityScore = uniqueStrategies.size / Math.max(strategies.length, 1);
    return creativityScore;
  }

  calculateGapPriority(dimension, currentLevel) {
    const priorityWeights = {
      'accuracy': 0.9,
      'consistency': 0.8,
      'automaticity': 0.7,
      'flexibility': 0.6,
      'creativity': 0.5,
      'transferability': 0.6
    };
    
    const weight = priorityWeights[dimension] || 0.5;
    const gap = 1 - currentLevel;
    return weight * gap;
  }

  clearData() {
    this.learningData = [];
    this.progressionData = [];
    this.retentionData = [];
    this.transferData = [];
    this.adaptationData = [];
    this.masteryData = [];
    
    if (this.debugMode) {
      console.log('📚 MusicalLearningCollector - Dados limpos');
    }
  }
}
