/**
 * 🔍 TESTE SIMPLES DE UM ÚNICO ERRO PATTERN COLLECTOR
 * Verificação pontual de um ErrorPatternCollector específico
 */

import { ColorMatchCollectorsHub } from './src/games/ColorMatch/collectors/index.js';

async function testSingleErrorCollector() {
  console.log('🔍 TESTE SIMPLES DO ERROR PATTERN COLLECTOR');
  console.log('='.repeat(50));

  try {
    console.log('Testando ColorMatch ErrorPatternCollector...');
    
    // Instanciar o hub
    const hub = new ColorMatchCollectorsHub();
    console.log('Hub instanciado com sucesso');
    
    // Acessar coletores
    const collectors = hub.collectors || hub._collectors;
    if (!collectors) {
      console.log('❌ Propriedade collectors não encontrada');
      return;
    }
    
    console.log('Coletores disponíveis:', Object.keys(collectors).join(', '));
    
    // Encontrar o ErrorPatternCollector
    const errorCollector = collectors.errorPattern || 
                         collectors.ErrorPattern ||
                         Object.values(collectors).find(c => c.name && c.name.includes('Error'));
    
    if (!errorCollector) {
      console.log('❌ ErrorPatternCollector não encontrado');
      return;
    }
    
    console.log('✅ ErrorPatternCollector encontrado');
    console.log('Nome:', errorCollector.name);
    console.log('Descrição:', errorCollector.description);
    console.log('Versão:', errorCollector.version);
    console.log('Métodos disponíveis:', Object.getOwnPropertyNames(Object.getPrototypeOf(errorCollector)).join(', '));
    
    // Testar método collect
    if (typeof errorCollector.collect !== 'function') {
      console.log('❌ Método collect não implementado');
    } else {
      console.log('✅ Método collect encontrado, testando...');
      
      // Dados de teste
      const mockData = {
        sessionId: 'test-session-' + Date.now(),
        playerId: 'test-player',
        gameId: 'colormatch',
        timestamp: new Date().toISOString(),
        score: 85,
        colorMatches: 8,
        colorErrors: 2,
        errors: [
          { type: 'color_mismatch', timestamp: Date.now() - 5000 },
          { type: 'timeout', timestamp: Date.now() - 2000 }
        ]
      };
      
      try {
        const result = await errorCollector.collect(mockData);
        console.log('Resultado do método collect:');
        console.log(JSON.stringify(result, null, 2));
      } catch (error) {
        console.log(`❌ Erro ao executar collect: ${error.message}`);
      }
    }
    
    // Testar método analyze se estiver disponível
    if (typeof errorCollector.analyze === 'function') {
      console.log('✅ Método analyze encontrado, testando...');
      
      // Dados de teste
      const mockData = {
        sessionId: 'test-session-' + Date.now(),
        playerId: 'test-player',
        gameId: 'colormatch',
        timestamp: new Date().toISOString(),
        score: 85,
        colorMatches: 8,
        colorErrors: 2,
        errors: [
          { type: 'color_mismatch', timestamp: Date.now() - 5000 },
          { type: 'timeout', timestamp: Date.now() - 2000 }
        ]
      };
      
      try {
        const result = await errorCollector.analyze(mockData);
        console.log('Resultado do método analyze:');
        console.log(JSON.stringify(result, null, 2));
      } catch (error) {
        console.log(`❌ Erro ao executar analyze: ${error.message}`);
      }
    } else {
      console.log('⚠️ Método analyze não encontrado');
    }
    
  } catch (error) {
    console.log(`❌ Erro no teste: ${error.message}`);
    console.log(error.stack);
  }
}

// Executar teste
await testSingleErrorCollector();
