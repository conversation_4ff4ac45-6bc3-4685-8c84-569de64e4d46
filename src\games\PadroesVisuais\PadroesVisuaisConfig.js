// 🎯 CONFIGURAÇÕES V3 - SISTEMA DE 6 ATIVIDADES TERAPÊUTICAS
export const ACTIVITY_CONFIG = {
  reproducao_sequencias: {
    name: "Reprodução de Sequências",
    icon: "🔄",
    description: "Reproduza a sequência de padrões apresentada",
    minTime: 5000,
    maxTime: 30000,
    successCriteria: { accuracy: 0.8 },
    therapeuticFocus: ["memória_sequencial", "atenção_sustentada"]
  },
  completar_padroes: {
    name: "Completar Padrões",
    icon: "🧩",
    description: "Complete o padrão visual seguindo a lógica",
    minTime: 3000,
    maxTime: 25000,
    successCriteria: { accuracy: 0.75 },
    therapeuticFocus: ["raciocínio_lógico", "reconhecimento_padrões"]
  },
  construcao_padroes: {
    name: "Construção de Padrões",
    icon: "🔨",
    description: "Construa padrões seguindo as regras",
    minTime: 8000,
    maxTime: 45000,
    successCriteria: { accuracy: 0.7 },
    therapeuticFocus: ["criatividade_espacial", "planejamento"]
  },
  classificacao_visual: {
    name: "Classificação Visual",
    icon: "📊",
    description: "Agrupe elementos por características",
    minTime: 4000,
    maxTime: 20000,
    successCriteria: { accuracy: 0.85 },
    therapeuticFocus: ["flexibilidade_cognitiva", "categorização"]
  },
  transformacao_padroes: {
    name: "Transformação de Padrões",
    icon: "🔄",
    description: "Aplique transformações nos padrões",
    minTime: 6000,
    maxTime: 35000,
    successCriteria: { accuracy: 0.7 },
    therapeuticFocus: ["visualização_espacial", "raciocínio_abstrato"]
  },
  deteccao_anomalias: {
    name: "Detecção de Anomalias",
    icon: "🔍",
    description: "Encontre elementos que não seguem o padrão",
    minTime: 3000,
    maxTime: 15000,
    successCriteria: { accuracy: 0.9 },
    therapeuticFocus: ["atenção_detalhes", "controle_inibitório"]
  }
};

export const VISUAL_ELEMENTS = {
  shapes: ['circle', 'square', 'triangle', 'diamond', 'hexagon', 'star'],
  colors: ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'cyan', 'magenta'],
  sizes: ['small', 'medium', 'large'],
  patterns: ['solid', 'striped', 'dotted', 'checkered'],
  orientations: [0, 45, 90, 135, 180, 225, 270, 315]
};

export const ACTIVITY_DIFFICULTY_CONFIG = {
  easy: {
    reproducao_sequencias: { sequenceLength: 3, elements: 4, timeLimit: 25000 },
    completar_padroes: { patternLength: 4, options: 3, timeLimit: 20000 },
    construcao_padroes: { gridSize: '3x3', rules: 2, timeLimit: 40000 },
    classificacao_visual: { categories: 2, elements: 6, timeLimit: 18000 },
    transformacao_padroes: { transformations: 1, elements: 3, timeLimit: 30000 },
    deteccao_anomalias: { gridSize: '4x3', anomalies: 1, timeLimit: 12000 }
  },
  medium: {
    reproducao_sequencias: { sequenceLength: 5, elements: 6, timeLimit: 30000 },
    completar_padroes: { patternLength: 6, options: 4, timeLimit: 22000 },
    construcao_padroes: { gridSize: '4x4', rules: 3, timeLimit: 42000 },
    classificacao_visual: { categories: 3, elements: 9, timeLimit: 20000 },
    transformacao_padroes: { transformations: 2, elements: 4, timeLimit: 33000 },
    deteccao_anomalias: { gridSize: '5x4', anomalies: 2, timeLimit: 14000 }
  },
  hard: {
    reproducao_sequencias: { sequenceLength: 7, elements: 8, timeLimit: 35000 },
    completar_padroes: { patternLength: 8, options: 5, timeLimit: 25000 },
    construcao_padroes: { gridSize: '5x5', rules: 4, timeLimit: 45000 },
    classificacao_visual: { categories: 4, elements: 12, timeLimit: 22000 },
    transformacao_padroes: { transformations: 3, elements: 6, timeLimit: 35000 },
    deteccao_anomalias: { gridSize: '6x5', anomalies: 3, timeLimit: 15000 }
  }
};

export const PadroesVisuaisConfig = {
  // Formas disponíveis no jogo (mantido para compatibilidade)
  shapes: [
    { id: 'star', name: 'Estrela', emoji: '⭐', color: '#FFD93D' },
    { id: 'circle', name: 'Círculo', emoji: '🟢', color: '#4ECDC4' },
    { id: 'triangle', name: 'Triângulo', emoji: '🔺', color: '#45B7D1' },
    { id: 'square', name: 'Quadrado', emoji: '🟥', color: '#FF6B6B' },
    { id: 'diamond', name: 'Diamante', emoji: '🔷', color: '#A8E6CF' },
    { id: 'heart', name: 'Coração', emoji: '❤️', color: '#FF8B94' }
  ],
  // Níveis de dificuldade
  difficulties: [
    { id: 'easy', name: 'Fácil', sequenceLength: 3, maxLevel: 5, showTime: 9000 },
    { id: 'medium', name: 'Médio', sequenceLength: 4, maxLevel: 7, showTime: 8000 },
    { id: 'hard', name: 'Difícil', sequenceLength: 5, maxLevel: 10, showTime: 7000 }
  ],

  // Mensagens de encorajamento
  encouragingMessages: [
    'Muito bem! Você tem um ótimo olho para padrões! 👀',
    'Excelente! Continue assim! 🎉',
    'Perfeito! Você está indo muito bem! ✨',
    'Fantástico! Sua memória está ótima! 🧠',
    'Incrível! Você domina os padrões visuais! 🌟'
  ],

  // Configurações do jogo
  gameSettings: {
    basePoints: 10,
    maxSequenceLength: 8,
    shapePlayDuration: 600,
    sequenceDelay: 800,
    feedbackDuration: 3000,
    errorFeedbackDuration: 2000,
    levelProgressionRate: 3 // a cada 3 níveis, aumenta sequência
  },

  // Informações do jogo
  gameInfo: {
    title: 'Padrões Visuais',
    description: 'Desenvolva sua memória visual e sequencial',
    icon: '🔷',
    category: 'memory',
    ageRange: '4-12',
    skills: ['memória visual', 'sequenciação', 'padrões', 'concentração']
  }
}
