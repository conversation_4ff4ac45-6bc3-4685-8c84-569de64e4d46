{"name": "portal-betina-v3", "version": "3.0.0", "type": "module", "description": "Sistema terapêutico especializado para neurodivergência (autismo) - Portal Betina V3", "main": "src/api/server.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "nodemon src/api/server.js", "dev:frontend": "vite", "start": "node src/api/server.js", "build": "vite build --mode production", "build:analyze": "npm run build && npm run analyze", "build:clean": "rm -rf dist && npm run build", "build:optimized": "npm run clean:cache && npm run build:clean", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --config .eslintrc.cjs", "lint:fix": "eslint src/ --fix --config .eslintrc.cjs", "logs": "tail -f logs/app.log", "logs:therapeutic": "tail -f logs/therapeutic.log", "logs:error": "tail -f logs/error.log", "validate-ai": "node validate-ai-config.mjs", "setup-ai": "node setup-ai.mjs", "clean:cache": "npm cache clean --force", "clean:dist": "rm -rf dist", "clean:modules": "rm -rf node_modules && npm install", "analyze": "npx vite-bundle-analyzer dist"}, "keywords": ["autism", "neurodivergence", "therapeutic", "deterministic-algorithms", "multisensory", "games", "therapy", "dashboard", "metrics"], "author": "Portal Betina V3 Team", "license": "MIT", "dependencies": {"@tanstack/react-query": "^5.80.7", "@vitejs/plugin-react": "^4.5.2", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "chart.js": "^4.5.0", "compression": "^1.7.4", "concurrently": "^9.1.2", "cors": "^2.8.5", "csurf": "^1.11.0", "date-fns": "^4.1.0", "dotenv": "^16.6.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.1", "framer-motion": "^12.18.1", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "node-cache": "^5.1.2", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "prop-types": "^15.8.1", "rate-limit-redis": "^4.2.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.6.2", "redis": "^4.6.0", "sanitize-html": "^2.17.0", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "styled-components": "^6.1.19", "uuid": "^9.0.1", "validator": "^13.15.15", "vite": "^6.3.5", "winston": "^3.17.0", "xss": "^1.0.15", "zod": "^3.22.4"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "babel-jest": "^30.0.1", "eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "node-fetch": "^3.3.2", "nodemon": "^3.0.2", "prettier": "^3.1.0", "rollup-plugin-visualizer": "^6.0.3", "supertest": "^6.3.3", "terser": "^5.43.1"}, "engines": {"node": ">=16.0.0", "npm": ">=7.0.0"}, "repository": {"type": "git", "url": "https://github.com/portal-betina/v3-api"}, "bugs": {"url": "https://github.com/portal-betina/v3-api/issues"}, "homepage": "https://portal-betina.com"}