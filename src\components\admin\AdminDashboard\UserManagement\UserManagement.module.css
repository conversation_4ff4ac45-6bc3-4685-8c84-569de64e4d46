/**
 * 🎨 USER MANAGEMENT V3 - UI/UX MODERNO
 * @file UserManagement.module.css
 * @description Estilos modernos para Gerenciamento de Usuários
 * @version 3.0.0
 * @features Glassmorphism, Dark Mode, Animations
 */

/* ===== VARIÁVEIS LOCAIS ===== */
:root {
  --user-primary: #6366f1;
  --user-success: #10b981;
  --user-warning: #f59e0b;
  --user-error: #ef4444;
  --user-info: #3b82f6;

  --user-bg-primary: #0f172a;
  --user-bg-secondary: #1e293b;
  --user-bg-tertiary: #334155;
  --user-bg-glass: rgba(255, 255, 255, 0.1);

  --user-text-primary: #f8fafc;
  --user-text-secondary: #cbd5e1;
  --user-text-muted: #94a3b8;

  --user-border-radius: 16px;
  --user-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== CONTAINER PRINCIPAL ===== */
.userManagement {
  padding: 0;
  max-width: 100%;
  margin: 0;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HEADER MODERNO ===== */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 24px;
  padding: 24px 32px;
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--user-border-radius);
  box-shadow: var(--user-shadow);
}

.title {
  color: var(--user-text-primary);
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title::before {
  content: '👥';
  font-size: 28px;
}

/* ===== CONTROLES MODERNOS ===== */
.controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.searchInput {
  padding: 12px 20px;
  background: var(--user-bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 14px;
  color: var(--user-text-primary);
  min-width: 280px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
}

.searchInput::placeholder {
  color: var(--user-text-muted);
}

.searchInput:focus {
  outline: none;
  border-color: var(--user-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

.filterSelect {
  padding: 12px 20px;
  background: var(--user-bg-glass);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  font-size: 14px;
  color: var(--user-text-primary);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
}

.filterSelect:focus {
  outline: none;
  border-color: var(--user-primary);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filterSelect option {
  background: var(--user-bg-secondary);
  color: var(--user-text-primary);
  padding: 8px;
}

/* ===== CARDS DE ESTATÍSTICAS MODERNOS ===== */
.statsCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  padding: 0 32px;
}

.statCard {
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--user-text-primary);
  padding: 32px 24px;
  border-radius: var(--user-border-radius);
  text-align: center;
  box-shadow: var(--user-shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--user-primary), var(--user-info));
}

.statCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.statValue {
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--user-primary), var(--user-info));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.statLabel {
  font-size: 14px;
  color: var(--user-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== TABELA MODERNA ===== */
.usersTable {
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--user-border-radius);
  box-shadow: var(--user-shadow);
  overflow: hidden;
  margin: 0 32px;
}

.tableHeader {
  background: rgba(255, 255, 255, 0.05);
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  font-size: 14px;
  color: var(--user-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 24px;
  align-items: center;
}

.userRow {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
  gap: 24px;
  align-items: center;
  transition: all 0.3s ease;
  color: var(--user-text-primary);
}

.userRow:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(8px);
}

.userRow:last-child {
  border-bottom: none;
}

/* ===== INFORMAÇÕES DO USUÁRIO ===== */
.userInfo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.userAvatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--user-primary), var(--user-info));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
}

.userAvatar:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
}

.userDetails {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.userName {
  font-weight: 600;
  color: var(--user-text-primary);
  font-size: 16px;
}

.userEmail {
  font-size: 14px;
  color: var(--user-text-muted);
}

/* ===== BADGES DE STATUS MODERNOS ===== */
.statusBadge {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.statusBadge::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.statusActive {
  background: rgba(16, 185, 129, 0.2);
  color: var(--user-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.statusActive::before {
  background: var(--user-success);
}

.statusInactive {
  background: rgba(239, 68, 68, 0.2);
  color: var(--user-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.statusInactive::before {
  background: var(--user-error);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* ===== BOTÕES DE AÇÃO MODERNOS ===== */
.actionButtons {
  display: flex;
  gap: 8px;
}

.actionButton {
  padding: 10px 12px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.actionButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.actionButton:hover::before {
  left: 100%;
}

.viewButton {
  background: rgba(59, 130, 246, 0.2);
  color: var(--user-info);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.viewButton:hover {
  background: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.editButton {
  background: rgba(16, 185, 129, 0.2);
  color: var(--user-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.editButton:hover {
  background: rgba(16, 185, 129, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.deleteButton {
  background: rgba(239, 68, 68, 0.2);
  color: var(--user-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.deleteButton:hover {
  background: rgba(239, 68, 68, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* ===== ESTADOS ESPECIAIS ===== */
.noUsers {
  text-align: center;
  padding: 80px 32px;
  color: var(--user-text-muted);
  font-size: 18px;
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--user-border-radius);
  margin: 0 32px;
}

.noUsers::before {
  content: '👤';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.loading {
  text-align: center;
  padding: 80px 32px;
  color: var(--user-text-secondary);
  background: var(--user-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--user-border-radius);
  margin: 0 32px;
}

.spinner {
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top: 4px solid var(--user-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVIDADE MODERNA ===== */
@media (max-width: 1024px) {
  .statsCards {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    padding: 0 24px;
  }

  .header,
  .usersTable {
    margin: 0 24px;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
    padding: 20px 24px;
    margin: 0 16px;
  }

  .controls {
    flex-direction: column;
    gap: 12px;
  }

  .searchInput,
  .filterSelect {
    min-width: 100%;
  }

  .statsCards {
    grid-template-columns: 1fr;
    padding: 0 16px;
    gap: 16px;
  }

  .usersTable {
    margin: 0 16px;
  }

  .tableHeader {
    display: none; /* Ocultar header em mobile */
  }

  .userRow {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    margin-bottom: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .userRow:hover {
    transform: none;
    background: rgba(255, 255, 255, 0.05);
  }

  .userInfo {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 16px;
    margin-bottom: 16px;
  }

  .actionButtons {
    justify-content: center;
    gap: 12px;
  }

  .actionButton {
    flex: 1;
    max-width: 120px;
  }
}

@media (max-width: 480px) {
  .header {
    margin: 0 8px;
    padding: 16px 20px;
  }

  .statsCards,
  .usersTable {
    margin: 0 8px;
  }

  .title {
    font-size: 20px;
  }

  .statCard {
    padding: 24px 20px;
  }

  .statValue {
    font-size: 28px;
  }

  .userRow {
    padding: 20px;
  }

  .userAvatar {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
}
