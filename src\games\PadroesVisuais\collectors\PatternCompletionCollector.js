/**
 * @file PatternCompletionCollector.js
 * @description Coletor especializado para análise de completamento de padrões
 * @version 3.0.0
 */

export class PatternCompletionCollector {
  constructor() {
    this.name = 'PatternCompletionCollector';
    this.version = '3.0.0';
    this.description = 'Analisa habilidades de identificação e completamento de padrões visuais';
  }

  async collect(gameState) {
    try {
      const pattern = gameState.incompletePattern || [];
      const playerCompletion = gameState.playerCompletion || [];
      const correctCompletion = gameState.correctCompletion || [];

      return {
        // Métricas de reconhecimento de padrões
        patternRecognitionAccuracy: this.calculatePatternAccuracy(correctCompletion, playerCompletion),
        logicalReasoningScore: this.assessLogicalReasoning(pattern, correctCompletion, playerCompletion),
        patternComplexityHandled: this.evaluateComplexityHandling(pattern),
        completionEfficiency: this.calculateCompletionEfficiency(gameState.responseTime, pattern.length),
        
        // Análise de estratégias
        completionStrategy: this.analyzeCompletionStrategy(pattern, playerCompletion),
        strategicConsistency: this.assessStrategicConsistency(gameState.sessionAttempts || []),
        
        // Habilidades cognitivas
        spatialReasoningIndex: this.assessSpatialReasoning(pattern, playerCompletion),
        abstractThinkingScore: this.evaluateAbstractThinking(pattern, gameState.difficulty),
        problemSolvingApproach: this.analyzeProblemSolvingApproach(gameState),
        
        // Métricas de aprendizagem
        adaptationRate: this.calculateAdaptationRate(gameState.sessionAttempts || []),
        learningCurveSlope: this.assessLearningCurve(gameState.sessionAttempts || []),
        
        timestamp: Date.now(),
        sessionId: gameState.sessionId || 'unknown'
      };
    } catch (error) {
      console.error('Erro no PatternCompletionCollector:', error);
      return null;
    }
  }

  calculatePatternAccuracy(correct, player) {
    if (!correct.length || !player.length) return 0;
    
    let matches = 0;
    const maxLength = Math.max(correct.length, player.length);
    
    for (let i = 0; i < maxLength; i++) {
      const correctElement = correct[i];
      const playerElement = player[i];
      
      if (correctElement && playerElement) {
        if (this.elementsMatch(correctElement, playerElement)) {
          matches++;
        }
      }
    }
    
    return (matches / correct.length) * 100;
  }

  elementsMatch(element1, element2) {
    return element1.shape === element2.shape && 
           element1.color === element2.color &&
           element1.size === element2.size;
  }

  assessLogicalReasoning(pattern, correct, player) {
    // Analisa se o jogador seguiu a lógica do padrão
    const patternRule = this.identifyPatternRule(pattern);
    const playerFollowsRule = this.checkRuleCompliance(player, patternRule);
    const correctFollowsRule = this.checkRuleCompliance(correct, patternRule);
    
    if (correctFollowsRule === 0) return 0;
    
    return (playerFollowsRule / correctFollowsRule) * 100;
  }

  identifyPatternRule(pattern) {
    if (pattern.length < 3) return { type: 'simple', confidence: 0.5 };
    
    // Verifica padrões de repetição
    const repetitionPattern = this.checkRepetitionPattern(pattern);
    if (repetitionPattern.confidence > 0.8) {
      return { type: 'repetition', ...repetitionPattern };
    }
    
    // Verifica padrões de progressão
    const progressionPattern = this.checkProgressionPattern(pattern);
    if (progressionPattern.confidence > 0.8) {
      return { type: 'progression', ...progressionPattern };
    }
    
    // Verifica padrões alternados
    const alternationPattern = this.checkAlternationPattern(pattern);
    if (alternationPattern.confidence > 0.8) {
      return { type: 'alternation', ...alternationPattern };
    }
    
    return { type: 'complex', confidence: 0.3 };
  }

  checkRepetitionPattern(pattern) {
    // Verifica se há um padrão de repetição simples
    for (let size = 1; size <= pattern.length / 2; size++) {
      let isRepeating = true;
      const segment = pattern.slice(0, size);
      
      for (let i = size; i < pattern.length; i++) {
        if (!this.elementsMatch(pattern[i], segment[i % size])) {
          isRepeating = false;
          break;
        }
      }
      
      if (isRepeating) {
        return {
          segmentSize: size,
          confidence: 0.9,
          rule: 'repeat_segment'
        };
      }
    }
    
    return { confidence: 0 };
  }

  checkProgressionPattern(pattern) {
    // Verifica padrões de progressão (mudança gradual)
    const properties = ['shape', 'color', 'size'];
    
    for (const prop of properties) {
      const progression = this.analyzePropertyProgression(pattern, prop);
      if (progression.confidence > 0.7) {
        return {
          property: prop,
          direction: progression.direction,
          confidence: progression.confidence,
          rule: 'progression'
        };
      }
    }
    
    return { confidence: 0 };
  }

  analyzePropertyProgression(pattern, property) {
    const values = pattern.map(element => element[property]);
    const uniqueValues = [...new Set(values)];
    
    if (uniqueValues.length < 2) return { confidence: 0 };
    
    // Verifica se há uma progressão ordenada
    let isAscending = true;
    let isDescending = true;
    
    for (let i = 1; i < values.length; i++) {
      if (values[i] <= values[i - 1]) isAscending = false;
      if (values[i] >= values[i - 1]) isDescending = false;
    }
    
    if (isAscending) {
      return { direction: 'ascending', confidence: 0.8 };
    } else if (isDescending) {
      return { direction: 'descending', confidence: 0.8 };
    }
    
    return { confidence: 0 };
  }

  checkAlternationPattern(pattern) {
    // Verifica padrões de alternância
    if (pattern.length < 4) return { confidence: 0 };
    
    const properties = ['shape', 'color', 'size'];
    
    for (const prop of properties) {
      if (this.isAlternatingProperty(pattern, prop)) {
        return {
          property: prop,
          confidence: 0.9,
          rule: 'alternation'
        };
      }
    }
    
    return { confidence: 0 };
  }

  isAlternatingProperty(pattern, property) {
    const values = pattern.map(element => element[property]);
    
    for (let i = 2; i < values.length; i++) {
      if (values[i] !== values[i - 2]) {
        return false;
      }
    }
    
    return values[0] !== values[1];
  }

  checkRuleCompliance(completion, rule) {
    if (!rule || rule.confidence < 0.5) return 0;
    
    let compliance = 0;
    
    switch (rule.type) {
      case 'repetition':
        compliance = this.checkRepetitionCompliance(completion, rule);
        break;
      case 'progression':
        compliance = this.checkProgressionCompliance(completion, rule);
        break;
      case 'alternation':
        compliance = this.checkAlternationCompliance(completion, rule);
        break;
      default:
        compliance = 0.5; // Padrão neutro para casos complexos
    }
    
    return compliance;
  }

  checkRepetitionCompliance(completion, rule) {
    // Verifica se a completação segue o padrão de repetição
    const segmentSize = rule.segmentSize;
    let compliance = 0;
    
    for (let i = 0; i < completion.length; i++) {
      const expectedIndex = i % segmentSize;
      // Aqui precisaríamos do segmento original para comparar
      // Por simplicidade, assumimos compliance parcial
      compliance += 0.5;
    }
    
    return completion.length > 0 ? compliance / completion.length : 0;
  }

  checkProgressionCompliance(completion, rule) {
    // Verifica se a completação segue a progressão
    if (completion.length < 2) return 0.5;
    
    const values = completion.map(element => element[rule.property]);
    let isCorrectDirection = true;
    
    for (let i = 1; i < values.length; i++) {
      if (rule.direction === 'ascending' && values[i] <= values[i - 1]) {
        isCorrectDirection = false;
      } else if (rule.direction === 'descending' && values[i] >= values[i - 1]) {
        isCorrectDirection = false;
      }
    }
    
    return isCorrectDirection ? 1.0 : 0.2;
  }

  checkAlternationCompliance(completion, rule) {
    // Verifica se a completação segue a alternância
    if (completion.length < 2) return 0.5;
    
    const values = completion.map(element => element[rule.property]);
    
    for (let i = 2; i < values.length; i++) {
      if (values[i] !== values[i - 2]) {
        return 0.2;
      }
    }
    
    return 1.0;
  }

  evaluateComplexityHandling(pattern) {
    // Avalia a complexidade do padrão manipulado
    const factors = {
      length: Math.min(pattern.length / 10, 1), // Normalizado para 10 elementos
      uniqueShapes: new Set(pattern.map(e => e.shape)).size,
      uniqueColors: new Set(pattern.map(e => e.color)).size,
      uniqueSizes: new Set(pattern.map(e => e.size)).size
    };
    
    const complexityScore = (
      factors.length * 30 +
      factors.uniqueShapes * 20 +
      factors.uniqueColors * 20 +
      factors.uniqueSizes * 30
    );
    
    return Math.min(100, complexityScore);
  }

  calculateCompletionEfficiency(responseTime, patternLength) {
    const expectedTime = patternLength * 2000; // 2 segundos por elemento
    const efficiency = expectedTime / Math.max(responseTime, 1000);
    return Math.min(100, efficiency * 100);
  }

  analyzeCompletionStrategy(pattern, completion) {
    // Analisa a estratégia usada pelo jogador
    if (completion.length === 0) return 'no_attempt';
    
    const timeToFirstElement = completion[0]?.timestamp || 0;
    const averageTimeBetweenElements = this.calculateAverageTimeBetween(completion);
    
    if (averageTimeBetweenElements < 1000) {
      return 'rapid_fire'; // Muito rápido, possivelmente aleatório
    } else if (averageTimeBetweenElements > 5000) {
      return 'deliberate'; // Pensativo e cuidadoso
    } else if (timeToFirstElement > 10000) {
      return 'analytical'; // Muito tempo antes do primeiro movimento
    } else {
      return 'balanced'; // Estratégia equilibrada
    }
  }

  calculateAverageTimeBetween(completion) {
    if (completion.length < 2) return 0;
    
    let totalTime = 0;
    for (let i = 1; i < completion.length; i++) {
      const timeDiff = (completion[i].timestamp || 0) - (completion[i-1].timestamp || 0);
      totalTime += timeDiff;
    }
    
    return totalTime / (completion.length - 1);
  }

  assessStrategicConsistency(attempts) {
    if (attempts.length < 2) return 100;
    
    const strategies = attempts.map(attempt => attempt.strategy || 'unknown');
    const uniqueStrategies = new Set(strategies);
    
    // Menos estratégias diferentes = maior consistência
    return Math.max(0, 100 - (uniqueStrategies.size - 1) * 20);
  }

  assessSpatialReasoning(pattern, completion) {
    // Avalia habilidades de raciocínio espacial
    const spatialRelationships = this.analyzeSpatialRelationships(pattern);
    const completionSpatialConsistency = this.checkSpatialConsistency(completion, spatialRelationships);
    
    return completionSpatialConsistency * 100;
  }

  analyzeSpatialRelationships(pattern) {
    // Analisa relações espaciais no padrão
    const relationships = {
      adjacency: [],
      symmetry: false,
      progression: false
    };
    
    // Análise simplificada de relações espaciais
    for (let i = 0; i < pattern.length - 1; i++) {
      const current = pattern[i];
      const next = pattern[i + 1];
      
      relationships.adjacency.push({
        similarity: this.calculateSimilarity(current, next),
        position: i
      });
    }
    
    return relationships;
  }

  calculateSimilarity(element1, element2) {
    let similarity = 0;
    if (element1.shape === element2.shape) similarity += 0.33;
    if (element1.color === element2.color) similarity += 0.33;
    if (element1.size === element2.size) similarity += 0.34;
    return similarity;
  }

  checkSpatialConsistency(completion, relationships) {
    // Verifica se a completação mantém consistência espacial
    if (completion.length === 0 || relationships.adjacency.length === 0) return 0.5;
    
    let consistencyScore = 0;
    let validComparisons = 0;
    
    for (let i = 0; i < completion.length - 1; i++) {
      if (i < relationships.adjacency.length) {
        const expectedSimilarity = relationships.adjacency[i].similarity;
        const actualSimilarity = this.calculateSimilarity(completion[i], completion[i + 1]);
        
        const difference = Math.abs(expectedSimilarity - actualSimilarity);
        consistencyScore += Math.max(0, 1 - difference);
        validComparisons++;
      }
    }
    
    return validComparisons > 0 ? consistencyScore / validComparisons : 0.5;
  }

  evaluateAbstractThinking(pattern, difficulty) {
    // Avalia pensamento abstrato baseado na dificuldade e complexidade
    const difficultyMultipliers = {
      easy: 0.5,
      medium: 0.8,
      hard: 1.0
    };
    
    const patternComplexity = this.evaluateComplexityHandling(pattern);
    const abstractionLevel = patternComplexity * (difficultyMultipliers[difficulty] || 0.8);
    
    return Math.min(100, abstractionLevel);
  }

  analyzeProblemSolvingApproach(gameState) {
    // Analisa a abordagem de resolução de problemas
    const responseTime = gameState.responseTime || 0;
    const attempts = gameState.sessionAttempts || [];
    
    if (responseTime < 5000) {
      return 'intuitive'; // Resposta rápida e intuitiva
    } else if (responseTime > 30000) {
      return 'systematic'; // Abordagem sistemática e metodológica
    } else if (attempts.length > 3 && this.hasImprovementTrend(attempts)) {
      return 'adaptive'; // Adapta estratégia baseada em feedback
    } else {
      return 'trial_error'; // Tentativa e erro
    }
  }

  hasImprovementTrend(attempts) {
    if (attempts.length < 3) return false;
    
    const recentScores = attempts.slice(-3).map(attempt => attempt.accuracy || 0);
    return recentScores[2] > recentScores[0];
  }

  calculateAdaptationRate(attempts) {
    if (attempts.length < 3) return 50;
    
    const performanceChanges = [];
    for (let i = 1; i < attempts.length; i++) {
      const change = (attempts[i].accuracy || 0) - (attempts[i-1].accuracy || 0);
      performanceChanges.push(change);
    }
    
    const positiveChanges = performanceChanges.filter(change => change > 0).length;
    return (positiveChanges / performanceChanges.length) * 100;
  }

  assessLearningCurve(attempts) {
    if (attempts.length < 4) return 0;
    
    const scores = attempts.map(attempt => attempt.accuracy || 0);
    const firstHalf = scores.slice(0, Math.floor(scores.length / 2));
    const secondHalf = scores.slice(Math.floor(scores.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;
    
    return secondAvg - firstAvg; // Slope da curva de aprendizagem
  }
}
