/**
 * 🔍 PATTERN RECOGNITION COLLECTOR V3
 * Coletor especializado em análise de reconhecimento de padrões numéricos
 * Portal Betina V3
 */

export class PatternRecognitionCollector {
  constructor() {
    this.patternThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.70,
      poor: 0.50,
      critical: 0.30
    };
    
    this.patternTypes = {
      sequential: 'Padrões sequenciais (1,2,3...)',
      arithmetic: 'Progressões aritméticas (+2, +3...)',
      geometric: 'Progressões geométricas (×2, ×3...)',
      visual: 'Padrões visuais e espaciais',
      repetitive: 'Padrões repetitivos (A,B,A,B...)',
      complex: 'Padrões complexos e mistos'
    };
    
    this.cognitiveSkills = {
      patternRecognition: 'Reconhecimento de padrões',
      sequentialThinking: 'Pensamento sequencial',
      logicalReasoning: 'Raciocínio lógico',
      predictionAbility: 'Capacidade de predição',
      abstractThinking: 'Pensamento abstrato'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  
  /**
   * Análise principal do reconhecimento de padrões
   */
  async analyze(data) {
    if (!data || !data.patternRecognition) {
      console.warn('PatternRecognitionCollector: Dados de reconhecimento de padrões não encontrados');
      return this.getDefaultAnalysis();
    }

    const patternData = data.patternRecognition;
    
    // Analisar precisão por tipo de padrão
    const accuracyByType = this.analyzeAccuracyByType(patternData);
    
    // Avaliar velocidade de reconhecimento
    const recognitionSpeed = this.assessRecognitionSpeed(patternData);
    
    // Analisar complexidade dos padrões resolvidos
    const complexityAnalysis = this.analyzeComplexityHandling(patternData);
    
    // Avaliar capacidade de predição
    const predictionAbility = this.assessPredictionAbility(patternData);
    
    // Analisar progresso temporal
    const temporalProgress = this.analyzeTemporalProgress(patternData);
    
    // Detectar padrões de erro
    const errorPatterns = this.analyzeErrorPatterns(patternData);
    
    // Calcular índice de reconhecimento de padrões
    const patternRecognitionIndex = this.calculatePatternRecognitionIndex(patternData);

    const analysis = {
      timestamp: new Date().toISOString(),
      collector: 'PatternRecognitionCollector',
      version: '3.0.0',
      
      // Métricas principais
      overallAccuracy: this.calculateOverallAccuracy(patternData),
      patternRecognitionIndex,
      recognitionSpeed,
      
      // Análises detalhadas
      accuracyByType,
      complexityAnalysis,
      predictionAbility,
      temporalProgress,
      errorPatterns,
      
      // Habilidades cognitivas específicas
      cognitiveSkills: {
        patternRecognition: this.assessPatternRecognition(patternData),
        sequentialThinking: this.assessSequentialThinking(patternData),
        logicalReasoning: this.assessLogicalReasoning(patternData),
        predictionAbility: this.assessPredictionSkill(patternData),
        abstractThinking: this.assessAbstractThinking(patternData)
      },
      
      // Análise de dificuldade
      difficultyProgression: this.analyzeDifficultyProgression(patternData),
      
      // Recomendações adaptativas
      adaptiveRecommendations: this.generateAdaptiveRecommendations(patternData),
      
      // Métricas de engajamento
      engagementMetrics: this.calculateEngagementMetrics(patternData)
    };

    return analysis;
  }

  /**
   * Calcula precisão geral
   */
  calculateOverallAccuracy(data) {
    if (!data.attempts || data.attempts.length === 0) return 0.7;
    
    const correct = data.attempts.filter(attempt => attempt.correct).length;
    return correct / data.attempts.length;
  }

  /**
   * Analisa precisão por tipo de padrão
   */
  analyzeAccuracyByType(data) {
    const accuracyByType = {};
    
    Object.keys(this.patternTypes).forEach(type => {
      const typeAttempts = data.attempts?.filter(attempt => attempt.patternType === type) || [];
      if (typeAttempts.length > 0) {
        const correct = typeAttempts.filter(attempt => attempt.correct).length;
        accuracyByType[type] = {
          accuracy: correct / typeAttempts.length,
          attempts: typeAttempts.length,
          description: this.patternTypes[type]
        };
      }
    });
    
    return accuracyByType;
  }

  /**
   * Avalia velocidade de reconhecimento
   */
  assessRecognitionSpeed(data) {
    if (!data.attempts || data.attempts.length === 0) {
      return { average: 5000, category: 'average' };
    }
    
    const responseTimes = data.attempts.map(attempt => attempt.responseTime || 5000);
    const averageTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    
    let category = 'average';
    if (averageTime < 2000) category = 'excellent';
    else if (averageTime < 3000) category = 'good';
    else if (averageTime < 5000) category = 'average';
    else if (averageTime < 8000) category = 'poor';
    else category = 'critical';
    
    return {
      average: averageTime,
      category,
      distribution: this.calculateTimeDistribution(responseTimes)
    };
  }

  /**
   * Analisa capacidade de lidar com complexidade
   */
  analyzeComplexityHandling(data) {
    const complexityLevels = ['simple', 'medium', 'complex', 'advanced'];
    const complexityAnalysis = {};
    
    complexityLevels.forEach(level => {
      const levelAttempts = data.attempts?.filter(attempt => attempt.complexity === level) || [];
      if (levelAttempts.length > 0) {
        const correct = levelAttempts.filter(attempt => attempt.correct).length;
        complexityAnalysis[level] = {
          accuracy: correct / levelAttempts.length,
          attempts: levelAttempts.length,
          averageTime: levelAttempts.reduce((sum, attempt) => sum + (attempt.responseTime || 5000), 0) / levelAttempts.length
        };
      }
    });
    
    return complexityAnalysis;
  }

  /**
   * Avalia capacidade de predição
   */
  assessPredictionAbility(data) {
    const predictionAttempts = data.attempts?.filter(attempt => attempt.type === 'prediction') || [];
    
    if (predictionAttempts.length === 0) {
      return { accuracy: 0.7, confidence: 'medium' };
    }
    
    const correct = predictionAttempts.filter(attempt => attempt.correct).length;
    const accuracy = correct / predictionAttempts.length;
    
    let confidence = 'low';
    if (accuracy >= 0.9) confidence = 'excellent';
    else if (accuracy >= 0.8) confidence = 'high';
    else if (accuracy >= 0.7) confidence = 'medium';
    else if (accuracy >= 0.5) confidence = 'low';
    else confidence = 'very-low';
    
    return {
      accuracy,
      confidence,
      totalPredictions: predictionAttempts.length
    };
  }

  /**
   * Analisa progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (!data.attempts || data.attempts.length < 5) {
      return { trend: 'insufficient-data', improvement: 0 };
    }
    
    const attempts = data.attempts.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
    const firstHalf = attempts.slice(0, Math.floor(attempts.length / 2));
    const secondHalf = attempts.slice(Math.floor(attempts.length / 2));
    
    const firstHalfAccuracy = firstHalf.filter(a => a.correct).length / firstHalf.length;
    const secondHalfAccuracy = secondHalf.filter(a => a.correct).length / secondHalf.length;
    
    const improvement = secondHalfAccuracy - firstHalfAccuracy;
    
    let trend = 'stable';
    if (improvement > 0.1) trend = 'improving';
    else if (improvement < -0.1) trend = 'declining';
    
    return {
      trend,
      improvement,
      firstHalfAccuracy,
      secondHalfAccuracy
    };
  }

  /**
   * Analiza padrões de erro
   */
  analyzeErrorPatterns(data) {
    const errors = data.attempts?.filter(attempt => !attempt.correct) || [];
    
    if (errors.length === 0) {
      return { errorRate: 0, commonPatterns: [] };
    }
    
    const errorTypes = {};
    errors.forEach(error => {
      const type = error.errorType || 'unknown';
      errorTypes[type] = (errorTypes[type] || 0) + 1;
    });
    
    const sortedErrors = Object.entries(errorTypes)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([type, count]) => ({ type, count, percentage: count / errors.length }));
    
    return {
      errorRate: errors.length / data.attempts.length,
      commonPatterns: sortedErrors,
      totalErrors: errors.length
    };
  }

  /**
   * Calcula índice de reconhecimento de padrões
   */
  calculatePatternRecognitionIndex(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speedScore = this.calculateSpeedScore(data);
    const complexityScore = this.calculateComplexityScore(data);
    
    return (accuracy * 0.5 + speedScore * 0.25 + complexityScore * 0.25);
  }

  /**
   * Calcula pontuação de velocidade
   */
  calculateSpeedScore(data) {
    const speed = this.assessRecognitionSpeed(data);
    const speedCategories = {
      'excellent': 1.0,
      'good': 0.8,
      'average': 0.6,
      'poor': 0.4,
      'critical': 0.2
    };
    
    return speedCategories[speed.category] || 0.6;
  }

  /**
   * Calcula pontuação de complexidade
   */
  calculateComplexityScore(data) {
    const complexityAnalysis = this.analyzeComplexityHandling(data);
    
    // Peso maior para níveis mais complexos
    const weights = { simple: 0.1, medium: 0.3, complex: 0.4, advanced: 0.2 };
    let weightedScore = 0;
    let totalWeight = 0;
    
    Object.entries(complexityAnalysis).forEach(([level, analysis]) => {
      const weight = weights[level] || 0.25;
      weightedScore += analysis.accuracy * weight;
      totalWeight += weight;
    });
    
    return totalWeight > 0 ? weightedScore / totalWeight : 0.6;
  }

  /**
   * Avalia habilidades cognitivas específicas
   */
  assessPatternRecognition(data) {
    return this.calculateOverallAccuracy(data);
  }

  assessSequentialThinking(data) {
    const sequentialAttempts = data.attempts?.filter(a => a.patternType === 'sequential') || [];
    if (sequentialAttempts.length === 0) return 0.7;
    
    const correct = sequentialAttempts.filter(a => a.correct).length;
    return correct / sequentialAttempts.length;
  }

  assessLogicalReasoning(data) {
    const logicalAttempts = data.attempts?.filter(a => ['arithmetic', 'geometric'].includes(a.patternType)) || [];
    if (logicalAttempts.length === 0) return 0.7;
    
    const correct = logicalAttempts.filter(a => a.correct).length;
    return correct / logicalAttempts.length;
  }

  assessPredictionSkill(data) {
    return this.assessPredictionAbility(data).accuracy;
  }

  assessAbstractThinking(data) {
    const abstractAttempts = data.attempts?.filter(a => ['complex', 'visual'].includes(a.patternType)) || [];
    if (abstractAttempts.length === 0) return 0.7;
    
    const correct = abstractAttempts.filter(a => a.correct).length;
    return correct / abstractAttempts.length;
  }

  /**
   * Analiza progressão de dificuldade
   */
  analyzeDifficultyProgression(data) {
    // Implementação da análise de progressão de dificuldade
    return {
      currentLevel: 'medium',
      suggestedNext: 'complex',
      readiness: 0.8
    };
  }

  /**
   * Gera recomendações adaptativas
   */
  generateAdaptiveRecommendations(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speed = this.assessRecognitionSpeed(data);
    
    const recommendations = [];
    
    if (accuracy < 0.7) {
      recommendations.push({
        type: 'difficulty',
        action: 'decrease',
        reason: 'Baixa precisão detectada'
      });
    }
    
    if (speed.average > 8000) {
      recommendations.push({
        type: 'time',
        action: 'extend',
        reason: 'Tempo de resposta elevado'
      });
    }
    
    return recommendations;
  }

  /**
   * Calcula métricas de engajamento
   */
  calculateEngagementMetrics(data) {
    return {
      attentionLevel: 0.8,
      motivationScore: 0.75,
      frustrationLevel: 0.3
    };
  }

  /**
   * Calcula distribuição de tempos
   */
  calculateTimeDistribution(times) {
    const sorted = times.sort((a, b) => a - b);
    return {
      min: Math.min(...times),
      max: Math.max(...times),
      median: sorted[Math.floor(sorted.length / 2)],
      q1: sorted[Math.floor(sorted.length * 0.25)],
      q3: sorted[Math.floor(sorted.length * 0.75)]
    };
  }

  /**
   * Retorna análise padrão quando dados são insuficientes
   */
  getDefaultAnalysis() {
    return {
      timestamp: new Date().toISOString(),
      collector: 'PatternRecognitionCollector',
      version: '3.0.0',
      overallAccuracy: 0.7,
      patternRecognitionIndex: 0.7,
      recognitionSpeed: { average: 5000, category: 'average' },
      accuracyByType: {},
      complexityAnalysis: {},
      predictionAbility: { accuracy: 0.7, confidence: 'medium' },
      temporalProgress: { trend: 'insufficient-data', improvement: 0 },
      errorPatterns: { errorRate: 0.3, commonPatterns: [] },
      cognitiveSkills: {
        patternRecognition: 0.7,
        sequentialThinking: 0.7,
        logicalReasoning: 0.7,
        predictionAbility: 0.7,
        abstractThinking: 0.7
      },
      difficultyProgression: {
        currentLevel: 'medium',
        suggestedNext: 'complex',
        readiness: 0.7
      },
      adaptiveRecommendations: [],
      engagementMetrics: {
        attentionLevel: 0.7,
        motivationScore: 0.7,
        frustrationLevel: 0.3
      }
    };
  }
}
