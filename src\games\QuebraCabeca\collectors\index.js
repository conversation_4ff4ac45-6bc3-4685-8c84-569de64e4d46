/**
 * 🧩 QUEBRA-CABEÇA COLLECTORS HUB
 * Portal Betina V3 - Hub de coletores para jogos de quebra-cabeça
 * Coleta dados sobre raciocínio espacial, resolução de problemas e processamento visual-espacial
 */

import { SpatialReasoningCollector } from './SpatialReasoningCollector.js';
import { ProblemSolvingCollector } from './ProblemSolvingCollector.js';
import { VisualSpatialCollector } from './VisualSpatialCollector.js';
import { MotorSkillsCollector } from './MotorSkillsCollector.js';
import { PatternRecognitionCollector } from './PatternRecognitionCollector.js';
import { MemoryCollector } from './MemoryCollector.js';
import { PerceptualProcessingCollector } from './PerceptualProcessingCollector.js';

export class QuebraCabecaCollectorsHub {
  constructor() {
    this.hubId = 'quebra-cabeca-collectors-hub';
    this.version = '3.0.0';
    this.gameType = 'QuebraCabeca';
    
    // Inicializar coletores especializados
    this.collectors = {
      spatialReasoning: new SpatialReasoningCollector(),
      problemSolving: new ProblemSolvingCollector(),
      visualSpatial: new VisualSpatialCollector(),
      motorSkills: new MotorSkillsCollector(),
      patternRecognition: new PatternRecognitionCollector(),
      memory: new MemoryCollector(),
      perceptualProcessing: new PerceptualProcessingCollector()
    };
    
    this.sessionData = [];
    this.analysisCache = new Map();
    this.isInitialized = true;
    
    console.log('🧩 QuebraCabecaCollectorsHub inicializado v3.0.0');
  }
  
  /**
   * Executa análise completa dos dados do quebra-cabeça
   */
  async runCompleteAnalysis(gameData) {
    try {
      const timestamp = Date.now();
      const sessionId = gameData.sessionId || `session_${timestamp}`;
      
      // Coletar dados de cada coletor
      const spatialAnalysis = await this.collectors.spatialReasoning.collect(gameData);
      const problemSolvingAnalysis = await this.collectors.problemSolving.collect(gameData);
      const visualSpatialAnalysis = await this.collectors.visualSpatial.collect(gameData);
      const motorSkillsAnalysis = await this.collectors.motorSkills.collect(gameData);
      const patternRecognitionAnalysis = await this.collectors.patternRecognition.collect(gameData);
      const memoryAnalysis = await this.collectors.memory.collect(gameData);
      const perceptualProcessingAnalysis = await this.collectors.perceptualProcessing.collect(gameData);
      
      const completeAnalysis = {
        timestamp,
        sessionId,
        gameType: 'QuebraCabeca',
        spatialReasoning: spatialAnalysis,
        problemSolving: problemSolvingAnalysis,
        visualSpatial: visualSpatialAnalysis,
        motorSkills: motorSkillsAnalysis,
        patternRecognition: patternRecognitionAnalysis,
        memory: memoryAnalysis,
        perceptualProcessing: perceptualProcessingAnalysis,
        overallPerformance: this.calculateOverallPerformance({
          spatialAnalysis,
          problemSolvingAnalysis, 
          visualSpatialAnalysis,
          motorSkillsAnalysis,
          patternRecognitionAnalysis,
          memoryAnalysis,
          perceptualProcessingAnalysis
        }),
        insights: this.generateInsights({
          spatialAnalysis,
          problemSolvingAnalysis,
          visualSpatialAnalysis,
          motorSkillsAnalysis,
          patternRecognitionAnalysis,
          memoryAnalysis,
          perceptualProcessingAnalysis
        }),
        recommendations: this.generateRecommendations({
          spatialAnalysis,
          problemSolvingAnalysis,
          visualSpatialAnalysis,
          motorSkillsAnalysis,
          patternRecognitionAnalysis,
          memoryAnalysis,
          perceptualProcessingAnalysis
        })
      };
      
      this.sessionData.push(completeAnalysis);
      return completeAnalysis;
      
    } catch (error) {
      console.error('🧩 Erro na análise completa do QuebraCabeca:', error);
      return {
        error: error.message,
        timestamp: Date.now(),
        gameType: 'QuebraCabeca'
      };
    }
  }
  
  /**
   * Calcula desempenho geral
   */
  calculateOverallPerformance(analyses) {
    try {
      const scores = Object.values(analyses).map(analysis => analysis.score || 0.5);
      const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      
      return {
        score: averageScore,
        level: averageScore > 0.8 ? 'Avançado' : averageScore > 0.6 ? 'Intermediário' : 'Básico',
        strengths: this.identifyStrengths(analyses),
        challenges: this.identifyChallenges(analyses)
      };
    } catch (error) {
      console.error('🧩 Erro ao calcular desempenho geral:', error);
      return { score: 0.5, level: 'Básico', strengths: [], challenges: [] };
    }
  }
  
  /**
   * Identifica pontos fortes
   */
  identifyStrengths(analyses) {
    const strengths = [];
    if (analyses.spatialAnalysis?.score > 0.7) strengths.push('Raciocínio espacial forte');
    if (analyses.problemSolvingAnalysis?.score > 0.7) strengths.push('Boa resolução de problemas');
    if (analyses.visualSpatialAnalysis?.score > 0.7) strengths.push('Processamento visual-espacial eficiente');
    if (analyses.motorSkillsAnalysis?.score > 0.7) strengths.push('Habilidades motoras finas desenvolvidas');
    if (analyses.patternRecognitionAnalysis?.score > 0.7) strengths.push('Excelente reconhecimento de padrões');
    if (analyses.memoryAnalysis?.score > 0.7) strengths.push('Memória visual e espacial eficaz');
    if (analyses.perceptualProcessingAnalysis?.score > 0.7) strengths.push('Processamento perceptual avançado');
    return strengths;
  }
  
  /**
   * Identifica desafios
   */
  identifyChallenges(analyses) {
    const challenges = [];
    if (analyses.spatialAnalysis?.score < 0.5) challenges.push('Raciocínio espacial precisa de desenvolvimento');
    if (analyses.problemSolvingAnalysis?.score < 0.5) challenges.push('Resolução de problemas requer prática');
    if (analyses.visualSpatialAnalysis?.score < 0.5) challenges.push('Processamento visual-espacial precisa de suporte');
    if (analyses.motorSkillsAnalysis?.score < 0.5) challenges.push('Habilidades motoras finas necessitam treino');
    if (analyses.patternRecognitionAnalysis?.score < 0.5) challenges.push('Reconhecimento de padrões precisa ser fortalecido');
    if (analyses.memoryAnalysis?.score < 0.5) challenges.push('Memória visual e espacial requer desenvolvimento');
    if (analyses.perceptualProcessingAnalysis?.score < 0.5) challenges.push('Processamento perceptual necessita aprimoramento');
    return challenges;
  }
  
  /**
   * Gera insights terapêuticos
   */
  generateInsights(analyses) {
    const insights = [
      'Quebra-cabeça desenvolve raciocínio espacial e resolução de problemas',
      'Atividade estimula processamento visual-espacial e memória',
      'Jogos de puzzle fortalecem persistência e paciência',
      'Manipulação de peças desenvolve habilidades motoras finas',
      'Reconhecimento de padrões aprimora processamento visual',
      'Integração perceptual é exercitada através da montagem',
      'Memória de trabalho é constantemente desafiada'
    ];
    
    return insights;
  }
  
  /**
   * Gera recomendações
   */
  generateRecommendations(analyses) {
    const recommendations = [];
    
    if (analyses.spatialAnalysis?.score < 0.6) {
      recommendations.push('Praticar com quebra-cabeças mais simples primeiro');
    }
    
    if (analyses.problemSolvingAnalysis?.score > 0.8) {
      recommendations.push('Aumentar complexidade dos quebra-cabeças');
    }
    
    if (analyses.motorSkillsAnalysis?.score < 0.6) {
      recommendations.push('Incluir exercícios de coordenação motora fina');
    }
    
    if (analyses.patternRecognitionAnalysis?.score < 0.6) {
      recommendations.push('Praticar jogos de reconhecimento de padrões');
    }
    
    if (analyses.memoryAnalysis?.score < 0.6) {
      recommendations.push('Exercitar memória visual com atividades específicas');
    }
    
    if (analyses.perceptualProcessingAnalysis?.score < 0.6) {
      recommendations.push('Desenvolver processamento perceptual com exercícios visuais');
    }
    
    recommendations.push('Combinar quebra-cabeças com atividades de construção');
    
    return recommendations;
  }
}

export default QuebraCabecaCollectorsHub;

// Exportar coletores individuais para uso específico
export {
  SpatialReasoningCollector,
  ProblemSolvingCollector,
  VisualSpatialCollector,
  MotorSkillsCollector,
  PatternRecognitionCollector,
  MemoryCollector,
  PerceptualProcessingCollector
};
