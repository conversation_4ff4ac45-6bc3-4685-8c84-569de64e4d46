/**
 * @file test-complete-architecture.js
 * @description Teste completo da arquitetura integrada do Portal Betina V3
 * @version 1.0.0
 * @date 2025-07-08
 */

import { logger } from './src/api/services/core/logging/StructuredLogger.js';
import { getCache } from './src/api/services/core/cache/IntelligentCache.js';
import { getHealthCheckService } from './src/api/services/core/health/HealthCheckService.js';
import { SystemOrchestrator } from './src/api/services/core/SystemOrchestrator.js';
import { AIBrainOrchestrator } from './src/api/services/ai/AIBrainOrchestrator.js';
import { getAnalysisOrchestrator } from './src/api/services/analysis/AnalysisOrchestrator.js';
import { getBehavioralAnalyzer } from './src/api/services/analysis/BehavioralAnalyzer.js';

/**
 * Mock do DatabaseService para testes
 */
class MockDatabaseService {
  constructor() {
    this.data = new Map();
    this.connected = true;
  }

  async store(collection, data) {
    const key = `${collection}_${Date.now()}_${Math.random()}`;
    this.data.set(key, { ...data, _id: key, timestamp: new Date() });
    return { success: true, id: key };
  }

  async query(collection, query = {}) {
    const results = [];
    for (const [key, value] of this.data.entries()) {
      if (key.startsWith(collection)) {
        results.push(value);
      }
    }
    return results.slice(0, 10); // Limitar resultados
  }

  async testConnection() {
    return this.connected;
  }

  disconnect() {
    this.connected = false;
  }
}

/**
 * Teste principal da arquitetura
 */
async function testCompleteArchitecture() {
  console.log('🏗️ INICIANDO TESTE COMPLETO DA ARQUITETURA PORTAL BETINA V3\n');
  
  const results = {
    core: {},
    analysis: {},
    ai: {},
    integration: {},
    performance: {},
    errors: []
  };

  try {
    // ==================== TESTE 1: COMPONENTES CORE ====================
    console.log('🔧 TESTE 1: COMPONENTES CORE');
    console.log('='.repeat(50));

    // 1.1 StructuredLogger
    console.log('📝 Testando StructuredLogger...');
    try {
      logger.info('Test log message', { test: true, timestamp: Date.now() });
      results.core.logger = { status: 'OK', error: null };
      console.log('✅ StructuredLogger: OK');
    } catch (error) {
      results.core.logger = { status: 'ERRO', error: error.message };
      results.errors.push(`StructuredLogger: ${error.message}`);
      console.log('❌ StructuredLogger: ERRO -', error.message);
    }

    // 1.2 IntelligentCache
    console.log('💾 Testando IntelligentCache...');
    try {
      const cache = getCache({ maxSize: 100, defaultTTL: 60000 });
      
      // Teste de operações básicas
      cache.set('test_key', { data: 'test_value', timestamp: Date.now() });
      const retrieved = cache.get('test_key');
      
      if (!retrieved) throw new Error('Cache não retornou valor armazenado');
      
      // Teste de métricas
      const metrics = cache.getMetrics();
      if (typeof metrics.hitRate !== 'number') throw new Error('Métricas inválidas');
      
      // Teste de health
      const health = cache.getHealthStatus();
      if (!health.status) throw new Error('Health status inválido');
      
      results.core.cache = { 
        status: 'OK', 
        metrics: { 
          size: cache.cache.size, 
          hitRate: metrics.hitRate,
          health: health.status 
        },
        error: null 
      };
      console.log('✅ IntelligentCache: OK -', `Size: ${cache.cache.size}, HitRate: ${metrics.hitRate}`);
    } catch (error) {
      results.core.cache = { status: 'ERRO', error: error.message };
      results.errors.push(`IntelligentCache: ${error.message}`);
      console.log('❌ IntelligentCache: ERRO -', error.message);
    }

    // 1.3 HealthCheckService
    console.log('🏥 Testando HealthCheckService...');
    try {
      const healthService = getHealthCheckService({ checkInterval: 60000 });
      
      // Executar health check
      const healthResult = await healthService.runHealthCheck();
      
      if (!healthResult.overall) throw new Error('Health check não retornou status geral');
      if (!healthResult.components) throw new Error('Health check não retornou componentes');
      
      results.core.health = { 
        status: 'OK', 
        overall: healthResult.overall,
        componentsCount: Object.keys(healthResult.components).length,
        error: null 
      };
      console.log('✅ HealthCheckService: OK -', `Status: ${healthResult.overall}, Componentes: ${Object.keys(healthResult.components).length}`);
    } catch (error) {
      results.core.health = { status: 'ERRO', error: error.message };
      results.errors.push(`HealthCheckService: ${error.message}`);
      console.log('❌ HealthCheckService: ERRO -', error.message);
    }

    console.log();

    // ==================== TESTE 2: ANALISADORES ====================
    console.log('🔬 TESTE 2: ANALISADORES ESPECIALIZADOS');
    console.log('='.repeat(50));

    // 2.1 BehavioralAnalyzer
    console.log('🧠 Testando BehavioralAnalyzer...');
    try {
      const behavioralAnalyzer = getBehavioralAnalyzer();
      
      // Teste de inicialização
      if (!behavioralAnalyzer) throw new Error('BehavioralAnalyzer não foi criado');
      
      // Teste de métricas
      const metrics = behavioralAnalyzer.getAnalyzerMetrics();
      if (typeof metrics.totalAnalyses !== 'number') throw new Error('Métricas inválidas');
      
      results.analysis.behavioral = { 
        status: 'OK', 
        metrics: {
          totalAnalyses: metrics.totalAnalyses,
          successRate: metrics.successRate
        },
        error: null 
      };
      console.log('✅ BehavioralAnalyzer: OK -', `Análises: ${metrics.totalAnalyses}`);
    } catch (error) {
      results.analysis.behavioral = { status: 'ERRO', error: error.message };
      results.errors.push(`BehavioralAnalyzer: ${error.message}`);
      console.log('❌ BehavioralAnalyzer: ERRO -', error.message);
    }

    // 2.2 AnalysisOrchestrator
    console.log('🎭 Testando AnalysisOrchestrator...');
    try {
      const analysisOrchestrator = getAnalysisOrchestrator({
        enableParallelAnalysis: true,
        analysisTimeout: 5000
      });
      
      if (!analysisOrchestrator) throw new Error('AnalysisOrchestrator não foi criado');
      
      // Teste de métricas do orquestrador
      const orchestratorMetrics = analysisOrchestrator.getOrchestratorMetrics();
      if (typeof orchestratorMetrics.totalOrchestrations !== 'number') {
        throw new Error('Métricas do orquestrador inválidas');
      }
      
      results.analysis.orchestrator = { 
        status: 'OK', 
        metrics: {
          totalOrchestrations: orchestratorMetrics.totalOrchestrations,
          successfulAnalyses: orchestratorMetrics.successfulAnalyses
        },
        error: null 
      };
      console.log('✅ AnalysisOrchestrator: OK -', `Orquestrações: ${orchestratorMetrics.totalOrchestrations}`);
    } catch (error) {
      results.analysis.orchestrator = { status: 'ERRO', error: error.message };
      results.errors.push(`AnalysisOrchestrator: ${error.message}`);
      console.log('❌ AnalysisOrchestrator: ERRO -', error.message);
    }

    console.log();

    // ==================== TESTE 3: SISTEMA DE IA ====================
    console.log('🧠 TESTE 3: SISTEMA DE IA');
    console.log('='.repeat(50));

    // 3.1 AIBrainOrchestrator
    console.log('🌟 Testando AIBrainOrchestrator...');
    try {
      const mockDb = new MockDatabaseService();
      const aiBrain = new AIBrainOrchestrator({
        databaseService: mockDb,
        simulationMode: true,
        apiKey: 'test_key'
      });
      
      if (!aiBrain) throw new Error('AIBrainOrchestrator não foi criado');
      
      // Teste de métricas do cache do AI Brain
      const cacheMetrics = aiBrain.getCacheMetrics();
      if (typeof cacheMetrics.hitRate !== 'number') throw new Error('Cache metrics inválidas');
      
      results.ai.brain = { 
        status: 'OK', 
        cache: {
          hitRate: cacheMetrics.hitRate,
          size: cacheMetrics.size
        },
        error: null 
      };
      console.log('✅ AIBrainOrchestrator: OK -', `Cache HitRate: ${cacheMetrics.hitRate}`);
    } catch (error) {
      results.ai.brain = { status: 'ERRO', error: error.message };
      results.errors.push(`AIBrainOrchestrator: ${error.message}`);
      console.log('❌ AIBrainOrchestrator: ERRO -', error.message);
    }

    console.log();

    // ==================== TESTE 4: INTEGRAÇÃO COMPLETA ====================
    console.log('🔗 TESTE 4: INTEGRAÇÃO COMPLETA (SystemOrchestrator)');
    console.log('='.repeat(50));

    console.log('🏗️ Testando SystemOrchestrator...');
    try {
      const mockDb = new MockDatabaseService();
      
      // Teste de criação via singleton
      const systemOrchestrator = SystemOrchestrator.getInstance(mockDb, {
        logLevel: 'info',
        cacheSize: 500,
        healthCheckInterval: 60000
      });
      
      if (!systemOrchestrator) throw new Error('SystemOrchestrator não foi criado');
      
      // Teste de inicialização
      console.log('📡 Inicializando SystemOrchestrator...');
      await systemOrchestrator.initialize();
      
      // Verificar componentes essenciais
      const hasAIBrain = !!systemOrchestrator.aiBrain;
      const hasCache = !!systemOrchestrator.cache;
      const hasHealthCheck = !!systemOrchestrator.healthCheck;
      const hasAnalyzers = !!systemOrchestrator.behavioralAnalyzer;
      
      if (!hasAIBrain) throw new Error('AI Brain não inicializado');
      if (!hasCache) throw new Error('Cache não inicializado');
      if (!hasHealthCheck) throw new Error('Health Check não inicializado');
      
      results.integration.orchestrator = { 
        status: 'OK', 
        components: {
          aiBrain: hasAIBrain,
          cache: hasCache,
          healthCheck: hasHealthCheck,
          analyzers: hasAnalyzers
        },
        error: null 
      };
      console.log('✅ SystemOrchestrator: OK -', 
        `AI Brain: ${hasAIBrain ? '✅' : '❌'}, ` +
        `Cache: ${hasCache ? '✅' : '❌'}, ` +
        `Health: ${hasHealthCheck ? '✅' : '❌'}, ` +
        `Analyzers: ${hasAnalyzers ? '✅' : '❌'}`);

      // Teste do fluxo completo de processamento
      console.log('🔄 Testando fluxo completo de processamento...');
      const mockGameMetrics = {
        sessionId: 'test_session_' + Date.now(),
        userId: 'test_user_123',
        gameName: 'MemoryGame',
        accuracy: 0.85,
        responseTime: 1200,
        duration: 120000,
        interactions: [
          { type: 'click', timestamp: Date.now() - 60000 },
          { type: 'match', timestamp: Date.now() - 30000 }
        ]
      };

      const processResult = await systemOrchestrator.processGameMetrics(
        'test_child_123',
        'MemoryGame',
        mockGameMetrics
      );

      if (!processResult.success) {
        throw new Error(`Processamento falhou: ${processResult.error}`);
      }

      results.integration.processing = { 
        status: 'OK', 
        data: {
          hasTherapeuticMetrics: !!processResult.therapeuticMetrics,
          hasSpecializedAnalyses: !!processResult.specializedAnalyses,
          processingSuccess: processResult.success
        },
        error: null 
      };
      console.log('✅ Fluxo de processamento: OK -', 
        `Métricas: ${!!processResult.therapeuticMetrics ? '✅' : '❌'}, ` +
        `Análises: ${!!processResult.specializedAnalyses ? '✅' : '❌'}`);

    } catch (error) {
      results.integration.orchestrator = { status: 'ERRO', error: error.message };
      results.errors.push(`SystemOrchestrator: ${error.message}`);
      console.log('❌ SystemOrchestrator: ERRO -', error.message);
    }

    console.log();

    // ==================== TESTE 5: PERFORMANCE E SAÚDE ====================
    console.log('⚡ TESTE 5: PERFORMANCE E SAÚDE DO SISTEMA');
    console.log('='.repeat(50));

    console.log('📊 Coletando métricas de performance...');
    try {
      const cache = getCache();
      const cacheMetrics = cache.getMetrics();
      
      const healthService = getHealthCheckService();
      const systemHealth = await healthService.runHealthCheck();
      
      results.performance.metrics = {
        cache: {
          hitRate: cacheMetrics.hitRate,
          size: cacheMetrics.size,
          hits: cacheMetrics.hits,
          misses: cacheMetrics.misses
        },
        system: {
          overall: systemHealth.overall,
          componentsHealthy: Object.values(systemHealth.components)
            .filter(c => c.status === 'healthy').length,
          totalComponents: Object.keys(systemHealth.components).length
        },
        timestamp: new Date().toISOString()
      };

      console.log('✅ Métricas coletadas:', {
        cacheHitRate: cacheMetrics.hitRate.toFixed(3),
        systemHealth: systemHealth.overall,
        healthyComponents: `${results.performance.metrics.system.componentsHealthy}/${results.performance.metrics.system.totalComponents}`
      });

    } catch (error) {
      results.performance.error = error.message;
      results.errors.push(`Performance: ${error.message}`);
      console.log('❌ Coleta de métricas: ERRO -', error.message);
    }

    console.log();

    // ==================== RESULTADOS FINAIS ====================
    console.log('📋 RESULTADOS FINAIS DO TESTE');
    console.log('='.repeat(50));
    
    const totalTests = Object.keys(results.core).length + 
                      Object.keys(results.analysis).length + 
                      Object.keys(results.ai).length + 
                      Object.keys(results.integration).length;
    
    const successfulTests = [
      ...Object.values(results.core),
      ...Object.values(results.analysis),
      ...Object.values(results.ai),
      ...Object.values(results.integration)
    ].filter(result => result.status === 'OK').length;

    console.log(`\n📊 RESUMO DOS TESTES:`);
    console.log(`✅ Testes Bem-sucedidos: ${successfulTests}`);
    console.log(`❌ Testes com Erro: ${totalTests - successfulTests}`);
    console.log(`📈 Taxa de Sucesso: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);
    
    if (results.errors.length > 0) {
      console.log(`\n⚠️ ERROS ENCONTRADOS (${results.errors.length}):`);
      results.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    console.log(`\n🎯 STATUS GERAL: ${successfulTests === totalTests ? '✅ SISTEMA TOTALMENTE FUNCIONAL' : '⚠️ SISTEMA COM ALGUNS PROBLEMAS'}`);
    
    if (results.performance.metrics) {
      console.log(`\n⚡ PERFORMANCE:`);
      console.log(`💾 Cache Hit Rate: ${(results.performance.metrics.cache.hitRate * 100).toFixed(1)}%`);
      console.log(`🏥 Saúde do Sistema: ${results.performance.metrics.system.overall}`);
      console.log(`🔧 Componentes Saudáveis: ${results.performance.metrics.system.componentsHealthy}/${results.performance.metrics.system.totalComponents}`);
    }

    console.log('\n🎉 TESTE DA ARQUITETURA CONCLUÍDO!');
    
    return {
      success: successfulTests === totalTests,
      results,
      summary: {
        totalTests,
        successfulTests,
        failedTests: totalTests - successfulTests,
        successRate: (successfulTests / totalTests) * 100,
        errors: results.errors
      }
    };

  } catch (error) {
    console.error('❌ ERRO CRÍTICO NO TESTE DA ARQUITETURA:', error);
    return {
      success: false,
      error: error.message,
      results,
      summary: {
        totalTests: 0,
        successfulTests: 0,
        failedTests: 1,
        successRate: 0,
        errors: [error.message]
      }
    };
  }
}

// Executar teste principal
console.log('🚀 Iniciando teste da arquitetura do Portal Betina V3...\n');
testCompleteArchitecture()
  .then(result => {
    if (result.success) {
      console.log('\n🎊 TESTE COMPLETO: SUCESSO TOTAL!');
      process.exit(0);
    } else {
      console.log('\n⚠️ TESTE COMPLETO: CONCLUÍDO COM PROBLEMAS');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 FALHA CRÍTICA NO TESTE:', error);
    process.exit(1);
  });
