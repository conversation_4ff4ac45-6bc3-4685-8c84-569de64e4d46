<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔢 Contagem de Números V3 - Preview Interativo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            color: white;
        }
        
        .game-container {
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }
        
        .header {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem 3rem 1rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            min-height: 60px;
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
            color: white;
            text-align: center;
            flex: 1;
        }
        
        .header-tts-button {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            color: white;
            z-index: 10;
        }
        
        .header-tts-button:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            min-width: 80px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.25rem;
        }
        
        .activity-selector {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .activity-selector h3 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .activity-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .activity-btn {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .activity-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-1px);
        }
        
        .activity-btn.active {
            background: rgba(76, 175, 80, 0.4);
            border-color: rgba(76, 175, 80, 0.6);
        }
        
        .game-area {
            padding: 0;
        }
        
        .activity {
            display: none;
        }
        
        .activity.active {
            display: block;
        }
        
        .instruction {
            text-align: center;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .instruction h3 {
            color: white;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }
        
        .instruction p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
        }
        
        .options-grid {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .number-option {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
            text-align: center;
        }
        
        .number-option:hover {
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .number-option .number {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            display: block;
        }
        
        .counting-display {
            text-align: center;
            margin: 2rem 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .counting-objects {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }
        
        .counting-object {
            font-size: 2rem;
            animation: bounce 0.5s ease-in-out;
        }
        
        @keyframes bounce {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .estimation-timer {
            background: rgba(255, 193, 7, 0.9);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: bold;
            margin-bottom: 1rem;
            display: inline-block;
        }
        
        .sequence-display {
            text-align: center;
            margin: 2rem 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .sequence-numbers {
            font-size: 3rem;
            font-weight: bold;
            color: white;
            letter-spacing: 1rem;
        }
        
        .missing-number {
            color: #ff6b6b;
        }
        
        .comparison-groups {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
            margin: 2rem 0;
        }
        
        .comparison-group {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .comparison-group:hover {
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
        }
        
        .comparison-group.selected {
            background: rgba(76, 175, 80, 0.4);
            border-color: rgba(76, 175, 80, 0.7);
        }
        
        .group-items {
            display: flex;
            gap: 0.3rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 0.5rem;
        }
        
        .group-item {
            font-size: 1.5rem;
        }
        
        .group-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .number-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            max-width: 300px;
            margin: 2rem auto;
        }
        
        .number-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
        }
        
        .number-item.target {
            background: rgba(33, 150, 243, 0.2);
            border-color: rgba(33, 150, 243, 0.5);
        }
        
        .number-item.selected {
            background: rgba(76, 175, 80, 0.4);
            color: white;
            border-color: rgba(76, 175, 80, 0.7);
        }
        
        .progress {
            text-align: center;
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.9);
            margin-top: 1rem;
        }
        
        .play-sound-btn {
            background: rgba(255, 107, 107, 0.9);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 1rem 2rem;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.2rem;
            margin: 1rem;
            transition: all 0.3s ease;
        }
        
        .play-sound-btn:hover {
            background: rgba(255, 82, 82, 1);
            transform: scale(1.05);
        }
        
        .repeat-btn {
            background: rgba(255, 193, 7, 0.9);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .repeat-btn:hover {
            background: rgba(255, 193, 7, 1);
        }
        
        .sound-wave {
            font-size: 2rem;
            animation: wave 1s ease-in-out infinite;
            color: white;
        }
        
        @keyframes wave {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        @media (max-width: 600px) {
            .stats {
                gap: 0.5rem;
            }
            
            .stat-item {
                min-width: 60px;
                padding: 0.75rem;
            }
            
            .options-grid {
                flex-direction: column;
                align-items: center;
            }
            
            .number-option {
                min-width: 120px;
            }
            
            .sequence-numbers {
                font-size: 2rem;
                letter-spacing: 0.5rem;
            }
            
            .comparison-groups {
                flex-direction: column;
                align-items: center;
            }
            
            .activity-buttons {
                gap: 0.25rem;
            }
            
            .activity-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <div class="header">
            <h1>🔢 Contagem de Números V3</h1>
            <button class="header-tts-button" onclick="toggleTTS()" title="Ativar/Desativar TTS">
                🔊
            </button>
        </div>
        
        <!-- Stats -->
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">8</div>
                <div class="stat-label">Rodada</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">65</div>
                <div class="stat-label">Pontos</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">92%</div>
                <div class="stat-label">Precisão</div>
            </div>
        </div>
        
        <!-- Activity Selector (for demo) -->
        <div class="activity-selector">
            <h3>🎮 <strong>PREVIEW:</strong> Clique para testar cada atividade:</h3>
            <div class="activity-buttons">
                <button class="activity-btn active" onclick="showActivity('number-counting')">🔢 Contagem</button>
                <button class="activity-btn" onclick="showActivity('sound-matching')">🎵 Sons</button>
                <button class="activity-btn" onclick="showActivity('quantity-estimation')">📊 Estimativa</button>
                <button class="activity-btn" onclick="showActivity('number-sequence')">🔗 Sequência</button>
                <button class="activity-btn" onclick="showActivity('magnitude-comparison')">⚖️ Comparação</button>
                <button class="activity-btn" onclick="showActivity('number-recognition')">🎯 Reconhecimento</button>
            </div>
        </div>
        
        <!-- Game Area -->
        <div class="game-area">
            
            <!-- 1. Number Counting (Current) -->
            <div id="number-counting" class="activity active">
                <div class="instruction">
                    <h3>Quantos objetos você vê?</h3>
                    <p>Conte todos os 🍎</p>
                    <button class="repeat-btn" onclick="playSound('count')">🔊 Repetir</button>
                </div>
                <div class="counting-display">
                    <div class="counting-objects">
                        <div class="counting-object">🍎</div>
                        <div class="counting-object">🍎</div>
                        <div class="counting-object">🍎</div>
                        <div class="counting-object">🍎</div>
                        <div class="counting-object">🍎</div>
                    </div>
                </div>
                <div class="options-grid">
                    <div class="number-option" onclick="selectNumber('4')">
                        <span class="number">4</span>
                    </div>
                    <div class="number-option" onclick="selectNumber('5')">
                        <span class="number">5</span>
                    </div>
                    <div class="number-option" onclick="selectNumber('6')">
                        <span class="number">6</span>
                    </div>
                </div>
            </div>
            
            <!-- 2. Sound Matching -->
            <div id="sound-matching" class="activity">
                <div class="instruction">
                    <h3>Que número você ouviu?</h3>
                    <button class="play-sound-btn" onclick="playTargetNumber()">
                        ▶️ Tocar Número
                    </button>
                    <div class="sound-wave" id="sound-wave" style="display: none;">🎵 ~~~</div>
                </div>
                <div class="options-grid">
                    <div class="number-option" onclick="selectSound('3')">
                        <span class="number">3</span>
                        <div style="font-size: 1rem; margin-top: 0.5rem;">🔊</div>
                    </div>
                    <div class="number-option" onclick="selectSound('7')">
                        <span class="number">7</span>
                        <div style="font-size: 1rem; margin-top: 0.5rem;">🔊</div>
                    </div>
                    <div class="number-option" onclick="selectSound('5')">
                        <span class="number">5</span>
                        <div style="font-size: 1rem; margin-top: 0.5rem;">🔊</div>
                    </div>
                </div>
            </div>
            
            <!-- 3. Quantity Estimation -->
            <div id="quantity-estimation" class="activity">
                <div class="instruction">
                    <h3>Estimativa Rápida</h3>
                    <p>Quantos pontos há? (Tempo limitado!)</p>
                    <div class="estimation-timer">⏱️ 3 segundos</div>
                </div>
                <div class="counting-display">
                    <div class="counting-objects">
                        <div class="counting-object">🔴</div>
                        <div class="counting-object">🔴</div>
                        <div class="counting-object">🔴</div>
                        <div class="counting-object">🔴</div>
                        <div class="counting-object">🔴</div>
                        <div class="counting-object">🔴</div>
                        <div class="counting-object">🔴</div>
                        <div class="counting-object">🔴</div>
                    </div>
                </div>
                <div class="options-grid">
                    <div class="number-option" onclick="selectEstimation('7')">
                        <span class="number">7</span>
                    </div>
                    <div class="number-option" onclick="selectEstimation('8')">
                        <span class="number">8</span>
                    </div>
                    <div class="number-option" onclick="selectEstimation('9')">
                        <span class="number">9</span>
                    </div>
                </div>
            </div>
            
            <!-- 4. Number Sequence -->
            <div id="number-sequence" class="activity">
                <div class="instruction">
                    <h3>Complete a sequência:</h3>
                    <p>Que número vem depois?</p>
                </div>
                <div class="sequence-display">
                    <div class="sequence-numbers">
                        2 → 4 → 6 → <span class="missing-number">?</span>
                    </div>
                </div>
                <div class="options-grid">
                    <div class="number-option" onclick="selectSequence('7')">
                        <span class="number">7</span>
                    </div>
                    <div class="number-option" onclick="selectSequence('8')">
                        <span class="number">8</span>
                    </div>
                    <div class="number-option" onclick="selectSequence('10')">
                        <span class="number">10</span>
                    </div>
                </div>
            </div>
            
            <!-- 5. Magnitude Comparison -->
            <div id="magnitude-comparison" class="activity">
                <div class="instruction">
                    <h3>Qual grupo tem MAIS objetos?</h3>
                    <p>Compare as quantidades!</p>
                </div>
                <div class="comparison-groups">
                    <div class="comparison-group" onclick="selectComparison('A')">
                        <div class="group-items">
                            <span class="group-item">⭐</span>
                            <span class="group-item">⭐</span>
                            <span class="group-item">⭐</span>
                        </div>
                        <div class="group-label">Grupo A</div>
                    </div>
                    <div class="comparison-group" onclick="selectComparison('B')">
                        <div class="group-items">
                            <span class="group-item">⭐</span>
                            <span class="group-item">⭐</span>
                            <span class="group-item">⭐</span>
                            <span class="group-item">⭐</span>
                            <span class="group-item">⭐</span>
                        </div>
                        <div class="group-label">Grupo B</div>
                    </div>
                </div>
            </div>
            
            <!-- 6. Number Recognition -->
            <div id="number-recognition" class="activity">
                <div class="instruction">
                    <h3>Encontre todos os números: <span style="color: #007bff;">7</span></h3>
                    <p>Cuidado com os parecidos!</p>
                </div>
                <div class="number-grid">
                    <div class="number-item target" onclick="toggleRecognition(this, '7')">7</div>
                    <div class="number-item" onclick="toggleRecognition(this, '1')">1</div>
                    <div class="number-item target" onclick="toggleRecognition(this, '7')">7</div>
                    <div class="number-item" onclick="toggleRecognition(this, '4')">4</div>
                    <div class="number-item" onclick="toggleRecognition(this, '9')">9</div>
                    <div class="number-item target" onclick="toggleRecognition(this, '7')">7</div>
                </div>
                <div class="progress">
                    Encontrados: <span id="found-count">0</span>/3
                </div>
            </div>
            
        </div>
    </div>

    <script>
        // Activity Management seguindo padrão do Letter Recognition V3
        const activities = {
            'number-counting': 'Contagem de Números',
            'sound-matching': 'Combinação Sonora',
            'quantity-estimation': 'Estimativa de Quantidade',
            'number-sequence': 'Sequência Numérica',
            'magnitude-comparison': 'Comparação de Magnitude',
            'number-recognition': 'Reconhecimento Numérico'
        };
        
        let currentActivity = 'number-counting';
        let foundCount = 0;
        let ttsActive = true;
        
        function toggleTTS() {
            ttsActive = !ttsActive;
            const btn = document.querySelector('.header-tts-button');
            btn.textContent = ttsActive ? '🔊' : '🔇';
            btn.style.background = ttsActive ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)';
            btn.style.borderColor = ttsActive ? 'rgba(76, 175, 80, 0.5)' : 'rgba(244, 67, 54, 0.5)';
        }
        
        function showActivity(activityId) {
            // Hide all activities
            document.querySelectorAll('.activity').forEach(el => {
                el.classList.remove('active');
            });
            
            // Show selected activity
            document.getElementById(activityId).classList.add('active');
            
            // Update button states
            document.querySelectorAll('.activity-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            if (ttsActive) {
                const utterance = new SpeechSynthesisUtterance(`Atividade alterada para: ${activities[activityId]}`);
                utterance.lang = 'pt-BR';
                speechSynthesis.speak(utterance);
            }
            
            // Reset activity states
            if (activityId === 'number-recognition') {
                resetRecognition();
            }
            
            currentActivity = activityId;
        }
        
        // Number Counting
        function selectNumber(number) {
            const options = document.querySelectorAll('#number-counting .number-option');
            options.forEach(opt => {
                opt.style.background = number === '5' ? 'rgba(76, 175, 80, 0.4)' : 'rgba(244, 67, 54, 0.4)';
                opt.style.color = 'white';
                opt.style.pointerEvents = 'none';
            });
            
            setTimeout(() => {
                options.forEach(opt => {
                    opt.style.background = 'rgba(255, 255, 255, 0.1)';
                    opt.style.color = 'white';
                    opt.style.pointerEvents = 'auto';
                });
            }, 2000);
        }
        
        // Sound Matching
        function playTargetNumber() {
            document.getElementById('sound-wave').style.display = 'block';
            if (ttsActive) {
                const utterance = new SpeechSynthesisUtterance('Cinco');
                utterance.lang = 'pt-BR';
                speechSynthesis.speak(utterance);
            }
            setTimeout(() => {
                document.getElementById('sound-wave').style.display = 'none';
            }, 2000);
        }
        
        function selectSound(number) {
            alert(`Número selecionado: ${number}`);
        }
        
        // Quantity Estimation
        function selectEstimation(number) {
            const options = document.querySelectorAll('#quantity-estimation .number-option');
            options.forEach(opt => {
                const isCorrect = opt.querySelector('.number').textContent === '8';
                opt.style.background = (number === '8' && isCorrect) ? 'rgba(76, 175, 80, 0.4)' : 'rgba(244, 67, 54, 0.4)';
                opt.style.color = 'white';
            });
        }
        
        // Number Sequence
        function selectSequence(number) {
            const options = document.querySelectorAll('#number-sequence .number-option');
            options.forEach(opt => {
                const isCorrect = opt.querySelector('.number').textContent === '8';
                opt.style.background = (number === '8' && isCorrect) ? 'rgba(76, 175, 80, 0.4)' : 'rgba(244, 67, 54, 0.4)';
                opt.style.color = 'white';
            });
        }
        
        // Magnitude Comparison
        function selectComparison(group) {
            const groups = document.querySelectorAll('#magnitude-comparison .comparison-group');
            groups.forEach(grp => {
                grp.classList.remove('selected');
            });
            
            if (group === 'B') {
                event.target.classList.add('selected');
                alert('Correto! Grupo B tem mais objetos!');
            } else {
                alert('Tente novamente! Conte os objetos em cada grupo.');
            }
        }
        
        // Number Recognition
        function toggleRecognition(element, number) {
            if (number === '7') {
                element.classList.toggle('selected');
                if (element.classList.contains('selected')) {
                    foundCount++;
                } else {
                    foundCount--;
                }
                document.getElementById('found-count').textContent = foundCount;
            } else {
                element.style.background = 'rgba(244, 67, 54, 0.4)';
                element.style.color = 'white';
                setTimeout(() => {
                    element.style.background = 'rgba(255, 255, 255, 0.1)';
                    element.style.color = 'white';
                }, 1000);
            }
        }
        
        function resetRecognition() {
            foundCount = 0;
            document.getElementById('found-count').textContent = '0';
            document.querySelectorAll('#number-recognition .number-item').forEach(item => {
                item.classList.remove('selected');
                item.style.background = item.classList.contains('target') ? 'rgba(33, 150, 243, 0.2)' : 'rgba(255, 255, 255, 0.1)';
                item.style.color = 'white';
            });
        }
        
        // Sound effects with TTS check
        function playSound(type) {
            if (ttsActive) {
                let text = '';
                switch(type) {
                    case 'count': text = 'Conte os objetos'; break;
                    default: text = 'Som do jogo';
                }
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'pt-BR';
                speechSynthesis.speak(utterance);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎮 Number Counting V3 Preview carregado!');
        });
    </script>
</body>
</html>
