<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorMatch V3 - Final</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .preview-header {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .preview-header h1 {
            margin: 0;
            color: #333;
            font-size: 2rem;
        }
        .preview-header p {
            margin: 10px 0 0 0;
            color: #666;
            font-size: 1.1rem;
        }
        .game-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
            min-height: 600px;
        }
        .status-info {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
            border-radius: 5px;
        }
        .status-info h3 {
            margin: 0 0 10px 0;
            color: #28a745;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .feature-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            font-size: 0.9rem;
        }
        .feature-item.complete {
            border-color: #28a745;
            background: #f8fff9;
        }
        .feature-item .icon {
            margin-right: 8px;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <div class="preview-header">
        <h1>🎨 ColorMatch V3 - Versão Final</h1>
        <p>Jogo de combinação de cores com formato simples e padrão Portal Betina V3</p>
    </div>

    <div class="status-info">
        <h3>✅ Status: Implementação Completa</h3>
        <p><strong>Formato:</strong> Seguindo o padrão do jogo Contagem de Números</p>
        <p><strong>Interface:</strong> GameStartScreen padrão para seleção de dificuldade</p>
        
        <div class="feature-list">
            <div class="feature-item complete">
                <span class="icon">🎮</span>
                Tela inicial padrão
            </div>
            <div class="feature-item complete">
                <span class="icon">⚙️</span>
                Seleção de dificuldade
            </div>
            <div class="feature-item complete">
                <span class="icon">🎨</span>
                Combinação de cores
            </div>
            <div class="feature-item complete">
                <span class="icon">♿</span>
                Controles de acessibilidade
            </div>
            <div class="feature-item complete">
                <span class="icon">🔊</span>
                Narração por voz
            </div>
            <div class="feature-item complete">
                <span class="icon">📊</span>
                Sistema de pontuação
            </div>
            <div class="feature-item complete">
                <span class="icon">🏆</span>
                Feedback visual/sonoro
            </div>
            <div class="feature-item complete">
                <span class="icon">💡</span>
                Sistema de dicas
            </div>
        </div>
    </div>

    <div class="game-container">
        <div id="root"></div>
    </div>

    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <script type="text/babel">
        // Simulação das dependências
        const AccessibilityContext = React.createContext({
            speak: (text, options = {}) => {
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.lang = 'pt-BR';
                    utterance.rate = options.rate || 1;
                    utterance.pitch = options.pitch || 1;
                    utterance.volume = options.volume || 1;
                    speechSynthesis.speak(utterance);
                }
            },
            stopSpeaking: () => {
                if ('speechSynthesis' in window) {
                    speechSynthesis.cancel();
                }
            }
        });

        // Hook de acessibilidade
        const useAccessibility = () => React.useContext(AccessibilityContext);

        // Hook do jogo
        const useGame = () => ({
            updateGameData: (data) => console.log('Game data updated:', data),
            endGame: () => console.log('Game ended')
        });

        // Configurações do ColorMatch
        const DIFFICULTY_CONFIGS = {
            easy: {
                name: 'Fácil',
                colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                timeLimit: 60,
                description: 'Cores básicas com mais tempo'
            },
            medium: {
                name: 'Médio', 
                colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3'],
                timeLimit: 45,
                description: 'Mais cores e menos tempo'
            },
            hard: {
                name: 'Difícil',
                colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3', '#A29BFE', '#FD79A8'],
                timeLimit: 30,
                description: 'Muitas cores e pouco tempo'
            }
        };

        // Componente GameStartScreen simplificado
        const GameStartScreen = ({ onStart, difficulties = DIFFICULTY_CONFIGS, gameTitle = "ColorMatch" }) => {
            const [selectedDifficulty, setSelectedDifficulty] = React.useState('easy');

            const handleStart = () => {
                onStart(selectedDifficulty);
            };

            return (
                <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: '400px',
                    padding: '20px',
                    textAlign: 'center'
                }}>
                    <h1 style={{ fontSize: '2.5rem', marginBottom: '30px', color: '#333' }}>
                        🎨 {gameTitle}
                    </h1>
                    
                    <div style={{ marginBottom: '30px' }}>
                        <h3 style={{ marginBottom: '20px', color: '#555' }}>Escolha a dificuldade:</h3>
                        {Object.entries(difficulties).map(([key, config]) => (
                            <label key={key} style={{
                                display: 'block',
                                margin: '10px 0',
                                padding: '15px',
                                border: selectedDifficulty === key ? '3px solid #4ECDC4' : '2px solid #ddd',
                                borderRadius: '10px',
                                cursor: 'pointer',
                                backgroundColor: selectedDifficulty === key ? '#f0fffe' : '#fff',
                                transition: 'all 0.3s ease'
                            }}>
                                <input
                                    type="radio"
                                    name="difficulty"
                                    value={key}
                                    checked={selectedDifficulty === key}
                                    onChange={(e) => setSelectedDifficulty(e.target.value)}
                                    style={{ marginRight: '10px' }}
                                />
                                <strong>{config.name}</strong>
                                <div style={{ fontSize: '0.9rem', color: '#666', marginTop: '5px' }}>
                                    {config.description}
                                </div>
                            </label>
                        ))}
                    </div>

                    <button
                        onClick={handleStart}
                        style={{
                            backgroundColor: '#4ECDC4',
                            color: 'white',
                            border: 'none',
                            padding: '15px 30px',
                            fontSize: '1.2rem',
                            borderRadius: '10px',
                            cursor: 'pointer',
                            transition: 'background-color 0.3s ease'
                        }}
                        onMouseOver={(e) => e.target.style.backgroundColor = '#45B7D1'}
                        onMouseOut={(e) => e.target.style.backgroundColor = '#4ECDC4'}
                    >
                        🚀 Começar Jogo
                    </button>
                </div>
            );
        };

        // Componente ColorMatch principal (versão simplificada para demo)
        const ColorMatchGame = () => {
            const [gameState, setGameState] = React.useState('start'); // start, playing, finished
            const [difficulty, setDifficulty] = React.useState('easy');
            const [targetColor, setTargetColor] = React.useState(null);
            const [score, setScore] = React.useState(0);
            const [timeLeft, setTimeLeft] = React.useState(60);
            const [feedback, setFeedback] = React.useState(null);
            
            const { speak } = useAccessibility();
            const { updateGameData, endGame } = useGame();

            const config = DIFFICULTY_CONFIGS[difficulty];

            const startGame = (selectedDifficulty) => {
                setDifficulty(selectedDifficulty);
                setGameState('playing');
                setScore(0);
                setTimeLeft(DIFFICULTY_CONFIGS[selectedDifficulty].timeLimit);
                generateNewTarget(selectedDifficulty);
                speak(`Jogo iniciado no nível ${DIFFICULTY_CONFIGS[selectedDifficulty].name}! Encontre a cor solicitada.`);
            };

            const generateNewTarget = (diff = difficulty) => {
                const colors = DIFFICULTY_CONFIGS[diff].colors;
                const randomColor = colors[Math.floor(Math.random() * colors.length)];
                setTargetColor(randomColor);
            };

            const handleColorSelect = (color) => {
                if (color === targetColor) {
                    setScore(prev => prev + 10);
                    setFeedback({ type: 'success', text: 'Acertou! 🎉' });
                    speak('Parabéns! Você acertou!');
                    setTimeout(() => {
                        generateNewTarget();
                        setFeedback(null);
                    }, 1000);
                } else {
                    setFeedback({ type: 'error', text: 'Tente novamente! 🤔' });
                    speak('Ops! Essa não é a cor certa. Tente novamente.');
                    setTimeout(() => setFeedback(null), 1500);
                }
            };

            const explainGame = () => {
                speak('No ColorMatch, você deve encontrar e clicar na cor que aparece destacada. Observe a cor alvo e clique na cor correspondente entre as opções.');
            };

            const announceCurrentColor = () => {
                if (targetColor) {
                    speak(`A cor alvo atual é ${targetColor}`);
                }
            };

            // Timer
            React.useEffect(() => {
                if (gameState === 'playing' && timeLeft > 0) {
                    const timer = setTimeout(() => setTimeLeft(prev => prev - 1), 1000);
                    return () => clearTimeout(timer);
                } else if (timeLeft === 0) {
                    setGameState('finished');
                    speak(`Tempo esgotado! Sua pontuação final foi ${score} pontos.`);
                }
            }, [gameState, timeLeft, score]);

            if (gameState === 'start') {
                return <GameStartScreen onStart={startGame} />;
            }

            if (gameState === 'finished') {
                return (
                    <div style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        minHeight: '400px',
                        padding: '20px',
                        textAlign: 'center'
                    }}>
                        <h1>🏆 Jogo Finalizado!</h1>
                        <p style={{ fontSize: '1.5rem', margin: '20px 0' }}>
                            Pontuação: {score} pontos
                        </p>
                        <button
                            onClick={() => setGameState('start')}
                            style={{
                                backgroundColor: '#4ECDC4',
                                color: 'white',
                                border: 'none',
                                padding: '15px 30px',
                                fontSize: '1.2rem',
                                borderRadius: '10px',
                                cursor: 'pointer'
                            }}
                        >
                            🔄 Jogar Novamente
                        </button>
                    </div>
                );
            }

            return (
                <div style={{ padding: '20px', textAlign: 'center' }}>
                    <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        marginBottom: '30px',
                        padding: '15px',
                        backgroundColor: '#f8f9fa',
                        borderRadius: '10px'
                    }}>
                        <div>Pontuação: <strong>{score}</strong></div>
                        <div>Tempo: <strong>{timeLeft}s</strong></div>
                        <div>Nível: <strong>{config.name}</strong></div>
                    </div>

                    <div style={{ marginBottom: '30px' }}>
                        <h2>🎯 Encontre esta cor:</h2>
                        {targetColor && (
                            <div style={{
                                width: '100px',
                                height: '100px',
                                backgroundColor: targetColor,
                                margin: '20px auto',
                                borderRadius: '15px',
                                border: '3px solid #333',
                                boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
                            }}></div>
                        )}
                    </div>

                    <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(80px, 1fr))',
                        gap: '15px',
                        maxWidth: '600px',
                        margin: '0 auto 30px auto'
                    }}>
                        {config.colors.map((color, index) => (
                            <button
                                key={index}
                                onClick={() => handleColorSelect(color)}
                                style={{
                                    width: '80px',
                                    height: '80px',
                                    backgroundColor: color,
                                    border: '3px solid #333',
                                    borderRadius: '12px',
                                    cursor: 'pointer',
                                    transition: 'transform 0.2s ease',
                                    boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                                }}
                                onMouseOver={(e) => e.target.style.transform = 'scale(1.1)'}
                                onMouseOut={(e) => e.target.style.transform = 'scale(1)'}
                            />
                        ))}
                    </div>

                    <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        gap: '10px',
                        flexWrap: 'wrap',
                        marginBottom: '20px'
                    }}>
                        <button onClick={explainGame} style={{
                            padding: '8px 15px',
                            backgroundColor: '#FFA726',
                            color: 'white',
                            border: 'none',
                            borderRadius: '5px',
                            cursor: 'pointer'
                        }}>
                            ❓ Explicar
                        </button>
                        <button onClick={announceCurrentColor} style={{
                            padding: '8px 15px',
                            backgroundColor: '#AB47BC',
                            color: 'white',
                            border: 'none',
                            borderRadius: '5px',
                            cursor: 'pointer'
                        }}>
                            🎨 Cor Atual
                        </button>
                    </div>

                    {feedback && (
                        <div style={{
                            padding: '15px',
                            borderRadius: '10px',
                            marginTop: '20px',
                            backgroundColor: feedback.type === 'success' ? '#d4edda' : '#f8d7da',
                            color: feedback.type === 'success' ? '#155724' : '#721c24',
                            border: `1px solid ${feedback.type === 'success' ? '#c3e6cb' : '#f5c6cb'}`
                        }}>
                            {feedback.text}
                        </div>
                    )}
                </div>
            );
        };

        // Renderizar o jogo
        const App = () => {
            return (
                <AccessibilityContext.Provider value={{
                    speak: (text, options = {}) => {
                        if ('speechSynthesis' in window) {
                            const utterance = new SpeechSynthesisUtterance(text);
                            utterance.lang = 'pt-BR';
                            utterance.rate = options.rate || 1;
                            utterance.pitch = options.pitch || 1;
                            utterance.volume = options.volume || 1;
                            speechSynthesis.speak(utterance);
                        }
                    },
                    stopSpeaking: () => {
                        if ('speechSynthesis' in window) {
                            speechSynthesis.cancel();
                        }
                    }
                }}>
                    <ColorMatchGame />
                </AccessibilityContext.Provider>
            );
        };

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
