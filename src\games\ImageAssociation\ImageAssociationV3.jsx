// ============================================================================
// IMAGE ASSOCIATION V3 - COMPONENTE PRINCIPAL
// Sistema Avançado de Análise de Associação Conceitual e Processamento Semântico
// ============================================================================

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ImageAssociationV3Config } from './ImageAssociationV3Config';
import { useMultiSensoryIntegration } from '../../hooks/useMultiSensoryIntegration';
import { useTherapeuticGameEngine } from '../../hooks/useTherapeuticGameEngine';
import { useCognitiveMetrics } from '../../hooks/useCognitiveMetrics';
import { SimpleAssociationCollector } from './collectors/SimpleAssociationCollector';
import { CategoricalAssociationCollector } from './collectors/CategoricalAssociationCollector';
import { EmotionalAssociationCollector } from './collectors/EmotionalAssociationCollector';
import { FunctionalAssociationCollector } from './collectors/FunctionalAssociationCollector';
import { ConceptualAssociationCollector } from './collectors/ConceptualAssociationCollector';
import { SequentialAssociationCollector } from './collectors/SequentialAssociationCollector';
import './ImageAssociationV3.module.css';

const ImageAssociationV3 = ({
  gameSettings = {},
  onGameComplete = () => {},
  onProgressUpdate = () => {},
  onDataCollect = () => {},
  onTherapeuticInsight = () => {},
  patientProfile = {},
  therapistMode = false
}) => {
  // ========================================================================
  // ESTADOS E HOOKS PRINCIPAIS
  // ========================================================================
  
  const [gameState, setGameState] = useState({
    currentActivity: 'simpleAssociation',
    activityIndex: 0,
    isPlaying: false,
    isPaused: false,
    isComplete: false,
    startTime: null,
    sessionData: {
      totalAssociations: 0,
      correctAssociations: 0,
      incorrectAssociations: 0,
      averageResponseTime: 0,
      cognitiveScore: 0
    }
  });

  const [activityState, setActivityState] = useState({
    currentStimuli: [],
    selectedItems: [],
    matches: [],
    errors: [],
    feedback: null,
    score: 0,
    timeElapsed: 0,
    attempts: 0
  });

  const [difficultyLevel, setDifficultyLevel] = useState('beginner');
  const [adaptiveParameters, setAdaptiveParameters] = useState({
    stimuliCount: 6,
    timeLimit: 60000,
    hintsEnabled: true,
    visualCues: true
  });

  // Hooks especializados
  const {
    playSound,
    triggerHaptic,
    showVisualCue,
    enableMultiSensory
  } = useMultiSensoryIntegration(ImageAssociationV3Config.interface.multiSensory);

  const {
    startGame,
    pauseGame,
    resumeGame,
    endGame,
    updateGameMetrics,
    getTherapeuticInsights
  } = useTherapeuticGameEngine(ImageAssociationV3Config);

  const {
    updateCognitiveMetrics,
    getCognitiveAnalysis,
    getErrorPatterns,
    getAdaptiveRecommendations
  } = useCognitiveMetrics();

  // Referências
  const gameTimerRef = useRef(null);
  const activityTimerRef = useRef(null);
  const collectorsRef = useRef({
    simpleAssociation: new SimpleAssociationCollector(),
    categoricalAssociation: new CategoricalAssociationCollector(),
    emotionalAssociation: new EmotionalAssociationCollector(),
    functionalAssociation: new FunctionalAssociationCollector(),
    conceptualAssociation: new ConceptualAssociationCollector(),
    sequentialAssociation: new SequentialAssociationCollector()
  });

  // ========================================================================
  // LISTA DAS 6 ATIVIDADES ESPECIALIZADAS
  // ========================================================================

  const activities = [
    'simpleAssociation',
    'categoricalAssociation', 
    'emotionalAssociation',
    'functionalAssociation',
    'conceptualAssociation',
    'sequentialAssociation'
  ];

  // ========================================================================
  // INICIALIZAÇÃO DO JOGO
  // ========================================================================

  useEffect(() => {
    initializeGame();
    return () => cleanup();
  }, []);

  const initializeGame = useCallback(() => {
    setGameState(prev => ({
      ...prev,
      startTime: Date.now(),
      isPlaying: false
    }));

    // Configurar parâmetros adaptativos baseados no perfil do paciente
    configureAdaptiveParameters();
    
    // Inicializar primeira atividade
    initializeActivity('simpleAssociation');
    
    // Configurar multi-sensorial
    enableMultiSensory();
    
    playSound('gameStart');
    showVisualCue('welcome');
  }, [patientProfile]);

  const configureAdaptiveParameters = () => {
    const profile = patientProfile;
    let level = 'beginner';
    
    if (profile.cognitiveLevel >= 0.7) level = 'advanced';
    else if (profile.cognitiveLevel >= 0.4) level = 'intermediate';
    
    setDifficultyLevel(level);
    
    const config = ImageAssociationV3Config.activities.simpleAssociation.difficulty[level];
    setAdaptiveParameters({
      stimuliCount: config.pairs * 2,
      timeLimit: config.timeLimit,
      hintsEnabled: config.hintsEnabled,
      visualCues: config.visualCues
    });
  };

  // ========================================================================
  // GERENCIAMENTO DE ATIVIDADES
  // ========================================================================

  const initializeActivity = useCallback((activityId) => {
    const config = ImageAssociationV3Config.activities[activityId];
    const difficulty = config.difficulty[difficultyLevel];
    
    let stimuli = [];
    
    switch (activityId) {
      case 'simpleAssociation':
        stimuli = generateSimpleAssociationStimuli(difficulty);
        break;
      case 'categoricalAssociation':
        stimuli = generateCategoricalStimuli(difficulty);
        break;
      case 'emotionalAssociation':
        stimuli = generateEmotionalStimuli(difficulty);
        break;
      case 'functionalAssociation':
        stimuli = generateFunctionalStimuli(difficulty);
        break;
      case 'conceptualAssociation':
        stimuli = generateConceptualStimuli(difficulty);
        break;
      case 'sequentialAssociation':
        stimuli = generateSequentialStimuli(difficulty);
        break;
      default:
        stimuli = [];
    }

    setActivityState({
      currentStimuli: stimuli,
      selectedItems: [],
      matches: [],
      errors: [],
      feedback: null,
      score: 0,
      timeElapsed: 0,
      attempts: 0
    });

    // Iniciar timer da atividade
    startActivityTimer(difficulty.timeLimit);
    
    // Coletar dados de início da atividade
    collectorsRef.current[activityId].collectActivityStart({
      activityId,
      difficulty: difficultyLevel,
      stimuli: stimuli.length,
      timestamp: Date.now()
    });

  }, [difficultyLevel]);

  // ========================================================================
  // GERADORES DE ESTÍMULOS ESPECIALIZADOS
  // ========================================================================

  const generateSimpleAssociationStimuli = (difficulty) => {
    const pairs = ImageAssociationV3Config.activities.simpleAssociation.stimuli.pairs;
    const selectedPairs = pairs
      .filter(pair => pair.difficulty <= (difficulty.pairs > 4 ? 3 : 2))
      .slice(0, difficulty.pairs);
    
    const stimuli = [];
    selectedPairs.forEach(pair => {
      stimuli.push(
        { id: `${pair.id}_primary`, type: 'primary', pairId: pair.id, image: pair.primary, relationship: pair.relationship },
        { id: `${pair.id}_secondary`, type: 'secondary', pairId: pair.id, image: pair.secondary, relationship: pair.relationship }
      );
    });
    
    return shuffleArray(stimuli);
  };

  const generateCategoricalStimuli = (difficulty) => {
    const categories = ImageAssociationV3Config.activities.categoricalAssociation.categories;
    const categoryKeys = Object.keys(categories).slice(0, difficulty.categories);
    
    const stimuli = [];
    categoryKeys.forEach(categoryKey => {
      const category = categories[categoryKey];
      const items = category.items.slice(0, difficulty.itemsPerCategory);
      
      items.forEach((item, index) => {
        stimuli.push({
          id: `${categoryKey}_${index}`,
          category: categoryKey,
          image: item,
          label: category.label,
          color: category.color
        });
      });
    });
    
    return shuffleArray(stimuli);
  };

  const generateEmotionalStimuli = (difficulty) => {
    const emotions = ImageAssociationV3Config.activities.emotionalAssociation.emotions;
    const emotionKeys = Object.keys(emotions).slice(0, difficulty.emotions);
    
    const stimuli = [];
    emotionKeys.forEach(emotionKey => {
      const emotion = emotions[emotionKey];
      const situations = emotion.situations.slice(0, 2);
      
      situations.forEach((situation, index) => {
        stimuli.push({
          id: `${emotionKey}_${index}`,
          emotion: emotionKey,
          image: situation,
          label: emotion.label,
          color: emotion.color,
          triggers: emotion.triggers
        });
      });
    });
    
    return shuffleArray(stimuli);
  };

  const generateFunctionalStimuli = (difficulty) => {
    const functions = ImageAssociationV3Config.activities.functionalAssociation.functions;
    const functionKeys = Object.keys(functions).slice(0, difficulty.functions);
    
    const stimuli = [];
    functionKeys.forEach(functionKey => {
      const func = functions[functionKey];
      const objects = func.objects.slice(0, 2);
      
      objects.forEach((object, index) => {
        stimuli.push({
          id: `${functionKey}_${index}`,
          function: functionKey,
          image: object,
          label: func.label,
          color: func.color,
          contexts: func.contexts
        });
      });
    });
    
    return shuffleArray(stimuli);
  };

  const generateConceptualStimuli = (difficulty) => {
    const concepts = ImageAssociationV3Config.activities.conceptualAssociation.concepts;
    const conceptKeys = Object.keys(concepts).slice(0, difficulty.concepts);
    
    const stimuli = [];
    conceptKeys.forEach(conceptKey => {
      const concept = concepts[conceptKey];
      const symbols = concept.symbols.slice(0, 2);
      
      symbols.forEach((symbol, index) => {
        stimuli.push({
          id: `${conceptKey}_${index}`,
          concept: conceptKey,
          image: symbol,
          label: concept.label,
          color: concept.color,
          metaphors: concept.metaphors,
          abstractionLevel: concept.abstractionLevel
        });
      });
    });
    
    return shuffleArray(stimuli);
  };

  const generateSequentialStimuli = (difficulty) => {
    const sequences = ImageAssociationV3Config.activities.sequentialAssociation.sequences;
    const sequenceKeys = Object.keys(sequences).slice(0, difficulty.sequences);
    
    const stimuli = [];
    sequenceKeys.forEach(sequenceKey => {
      const sequence = sequences[sequenceKey];
      const steps = sequence.steps.slice(0, difficulty.sequenceLength);
      
      steps.forEach(step => {
        stimuli.push({
          id: `${sequenceKey}_${step.order}`,
          sequence: sequenceKey,
          image: step.image,
          label: step.label,
          order: step.order,
          logicalType: sequence.logicalType
        });
      });
    });
    
    return shuffleArray(stimuli);
  };

  // ========================================================================
  // HANDLERS DE INTERAÇÃO
  // ========================================================================

  const handleItemSelect = useCallback((item) => {
    if (!gameState.isPlaying || gameState.isPaused) return;

    const startTime = Date.now();
    
    setActivityState(prev => {
      const newSelected = [...prev.selectedItems, item];
      
      // Verificar se temos itens suficientes para validar
      if (shouldValidateSelection(newSelected)) {
        const validation = validateSelection(newSelected);
        
        // Coletar dados da interação
        const interactionData = {
          selectedItems: newSelected,
          validation,
          responseTime: Date.now() - startTime,
          timestamp: Date.now(),
          activityType: gameState.currentActivity
        };
        
        collectorsRef.current[gameState.currentActivity].collectInteraction(interactionData);
        
        // Aplicar resultado
        if (validation.isCorrect) {
          handleCorrectSelection(validation);
        } else {
          handleIncorrectSelection(validation);
        }
        
        return {
          ...prev,
          selectedItems: [],
          attempts: prev.attempts + 1
        };
      }
      
      return {
        ...prev,
        selectedItems: newSelected
      };
    });

    // Feedback tátil e sonoro
    triggerHaptic('selection');
    playSound('select');
    
  }, [gameState.currentActivity, gameState.isPlaying, gameState.isPaused]);

  const shouldValidateSelection = (selectedItems) => {
    switch (gameState.currentActivity) {
      case 'simpleAssociation':
        return selectedItems.length === 2;
      case 'categoricalAssociation':
      case 'emotionalAssociation':
      case 'functionalAssociation':
      case 'conceptualAssociation':
        return selectedItems.length === 1; // Seleção + categoria
      case 'sequentialAssociation':
        return selectedItems.length === 1; // Posição + item
      default:
        return false;
    }
  };

  const validateSelection = (selectedItems) => {
    switch (gameState.currentActivity) {
      case 'simpleAssociation':
        return validateSimpleAssociation(selectedItems);
      case 'categoricalAssociation':
        return validateCategoricalAssociation(selectedItems);
      case 'emotionalAssociation':
        return validateEmotionalAssociation(selectedItems);
      case 'functionalAssociation':
        return validateFunctionalAssociation(selectedItems);
      case 'conceptualAssociation':
        return validateConceptualAssociation(selectedItems);
      case 'sequentialAssociation':
        return validateSequentialAssociation(selectedItems);
      default:
        return { isCorrect: false, reason: 'unknown_activity' };
    }
  };

  // ========================================================================
  // VALIDADORES ESPECÍFICOS
  // ========================================================================

  const validateSimpleAssociation = (items) => {
    if (items.length !== 2) return { isCorrect: false, reason: 'incomplete_selection' };
    
    const [item1, item2] = items;
    const isCorrect = item1.pairId === item2.pairId;
    
    return {
      isCorrect,
      reason: isCorrect ? 'correct_pair' : 'incorrect_pair',
      relationship: isCorrect ? item1.relationship : null,
      items: items
    };
  };

  const validateCategoricalAssociation = (items) => {
    // Implementação será completada com base na categoria selecionada
    return { isCorrect: true, reason: 'category_match' };
  };

  const validateEmotionalAssociation = (items) => {
    // Implementação será completada com base na emoção selecionada
    return { isCorrect: true, reason: 'emotion_match' };
  };

  const validateFunctionalAssociation = (items) => {
    // Implementação será completada com base na função selecionada
    return { isCorrect: true, reason: 'function_match' };
  };

  const validateConceptualAssociation = (items) => {
    // Implementação será completada com base no conceito selecionado
    return { isCorrect: true, reason: 'concept_match' };
  };

  const validateSequentialAssociation = (items) => {
    // Implementação será completada com base na sequência
    return { isCorrect: true, reason: 'sequence_correct' };
  };

  // ========================================================================
  // HANDLERS DE RESULTADO
  // ========================================================================

  const handleCorrectSelection = (validation) => {
    const activityConfig = ImageAssociationV3Config.activities[gameState.currentActivity];
    const points = activityConfig.scoring.correctMatch || 100;
    
    setActivityState(prev => ({
      ...prev,
      matches: [...prev.matches, validation],
      score: prev.score + points,
      feedback: {
        type: 'success',
        message: getFeedbackMessage('correct', validation),
        points: points
      }
    }));

    // Feedback multissensorial
    playSound('correct');
    triggerHaptic('success');
    showVisualCue('correct');

    // Atualizar métricas
    updateCognitiveMetrics('associativeSuccess', {
      activityType: gameState.currentActivity,
      responseTime: validation.responseTime,
      difficulty: difficultyLevel
    });

    // Verificar se a atividade foi completada
    checkActivityCompletion();
  };

  const handleIncorrectSelection = (validation) => {
    const activityConfig = ImageAssociationV3Config.activities[gameState.currentActivity];
    const penalty = activityConfig.scoring.incorrectMatch || -20;
    
    setActivityState(prev => ({
      ...prev,
      errors: [...prev.errors, validation],
      score: Math.max(0, prev.score + penalty),
      feedback: {
        type: 'error',
        message: getFeedbackMessage('incorrect', validation),
        penalty: penalty
      }
    }));

    // Feedback multissensorial
    playSound('incorrect');
    triggerHaptic('error');
    showVisualCue('incorrect');

    // Atualizar métricas de erro
    updateCognitiveMetrics('associativeError', {
      activityType: gameState.currentActivity,
      errorType: validation.reason,
      difficulty: difficultyLevel
    });
  };

  // ========================================================================
  // SISTEMA DE FEEDBACK
  // ========================================================================

  const getFeedbackMessage = (type, validation) => {
    const messages = {
      correct: {
        simpleAssociation: 'Excelente! Associação correta!',
        categoricalAssociation: 'Perfeito! Categoria identificada!',
        emotionalAssociation: 'Muito bem! Emoção reconhecida!',
        functionalAssociation: 'Ótimo! Função correta!',
        conceptualAssociation: 'Brilhante! Conceito abstrato!',
        sequentialAssociation: 'Fantástico! Sequência lógica!'
      },
      incorrect: {
        simpleAssociation: 'Tente novamente. Procure relações diretas!',
        categoricalAssociation: 'Categoria incorreta. Pense no grupo!',
        emotionalAssociation: 'Emoção incorreta. Que sentimento evoca?',
        functionalAssociation: 'Função incorreta. Para que serve?',
        conceptualAssociation: 'Conceito incorreto. Pense simbolicamente!',
        sequentialAssociation: 'Sequência incorreta. Qual a ordem lógica?'
      }
    };

    return messages[type][gameState.currentActivity] || 'Continue tentando!';
  };

  // ========================================================================
  // CONTROLE DE FLUXO
  // ========================================================================

  const checkActivityCompletion = () => {
    const currentConfig = ImageAssociationV3Config.activities[gameState.currentActivity];
    const difficulty = currentConfig.difficulty[difficultyLevel];
    
    // Critérios de conclusão variam por atividade
    let isComplete = false;
    
    switch (gameState.currentActivity) {
      case 'simpleAssociation':
        isComplete = activityState.matches.length >= difficulty.pairs;
        break;
      default:
        isComplete = activityState.attempts >= 5; // Critério temporário
    }

    if (isComplete) {
      completeActivity();
    }
  };

  const completeActivity = () => {
    // Parar timer da atividade
    if (activityTimerRef.current) {
      clearInterval(activityTimerRef.current);
    }

    // Coletar dados finais da atividade
    const activityData = {
      activityId: gameState.currentActivity,
      difficulty: difficultyLevel,
      score: activityState.score,
      matches: activityState.matches.length,
      errors: activityState.errors.length,
      attempts: activityState.attempts,
      timeElapsed: activityState.timeElapsed,
      cognitiveMetrics: getCognitiveAnalysis()
    };

    collectorsRef.current[gameState.currentActivity].collectActivityEnd(activityData);
    onDataCollect(activityData);

    // Avançar para próxima atividade ou finalizar
    const nextActivityIndex = gameState.activityIndex + 1;
    
    if (nextActivityIndex < activities.length) {
      setTimeout(() => {
        setGameState(prev => ({
          ...prev,
          currentActivity: activities[nextActivityIndex],
          activityIndex: nextActivityIndex
        }));
        
        initializeActivity(activities[nextActivityIndex]);
      }, 2000);
    } else {
      completeGame();
    }
  };

  const completeGame = () => {
    setGameState(prev => ({ ...prev, isComplete: true, isPlaying: false }));
    
    if (gameTimerRef.current) {
      clearInterval(gameTimerRef.current);
    }

    // Gerar relatório final
    const finalReport = generateFinalReport();
    
    onGameComplete(finalReport);
    onTherapeuticInsight(getTherapeuticInsights());
    
    playSound('gameComplete');
    showVisualCue('completion');
  };

  // ========================================================================
  // UTILITÁRIOS
  // ========================================================================

  const shuffleArray = (array) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  const startActivityTimer = (timeLimit) => {
    if (activityTimerRef.current) {
      clearInterval(activityTimerRef.current);
    }

    activityTimerRef.current = setInterval(() => {
      setActivityState(prev => {
        const newTimeElapsed = prev.timeElapsed + 1000;
        
        if (newTimeElapsed >= timeLimit) {
          handleTimeUp();
          return prev;
        }
        
        return { ...prev, timeElapsed: newTimeElapsed };
      });
    }, 1000);
  };

  const handleTimeUp = () => {
    setActivityState(prev => ({
      ...prev,
      feedback: {
        type: 'warning',
        message: 'Tempo esgotado! Vamos para a próxima atividade.',
        points: 0
      }
    }));

    setTimeout(() => {
      completeActivity();
    }, 2000);
  };

  const generateFinalReport = () => {
    return {
      gameId: ImageAssociationV3Config.gameId,
      version: ImageAssociationV3Config.version,
      sessionDuration: Date.now() - gameState.startTime,
      activitiesCompleted: gameState.activityIndex + 1,
      totalScore: activities.reduce((sum, activity) => {
        const collector = collectorsRef.current[activity];
        return sum + (collector.getActivityScore() || 0);
      }, 0),
      cognitiveAnalysis: getCognitiveAnalysis(),
      errorPatterns: getErrorPatterns(),
      adaptiveRecommendations: getAdaptiveRecommendations(),
      therapeuticInsights: getTherapeuticInsights()
    };
  };

  const cleanup = () => {
    if (gameTimerRef.current) clearInterval(gameTimerRef.current);
    if (activityTimerRef.current) clearInterval(activityTimerRef.current);
  };

  // ========================================================================
  // CONTROLES DE JOGO
  // ========================================================================

  const handleStartGame = () => {
    setGameState(prev => ({ ...prev, isPlaying: true }));
    startGame();
    playSound('gameStart');
  };

  const handlePauseGame = () => {
    setGameState(prev => ({ ...prev, isPaused: !prev.isPaused }));
    if (gameState.isPaused) {
      resumeGame();
    } else {
      pauseGame();
    }
  };

  const handleResetGame = () => {
    setGameState({
      currentActivity: 'simpleAssociation',
      activityIndex: 0,
      isPlaying: false,
      isPaused: false,
      isComplete: false,
      startTime: null,
      sessionData: {
        totalAssociations: 0,
        correctAssociations: 0,
        incorrectAssociations: 0,
        averageResponseTime: 0,
        cognitiveScore: 0
      }
    });
    
    initializeActivity('simpleAssociation');
  };

  // ========================================================================
  // RENDERIZAÇÃO
  // ========================================================================

  return (
    <div className="image-association-v3">
      <div className="game-header">
        <h1 className="game-title">
          {ImageAssociationV3Config.activities[gameState.currentActivity]?.icon} 
          {ImageAssociationV3Config.activities[gameState.currentActivity]?.name}
        </h1>
        <div className="game-controls">
          {!gameState.isPlaying && !gameState.isComplete && (
            <button onClick={handleStartGame} className="btn-start">
              ▶️ Iniciar
            </button>
          )}
          {gameState.isPlaying && (
            <button onClick={handlePauseGame} className="btn-pause">
              {gameState.isPaused ? '▶️ Continuar' : '⏸️ Pausar'}
            </button>
          )}
          <button onClick={handleResetGame} className="btn-reset">
            🔄 Reiniciar
          </button>
        </div>
      </div>

      <div className="game-stats">
        <div className="stat-item">
          <span className="stat-label">Atividade</span>
          <span className="stat-value">{gameState.activityIndex + 1}/6</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Pontuação</span>
          <span className="stat-value">{activityState.score}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Tempo</span>
          <span className="stat-value">
            {Math.floor(activityState.timeElapsed / 1000)}s
          </span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Tentativas</span>
          <span className="stat-value">{activityState.attempts}</span>
        </div>
      </div>

      {activityState.feedback && (
        <div className={`feedback-message ${activityState.feedback.type}`}>
          {activityState.feedback.message}
          {activityState.feedback.points && (
            <span className="points">+{activityState.feedback.points}</span>
          )}
        </div>
      )}

      <div className="game-area">
        <div className="stimuli-container">
          {activityState.currentStimuli.map(stimulus => (
            <div
              key={stimulus.id}
              className={`stimulus-item ${
                activityState.selectedItems.includes(stimulus) ? 'selected' : ''
              }`}
              onClick={() => handleItemSelect(stimulus)}
            >
              <div className="stimulus-image">{stimulus.image}</div>
              {adaptiveParameters.visualCues && (
                <div className="stimulus-label">{stimulus.label}</div>
              )}
            </div>
          ))}
        </div>

        {gameState.currentActivity === 'categoricalAssociation' && (
          <div className="category-slots">
            {/* Renderizar slots de categoria */}
          </div>
        )}

        {gameState.currentActivity === 'sequentialAssociation' && (
          <div className="sequence-area">
            {/* Renderizar área de sequência */}
          </div>
        )}
      </div>

      {gameState.isComplete && (
        <div className="completion-screen">
          <h2>🎉 Parabéns! Jogo Completado!</h2>
          <div className="final-stats">
            {/* Renderizar estatísticas finais */}
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageAssociationV3;
