// ============================================================================
// FUNCTIONAL ASSOCIATION COLLECTOR - ATIVIDADE 4
// Coleta e análise de dados para associação funcional e raciocínio pragmático
// ============================================================================

import { BaseCollector } from '../../../utils/BaseCollector.js';

export class FunctionalAssociationCollector extends BaseCollector {
  constructor() {
    super('FunctionalAssociation');
    
    this.cognitiveMetrics = {
      // Métricas específicas de raciocínio funcional
      functionalReasoning: [],
      pragmaticThinking: [],
      purposeRecognition: [],
      toolCognition: [],
      functionalTransfer: [],
      
      // Padrões funcionais
      functionalCategories: [],
      affordancePerception: [],
      contextualUsage: [],
      functionalFlexibility: [],
      
      // Análise de conhecimento prático
      practicalKnowledge: [],
      proceduraMemory: [],
      functionalMapping: [],
      goalDirectedThinking: []
    };

    this.functionalDomains = {
      cutting: {
        purpose: 'separation',
        context: ['kitchen', 'craft', 'garden', 'office'],
        affordances: ['sharp_edge', 'handle', 'leverage'],
        objects: ['✂️', '🔪', '🪚', '⚔️', '💎'],
        complexity: 'medium',
        skills: ['precision', 'force_control', 'safety']
      },
      writing: {
        purpose: 'communication',
        context: ['school', 'office', 'art', 'documentation'],
        affordances: ['mark_making', 'grip', 'precision'],
        objects: ['✏️', '🖊️', '🖌️', '🖍️', '⌨️'],
        complexity: 'low',
        skills: ['fine_motor', 'cognitive', 'symbolic']
      },
      measuring: {
        purpose: 'quantification',
        context: ['construction', 'science', 'cooking', 'health'],
        affordances: ['scale', 'reference', 'precision'],
        objects: ['📏', '📐', '⚖️', '🌡️', '⏰'],
        complexity: 'high',
        skills: ['mathematical', 'analytical', 'comparative']
      },
      communication: {
        purpose: 'information_exchange',
        context: ['personal', 'business', 'emergency', 'entertainment'],
        affordances: ['signal_transmission', 'interface', 'connectivity'],
        objects: ['📱', '📞', '📻', '📺', '💻'],
        complexity: 'medium',
        skills: ['social', 'technological', 'linguistic']
      },
      transportation: {
        purpose: 'mobility',
        context: ['daily_commute', 'travel', 'cargo', 'recreation'],
        affordances: ['movement', 'carrying_capacity', 'speed'],
        objects: ['🚗', '🚲', '✈️', '🚢', '🛴'],
        complexity: 'medium',
        skills: ['coordination', 'navigation', 'operation']
      },
      protection: {
        purpose: 'safety',
        context: ['work', 'weather', 'sports', 'hazards'],
        affordances: ['barrier', 'shielding', 'durability'],
        objects: ['⛑️', '👓', '🧤', '☂️', '🦺'],
        complexity: 'low',
        skills: ['risk_assessment', 'prevention', 'adaptation']
      }
    };

    this.sessionData = {
      startTime: null,
      endTime: null,
      totalFunctionalAssociations: 0,
      correctFunctionalAssociations: 0,
      incorrectFunctionalAssociations: 0,
      averageFunctionalProcessingTime: 0,
      functionsByAccuracy: {},
      functionalConfusions: {},
      affordanceRecognition: 0,
      pragmaticScore: 0,
      difficultyLevel: 'beginner'
    };
  }

  // ========================================================================
  // COLETA DE DADOS DE ASSOCIAÇÃO FUNCIONAL
  // ========================================================================

  collectFunctionalAssociation(associationData) {
    const {
      selectedObject,
      targetFunction,
      actualFunction,
      responseTime,
      timestamp,
      contextualClues = [],
      affordancesIdentified = [],
      functionalJustification = null
    } = associationData;

    const isCorrect = targetFunction === actualFunction;
    
    const association = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedObject: {
        id: selectedObject.id,
        image: selectedObject.image,
        actualFunction: actualFunction,
        affordances: this.identifyAffordances(selectedObject),
        contexts: this.extractContexts(selectedObject)
      },
      targetFunction,
      isCorrect,
      functionalAnalysis: {
        purpose: this.functionalDomains[actualFunction]?.purpose,
        complexity: this.functionalDomains[actualFunction]?.complexity,
        skillsRequired: this.functionalDomains[actualFunction]?.skills
      },
      context: {
        contextualClues,
        affordancesIdentified,
        functionalJustification,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };

    this.interactions.push(association);

    // Análises cognitivas especializadas
    this.analyzeFunctionalReasoning(association);
    this.analyzePragmaticThinking(association);
    this.analyzeAffordancePerception(association);
    this.analyzeToolCognition(association);
    
    // Atualizar métricas de sessão
    this.updateSessionMetrics(association);

    return association;
  }

  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================

  analyzeFunctionalReasoning(association) {
    const { responseTime, isCorrect, targetFunction, selectedObject } = association;
    
    const reasoningMetric = {
      timestamp: association.timestamp,
      function: targetFunction,
      object: selectedObject.image,
      recognized: isCorrect,
      responseTime,
      functionalComplexity: this.calculateFunctionalComplexity(targetFunction),
      reasoningAccuracy: isCorrect ? 1 : 0,
      functionalSalience: this.calculateFunctionalSalience(selectedObject, targetFunction)
    };

    this.cognitiveMetrics.functionalReasoning.push(reasoningMetric);

    // Análise de reconhecimento de propósito
    const purposeMetric = {
      timestamp: association.timestamp,
      function: targetFunction,
      purpose: association.functionalAnalysis.purpose,
      purposeRecognized: isCorrect,
      purposeClarity: this.assessPurposeClarity(association),
      goalAlignment: this.assessGoalAlignment(association)
    };

    this.cognitiveMetrics.purposeRecognition.push(purposeMetric);
  }

  analyzePragmaticThinking(association) {
    const { targetFunction, selectedObject, context } = association;
    
    const pragmaticMetric = {
      timestamp: association.timestamp,
      function: targetFunction,
      contextualAdaptation: this.assessContextualAdaptation(association),
      practicalApplication: this.assessPracticalApplication(association),
      situationalRelevance: this.assessSituationalRelevance(association),
      pragmaticFlexibility: this.assessPragmaticFlexibility(association)
    };

    this.cognitiveMetrics.pragmaticThinking.push(pragmaticMetric);
  }

  analyzeAffordancePerception(association) {
    const { selectedObject, context, isCorrect } = association;
    
    const affordanceMetric = {
      timestamp: association.timestamp,
      object: selectedObject.image,
      affordancesIdentified: context.affordancesIdentified,
      affordancesActual: selectedObject.affordances,
      affordanceAccuracy: this.calculateAffordanceAccuracy(context.affordancesIdentified, selectedObject.affordances),
      perceptualSensitivity: this.assessPerceptualSensitivity(association),
      functionalPerception: isCorrect ? 1 : 0
    };

    this.cognitiveMetrics.affordancePerception.push(affordanceMetric);
  }

  analyzeToolCognition(association) {
    const { targetFunction, selectedObject, isCorrect } = association;
    
    const toolMetric = {
      timestamp: association.timestamp,
      tool: selectedObject.image,
      function: targetFunction,
      toolRecognition: isCorrect,
      toolComplexity: this.calculateToolComplexity(selectedObject),
      functionalMapping: this.assessFunctionalMapping(association),
      toolUseKnowledge: this.assessToolUseKnowledge(association)
    };

    this.cognitiveMetrics.toolCognition.push(toolMetric);
  }

  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS
  // ========================================================================

  calculateFunctionalComplexity(functionType) {
    const complexityMap = {
      cutting: 0.6,
      writing: 0.3,
      measuring: 0.8,
      communication: 0.5,
      transportation: 0.6,
      protection: 0.4
    };

    return complexityMap[functionType] || 0.5;
  }

  calculateFunctionalSalience(object, targetFunction) {
    // Quão saliente é a função no objeto
    const objectAffordances = this.identifyAffordances(object);
    const functionAffordances = this.functionalDomains[targetFunction]?.affordances || [];
    
    const overlap = objectAffordances.filter(affordance => 
      functionAffordances.includes(affordance)
    ).length;
    
    return overlap / Math.max(functionAffordances.length, 1);
  }

  assessPurposeClarity(association) {
    const { targetFunction, selectedObject } = association;
    const functionPurpose = this.functionalDomains[targetFunction]?.purpose;
    
    // Clareza baseada na especificidade da função
    const purposeSpecificity = {
      'separation': 0.8,
      'communication': 0.6,
      'quantification': 0.9,
      'information_exchange': 0.5,
      'mobility': 0.7,
      'safety': 0.8
    };

    return purposeSpecificity[functionPurpose] || 0.5;
  }

  assessGoalAlignment(association) {
    const { isCorrect, targetFunction, selectedObject } = association;
    
    if (!isCorrect) return 0;
    
    // Alinhamento entre objeto e objetivo funcional
    const objectContexts = this.extractContexts(selectedObject);
    const functionContexts = this.functionalDomains[targetFunction]?.context || [];
    
    const contextOverlap = objectContexts.filter(context => 
      functionContexts.includes(context)
    ).length;
    
    return contextOverlap / Math.max(functionContexts.length, 1);
  }

  assessContextualAdaptation(association) {
    const { context, targetFunction } = association;
    const functionContexts = this.functionalDomains[targetFunction]?.context || [];
    
    // Adaptação às pistas contextuais fornecidas
    const contextualClues = context.contextualClues;
    const relevantClues = contextualClues.filter(clue => 
      functionContexts.some(context => context.includes(clue))
    );

    return contextualClues.length > 0 ? relevantClues.length / contextualClues.length : 0.5;
  }

  assessPracticalApplication(association) {
    const { isCorrect, targetFunction, context } = association;
    
    let practicalScore = isCorrect ? 0.6 : 0;
    
    // Se forneceu justificativa prática
    if (context.functionalJustification) {
      practicalScore += 0.2;
    }

    // Se identificou affordances corretamente
    const affordanceAccuracy = this.calculateAffordanceAccuracy(
      context.affordancesIdentified, 
      association.selectedObject.affordances
    );
    practicalScore += affordanceAccuracy * 0.2;

    return Math.min(1.0, practicalScore);
  }

  assessSituationalRelevance(association) {
    const { targetFunction, selectedObject } = association;
    
    // Relevância situacional baseada em contextos compartilhados
    const objectContexts = this.extractContexts(selectedObject);
    const functionContexts = this.functionalDomains[targetFunction]?.context || [];
    
    const intersection = objectContexts.filter(context => 
      functionContexts.includes(context)
    );

    const union = [...new Set([...objectContexts, ...functionContexts])];
    
    return union.length > 0 ? intersection.length / union.length : 0;
  }

  assessPragmaticFlexibility(association) {
    // Flexibilidade no uso funcional
    const recentAssociations = this.interactions.slice(-5);
    const functionTypes = recentAssociations.map(a => a.targetFunction);
    const uniqueFunctions = new Set(functionTypes);
    
    // Flexibilidade baseada na variedade de funções utilizadas
    return uniqueFunctions.size / Math.min(functionTypes.length, 6);
  }

  calculateAffordanceAccuracy(identifiedAffordances, actualAffordances) {
    if (actualAffordances.length === 0) return 0;
    
    const correctAffordances = identifiedAffordances.filter(affordance => 
      actualAffordances.includes(affordance)
    );

    return correctAffordances.length / actualAffordances.length;
  }

  assessPerceptualSensitivity(association) {
    const { selectedObject, context } = association;
    const actualAffordances = selectedObject.affordances;
    const identifiedAffordances = context.affordancesIdentified;
    
    // Sensibilidade = capacidade de detectar affordances sutis
    const subtleAffordances = actualAffordances.filter(affordance => 
      !['handle', 'sharp_edge', 'button'].includes(affordance)
    );

    const detectedSubtle = identifiedAffordances.filter(affordance => 
      subtleAffordances.includes(affordance)
    );

    return subtleAffordances.length > 0 ? detectedSubtle.length / subtleAffordances.length : 0.5;
  }

  calculateToolComplexity(object) {
    const complexityMap = {
      '✂️': 0.4, '🔪': 0.5, '🪚': 0.7, '⚔️': 0.8, '💎': 0.3,
      '✏️': 0.2, '🖊️': 0.3, '🖌️': 0.4, '🖍️': 0.2, '⌨️': 0.6,
      '📏': 0.3, '📐': 0.5, '⚖️': 0.7, '🌡️': 0.6, '⏰': 0.8,
      '📱': 0.8, '📞': 0.4, '📻': 0.5, '📺': 0.6, '💻': 0.9,
      '🚗': 0.9, '🚲': 0.4, '✈️': 1.0, '🚢': 0.8, '🛴': 0.3,
      '⛑️': 0.3, '👓': 0.2, '🧤': 0.2, '☂️': 0.3, '🦺': 0.4
    };

    return complexityMap[object.image] || 0.5;
  }

  assessFunctionalMapping(association) {
    const { isCorrect, targetFunction, selectedObject } = association;
    
    if (!isCorrect) {
      // Analisar o tipo de mapeamento incorreto
      const actualFunction = selectedObject.actualFunction;
      const mappingError = this.classifyMappingError(targetFunction, actualFunction);
      
      return {
        accuracy: 0,
        errorType: mappingError,
        mappingDistance: this.calculateMappingDistance(targetFunction, actualFunction)
      };
    }

    return {
      accuracy: 1,
      errorType: 'none',
      mappingDistance: 0
    };
  }

  assessToolUseKnowledge(association) {
    const { targetFunction, selectedObject, isCorrect } = association;
    
    let knowledgeScore = isCorrect ? 0.7 : 0;
    
    // Conhecimento das habilidades necessárias
    const requiredSkills = this.functionalDomains[targetFunction]?.skills || [];
    const demonstratedSkills = this.inferDemonstratedSkills(association);
    
    const skillOverlap = demonstratedSkills.filter(skill => 
      requiredSkills.includes(skill)
    ).length;
    
    knowledgeScore += (skillOverlap / Math.max(requiredSkills.length, 1)) * 0.3;
    
    return Math.min(1.0, knowledgeScore);
  }

  classifyMappingError(targetFunction, actualFunction) {
    // Classificar tipos de erro no mapeamento funcional
    const targetDomain = this.functionalDomains[targetFunction];
    const actualDomain = this.functionalDomains[actualFunction];
    
    if (!targetDomain || !actualDomain) return 'unknown_mapping';
    
    // Mesmo propósito, função diferente
    if (targetDomain.purpose === actualDomain.purpose) {
      return 'purpose_confusion';
    }
    
    // Contextos sobrepostos
    const contextOverlap = targetDomain.context.filter(context => 
      actualDomain.context.includes(context)
    ).length;
    
    if (contextOverlap > 0) return 'context_confusion';
    
    // Affordances similares
    const affordanceOverlap = targetDomain.affordances.filter(affordance => 
      actualDomain.affordances.includes(affordance)
    ).length;
    
    if (affordanceOverlap > 0) return 'affordance_confusion';
    
    return 'distant_mapping';
  }

  calculateMappingDistance(targetFunction, actualFunction) {
    const targetDomain = this.functionalDomains[targetFunction];
    const actualDomain = this.functionalDomains[actualFunction];
    
    if (!targetDomain || !actualDomain) return 1.0;
    
    // Distância baseada em sobreposição de características
    const purposeDistance = targetDomain.purpose === actualDomain.purpose ? 0 : 0.4;
    
    const contextOverlap = targetDomain.context.filter(context => 
      actualDomain.context.includes(context)
    ).length;
    const contextDistance = (1 - contextOverlap / Math.max(targetDomain.context.length, actualDomain.context.length)) * 0.3;
    
    const affordanceOverlap = targetDomain.affordances.filter(affordance => 
      actualDomain.affordances.includes(affordance)
    ).length;
    const affordanceDistance = (1 - affordanceOverlap / Math.max(targetDomain.affordances.length, actualDomain.affordances.length)) * 0.3;
    
    return purposeDistance + contextDistance + affordanceDistance;
  }

  inferDemonstratedSkills(association) {
    const { responseTime, context, isCorrect } = association;
    const skills = [];
    
    // Habilidade analítica (baseada em justificativa)
    if (context.functionalJustification && context.functionalJustification.length > 10) {
      skills.push('analytical');
    }
    
    // Velocidade cognitiva
    if (responseTime < 4000) {
      skills.push('cognitive');
    }
    
    // Percepção detalhada (baseada em affordances identificadas)
    if (context.affordancesIdentified.length > 2) {
      skills.push('precision');
    }
    
    // Conhecimento prático (precisão)
    if (isCorrect) {
      skills.push('practical');
    }
    
    return skills;
  }

  identifyAffordances(object) {
    const affordanceMap = {
      '✂️': ['sharp_edge', 'handle', 'pivot', 'grip'],
      '🔪': ['sharp_edge', 'handle', 'blade', 'weight'],
      '🪚': ['teeth', 'handle', 'cutting_edge', 'leverage'],
      '⚔️': ['sharp_edge', 'handle', 'point', 'balance'],
      '💎': ['hardness', 'cutting_edge', 'precision', 'durability'],
      '✏️': ['mark_making', 'grip', 'erasable', 'portable'],
      '🖊️': ['mark_making', 'grip', 'permanent', 'precision'],
      '🖌️': ['mark_making', 'grip', 'flexible', 'artistic'],
      '🖍️': ['mark_making', 'grip', 'colorful', 'waxy'],
      '⌨️': ['input', 'tactile', 'digital', 'layout'],
      '📏': ['measurement', 'straight_edge', 'markings', 'rigid'],
      '📐': ['angle_measurement', 'triangular', 'markings', 'precision'],
      '⚖️': ['balance', 'comparison', 'precision', 'calibration'],
      '🌡️': ['temperature_sensing', 'calibration', 'reading', 'sensitive'],
      '⏰': ['time_display', 'alarm', 'precision', 'regulation'],
      '📱': ['communication', 'interface', 'portable', 'connectivity'],
      '📞': ['communication', 'handset', 'audio', 'dial'],
      '📻': ['audio', 'reception', 'tuning', 'portable'],
      '📺': ['visual', 'audio', 'reception', 'display'],
      '💻': ['computing', 'interface', 'processing', 'connectivity'],
      '🚗': ['transportation', 'enclosed', 'steering', 'powered'],
      '🚲': ['transportation', 'pedal_powered', 'steering', 'balance'],
      '✈️': ['flight', 'high_speed', 'navigation', 'capacity'],
      '🚢': ['water_transport', 'large_capacity', 'navigation', 'buoyancy'],
      '🛴': ['transportation', 'push_powered', 'balance', 'portable'],
      '⛑️': ['protection', 'head_coverage', 'hard_shell', 'fitting'],
      '👓': ['vision_correction', 'transparent', 'fitting', 'lightweight'],
      '🧤': ['hand_protection', 'grip', 'warmth', 'flexibility'],
      '☂️': ['weather_protection', 'expandable', 'portable', 'waterproof'],
      '🦺': ['visibility', 'protection', 'reflective', 'wearable']
    };

    return affordanceMap[object.image] || [];
  }

  extractContexts(object) {
    const contextMap = {
      '✂️': ['office', 'craft', 'household'],
      '🔪': ['kitchen', 'cooking', 'food_prep'],
      '🪚': ['construction', 'woodworking', 'crafts'],
      '⚔️': ['historical', 'ceremonial', 'combat'],
      '💎': ['jewelry', 'industrial', 'cutting'],
      '✏️': ['school', 'office', 'drawing'],
      '🖊️': ['office', 'writing', 'documentation'],
      '🖌️': ['art', 'painting', 'creative'],
      '🖍️': ['school', 'children', 'coloring'],
      '⌨️': ['computing', 'office', 'digital'],
      '📏': ['school', 'construction', 'measurement'],
      '📐': ['school', 'technical_drawing', 'mathematics'],
      '⚖️': ['legal', 'science', 'cooking'],
      '🌡️': ['medical', 'weather', 'science'],
      '⏰': ['household', 'scheduling', 'time_management'],
      '📱': ['personal', 'business', 'social'],
      '📞': ['office', 'household', 'emergency'],
      '📻': ['entertainment', 'news', 'emergency'],
      '📺': ['entertainment', 'household', 'information'],
      '💻': ['work', 'education', 'entertainment'],
      '🚗': ['transportation', 'daily_commute', 'travel'],
      '🚲': ['recreation', 'exercise', 'eco_transport'],
      '✈️': ['travel', 'business', 'vacation'],
      '🚢': ['cargo', 'cruise', 'transportation'],
      '🛴': ['recreation', 'urban_transport', 'children'],
      '⛑️': ['construction', 'safety', 'industrial'],
      '👓': ['vision', 'medical', 'daily_use'],
      '🧤': ['work', 'weather', 'protection'],
      '☂️': ['weather', 'outdoor', 'travel'],
      '🦺': ['safety', 'work', 'visibility']
    };

    return contextMap[object.image] || [];
  }

  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================

  updateSessionMetrics(association) {
    const { isCorrect, responseTime, targetFunction } = association;

    this.sessionData.totalFunctionalAssociations++;
    
    if (isCorrect) {
      this.sessionData.correctFunctionalAssociations++;
    } else {
      this.sessionData.incorrectFunctionalAssociations++;
      
      // Rastrear confusões funcionais
      const actualFunction = association.selectedObject.actualFunction;
      const confusionKey = `${targetFunction}->${actualFunction}`;
      
      if (!this.sessionData.functionalConfusions[confusionKey]) {
        this.sessionData.functionalConfusions[confusionKey] = 0;
      }
      this.sessionData.functionalConfusions[confusionKey]++;
    }

    // Atualizar precisão por função
    if (!this.sessionData.functionsByAccuracy[targetFunction]) {
      this.sessionData.functionsByAccuracy[targetFunction] = { total: 0, correct: 0 };
    }
    
    this.sessionData.functionsByAccuracy[targetFunction].total++;
    if (isCorrect) {
      this.sessionData.functionsByAccuracy[targetFunction].correct++;
    }

    // Atualizar tempo médio
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageFunctionalProcessingTime = totalTime / this.interactions.length;

    // Calcular reconhecimento de affordances
    this.updateAffordanceRecognition();
    
    // Calcular score pragmático
    this.updatePragmaticScore();
  }

  updateAffordanceRecognition() {
    const affordanceMetrics = this.cognitiveMetrics.affordancePerception;
    
    if (affordanceMetrics.length === 0) {
      this.sessionData.affordanceRecognition = 0;
      return;
    }

    const totalAccuracy = affordanceMetrics.reduce((sum, metric) => 
      sum + metric.affordanceAccuracy, 0
    );

    this.sessionData.affordanceRecognition = totalAccuracy / affordanceMetrics.length;
  }

  updatePragmaticScore() {
    const pragmaticMetrics = this.cognitiveMetrics.pragmaticThinking;
    
    if (pragmaticMetrics.length === 0) {
      this.sessionData.pragmaticScore = 0;
      return;
    }

    const totalAdaptation = pragmaticMetrics.reduce((sum, metric) => 
      sum + metric.contextualAdaptation, 0
    );
    
    const totalApplication = pragmaticMetrics.reduce((sum, metric) => 
      sum + metric.practicalApplication, 0
    );

    const totalRelevance = pragmaticMetrics.reduce((sum, metric) => 
      sum + metric.situationalRelevance, 0
    );

    this.sessionData.pragmaticScore = (
      (totalAdaptation / pragmaticMetrics.length) * 0.3 +
      (totalApplication / pragmaticMetrics.length) * 0.4 +
      (totalRelevance / pragmaticMetrics.length) * 0.3
    );
  }

  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================

  generateCognitiveReport() {
    return {
      functionalReasoning: this.analyzeFunctionalReasoning(),
      pragmaticThinking: this.analyzePragmaticThinking(),
      toolCognition: this.analyzeToolCognition(),
      affordancePerception: this.analyzeAffordancePerception(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }

  assessComplexityHandling() {
    const complexityPerformance = {};
    
    this.interactions.forEach(interaction => {
      const func = interaction.targetFunction;
      const complexity = this.functionalDomains[func]?.complexity;
      
      if (!complexityPerformance[complexity]) {
        complexityPerformance[complexity] = { total: 0, correct: 0 };
      }
      
      complexityPerformance[complexity].total++;
      if (interaction.isCorrect) {
        complexityPerformance[complexity].correct++;
      }
    });

    Object.keys(complexityPerformance).forEach(complexity => {
      const data = complexityPerformance[complexity];
      complexityPerformance[complexity].accuracy = data.correct / data.total;
    });

    return complexityPerformance;
  }

  analyzeFunctionalMapping() {
    const mappingAnalysis = {
      correctMappings: 0,
      mappingErrors: {},
      averageMappingDistance: 0
    };

    this.interactions.forEach(interaction => {
      if (interaction.isCorrect) {
        mappingAnalysis.correctMappings++;
      } else {
        const errorType = this.classifyMappingError(
          interaction.targetFunction, 
          interaction.selectedObject.actualFunction
        );
        
        mappingAnalysis.mappingErrors[errorType] = 
          (mappingAnalysis.mappingErrors[errorType] || 0) + 1;
      }
    });

    const toolMetrics = this.cognitiveMetrics.toolCognition;
    if (toolMetrics.length > 0) {
      const totalDistance = toolMetrics.reduce((sum, metric) => {
        return sum + (metric.functionalMapping.mappingDistance || 0);
      }, 0);
      
      mappingAnalysis.averageMappingDistance = totalDistance / toolMetrics.length;
    }

    return mappingAnalysis;
  }

  generateAdaptiveRecommendations() {
    const recommendations = [];
    
    // Análise por função
    const functionPerformance = {};
    Object.entries(this.sessionData.functionsByAccuracy).forEach(([func, data]) => {
      functionPerformance[func] = data.correct / data.total;
    });

    const weakFunctions = Object.entries(functionPerformance)
      .filter(([func, accuracy]) => accuracy < 0.6)
      .map(([func]) => func);

    if (weakFunctions.length > 0) {
      recommendations.push({
        type: 'functional_training',
        recommendation: `Reforçar funções: ${weakFunctions.join(', ')}`,
        confidence: 0.8,
        details: {
          functions: weakFunctions,
          suggestedActivities: ['tool_exploration', 'context_training', 'affordance_highlighting']
        }
      });
    }

    // Análise de affordances
    if (this.sessionData.affordanceRecognition < 0.5) {
      recommendations.push({
        type: 'affordance_training',
        recommendation: 'Exercícios de percepção de affordances',
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.affordanceRecognition,
          suggestedActivities: ['perceptual_training', 'feature_analysis', 'hands_on_exploration']
        }
      });
    }

    // Análise pragmática
    if (this.sessionData.pragmaticScore < 0.6) {
      recommendations.push({
        type: 'pragmatic_training',
        recommendation: 'Desenvolvimento de pensamento pragmático',
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.pragmaticScore,
          suggestedActivities: ['contextual_exercises', 'problem_solving', 'real_world_applications']
        }
      });
    }

    return recommendations;
  }

  getActivityScore() {
    if (this.sessionData.totalFunctionalAssociations === 0) return 0;
    
    const accuracy = this.sessionData.correctFunctionalAssociations / this.sessionData.totalFunctionalAssociations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageFunctionalProcessingTime - 5000) / 10000);
    const affordanceFactor = this.sessionData.affordanceRecognition;
    const pragmaticFactor = this.sessionData.pragmaticScore;
    
    return Math.round(accuracy * speedFactor * affordanceFactor * pragmaticFactor * 1000);
  }
}

export default FunctionalAssociationCollector;
