import bcrypt from 'bcrypt';

const password = 'admin123';
const hash = '$2b$12$LQv3c1yqBwEHFqAiAGcUCeAqWx.8liI6.WlKbFJUOXxlqG7X.sNhG';

console.log('Testing password:', password);
console.log('Against hash:', hash);

const isValid = await bcrypt.compare(password, hash);
console.log('Is valid:', isValid);

// Let's also generate a new hash to compare
const newHash = await bcrypt.hash(password, 12);
console.log('New hash:', newHash);

const isNewValid = await bcrypt.compare(password, newHash);
console.log('New hash is valid:', isNewValid);
