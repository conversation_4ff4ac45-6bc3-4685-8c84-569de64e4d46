/**
 * 🔍 TESTE ESPECÍFICO DOS ERROR PATTERN COLLECTORS
 * Verificação pontual dos ErrorPatternCollector para cada jogo
 * Portal Betina V3 - Validação final
 */

import { ColorMatchCollectorsHub } from './src/games/ColorMatch/collectors/index.js';
import { NumberCountingCollectorsHub } from './src/games/ContagemNumeros/collectors/index.js';
import { ImageAssociationCollectorsHub } from './src/games/ImageAssociation/collectors/index.js';
import { MusicalSequenceCollectorsHub } from './src/games/MusicalSequence/collectors/index.js';
import { QuebraCabecaCollectorsHub } from './src/games/QuebraCabeca/collectors/index.js';
import { CreativePaintingCollectorsHub } from './src/games/CreativePainting/collectors/index.js';
import { LetterRecognitionCollectorsHub } from './src/games/LetterRecognition/collectors/index.js';
import { MemoryGameCollectorsHub } from './src/games/MemoryGame/collectors/index.js';

// Configuração de jogos e seus respectivos hubs
const games = [
  { name: 'ColorMatch', hub: ColorMatchCollectorsHub },
  { name: 'ContagemNumeros', hub: NumberCountingCollectorsHub },
  { name: 'ImageAssociation', hub: ImageAssociationCollectorsHub },
  { name: 'MusicalSequence', hub: MusicalSequenceCollectorsHub },
  { name: 'QuebraCabeca', hub: QuebraCabecaCollectorsHub },
  { name: 'CreativePainting', hub: CreativePaintingCollectorsHub },
  { name: 'LetterRecognition', hub: LetterRecognitionCollectorsHub },
  { name: 'MemoryGame', hub: MemoryGameCollectorsHub }
];

// Dados de teste para cada jogo
const mockGameData = {
  ColorMatch: {
    sessionId: 'test-colormatch-' + Date.now(),
    playerId: 'test-player',
    gameId: 'colormatch',
    timestamp: new Date().toISOString(),
    score: 85,
    duration: 120000,
    difficulty: 'medium',
    colorMatches: 8,
    colorErrors: 2,
    avgResponseTime: 1500,
    errors: [
      { type: 'color_mismatch', timestamp: Date.now() - 5000 },
      { type: 'timeout', timestamp: Date.now() - 2000 }
    ]
  },
  ContagemNumeros: {
    sessionId: 'test-contagemnumeros-' + Date.now(),
    playerId: 'test-player',
    gameId: 'contagemnumeros',
    timestamp: new Date().toISOString(),
    score: 90,
    duration: 150000,
    difficulty: 'easy',
    numbersCount: 10,
    countingErrors: 1,
    errors: [
      { type: 'sequence_error', timestamp: Date.now() - 10000 },
      { type: 'skip_number', timestamp: Date.now() - 5000 }
    ]
  },
  ImageAssociation: {
    sessionId: 'test-imageassociation-' + Date.now(),
    playerId: 'test-player',
    gameId: 'imageassociation',
    timestamp: new Date().toISOString(),
    score: 75,
    duration: 180000,
    difficulty: 'medium',
    associations: 12,
    associationErrors: 3,
    errors: [
      { type: 'wrong_association', timestamp: Date.now() - 8000 },
      { type: 'category_confusion', timestamp: Date.now() - 3000 }
    ]
  },
  MusicalSequence: {
    sessionId: 'test-musicalsequence-' + Date.now(),
    playerId: 'test-player',
    gameId: 'musicalsequence',
    timestamp: new Date().toISOString(),
    score: 80,
    duration: 200000,
    difficulty: 'hard',
    sequencesCompleted: 5,
    rhythmErrors: 2,
    errors: [
      { type: 'rhythm_error', timestamp: Date.now() - 15000 },
      { type: 'missed_note', timestamp: Date.now() - 7000 }
    ]
  },
  QuebraCabeca: {
    sessionId: 'test-quebracabeca-' + Date.now(),
    playerId: 'test-player',
    gameId: 'quebracabeca',
    timestamp: new Date().toISOString(),
    score: 70,
    duration: 300000,
    difficulty: 'medium',
    piecesPlaced: 15,
    incorrectPlacements: 4,
    errors: [
      { type: 'wrong_placement', timestamp: Date.now() - 20000 },
      { type: 'rotation_error', timestamp: Date.now() - 10000 }
    ]
  },
  CreativePainting: {
    sessionId: 'test-creativepainting-' + Date.now(),
    playerId: 'test-player',
    gameId: 'creativepainting',
    timestamp: new Date().toISOString(),
    score: 95,
    duration: 250000,
    difficulty: 'easy',
    elementsUsed: 8,
    colorsPalette: 6,
    errors: [
      { type: 'tool_selection_error', timestamp: Date.now() - 12000 },
      { type: 'color_mixing_issue', timestamp: Date.now() - 6000 }
    ]
  },
  LetterRecognition: {
    sessionId: 'test-letterrecognition-' + Date.now(),
    playerId: 'test-player',
    gameId: 'letterrecognition',
    timestamp: new Date().toISOString(),
    score: 85,
    duration: 180000,
    difficulty: 'medium',
    lettersRecognized: 20,
    letterErrors: 3,
    errors: [
      { type: 'letter_confusion', timestamp: Date.now() - 9000 },
      { type: 'phonetic_error', timestamp: Date.now() - 4000 }
    ]
  },
  MemoryGame: {
    sessionId: 'test-memorygame-' + Date.now(),
    playerId: 'test-player',
    gameId: 'memorygame',
    timestamp: new Date().toISOString(),
    score: 80,
    duration: 220000,
    difficulty: 'medium',
    memoryPairs: 8,
    memoryErrors: 3,
    errors: [
      { type: 'wrong_match', timestamp: Date.now() - 11000 },
      { type: 'repeat_selection', timestamp: Date.now() - 5500 }
    ]
  }
};

// Função principal para testar os ErrorPatternCollector de todos os jogos
async function testErrorPatternCollectors() {
  console.log('🔍 TESTE ESPECÍFICO DOS ERROR PATTERN COLLECTORS');
  console.log('='.repeat(70));
  console.log('Verificando se os coletores processam e encaminham dados corretamente\n');

  const results = [];

  // Testar cada jogo sequencialmente
  for (const game of games) {
    console.log(`\n🎮 Testando ${game.name}...`);
    
    try {
      // Instanciar o hub de coletores do jogo
      const hub = new game.hub();
      console.log(`✅ Hub de ${game.name} instanciado com sucesso`);
      
      // Obter o coletor de erro
      const collectorsProperty = hub.collectors || hub._collectors;
      if (!collectorsProperty) {
        console.log(`❌ ${game.name}: Propriedade collectors não encontrada`);
        results.push({
          game: game.name,
          success: false,
          error: 'Propriedade collectors não encontrada'
        });
        continue;
      }
      
      // Encontrar o coletor de erro (pode ter nomes ligeiramente diferentes)
      const errorCollectorKey = Object.keys(collectorsProperty).find(key => 
        key.toLowerCase().includes('error') || key.toLowerCase().includes('errorpattern')
      );
      
      if (!errorCollectorKey) {
        console.log(`❌ ${game.name}: ErrorPatternCollector não encontrado`);
        results.push({
          game: game.name,
          success: false,
          error: 'ErrorPatternCollector não encontrado'
        });
        continue;
      }
      
      const errorCollector = collectorsProperty[errorCollectorKey];
      console.log(`✅ ${game.name}: ErrorPatternCollector encontrado (${errorCollectorKey})`);
      
      // Verificar método collect
      if (typeof errorCollector.collect !== 'function') {
        console.log(`❌ ${game.name}: Método collect não implementado no ErrorPatternCollector`);
        results.push({
          game: game.name,
          success: false,
          error: 'Método collect não implementado'
        });
        continue;
      }
      
      // Testar a coleta de dados
      console.log(`🔄 Testando coleta de dados de erro para ${game.name}...`);
      const testData = mockGameData[game.name];
      
      try {
        const collectedData = await errorCollector.collect(testData);
        
        if (collectedData) {
          console.log(`✅ ${game.name}: ErrorPatternCollector coletou dados com sucesso:`);
          console.log(JSON.stringify(collectedData, null, 2));
          
          results.push({
            game: game.name,
            success: true,
            data: collectedData
          });
        } else {
          console.log(`⚠️ ${game.name}: ErrorPatternCollector não retornou dados`);
          results.push({
            game: game.name,
            success: false,
            error: 'Sem dados retornados'
          });
        }
      } catch (error) {
        console.log(`❌ ${game.name}: Erro ao coletar dados: ${error.message}`);
        results.push({
          game: game.name,
          success: false,
          error: error.message
        });
      }
      
      // Testar a integração com o hub
      console.log(`🔄 Testando integração com o hub para ${game.name}...`);
      
      if (typeof hub.runCompleteAnalysis === 'function') {
        try {
          const analysisResults = await hub.runCompleteAnalysis(testData);
          
          if (analysisResults) {
            console.log(`✅ ${game.name}: Hub processou dados com sucesso`);
            
            // Verificar se os dados do ErrorPatternCollector estão incluídos no resultado
            const hasErrorData = JSON.stringify(analysisResults).includes('error') || 
                               Object.keys(analysisResults).some(k => k.toLowerCase().includes('error'));
            
            if (hasErrorData) {
              console.log(`✅ ${game.name}: Dados do ErrorPatternCollector incluídos no resultado da análise`);
            } else {
              console.log(`⚠️ ${game.name}: Dados do ErrorPatternCollector parecem não estar incluídos no resultado da análise`);
            }
          } else {
            console.log(`⚠️ ${game.name}: Hub não retornou resultados de análise`);
          }
        } catch (error) {
          console.log(`❌ ${game.name}: Erro na análise do hub: ${error.message}`);
        }
      } else {
        console.log(`⚠️ ${game.name}: Hub não possui método runCompleteAnalysis`);
      }
      
    } catch (error) {
      console.log(`❌ ${game.name}: Erro no teste: ${error.message}`);
      results.push({
        game: game.name,
        success: false,
        error: error.message
      });
    }
  }

  // Imprimir resumo
  const successful = results.filter(r => r.success).length;
  
  console.log('\n' + '='.repeat(70));
  console.log('📊 RESUMO DO TESTE DE ERROR PATTERN COLLECTORS');
  console.log('='.repeat(70));
  console.log(`✅ Coletores funcionais: ${successful}/${games.length}`);
  console.log(`❌ Coletores com problemas: ${games.length - successful}`);
  
  if (successful === games.length) {
    console.log('\n🎉 TODOS OS ERROR PATTERN COLLECTORS ESTÃO FUNCIONAIS!');
    console.log('✅ Os dados estão sendo coletados e processados corretamente');
  } else {
    console.log('\n⚠️ ATENÇÃO: Alguns ErrorPatternCollector precisam ser revisados:');
    
    results.filter(r => !r.success).forEach(result => {
      console.log(`   ❌ ${result.game}: ${result.error}`);
    });
  }
}

// Executar teste
await testErrorPatternCollectors();
