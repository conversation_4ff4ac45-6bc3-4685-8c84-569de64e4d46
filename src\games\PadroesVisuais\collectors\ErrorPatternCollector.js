/**
 * Error Pattern Collector - Padroes Visuais
 * Coleta e analisa padrões de erro em jogos de reconhecimento de padrões visuais
 * Portal Betina V3
 */

export class ErrorPatternCollector {
  constructor() {
    this.collectorName = 'ErrorPatternCollector';
    this.gameType = 'PadroesVisuais';
    this.version = '1.0.0';
  }
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }


  /**
   * Analisa padrões de erro no jogo
   * @param {Object} gameData - Dados do jogo
   * @returns {Object} Análise dos padrões de erro
   */
  analyze(gameData) {
    try {
      if (!gameData || !gameData.interactions) {
        return this.createEmptyAnalysis();
      }

      const errorAnalysis = {
        // Análise de tipos de erro
        errorTypes: this.analyzeErrorTypes(gameData.interactions),
        
        // Análise de frequência de erros
        errorFrequency: this.analyzeErrorFrequency(gameData.interactions),
        
        // Análise de padrões temporais de erro
        temporalPatterns: this.analyzeTemporalErrorPatterns(gameData.interactions),
        
        // Análise de persistência de erros
        errorPersistence: this.analyzeErrorPersistence(gameData.interactions),
        
        // Métricas de qualidade
        qualityMetrics: this.calculateQualityMetrics(gameData.interactions),
        
        timestamp: new Date().toISOString(),
        collector: this.collectorName,
        confidence: this.calculateConfidence(gameData.interactions)
      };

      return errorAnalysis;

    } catch (error) {
      console.error('Error in ErrorPatternCollector:', error);
      return this.createErrorAnalysis(error);
    }
  }

  /**
   * Analisa tipos de erro
   */
  analyzeErrorTypes(interactions) {
    const errorInteractions = interactions.filter(i => i.success === false || i.error === true);
    
    const errorTypes = {
      patternMismatch: 0,
      colorConfusion: 0,
      spatialError: 0,
      sequenceError: 0,
      timingError: 0,
      recognitionError: 0
    };

    errorInteractions.forEach(interaction => {
      if (interaction.errorType) {
        errorTypes[interaction.errorType] = (errorTypes[interaction.errorType] || 0) + 1;
      } else {
        // Inferir tipo de erro baseado nos dados
        if (interaction.patternType === 'color') {
          errorTypes.colorConfusion++;
        } else if (interaction.patternType === 'spatial') {
          errorTypes.spatialError++;
        } else if (interaction.patternType === 'sequence') {
          errorTypes.sequenceError++;
        } else {
          errorTypes.recognitionError++;
        }
      }
    });

    return {
      distribution: errorTypes,
      totalErrors: errorInteractions.length,
      mostCommonError: this.findMostCommonError(errorTypes),
      errorRate: errorInteractions.length / Math.max(interactions.length, 1)
    };
  }

  /**
   * Analisa frequência de erros
   */
  analyzeErrorFrequency(interactions) {
    const errorInteractions = interactions.filter(i => i.success === false || i.error === true);
    const totalInteractions = interactions.length;

    if (totalInteractions === 0) {
      return { frequency: 0, pattern: 'none', stability: 'stable' };
    }

    // Calcular frequência por bloco de tempo
    const timeBlocks = this.divideIntoTimeBlocks(interactions, 5);
    const errorFrequencyByBlock = timeBlocks.map(block => {
      const blockErrors = block.filter(i => i.success === false || i.error === true);
      return blockErrors.length / Math.max(block.length, 1);
    });

    return {
      frequency: errorInteractions.length / totalInteractions,
      pattern: this.identifyFrequencyPattern(errorFrequencyByBlock),
      stability: this.calculateErrorStability(errorFrequencyByBlock),
      distribution: errorFrequencyByBlock
    };
  }

  /**
   * Analisa padrões temporais de erro
   */
  analyzeTemporalErrorPatterns(interactions) {
    const errorInteractions = interactions.filter(i => i.success === false || i.error === true);
    
    if (errorInteractions.length === 0) {
      return { pattern: 'none', concentration: 'dispersed', timing: 'random' };
    }

    // Analisar distribuição temporal
    const timestamps = errorInteractions.map(i => i.timestamp || Date.now());
    const timeGaps = this.calculateTimeGaps(timestamps);
    
    return {
      pattern: this.identifyTemporalPattern(timeGaps),
      concentration: this.analyzeErrorConcentration(timestamps),
      timing: this.analyzeErrorTiming(timestamps),
      averageGap: timeGaps.reduce((a, b) => a + b, 0) / Math.max(timeGaps.length, 1)
    };
  }

  /**
   * Analisa persistência de erros
   */
  analyzeErrorPersistence(interactions) {
    const errorSequences = this.findErrorSequences(interactions);
    
    return {
      maxSequenceLength: Math.max(...errorSequences.map(s => s.length), 0),
      averageSequenceLength: errorSequences.reduce((sum, seq) => sum + seq.length, 0) / Math.max(errorSequences.length, 1),
      sequenceCount: errorSequences.length,
      recoveryRate: this.calculateRecoveryRate(interactions),
      persistence: this.calculatePersistenceScore(errorSequences)
    };
  }

  /**
   * Calcula métricas de qualidade
   */
  calculateQualityMetrics(interactions) {
    const successfulInteractions = interactions.filter(i => i.success === true);
    const errorInteractions = interactions.filter(i => i.success === false || i.error === true);

    return {
      accuracy: successfulInteractions.length / Math.max(interactions.length, 1),
      errorRate: errorInteractions.length / Math.max(interactions.length, 1),
      consistency: this.calculateConsistency(interactions),
      improvement: this.calculateImprovementTrend(interactions),
      qualityScore: this.calculateOverallQualityScore(interactions)
    };
  }

  /**
   * Métodos auxiliares
   */
  findMostCommonError(errorTypes) {
    const entries = Object.entries(errorTypes);
    if (entries.length === 0) return 'none';
    
    const maxEntry = entries.reduce((max, curr) => curr[1] > max[1] ? curr : max);
    return maxEntry[0];
  }

  divideIntoTimeBlocks(interactions, blockCount) {
    const blockSize = Math.ceil(interactions.length / blockCount);
    const blocks = [];
    
    for (let i = 0; i < interactions.length; i += blockSize) {
      blocks.push(interactions.slice(i, i + blockSize));
    }
    
    return blocks;
  }

  identifyFrequencyPattern(frequencies) {
    if (frequencies.length < 2) return 'insufficient_data';
    
    const trend = this.calculateTrend(frequencies);
    if (trend > 0.1) return 'increasing';
    if (trend < -0.1) return 'decreasing';
    return 'stable';
  }

  calculateErrorStability(frequencies) {
    if (frequencies.length < 2) return 'stable';
    
    const variance = this.calculateVariance(frequencies);
    if (variance < 0.01) return 'very_stable';
    if (variance < 0.05) return 'stable';
    if (variance < 0.1) return 'moderate';
    return 'unstable';
  }

  calculateTimeGaps(timestamps) {
    const gaps = [];
    for (let i = 1; i < timestamps.length; i++) {
      gaps.push(timestamps[i] - timestamps[i-1]);
    }
    return gaps;
  }

  identifyTemporalPattern(timeGaps) {
    if (timeGaps.length === 0) return 'none';
    
    const avgGap = timeGaps.reduce((a, b) => a + b, 0) / timeGaps.length;
    const variance = this.calculateVariance(timeGaps);
    
    if (variance < avgGap * 0.1) return 'regular';
    if (variance < avgGap * 0.5) return 'semi_regular';
    return 'irregular';
  }

  analyzeErrorConcentration(timestamps) {
    if (timestamps.length < 2) return 'dispersed';
    
    const gaps = this.calculateTimeGaps(timestamps);
    const avgGap = gaps.reduce((a, b) => a + b, 0) / gaps.length;
    const shortGaps = gaps.filter(gap => gap < avgGap * 0.5);
    
    if (shortGaps.length > gaps.length * 0.7) return 'concentrated';
    if (shortGaps.length > gaps.length * 0.3) return 'moderate';
    return 'dispersed';
  }

  analyzeErrorTiming(timestamps) {
    // Implementação simplificada
    return 'random';
  }

  findErrorSequences(interactions) {
    const sequences = [];
    let currentSequence = [];
    
    interactions.forEach(interaction => {
      if (interaction.success === false || interaction.error === true) {
        currentSequence.push(interaction);
      } else {
        if (currentSequence.length > 0) {
          sequences.push(currentSequence);
          currentSequence = [];
        }
      }
    });
    
    if (currentSequence.length > 0) {
      sequences.push(currentSequence);
    }
    
    return sequences;
  }

  calculateRecoveryRate(interactions) {
    let recoveries = 0;
    let errorSequences = 0;
    let inErrorSequence = false;
    
    interactions.forEach(interaction => {
      if (interaction.success === false || interaction.error === true) {
        if (!inErrorSequence) {
          errorSequences++;
          inErrorSequence = true;
        }
      } else {
        if (inErrorSequence) {
          recoveries++;
          inErrorSequence = false;
        }
      }
    });
    
    return errorSequences > 0 ? recoveries / errorSequences : 1;
  }

  calculatePersistenceScore(errorSequences) {
    if (errorSequences.length === 0) return 0;
    
    const totalErrors = errorSequences.reduce((sum, seq) => sum + seq.length, 0);
    const averageSequenceLength = totalErrors / errorSequences.length;
    
    return Math.min(1, averageSequenceLength / 5); // Normalizado para 0-1
  }

  calculateConsistency(interactions) {
    const successRates = this.divideIntoTimeBlocks(interactions, 5).map(block => {
      const successes = block.filter(i => i.success === true);
      return successes.length / Math.max(block.length, 1);
    });
    
    const variance = this.calculateVariance(successRates);
    return Math.max(0, 1 - variance);
  }

  calculateImprovementTrend(interactions) {
    const blockSize = Math.ceil(interactions.length / 5);
    const blocks = this.divideIntoTimeBlocks(interactions, 5);
    
    const successRates = blocks.map(block => {
      const successes = block.filter(i => i.success === true);
      return successes.length / Math.max(block.length, 1);
    });
    
    return this.calculateTrend(successRates);
  }

  calculateOverallQualityScore(interactions) {
    const accuracy = interactions.filter(i => i.success === true).length / Math.max(interactions.length, 1);
    const consistency = this.calculateConsistency(interactions);
    const improvement = Math.max(0, this.calculateImprovementTrend(interactions));
    
    return (accuracy * 0.5) + (consistency * 0.3) + (improvement * 0.2);
  }

  calculateTrend(values) {
    if (values.length < 2) return 0;
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    
    return (secondAvg - firstAvg) / Math.max(firstAvg, 0.001);
  }

  calculateVariance(values) {
    if (values.length < 2) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  calculateConfidence(interactions) {
    if (interactions.length < 5) return 0.3;
    if (interactions.length < 10) return 0.6;
    if (interactions.length < 20) return 0.8;
    return 0.95;
  }

  createEmptyAnalysis() {
    return {
      errorTypes: {
        distribution: {},
        totalErrors: 0,
        mostCommonError: 'none',
        errorRate: 0
      },
      errorFrequency: {
        frequency: 0,
        pattern: 'none',
        stability: 'stable'
      },
      temporalPatterns: {
        pattern: 'none',
        concentration: 'dispersed',
        timing: 'random'
      },
      errorPersistence: {
        maxSequenceLength: 0,
        averageSequenceLength: 0,
        sequenceCount: 0,
        recoveryRate: 1,
        persistence: 0
      },
      qualityMetrics: {
        accuracy: 0,
        errorRate: 0,
        consistency: 0,
        improvement: 0,
        qualityScore: 0
      },
      timestamp: new Date().toISOString(),
      collector: this.collectorName,
      confidence: 0.1
    };
  }

  createErrorAnalysis(error) {
    return {
      error: error.message,
      timestamp: new Date().toISOString(),
      collector: this.collectorName,
      confidence: 0
    };
  }
}
