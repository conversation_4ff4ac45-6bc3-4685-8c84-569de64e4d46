/**
 * 🧠 ANALYZERS MONITOR V3 - UI/UX MODERNO
 * @file AnalyzersMonitor.module.css
 * @description Monitor de Analisadores com design futurístico
 * @version 3.0.0
 * @features Glassmorphism, Real-time indicators, Organized metrics
 */

/* ===== VARIÁVEIS LOCAIS ===== */
:root {
  --analyzer-primary: #8b5cf6;
  --analyzer-success: #10b981;
  --analyzer-warning: #f59e0b;
  --analyzer-error: #ef4444;
  --analyzer-info: #3b82f6;

  --analyzer-bg-primary: #0f172a;
  --analyzer-bg-secondary: #1e293b;
  --analyzer-bg-glass: rgba(255, 255, 255, 0.1);

  --analyzer-text-primary: #f8fafc;
  --analyzer-text-secondary: #cbd5e1;
  --analyzer-text-muted: #94a3b8;

  --analyzer-border-radius: 16px;
  --analyzer-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== CONTAINER PRINCIPAL ===== */
.analyzersMonitor {
  padding: 0;
  max-width: 100%;
  margin: 0;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HEADER MODERNO ===== */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 24px;
  padding: 24px 32px;
  background: var(--analyzer-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--analyzer-border-radius);
  box-shadow: var(--analyzer-shadow);
}

.title {
  color: var(--analyzer-text-primary);
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title::before {
  content: '🧠';
  font-size: 28px;
}

.refreshButton {
  background: linear-gradient(135deg, var(--analyzer-primary), #a855f7);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.refreshButton::before {
  content: '🔄';
  font-size: 16px;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.refreshButton:disabled {
  background: rgba(148, 163, 184, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== GRID DE ANALISADORES ORGANIZADO ===== */
.analyzersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin: 0 32px 32px 32px;
}

.analyzerCard {
  background: var(--analyzer-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--analyzer-border-radius);
  padding: 24px;
  box-shadow: var(--analyzer-shadow);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.analyzerCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--analyzer-primary), #a855f7);
  transition: all 0.3s ease;
}

.analyzerCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.analyzerHeader {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.analyzerIcon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.analyzerInfo {
  flex: 1;
  min-width: 0;
}

.analyzerName {
  font-size: 16px;
  font-weight: 600;
  color: var(--analyzer-text-primary);
  margin: 0 0 6px 0;
  line-height: 1.2;
}

.analyzerDescription {
  color: var(--analyzer-text-secondary);
  font-size: 13px;
  margin: 0;
  line-height: 1.4;
}

.analyzerStatus {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 6px;
  position: absolute;
  top: 20px;
  right: 20px;
}

.analyzerStatus::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.statusActive {
  background: rgba(16, 185, 129, 0.2);
  color: var(--analyzer-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.statusActive::before {
  background: var(--analyzer-success);
}

.statusInactive {
  background: rgba(239, 68, 68, 0.2);
  color: var(--analyzer-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.statusInactive::before {
  background: var(--analyzer-error);
}

.statusProcessing {
  background: rgba(245, 158, 11, 0.2);
  color: var(--analyzer-warning);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.statusProcessing::before {
  background: var(--analyzer-warning);
}

/* ===== MÉTRICAS ORGANIZADAS ===== */
.analyzerMetrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.metric {
  text-align: center;
  padding: 12px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.metricValue {
  font-size: 18px;
  font-weight: 700;
  color: var(--analyzer-text-primary);
  margin-bottom: 4px;
  line-height: 1;
}

.metricLabel {
  font-size: 10px;
  color: var(--analyzer-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
}

.analyzerActions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
  margin-top: 1rem;
}

.actionButton {
  padding: 0.4rem 0.8rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.2s;
}

.viewButton {
  background: #007bff;
  color: white;
}

.viewButton:hover {
  background: #0056b3;
}

.configButton {
  background: #28a745;
  color: white;
}

.configButton:hover {
  background: #1e7e34;
}

.restartButton {
  background: #ffc107;
  color: #212529;
}

.restartButton:hover {
  background: #e0a800;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
  margin: 1rem;
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.modalTitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  color: #333;
}

.detailsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.detailCard {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
}

.detailLabel {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  font-weight: 500;
}

.detailValue {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.configSection {
  margin-bottom: 2rem;
}

.configTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.configList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.configItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #dee2e6;
}

.configItem:last-child {
  border-bottom: none;
}

.configKey {
  font-weight: 500;
  color: #333;
}

.configValue {
  color: #666;
  font-family: monospace;
  background: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.logsSection {
  margin-top: 2rem;
}

.logsTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.logsList {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 0.9rem;
}

.logEntry {
  margin-bottom: 0.5rem;
  padding: 0.25rem 0;
}

.logTimestamp {
  color: #666;
  margin-right: 0.5rem;
}

.logLevel {
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

.logInfo {
  background: #d1ecf1;
  color: #0c5460;
}

.logWarning {
  background: #fff3cd;
  color: #856404;
}

.logError {
  background: #f8d7da;
  color: #721c24;
}

.logMessage {
  color: #333;
}

.loading {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
  }

  .analyzersGrid {
    grid-template-columns: 1fr;
  }

  .analyzerMetrics {
    grid-template-columns: 1fr;
  }

  .detailsGrid {
    grid-template-columns: 1fr;
  }

  .modalContent {
    margin: 0.5rem;
    padding: 1rem;
  }

  .configItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
