/**
 * Script para listar todos os endpoints disponíveis na API
 */

const endpoints = [
  // <PERSON>otas <PERSON>ú<PERSON>licas
  'GET /api/public/health',
  'GET /api/public/health/detailed',
  'GET /api/public/games',
  'POST /api/public/games',
  'GET /api/public/activities',
  'POST /api/public/activities',
  'GET /api/public/metrics',
  'POST /api/public/metrics',
  
  // Autenticação
  'POST /api/auth/login',
  'POST /api/auth/logout',
  'POST /api/auth/refresh',
  'GET /api/auth/dashboard/check',
  'POST /api/auth/dashboard/login',
  
  // Métricas (Protegidas)
  'GET /api/metrics/sessions',
  'POST /api/metrics/sessions',
  'GET /api/metrics/interactions',
  'POST /api/metrics/interactions',
  'GET /api/metrics/dashboard',
  
  // Dashboards Premium (Protegidas)
  'GET /api/premium/dashboards',
  'POST /api/premium/dashboards',
  'GET /api/dashboard/overview',
  'GET /api/dashboard/behavior',
  'GET /api/dashboard/therapeutic',
  'POST /api/dashboard/profile-linking',
  
  // Relatórios (Protegidas)
  'GET /api/reports/therapeutic',
  'POST /api/reports/therapeutic',
  
  // Backup
  'GET /api/backup',
  'POST /api/backup',
  
  // Informações do Sistema (Protegidas)
  'GET /api/info'
];

console.log('🌐 Endpoints disponíveis no Portal Betina V3:');
console.log('==========================================');

endpoints.forEach((endpoint, index) => {
  const [method, path] = endpoint.split(' ');
  const isProtected = !path.includes('/public/') && !path.includes('/auth/') && !path.includes('/backup/');
  const protection = isProtected ? '🔒 (Protegido)' : '🔓 (Público)';
  console.log(`${index + 1}. ${method.padEnd(4)} ${path.padEnd(30)} ${protection}`);
});

console.log('\n📝 Legenda:');
console.log('🔓 Público: Não requer autenticação');
console.log('🔒 Protegido: Requer autenticação JWT');

console.log('\n🔧 URLs completas para teste:');
console.log('Backend API: http://localhost:3000');
console.log('Frontend: http://localhost:5173');

console.log('\n🧪 Teste rápido de endpoints públicos:');
console.log('curl http://localhost:3000/api/public/health');
console.log('curl http://localhost:3000/api/public/games');
console.log('curl http://localhost:3000/api/public/activities');
