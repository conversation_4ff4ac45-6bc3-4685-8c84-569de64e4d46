import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  
  build: {
    // Aumentar o limite de warning para chunks grandes
    chunkSizeWarningLimit: 1000,
    
    rollupOptions: {
      output: {
        // Configuração manual de chunks para otimizar o bundle
        manualChunks: {
          // Chunk para bibliotecas React
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          
          // Chunk para bibliotecas de UI/Animação
          'ui-vendor': ['framer-motion', 'styled-components'],
          
          // Chunk para bibliotecas de gráficos
          'chart-vendor': ['chart.js', 'react-chartjs-2'],
          
          // Chunk para utilitários
          'utils-vendor': ['lodash', 'axios', 'uuid'],
          
          // Chunk para bibliotecas de dados/estado
          'data-vendor': ['@tanstack/react-query'],
          
          // Separar componentes grandes do dashboard
          'dashboard': [
            'src/components/dashboard/DashboardContainer.jsx',
            'src/components/dashboard/GameDashboard.jsx'
          ],
          
          // Separar jogos em chunk próprio
          'games': [
            'src/components/games/GamePage.jsx',
            'src/components/games'
          ],
          
          // Separar análises e serviços
          'analysis': [
            'src/api/services/analysis',
            'src/api/services/core'
          ]
        },
        
        // Nomear chunks de forma mais limpa
        chunkFileNames: 'assets/[name]-[hash].js',
        entryFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    
    // Otimizações adicionais
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs em produção
        drop_debugger: true
      }
    }
  },
  
  // Otimizações para desenvolvimento
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'chart.js',
      'styled-components'
    ]
  },
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@api': resolve(__dirname, 'src/api'),
      '@utils': resolve(__dirname, 'src/utils')
    }
  }
});
