/**
 * 🔧 CORREÇÃO DE SCHEMAS DO BANCO DE DADOS
 * Executa as correções necessárias nas tabelas
 * Portal Betina V3
 */

import { Client } from 'pg';
import fs from 'fs';

const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER || 'betina_user',
  password: process.env.DB_PASSWORD || 'betina_password',
  database: process.env.DB_NAME || 'betina_db'
};

class DatabaseSchemaFixer {
  constructor() {
    this.client = null;
  }

  async fixSchemas() {
    console.log('🔧 INICIANDO CORREÇÃO DE SCHEMAS DO BANCO');
    console.log('='.repeat(50));

    try {
      this.client = new Client(DB_CONFIG);
      await this.client.connect();
      console.log('✅ Conectado ao banco de dados');

      // 1. Corrigir tabela game_sessions
      await this.fixGameSessions();
      
      // 2. Corrigir tabela game_metrics
      await this.fixGameMetrics();
      
      // 3. Corrigir tabelas multissensoriais
      await this.fixMultisensoryTables();
      
      // 4. Corrigir tabelas dos jogos
      await this.fixGameTables();
      
      // 5. Inserir dados de teste
      await this.insertTestData();
      
      // 6. Verificar correções
      await this.verifyFixes();
      
      console.log('\n🎉 CORREÇÕES CONCLUÍDAS COM SUCESSO!');
      
    } catch (error) {
      console.error('❌ ERRO NA CORREÇÃO:', error.message);
      throw error;
    } finally {
      if (this.client) {
        await this.client.end();
      }
    }
  }

  async fixGameSessions() {
    console.log('\n📋 Corrigindo tabela game_sessions...');
    
    const fixes = [
      'ALTER TABLE game_sessions ADD COLUMN IF NOT EXISTS session_data JSONB DEFAULT \'{}\'',
      'ALTER TABLE game_sessions ADD COLUMN IF NOT EXISTS started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
      'ALTER TABLE game_sessions ADD COLUMN IF NOT EXISTS ended_at TIMESTAMP',
      'ALTER TABLE game_sessions ADD COLUMN IF NOT EXISTS duration INTEGER'
    ];

    for (const fix of fixes) {
      try {
        await this.client.query(fix);
        console.log('✅ Executado:', fix.substring(0, 50) + '...');
      } catch (error) {
        console.log('⚠️ Já existe:', fix.substring(0, 50) + '...');
      }
    }
  }

  async fixGameMetrics() {
    console.log('\n📊 Corrigindo tabela game_metrics...');
    
    const fixes = [
      'ALTER TABLE game_metrics ADD COLUMN IF NOT EXISTS accuracy DECIMAL(5,2) DEFAULT 0',
      'ALTER TABLE game_metrics ADD COLUMN IF NOT EXISTS response_time INTEGER DEFAULT 0',
      'ALTER TABLE game_metrics ADD COLUMN IF NOT EXISTS engagement_score DECIMAL(5,2) DEFAULT 0',
      'ALTER TABLE game_metrics ADD COLUMN IF NOT EXISTS analysis_data JSONB DEFAULT \'{}\'',
      'CREATE INDEX IF NOT EXISTS idx_game_metrics_accuracy ON game_metrics(accuracy)'
    ];

    for (const fix of fixes) {
      try {
        await this.client.query(fix);
        console.log('✅ Executado:', fix.substring(0, 50) + '...');
      } catch (error) {
        console.log('⚠️ Já existe:', fix.substring(0, 50) + '...');
      }
    }
  }

  async fixMultisensoryTables() {
    console.log('\n🔬 Corrigindo tabelas multissensoriais...');
    
    const fixes = [
      'ALTER TABLE multisensory_data ADD COLUMN IF NOT EXISTS calibration_data JSONB DEFAULT \'{}\'',
      'ALTER TABLE multisensory_data ADD COLUMN IF NOT EXISTS processing_metadata JSONB DEFAULT \'{}\'',
      'ALTER TABLE sensor_calibration ADD COLUMN IF NOT EXISTS accuracy_score DECIMAL(5,2) DEFAULT 0',
      'ALTER TABLE sensor_calibration ADD COLUMN IF NOT EXISTS device_id VARCHAR(255)',
      'ALTER TABLE sensor_calibration ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
    ];

    for (const fix of fixes) {
      try {
        await this.client.query(fix);
        console.log('✅ Executado:', fix.substring(0, 50) + '...');
      } catch (error) {
        console.log('⚠️ Já existe:', fix.substring(0, 50) + '...');
      }
    }
  }

  async fixGameTables() {
    console.log('\n🎮 Corrigindo tabelas dos jogos...');
    
    const gameTables = [
      'colormatch_metrics',
      'contagemnumeros_metrics', 
      'imageassociation_metrics',
      'memorygame_metrics',
      'musicalsequence_metrics',
      'padroesvisuais_metrics',
      'quebracabeca_metrics',
      'creativepainting_metrics',
      'letterrecognition_metrics'
    ];

    for (const table of gameTables) {
      console.log(`  Corrigindo ${table}...`);
      
      const fixes = [
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS session_id VARCHAR(255) NOT NULL DEFAULT 'default_session'`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS user_id VARCHAR(255) NOT NULL DEFAULT 'default_user'`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS game_specific_data JSONB DEFAULT '{}'`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS collectors_data JSONB DEFAULT '{}'`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS performance_metrics JSONB DEFAULT '{}'`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS therapeutic_indicators JSONB DEFAULT '{}'`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS multisensory_data JSONB DEFAULT '{}'`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS accuracy DECIMAL(5,2) DEFAULT 0`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS response_time INTEGER DEFAULT 0`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS engagement_score DECIMAL(5,2) DEFAULT 0`,
        `ALTER TABLE ${table} ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`
      ];

      for (const fix of fixes) {
        try {
          await this.client.query(fix);
        } catch (error) {
          // Ignorar erros de colunas já existentes
        }
      }
      
      console.log(`  ✅ ${table} corrigida`);
    }
  }

  async insertTestData() {
    console.log('\n📊 Inserindo dados de teste...');
    
    const testSessionId = `test_session_${Date.now()}`;
    const testUserId = 'test_user_dashboard';

    try {
      // Inserir sessão de teste
      await this.client.query(`
        INSERT INTO game_sessions (session_id, user_id, game_id, session_data, duration)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (session_id) DO NOTHING
      `, [testSessionId, testUserId, 'ColorMatch', JSON.stringify({ test: true }), 120000]);

      // Inserir métricas de teste
      await this.client.query(`
        INSERT INTO game_metrics (session_id, user_id, game_id, metrics_data, accuracy, response_time, engagement_score)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [testSessionId, testUserId, 'ColorMatch', JSON.stringify({ test_metrics: true }), 85.5, 1500, 92.3]);

      // Inserir dados multissensoriais de teste
      await this.client.query(`
        INSERT INTO multisensory_data (session_id, user_id, game_id, sensor_type, sensor_data)
        VALUES ($1, $2, $3, $4, $5)
      `, [testSessionId, testUserId, 'ColorMatch', 'touch', JSON.stringify({ x: 100, y: 200, pressure: 0.8 })]);

      // Inserir análise terapêutica de teste
      await this.client.query(`
        INSERT INTO therapeutic_analysis (session_id, user_id, analysis_type, analysis_data, confidence_score)
        VALUES ($1, $2, $3, $4, $5)
      `, [testSessionId, testUserId, 'cognitive', JSON.stringify({ attention: 0.85, memory: 0.78 }), 0.82]);

      console.log(`✅ Dados de teste inseridos para sessão: ${testSessionId}`);
      
    } catch (error) {
      console.log('⚠️ Erro ao inserir dados de teste:', error.message);
    }
  }

  async verifyFixes() {
    console.log('\n🔍 Verificando correções...');
    
    try {
      // Verificar se as colunas existem
      const columnsCheck = await this.client.query(`
        SELECT 
          table_name,
          column_name,
          data_type
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name IN ('game_sessions', 'game_metrics', 'multisensory_data')
        AND column_name IN ('session_data', 'accuracy', 'calibration_data')
        ORDER BY table_name, column_name
      `);

      console.log('📋 Colunas verificadas:');
      columnsCheck.rows.forEach(row => {
        console.log(`  ${row.table_name}.${row.column_name}: ${row.data_type}`);
      });

      // Verificar dados de teste
      const dataCheck = await this.client.query(`
        SELECT 
          'game_sessions' as tabela, count(*) as registros 
        FROM game_sessions
        UNION ALL
        SELECT 'game_metrics' as tabela, count(*) as registros 
        FROM game_metrics
        UNION ALL
        SELECT 'multisensory_data' as tabela, count(*) as registros 
        FROM multisensory_data
        UNION ALL
        SELECT 'therapeutic_analysis' as tabela, count(*) as registros 
        FROM therapeutic_analysis
      `);

      console.log('\n📊 Dados no banco:');
      dataCheck.rows.forEach(row => {
        console.log(`  ${row.tabela}: ${row.registros} registros`);
      });

      // Testar consulta do dashboard
      const dashboardTest = await this.client.query(`
        SELECT 
          gs.game_id,
          gs.user_id,
          gm.accuracy,
          COUNT(md.id) as multisensory_readings
        FROM game_sessions gs
        LEFT JOIN game_metrics gm ON gs.session_id = gm.session_id
        LEFT JOIN multisensory_data md ON gs.session_id = md.session_id
        GROUP BY gs.game_id, gs.user_id, gm.accuracy
        LIMIT 3
      `);

      console.log('\n📈 Teste de consulta do dashboard:');
      dashboardTest.rows.forEach(row => {
        console.log(`  ${row.game_id} - ${row.user_id}: ${row.accuracy}% accuracy, ${row.multisensory_readings} sensores`);
      });

    } catch (error) {
      console.error('❌ Erro na verificação:', error.message);
    }
  }
}

// Executar correções se chamado diretamente
if (typeof window === 'undefined') {
  const isMainModule = process.argv[1] && process.argv[1].includes('fix-database-schemas.js');
  
  if (isMainModule) {
    const fixer = new DatabaseSchemaFixer();
    
    fixer.fixSchemas()
      .then(() => {
        console.log('\n🎯 CORREÇÕES CONCLUÍDAS COM SUCESSO!');
        process.exit(0);
      })
      .catch(error => {
        console.error('💥 ERRO CRÍTICO:', error);
        process.exit(1);
      });
  }
}

export { DatabaseSchemaFixer };
