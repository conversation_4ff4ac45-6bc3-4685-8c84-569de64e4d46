/**
 * 🔀 INTERFERENCE PATTERN COLLECTOR - Portal Betina V3
 * Coletor especializado em análise de padrões de interferência cognitiva
 * Detecta interferências proativas e retroativas na memória
 */

export class InterferencePatternCollector {
  constructor() {
    this.name = 'InterferencePatternCollector';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    this.sessionData = {
      currentSession: null,
      interferenceEvents: [],
      memoryConflicts: [],
      confusionPatterns: []
    };
    
    console.log('🔀 InterferencePatternCollector inicializado');
  }

  /**
   * Inicializar nova sessão de coleta
   */
  startSession(sessionId, gameData = {}) {
    this.sessionData.currentSession = {
      sessionId,
      startTime: Date.now(),
      difficulty: gameData.difficulty || 'unknown',
      theme: gameData.theme || 'unknown',
      totalCards: gameData.cardsCount || 0,
      cardPairs: this.extractCardPairs(gameData),
      interferenceEvents: [],
      memoryConflicts: [],
      confusionMatrix: this.initializeConfusionMatrix(),
      similarityMapping: this.buildSimilarityMapping(gameData),
      interferenceMetrics: {
        proactiveInterference: 0,
        retroactiveInterference: 0,
        similarityBasedConfusion: 0,
        positionBasedConfusion: 0,
        semanticInterference: 0,
        visualInterference: 0
      }
    };
    
    console.log(`🔀 InterferencePatternCollector: Nova sessão iniciada - ${sessionId}`);
  }

  /**
   * Extrair pares de cartas
   */
  extractCardPairs(gameData) {
    // Em implementação real, extrairia do estado atual do jogo
    const themeMapping = {
      'farm_animals': ['pig', 'cow'],
      'tasty_fruits': ['apple', 'banana', 'orange'],
      'transportation': ['car', 'airplane', 'bicycle', 'train'],
      'space_stars': ['star', 'moon', 'rocket', 'planet', 'sun', 'bright_star'],
      'enchanted_garden': ['flower', 'butterfly', 'hibiscus', 'sunflower', 'bee', 'tulip', 'rose', 'daisy']
    };
    
    const cards = themeMapping[gameData.theme] || [];
    const pairs = [];
    
    // Criar pares para o jogo da memória
    cards.forEach(card => {
      pairs.push({ id: `${card}_1`, type: card, pairId: card });
      pairs.push({ id: `${card}_2`, type: card, pairId: card });
    });
    
    return pairs;
  }

  /**
   * Inicializar matriz de confusão
   */
  initializeConfusionMatrix() {
    return {
      similarCards: {},
      positionErrors: {},
      sequenceErrors: {},
      thematicErrors: {}
    };
  }

  /**
   * Construir mapeamento de similaridade
   */
  buildSimilarityMapping(gameData) {
    const similarityMaps = {
      'farm_animals': {
        'pig': { similar: ['cow'], similarity: 0.7 },
        'cow': { similar: ['pig'], similarity: 0.7 }
      },
      'tasty_fruits': {
        'apple': { similar: ['orange'], similarity: 0.6 },
        'banana': { similar: ['orange'], similarity: 0.4 },
        'orange': { similar: ['apple', 'banana'], similarity: 0.5 }
      },
      'transportation': {
        'car': { similar: ['bicycle'], similarity: 0.5 },
        'airplane': { similar: ['rocket'], similarity: 0.6 },
        'bicycle': { similar: ['car'], similarity: 0.5 },
        'train': { similar: ['car'], similarity: 0.4 }
      },
      'space_stars': {
        'star': { similar: ['bright_star'], similarity: 0.8 },
        'bright_star': { similar: ['star'], similarity: 0.8 },
        'moon': { similar: ['planet'], similarity: 0.6 },
        'planet': { similar: ['moon'], similarity: 0.6 },
        'rocket': { similar: ['airplane'], similarity: 0.5 }
      },
      'enchanted_garden': {
        'flower': { similar: ['hibiscus', 'sunflower', 'tulip', 'rose', 'daisy'], similarity: 0.7 },
        'hibiscus': { similar: ['flower', 'rose'], similarity: 0.8 },
        'sunflower': { similar: ['flower', 'daisy'], similarity: 0.8 },
        'tulip': { similar: ['flower', 'rose'], similarity: 0.7 },
        'rose': { similar: ['flower', 'hibiscus', 'tulip'], similarity: 0.8 },
        'daisy': { similar: ['flower', 'sunflower'], similarity: 0.7 },
        'butterfly': { similar: ['bee'], similarity: 0.6 },
        'bee': { similar: ['butterfly'], similarity: 0.6 }
      }
    };
    
    return similarityMaps[gameData.theme] || {};
  }

  /**
   * Registrar interação para análise de interferência
   */
  recordInteraction(interactionData) {
    if (!this.sessionData.currentSession) {
      console.warn('🔀 InterferencePatternCollector: Sessão não iniciada');
      return;
    }

    const timestamp = Date.now();
    const session = this.sessionData.currentSession;
    
    const interferenceEvent = {
      timestamp,
      cardId: interactionData.cardId,
      cardType: this.extractCardType(interactionData.cardId),
      targetCardId: interactionData.targetCardId,
      targetCardType: this.extractCardType(interactionData.targetCardId),
      position: interactionData.position || { row: 0, col: 0, index: 0 },
      targetPosition: interactionData.targetPosition,
      isMatch: interactionData.isMatch || false,
      reactionTime: interactionData.reactionTime || 0,
      attemptNumber: this.getAttemptNumber(interactionData),
      interferenceTypes: this.detectInterferenceTypes(interactionData),
      confusionFactors: this.analyzeConfusionFactors(interactionData)
    };

    // Adicionar ao histórico da sessão
    session.interferenceEvents.push(interferenceEvent);
    
    // Atualizar matriz de confusão
    this.updateConfusionMatrix(interferenceEvent);
    
    // Detectar padrões de interferência
    this.detectInterferencePatterns(interferenceEvent);
    
    // Atualizar métricas de interferência
    this.updateInterferenceMetrics(interferenceEvent);
    
    console.log('🔀 InterferencePatternCollector: Evento de interferência registrado', {
      interferenceTypes: interferenceEvent.interferenceTypes,
      confusionFactors: interferenceEvent.confusionFactors
    });
  }

  /**
   * Extrair tipo de carta do ID
   */
  extractCardType(cardId) {
    if (!cardId) return 'unknown';
    // Remover sufixos _1, _2 etc.
    return cardId.replace(/_\d+$/, '');
  }

  /**
   * Obter número da tentativa
   */
  getAttemptNumber(interactionData) {
    const session = this.sessionData.currentSession;
    const cardId = interactionData.cardId;
    
    // Contar quantas vezes esta carta foi clicada
    const cardClicks = session.interferenceEvents.filter(e => e.cardId === cardId);
    return cardClicks.length + 1;
  }

  /**
   * Detectar tipos de interferência
   */
  detectInterferenceTypes(interactionData) {
    const interferenceTypes = [];
    const cardType = this.extractCardType(interactionData.cardId);
    const targetCardType = this.extractCardType(interactionData.targetCardId);
    
    // Interferência por similaridade visual/semântica
    if (this.isVisuallyOrSemanticallySimilar(cardType, targetCardType)) {
      interferenceTypes.push('semantic_visual_similarity');
    }
    
    // Interferência por posição próxima
    if (this.isPositionallySimilar(interactionData.position, interactionData.targetPosition)) {
      interferenceTypes.push('positional_proximity');
    }
    
    // Interferência temporal (baseada em eventos recentes)
    if (this.hasRecentTemporalInterference(interactionData)) {
      interferenceTypes.push('temporal_proximity');
    }
    
    // Interferência proativa (aprendizado anterior interfere no atual)
    if (this.hasProactiveInterference(interactionData)) {
      interferenceTypes.push('proactive_interference');
    }
    
    // Interferência retroativa (nova informação interfere na já aprendida)
    if (this.hasRetroactiveInterference(interactionData)) {
      interferenceTypes.push('retroactive_interference');
    }
    
    return interferenceTypes;
  }

  /**
   * Verificar similaridade visual/semântica
   */
  isVisuallyOrSemanticallySimilar(cardType1, cardType2) {
    const session = this.sessionData.currentSession;
    const similarityMap = session.similarityMapping;
    
    if (similarityMap[cardType1]) {
      return similarityMap[cardType1].similar.includes(cardType2);
    }
    
    return false;
  }

  /**
   * Verificar similaridade posicional
   */
  isPositionallySimilar(pos1, pos2) {
    if (!pos1 || !pos2) return false;
    
    const rowDiff = Math.abs(pos1.row - pos2.row);
    const colDiff = Math.abs(pos1.col - pos2.col);
    
    // Considera similaridade se estão adjacentes
    return (rowDiff <= 1 && colDiff <= 1) && !(rowDiff === 0 && colDiff === 0);
  }

  /**
   * Verificar interferência temporal recente
   */
  hasRecentTemporalInterference(interactionData) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.interferenceEvents.filter(e => 
      Date.now() - e.timestamp < 10000 // Últimos 10 segundos
    );
    
    const cardType = this.extractCardType(interactionData.cardId);
    
    // Verificar se houve interação recente com carta similar
    return recentEvents.some(e => 
      this.isVisuallyOrSemanticallySimilar(cardType, e.cardType)
    );
  }

  /**
   * Verificar interferência proativa
   */
  hasProactiveInterference(interactionData) {
    const session = this.sessionData.currentSession;
    const cardType = this.extractCardType(interactionData.cardId);
    
    // Verificar se há padrão estabelecido que pode interferir
    const previousInteractions = session.interferenceEvents.filter(e => 
      this.isVisuallyOrSemanticallySimilar(cardType, e.cardType)
    );
    
    return previousInteractions.length > 2; // Interferência se houver múltiplas interações similares
  }

  /**
   * Verificar interferência retroativa
   */
  hasRetroactiveInterference(interactionData) {
    const session = this.sessionData.currentSession;
    const position = interactionData.position;
    
    // Verificar se a posição foi associada a cartas diferentes recentemente
    const positionEvents = session.interferenceEvents.filter(e => 
      e.position.row === position.row && e.position.col === position.col
    );
    
    const uniqueCardTypes = new Set(positionEvents.map(e => e.cardType));
    return uniqueCardTypes.size > 1; // Interferência se múltiplas cartas na mesma posição
  }

  /**
   * Analisar fatores de confusão
   */
  analyzeConfusionFactors(interactionData) {
    const factors = [];
    
    // Fator de similaridade
    const cardType = this.extractCardType(interactionData.cardId);
    const targetCardType = this.extractCardType(interactionData.targetCardId);
    
    if (this.isVisuallyOrSemanticallySimilar(cardType, targetCardType)) {
      const similarity = this.getSimilarityScore(cardType, targetCardType);
      factors.push({
        type: 'semantic_similarity',
        strength: similarity,
        description: `Cartas semanticamente similares (${similarity.toFixed(2)})`
      });
    }
    
    // Fator de proximidade posicional
    if (this.isPositionallySimilar(interactionData.position, interactionData.targetPosition)) {
      const distance = this.calculatePositionalDistance(interactionData.position, interactionData.targetPosition);
      factors.push({
        type: 'positional_proximity',
        strength: 1 - distance, // Quanto menor a distância, maior a confusão
        description: `Posições próximas (distância: ${distance.toFixed(2)})`
      });
    }
    
    // Fator de sobrecarga cognitiva
    const cognitiveLoad = this.calculateCognitiveLoad();
    if (cognitiveLoad > 0.7) {
      factors.push({
        type: 'cognitive_overload',
        strength: cognitiveLoad,
        description: `Alta carga cognitiva (${cognitiveLoad.toFixed(2)})`
      });
    }
    
    return factors;
  }

  /**
   * Obter pontuação de similaridade
   */
  getSimilarityScore(cardType1, cardType2) {
    const session = this.sessionData.currentSession;
    const similarityMap = session.similarityMapping;
    
    if (similarityMap[cardType1]) {
      return similarityMap[cardType1].similarity || 0;
    }
    
    return 0;
  }

  /**
   * Calcular distância posicional
   */
  calculatePositionalDistance(pos1, pos2) {
    if (!pos1 || !pos2) return 1;
    
    const rowDiff = Math.abs(pos1.row - pos2.row);
    const colDiff = Math.abs(pos1.col - pos2.col);
    
    // Distância euclidiana normalizada
    const maxDistance = Math.sqrt(2); // Distância máxima possível entre posições adjacentes
    return Math.sqrt(rowDiff * rowDiff + colDiff * colDiff) / maxDistance;
  }

  /**
   * Calcular carga cognitiva atual
   */
  calculateCognitiveLoad() {
    const session = this.sessionData.currentSession;
    const recentEvents = session.interferenceEvents.filter(e => 
      Date.now() - e.timestamp < 30000 // Últimos 30 segundos
    );
    
    if (recentEvents.length === 0) return 0;
    
    // Calcular carga baseada na frequência de interações e complexidade
    const eventFrequency = recentEvents.length / 30; // eventos por segundo
    const avgReactionTime = recentEvents.reduce((sum, e) => sum + e.reactionTime, 0) / recentEvents.length;
    const errorRate = recentEvents.filter(e => !e.isMatch).length / recentEvents.length;
    
    // Normalizar e combinar fatores
    const frequencyFactor = Math.min(1, eventFrequency / 0.5); // Max 0.5 eventos/segundo
    const timeFactor = Math.min(1, avgReactionTime / 5000); // Max 5 segundos
    const errorFactor = errorRate;
    
    return (frequencyFactor + timeFactor + errorFactor) / 3;
  }

  /**
   * Atualizar matriz de confusão
   */
  updateConfusionMatrix(interferenceEvent) {
    const session = this.sessionData.currentSession;
    const matrix = session.confusionMatrix;
    
    if (!interferenceEvent.isMatch) {
      const cardType = interferenceEvent.cardType;
      const targetCardType = interferenceEvent.targetCardType;
      
      // Atualizar confusões por similaridade
      if (this.isVisuallyOrSemanticallySimilar(cardType, targetCardType)) {
        const key = `${cardType}-${targetCardType}`;
        matrix.similarCards[key] = (matrix.similarCards[key] || 0) + 1;
      }
      
      // Atualizar confusões por posição
      const posKey = `${interferenceEvent.position.row}-${interferenceEvent.position.col}`;
      matrix.positionErrors[posKey] = (matrix.positionErrors[posKey] || 0) + 1;
    }
  }

  /**
   * Detectar padrões de interferência
   */
  detectInterferencePatterns(interferenceEvent) {
    const session = this.sessionData.currentSession;
    
    // Detectar padrão de confusão recorrente
    if (this.isRecurringConfusion(interferenceEvent)) {
      session.memoryConflicts.push({
        type: 'recurring_confusion',
        timestamp: interferenceEvent.timestamp,
        pattern: this.identifyConfusionPattern(interferenceEvent),
        severity: this.calculatePatternSeverity(interferenceEvent)
      });
    }
    
    // Detectar padrão de interferência espacial
    if (this.hasSpatialInterferencePattern(interferenceEvent)) {
      session.memoryConflicts.push({
        type: 'spatial_interference',
        timestamp: interferenceEvent.timestamp,
        pattern: this.identifySpatialPattern(interferenceEvent),
        severity: this.calculateSpatialSeverity(interferenceEvent)
      });
    }
  }

  /**
   * Verificar confusão recorrente
   */
  isRecurringConfusion(interferenceEvent) {
    const session = this.sessionData.currentSession;
    const cardType = interferenceEvent.cardType;
    
    // Contar erros similares nos últimos eventos
    const similarErrors = session.interferenceEvents.filter(e => 
      !e.isMatch && 
      e.cardType === cardType &&
      e.timestamp > Date.now() - 60000 // Último minuto
    );
    
    return similarErrors.length >= 3;
  }

  /**
   * Identificar padrão de confusão
   */
  identifyConfusionPattern(interferenceEvent) {
    const cardType = interferenceEvent.cardType;
    const confusedWith = interferenceEvent.targetCardType;
    
    return {
      sourceCard: cardType,
      confusedCard: confusedWith,
      similarity: this.getSimilarityScore(cardType, confusedWith),
      frequency: this.getConfusionFrequency(cardType, confusedWith)
    };
  }

  /**
   * Calcular severidade do padrão
   */
  calculatePatternSeverity(interferenceEvent) {
    const frequency = this.getConfusionFrequency(
      interferenceEvent.cardType, 
      interferenceEvent.targetCardType
    );
    
    // Severidade baseada na frequência e tempo de reação
    const frequencySeverity = Math.min(1, frequency / 5); // Max 5 confusões
    const timeSeverity = Math.min(1, interferenceEvent.reactionTime / 8000); // Max 8 segundos
    
    return (frequencySeverity + timeSeverity) / 2;
  }

  /**
   * Obter frequência de confusão
   */
  getConfusionFrequency(cardType1, cardType2) {
    const session = this.sessionData.currentSession;
    const key = `${cardType1}-${cardType2}`;
    
    return session.confusionMatrix.similarCards[key] || 0;
  }

  /**
   * Verificar padrão de interferência espacial
   */
  hasSpatialInterferencePattern(interferenceEvent) {
    const session = this.sessionData.currentSession;
    const position = interferenceEvent.position;
    const posKey = `${position.row}-${position.col}`;
    
    // Verificar se a posição tem histórico de erros
    return (session.confusionMatrix.positionErrors[posKey] || 0) >= 2;
  }

  /**
   * Identificar padrão espacial
   */
  identifySpatialPattern(interferenceEvent) {
    const position = interferenceEvent.position;
    
    return {
      position: position,
      errorCount: this.getPositionErrorCount(position),
      interferenceType: this.classifyPositionalInterference(position)
    };
  }

  /**
   * Calcular severidade espacial
   */
  calculateSpatialSeverity(interferenceEvent) {
    const errorCount = this.getPositionErrorCount(interferenceEvent.position);
    return Math.min(1, errorCount / 5); // Max 5 erros
  }

  /**
   * Obter contagem de erros por posição
   */
  getPositionErrorCount(position) {
    const session = this.sessionData.currentSession;
    const posKey = `${position.row}-${position.col}`;
    
    return session.confusionMatrix.positionErrors[posKey] || 0;
  }

  /**
   * Classificar interferência posicional
   */
  classifyPositionalInterference(position) {
    // Classificações simples baseadas na posição
    if (position.row === 0 || position.col === 0) {
      return 'edge_interference';
    }
    
    return 'central_interference';
  }

  /**
   * Atualizar métricas de interferência
   */
  updateInterferenceMetrics(interferenceEvent) {
    const session = this.sessionData.currentSession;
    const metrics = session.interferenceMetrics;
    
    // Atualizar interferência proativa
    if (interferenceEvent.interferenceTypes.includes('proactive_interference')) {
      metrics.proactiveInterference += 0.1;
    }
    
    // Atualizar interferência retroativa
    if (interferenceEvent.interferenceTypes.includes('retroactive_interference')) {
      metrics.retroactiveInterference += 0.1;
    }
    
    // Atualizar confusão por similaridade
    if (interferenceEvent.interferenceTypes.includes('semantic_visual_similarity')) {
      metrics.similarityBasedConfusion += 0.15;
    }
    
    // Atualizar confusão posicional
    if (interferenceEvent.interferenceTypes.includes('positional_proximity')) {
      metrics.positionBasedConfusion += 0.1;
    }
    
    // Atualizar interferência semântica
    const semanticFactors = interferenceEvent.confusionFactors.filter(f => 
      f.type === 'semantic_similarity'
    );
    if (semanticFactors.length > 0) {
      metrics.semanticInterference += semanticFactors[0].strength * 0.2;
    }
    
    // Atualizar interferência visual (baseada na similaridade)
    if (interferenceEvent.interferenceTypes.includes('semantic_visual_similarity')) {
      metrics.visualInterference += 0.12;
    }
    
    // Normalizar métricas para evitar valores muito altos
    Object.keys(metrics).forEach(key => {
      metrics[key] = Math.min(1, metrics[key]);
    });
  }

  /**
   * Análise completa dos dados coletados
   */
  async analyze(gameData = {}) {
    const session = this.sessionData.currentSession;
    if (!session) {
      return this.getEmptyAnalysis();
    }

    const analysis = {
      collectorName: this.name,
      version: this.version,
      sessionId: session.sessionId,
      timestamp: Date.now(),
      
      // Dados básicos da sessão
      sessionSummary: {
        totalEvents: session.interferenceEvents.length,
        sessionDuration: Date.now() - session.startTime,
        difficulty: session.difficulty,
        theme: session.theme,
        totalErrors: session.interferenceEvents.filter(e => !e.isMatch).length,
        completed: gameData.completed || false
      },
      
      // Análise de padrões de interferência
      interferencePatterns: {
        proactiveInterference: metrics.proactiveInterference,
        retroactiveInterference: metrics.retroactiveInterference,
        similarityBasedConfusion: metrics.similarityBasedConfusion,
        positionBasedConfusion: metrics.positionBasedConfusion,
        semanticInterference: metrics.semanticInterference,
        visualInterference: metrics.visualInterference,
        dominantPattern: this.identifyDominantInterferencePattern(),
        interferenceFrequency: this.calculateInterferenceFrequency()
      },
      
      // Análise da matriz de confusão
      confusionAnalysis: {
        confusionMatrix: session.confusionMatrix,
        mostConfusedPairs: this.identifyMostConfusedPairs(),
        problematicPositions: this.identifyProblematicPositions(),
        confusionClusters: this.identifyConfusionClusters(),
        confusionSeverity: this.calculateOverallConfusionSeverity()
      },
      
      // Análise de conflitos de memória
      memoryConflicts: {
        totalConflicts: session.memoryConflicts.length,
        conflictTypes: this.analyzeConflictTypes(),
        severityDistribution: this.analyzeConflictSeverity(),
        temporalDistribution: this.analyzeConflictTiming(),
        resolutionPatterns: this.analyzeConflictResolution()
      },
      
      // Análise de fatores de confusão
      confusionFactors: {
        cognitiveLoadImpact: this.analyzeCognitiveLoadImpact(),
        similarityImpact: this.analyzeSimilarityImpact(),
        positionalImpact: this.analyzePositionalImpact(),
        temporalImpact: this.analyzeTemporalImpact(),
        cumulativeEffect: this.analyzeCumulativeConfusionEffect()
      },
      
      // Recomendações terapêuticas
      therapeuticRecommendations: this.generateTherapeuticRecommendations(),
      
      // Pontuação de capacidades
      capacityScores: {
        interferenceResistance: this.calculateInterferenceResistanceScore(),
        conflictResolution: this.calculateConflictResolutionScore(),
        cognitiveFlexibility: this.calculateCognitiveFlexibilityScore(),
        attentionalControl: this.calculateAttentionalControlScore()
      }
    };

    // Corrigir referência a metrics
    const metrics = session.interferenceMetrics;
    analysis.interferencePatterns.proactiveInterference = metrics.proactiveInterference;
    analysis.interferencePatterns.retroactiveInterference = metrics.retroactiveInterference;
    analysis.interferencePatterns.similarityBasedConfusion = metrics.similarityBasedConfusion;
    analysis.interferencePatterns.positionBasedConfusion = metrics.positionBasedConfusion;
    analysis.interferencePatterns.semanticInterference = metrics.semanticInterference;
    analysis.interferencePatterns.visualInterference = metrics.visualInterference;

    // Armazenar análise
    this.collectedData.push(analysis);
    
    console.log('🔀 InterferencePatternCollector: Análise completa gerada', {
      dominantPattern: analysis.interferencePatterns.dominantPattern,
      totalConflicts: analysis.memoryConflicts.totalConflicts,
      therapeuticRecommendations: analysis.therapeuticRecommendations.length
    });

    return analysis;
  }

  /**
   * Identificar padrão de interferência dominante
   */
  identifyDominantInterferencePattern() {
    const session = this.sessionData.currentSession;
    const metrics = session.interferenceMetrics;
    
    const patterns = [
      { name: 'proactive_interference', value: metrics.proactiveInterference },
      { name: 'retroactive_interference', value: metrics.retroactiveInterference },
      { name: 'similarity_based_confusion', value: metrics.similarityBasedConfusion },
      { name: 'position_based_confusion', value: metrics.positionBasedConfusion },
      { name: 'semantic_interference', value: metrics.semanticInterference },
      { name: 'visual_interference', value: metrics.visualInterference }
    ];
    
    const dominantPattern = patterns.reduce((max, pattern) => 
      pattern.value > max.value ? pattern : max
    );
    
    return {
      type: dominantPattern.name,
      strength: dominantPattern.value,
      description: this.getPatternDescription(dominantPattern.name)
    };
  }

  /**
   * Obter descrição do padrão
   */
  getPatternDescription(patternName) {
    const descriptions = {
      'proactive_interference': 'Conhecimento anterior interfere no novo aprendizado',
      'retroactive_interference': 'Novas informações interferem no conhecimento consolidado',
      'similarity_based_confusion': 'Confusão entre elementos visualmente ou semanticamente similares',
      'position_based_confusion': 'Confusão baseada na proximidade espacial',
      'semantic_interference': 'Interferência no nível do significado conceitual',
      'visual_interference': 'Interferência no processamento visual das características'
    };
    
    return descriptions[patternName] || 'Padrão de interferência não identificado';
  }

  /**
   * Calcular frequência de interferência
   */
  calculateInterferenceFrequency() {
    const session = this.sessionData.currentSession;
    const totalEvents = session.interferenceEvents.length;
    
    if (totalEvents === 0) return 0;
    
    const interferenceEvents = session.interferenceEvents.filter(e => 
      e.interferenceTypes.length > 0
    );
    
    return interferenceEvents.length / totalEvents;
  }

  /**
   * Identificar pares mais confundidos
   */
  identifyMostConfusedPairs() {
    const session = this.sessionData.currentSession;
    const similarCards = session.confusionMatrix.similarCards;
    
    const sortedPairs = Object.entries(similarCards)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([pair, count]) => {
        const [card1, card2] = pair.split('-');
        return {
          card1,
          card2,
          confusionCount: count,
          similarity: this.getSimilarityScore(card1, card2)
        };
      });
    
    return sortedPairs;
  }

  /**
   * Identificar posições problemáticas
   */
  identifyProblematicPositions() {
    const session = this.sessionData.currentSession;
    const positionErrors = session.confusionMatrix.positionErrors;
    
    const sortedPositions = Object.entries(positionErrors)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([posKey, count]) => {
        const [row, col] = posKey.split('-').map(Number);
        return {
          position: { row, col },
          errorCount: count,
          errorRate: this.calculatePositionErrorRate({ row, col })
        };
      });
    
    return sortedPositions;
  }

  /**
   * Calcular taxa de erro por posição
   */
  calculatePositionErrorRate(position) {
    const session = this.sessionData.currentSession;
    const posKey = `${position.row}-${position.col}`;
    
    const totalInteractions = session.interferenceEvents.filter(e => 
      e.position.row === position.row && e.position.col === position.col
    ).length;
    
    const errors = session.confusionMatrix.positionErrors[posKey] || 0;
    
    return totalInteractions > 0 ? errors / totalInteractions : 0;
  }

  /**
   * Identificar clusters de confusão
   */
  identifyConfusionClusters() {
    const session = this.sessionData.currentSession;
    const similarCards = session.confusionMatrix.similarCards;
    
    // Agrupar confusões por tema ou tipo
    const clusters = {};
    
    Object.entries(similarCards).forEach(([pair, count]) => {
      const [card1, card2] = pair.split('-');
      const category = this.getCardCategory(card1);
      
      if (!clusters[category]) {
        clusters[category] = [];
      }
      
      clusters[category].push({
        pair: [card1, card2],
        confusionCount: count
      });
    });
    
    return clusters;
  }

  /**
   * Obter categoria da carta
   */
  getCardCategory(cardType) {
    const categories = {
      'pig': 'animal',
      'cow': 'animal',
      'apple': 'fruit',
      'banana': 'fruit',
      'orange': 'fruit',
      'car': 'vehicle',
      'airplane': 'vehicle',
      'bicycle': 'vehicle',
      'train': 'vehicle',
      'star': 'celestial',
      'moon': 'celestial',
      'rocket': 'space_object',
      'planet': 'celestial',
      'sun': 'celestial',
      'bright_star': 'celestial',
      'flower': 'plant',
      'butterfly': 'insect',
      'hibiscus': 'plant',
      'sunflower': 'plant',
      'bee': 'insect',
      'tulip': 'plant',
      'rose': 'plant',
      'daisy': 'plant'
    };
    
    return categories[cardType] || 'unknown';
  }

  /**
   * Calcular severidade geral da confusão
   */
  calculateOverallConfusionSeverity() {
    const session = this.sessionData.currentSession;
    const totalEvents = session.interferenceEvents.length;
    
    if (totalEvents === 0) return 0;
    
    const errorEvents = session.interferenceEvents.filter(e => !e.isMatch);
    const errorRate = errorEvents.length / totalEvents;
    
    // Severidade baseada na taxa de erro e fatores de confusão
    const avgConfusionFactors = errorEvents.reduce((sum, event) => 
      sum + event.confusionFactors.length, 0
    ) / Math.max(errorEvents.length, 1);
    
    return (errorRate * 0.7 + (avgConfusionFactors / 3) * 0.3);
  }

  /**
   * Analisar tipos de conflito
   */
  analyzeConflictTypes() {
    const session = this.sessionData.currentSession;
    const conflicts = session.memoryConflicts;
    
    const typeCount = {};
    conflicts.forEach(conflict => {
      typeCount[conflict.type] = (typeCount[conflict.type] || 0) + 1;
    });
    
    return typeCount;
  }

  /**
   * Analisar severidade dos conflitos
   */
  analyzeConflictSeverity() {
    const session = this.sessionData.currentSession;
    const conflicts = session.memoryConflicts;
    
    const severityLevels = { low: 0, medium: 0, high: 0 };
    
    conflicts.forEach(conflict => {
      if (conflict.severity < 0.3) severityLevels.low++;
      else if (conflict.severity < 0.7) severityLevels.medium++;
      else severityLevels.high++;
    });
    
    return severityLevels;
  }

  /**
   * Analisar timing dos conflitos
   */
  analyzeConflictTiming() {
    const session = this.sessionData.currentSession;
    const conflicts = session.memoryConflicts;
    const sessionStart = session.startTime;
    const sessionDuration = Date.now() - sessionStart;
    
    // Dividir sessão em terços
    const thirdDuration = sessionDuration / 3;
    const timing = { early: 0, middle: 0, late: 0 };
    
    conflicts.forEach(conflict => {
      const conflictTime = conflict.timestamp - sessionStart;
      
      if (conflictTime < thirdDuration) timing.early++;
      else if (conflictTime < thirdDuration * 2) timing.middle++;
      else timing.late++;
    });
    
    return timing;
  }

  /**
   * Analisar resolução de conflitos
   */
  analyzeConflictResolution() {
    // Análise simplificada - pode ser expandida
    const session = this.sessionData.currentSession;
    const recentEvents = session.interferenceEvents.slice(-10);
    
    const recentErrors = recentEvents.filter(e => !e.isMatch).length;
    const resolutionRate = 1 - (recentErrors / recentEvents.length);
    
    return {
      resolutionRate,
      pattern: resolutionRate > 0.7 ? 'good_resolution' : resolutionRate > 0.4 ? 'moderate_resolution' : 'poor_resolution'
    };
  }

  /**
   * Analisar impacto da carga cognitiva
   */
  analyzeCognitiveLoadImpact() {
    const session = this.sessionData.currentSession;
    const events = session.interferenceEvents;
    
    const highLoadEvents = events.filter(e => {
      const load = this.calculateCognitiveLoad();
      return load > 0.7;
    });
    
    const highLoadErrors = highLoadEvents.filter(e => !e.isMatch).length;
    const errorRate = highLoadEvents.length > 0 ? highLoadErrors / highLoadEvents.length : 0;
    
    return {
      highLoadEventCount: highLoadEvents.length,
      errorRateUnderLoad: errorRate,
      loadSensitivity: errorRate > 0.5 ? 'high' : errorRate > 0.3 ? 'medium' : 'low'
    };
  }

  /**
   * Analisar impacto da similaridade
   */
  analyzeSimilarityImpact() {
    const session = this.sessionData.currentSession;
    const events = session.interferenceEvents;
    
    const similarityEvents = events.filter(e => 
      e.interferenceTypes.includes('semantic_visual_similarity')
    );
    
    const similarityErrors = similarityEvents.filter(e => !e.isMatch).length;
    const errorRate = similarityEvents.length > 0 ? similarityErrors / similarityEvents.length : 0;
    
    return {
      similarityEventCount: similarityEvents.length,
      errorRateWithSimilarity: errorRate,
      similaritySensitivity: errorRate > 0.6 ? 'high' : errorRate > 0.4 ? 'medium' : 'low'
    };
  }

  /**
   * Analisar impacto posicional
   */
  analyzePositionalImpact() {
    const session = this.sessionData.currentSession;
    const events = session.interferenceEvents;
    
    const positionalEvents = events.filter(e => 
      e.interferenceTypes.includes('positional_proximity')
    );
    
    const positionalErrors = positionalEvents.filter(e => !e.isMatch).length;
    const errorRate = positionalEvents.length > 0 ? positionalErrors / positionalEvents.length : 0;
    
    return {
      positionalEventCount: positionalEvents.length,
      errorRateWithProximity: errorRate,
      positionalSensitivity: errorRate > 0.5 ? 'high' : errorRate > 0.3 ? 'medium' : 'low'
    };
  }

  /**
   * Analisar impacto temporal
   */
  analyzeTemporalImpact() {
    const session = this.sessionData.currentSession;
    const events = session.interferenceEvents;
    
    const temporalEvents = events.filter(e => 
      e.interferenceTypes.includes('temporal_proximity')
    );
    
    const temporalErrors = temporalEvents.filter(e => !e.isMatch).length;
    const errorRate = temporalEvents.length > 0 ? temporalErrors / temporalEvents.length : 0;
    
    return {
      temporalEventCount: temporalEvents.length,
      errorRateWithTempo: errorRate,
      temporalSensitivity: errorRate > 0.5 ? 'high' : errorRate > 0.3 ? 'medium' : 'low'
    };
  }

  /**
   * Analisar efeito cumulativo da confusão
   */
  analyzeCumulativeConfusionEffect() {
    const session = this.sessionData.currentSession;
    const events = session.interferenceEvents;
    
    if (events.length < 10) return { effect: 'insufficient_data', strength: 0 };
    
    // Analisar se a confusão aumenta ao longo do tempo
    const firstHalf = events.slice(0, Math.floor(events.length / 2));
    const secondHalf = events.slice(Math.floor(events.length / 2));
    
    const firstHalfErrors = firstHalf.filter(e => !e.isMatch).length / firstHalf.length;
    const secondHalfErrors = secondHalf.filter(e => !e.isMatch).length / secondHalf.length;
    
    const cumulative = secondHalfErrors - firstHalfErrors;
    
    return {
      effect: cumulative > 0.1 ? 'increasing' : cumulative < -0.1 ? 'decreasing' : 'stable',
      strength: Math.abs(cumulative),
      firstHalfErrorRate: firstHalfErrors,
      secondHalfErrorRate: secondHalfErrors
    };
  }

  /**
   * Calcular pontuações de capacidade
   */
  calculateInterferenceResistanceScore() {
    const session = this.sessionData.currentSession;
    const metrics = session.interferenceMetrics;
    
    // Resistência = baixa interferência
    const avgInterference = (
      metrics.proactiveInterference +
      metrics.retroactiveInterference +
      metrics.similarityBasedConfusion +
      metrics.positionBasedConfusion
    ) / 4;
    
    return Math.min(100, Math.round((1 - avgInterference) * 100));
  }

  calculateConflictResolutionScore() {
    const session = this.sessionData.currentSession;
    const conflicts = session.memoryConflicts;
    const totalEvents = session.interferenceEvents.length;
    
    if (totalEvents === 0) return 50;
    
    const conflictRate = conflicts.length / totalEvents;
    const resolutionAnalysis = this.analyzeConflictResolution();
    
    return Math.min(100, Math.round(
      ((1 - conflictRate) * 50 + resolutionAnalysis.resolutionRate * 50)
    ));
  }

  calculateCognitiveFlexibilityScore() {
    const loadImpact = this.analyzeCognitiveLoadImpact();
    const cumulativeEffect = this.analyzeCumulativeConfusionEffect();
    
    let score = 70; // Base score
    
    // Penalizar alta sensibilidade à carga
    if (loadImpact.loadSensitivity === 'high') score -= 30;
    else if (loadImpact.loadSensitivity === 'medium') score -= 15;
    
    // Bonificar resistência ao efeito cumulativo
    if (cumulativeEffect.effect === 'decreasing') score += 20;
    else if (cumulativeEffect.effect === 'increasing') score -= 20;
    
    return Math.max(0, Math.min(100, score));
  }

  calculateAttentionalControlScore() {
    const similarityImpact = this.analyzeSimilarityImpact();
    const positionalImpact = this.analyzePositionalImpact();
    const temporalImpact = this.analyzeTemporalImpact();
    
    let score = 70; // Base score
    
    // Penalizar alta sensibilidade a diferentes tipos de interferência
    if (similarityImpact.similaritySensitivity === 'high') score -= 15;
    if (positionalImpact.positionalSensitivity === 'high') score -= 10;
    if (temporalImpact.temporalSensitivity === 'high') score -= 15;
    
    return Math.max(0, Math.min(100, score));
  }

  /**
   * Gerar recomendações terapêuticas
   */
  generateTherapeuticRecommendations() {
    const recommendations = [];
    const session = this.sessionData.currentSession;
    const dominantPattern = this.identifyDominantInterferencePattern();
    
    // Recomendações baseadas no padrão dominante
    if (dominantPattern.strength > 0.4) {
      recommendations.push({
        area: 'interference_management',
        priority: 'high',
        title: `Reduzir ${dominantPattern.type.replace('_', ' ')}`,
        description: dominantPattern.description,
        activities: this.getInterferenceActivities(dominantPattern.type)
      });
    }
    
    // Recomendações baseadas na carga cognitiva
    const loadImpact = this.analyzeCognitiveLoadImpact();
    if (loadImpact.loadSensitivity === 'high') {
      recommendations.push({
        area: 'cognitive_load_management',
        priority: 'medium',
        title: 'Gerenciar Carga Cognitiva',
        description: 'Alta sensibilidade à sobrecarga cognitiva.',
        activities: [
          'Técnicas de respiração e relaxamento',
          'Divisão de tarefas em etapas menores',
          'Pausas estratégicas durante a atividade'
        ]
      });
    }
    
    // Recomendações baseadas na confusão por similaridade
    const similarityImpact = this.analyzeSimilarityImpact();
    if (similarityImpact.similaritySensitivity === 'high') {
      recommendations.push({
        area: 'similarity_discrimination',
        priority: 'medium',
        title: 'Melhorar Discriminação de Similaridades',
        description: 'Dificuldade para distinguir elementos similares.',
        activities: [
          'Exercícios de discriminação visual',
          'Técnicas de atenção aos detalhes',
          'Treinamento de características distintivas'
        ]
      });
    }
    
    return recommendations;
  }

  /**
   * Obter atividades para diferentes tipos de interferência
   */
  getInterferenceActivities(interferenceType) {
    const activities = {
      'proactive_interference': [
        'Técnicas de esquecimento dirigido',
        'Estratégias de compartimentalização',
        'Exercícios de mudança de contexto'
      ],
      'retroactive_interference': [
        'Consolidação através de repetição',
        'Técnicas de elaboração semântica',
        'Proteção da memória através do sono'
      ],
      'similarity_based_confusion': [
        'Treinamento de discriminação visual',
        'Exercícios de categorização',
        'Desenvolvimento de estratégias de codificação'
      ],
      'position_based_confusion': [
        'Treinamento de memória espacial',
        'Técnicas de navegação mental',
        'Exercícios de orientação visuoespacial'
      ]
    };
    
    return activities[interferenceType] || [
      'Exercícios de atenção sustentada',
      'Técnicas de controle inibitório',
      'Treinamento de flexibilidade cognitiva'
    ];
  }

  /**
   * Obter análise vazia
   */
  getEmptyAnalysis() {
    return {
      collectorName: this.name,
      version: this.version,
      sessionId: null,
      timestamp: Date.now(),
      error: 'No session data available',
      interferencePatterns: null,
      confusionAnalysis: null,
      memoryConflicts: null,
      confusionFactors: null,
      therapeuticRecommendations: [],
      capacityScores: {
        interferenceResistance: 0,
        conflictResolution: 0,
        cognitiveFlexibility: 0,
        attentionalControl: 0
      }
    };
  }

  /**
   * Finalizar sessão
   */
  endSession() {
    if (this.sessionData.currentSession) {
      console.log(`🔀 InterferencePatternCollector: Sessão finalizada - ${this.sessionData.currentSession.sessionId}`);
      this.sessionData.currentSession = null;
    }
  }

  /**
   * Obter dados coletados
   */
  getCollectedData() {
    return {
      collectorName: this.name,
      version: this.version,
      isActive: this.isActive,
      totalSessions: this.collectedData.length,
      lastSession: this.sessionData.currentSession,
      collectedData: this.collectedData
    };
  }

  /**
   * Limpar dados
   */
  clearData() {
    this.collectedData = [];
    this.sessionData = {
      currentSession: null,
      interferenceEvents: [],
      memoryConflicts: [],
      confusionPatterns: []
    };
    console.log('🔀 InterferencePatternCollector: Dados limpos');
  }
}
