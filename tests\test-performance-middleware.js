/**
 * Script de teste do middleware de monitoramento de performance
 * Portal Betina V3
 */

import { monitorPerformance, getCurrentPerformanceMetrics, getUnresolvedAlerts, autoResolveAlerts } from './src/api/middleware/monitoring/performance.js';
import { createLogger } from './src/utils/logger.js';

const logger = createLogger('performance-test');

// Mock de requisição HTTP
const mockRequest = (endpoint, user = null) => ({
  originalUrl: endpoint,
  method: 'GET',
  ip: '127.0.0.1',
  user: user
});

// Mock de resposta HTTP
const mockResponse = () => {
  const res = {};
  res.on = (event, callback) => {
    if (event === 'finish') {
      // Simulamos o evento finish após 100-500ms
      setTimeout(callback, Math.random() * 400 + 100);
    }
    return res;
  };
  res.status = (code) => {
    res.statusCode = code;
    return res;
  };
  res.json = (data) => {
    res.body = data;
    return res;
  };
  res.end = () => {
    if (res._onFinish) res._onFinish();
    return res;
  };
  return res;
};

// Função para simular requisições em paralelo
async function simulateRequests(count = 10, delay = 100, therapeutic = false) {
  logger.info(`Simulando ${count} requisições ${therapeutic ? 'terapêuticas' : 'normais'}`);
  
  const endpoints = therapeutic 
    ? ['/games/memory', '/games/attention', '/therapeutic/session', '/dashboard/progress', '/progress/metrics']
    : ['/api/health', '/api/status', '/login', '/register', '/about'];
  
  const users = [
    { id: 'user1', role: 'patient' },
    { id: 'user2', role: 'therapist' },
    { id: 'user3', role: 'admin' },
    null // Sem usuário (anônimo)
  ];
  
  for (let i = 0; i < count; i++) {
    const req = mockRequest(
      endpoints[Math.floor(Math.random() * endpoints.length)],
      users[Math.floor(Math.random() * users.length)]
    );
    const res = mockResponse();
    
    monitorPerformance(req, res, () => {});
    
    // Simular fim da requisição
    setTimeout(() => {
      res.status(200).json({ success: true }).end();
    }, Math.random() * delay + 50);
    
    // Aguardar um pouco entre requisições
    if (i < count - 1) {
      await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
    }
  }
}

// Função para simular requisição lenta que gera alerta
async function simulateSlowRequest() {
  logger.info('Simulando requisição lenta que deve gerar alerta');
  
  const req = mockRequest('/games/heavy-game', { id: 'slowUser', role: 'patient' });
  const res = mockResponse();
  
  // Definir a função _onFinish manualmente para garantir que o evento 'finish' seja disparado
  res._onFinish = () => {
    // Simulamos o evento 'finish' sendo disparado após o tempo de resposta
    if (res._finishCallback) {
      res._finishCallback();
    }
  };
  
  // Armazenar o callback do 'finish' para chamar manualmente
  const originalOn = res.on;
  res.on = (event, callback) => {
    if (event === 'finish') {
      res._finishCallback = callback;
    }
    return originalOn(event, callback);
  };
  
  monitorPerformance(req, res, () => {});
  
  // Simular resposta lenta (4 segundos)
  logger.info('Iniciando requisição lenta (4 segundos)');
  setTimeout(() => {
    logger.info('Finalizando requisição lenta após 4 segundos');
    res.status(200).json({ success: true }).end();
  }, 4000);
  
  // Aguardar para garantir que o alerta seja processado
  await new Promise(resolve => setTimeout(resolve, 4500));
}

// Função para simular alta carga no sistema
async function simulateHighLoad() {
  logger.info('Simulando alta carga no sistema (muitas requisições concorrentes)');
  
  // Várias requisições em paralelo
  const promises = [];
  const requestCount = 50; // Número elevado para estressar o sistema
  
  for (let i = 0; i < requestCount; i++) {
    const endpoint = `/api/intensive-task/${i}`;
    const req = mockRequest(endpoint, { id: `load-test-user-${i % 5}`, role: 'patient' });
    const res = mockResponse();
    
    monitorPerformance(req, res, () => {});
    
    // Adicionar à lista de promessas
    promises.push(new Promise(resolve => {
      setTimeout(() => {
        res.status(200).json({ success: true }).end();
        resolve();
      }, 50); // Respostas rápidas, mas muitas em paralelo
    }));
  }
  
  // Esperar todas as requisições terminarem
  await Promise.all(promises);
  logger.info(`${requestCount} requisições concorrentes processadas`);
  
  // Pequeno delay para permitir que as métricas sejam atualizadas
  await new Promise(resolve => setTimeout(resolve, 500));
}

// Função para testar a resolução automática de alertas
async function testAutoResolveAlerts() {
  logger.info('Testando resolução automática de alertas');
  
  // Verificar alertas não resolvidos antes
  const alertsBefore = getUnresolvedAlerts();
  logger.info(`Alertas não resolvidos antes: ${alertsBefore.length}`);
  
  // Na primeira tentativa pode não resolver se não passou tempo suficiente
  let resolvedCount = autoResolveAlerts();
  logger.info(`Alertas resolvidos na primeira tentativa: ${resolvedCount}`);
  
  if (resolvedCount === 0 && alertsBefore.length > 0) {
    // Se não resolveu nenhum e temos alertas, aguardar mais tempo (12 segundos para superar os 10 segundos configurados)
    logger.info('Aguardando 12 segundos para permitir resolução automática de alertas de tempo de resposta...');
    await new Promise(resolve => setTimeout(resolve, 12000));
    
    // Tentar novamente
    resolvedCount = autoResolveAlerts();
    logger.info(`Alertas resolvidos na segunda tentativa (após espera): ${resolvedCount}`);
  }
  
  // Verificar alertas não resolvidos depois
  const alertsAfter = getUnresolvedAlerts();
  logger.info(`Alertas não resolvidos após resolução: ${alertsAfter.length}`);
  
  // Se ainda tivermos alertas não resolvidos, vamos verificar seus tipos
  if (alertsAfter.length > 0) {
    const alertTypes = {};
    alertsAfter.forEach(alert => {
      alertTypes[alert.type] = (alertTypes[alert.type] || 0) + 1;
    });
    logger.info('Tipos de alertas ainda não resolvidos:', alertTypes);
  }
}

// Função principal para testar o middleware
async function testPerformanceMiddleware() {
  logger.info('Iniciando teste do middleware de performance');
  
  // Simular várias requisições normais
  await simulateRequests(15, 200);
  
  // Aguardar um pouco
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Simular várias requisições terapêuticas
  await simulateRequests(20, 300, true);
  
  // Simular requisição lenta para gerar alerta
  await simulateSlowRequest();
  
  // Aguardar para ver o alerta ser gerado
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Testar carga intensiva
  logger.info('Iniciando teste de carga intensiva');
  await simulateHighLoad();
  
  // Aguardar para processar métricas
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Obter métricas atuais
  const metrics = getCurrentPerformanceMetrics();
  logger.info('Métricas de performance coletadas:', metrics);
  
  // Log detalhado do uso de memória para depuração
  const memoryUsage = process.memoryUsage();
  logger.info('Detalhes do uso de memória:', {
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + 'MB',
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + 'MB',
    rss: Math.round(memoryUsage.rss / 1024 / 1024) + 'MB',
    percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100) + '%',
    currentThresholds: {
      warning: Math.round(PERFORMANCE_THRESHOLDS.memoryUsage.warning * 100) + '%',
      critical: Math.round(PERFORMANCE_THRESHOLDS.memoryUsage.critical * 100) + '%'
    }
  });
  
  // Verificar alertas
  const alerts = getUnresolvedAlerts();
  logger.info(`Alertas não resolvidos: ${alerts.length}`, { alertCount: alerts.length });
  if (alerts.length > 0) {
    logger.info('Detalhes dos alertas:', alerts);
    
    // Testar resolução automática de alertas
    await testAutoResolveAlerts();
  } else {
    logger.warn('Nenhum alerta gerado, mesmo com requisição lenta simulada. Possível problema com os thresholds.');
  }
  
  // Verificar thresholds configurados
  try {
    const { PERFORMANCE_THRESHOLDS } = await import('./src/api/middleware/monitoring/performance.js');
    logger.info('Thresholds configurados:', {
      responseTime: PERFORMANCE_THRESHOLDS.responseTime,
      memory: PERFORMANCE_THRESHOLDS.memoryUsage,
      cpu: PERFORMANCE_THRESHOLDS.cpuUsage,
      users: PERFORMANCE_THRESHOLDS.concurrentUsers
    });
  } catch (error) {
    logger.error('Não foi possível obter os thresholds configurados:', error);
  }
  
  logger.info('Teste finalizado com sucesso');
}

// Executar o teste
testPerformanceMiddleware().catch(error => {
  logger.error('Erro durante o teste de performance:', error);
});
