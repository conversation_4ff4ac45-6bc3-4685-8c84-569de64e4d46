/**
 * InputValidator - Utilitário para validação e sanitização de entradas
 */
export class InputValidator {
  /**
   * Sanitiza dados de métricas
   * @param {Object} metricsData - Dados de métricas
   * @returns {Object} Dados sanitizados
   */
  static sanitizeMetricsData(metricsData) {
    if (!metricsData || typeof metricsData !== 'object') {
      return {};
    }

    const sanitized = {};
    
    // Sanitizar campos numéricos
    if (typeof metricsData.accuracy === 'number' && !isNaN(metricsData.accuracy)) {
      sanitized.accuracy = Math.max(0, Math.min(1, metricsData.accuracy));
    }
    
    if (typeof metricsData.responseTime === 'number' && !isNaN(metricsData.responseTime)) {
      sanitized.responseTime = Math.max(0, metricsData.responseTime);
    }
    
    if (typeof metricsData.duration === 'number' && !isNaN(metricsData.duration)) {
      sanitized.duration = Math.max(0, metricsData.duration);
    }

    // Sanitizar strings
    if (typeof metricsData.sessionId === 'string') {
      sanitized.sessionId = metricsData.sessionId.trim().substring(0, 100);
    }
    
    if (typeof metricsData.childId === 'string') {
      sanitized.childId = metricsData.childId.trim().substring(0, 100);
    }
    
    if (typeof metricsData.gameName === 'string') {
      sanitized.gameName = metricsData.gameName.trim().substring(0, 50);
    }

    // Sanitizar arrays
    if (Array.isArray(metricsData.interactions)) {
      sanitized.interactions = metricsData.interactions.slice(0, 1000); // Limitar a 1000 interações
    }

    return sanitized;
  }

  /**
   * Sanitiza dados de eventos
   * @param {Object} eventData - Dados do evento
   * @returns {Object} Dados sanitizados
   */
  static sanitizeEventData(eventData) {
    if (!eventData || typeof eventData !== 'object') {
      return {};
    }

    const sanitized = {};
    
    // Sanitizar tipo de evento
    if (typeof eventData.type === 'string') {
      sanitized.type = eventData.type.trim().substring(0, 50);
    }
    
    // Sanitizar dados do evento
    if (eventData.data && typeof eventData.data === 'object') {
      sanitized.data = this.sanitizeMetricsData(eventData.data);
    }
    
    // Sanitizar timestamp
    if (typeof eventData.timestamp === 'number') {
      sanitized.timestamp = eventData.timestamp;
    } else if (typeof eventData.timestamp === 'string') {
      const parsed = new Date(eventData.timestamp);
      if (!isNaN(parsed.getTime())) {
        sanitized.timestamp = parsed.getTime();
      }
    }
    
    if (!sanitized.timestamp) {
      sanitized.timestamp = Date.now();
    }

    return sanitized;
  }

  /**
   * Valida se um ID é válido
   * @param {string} id - ID para validar
   * @returns {boolean} True se válido
   */
  static isValidId(id) {
    return typeof id === 'string' && id.trim().length > 0 && id.length <= 100;
  }

  /**
   * Valida se um nome de jogo é válido
   * @param {string} gameName - Nome do jogo
   * @returns {boolean} True se válido
   */
  static isValidGameName(gameName) {
    return typeof gameName === 'string' && 
           gameName.trim().length > 0 && 
           gameName.length <= 50 &&
           /^[a-zA-Z0-9_-]+$/.test(gameName);
  }

  /**
   * Valida se dados de sessão são válidos
   * @param {Object} sessionData - Dados da sessão
   * @returns {boolean} True se válido
   */
  static isValidSessionData(sessionData) {
    return sessionData &&
           typeof sessionData === 'object' &&
           this.isValidId(sessionData.sessionId) &&
           this.isValidId(sessionData.childId) &&
           this.isValidGameName(sessionData.gameName);
  }
}
