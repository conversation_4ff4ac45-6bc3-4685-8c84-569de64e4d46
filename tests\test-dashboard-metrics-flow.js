/**
 * @file test-dashboard-metrics-flow.js
 * @description Teste completo do fluxo de métricas no dashboard
 * @version 1.0.0
 */

import axios from 'axios';

const API_BASE = 'http://localhost:3000';
const FRONTEND_BASE = 'http://localhost:5173';

async function testMetricsFlow() {
    console.log('🧪 Iniciando teste completo do fluxo de métricas...\n');

    try {
        // 1. Teste do endpoint público de métricas
        console.log('📊 1. Testando endpoint público de métricas...');
        const metricsResponse = await axios.get(`${API_BASE}/api/public/metrics`);
        
        if (metricsResponse.status === 200 && metricsResponse.data.success) {
            console.log('✅ Endpoint público de métricas funcionando');
            console.log('📋 Dados retornados:', JSON.stringify(metricsResponse.data, null, 2));
        } else {
            console.log('❌ Erro no endpoint público de métricas');
            return;
        }

        // 2. Teste de autenticação para dashboard
        console.log('\n🔐 2. Testando autenticação do dashboard...');
        try {
            const authResponse = await axios.post(`${API_BASE}/api/auth/dashboard/login`, {
                email: '<EMAIL>',
                password: 'admin123'
            });
            
            if (authResponse.data.success) {
                console.log('✅ Autenticação do dashboard funcionando');
                console.log('🎫 Token obtido:', authResponse.data.token ? 'SIM' : 'NÃO');
                
                // 3. Teste do endpoint de métricas do dashboard com token
                console.log('\n📈 3. Testando endpoint de métricas do dashboard...');
                const dashboardMetricsResponse = await axios.get(`${API_BASE}/api/metrics/dashboard`, {
                    headers: {
                        'Authorization': `Bearer ${authResponse.data.token}`
                    }
                });
                
                if (dashboardMetricsResponse.status === 200) {
                    console.log('✅ Endpoint de métricas do dashboard funcionando');
                    console.log('📊 Dados do dashboard:', JSON.stringify(dashboardMetricsResponse.data, null, 2));
                } else {
                    console.log('❌ Erro no endpoint de métricas do dashboard');
                }
                
            } else {
                console.log('❌ Falha na autenticação do dashboard');
                console.log('💬 Resposta:', authResponse.data.message);
            }
        } catch (authError) {
            console.log('⚠️ Erro de autenticação (esperado se credenciais não existirem)');
            console.log('📝 Detalhes:', authError.response?.data?.message || authError.message);
        }

        // 4. Teste do frontend
        console.log('\n🌐 4. Testando disponibilidade do frontend...');
        const frontendResponse = await axios.get(FRONTEND_BASE);
        
        if (frontendResponse.status === 200) {
            console.log('✅ Frontend respondendo corretamente');
            console.log('📄 Página carregada:', frontendResponse.data.includes('Portal Betina') ? 'SIM' : 'NÃO');
        } else {
            console.log('❌ Erro no frontend');
        }

        // 5. Verificação do banco de dados através da API
        console.log('\n💾 5. Testando conectividade com banco de dados...');
        try {
            const healthResponse = await axios.get(`${API_BASE}/api/health`);
            if (healthResponse.data.database?.status === 'healthy') {
                console.log('✅ Banco de dados conectado');
                console.log('📊 Sessões de jogo:', healthResponse.data.database.sessionCount || 'N/A');
            } else {
                console.log('⚠️ Status do banco:', healthResponse.data.database?.status || 'Desconhecido');
            }
        } catch (healthError) {
            console.log('❌ Erro ao verificar health check');
        }

        console.log('\n🎯 RESUMO DO TESTE:');
        console.log('✅ API rodando na porta 3000');
        console.log('✅ Frontend rodando na porta 5173');
        console.log('✅ Endpoint de métricas públicas funcionando');
        console.log('📊 Para acessar o dashboard:');
        console.log('   1. Acesse http://localhost:5173');
        console.log('   2. Role até a seção "Ferramentas e Configurações"');
        console.log('   3. Clique em "Dashboards do Sistema" (ícone 📊)');
        console.log('   4. Se solicitado login, use credenciais de teste');

    } catch (error) {
        console.error('❌ Erro geral no teste:', error.message);
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Verifique se os serviços estão rodando:');
            console.log('   - docker-compose up -d');
        }
    }
}

// Executar teste
testMetricsFlow().catch(console.error);
