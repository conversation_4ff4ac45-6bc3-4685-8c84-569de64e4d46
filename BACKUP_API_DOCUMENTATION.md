# 📋 API de Backup e Exportação - Portal Betina V3

## ✅ **STATUS: TODOS OS ENDPOINTS FUNCIONAIS**

### 🔗 **Base URL**: `http://localhost:3000/api/backup`

---

## 📚 **ENDPOINTS DISPONÍVEIS**

### **1. 📖 Documentação da API**
```http
GET /api/backup/
```
**Descrição**: Lista todos os endpoints disponíveis com documentação completa
**Parâmetros**: Nenhum
**Resposta**: Documentação completa da API

---

### **2. 🧪 Teste de Funcionamento**
```http
GET /api/backup/test
```
**Descrição**: Verifica se a API está funcionando
**Parâmetros**: Nenhum
**Resposta**: Status da API com timestamp

---

### **3. 📊 Status de Backup do Usuário**
```http
GET /api/backup/status/:userId
```
**Descrição**: Busca informações detalhadas sobre backups do usuário
**Parâmetros**: 
- `userId` (string) - ID do usuário
**Resposta**: Status completo incluindo histórico recente

**Exemplo**:
```bash
GET /api/backup/status/user_demo
```

---

### **4. 📜 Histórico de Backups**
```http
GET /api/backup/history/:userId?limit=10&offset=0
```
**Descrição**: Busca histórico paginado de backups do usuário
**Parâmetros**: 
- `userId` (string) - ID do usuário
- `limit` (number, opcional) - Número de itens por página (padrão: 10)
- `offset` (number, opcional) - Offset para paginação (padrão: 0)
**Resposta**: Lista paginada de backups anteriores

**Exemplo**:
```bash
GET /api/backup/history/user_demo?limit=5&offset=0
```

---

### **5. 💾 Geração de Backup**
```http
POST /api/backup/user-data
```
**Descrição**: Gera backup completo dos dados do usuário
**Parâmetros**: 
```json
{
  "userId": "string (obrigatório)",
  "options": {
    "userProfiles": boolean,
    "gameProgress": boolean,
    "gameMetrics": boolean,
    "sessionData": boolean,
    "accessibilitySettings": boolean,
    "preferences": boolean
  }
}
```
**Resposta**: Dados completos do backup gerado

**Exemplo**:
```json
{
  "userId": "user_demo",
  "options": {
    "userProfiles": true,
    "gameProgress": true,
    "gameMetrics": true,
    "sessionData": true
  }
}
```

---

### **6. 📥 Importação de Backup**
```http
POST /api/backup/import
```
**Descrição**: Importa dados de um backup para o usuário
**Parâmetros**: 
```json
{
  "userId": "string (obrigatório)",
  "backupData": "object (obrigatório)",
  "options": {
    "userProfiles": boolean,
    "gameMetrics": boolean,
    "sessionData": boolean
  }
}
```
**Resposta**: Resultado da importação com itens importados/falhados

---

### **7. ✅ Validação de Backup**
```http
POST /api/backup/validate
```
**Descrição**: Valida a estrutura e integridade de um arquivo de backup
**Parâmetros**: 
```json
{
  "backupData": "object (obrigatório)"
}
```
**Resposta**: Resultado detalhado da validação com erros/avisos

---

### **8. 🗑️ Remoção de Backup**
```http
DELETE /api/backup/:userId/:backupId
```
**Descrição**: Remove um backup específico do usuário
**Parâmetros**: 
- `userId` (string) - ID do usuário
- `backupId` (string) - ID do backup a ser removido
**Resposta**: Confirmação da remoção

**Exemplo**:
```bash
DELETE /api/backup/user_demo/backup_123456
```

---

## 🧪 **RESULTADOS DOS TESTES**

### ✅ **Endpoints que PASSARAM nos testes:**
1. **GET** `/api/backup/` - Documentação da API
2. **GET** `/api/backup/test` - Teste de funcionamento
3. **GET** `/api/backup/status/:userId` - Status de backup
4. **GET** `/api/backup/history/:userId` - Histórico de backups
5. **POST** `/api/backup/user-data` - Geração de backup (sem opções)
6. **POST** `/api/backup/user-data` - Geração de backup (com opções)
7. **POST** `/api/backup/import` - Importação válida
8. **POST** `/api/backup/validate` - Validação de backup válido
9. **DELETE** `/api/backup/:userId/:backupId` - Remoção de backup

### ❌ **Validações de erro que funcionaram corretamente:**
1. **POST** `/api/backup/user-data` sem userId (400 - Bad Request)
2. **POST** `/api/backup/import` com versão inválida (400 - Bad Request)
3. **POST** `/api/backup/import` sem dados (400 - Bad Request)
4. **POST** `/api/backup/validate` com dados inválidos (retorna erros)
5. **GET** `/api/backup/status/` sem userId (404 - Not Found)

---

## 🎯 **FUNCIONALIDADES IMPLEMENTADAS**

### **Recursos Principais:**
- ✅ **Backup completo** de dados do usuário
- ✅ **Importação** de backups com validação
- ✅ **Histórico** paginado de backups
- ✅ **Status detalhado** de backups
- ✅ **Validação** de integridade de backups
- ✅ **Remoção** de backups específicos
- ✅ **Documentação** automática da API

### **Validações e Segurança:**
- ✅ **Validação de parâmetros** obrigatórios
- ✅ **Validação de versão** de backup
- ✅ **Tratamento de erros** robusto
- ✅ **Logs detalhados** para debugging
- ✅ **Respostas padronizadas** JSON

### **Dados Suportados:**
- ✅ **Perfis de usuário** (userProfiles)
- ✅ **Progresso nos jogos** (gameProgress)
- ✅ **Métricas de jogos** (gameMetrics)
- ✅ **Dados de sessão** (sessionData)
- ✅ **Configurações de acessibilidade** (accessibilitySettings)
- ✅ **Preferências gerais** (preferences)

---

## 🚀 **COMO USAR**

### **1. Testar se a API está funcionando:**
```bash
curl http://localhost:3000/api/backup/test
```

### **2. Gerar um backup completo:**
```bash
curl -X POST http://localhost:3000/api/backup/user-data \
  -H "Content-Type: application/json" \
  -d '{"userId":"user_demo","options":{"userProfiles":true,"gameProgress":true}}'
```

### **3. Verificar status de backup:**
```bash
curl http://localhost:3000/api/backup/status/user_demo
```

### **4. Ver histórico de backups:**
```bash
curl http://localhost:3000/api/backup/history/user_demo?limit=5
```

---

## 📈 **ESTATÍSTICAS**

- **Total de endpoints**: 8
- **Endpoints funcionais**: 8 (100%)
- **Cobertura de testes**: 13 cenários testados
- **Taxa de sucesso**: 100%
- **Validações de erro**: 5 cenários testados
- **Documentação**: Completa e automática

---

## 🎉 **CONCLUSÃO**

A **API de Backup e Exportação** está **100% funcional** com todos os endpoints testados e validados. A API oferece funcionalidades completas para:

- Geração de backups personalizáveis
- Importação segura com validação
- Histórico e status detalhados
- Validação de integridade
- Documentação automática

**Status**: ✅ **PRONTO PARA PRODUÇÃO**
