/**
 * @file PadroesVisuais.module.css
 * @description Estilos modulares para o Jogo de Padrões Visuais - Padrão Elegante Unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.15);
  --card-border: 1px solid rgba(255, 255, 255, 0.25);
  --card-blur: blur(15px);
  --success-bg: rgba(76, 175, 80, 0.25);
  --success-border: rgba(76, 175, 80, 0.6);
  --error-bg: rgba(244, 67, 54, 0.25);
  --error-border: rgba(244, 67, 54, 0.6);
  --warning-bg: rgba(255, 193, 7, 0.25);
  --warning-border: rgba(255, 193, 7, 0.6);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 16px 48px rgba(0, 0, 0, 0.2);
}

/* Container principal do PadroesVisuais */
.padroesVisuaisGame {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1.5rem 4rem 1.5rem 1.5rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  position: relative;
  min-height: 80px;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s ease;
}

.gameHeader:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.gameTitle {
  font-size: 2rem;
  font-weight: 800;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.activitySubtitle {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-top: 0.5rem;
  background: var(--card-background);
  padding: 0.4rem 1rem;
  border-radius: 15px;
  border: var(--card-border);
  backdrop-filter: var(--card-blur);
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* Botões TTS */
.headerTtsButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  color: white;
  z-index: 10;
  backdrop-filter: var(--card-blur);
  box-shadow: var(--shadow-light);
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
  box-shadow: var(--shadow-medium);
}

.headerTtsButton.ttsActive {
  background: var(--success-bg);
  border: var(--success-border);
  animation: pulse 2s infinite;
}

.headerTtsButton.speaking {
  background: var(--warning-bg) !important;
  border: var(--warning-border) !important;
  animation: speaking 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes speaking {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.8);
  }
}

.headerTtsButton.active {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

/* Estatísticas do jogo */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-light);
}

.statCard:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.8;
}

.statValue {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  line-height: 1;
}

.statLabel {
  font-size: 0.85rem;
  opacity: 0.9;
  color: white;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 600;
}

/* ===== ELEGANTE SISTEMA DE CARDS ===== */

/* Área do jogo */
.gameArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
  min-height: 400px;
  /* DEBUG: Adicionar borda temporária para visualizar */
  border: 2px solid rgba(255, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.05);
}

/* Área da pergunta */
.questionArea {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: var(--shadow-medium);
  transition: all 0.3s ease;
  min-height: 200px;
  /* DEBUG: Adicionar borda temporária para visualizar */
  border: 3px solid rgba(0, 255, 0, 0.5) !important;
}

.questionArea:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.questionHeader {
  margin-bottom: 2rem;
}

.questionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.repeatButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.repeatButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

/* Display dos objetos principais */
.objectsDisplay {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  min-height: 120px;
}

.countingObject {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 20px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  min-width: 100px;
  min-height: 100px;
}

.countingObject::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s ease-in-out infinite;
}

.countingObject:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.2);
}

/* Animações */
@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
  100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); opacity: 1; }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); }
}

/* Cards de opções de resposta */
.answerOptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.elegantCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.elegantCard::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.elegantCard:hover::before {
  animation: shimmer 1.5s ease-in-out;
  opacity: 1;
}

.elegantCard:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.elegantCard:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

/* Estados especiais dos cards */
.elegantCard.selected {
  background: var(--success-bg);
  border-color: var(--success-border);
  box-shadow: 0 8px 32px rgba(76, 175, 80, 0.3);
}

.elegantCard.correct {
  background: var(--success-bg);
  border-color: var(--success-border);
  animation: bounceIn 0.6s ease;
}

.elegantCard.incorrect {
  background: var(--error-bg);
  border-color: var(--error-border);
  animation: pulse 0.5s ease 2;
}

/* Compatibilidade com sistema antigo */
.answerButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.answerButton::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.answerButton:hover::before {
  animation: shimmer 1.5s ease-in-out;
  opacity: 1;
}

.answerButton:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.answerButton:active {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.1s ease;
}

.optionNumber {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

/* Feedback visual */
.feedbackContainer {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 12px;
  text-align: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.feedbackContainer.correct {
  background: var(--success-bg);
  border: 1px solid var(--success-border);
  color: white;
  animation: bounceIn 0.6s ease;
}

.feedbackContainer.incorrect {
  background: var(--error-bg);
  border: 1px solid var(--error-border);
  color: white;
  animation: pulse 0.5s ease 2;
}

/* Controles do jogo */
.gameControls {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2.5rem;
  flex-wrap: wrap;
  padding: 1rem;
}

.controlButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 15px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-weight: 700;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  box-shadow: var(--shadow-light);
  min-width: 120px;
  justify-content: center;
  letter-spacing: 0.3px;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px);
  box-shadow: var(--shadow-heavy);
}

.controlButton:active {
  transform: translateY(-1px);
  transition: all 0.1s ease;
}

/* TTS Indicators */
.ttsIndicator {
  font-size: 0.8rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

/* Responsividade Mobile-First */
@media (max-width: 768px) {
  .padroesVisuaisGame {
    padding: 0.5rem;
  }

  .gameHeader {
    padding: 0.75rem 2.5rem 0.75rem 0.75rem;
    min-height: 60px;
  }

  .gameTitle {
    font-size: 1.4rem;
  }

  .activitySubtitle {
    font-size: 0.65rem;
    padding: 0.2rem 0.5rem;
  }

  .questionArea {
    padding: 1.5rem;
    border-radius: 16px;
  }

  .questionTitle {
    font-size: 1.2rem;
  }

  .objectsDisplay {
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .countingObject {
    padding: 1rem;
    font-size: 2rem;
    min-width: 80px;
    min-height: 80px;
  }

  .answerOptions {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
  }

  .elegantCard, .answerButton {
    padding: 1rem;
    font-size: 1rem;
    min-height: 60px;
  }

  .optionNumber {
    font-size: 1.5rem;
  }

  .gameControls {
    gap: 0.5rem;
  }

  .controlButton {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .gameStats {
    grid-template-columns: repeat(2, 1fr);
  }

  .answerOptions {
    grid-template-columns: repeat(2, 1fr);
  }

  .gameControls {
    flex-direction: column;
    align-items: center;
  }

  .controlButton {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
}

/* Estados de acessibilidade */
.padroesVisuaisGame.high-contrast {
  --card-background: rgba(0, 0, 0, 0.8);
  --card-border: 2px solid rgba(255, 255, 255, 0.8);
}

.padroesVisuaisGame.reduced-motion * {
  animation: none !important;
  transition: none !important;
}

/* Tamanhos de fonte personalizáveis */
.padroesVisuaisGame[data-font-size="small"] {
  font-size: 0.875rem;
}

.padroesVisuaisGame[data-font-size="large"] {
  font-size: 1.25rem;
}

/* Temas de cor */
.padroesVisuaisGame[data-theme="dark"] {
  --gradient-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.padroesVisuaisGame[data-theme="light"] {
  --gradient-bg: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: #2d3436;
}

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do PadroesVisuais */
.padroesVisuaisGame {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Container principal - genérico para outros jogos */
.gameContainer {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activitySubtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Estatísticas */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.7;
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

.statLabel {
  font-size: 0.8rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.8);
}

/* REMOVIDO - Duplicação da classe questionArea */

.questionTitle {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: white;
}

.objectsDisplay {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin: 2rem 0;
  min-height: 150px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.countingObject {
  font-size: 3rem;
  transition: all 0.3s ease;
  animation: objectAppear 0.5s ease-out;
  cursor: default;
  user-select: none;
}

.countingObject:hover {
  transform: scale(1.1);
}

@keyframes objectAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Opções de resposta */
.answerOptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.answerButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.answerButton:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.25);
}

.answerButton.correct {
  border-color: var(--success-border);
  background: var(--success-bg);
  animation: correctPulse 0.6s ease-in-out;
}

.answerButton.incorrect {
  border-color: var(--error-border);
  background: var(--error-bg);
  animation: incorrectShake 0.6s ease-in-out;
}

.answerButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes correctPulse {
  0%, 100% { transform: translateY(-5px) scale(1.05); }
  50% { transform: translateY(-5px) scale(1.2); }
}

@keyframes incorrectShake {
  0%, 100% { transform: translateY(-5px) translateX(0); }
  25% { transform: translateY(-5px) translateX(-10px); }
  75% { transform: translateY(-5px) translateX(10px); }
}

/* Controles do jogo */
.gameControls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.nextButton {
  background: var(--success-bg);
  border-color: var(--success-border);
}

.nextButton:hover {
  background: rgba(76, 175, 80, 0.4);
}

/* Feedback */
.feedbackMessage {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: messageSlide 3s ease-in-out;
}

.feedbackMessage.success {
  background: var(--success-bg);
  color: white;
}

.feedbackMessage.error {
  background: var(--error-bg);
  color: white;
}

@keyframes messageSlide {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Botões TTS */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

.ttsActive {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

.ttsInactive {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

.repeatButton {
  background: rgba(74, 144, 226, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  font-weight: 600;
  margin-left: 1rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.repeatButton:hover {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.4);
}

.repeatButton:active {
  transform: scale(0.95);
}

.ttsIndicator {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(74, 144, 226, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
  border-radius: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  z-index: 5;
  pointer-events: none;
  transition: all 0.2s ease;
}

.answerButton:hover .ttsIndicator {
  background: rgba(74, 144, 226, 1);
  transform: scale(1.1);
}

/* Menu de atividades */
.activityMenu {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  justify-content: center;
  padding: 0.5rem;
}

.activityButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 15px;
  padding: 0.8rem 1.2rem;
  color: white;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.6rem;
  backdrop-filter: var(--card-blur);
  box-shadow: var(--shadow-light);
  min-width: 140px;
  justify-content: center;
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.activityButton.active {
  background: var(--success-bg) !important;
  border: 2px solid var(--success-border) !important;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
  transform: translateY(-2px);
}

.activityIcon {
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.activityName {
  font-weight: 600;
  letter-spacing: 0.3px;
}

/* Classes específicas do ColorMatch */
.questionHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  position: relative;
}

.optionNumber {
  font-size: 1.2rem;
  font-weight: bold;
}

.colorDisplay {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
  border: 3px solid rgba(255,255,255,0.3);
  box-shadow: 0 4px 20px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.colorDisplay:hover {
  transform: scale(1.05);
}

/* Atividade de Som */
.soundActivity {
  text-align: center;
}

.soundIndicator {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: soundPulse 2s ease-in-out infinite;
}

@keyframes soundPulse {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

.soundButton {
  background: rgba(74, 144, 226, 0.3);
  border: 2px solid rgba(74, 144, 226, 0.5);
  border-radius: 12px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  margin: 1rem;
}

.soundButton:hover {
  background: rgba(74, 144, 226, 0.5);
  transform: scale(1.05);
}

/* Atividade de Estimativa */
.estimationDisplay {
  position: relative;
}

.estimationObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  opacity: 0.9;
}

.estimationObject {
  font-size: 1.5rem;
  transform: rotate(var(--rotation));
  transition: opacity 0.3s ease;
}

.estimationTip {
  text-align: center;
  margin-top: 1rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  background: rgba(255, 193, 7, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #FFC107;
}

/* Atividade de Sequência */
.sequenceDisplay {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

.sequenceNumber {
  background: var(--card-background);
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  transition: all 0.3s ease;
}

.sequenceNumber:hover {
  transform: scale(1.05);
}

.sequenceArrow {
  color: rgba(255, 255, 255, 0.7);
  font-size: 1.5rem;
}

.sequenceMissing {
  background: rgba(255, 255, 80, 0.3) !important;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  animation: missingPulse 2s ease-in-out infinite;
}

@keyframes missingPulse {
  0%, 100% { box-shadow: 0 0 0 rgba(255, 255, 80, 0.3); }
  50% { box-shadow: 0 0 20px rgba(255, 255, 80, 0.6); }
}

/* Atividade de Comparação */
.comparisonDisplay {
  display: flex;
  gap: 3rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.comparisonGroup {
  text-align: center;
  background: var(--card-background);
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.comparisonGroup:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
}

.comparisonObjects {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
  max-width: 150px;
  margin-bottom: 1rem;
}

.comparisonNumber {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  background: var(--card-background);
  padding: 0.5rem 1rem;
  border-radius: 8px;
}

/* Atividade de Padrões */
.patternDisplay {
  text-align: center;
}

.patternDescription {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 1rem;
  background: rgba(156, 39, 176, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border-left: 4px solid #9C27B0;
}

.patternSequence {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  flex-wrap: wrap;
}

.patternNumber {
  background: linear-gradient(135deg, rgba(156, 39, 176, 0.3), rgba(156, 39, 176, 0.1));
  padding: 1rem;
  border-radius: 12px;
  min-width: 60px;
  text-align: center;
  color: white;
  font-weight: bold;
  border: 1px solid rgba(156, 39, 176, 0.4);
  transition: all 0.3s ease;
}

.patternNumber:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
  .activityMenu {
    gap: 0.25rem;
  }
  
  .activityButton {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
  
  .comparisonDisplay {
    gap: 1.5rem;
  }
  
  .sequenceDisplay,
  .patternSequence {
    gap: 0.5rem;
    font-size: 1.5rem;
  }
  
  .sequenceNumber,
  .patternNumber {
    padding: 0.75rem;
    min-width: 50px;
  }
  
  .gameStats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .activityMenu {
    flex-direction: column;
    align-items: center;
  }
  
  .activityButton {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
  
  .comparisonDisplay {
    flex-direction: column;
    gap: 1rem;
  }
  
  .sequenceDisplay,
  .patternSequence {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .sequenceArrow {
    transform: rotate(90deg);
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* =====================================================
 * 🎯 LAYOUT BASEADO NO COLORMATCH - ESTRUTURA PADRÃO PORTAL BETINA V3
 * ===================================================== */

/* Container principal do jogo seguindo padrão ColorMatch */
.gameContainer {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

/* REMOVIDO - Duplicação da classe questionArea */

.questionHeader {
  margin: 0;
}

.activityTitle {
  font-size: 1.6rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.activityDescription {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0;
  line-height: 1.4;
}

/* Objects Display - Conteúdo principal da atividade */
.objectsDisplay {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.displayTitle {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: white;
  text-align: center;
}

.objectGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  justify-content: center;
  max-width: 700px;
  margin: 1.5rem auto;
  padding: 1rem;
}

.objectItem {
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1rem;
  font-size: 2.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 80px;
  position: relative;
  backdrop-filter: var(--card-blur);
  box-shadow: var(--shadow-light);
}

.objectItem:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.08);
  box-shadow: var(--shadow-medium);
}

.objectItem.active {
  background: rgba(255, 215, 0, 0.25);
  border-color: #ffd700;
  transform: scale(1.12);
  box-shadow: 0 0 25px rgba(255, 215, 0, 0.6);
  animation: activeGlow 1.5s ease-in-out infinite alternate;
}

@keyframes activeGlow {
  0% { box-shadow: 0 0 25px rgba(255, 215, 0, 0.6); }
  100% { box-shadow: 0 0 35px rgba(255, 215, 0, 0.8); }
}

.objectItem.missing {
  background: rgba(255, 255, 255, 0.08);
  border: 2px dashed rgba(255, 255, 255, 0.4);
  opacity: 0.8;
  font-size: 1.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.objectItem.anomaly {
  animation: anomalyPulse 1s infinite alternate;
}

@keyframes anomalyPulse {
  0% { box-shadow: 0 0 5px rgba(255, 69, 58, 0.5); }
  100% { box-shadow: 0 0 15px rgba(255, 69, 58, 0.8); }
}

.objectSlot {
  background: rgba(255, 255, 255, 0.1);
  border: 2px dashed rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem;
  min-height: 60px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.objectSlot.filled {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.objectSlot.empty:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.5);
}

.slotNumber {
  font-size: 0.7rem;
  opacity: 0.6;
  margin-bottom: 0.25rem;
}

.slotContent {
  font-size: 1.6rem;
  line-height: 1;
}

.progressBar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  height: 8px;
  margin: 1rem 0 0.5rem 0;
  overflow: hidden;
  position: relative;
}

.progressFill {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  height: 100%;
  transition: width 0.5s ease;
  border-radius: 10px;
}

.progressText {
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.9;
  margin-top: 0.5rem;
  display: block;
}

.timer {
  text-align: center;
  font-size: 1rem;
  color: #FFD700;
  margin-top: 1rem;
  padding: 0.5rem;
  background: rgba(255, 215, 0, 0.1);
  border-radius: 8px;
}

/* Answer Options - Opções de resposta */
.answerOptions {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
}

.answerGrid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.optionsTitle {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: white;
  text-align: center;
}

.optionsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.answerOption {
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  padding: 1.2rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 90px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.6rem;
  position: relative;
  overflow: hidden;
  backdrop-filter: var(--card-blur);
  box-shadow: var(--shadow-light);
  font-weight: 600;
}

.answerOption:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.answerOption.selected {
  background: rgba(0, 122, 255, 0.25);
  border-color: #007AFF;
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.5);
  transform: translateY(-2px);
}

.answerOption.dragging {
  opacity: 0.8;
  transform: scale(0.98) rotate(2deg);
  box-shadow: var(--shadow-heavy);
}

.answerOption.primary {
  background: var(--success-bg);
  border-color: var(--success-border);
}

.answerOption.primary:hover {
  background: rgba(52, 199, 89, 0.35);
  box-shadow: 0 0 20px rgba(52, 199, 89, 0.4);
}

.optionContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.optionEmoji {
  font-size: 1.8rem;
  line-height: 1;
}

.optionLabel {
  font-size: 0.9rem;
  text-align: center;
  opacity: 0.9;
  line-height: 1.2;
}

.gameHint {
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 1rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  line-height: 1.4;
}

/* Responsividade */
@media (max-width: 768px) {
  .gameContainer {
    gap: 1rem;
  }
  
  .questionArea,
  .objectsDisplay,
  .answerOptions {
    padding: 1rem;
  }
  
  .objectGrid {
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 0.5rem;
  }
  
  .objectItem,
  .objectSlot {
    min-height: 50px;
    padding: 0.5rem;
    font-size: 1.4rem;
  }
  
  .optionsContainer {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }
  
  .answerOption {
    min-height: 70px;
    padding: 0.75rem;
  }
  
  .activityTitle {
    font-size: 1.4rem;
  }
  
  .displayTitle {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .objectGrid {
    grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
  }
  
  .objectItem,
  .objectSlot {
    min-height: 45px;
    font-size: 1.2rem;
  }
  
  .optionsContainer {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Suporte a movimento reduzido */
.reduced-motion {
  .answerButton, .controlButton, .countingObject, .feedbackMessage, .soundIndicator, .sequenceMissing {
    animation: none !important;
    transition: none !important;
  }
}


