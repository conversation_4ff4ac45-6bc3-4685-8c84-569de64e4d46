/**
 * 👁️ VISUAL PROCESSING COLLECTOR
 * Coletor especializado em análise de processamento visual para ColorMatch
 * Portal Betina V3
 */

export class VisualProcessingCollector {
  constructor() {
    this.processingStages = {
      detection: 'detecção visual inicial',
      recognition: 'reconhecimento de padrões',
      categorization: 'categorização cognitiva',
      decision: 'tomada de decisão'
    };
    
    this.speedThresholds = {
      veryFast: 500,    // < 500ms
      fast: 1000,       // 500-1000ms
      normal: 2000,     // 1-2s
      slow: 4000,       // 2-4s
      verySlow: 8000    // > 4s
    };
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de processamento visual
   */
  collect(data) {
    return this.analyze(data);
  }
  
  async analyze(data) {
    if (!data || !data.visualData) {
      console.warn('VisualProcessingCollector: Dados inválidos recebidos', data);
      return {
        processingSpeed: 0.6,
        visualScanning: 0.6,
        patternRecognition: 0.6,
        visualSearch: 0.6,
        figureGroundSeparation: 0.6,
        visualSpan: 0.6,
        processingEfficiency: 0.6,
        visualIntegration: 0.6
      };
    }

    return {
      processingSpeed: this.assessProcessingSpeed(data),
      visualScanning: this.assessVisualScanning(data),
      patternRecognition: this.assessPatternRecognition(data),
      visualSearch: this.assessVisualSearch(data),
      figureGroundSeparation: this.assessFigureGroundSeparation(data),
      visualSpan: this.calculateVisualSpan(data),
      processingEfficiency: this.calculateProcessingEfficiency(data),
      visualIntegration: this.assessVisualIntegration(data),
      scanningPatterns: this.analyzeScanningPatterns(data),
      processingBottlenecks: this.identifyProcessingBottlenecks(data)
    };
  }

  assessProcessingSpeed(data) {
    const interactions = data.visualData.interactions || [];
    
    if (interactions.length === 0) return 0.6;
    
    const responseTimes = interactions
      .filter(i => i.responseTime && i.responseTime > 0)
      .map(i => i.responseTime);
    
    if (responseTimes.length === 0) return 0.6;
    
    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    
    // Calcular pontuação baseada na velocidade
    let speedScore = 1.0;
    
    if (avgResponseTime <= this.speedThresholds.veryFast) {
      speedScore = 1.0;
    } else if (avgResponseTime <= this.speedThresholds.fast) {
      speedScore = 0.9;
    } else if (avgResponseTime <= this.speedThresholds.normal) {
      speedScore = 0.7;
    } else if (avgResponseTime <= this.speedThresholds.slow) {
      speedScore = 0.5;
    } else {
      speedScore = 0.3;
    }
    
    // Ajustar pela consistência da velocidade
    const consistencyBonus = this.calculateSpeedConsistency(responseTimes) * 0.1;
    
    return Math.max(0, Math.min(1, speedScore + consistencyBonus));
  }

  assessVisualScanning(data) {
    const interactions = data.visualData.interactions || [];
    const scanningData = data.visualData.scanningPatterns || [];
    
    if (scanningData.length === 0) {
      // Estimar baseado em padrões de resposta
      return this.estimateScanningFromResponses(interactions);
    }
    
    let scanningScore = 0;
    
    // Avaliar eficiência do padrão de varredura
    const scanningEfficiency = this.calculateScanningEfficiency(scanningData);
    scanningScore += scanningEfficiency * 0.4;
    
    // Avaliar sistematicidade da varredura
    const scanningSystematicity = this.calculateScanningSystematicity(scanningData);
    scanningScore += scanningSystematicity * 0.3;
    
    // Avaliar adaptabilidade do padrão
    const scanningAdaptability = this.calculateScanningAdaptability(scanningData);
    scanningScore += scanningAdaptability * 0.3;
    
    return Math.max(0, Math.min(1, scanningScore));
  }

  assessPatternRecognition(data) {
    const interactions = data.visualData.interactions || [];
    const colorPatterns = this.extractColorPatterns(interactions);
    
    if (colorPatterns.length === 0) return 0.6;
    
    let recognitionScore = 0;
    let totalPatterns = 0;
    
    colorPatterns.forEach(pattern => {
      const patternAccuracy = pattern.correctResponses / pattern.totalResponses;
      const patternComplexity = this.assessPatternComplexity(pattern);
      
      // Ponderação por complexidade
      const weightedAccuracy = patternAccuracy * (1 + patternComplexity * 0.5);
      recognitionScore += weightedAccuracy;
      totalPatterns++;
    });
    
    const baseRecognition = totalPatterns > 0 ? recognitionScore / totalPatterns : 0.6;
    
    // Bonus por reconhecimento rápido de padrões
    const speedBonus = this.calculatePatternSpeedBonus(interactions);
    
    return Math.max(0, Math.min(1, baseRecognition + speedBonus));
  }

  assessVisualSearch(data) {
    const interactions = data.visualData.interactions || [];
    
    if (interactions.length === 0) return 0.6;
    
    // Analisar estratégia de busca visual
    const searchStrategy = this.identifySearchStrategy(interactions);
    const searchEfficiency = this.calculateSearchEfficiency(interactions);
    const targetDetectionAccuracy = this.calculateTargetDetectionAccuracy(interactions);
    
    // Combinar métricas
    const searchScore = (
      searchStrategy.efficiency * 0.3 +
      searchEfficiency * 0.4 +
      targetDetectionAccuracy * 0.3
    );
    
    return Math.max(0, Math.min(1, searchScore));
  }

  assessFigureGroundSeparation(data) {
    const interactions = data.visualData.interactions || [];
    const complexity = data.visualData.backgroundComplexity || 'low';
    
    if (interactions.length === 0) return 0.6;
    
    // Avaliar capacidade de separar figura do fundo
    const separationAccuracy = this.calculateSeparationAccuracy(interactions, complexity);
    const separationSpeed = this.calculateSeparationSpeed(interactions, complexity);
    
    // Ajustar baseado na complexidade do fundo
    const complexityMultiplier = this.getComplexityMultiplier(complexity);
    
    const separationScore = (separationAccuracy * 0.6 + separationSpeed * 0.4) * complexityMultiplier;
    
    return Math.max(0, Math.min(1, separationScore));
  }

  calculateVisualSpan(data) {
    const interactions = data.visualData.interactions || [];
    
    // Estimar span visual baseado na capacidade de processar múltiplos elementos
    const simultaneousElements = this.calculateSimultaneousProcessing(interactions);
    const maxSpan = Math.max(...simultaneousElements, 1);
    
    // Normalizar span visual (1-7 elementos típicos)
    const normalizedSpan = Math.min(maxSpan / 7, 1);
    
    // Ajustar pela precisão com múltiplos elementos
    const multiElementAccuracy = this.calculateMultiElementAccuracy(interactions);
    
    return normalizedSpan * multiElementAccuracy;
  }

  calculateProcessingEfficiency(data) {
    const interactions = data.visualData.interactions || [];
    
    if (interactions.length === 0) return 0.6;
    
    // Calcular eficiência como razão precisão/tempo
    const totalAccuracy = interactions.filter(i => i.correct).length / interactions.length;
    const avgResponseTime = this.calculateAverageResponseTime(interactions);
    
    // Normalizar tempo (2000ms como referência)
    const normalizedTime = Math.min(avgResponseTime / 2000, 2);
    
    const efficiency = totalAccuracy / normalizedTime;
    
    return Math.max(0, Math.min(1, efficiency));
  }

  assessVisualIntegration(data) {
    const interactions = data.visualData.interactions || [];
    
    if (interactions.length === 0) return 0.6;
    
    // Avaliar capacidade de integrar informações visuais de diferentes fontes
    const integrationTasks = this.identifyIntegrationTasks(interactions);
    
    if (integrationTasks.length === 0) return 0.6;
    
    let integrationScore = 0;
    
    integrationTasks.forEach(task => {
      const taskAccuracy = task.correctResponses / task.totalResponses;
      const integrationComplexity = this.assessIntegrationComplexity(task);
      
      // Ponderação por complexidade da integração
      integrationScore += taskAccuracy * (1 + integrationComplexity * 0.3);
    });
    
    return Math.max(0, Math.min(1, integrationScore / integrationTasks.length));
  }

  analyzeScanningPatterns(data) {
    const interactions = data.visualData.interactions || [];
    
    const patterns = {
      systematic: this.identifySystematicScanning(interactions),
      random: this.identifyRandomScanning(interactions),
      guided: this.identifyGuidedScanning(interactions),
      adaptive: this.identifyAdaptiveScanning(interactions)
    };
    
    // Identificar padrão dominante
    const dominantPattern = Object.entries(patterns)
      .sort((a, b) => b[1] - a[1])[0];
    
    return {
      patterns: patterns,
      dominantPattern: dominantPattern[0],
      efficiency: dominantPattern[1]
    };
  }

  identifyProcessingBottlenecks(data) {
    const interactions = data.visualData.interactions || [];
    const bottlenecks = [];
    
    // Identificar gargalos no processamento
    const slowResponses = interactions.filter(i => 
      i.responseTime && i.responseTime > this.speedThresholds.slow
    );
    
    if (slowResponses.length > interactions.length * 0.3) {
      bottlenecks.push({
        type: 'speed_bottleneck',
        severity: 'high',
        description: 'Velocidade de processamento significativamente reduzida'
      });
    }
    
    // Identificar padrões de erro que indicam bottlenecks
    const errorPatterns = this.analyzeErrorPatterns(interactions);
    if (errorPatterns.systematicErrors > 0.3) {
      bottlenecks.push({
        type: 'recognition_bottleneck',
        severity: 'medium',
        description: 'Dificuldades consistentes no reconhecimento visual'
      });
    }
    
    // Identificar problemas de integração
    const integrationErrors = this.identifyIntegrationErrors(interactions);
    if (integrationErrors > 0.25) {
      bottlenecks.push({
        type: 'integration_bottleneck',
        severity: 'medium',
        description: 'Dificuldades na integração de informações visuais'
      });
    }
    
    return bottlenecks;
  }

  // Métodos auxiliares
  calculateSpeedConsistency(responseTimes) {
    if (responseTimes.length < 3) return 0;
    
    const mean = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / responseTimes.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Consistência maior = menor desvio padrão relativo
    const coefficientOfVariation = standardDeviation / mean;
    return Math.max(0, 1 - coefficientOfVariation);
  }

  estimateScanningFromResponses(interactions) {
    // Estimar qualidade da varredura visual baseado em padrões de resposta
    if (interactions.length === 0) return 0.6;
    
    const responseSequence = interactions.map((i, index) => ({
      index,
      position: i.elementPosition || index,
      correct: i.correct,
      responseTime: i.responseTime
    }));
    
    // Analisar se há padrão sistemático nas posições acessadas
    const positionVariance = this.calculatePositionVariance(responseSequence);
    const systematicity = 1 - Math.min(positionVariance / 10, 1);
    
    return Math.max(0.3, Math.min(1, systematicity));
  }

  calculateScanningEfficiency(scanningData) {
    // Eficiência = cobertura completa com mínimo de movimentos
    const totalMovements = scanningData.length;
    const uniquePositions = new Set(scanningData.map(s => s.position)).size;
    const redundantMovements = totalMovements - uniquePositions;
    
    const efficiency = 1 - (redundantMovements / totalMovements);
    return Math.max(0, efficiency);
  }

  calculateScanningSystematicity(scanningData) {
    // Mede quão sistemático é o padrão de varredura
    const positions = scanningData.map(s => s.position);
    
    if (positions.length < 3) return 0.5;
    
    // Calcular se há padrão sequencial
    let sequentialMoves = 0;
    for (let i = 1; i < positions.length; i++) {
      const distance = Math.abs(positions[i] - positions[i-1]);
      if (distance <= 2) sequentialMoves++; // Movimentos adjacentes
    }
    
    return sequentialMoves / (positions.length - 1);
  }

  calculateScanningAdaptability(scanningData) {
    // Mede capacidade de adaptar padrão de varredura conforme necessário
    // Implementação simplificada
    return 0.7;
  }

  extractColorPatterns(interactions) {
    const patterns = {};
    
    interactions.forEach(interaction => {
      const targetColor = interaction.targetColor;
      if (!patterns[targetColor]) {
        patterns[targetColor] = {
          color: targetColor,
          totalResponses: 0,
          correctResponses: 0,
          avgResponseTime: 0,
          responseTimes: []
        };
      }
      
      patterns[targetColor].totalResponses++;
      if (interaction.correct) {
        patterns[targetColor].correctResponses++;
      }
      if (interaction.responseTime) {
        patterns[targetColor].responseTimes.push(interaction.responseTime);
      }
    });
    
    // Calcular médias
    Object.values(patterns).forEach(pattern => {
      if (pattern.responseTimes.length > 0) {
        pattern.avgResponseTime = pattern.responseTimes.reduce((sum, time) => sum + time, 0) / pattern.responseTimes.length;
      }
    });
    
    return Object.values(patterns);
  }

  assessPatternComplexity(pattern) {
    // Complexidade baseada na cor e contexto
    const complexityMap = {
      'vermelho': 0.2, 'azul': 0.3, 'amarelo': 0.1, 'verde': 0.4,
      'laranja': 0.6, 'roxo': 0.8, 'rosa': 0.7, 'marrom': 0.9
    };
    
    return complexityMap[pattern.color] || 0.5;
  }

  calculatePatternSpeedBonus(interactions) {
    const patternSpeeds = this.extractColorPatterns(interactions)
      .map(pattern => pattern.avgResponseTime)
      .filter(time => time > 0);
    
    if (patternSpeeds.length === 0) return 0;
    
    const avgPatternSpeed = patternSpeeds.reduce((sum, speed) => sum + speed, 0) / patternSpeeds.length;
    
    // Bonus se reconhecimento de padrão é rápido
    return avgPatternSpeed < 1500 ? 0.1 : avgPatternSpeed < 2500 ? 0.05 : 0;
  }

  identifySearchStrategy(interactions) {
    // Analisar estratégia de busca visual predominante
    const strategies = {
      systematic: 0,
      random: 0,
      targeted: 0
    };
    
    // Implementação simplificada baseada em padrões de resposta
    const correctOnFirst = interactions.filter((i, idx) => i.correct && idx < 3).length;
    const totalFirst = Math.min(3, interactions.length);
    
    if (totalFirst > 0) {
      const earlyAccuracy = correctOnFirst / totalFirst;
      if (earlyAccuracy > 0.8) {
        strategies.targeted = 0.9;
      } else if (earlyAccuracy > 0.5) {
        strategies.systematic = 0.7;
      } else {
        strategies.random = 0.6;
      }
    }
    
    const dominantStrategy = Object.entries(strategies)
      .sort((a, b) => b[1] - a[1])[0];
    
    return {
      strategy: dominantStrategy[0],
      efficiency: dominantStrategy[1]
    };
  }

  calculateSearchEfficiency(interactions) {
    if (interactions.length === 0) return 0.6;
    
    // Eficiência = encontrar alvo rapidamente
    const successfulSearches = interactions.filter(i => i.correct);
    const avgSearchTime = successfulSearches.length > 0 ? 
      successfulSearches.reduce((sum, i) => sum + (i.responseTime || 2000), 0) / successfulSearches.length : 2000;
    
    // Normalizar (1000ms = eficiência máxima)
    const efficiency = Math.max(0, 1 - (avgSearchTime - 1000) / 3000);
    return Math.min(1, efficiency);
  }

  calculateTargetDetectionAccuracy(interactions) {
    if (interactions.length === 0) return 0.6;
    
    const correct = interactions.filter(i => i.correct).length;
    return correct / interactions.length;
  }

  calculateSeparationAccuracy(interactions, complexity) {
    // Capacidade de separar figura do fundo
    const baseAccuracy = interactions.filter(i => i.correct).length / interactions.length;
    
    // Ajustar pela complexidade (fundo mais complexo = maior dificuldade)
    const complexityPenalty = complexity === 'high' ? 0.2 : complexity === 'medium' ? 0.1 : 0;
    
    return Math.max(0, baseAccuracy - complexityPenalty);
  }

  calculateSeparationSpeed(interactions, complexity) {
    const avgTime = this.calculateAverageResponseTime(interactions);
    
    // Velocidade ideal varia com complexidade
    const idealTime = complexity === 'high' ? 3000 : complexity === 'medium' ? 2000 : 1500;
    
    return Math.max(0, 1 - Math.abs(avgTime - idealTime) / idealTime);
  }

  getComplexityMultiplier(complexity) {
    const multipliers = {
      'low': 1.0,
      'medium': 1.1,
      'high': 1.2
    };
    return multipliers[complexity] || 1.0;
  }

  calculateSimultaneousProcessing(interactions) {
    // Estimar quantos elementos podem ser processados simultaneamente
    // Implementação simplificada
    const groupedInteractions = this.groupInteractionsByTime(interactions, 1000);
    return groupedInteractions.map(group => group.length);
  }

  calculateMultiElementAccuracy(interactions) {
    const multiElementGroups = this.groupInteractionsByTime(interactions, 1000)
      .filter(group => group.length > 1);
    
    if (multiElementGroups.length === 0) return 1;
    
    const accuracies = multiElementGroups.map(group => {
      const correct = group.filter(i => i.correct).length;
      return correct / group.length;
    });
    
    return accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
  }

  calculateAverageResponseTime(interactions) {
    const times = interactions
      .filter(i => i.responseTime && i.responseTime > 0)
      .map(i => i.responseTime);
    
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 2000;
  }

  identifyIntegrationTasks(interactions) {
    // Identificar tarefas que requerem integração de múltiplas informações visuais
    // Implementação simplificada
    return [{
      type: 'color_integration',
      totalResponses: interactions.length,
      correctResponses: interactions.filter(i => i.correct).length
    }];
  }

  assessIntegrationComplexity(task) {
    // Complexidade da integração visual
    const complexityMap = {
      'color_integration': 0.5,
      'shape_integration': 0.7,
      'spatial_integration': 0.8
    };
    
    return complexityMap[task.type] || 0.5;
  }

  identifySystematicScanning(interactions) {
    // Identificar padrão de varredura sistemática
    return 0.7; // Implementação simplificada
  }

  identifyRandomScanning(interactions) {
    // Identificar padrão de varredura aleatória
    return 0.3; // Implementação simplificada
  }

  identifyGuidedScanning(interactions) {
    // Identificar padrão de varredura guiada
    return 0.5; // Implementação simplificada
  }

  identifyAdaptiveScanning(interactions) {
    // Identificar padrão de varredura adaptativa
    return 0.6; // Implementação simplificada
  }

  analyzeErrorPatterns(interactions) {
    const totalErrors = interactions.filter(i => !i.correct).length;
    const totalInteractions = interactions.length;
    
    return {
      systematicErrors: totalInteractions > 0 ? totalErrors / totalInteractions : 0,
      errorTypes: this.categorizeErrors(interactions)
    };
  }

  categorizeErrors(interactions) {
    const errorTypes = {
      speed: 0,    // Erros por velocidade excessiva
      accuracy: 0, // Erros por falta de precisão
      attention: 0 // Erros por desatenção
    };
    
    interactions.forEach(interaction => {
      if (!interaction.correct) {
        if (interaction.responseTime && interaction.responseTime < 1000) {
          errorTypes.speed++;
        } else if (interaction.responseTime && interaction.responseTime > 4000) {
          errorTypes.accuracy++;
        } else {
          errorTypes.attention++;
        }
      }
    });
    
    return errorTypes;
  }

  identifyIntegrationErrors(interactions) {
    // Identificar erros específicos de integração visual
    const integrationErrors = interactions.filter(i => 
      !i.correct && i.complexity && i.complexity === 'high'
    ).length;
    
    return interactions.length > 0 ? integrationErrors / interactions.length : 0;
  }

  calculatePositionVariance(responseSequence) {
    const positions = responseSequence.map(r => r.position);
    
    if (positions.length < 2) return 0;
    
    const mean = positions.reduce((sum, pos) => sum + pos, 0) / positions.length;
    const variance = positions.reduce((sum, pos) => sum + Math.pow(pos - mean, 2), 0) / positions.length;
    
    return variance;
  }

  groupInteractionsByTime(interactions, timeWindow) {
    const groups = [];
    let currentGroup = [];
    let lastTimestamp = null;
    
    interactions.forEach(interaction => {
      const timestamp = new Date(interaction.timestamp).getTime();
      
      if (lastTimestamp === null || timestamp - lastTimestamp <= timeWindow) {
        currentGroup.push(interaction);
      } else {
        if (currentGroup.length > 0) {
          groups.push(currentGroup);
        }
        currentGroup = [interaction];
      }
      
      lastTimestamp = timestamp;
    });
    
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }
    
    return groups;
  }

  generateProcessingInsights(results) {
    const insights = [];
    
    if (results.processingSpeed < 0.5) {
      insights.push('Velocidade de processamento visual reduzida');
    }
    
    if (results.visualScanning < 0.6) {
      insights.push('Padrões de varredura visual ineficientes');
    }
    
    if (results.patternRecognition < 0.6) {
      insights.push('Dificuldades no reconhecimento de padrões visuais');
    }
    
    if (results.visualSearch < 0.5) {
      insights.push('Estratégias de busca visual subótimas');
    }
    
    if (results.figureGroundSeparation < 0.6) {
      insights.push('Limitações na separação figura-fundo');
    }
    
    if (results.visualSpan < 0.5) {
      insights.push('Span visual reduzido');
    }
    
    if (results.processingEfficiency < 0.6) {
      insights.push('Eficiência geral do processamento visual comprometida');
    }
    
    if (results.processingBottlenecks && results.processingBottlenecks.length > 0) {
      insights.push('Gargalos específicos no processamento visual identificados');
    }
    
    return insights;
  }
}
