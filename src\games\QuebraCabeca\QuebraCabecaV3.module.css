/* ✅ QUEBRA-CABEÇA V3 - CSS MODERNO COM GLASSMORPHISM */

.quebra-cabeca-v3 {
  /* Container principal com gradiente e glassmorphism */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 2rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  min-height: 600px;
}

.quebra-cabeca-v3::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 25px;
  pointer-events: none;
}

/* ✅ SELETOR DE ATIVIDADES V3 */
.activity-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.activity-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2px solid transparent;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.activity-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.activity-card:hover::before {
  left: 100%;
}

.activity-card.active {
  border-color: #667eea;
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 25px rgba(102, 126, 234, 0.3);
  background: rgba(255, 255, 255, 0.95);
}

.activity-card:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.activity-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  transition: transform 0.3s ease;
}

.activity-card:hover .activity-icon {
  transform: scale(1.1) rotate(5deg);
}

.activity-card.active .activity-icon {
  transform: scale(1.15);
}

.activity-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
  line-height: 1.2;
}

.activity-description {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.3;
  opacity: 0.8;
}

/* ✅ ÁREA PRINCIPAL DO JOGO V3 */
.game-area {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 2rem;
  margin-bottom: 2rem;
  min-height: 450px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.current-activity-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2);
}

.current-activity-title {
  font-size: 1.8rem;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.current-activity-subtitle {
  color: #666;
  font-size: 1rem;
  font-weight: 400;
  opacity: 0.9;
}

/* ✅ ÁREA DA EMOÇÃO ATUAL */
.emotion-display {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.current-emotion {
  font-size: 4rem;
  margin-bottom: 0.5rem;
  animation: emotionPulse 3s ease-in-out infinite;
}

@keyframes emotionPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.emotion-name {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 0.3rem;
}

.emotion-context {
  color: #666;
  font-style: italic;
  font-size: 0.95rem;
}

/* ✅ PAINEL DE INSTRUÇÕES */
.instruction-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 2rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.instruction-text {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.instruction-detail {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 400;
}

/* ✅ GRID DO QUEBRA-CABEÇA V3 */
.puzzle-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  max-width: 320px;
  margin: 0 auto 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 15px;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
}

.puzzle-slot {
  aspect-ratio: 1;
  background: rgba(255, 255, 255, 0.9);
  border: 3px dashed rgba(102, 126, 234, 0.4);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.puzzle-slot::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.puzzle-slot:hover::before {
  width: 100%;
  height: 100%;
}

.puzzle-slot.filled {
  border-style: solid;
  border-color: #4CAF50;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, rgba(76, 175, 80, 0.1) 100%);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.puzzle-slot.pulse {
  animation: slotPulse 2s ease-in-out infinite;
}

@keyframes slotPulse {
  0%, 100% {
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
  }
  50% {
    border-color: rgba(102, 126, 234, 0.8);
    box-shadow: 0 0 0 8px rgba(102, 126, 234, 0);
  }
}

.puzzle-slot:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: scale(1.05);
  border-color: rgba(102, 126, 234, 0.6);
}

/* ✅ PEÇAS DISPONÍVEIS V3 */
.available-pieces {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  backdrop-filter: blur(5px);
}

.puzzle-piece {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  cursor: grab;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.puzzle-piece::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.puzzle-piece:hover::before {
  transform: translateX(100%);
}

.puzzle-piece:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
  border-color: #667eea;
}

.puzzle-piece:active {
  cursor: grabbing;
  transform: scale(0.95) rotate(0deg);
}

.puzzle-piece.bounce {
  animation: pieceBounce 1s ease-in-out;
}

@keyframes pieceBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-10px) scale(1.05);
  }
  60% {
    transform: translateY(-5px) scale(1.02);
  }
}

.puzzle-piece.shake {
  animation: pieceShake 0.5s ease-in-out;
}

@keyframes pieceShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px) rotate(-2deg); }
  75% { transform: translateX(5px) rotate(2deg); }
}

/* ✅ SELETOR DE DIFICULDADE V3 */
.difficulty-selector {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.difficulty-btn {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid transparent;
  font-size: 0.9rem;
}

.difficulty-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.difficulty-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
  border-color: rgba(255, 255, 255, 0.3);
}

/* ✅ PAINEL DE ESTATÍSTICAS V3 */
.stats-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 1.5rem;
  margin-top: 2rem;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.stats-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.stat-value {
  font-size: 1.6rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
}

.stat-label {
  font-size: 0.85rem;
  opacity: 0.9;
  font-weight: 500;
}

/* ✅ ATIVIDADES ESPECÍFICAS V3 */

/* Montagem Guiada */
.guided-sequence {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 15px;
}

.sequence-step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.step-number {
  width: 30px;
  height: 30px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.step-arrow {
  font-size: 1.5rem;
  color: #667eea;
}

/* Classificação de Peças */
.classification-area {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
}

.category-zone {
  text-align: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 2px dashed rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  min-height: 120px;
}

.category-zone:hover {
  border-color: rgba(102, 126, 234, 0.6);
  background: rgba(255, 255, 255, 0.8);
}

.category-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.category-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* Identificação de Padrões */
.pattern-sequence {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.pattern-item {
  width: 60px;
  height: 60px;
  background: #f8f9fa;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  border: 2px solid #e9ecef;
}

.pattern-arrow {
  font-size: 1.5rem;
  color: #667eea;
}

.pattern-options {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.pattern-option {
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pattern-option:hover {
  transform: scale(1.05);
  border-color: #667eea;
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Resolução Colaborativa */
.collaborative-area {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.player-zone {
  text-align: center;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid rgba(102, 126, 234, 0.3);
}

.player-title {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.player-pieces {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.team-puzzle-grid {
  grid-column: 1 / -1;
  margin-top: 1rem;
}

/* ✅ FEEDBACK E ANIMAÇÕES V3 */
.feedback-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: 600;
  z-index: 1000;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: feedbackBounce 0.5s ease;
}

@keyframes feedbackBounce {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

/* ✅ RESPONSIVIDADE V3 */
@media (max-width: 768px) {
  .quebra-cabeca-v3 {
    padding: 1rem;
    margin: 0.5rem;
  }

  .activity-selector {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    padding: 0.5rem;
  }

  .activity-card {
    padding: 0.8rem;
  }

  .activity-icon {
    font-size: 1.5rem;
  }

  .activity-name {
    font-size: 0.8rem;
  }

  .activity-description {
    font-size: 0.7rem;
  }

  .current-activity-title {
    font-size: 1.4rem;
  }

  .current-emotion {
    font-size: 3rem;
  }

  .puzzle-grid {
    max-width: 280px;
    gap: 8px;
  }

  .puzzle-slot {
    font-size: 1.5rem;
  }

  .available-pieces {
    gap: 10px;
  }

  .puzzle-piece {
    width: 60px;
    height: 60px;
    font-size: 1.6rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .difficulty-selector {
    flex-direction: column;
    align-items: center;
  }

  .classification-area {
    grid-template-columns: 1fr;
  }

  .collaborative-area {
    grid-template-columns: 1fr;
  }

  .pattern-sequence {
    gap: 0.5rem;
  }

  .pattern-item,
  .pattern-option {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .quebra-cabeca-v3 {
    padding: 0.8rem;
  }

  .game-area {
    padding: 1rem;
  }

  .activity-selector {
    grid-template-columns: 1fr;
  }

  .puzzle-grid {
    max-width: 240px;
    gap: 6px;
  }

  .puzzle-piece {
    width: 50px;
    height: 50px;
    font-size: 1.4rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .current-activity-title {
    font-size: 1.2rem;
    flex-direction: column;
    gap: 0.2rem;
  }

  .emotion-display {
    padding: 1rem;
  }

  .current-emotion {
    font-size: 2.5rem;
  }

  .emotion-name {
    font-size: 1.2rem;
  }
}

/* ✅ TEMAS ALTERNATIVOS */
.quebra-cabeca-v3.high-contrast {
  background: linear-gradient(135deg, #000000 0%, #333333 100%);
  color: #ffffff;
}

.quebra-cabeca-v3.high-contrast .activity-card {
  background: #ffffff;
  color: #000000;
  border-color: #000000;
}

.quebra-cabeca-v3.high-contrast .puzzle-slot {
  background: #ffffff;
  border-color: #000000;
}

.quebra-cabeca-v3.child-friendly {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.quebra-cabeca-v3.child-friendly .activity-card {
  border-radius: 20px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.quebra-cabeca-v3.child-friendly .puzzle-piece {
  border-radius: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}
