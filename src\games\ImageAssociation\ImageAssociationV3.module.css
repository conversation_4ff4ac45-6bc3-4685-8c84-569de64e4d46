/* ============================================================================ */
/* IMAGE ASSOCIATION V3 - ESTILOS SEGUINDO PADRÃO LETTER RECOGNITION V3 */
/* Sistema Avançado de Análise de Associação Conceitual e Processamento Semântico */
/* ============================================================================ */

/* Container principal do jogo */
.imageAssociationGame {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Conteúdo do jogo */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo - SEGUINDO PADRÃO LETTER RECOGNITION V3 */
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem; /* Padding direito maior para espaço do TTS */
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  min-height: 60px;
}

.title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
}

/* Botão TTS no header - IGUAL AO LETTER RECOGNITION V3 */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

/* Estados do toggle TTS */
.ttsActive {
  background: rgba(76, 175, 80, 0.3) !important;
  border-color: rgba(76, 175, 80, 0.5) !important;
}

.ttsInactive {
  background: rgba(244, 67, 54, 0.3) !important;
  border-color: rgba(244, 67, 54, 0.5) !important;
}

/* Stats - SEGUINDO PADRÃO LETTER RECOGNITION V3 */
.stats {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.statItem {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  min-width: 80px;
}

.statValue {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
}

.statLabel {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
}

/* Activity Selector - SEGUINDO PADRÃO LETTER RECOGNITION V3 */
.activitySelector {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  margin-bottom: 2rem;
}

.activitySelector h3 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.activityButtons {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.activityBtn {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.activityBtn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.activityBtn.active {
  background: rgba(76, 175, 80, 0.4);
  border-color: rgba(76, 175, 80, 0.6);
}

/* Game Area - SEGUINDO PADRÃO LETTER RECOGNITION V3 */
.gameArea {
  padding: 0;
}

.activity {
  display: none;
}

.activity.active {
  display: block;
}

.instruction {
  text-align: center;
  margin-bottom: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
}

.instruction h3 {
  color: white;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.instruction p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
}

/* Options Grid - SEGUINDO PADRÃO LETTER RECOGNITION V3 */
.optionsGrid {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.imageOption {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
  text-align: center;
}

.imageOption:hover {
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.imageOption .image {
  font-size: 2rem;
  font-weight: bold;
  color: white;
  display: block;
}

.imageOption .label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.5rem;
}

/* Responsive - SEGUINDO PADRÃO LETTER RECOGNITION V3 */
@media (max-width: 600px) {
  .stats {
    gap: 0.5rem;
  }
  
  .statItem {
    min-width: 60px;
    padding: 0.75rem;
  }
  
  .optionsGrid {
    flex-direction: column;
    align-items: center;
  }
  
  .imageOption {
    min-width: 120px;
  }
  
  .activityButtons {
    gap: 0.25rem;
  }
  
  .activityBtn {
    padding: 0.4rem 0.8rem;
    font-size: 0.7rem;
  }
}

.feedback-message .points {
  display: inline-block;
  background: #28a745;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9em;
  margin-left: 10px;
  animation: pointsPulse 0.6s ease;
}

@keyframes feedbackSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pointsPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* ========================================================================== */
/* ÁREA PRINCIPAL DO JOGO */
/* ========================================================================== */

.game-area {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border-radius: 25px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-height: 500px;
}

/* ========================================================================== */
/* CONTAINER DE ESTÍMULOS */
/* ========================================================================== */

.stimuli-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 20px;
  padding: 20px;
  justify-items: center;
}

.stimulus-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid transparent;
  text-align: center;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.stimulus-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-color: #667eea;
}

.stimulus-item.selected {
  border-color: #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 167, 69, 0.1));
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 35px rgba(40, 167, 69, 0.3);
}

.stimulus-item.correct {
  border-color: #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.3), rgba(40, 167, 69, 0.1));
  animation: correctPulse 0.8s ease;
}

.stimulus-item.incorrect {
  border-color: #dc3545;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.3), rgba(220, 53, 69, 0.1));
  animation: incorrectShake 0.6s ease;
}

@keyframes correctPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes incorrectShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-8px); }
  75% { transform: translateX(8px); }
}

.stimulus-image {
  font-size: 3em;
  margin-bottom: 10px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.2));
}

.stimulus-label {
  font-size: 0.9em;
  font-weight: 600;
  color: #555;
  opacity: 0.8;
}

/* ========================================================================== */
/* SLOTS DE CATEGORIA */
/* ========================================================================== */

.category-slots {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 30px;
  padding: 20px;
}

.category-slot {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 3px dashed #ccc;
  border-radius: 20px;
  padding: 25px;
  text-align: center;
  min-height: 150px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.category-slot:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-3px);
}

.category-slot.highlight {
  border-color: #28a745;
  background: rgba(40, 167, 69, 0.15);
  border-style: solid;
}

.category-slot .category-title {
  font-size: 1.3em;
  font-weight: 700;
  margin-bottom: 15px;
  color: #333;
}

.category-slot .category-items {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  align-items: center;
  min-height: 80px;
}

.category-slot .dropped-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  padding: 8px 12px;
  font-size: 1.5em;
  border: 2px solid #28a745;
  animation: dropAnimation 0.5s ease;
}

@keyframes dropAnimation {
  from {
    opacity: 0;
    transform: scale(0.5) rotate(180deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* ========================================================================== */
/* ÁREA DE SEQUÊNCIA */
/* ========================================================================== */

.sequence-area {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  margin-top: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.sequence-slot {
  width: 80px;
  height: 80px;
  border: 3px dashed #ccc;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2em;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  position: relative;
}

.sequence-slot.filled {
  border-color: #667eea;
  border-style: solid;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0.1));
  animation: fillSlot 0.5s ease;
}

.sequence-slot.correct-order {
  border-color: #28a745;
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 167, 69, 0.1));
}

.sequence-slot.incorrect-order {
  border-color: #dc3545;
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(220, 53, 69, 0.1));
}

@keyframes fillSlot {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.sequence-arrow {
  font-size: 1.5em;
  color: #667eea;
  margin: 0 5px;
  animation: arrowPulse 2s ease-in-out infinite;
}

@keyframes arrowPulse {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}

/* ========================================================================== */
/* TELA DE CONCLUSÃO */
/* ========================================================================== */

.completion-screen {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 25px;
  padding: 40px;
  text-align: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
  animation: completionSlideIn 0.8s ease;
}

.completion-screen h2 {
  font-size: 2.5em;
  color: #333;
  margin-bottom: 30px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.final-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.final-stat-item {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
}

.final-stat-item .stat-number {
  font-size: 2.2em;
  font-weight: 700;
  display: block;
  margin-bottom: 5px;
}

.final-stat-item .stat-description {
  font-size: 0.9em;
  opacity: 0.9;
}

@keyframes completionSlideIn {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* ========================================================================== */
/* INDICADORES DE PROGRESSO */
/* ========================================================================== */

.progress-indicator {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 50px;
  padding: 10px 20px;
  margin: 20px 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-bar {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  height: 8px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  background: linear-gradient(90deg, #28a745, #20c997);
  height: 100%;
  border-radius: 25px;
  transition: width 0.5s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ========================================================================== */
/* ADAPTAÇÕES RESPONSIVAS */
/* ========================================================================== */

@media (max-width: 768px) {
  .image-association-v3 {
    padding: 15px;
  }

  .game-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .game-title {
    font-size: 1.8em;
  }

  .game-controls {
    justify-content: center;
  }

  .stimuli-container {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 15px;
    padding: 15px;
  }

  .stimulus-item {
    min-height: 100px;
    padding: 15px;
  }

  .stimulus-image {
    font-size: 2.5em;
  }

  .category-slots {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .sequence-area {
    flex-direction: column;
    gap: 10px;
  }

  .completion-screen {
    padding: 25px;
  }

  .completion-screen h2 {
    font-size: 2em;
  }
}

@media (max-width: 480px) {
  .game-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .stimuli-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    padding: 10px;
  }

  .stimulus-item {
    min-height: 80px;
    padding: 10px;
  }

  .stimulus-image {
    font-size: 2em;
  }

  .sequence-slot {
    width: 60px;
    height: 60px;
    font-size: 1.5em;
  }
}

/* ========================================================================== */
/* ANIMAÇÕES ESPECIAIS */
/* ========================================================================== */

.floating-element {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.glow-effect {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.6); }
  to { box-shadow: 0 0 30px rgba(102, 126, 234, 0.8); }
}

/* ========================================================================== */
/* ESTADOS DE ACESSIBILIDADE */
/* ========================================================================== */

.high-contrast {
  filter: contrast(1.5) brightness(1.2);
}

.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
}

.large-text {
  font-size: 1.2em;
}

.large-text .stimulus-image {
  font-size: 4em;
}

.large-text .game-title {
  font-size: 2.8em;
}

/* ========================================================================== */
/* EFEITOS DE HOVER AVANÇADOS */
/* ========================================================================== */

.stimulus-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.stimulus-item:hover::before {
  transform: translateX(100%);
}

.category-slot::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3), transparent);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.category-slot:hover::after {
  width: 100px;
  height: 100px;
}

/* ========================================================================== */
/* LOADING E TRANSIÇÕES */
/* ========================================================================== */

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 25px;
  z-index: 10;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(102, 126, 234, 0.3);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.fade-in {
  animation: fadeIn 0.5s ease;
}

.fade-out {
  animation: fadeOut 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}
