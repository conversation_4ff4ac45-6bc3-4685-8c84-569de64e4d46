/**
 * 📝 COMPARISON SKILLS COLLECTOR V3
 * Coletor especializado em análise de habilidades de comparação numérica
 * Portal Betina V3
 */

export class ComparisonSkillsCollector {
  constructor() {
    this.comparisonThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.70,
      poor: 0.50,
      critical: 0.30
    };
    
    this.comparisonTypes = {
      magnitude: 'Comparação de magnitude (maior/menor)',
      quantity: 'Comparação de quantidades',
      difference: 'Cálculo de diferenças',
      ordering: 'Ordenação de números',
      relative: 'Posicionamento relativo',
      range: 'Comparação por faixas'
    };
    
    this.cognitiveSkills = {
      numberSense: 'Senso numérico',
      spatialReasoning: 'Raciocínio espacial',
      relationalThinking: 'Pensamento relacional',
      magnitudeEstimation: 'Estimativa de magnitude',
      comparativeAnalysis: 'Análise comparativa'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  
  /**
   * Análise principal das habilidades de comparação
   */
  async analyze(data) {
    if (!data || !data.numberComparison) {
      console.warn('ComparisonSkillsCollector: Dados de comparação não encontrados');
      return this.getDefaultAnalysis();
    }

    const comparisonData = data.numberComparison;
    
    // Analisar precisão por tipo de comparação
    const accuracyByType = this.analyzeAccuracyByType(comparisonData);
    
    // Avaliar discriminação de magnitude
    const magnitudeDiscrimination = this.assessMagnitudeDiscrimination(comparisonData);
    
    // Analisar estratégias de comparação
    const comparisonStrategies = this.analyzeComparisonStrategies(comparisonData);
    
    // Avaliar velocidade de processamento
    const processingSpeed = this.assessProcessingSpeed(comparisonData);
    
    // Analisar progresso temporal
    const temporalProgress = this.analyzeTemporalProgress(comparisonData);
    
    // Detectar padrões de erro
    const errorPatterns = this.analyzeErrorPatterns(comparisonData);
    
    // Calcular índice de habilidade comparativa
    const comparativeSkillIndex = this.calculateComparativeSkillIndex(comparisonData);

    const analysis = {
      timestamp: new Date().toISOString(),
      collector: 'ComparisonSkillsCollector',
      version: '3.0.0',
      
      // Métricas principais
      overallAccuracy: this.calculateOverallAccuracy(comparisonData),
      comparativeSkillIndex,
      processingSpeed,
      
      // Análises detalhadas
      accuracyByType,
      magnitudeDiscrimination,
      comparisonStrategies,
      temporalProgress,
      errorPatterns,
      
      // Habilidades cognitivas específicas
      cognitiveSkills: {
        numberSense: this.assessNumberSense(comparisonData),
        spatialReasoning: this.assessSpatialReasoning(comparisonData),
        relationalThinking: this.assessRelationalThinking(comparisonData),
        magnitudeEstimation: this.assessMagnitudeEstimation(comparisonData),
        comparativeAnalysis: this.assessComparativeAnalysis(comparisonData)
      },
      
      // Análise de dificuldade
      difficultyAnalysis: this.analyzeDifficultyProgression(comparisonData),
      
      // Análise de precisão por faixa numérica
      rangeAccuracy: this.analyzeRangeAccuracy(comparisonData),
      
      // Recomendações
      recommendations: this.generateRecommendations(comparisonData, comparativeSkillIndex),
      
      // Metadados
      metadata: {
        totalComparisons: comparisonData.length || 0,
        uniqueTypes: this.countUniqueTypes(comparisonData),
        averageDifference: this.calculateAverageDifference(comparisonData),
        rangeDistribution: this.analyzeRangeDistribution(comparisonData)
      }
    };

    return analysis;
  }

  /**
   * Analisar precisão por tipo de comparação
   */
  analyzeAccuracyByType(data) {
    const accuracyByType = {};
    
    // Agrupar por tipo de comparação
    const groupedData = this.groupByComparisonType(data);
    
    Object.keys(groupedData).forEach(type => {
      const typeData = groupedData[type];
      const correct = typeData.filter(attempt => attempt.isCorrect).length;
      accuracyByType[type] = {
        accuracy: typeData.length > 0 ? correct / typeData.length : 0,
        attempts: typeData.length,
        averageTime: this.calculateAverageTime(typeData),
        averageDifference: this.calculateAverageDifference(typeData),
        difficulty: this.assessTypeDifficulty(typeData)
      };
    });
    
    return accuracyByType;
  }

  /**
   * Avaliar discriminação de magnitude
   */
  assessMagnitudeDiscrimination(data) {
    // Analisar precisão baseada na diferença entre números
    const difficultyLevels = {
      easy: [],     // diferença > 5
      medium: [],   // diferença 3-5
      hard: [],     // diferença 1-2
      veryHard: []  // diferença < 1
    };

    data.forEach(attempt => {
      const diff = Math.abs(attempt.number1 - attempt.number2);
      
      if (diff > 5) difficultyLevels.easy.push(attempt);
      else if (diff >= 3) difficultyLevels.medium.push(attempt);
      else if (diff >= 1) difficultyLevels.hard.push(attempt);
      else difficultyLevels.veryHard.push(attempt);
    });

    const discrimination = {};
    Object.keys(difficultyLevels).forEach(level => {
      const levelData = difficultyLevels[level];
      discrimination[level] = {
        accuracy: this.calculateAccuracy(levelData),
        averageTime: this.calculateAverageTime(levelData),
        attempts: levelData.length
      };
    });

    return {
      byDifficulty: discrimination,
      discriminationThreshold: this.calculateDiscriminationThreshold(data),
      weberFraction: this.calculateWeberFraction(data)
    };
  }

  /**
   * Analisar estratégias de comparação
   */
  analyzeComparisonStrategies(data) {
    const strategies = {
      immediateComparison: false,   // Comparação imediata visual
      countingStrategy: false,      // Estratégia de contagem
      magnitudeEstimation: false,   // Estimativa de magnitude
      digitalComparison: false,     // Comparação dígito por dígito
      patternRecognition: false     // Reconhecimento de padrões
    };

    // Detectar comparação imediata (respostas muito rápidas e corretas)
    const quickCorrect = data.filter(attempt => 
      attempt.isCorrect && attempt.responseTime < 2000
    ).length;
    strategies.immediateComparison = quickCorrect / data.length > 0.6;

    // Detectar estratégia de contagem (tempo proporcional à diferença)
    const timeVsDifference = this.analyzeTimeVsDifference(data);
    strategies.countingStrategy = timeVsDifference.correlation > 0.7;

    // Detectar estimativa de magnitude (precisão consistente em diferentes faixas)
    strategies.magnitudeEstimation = this.detectMagnitudeStrategy(data);

    // Detectar comparação digital (melhor performance com números de dígitos diferentes)
    strategies.digitalComparison = this.detectDigitalStrategy(data);

    return {
      strategies,
      dominantStrategy: this.identifyDominantStrategy(strategies),
      strategyEffectiveness: this.assessStrategyEffectiveness(data, strategies)
    };
  }

  /**
   * Avaliar velocidade de processamento
   */
  assessProcessingSpeed(data) {
    const responseTimes = data.map(attempt => attempt.responseTime).filter(Boolean);
    
    if (responseTimes.length === 0) return { speed: 'unknown', score: 0.5 };
    
    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const medianTime = this.calculateMedian(responseTimes);
    
    let speedCategory = 'average';
    let speedScore = 0.5;
    
    if (averageTime < 2000) {
      speedCategory = 'very_fast';
      speedScore = 0.9;
    } else if (averageTime < 3000) {
      speedCategory = 'fast';
      speedScore = 0.8;
    } else if (averageTime < 5000) {
      speedCategory = 'average';
      speedScore = 0.6;
    } else {
      speedCategory = 'slow';
      speedScore = 0.4;
    }
    
    return {
      averageTime,
      medianTime,
      speedCategory,
      speedScore,
      consistency: this.calculateTimeConsistency(responseTimes),
      speedVsAccuracy: this.analyzeSpeedVsAccuracy(data)
    };
  }

  /**
   * Analisar progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (data.length < 3) return { trend: 'insufficient_data', improvement: 0 };
    
    const segments = this.divideIntoSegments(data, 3);
    const segmentAccuracies = segments.map(segment => this.calculateAccuracy(segment));
    const segmentTimes = segments.map(segment => this.calculateAverageTime(segment));
    
    const accuracyTrend = this.calculateTrend(segmentAccuracies);
    const speedTrend = this.calculateTrend(segmentTimes.map(time => 1/time)); // Inverso para representar melhoria
    
    return {
      accuracyTrend,
      speedTrend,
      improvement: segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0],
      segmentAccuracies,
      segmentTimes,
      learningRate: this.calculateLearningRate(segmentAccuracies),
      stability: this.calculateStability(segmentAccuracies)
    };
  }

  /**
   * Analisar padrões de erro
   */
  analyzeErrorPatterns(data) {
    const errors = data.filter(attempt => !attempt.isCorrect);
    
    const errorTypes = {
      magnitudeConfusion: 0,     // Confusão de magnitude
      digitalError: 0,          // Erro na comparação de dígitos
      inverseComparison: 0,     // Comparação invertida
      proximityError: 0,        // Erro em números próximos
      randomError: 0            // Erro aparentemente aleatório
    };

    errors.forEach(error => {
      if (error.userAnswer && error.correctAnswer) {
        const difference = Math.abs(error.number1 - error.number2);
        
        if (difference <= 2) {
          errorTypes.proximityError++;
        } else if (this.isInverseComparison(error)) {
          errorTypes.inverseComparison++;
        } else if (this.isDigitalError(error)) {
          errorTypes.digitalError++;
        } else if (difference > 10) {
          errorTypes.magnitudeConfusion++;
        } else {
          errorTypes.randomError++;
        }
      }
    });

    return {
      totalErrors: errors.length,
      errorTypes,
      errorRate: data.length > 0 ? errors.length / data.length : 0,
      criticalErrors: this.identifyCriticalErrors(errors),
      errorProgression: this.analyzeErrorProgression(data),
      errorsByDifficulty: this.analyzeErrorsByDifficulty(data)
    };
  }

  /**
   * Calcular índice de habilidade comparativa
   */
  calculateComparativeSkillIndex(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speed = this.assessProcessingSpeed(data).speedScore;
    const discrimination = this.assessMagnitudeDiscrimination(data);
    const discriminationScore = this.calculateDiscriminationScore(discrimination);
    const consistency = this.calculateConsistency(data);
    
    return (accuracy * 0.4) + (speed * 0.2) + (discriminationScore * 0.2) + (consistency * 0.2);
  }

  /**
   * Avaliar habilidades cognitivas específicas
   */
  assessNumberSense(data) {
    // Capacidade geral de trabalhar com números
    const overallAccuracy = this.calculateOverallAccuracy(data);
    const speedScore = this.assessProcessingSpeed(data).speedScore;
    
    return (overallAccuracy * 0.7) + (speedScore * 0.3);
  }

  assessSpatialReasoning(data) {
    // Capacidade de visualizar relações numéricas espacialmente
    const quickComparisons = data.filter(attempt => 
      attempt.isCorrect && attempt.responseTime < 3000
    ).length;
    
    return data.length > 0 ? quickComparisons / data.length : 0.5;
  }

  assessRelationalThinking(data) {
    // Capacidade de entender relações entre números
    const complexComparisons = data.filter(attempt => 
      attempt.comparisonType && 
      ['relative_position', 'range_comparison'].includes(attempt.comparisonType)
    );
    
    return this.calculateAccuracy(complexComparisons);
  }

  assessMagnitudeEstimation(data) {
    // Capacidade de estimar magnitudes numericas
    const discrimination = this.assessMagnitudeDiscrimination(data);
    return this.calculateDiscriminationScore(discrimination);
  }

  assessComparativeAnalysis(data) {
    // Capacidade de fazer análises comparativas complexas
    const multiStepComparisons = data.filter(attempt => 
      attempt.requiresMultipleSteps || 
      (attempt.number1 > 20 && attempt.number2 > 20)
    );
    
    return this.calculateAccuracy(multiStepComparisons);
  }

  /**
   * Analisar precisão por faixa numérica
   */
  analyzeRangeAccuracy(data) {
    const ranges = {
      small: [],    // 1-10
      medium: [],   // 11-50
      large: [],    // 51-100
      veryLarge: [] // >100
    };

    data.forEach(attempt => {
      const maxNum = Math.max(attempt.number1, attempt.number2);
      
      if (maxNum <= 10) ranges.small.push(attempt);
      else if (maxNum <= 50) ranges.medium.push(attempt);
      else if (maxNum <= 100) ranges.large.push(attempt);
      else ranges.veryLarge.push(attempt);
    });

    const rangeAccuracy = {};
    Object.keys(ranges).forEach(range => {
      const rangeData = ranges[range];
      rangeAccuracy[range] = {
        accuracy: this.calculateAccuracy(rangeData),
        averageTime: this.calculateAverageTime(rangeData),
        attempts: rangeData.length
      };
    });

    return rangeAccuracy;
  }

  /**
   * Gerar recomendações
   */
  generateRecommendations(data, skillIndex) {
    const recommendations = [];
    
    if (skillIndex < 0.6) {
      recommendations.push({
        type: 'improvement',
        priority: 'high',
        message: 'Pratique comparações simples com números pequenos',
        activities: ['simple_comparisons', 'number_line_games']
      });
    }
    
    const magnitudeDiscrimination = this.assessMagnitudeDiscrimination(data);
    if (magnitudeDiscrimination.byDifficulty.hard.accuracy < 0.5) {
      recommendations.push({
        type: 'specific_skill',
        priority: 'high',
        message: 'Trabalhe discriminação de números próximos',
        activities: ['close_numbers_practice', 'magnitude_games']
      });
    }
    
    const processingSpeed = this.assessProcessingSpeed(data);
    if (processingSpeed.speedCategory === 'slow') {
      recommendations.push({
        type: 'speed',
        priority: 'medium',
        message: 'Pratique comparações rápidas para desenvolver automatismo',
        activities: ['speed_comparisons', 'quick_decision_games']
      });
    }
    
    const rangeAccuracy = this.analyzeRangeAccuracy(data);
    Object.keys(rangeAccuracy).forEach(range => {
      if (rangeAccuracy[range].accuracy < 0.6 && rangeAccuracy[range].attempts >= 2) {
        recommendations.push({
          type: 'range_specific',
          priority: 'medium',
          message: `Pratique mais comparações na faixa ${range}`,
          activities: [`${range}_range_practice`, 'graduated_difficulty']
        });
      }
    });
    
    return recommendations;
  }

  /**
   * Funções auxiliares
   */
  calculateOverallAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter(attempt => attempt.isCorrect).length;
    return correct / data.length;
  }

  groupByComparisonType(data) {
    const grouped = {};
    data.forEach(attempt => {
      const type = attempt.comparisonType || 'magnitude';
      if (!grouped[type]) grouped[type] = [];
      grouped[type].push(attempt);
    });
    return grouped;
  }

  calculateAverageTime(data) {
    const times = data.map(attempt => attempt.responseTime).filter(Boolean);
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }

  calculateAverageDifference(data) {
    const differences = data.map(attempt => 
      Math.abs(attempt.number1 - attempt.number2)
    ).filter(Boolean);
    return differences.length > 0 ? differences.reduce((a, b) => a + b, 0) / differences.length : 0;
  }

  calculateAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter(attempt => attempt.isCorrect).length;
    return correct / data.length;
  }

  calculateMedian(numbers) {
    const sorted = numbers.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? 
      (sorted[middle - 1] + sorted[middle]) / 2 : 
      sorted[middle];
  }

  calculateDiscriminationScore(discrimination) {
    const scores = Object.values(discrimination.byDifficulty).map(level => level.accuracy);
    return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0.5;
  }

  calculateConsistency(data) {
    if (data.length < 2) return 0.5;
    
    const accuracies = this.divideIntoSegments(data, 3).map(segment => this.calculateAccuracy(segment));
    const variance = this.calculateVariance(accuracies);
    
    return Math.max(0, 1 - variance);
  }

  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
  }

  divideIntoSegments(data, numSegments) {
    const segmentSize = Math.floor(data.length / numSegments);
    const segments = [];
    
    for (let i = 0; i < numSegments; i++) {
      const start = i * segmentSize;
      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;
      segments.push(data.slice(start, end));
    }
    
    return segments.filter(segment => segment.length > 0);
  }

  getDefaultAnalysis() {
    return {
      timestamp: new Date().toISOString(),
      collector: 'ComparisonSkillsCollector',
      version: '3.0.0',
      overallAccuracy: 0.5,
      comparativeSkillIndex: 0.5,
      processingSpeed: { speed: 'unknown', score: 0.5 },
      accuracyByType: {},
      magnitudeDiscrimination: {},
      comparisonStrategies: {},
      temporalProgress: { trend: 'insufficient_data', improvement: 0 },
      errorPatterns: { totalErrors: 0, errorRate: 0 },
      cognitiveSkills: {},
      recommendations: [],
      metadata: { totalComparisons: 0, uniqueTypes: 0 }
    };
  }
}
