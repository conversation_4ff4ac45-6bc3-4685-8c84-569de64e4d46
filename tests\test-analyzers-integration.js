/**
 * @file test-analyzers-integration.js
 * @description Teste de integração dos analisadores sem dependências circulares
 * @version 1.0.0
 */

import { getAnalysisOrchestrator } from './src/api/services/analysis/AnalysisOrchestrator.js';
import { getBehavioralAnalyzer } from './src/api/services/analysis/BehavioralAnalyzer.js';
import { logger } from './src/api/services/core/logging/StructuredLogger.js';

async function testAnalyzersIntegration() {
  console.log('🧪 Iniciando teste de integração dos analisadores...\n');
  
  try {
    // 1. Testar criação do AnalysisOrchestrator
    console.log('📊 Criando AnalysisOrchestrator...');
    const orchestrator = getAnalysisOrchestrator({
      enableParallelAnalysis: true,
      analysisTimeout: 10000
    });
    console.log('✅ AnalysisOrchestrator criado com sucesso\n');
    
    // 2. Testar analisador individual
    console.log('🧠 Criando BehavioralAnalyzer...');
    const behavioralAnalyzer = getBehavioralAnalyzer();
    console.log('✅ BehavioralAnalyzer criado com sucesso\n');
    
    // 3. Simular injeção de dependência (sem SystemOrchestrator real)
    console.log('🔗 Testando injeção de dependências...');
    const mockSystemOrchestrator = {
      name: 'MockSystemOrchestrator',
      version: '1.0.0'
    };
    
    orchestrator.injectSystemOrchestrator(mockSystemOrchestrator);
    console.log('✅ Injeção de dependências testada com sucesso\n');
    
    // 4. Testar análise mock
    console.log('🎯 Executando análise mock...');
    const mockGameSession = {
      id: 'test_session_123',
      childId: 'test_child_456',
      gameName: 'MemoryGame',
      duration: 120000,
      interactions: [
        { type: 'click', timestamp: Date.now() - 60000 },
        { type: 'match', timestamp: Date.now() - 30000 },
        { type: 'complete', timestamp: Date.now() }
      ],
      accuracy: 0.85,
      responseTime: 1200
    };
    
    const analysisResult = await orchestrator.orchestrateCompleteAnalysis(mockGameSession);
    console.log('✅ Análise executada com sucesso');
    console.log('📊 Resultado da análise:', {
      orchestrationId: analysisResult.orchestrationId,
      overallScore: analysisResult.consolidated?.overallScore || 'N/A',
      analysisCount: Object.keys(analysisResult.analyses || {}).length,
      orchestrationTime: analysisResult.metadata?.orchestrationTime || 'N/A'
    });
    console.log();
    
    // 5. Testar métricas do cache
    console.log('💾 Verificando métricas do cache...');
    const cacheMetrics = orchestrator.cache.getMetrics();
    console.log('✅ Métricas do cache:', {
      hitRate: cacheMetrics.hitRate,
      size: cacheMetrics.size,
      hits: cacheMetrics.hits,
      misses: cacheMetrics.misses
    });
    console.log();
    
    // 6. Testar métricas do orquestrador
    console.log('📈 Verificando métricas do orquestrador...');
    const orchestratorMetrics = orchestrator.getOrchestratorMetrics();
    console.log('✅ Métricas do orquestrador:', {
      totalOrchestrations: orchestratorMetrics.totalOrchestrations,
      successfulAnalyses: orchestratorMetrics.successfulAnalyses,
      averageTime: Math.round(orchestratorMetrics.averageOrchestrationTime)
    });
    console.log();
    
    console.log('🎉 TESTE DE INTEGRAÇÃO DOS ANALISADORES CONCLUÍDO COM SUCESSO!');
    console.log('✅ Dependências circulares resolvidas');
    console.log('✅ Cache inteligente funcionando');
    console.log('✅ Orquestração de análises operacional');
    console.log('✅ Sistema pronto para integração completa');
    
  } catch (error) {
    console.error('❌ Erro no teste de integração:', error);
    console.error('Stack:', error.stack);
  }
}

// Executar teste
testAnalyzersIntegration();
