/**
 * 🧠 WORKING MEMORY COLLECTOR - Portal Betina V3
 * Coletor especializado em análise de memória de trabalho
 * Avalia capacidade de manter informações ativas durante o jogo
 */

export class WorkingMemoryCollector {
  constructor() {
    this.name = 'WorkingMemoryCollector';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    this.sessionData = {
      activeMemoryLoad: [],
      interferenceEvents: [],
      capacityMeasures: [],
      retentionIntervals: [],
      currentSession: null
    };
    
    console.log('🧠 WorkingMemoryCollector inicializado');
  }

  /**
   * Inicializar nova sessão de coleta
   */
  startSession(sessionId, gameData = {}) {
    this.sessionData.currentSession = {
      sessionId,
      startTime: Date.now(),
      difficulty: gameData.difficulty || 'unknown',
      theme: gameData.theme || 'unknown',
      totalCards: gameData.cardsCount || 0,
      maxExpectedLoad: Math.ceil((gameData.cardsCount || 0) / 2), // Número máximo de pares
      workingMemoryEvents: [],
      capacityMetrics: {
        maxSimultaneousCards: 0,
        averageRetentionTime: 0,
        interferenceRate: 0,
        loadEfficiency: 0,
        forgettingCurve: []
      },
      memorySpan: {
        shortTerm: 0,
        mediumTerm: 0,
        longTerm: 0
      }
    };
    
    console.log(`🧠 WorkingMemoryCollector: Nova sessão iniciada - ${sessionId}`);
  }

  /**
   * Registrar evento de memória de trabalho
   */
  recordInteraction(interactionData) {
    if (!this.sessionData.currentSession) {
      console.warn('🧠 WorkingMemoryCollector: Sessão não iniciada');
      return;
    }

    const timestamp = Date.now();
    const sessionStartTime = this.sessionData.currentSession.startTime;
    
    const workingMemoryEvent = {
      timestamp,
      relativeTime: timestamp - sessionStartTime,
      cardId: interactionData.cardId,
      cardPosition: interactionData.position || { row: 0, col: 0, index: 0 },
      isFirstCard: interactionData.isFirstCard || false,
      isSecondCard: interactionData.isSecondCard || false,
      isMatch: interactionData.isMatch || false,
      reactionTime: interactionData.reactionTime || 0,
      previousCard: interactionData.previousCard || null,
      memoryLoad: this.calculateCurrentMemoryLoad(interactionData),
      retentionInterval: this.calculateRetentionInterval(interactionData),
      interferenceLevel: this.detectInterference(interactionData),
      attentionalDemand: this.assessAttentionalDemand(interactionData)
    };

    // Adicionar ao histórico de eventos
    this.sessionData.currentSession.workingMemoryEvents.push(workingMemoryEvent);
    
    // Atualizar métricas em tempo real
    this.updateCapacityMetrics(workingMemoryEvent);
    
    // Analisar span de memória
    this.updateMemorySpan(workingMemoryEvent);
    
    console.log('🧠 WorkingMemoryCollector: Evento registrado', {
      memoryLoad: workingMemoryEvent.memoryLoad,
      retentionInterval: workingMemoryEvent.retentionInterval,
      interferenceLevel: workingMemoryEvent.interferenceLevel
    });
  }

  /**
   * Calcular carga atual de memória de trabalho
   */
  calculateCurrentMemoryLoad(interactionData) {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    const recentTimeWindow = 10000; // 10 segundos
    const currentTime = Date.now();
    
    // Contar cartas que estão ativamente sendo lembradas
    const recentCards = new Set();
    
    events.forEach(event => {
      if (currentTime - event.timestamp <= recentTimeWindow) {
        if (!event.isMatch) {
          // Adicionar cartas que foram vistas mas não combinadas
          recentCards.add(event.cardId);
          if (event.previousCard) {
            recentCards.add(event.previousCard.id);
          }
        }
      }
    });
    
    // Carga atual = número de cartas sendo ativamente lembradas
    return {
      activeCards: recentCards.size,
      normalizedLoad: recentCards.size / this.sessionData.currentSession.maxExpectedLoad,
      timeWindow: recentTimeWindow
    };
  }

  /**
   * Calcular intervalo de retenção
   */
  calculateRetentionInterval(interactionData) {
    if (!interactionData.previousCard) {
      return { interval: 0, type: 'immediate' };
    }

    const events = this.sessionData.currentSession.workingMemoryEvents;
    
    // Encontrar quando a carta anterior foi vista pela última vez
    let lastSeenTime = null;
    for (let i = events.length - 1; i >= 0; i--) {
      if (events[i].cardId === interactionData.previousCard.id) {
        lastSeenTime = events[i].timestamp;
        break;
      }
    }

    if (!lastSeenTime) {
      return { interval: 0, type: 'unknown' };
    }

    const interval = Date.now() - lastSeenTime;
    
    // Classificar tipo de retenção
    let type;
    if (interval < 2000) {
      type = 'immediate'; // < 2 segundos
    } else if (interval < 10000) {
      type = 'short_term'; // 2-10 segundos
    } else if (interval < 30000) {
      type = 'medium_term'; // 10-30 segundos
    } else {
      type = 'long_term'; // > 30 segundos
    }

    return { interval, type };
  }

  /**
   * Detectar interferência na memória de trabalho
   */
  detectInterference(interactionData) {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    const recentEvents = events.slice(-5); // Últimos 5 eventos
    
    let interferenceScore = 0;
    
    // Interferência por similaridade visual
    if (interactionData.previousCard) {
      const currentSymbol = interactionData.cardId; // Simplificado
      const previousSymbol = interactionData.previousCard.id;
      
      // Se símbolos são similares mas não iguais, há interferência
      if (this.areSymbolsSimilar(currentSymbol, previousSymbol) && currentSymbol !== previousSymbol) {
        interferenceScore += 0.3;
      }
    }
    
    // Interferência por sobrecarga de informação
    const recentCardsCount = new Set(recentEvents.map(e => e.cardId)).size;
    if (recentCardsCount > 4) { // Mais de 4 cartas diferentes nos últimos eventos
      interferenceScore += 0.4;
    }
    
    // Interferência temporal (mudanças rápidas de foco)
    if (recentEvents.length >= 2) {
      const lastInterval = recentEvents[recentEvents.length - 1].timestamp - recentEvents[recentEvents.length - 2].timestamp;
      if (lastInterval < 1000) { // Menos de 1 segundo entre cliques
        interferenceScore += 0.3;
      }
    }
    
    return Math.min(1.0, interferenceScore); // Normalizar entre 0-1
  }

  /**
   * Verificar se símbolos são similares
   */
  areSymbolsSimilar(symbol1, symbol2) {
    // Implementação simples - pode ser expandida
    const similarGroups = [
      ['🍎', '🍊', '🍌'], // Frutas
      ['🚗', '🚲', '🚂', '✈️'], // Transporte
      ['🌟', '⭐', '☀️'], // Estrelas/sol
      ['🌸', '🌺', '🌻', '🌷', '🌹', '🌼'], // Flores
      ['🦋', '🐝'], // Insetos
      ['🐷', '🐄'] // Animais da fazenda
    ];
    
    return similarGroups.some(group => 
      group.includes(symbol1) && group.includes(symbol2)
    );
  }

  /**
   * Avaliar demanda atencional
   */
  assessAttentionalDemand(interactionData) {
    let demand = 0.5; // Demanda base
    
    // Aumentar demanda baseado na carga de memória
    const memoryLoad = this.calculateCurrentMemoryLoad(interactionData);
    demand += memoryLoad.normalizedLoad * 0.3;
    
    // Aumentar demanda se há interferência
    const interference = this.detectInterference(interactionData);
    demand += interference * 0.2;
    
    // Aumentar demanda baseado no tempo de reação
    if (interactionData.reactionTime > 3000) { // Mais de 3 segundos
      demand += 0.2;
    }
    
    return Math.min(1.0, demand);
  }

  /**
   * Atualizar métricas de capacidade
   */
  updateCapacityMetrics(event) {
    const session = this.sessionData.currentSession;
    const metrics = session.capacityMetrics;
    
    // Atualizar carga máxima simultânea
    if (event.memoryLoad.activeCards > metrics.maxSimultaneousCards) {
      metrics.maxSimultaneousCards = event.memoryLoad.activeCards;
    }
    
    // Calcular tempo médio de retenção
    if (event.retentionInterval.interval > 0) {
      const currentAvg = metrics.averageRetentionTime;
      const eventCount = session.workingMemoryEvents.length;
      metrics.averageRetentionTime = (currentAvg * (eventCount - 1) + event.retentionInterval.interval) / eventCount;
    }
    
    // Calcular taxa de interferência
    const totalEvents = session.workingMemoryEvents.length;
    const interferenceEvents = session.workingMemoryEvents.filter(e => e.interferenceLevel > 0.5).length;
    metrics.interferenceRate = interferenceEvents / totalEvents;
    
    // Calcular eficiência de carga
    const successfulMatches = session.workingMemoryEvents.filter(e => e.isMatch).length;
    metrics.loadEfficiency = totalEvents > 0 ? successfulMatches / totalEvents : 0;
    
    // Atualizar curva de esquecimento
    this.updateForgettingCurve(event);
  }

  /**
   * Atualizar curva de esquecimento
   */
  updateForgettingCurve(event) {
    const metrics = this.sessionData.currentSession.capacityMetrics;
    
    if (event.retentionInterval.interval > 0) {
      const forgettingPoint = {
        interval: event.retentionInterval.interval,
        success: event.isMatch,
        timestamp: event.timestamp
      };
      
      metrics.forgettingCurve.push(forgettingPoint);
      
      // Manter apenas os últimos 20 pontos para evitar acúmulo excessivo
      if (metrics.forgettingCurve.length > 20) {
        metrics.forgettingCurve.shift();
      }
    }
  }

  /**
   * Atualizar span de memória
   */
  updateMemorySpan(event) {
    const span = this.sessionData.currentSession.memorySpan;
    const retentionType = event.retentionInterval.type;
    
    // Contabilizar sucessos por tipo de retenção
    if (event.isMatch) {
      switch (retentionType) {
        case 'immediate':
        case 'short_term':
          span.shortTerm++;
          break;
        case 'medium_term':
          span.mediumTerm++;
          break;
        case 'long_term':
          span.longTerm++;
          break;
      }
    }
  }

  /**
   * Análise completa dos dados coletados
   */
  async analyze(gameData = {}) {
    const session = this.sessionData.currentSession;
    if (!session) {
      return this.getEmptyAnalysis();
    }

    const analysis = {
      collectorName: this.name,
      version: this.version,
      sessionId: session.sessionId,
      timestamp: Date.now(),
      
      // Dados básicos da sessão
      sessionSummary: {
        totalEvents: session.workingMemoryEvents.length,
        sessionDuration: Date.now() - session.startTime,
        difficulty: session.difficulty,
        theme: session.theme,
        maxExpectedLoad: session.maxExpectedLoad,
        completed: gameData.completed || false
      },
      
      // Análise de capacidade de memória de trabalho
      workingMemoryCapacity: {
        maxSimultaneousCards: session.capacityMetrics.maxSimultaneousCards,
        averageRetentionTime: session.capacityMetrics.averageRetentionTime,
        interferenceRate: session.capacityMetrics.interferenceRate,
        loadEfficiency: session.capacityMetrics.loadEfficiency,
        estimatedSpan: this.estimateMemorySpan(),
        capacityUtilization: this.calculateCapacityUtilization()
      },
      
      // Análise de retenção
      retentionAnalysis: {
        shortTermSuccess: this.calculateRetentionSuccess('short_term'),
        mediumTermSuccess: this.calculateRetentionSuccess('medium_term'),
        longTermSuccess: this.calculateRetentionSuccess('long_term'),
        forgettingCurve: this.analyzeForgettingCurve(),
        retentionStrength: this.calculateRetentionStrength()
      },
      
      // Análise de interferência
      interferenceAnalysis: {
        overallInterferenceLevel: session.capacityMetrics.interferenceRate,
        visualInterference: this.calculateVisualInterference(),
        temporalInterference: this.calculateTemporalInterference(),
        loadInterference: this.calculateLoadInterference(),
        resistanceToInterference: this.calculateInterferenceResistance()
      },
      
      // Análise de demanda atencional
      attentionalAnalysis: {
        averageDemand: this.calculateAverageDemand(),
        peakDemand: this.calculatePeakDemand(),
        sustainedAttention: this.calculateSustainedAttention(),
        attentionalFlexibility: this.calculateAttentionalFlexibility()
      },
      
      // Recomendações terapêuticas
      therapeuticRecommendations: this.generateTherapeuticRecommendations(),
      
      // Pontuação de capacidades
      capacityScores: {
        workingMemoryCapacity: this.calculateWorkingMemoryScore(),
        retentionStrength: this.calculateRetentionScore(),
        interferenceResistance: this.calculateInterferenceResistanceScore(),
        attentionalControl: this.calculateAttentionalControlScore()
      }
    };

    // Armazenar análise
    this.collectedData.push(analysis);
    
    console.log('🧠 WorkingMemoryCollector: Análise completa gerada', {
      workingMemoryCapacity: analysis.workingMemoryCapacity.estimatedSpan,
      interferenceRate: analysis.interferenceAnalysis.overallInterferenceLevel,
      therapeuticRecommendations: analysis.therapeuticRecommendations.length
    });

    return analysis;
  }

  /**
   * Estimar span de memória de trabalho
   */
  estimateMemorySpan() {
    const session = this.sessionData.currentSession;
    const maxCards = session.capacityMetrics.maxSimultaneousCards;
    const efficiency = session.capacityMetrics.loadEfficiency;
    
    // Span estimado baseado na capacidade máxima e eficiência
    const estimatedSpan = Math.round(maxCards * efficiency);
    
    return {
      rawCapacity: maxCards,
      effectiveSpan: estimatedSpan,
      efficiency: efficiency,
      classification: this.classifyMemorySpan(estimatedSpan)
    };
  }

  /**
   * Classificar span de memória
   */
  classifyMemorySpan(span) {
    if (span >= 6) return 'excellent';
    if (span >= 4) return 'good';
    if (span >= 3) return 'average';
    if (span >= 2) return 'below_average';
    return 'poor';
  }

  /**
   * Calcular utilização de capacidade
   */
  calculateCapacityUtilization() {
    const session = this.sessionData.currentSession;
    const maxCards = session.capacityMetrics.maxSimultaneousCards;
    const expectedLoad = session.maxExpectedLoad;
    
    return expectedLoad > 0 ? maxCards / expectedLoad : 0;
  }

  /**
   * Calcular sucesso de retenção por tipo
   */
  calculateRetentionSuccess(retentionType) {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    const relevantEvents = events.filter(e => e.retentionInterval.type === retentionType);
    
    if (relevantEvents.length === 0) return 0;
    
    const successfulEvents = relevantEvents.filter(e => e.isMatch);
    return successfulEvents.length / relevantEvents.length;
  }

  /**
   * Analisar curva de esquecimento
   */
  analyzeForgettingCurve() {
    const curve = this.sessionData.currentSession.capacityMetrics.forgettingCurve;
    
    if (curve.length < 3) return { slope: 0, pattern: 'insufficient_data' };
    
    // Calcular declínio na performance ao longo do tempo
    const intervals = curve.map(p => p.interval);
    const successes = curve.map(p => p.success ? 1 : 0);
    
    // Análise simples de correlação entre tempo e sucesso
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const avgSuccess = successes.reduce((a, b) => a + b, 0) / successes.length;
    
    let correlation = 0;
    let numerator = 0;
    let denomX = 0;
    let denomY = 0;
    
    for (let i = 0; i < curve.length; i++) {
      const x = intervals[i] - avgInterval;
      const y = successes[i] - avgSuccess;
      numerator += x * y;
      denomX += x * x;
      denomY += y * y;
    }
    
    if (denomX > 0 && denomY > 0) {
      correlation = numerator / Math.sqrt(denomX * denomY);
    }
    
    let pattern;
    if (correlation < -0.3) {
      pattern = 'rapid_forgetting';
    } else if (correlation < -0.1) {
      pattern = 'gradual_forgetting';
    } else if (correlation > 0.1) {
      pattern = 'improving_retention';
    } else {
      pattern = 'stable_retention';
    }
    
    return {
      slope: correlation,
      pattern,
      dataPoints: curve.length
    };
  }

  /**
   * Calcular força de retenção
   */
  calculateRetentionStrength() {
    const shortTerm = this.calculateRetentionSuccess('short_term');
    const mediumTerm = this.calculateRetentionSuccess('medium_term');
    const longTerm = this.calculateRetentionSuccess('long_term');
    
    // Força de retenção com pesos diferentes para cada tipo
    return (shortTerm * 0.3 + mediumTerm * 0.4 + longTerm * 0.3);
  }

  /**
   * Calcular interferência visual
   */
  calculateVisualInterference() {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    const visualInterferenceEvents = events.filter(e => 
      e.interferenceLevel > 0.2 && this.isVisualInterference(e)
    );
    
    return events.length > 0 ? visualInterferenceEvents.length / events.length : 0;
  }

  /**
   * Verificar se é interferência visual
   */
  isVisualInterference(event) {
    // Implementação simplificada - pode ser expandida
    return event.interferenceLevel > 0 && event.previousCard !== null;
  }

  /**
   * Calcular interferência temporal
   */
  calculateTemporalInterference() {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    let rapidSequences = 0;
    
    for (let i = 1; i < events.length; i++) {
      const interval = events[i].timestamp - events[i-1].timestamp;
      if (interval < 1000) { // Menos de 1 segundo entre eventos
        rapidSequences++;
      }
    }
    
    return events.length > 1 ? rapidSequences / (events.length - 1) : 0;
  }

  /**
   * Calcular interferência de carga
   */
  calculateLoadInterference() {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    const highLoadEvents = events.filter(e => e.memoryLoad.normalizedLoad > 0.7);
    
    return events.length > 0 ? highLoadEvents.length / events.length : 0;
  }

  /**
   * Calcular resistência à interferência
   */
  calculateInterferenceResistance() {
    const interferenceRate = this.sessionData.currentSession.capacityMetrics.interferenceRate;
    const efficiency = this.sessionData.currentSession.capacityMetrics.loadEfficiency;
    
    // Resistência = alta eficiência apesar da interferência
    return interferenceRate > 0 ? efficiency / (1 + interferenceRate) : efficiency;
  }

  /**
   * Calcular demanda atencional média
   */
  calculateAverageDemand() {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    if (events.length === 0) return 0;
    
    const totalDemand = events.reduce((sum, e) => sum + e.attentionalDemand, 0);
    return totalDemand / events.length;
  }

  /**
   * Calcular pico de demanda atencional
   */
  calculatePeakDemand() {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    if (events.length === 0) return 0;
    
    return Math.max(...events.map(e => e.attentionalDemand));
  }

  /**
   * Calcular atenção sustentada
   */
  calculateSustainedAttention() {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    if (events.length < 5) return 0;
    
    // Calcular variabilidade do tempo de reação
    const reactionTimes = events.map(e => e.reactionTime).filter(rt => rt > 0);
    if (reactionTimes.length < 3) return 0;
    
    const avgRT = reactionTimes.reduce((a, b) => a + b, 0) / reactionTimes.length;
    const variance = reactionTimes.reduce((sum, rt) => sum + Math.pow(rt - avgRT, 2), 0) / reactionTimes.length;
    const coefficient = Math.sqrt(variance) / avgRT;
    
    // Menor variabilidade = melhor atenção sustentada
    return Math.max(0, 1 - coefficient);
  }

  /**
   * Calcular flexibilidade atencional
   */
  calculateAttentionalFlexibility() {
    const events = this.sessionData.currentSession.workingMemoryEvents;
    if (events.length < 3) return 0;
    
    // Medir capacidade de alternar entre diferentes cartas/posições
    let switches = 0;
    for (let i = 1; i < events.length; i++) {
      const prevPos = events[i-1].cardPosition;
      const currPos = events[i].cardPosition;
      
      if (prevPos.row !== currPos.row || prevPos.col !== currPos.col) {
        switches++;
      }
    }
    
    return events.length > 1 ? switches / (events.length - 1) : 0;
  }

  /**
   * Calcular pontuação de memória de trabalho
   */
  calculateWorkingMemoryScore() {
    const capacity = this.estimateMemorySpan();
    const efficiency = this.sessionData.currentSession.capacityMetrics.loadEfficiency;
    const retention = this.calculateRetentionStrength();
    
    return Math.min(100, Math.round(
      (capacity.effectiveSpan * 15 + efficiency * 40 + retention * 45)
    ));
  }

  /**
   * Calcular pontuação de retenção
   */
  calculateRetentionScore() {
    const retentionStrength = this.calculateRetentionStrength();
    const forgettingCurve = this.analyzeForgettingCurve();
    
    let curveBonus = 0;
    if (forgettingCurve.pattern === 'stable_retention') curveBonus = 0.2;
    else if (forgettingCurve.pattern === 'improving_retention') curveBonus = 0.3;
    else if (forgettingCurve.pattern === 'gradual_forgetting') curveBonus = 0.1;
    
    return Math.min(100, Math.round((retentionStrength + curveBonus) * 100));
  }

  /**
   * Calcular pontuação de resistência à interferência
   */
  calculateInterferenceResistanceScore() {
    const resistance = this.calculateInterferenceResistance();
    const interferenceRate = this.sessionData.currentSession.capacityMetrics.interferenceRate;
    
    // Bônus por baixa taxa de interferência
    const interferenceBonus = Math.max(0, (1 - interferenceRate) * 0.3);
    
    return Math.min(100, Math.round((resistance * 70 + interferenceBonus * 30) * 100));
  }

  /**
   * Calcular pontuação de controle atencional
   */
  calculateAttentionalControlScore() {
    const sustainedAttention = this.calculateSustainedAttention();
    const flexibility = this.calculateAttentionalFlexibility();
    const avgDemand = this.calculateAverageDemand();
    
    // Penalizar alta demanda atencional
    const demandPenalty = Math.max(0, (avgDemand - 0.5) * 0.3);
    
    return Math.min(100, Math.round(
      (sustainedAttention * 40 + flexibility * 30 + (1 - demandPenalty) * 30) * 100
    ));
  }

  /**
   * Gerar recomendações terapêuticas
   */
  generateTherapeuticRecommendations() {
    const recommendations = [];
    const session = this.sessionData.currentSession;
    
    // Recomendações baseadas na capacidade de memória de trabalho
    const span = this.estimateMemorySpan();
    if (span.effectiveSpan < 3) {
      recommendations.push({
        area: 'working_memory_capacity',
        priority: 'high',
        title: 'Expandir Capacidade de Memória de Trabalho',
        description: 'Capacidade limitada para manter informações ativas. Treinar com exercícios graduais.',
        activities: [
          'Jogos de span de dígitos crescente',
          'Atividades de n-back simples',
          'Exercícios de manter listas mentais curtas'
        ]
      });
    }
    
    // Recomendações baseadas na interferência
    if (session.capacityMetrics.interferenceRate > 0.6) {
      recommendations.push({
        area: 'interference_control',
        priority: 'high',
        title: 'Melhorar Resistência à Interferência',
        description: 'Alta susceptibilidade à interferência. Treinar foco seletivo.',
        activities: [
          'Exercícios de atenção seletiva',
          'Jogos com distrações controladas',
          'Atividades de ignorar informações irrelevantes'
        ]
      });
    }
    
    // Recomendações baseadas na retenção
    const retentionStrength = this.calculateRetentionStrength();
    if (retentionStrength < 0.4) {
      recommendations.push({
        area: 'memory_retention',
        priority: 'medium',
        title: 'Fortalecer Retenção de Informações',
        description: 'Dificuldade para manter informações ao longo do tempo.',
        activities: [
          'Exercícios de repetição espaçada',
          'Técnicas de elaboração mental',
          'Jogos de retenção com intervalos crescentes'
        ]
      });
    }
    
    // Recomendações baseadas na atenção
    const sustainedAttention = this.calculateSustainedAttention();
    if (sustainedAttention < 0.5) {
      recommendations.push({
        area: 'sustained_attention',
        priority: 'medium',
        title: 'Desenvolver Atenção Sustentada',
        description: 'Variabilidade alta na atenção. Treinar consistência.',
        activities: [
          'Atividades de vigilância simples',
          'Jogos de tempo de reação consistente',
          'Exercícios de mindfulness adaptados'
        ]
      });
    }
    
    // Recomendação baseada nos scores
    const scores = {
      workingMemory: this.calculateWorkingMemoryScore(),
      retention: this.calculateRetentionScore(),
      interference: this.calculateInterferenceResistanceScore(),
      attention: this.calculateAttentionalControlScore()
    };
    
    const weakestArea = Object.entries(scores).reduce((min, [area, score]) => 
      score < min.score ? { area, score } : min, 
      { area: null, score: 100 }
    );
    
    if (weakestArea.score < 60) {
      recommendations.push({
        area: weakestArea.area,
        priority: 'focus',
        title: `Foco em ${this.getAreaDisplayName(weakestArea.area)}`,
        description: `Área com maior necessidade de desenvolvimento (pontuação: ${weakestArea.score}).`,
        activities: this.getActivitiesForArea(weakestArea.area)
      });
    }
    
    return recommendations;
  }

  /**
   * Obter nome de exibição da área
   */
  getAreaDisplayName(area) {
    const names = {
      workingMemory: 'Memória de Trabalho',
      retention: 'Retenção de Informações',
      interference: 'Resistência à Interferência',
      attention: 'Controle Atencional'
    };
    return names[area] || area;
  }

  /**
   * Obter atividades específicas para área
   */
  getActivitiesForArea(area) {
    const activities = {
      workingMemory: [
        'Exercícios de span duplo (visual + auditivo)',
        'Jogos de atualização mental',
        'Atividades de manipulação mental'
      ],
      retention: [
        'Técnicas de chunking',
        'Exercícios de elaboração',
        'Jogos de associação'
      ],
      interference: [
        'Tarefas de Stroop adaptadas',
        'Exercícios de inibição',
        'Jogos de controle de impulsos'
      ],
      attention: [
        'Exercícios de varredura visual',
        'Atividades de vigilância',
        'Jogos de atenção dividida'
      ]
    };
    return activities[area] || [];
  }

  /**
   * Obter análise vazia
   */
  getEmptyAnalysis() {
    return {
      collectorName: this.name,
      version: this.version,
      sessionId: null,
      timestamp: Date.now(),
      error: 'No session data available',
      workingMemoryCapacity: null,
      retentionAnalysis: null,
      interferenceAnalysis: null,
      attentionalAnalysis: null,
      therapeuticRecommendations: [],
      capacityScores: {
        workingMemoryCapacity: 0,
        retentionStrength: 0,
        interferenceResistance: 0,
        attentionalControl: 0
      }
    };
  }

  /**
   * Finalizar sessão
   */
  endSession() {
    if (this.sessionData.currentSession) {
      console.log(`🧠 WorkingMemoryCollector: Sessão finalizada - ${this.sessionData.currentSession.sessionId}`);
      this.sessionData.currentSession = null;
    }
  }

  /**
   * Obter dados coletados
   */
  getCollectedData() {
    return {
      collectorName: this.name,
      version: this.version,
      isActive: this.isActive,
      totalSessions: this.collectedData.length,
      lastSession: this.sessionData.currentSession,
      collectedData: this.collectedData
    };
  }

  /**
   * Limpar dados
   */
  clearData() {
    this.collectedData = [];
    this.sessionData = {
      activeMemoryLoad: [],
      interferenceEvents: [],
      capacityMeasures: [],
      retentionIntervals: [],
      currentSession: null
    };
    console.log('🧠 WorkingMemoryCollector: Dados limpos');
  }
}

