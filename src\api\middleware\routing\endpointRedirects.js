/**
 * Middleware para redirecionamento de endpoints antigos/incorretos
 * Portal Betina V3 - Correção de 404s
 */

/**
 * Middleware de redirecionamento para endpoints antigos
 * @param {Express} app - Instância do Express
 */
export function setupEndpointRedirects(app) {
  console.log('🔄 Configurando redirecionamentos de endpoints...');

  // Redirecionamento de /api/sessions para /api/metrics/sessions
  app.use('/api/sessions', (req, res) => {
    const newUrl = `/api/metrics/sessions${req.url === '/' ? '' : req.url}`;
    console.log(`🔄 Redirecionando: ${req.originalUrl} → ${newUrl}`);
    
    // Preservar método HTTP e dados
    if (req.method === 'GET') {
      return res.redirect(301, newUrl);
    } else {
      // Para POST/PUT/DELETE, fazer proxy interno
      req.url = newUrl;
      req.originalUrl = newUrl;
      app.handle(req, res);
    }
  });

  // Redirecionamento de /sessions para /api/metrics/sessions
  app.use('/sessions', (req, res) => {
    const newUrl = `/api/metrics/sessions${req.url === '/' ? '' : req.url}`;
    console.log(`🔄 Redirecionando: ${req.originalUrl} → ${newUrl}`);
    res.redirect(301, newUrl);
  });

  // Redirecionamento de /api/games/start para /api/public/games
  app.use('/api/games/start', (req, res) => {
    console.log(`🔄 Redirecionando: ${req.originalUrl} → /api/public/games`);
    res.redirect(301, '/api/public/games');
  });

  // Redirecionamento de /api/games/padroes_visuais para /api/public/games
  app.use('/api/games/padroes_visuais', (req, res) => {
    console.log(`🔄 Redirecionando: ${req.originalUrl} → /api/public/games`);
    res.redirect(301, '/api/public/games');
  });

  // Redirecionamento de /api/padroes_visuais para /api/public/games
  app.use('/api/padroes_visuais', (req, res) => {
    console.log(`🔄 Redirecionando: ${req.originalUrl} → /api/public/games`);
    res.redirect(301, '/api/public/games');
  });

  // Redirecionamento de /metrics para /api/public/metrics (SEM DADOS SENSÍVEIS)
  app.use('/metrics', (req, res) => {
    console.log(`🔄 Redirecionando métricas: ${req.originalUrl} → /api/public/metrics (informações básicas apenas)`);
    res.redirect(301, '/api/public/metrics');
  });

  // Endpoints de compatibilidade (retornar informações básicas sem dados sensíveis)
  
  // Compatibilidade para /api/sessions (SEM DADOS REAIS)
  app.get('/api/sessions', async (req, res) => {
    try {
      console.log('📊 Endpoint de compatibilidade: /api/sessions (PROTEGIDO)');
      
      res.status(401).json({
        success: false,
        message: 'Dados de sessões requerem autenticação',
        error_code: 'AUTHENTICATION_REQUIRED',
        note: 'Sessões contêm dados sensíveis de usuários',
        authEndpoint: '/api/auth/login',
        redirect_to: '/api/metrics/sessions'
      });
    } catch (error) {
      console.error('❌ Erro no endpoint de compatibilidade /api/sessions:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno no endpoint de compatibilidade',
        error_code: 'COMPATIBILITY_ERROR',
        redirect_to: '/api/metrics/sessions'
      });
    }
  });

  // Root endpoint melhorado (SEM DADOS SENSÍVEIS)
  app.get('/', (req, res) => {
    // Verificar se é uma requisição de API
    if (req.headers.accept && req.headers.accept.includes('application/json')) {
      return res.json({
        success: true,
        message: 'Portal Betina V3 API',
        version: '3.0.0',
        description: 'Sistema Terapêutico Multissensorial para Desenvolvimento Cognitivo',
        status: 'operational',
        public_endpoints: {
          health: '/api/public/health',
          games: '/api/public/games',
          activities: '/api/public/activities',
          auth: '/api/auth/login'
        },
        note: 'Para dados específicos, autenticação é necessária'
      });
    }
    
    // Para requisições de browser, servir a página inicial
    res.sendFile('index.html', { root: 'public' });
  });

  console.log('✅ Redirecionamentos de endpoints configurados');
}

/**
 * Middleware de log para requisições 404
 * @param {Express} app - Instância do Express
 */
export function setup404Logger(app) {
  // Middleware para capturar 404s e logar
  app.use('*', (req, res, next) => {
    // Se chegou aqui, é 404
    console.log(`❌ 404 - Endpoint não encontrado: ${req.method} ${req.originalUrl}`);
    console.log(`   Headers Accept: ${req.headers.accept}`);
    console.log(`   User-Agent: ${req.headers['user-agent']?.substring(0, 100)}...`);
    
    const response = {
      success: false,
      message: 'Endpoint não encontrado',
      error_code: 'ENDPOINT_NOT_FOUND',
      requested_url: req.originalUrl,
      method: req.method,
      suggestions: getSuggestions(req.originalUrl)
    };
    
    res.status(404).json(response);
  });
}

/**
 * Sugerir endpoints similares para URLs 404 (APENAS PÚBLICOS E SEGUROS)
 * @param {string} url - URL que gerou 404
 * @returns {Array} Lista de sugestões
 */
function getSuggestions(url) {
  const suggestions = [];
  
  if (url.includes('session')) {
    suggestions.push('/api/auth/login (para acessar dados de sessões)');
  }
  
  if (url.includes('game')) {
    suggestions.push('/api/public/games');
  }
  
  if (url.includes('metric')) {
    suggestions.push('/api/public/metrics (informações básicas)');
    suggestions.push('/api/auth/login (para dados detalhados)');
  }
  
  if (url.includes('dashboard')) {
    suggestions.push('/api/auth/login (dashboard requer autenticação)');
  }
  
  if (url.includes('auth') || url.includes('login')) {
    suggestions.push('/api/auth/login');
  }
  
  // Sugestões padrão se nada for encontrado (APENAS PÚBLICOS)
  if (suggestions.length === 0) {
    suggestions.push('/api/public/health', '/api/public/games', '/api/public/activities', '/api/auth/login');
  }
  
  return suggestions;
}
