/**
 * ⚡ SEQUENTIAL REASONING COLLECTOR
 * Coletor especializado em análise de raciocínio sequencial para PadroesVisuais
 * Portal Betina V3
 */

export class SequentialReasoningCollector {
  constructor() {
    this.reasoningTypes = {
      temporal: 'raciocínio temporal',
      logical: 'raciocínio lógico',
      causal: 'raciocínio causal',
      predictive: 'raciocínio preditivo',
      analytical: 'raciocínio analítico',
      inductive: 'raciocínio indutivo'
    };
    
    this.sequenceComplexity = {
      simple: { level: 1, description: 'sequências lineares' },
      moderate: { level: 2, description: 'padrões básicos' },
      complex: { level: 3, description: 'múltiplas regras' },
      advanced: { level: 4, description: 'padrões aninhados' }
    };
    
    this.logicalOperations = {
      repetition: 'repetição',
      alternation: 'alternância',
      progression: 'progressão',
      symmetry: 'simetria',
      transformation: 'transformação',
      combination: 'combinação'
    };
  }
  
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }

  async analyze(data) {
    if (!data || !data.sequentialData) {
      console.warn('SequentialReasoningCollector: Dados inválidos recebidos', data);
      return {
        temporalReasoning: 0.7,
        logicalSequencing: 0.7,
        patternPrediction: 0.7,
        ruleExtraction: 0.7,
        sequentialMemory: 0.7,
        orderProcessing: 0.7,
        inductiveReasoning: 0.7,
        sequentialPlanning: 0.7
      };
    }

    return {
      temporalReasoning: this.assessTemporalReasoning(data),
      logicalSequencing: this.assessLogicalSequencing(data),
      patternPrediction: this.assessPatternPrediction(data),
      ruleExtraction: this.assessRuleExtraction(data),
      sequentialMemory: this.assessSequentialMemory(data),
      orderProcessing: this.assessOrderProcessing(data),
      inductiveReasoning: this.assessInductiveReasoning(data),
      sequentialPlanning: this.assessSequentialPlanning(data),
      reasoningStrategies: this.identifyReasoningStrategies(data),
      reasoningInsights: this.generateReasoningInsights(data)
    };
  }

  assessTemporalReasoning(data) {
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let temporalScore = 0;
    
    sequences.forEach(sequence => {
      const temporalAccuracy = this.calculateTemporalAccuracy(sequence);
      const timingConsistency = this.assessTimingConsistency(sequence);
      const temporalComplexity = this.getTemporalComplexity(sequence);
      
      // Avaliar qualidade do raciocínio temporal
      const temporalQuality = (temporalAccuracy * 0.5) + (timingConsistency * 0.3) + (temporalComplexity * 0.2);
      temporalScore += temporalQuality;
    });
    
    const avgTemporalScore = temporalScore / sequences.length;
    
    // Bonus para manutenção do raciocínio temporal em sequências longas
    const longSequences = sequences.filter(s => this.getSequenceLength(s) >= 5);
    const longSequenceBonus = longSequences.length > 0 ? 0.1 : 0;
    
    return Math.max(0, Math.min(1, avgTemporalScore + longSequenceBonus));
  }

  assessLogicalSequencing(data) {
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let logicalScore = 0;
    
    sequences.forEach(sequence => {
      const logicalPattern = this.identifyLogicalPattern(sequence);
      const patternComplexity = this.getPatternComplexity(logicalPattern);
      const logicalAccuracy = this.calculateLogicalAccuracy(sequence, logicalPattern);
      
      // Avaliar qualidade do sequenciamento lógico
      const logicalQuality = logicalAccuracy * patternComplexity;
      logicalScore += logicalQuality;
    });
    
    const avgLogicalScore = logicalScore / sequences.length;
    
    return Math.max(0, Math.min(1, avgLogicalScore));
  }

  assessPatternPrediction(data) {
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let predictionScore = 0;
    let predictiveTasks = 0;
    
    sequences.forEach(sequence => {
      if (this.isPredictiveTask(sequence)) {
        const predictionAccuracy = this.calculatePredictionAccuracy(sequence);
        const predictionComplexity = this.getPredictionComplexity(sequence);
        const confidenceLevel = this.assessPredictionConfidence(sequence);
        
        // Avaliar qualidade da predição de padrões
        const predictionQuality = (predictionAccuracy * 0.6) + (predictionComplexity * 0.2) + (confidenceLevel * 0.2);
        predictionScore += predictionQuality;
        predictiveTasks++;
      }
    });
    
    if (predictiveTasks === 0) {
      // Inferir capacidade preditiva das sequências gerais
      return this.inferPredictionFromSequences(sequences);
    }
    
    const avgPredictionScore = predictionScore / predictiveTasks;
    
    return Math.max(0, Math.min(1, avgPredictionScore));
  }

  assessRuleExtraction(data) {
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length < 3) return 0.7; // Precisa de múltiplas sequências para extrair regras
    
    // Identificar regras consistentes entre sequências
    const extractedRules = this.extractConsistentRules(sequences);
    let ruleExtractionScore = 0;
    
    extractedRules.forEach(rule => {
      const ruleAccuracy = this.calculateRuleAccuracy(rule, sequences);
      const ruleComplexity = this.getRuleComplexity(rule);
      const ruleGeneralizability = this.assessRuleGeneralizability(rule, sequences);
      
      // Avaliar qualidade da extração de regras
      const ruleQuality = (ruleAccuracy * 0.4) + (ruleComplexity * 0.3) + (ruleGeneralizability * 0.3);
      ruleExtractionScore += ruleQuality;
    });
    
    if (extractedRules.length === 0) return 0.7;
    
    const avgRuleScore = ruleExtractionScore / extractedRules.length;
    
    return Math.max(0, Math.min(1, avgRuleScore));
  }

  assessSequentialMemory(data) {
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let sequentialMemoryScore = 0;
    
    sequences.forEach(sequence => {
      const orderRetention = this.calculateOrderRetention(sequence);
      const sequenceSpan = this.calculateSequenceSpan(sequence);
      const memoryDecay = this.assessMemoryDecay(sequence);
      
      // Avaliar qualidade da memória sequencial
      const memoryQuality = (orderRetention * 0.5) + (sequenceSpan * 0.3) + (memoryDecay * 0.2);
      sequentialMemoryScore += memoryQuality;
    });
    
    const avgMemoryScore = sequentialMemoryScore / sequences.length;
    
    return Math.max(0, Math.min(1, avgMemoryScore));
  }

  assessOrderProcessing(data) {
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let orderProcessingScore = 0;
    
    sequences.forEach(sequence => {
      const orderAccuracy = this.calculateOrderAccuracy(sequence);
      const processingSpeed = this.calculateOrderProcessingSpeed(sequence);
      const orderComplexity = this.getOrderComplexity(sequence);
      
      // Avaliar qualidade do processamento de ordem
      const processingQuality = (orderAccuracy * 0.5) + (processingSpeed * 0.3) + (orderComplexity * 0.2);
      orderProcessingScore += processingQuality;
    });
    
    const avgProcessingScore = orderProcessingScore / sequences.length;
    
    return Math.max(0, Math.min(1, avgProcessingScore));
  }

  assessInductiveReasoning(data) {
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length < 2) return 0.7;
    
    // Avaliar capacidade de generalizar a partir de exemplos
    let inductiveScore = 0;
    let inductiveTasks = 0;
    
    for (let i = 1; i < sequences.length; i++) {
      const current = sequences[i];
      const previous = sequences.slice(0, i);
      
      const generalization = this.assessGeneralization(current, previous);
      
      if (generalization) {
        const inductiveAccuracy = generalization.accuracy;
        const generalizationLevel = generalization.level;
        const transferability = generalization.transferability;
        
        // Avaliar qualidade do raciocínio indutivo
        const inductiveQuality = (inductiveAccuracy * 0.5) + (generalizationLevel * 0.3) + (transferability * 0.2);
        inductiveScore += inductiveQuality;
        inductiveTasks++;
      }
    }
    
    if (inductiveTasks === 0) return 0.7;
    
    const avgInductiveScore = inductiveScore / inductiveTasks;
    
    return Math.max(0, Math.min(1, avgInductiveScore));
  }

  assessSequentialPlanning(data) {
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let planningScore = 0;
    
    sequences.forEach(sequence => {
      const planningEfficiency = this.calculatePlanningEfficiency(sequence);
      const stepOptimization = this.assessStepOptimization(sequence);
      const goalDirectedness = this.assessGoalDirectedness(sequence);
      
      // Avaliar qualidade do planejamento sequencial
      const planningQuality = (planningEfficiency * 0.4) + (stepOptimization * 0.3) + (goalDirectedness * 0.3);
      planningScore += planningQuality;
    });
    
    const avgPlanningScore = planningScore / sequences.length;
    
    return Math.max(0, Math.min(1, avgPlanningScore));
  }

  // Métodos auxiliares

  calculateTemporalAccuracy(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    const minLength = Math.min(target.length, player.length);
    
    let temporalMatches = 0;
    
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        temporalMatches++;
      }
    }
    
    return temporalMatches / target.length;
  }

  assessTimingConsistency(sequence) {
    if (!sequence.timingData) return 0.7;
    
    const intervals = sequence.timingData.intervals || [];
    
    if (intervals.length < 2) return 0.7;
    
    // Calcular consistência dos intervalos de tempo
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Menor variabilidade = maior consistência
    const consistency = Math.max(0, 1 - (standardDeviation / avgInterval));
    
    return consistency;
  }

  getTemporalComplexity(sequence) {
    const length = this.getSequenceLength(sequence);
    const timingVariability = sequence.timingData ? this.calculateTimingVariability(sequence.timingData) : 1;
    
    // Complexidade baseada em comprimento e variabilidade temporal
    const lengthComplexity = Math.min(1.5, length / 5);
    const timingComplexity = Math.min(1.5, timingVariability);
    
    return (lengthComplexity + timingComplexity) / 2;
  }

  getSequenceLength(sequence) {
    return sequence.targetSequence ? sequence.targetSequence.length : 3;
  }

  calculateTimingVariability(timingData) {
    const intervals = timingData.intervals || [];
    
    if (intervals.length < 2) return 1;
    
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    
    // Normalizar variabilidade
    return Math.min(2, Math.sqrt(variance) / avgInterval);
  }

  identifyLogicalPattern(sequence) {
    if (!sequence.targetSequence) return { type: 'none', complexity: 1 };
    
    const seq = sequence.targetSequence;
    
    // Identificar diferentes tipos de padrões lógicos
    if (this.isRepetitionPattern(seq)) {
      return { type: 'repetition', complexity: 1.2 };
    }
    
    if (this.isAlternationPattern(seq)) {
      return { type: 'alternation', complexity: 1.4 };
    }
    
    if (this.isProgressionPattern(seq)) {
      return { type: 'progression', complexity: 1.6 };
    }
    
    if (this.isSymmetryPattern(seq)) {
      return { type: 'symmetry', complexity: 1.8 };
    }
    
    return { type: 'complex', complexity: 2.0 };
  }

  isRepetitionPattern(sequence) {
    if (sequence.length < 2) return false;
    
    // Verificar repetição de elementos
    for (let patternLength = 1; patternLength <= Math.floor(sequence.length / 2); patternLength++) {
      const pattern = sequence.slice(0, patternLength);
      let isRepeating = true;
      
      for (let i = patternLength; i < sequence.length; i++) {
        if (sequence[i] !== pattern[i % patternLength]) {
          isRepeating = false;
          break;
        }
      }
      
      if (isRepeating) return true;
    }
    
    return false;
  }

  isAlternationPattern(sequence) {
    if (sequence.length < 4) return false;
    
    // Verificar alternância AB AB AB...
    for (let i = 2; i < sequence.length; i++) {
      if (sequence[i] !== sequence[i - 2]) {
        return false;
      }
    }
    
    return true;
  }

  isProgressionPattern(sequence) {
    if (sequence.length < 3) return false;
    
    // Verificar progressão (assumindo ordem das formas)
    const shapeOrder = ['circle', 'square', 'triangle', 'diamond', 'star', 'heart'];
    const indices = sequence.map(shape => shapeOrder.indexOf(shape));
    
    if (indices.includes(-1)) return false;
    
    // Verificar progressão aritmética
    if (indices.length < 3) return false;
    
    const diff = indices[1] - indices[0];
    for (let i = 2; i < indices.length; i++) {
      if (indices[i] - indices[i - 1] !== diff) {
        return false;
      }
    }
    
    return true;
  }

  isSymmetryPattern(sequence) {
    if (sequence.length < 3) return false;
    
    // Verificar simetria (palíndromo)
    for (let i = 0; i < Math.floor(sequence.length / 2); i++) {
      if (sequence[i] !== sequence[sequence.length - 1 - i]) {
        return false;
      }
    }
    
    return true;
  }

  getPatternComplexity(logicalPattern) {
    return logicalPattern.complexity || 1.0;
  }

  calculateLogicalAccuracy(sequence, logicalPattern) {
    if (!sequence.playerSequence && !sequence.targetSequence) return 0;
    
    // Se não temos playerSequence, usar targetSequence como referência
    const sequenceToAnalyze = sequence.playerSequence || sequence.targetSequence;
    
    // Verificar se a sequência segue o padrão lógico identificado
    const patternAdherence = this.checkPatternAdherence(sequenceToAnalyze, logicalPattern);
    const baseAccuracy = sequence.isCorrect ? 1.0 : 0.7;
    
    // Combinar aderência ao padrão com precisão base
    return (patternAdherence * 0.7) + (baseAccuracy * 0.3);
  }

  checkPatternAdherence(playerSequence, logicalPattern) {
    switch (logicalPattern.type) {
      case 'repetition':
        return this.isRepetitionPattern(playerSequence) ? 1 : 0;
      case 'alternation':
        return this.isAlternationPattern(playerSequence) ? 1 : 0;
      case 'progression':
        return this.isProgressionPattern(playerSequence) ? 1 : 0;
      case 'symmetry':
        return this.isSymmetryPattern(playerSequence) ? 1 : 0;
      default:
        return 0.5; // Padrão complexo ou não identificado
    }
  }

  isPredictiveTask(sequence) {
    // Verificar se a tarefa envolve predição (exemplo: completar sequência)
    return sequence.taskType === 'prediction' || sequence.incomplete === true;
  }

  calculatePredictionAccuracy(sequence) {
    if (!sequence.predictedElements || !sequence.actualElements) return 0;
    
    const predicted = sequence.predictedElements;
    const actual = sequence.actualElements;
    const minLength = Math.min(predicted.length, actual.length);
    
    let matches = 0;
    for (let i = 0; i < minLength; i++) {
      if (predicted[i] === actual[i]) {
        matches++;
      }
    }
    
    return matches / actual.length;
  }

  getPredictionComplexity(sequence) {
    const predictionLength = sequence.predictedElements ? sequence.predictedElements.length : 1;
    const contextLength = sequence.contextElements ? sequence.contextElements.length : 3;
    
    // Complexidade baseada no tamanho da predição e contexto
    const predictionFactor = Math.min(1.5, predictionLength / 2);
    const contextFactor = Math.min(1.5, contextLength / 5);
    
    return (predictionFactor + contextFactor) / 2;
  }

  assessPredictionConfidence(sequence) {
    // Avaliar confiança na predição baseado no tempo de resposta
    const responseTime = sequence.responseTime || 2000;
    
    // Tempos moderados indicam maior confiança (nem muito rápido nem muito lento)
    const optimalTime = 3000;
    const timeDifference = Math.abs(responseTime - optimalTime);
    const confidence = Math.max(0, 1 - (timeDifference / optimalTime));
    
    return confidence;
  }

  inferPredictionFromSequences(sequences) {
    if (sequences.length === 0) return 0.7;
    
    // Inferir capacidade preditiva das sequências gerais
    const accuracies = sequences.map(s => {
      const accuracy = this.calculateTemporalAccuracy(s);
      return isNaN(accuracy) ? 0.7 : accuracy;
    });
    
    const avgAccuracy = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    
    if (sequences.length < 2) {
      return Math.max(0, Math.min(1, avgAccuracy));
    }
    
    // Bonus para melhoria ao longo das sequências (indica aprendizado preditivo)
    const firstHalf = accuracies.slice(0, Math.floor(accuracies.length / 2));
    const secondHalf = accuracies.slice(Math.floor(accuracies.length / 2));
    
    const firstAvg = firstHalf.length > 0 ? firstHalf.reduce((sum, acc) => sum + acc, 0) / firstHalf.length : 0;
    const secondAvg = secondHalf.length > 0 ? secondHalf.reduce((sum, acc) => sum + acc, 0) / secondHalf.length : 0;
    
    const improvementBonus = Math.max(0, Math.min(0.2, secondAvg - firstAvg));
    
    return Math.max(0, Math.min(1, avgAccuracy + improvementBonus));
  }

  extractConsistentRules(sequences) {
    const rules = [];
    
    // Identificar regras que se aplicam a múltiplas sequências
    const patternTypes = {};
    
    sequences.forEach(sequence => {
      const pattern = this.identifyLogicalPattern(sequence);
      if (!patternTypes[pattern.type]) {
        patternTypes[pattern.type] = [];
      }
      patternTypes[pattern.type].push(sequence);
    });
    
    // Extrair regras consistentes
    Object.keys(patternTypes).forEach(patternType => {
      const sequencesOfType = patternTypes[patternType];
      
      if (sequencesOfType.length >= 2) { // Regra deve se aplicar a pelo menos 2 sequências
        rules.push({
          type: patternType,
          sequences: sequencesOfType,
          frequency: sequencesOfType.length / sequences.length
        });
      }
    });
    
    return rules;
  }

  calculateRuleAccuracy(rule, sequences) {
    const ruleSequences = rule.sequences;
    const correctApplications = ruleSequences.filter(s => s.isCorrect).length;
    
    return correctApplications / ruleSequences.length;
  }

  getRuleComplexity(rule) {
    const complexities = {
      repetition: 1.2,
      alternation: 1.4,
      progression: 1.6,
      symmetry: 1.8,
      complex: 2.0
    };
    
    return complexities[rule.type] || 1.0;
  }

  assessRuleGeneralizability(rule, allSequences) {
    // Avaliar se a regra se generaliza bem para outras sequências
    const ruleSequences = rule.sequences;
    const otherSequences = allSequences.filter(s => !ruleSequences.includes(s));
    
    if (otherSequences.length === 0) return 0.7;
    
    // Verificar se a regra poderia se aplicar a outras sequências
    let applicableCount = 0;
    
    otherSequences.forEach(sequence => {
      const pattern = this.identifyLogicalPattern(sequence);
      if (pattern.type === rule.type) {
        applicableCount++;
      }
    });
    
    return applicableCount / otherSequences.length;
  }

  calculateOrderRetention(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    
    // Calcular quantos elementos estão na ordem correta
    let correctOrder = 0;
    const minLength = Math.min(target.length, player.length);
    
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        correctOrder++;
      } else {
        break; // Para na primeira ordem incorreta
      }
    }
    
    return correctOrder / target.length;
  }

  calculateSequenceSpan(sequence) {
    const length = this.getSequenceLength(sequence);
    const maxSpan = 8; // Span máximo considerado
    
    // Normalizar span da sequência
    return Math.min(1, length / maxSpan);
  }

  assessMemoryDecay(sequence) {
    const showTime = sequence.showTime || 5000;
    const accuracy = this.calculateTemporalAccuracy(sequence);
    
    // Avaliar se há degradação da memória com o tempo
    const timeChallenge = Math.min(1, showTime / 10000); // Normalizado para 10 segundos
    const decayResistance = accuracy * (1 + timeChallenge);
    
    return Math.min(1, decayResistance);
  }

  calculateOrderAccuracy(sequence) {
    return this.calculateOrderRetention(sequence);
  }

  calculateOrderProcessingSpeed(sequence) {
    const responseTime = sequence.responseTime || 2000;
    const sequenceLength = this.getSequenceLength(sequence);
    
    // Velocidade normalizada baseada no comprimento da sequência
    const expectedTime = 1000 + (sequenceLength * 500); // Tempo base + tempo por elemento
    const speedScore = Math.max(0, Math.min(1, expectedTime / responseTime));
    
    return speedScore;
  }

  getOrderComplexity(sequence) {
    const length = this.getSequenceLength(sequence);
    const uniqueElements = sequence.targetSequence ? new Set(sequence.targetSequence).size : 1;
    
    // Complexidade baseada em comprimento e variedade
    const lengthComplexity = Math.min(1.5, length / 5);
    const varietyComplexity = Math.min(1.5, uniqueElements / 3);
    
    return (lengthComplexity + varietyComplexity) / 2;
  }

  assessGeneralization(currentSequence, previousSequences) {
    if (previousSequences.length === 0) return null;
    
    // Identificar padrões nas sequências anteriores
    const previousPatterns = previousSequences.map(s => this.identifyLogicalPattern(s));
    const currentPattern = this.identifyLogicalPattern(currentSequence);
    
    // Verificar se o padrão atual generaliza dos anteriores
    const similarPatterns = previousPatterns.filter(p => p.type === currentPattern.type);
    
    if (similarPatterns.length === 0) return null;
    
    const generalizationLevel = similarPatterns.length / previousSequences.length;
    const accuracy = currentSequence.isCorrect ? 1 : 0;
    const transferability = this.calculateTransferability(currentPattern, similarPatterns);
    
    return {
      accuracy,
      level: generalizationLevel,
      transferability
    };
  }

  calculateTransferability(currentPattern, similarPatterns) {
    // Avaliar o quão bem o padrão se transfere
    const avgComplexity = similarPatterns.reduce((sum, p) => sum + p.complexity, 0) / similarPatterns.length;
    const complexityDifference = Math.abs(currentPattern.complexity - avgComplexity);
    
    // Menor diferença de complexidade = maior transferabilidade
    const transferability = Math.max(0, 1 - (complexityDifference / 2));
    
    return transferability;
  }

  calculatePlanningEfficiency(sequence) {
    const responseTime = sequence.responseTime || 2000;
    const sequenceLength = this.getSequenceLength(sequence);
    const accuracy = sequence.isCorrect ? 1 : 0;
    
    // Eficiência baseada em tempo, comprimento e precisão
    const timeEfficiency = Math.max(0, Math.min(1, (4000 - responseTime) / 3000));
    const lengthFactor = Math.min(1, sequenceLength / 5);
    
    return (accuracy * 0.5) + (timeEfficiency * 0.3) + (lengthFactor * 0.2);
  }

  assessStepOptimization(sequence) {
    if (!sequence.stepData) return 0.7;
    
    const steps = sequence.stepData.steps || [];
    const optimalSteps = sequence.stepData.optimalSteps || steps.length;
    
    // Avaliar se o número de passos está próximo do ótimo
    const stepEfficiency = Math.max(0, 1 - Math.abs(steps.length - optimalSteps) / optimalSteps);
    
    return stepEfficiency;
  }

  assessGoalDirectedness(sequence) {
    const accuracy = sequence.isCorrect ? 1 : 0;
    const responseTime = sequence.responseTime || 2000;
    
    // Avaliar direcionamento ao objetivo baseado em precisão e tempo
    const timeScore = Math.max(0, Math.min(1, (4000 - responseTime) / 3000));
    const goalDirectedness = (accuracy * 0.7) + (timeScore * 0.3);
    
    return goalDirectedness;
  }

  identifyReasoningStrategies(data) {
    const strategies = [];
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length === 0) return strategies;
    
    // Identificar estratégias de raciocínio
    if (this.detectAnalyticalStrategy(sequences)) {
      strategies.push('analytical');
    }
    
    if (this.detectInductiveStrategy(sequences)) {
      strategies.push('inductive');
    }
    
    if (this.detectPatternMatchingStrategy(sequences)) {
      strategies.push('pattern_matching');
    }
    
    if (this.detectTrialAndErrorStrategy(sequences)) {
      strategies.push('trial_and_error');
    }
    
    return strategies;
  }

  detectAnalyticalStrategy(sequences) {
    // Detectar estratégia analítica (tempos consistentes, alta precisão)
    const responseTimes = sequences
      .filter(s => s.responseTime)
      .map(s => s.responseTime);
    
    if (responseTimes.length < 3) return false;
    
    const avgTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / responseTimes.length;
    const consistency = 1 - (Math.sqrt(variance) / avgTime);
    
    const accuracy = sequences.filter(s => s.isCorrect).length / sequences.length;
    
    return consistency > 0.7 && accuracy > 0.7 && avgTime > 2000; // Tempo suficiente para análise
  }

  detectInductiveStrategy(sequences) {
    // Detectar estratégia indutiva (melhoria ao longo do tempo)
    if (sequences.length < 4) return false;
    
    const firstHalf = sequences.slice(0, Math.floor(sequences.length / 2));
    const secondHalf = sequences.slice(Math.floor(sequences.length / 2));
    
    const firstAccuracy = firstHalf.filter(s => s.isCorrect).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter(s => s.isCorrect).length / secondHalf.length;
    
    return secondAccuracy > firstAccuracy + 0.15; // Melhoria significativa
  }

  detectPatternMatchingStrategy(sequences) {
    // Detectar estratégia de correspondência de padrões
    const patterns = sequences.map(s => this.identifyLogicalPattern(s));
    const patternTypes = {};
    
    patterns.forEach(pattern => {
      patternTypes[pattern.type] = (patternTypes[pattern.type] || 0) + 1;
    });
    
    // Se há predominância de um tipo de padrão, indica estratégia de matching
    const maxFrequency = Math.max(...Object.values(patternTypes));
    return maxFrequency > sequences.length * 0.6;
  }

  detectTrialAndErrorStrategy(sequences) {
    // Detectar estratégia de tentativa e erro (variabilidade alta, tempo baixo)
    const responseTimes = sequences
      .filter(s => s.responseTime)
      .map(s => s.responseTime);
    
    if (responseTimes.length < 3) return false;
    
    const avgTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / responseTimes.length;
    const variability = Math.sqrt(variance) / avgTime;
    
    const accuracy = sequences.filter(s => s.isCorrect).length / sequences.length;
    
    return variability > 0.5 && avgTime < 2000 && accuracy < 0.7; // Alta variabilidade, tempo baixo, precisão baixa
  }

  generateReasoningInsights(data) {
    const insights = [];
    const sequences = data.sequentialData.sequences || [];
    
    if (sequences.length === 0) return insights;
    
    // Analisar capacidade de raciocínio sequencial geral
    const avgAccuracy = sequences.filter(s => s.isCorrect).length / sequences.length;
    
    if (avgAccuracy < 0.6) {
      insights.push('Dificuldades no raciocínio sequencial');
    } else if (avgAccuracy > 0.8) {
      insights.push('Excelente capacidade de raciocínio sequencial');
    }
    
    // Analisar padrões identificados
    const patterns = sequences.map(s => this.identifyLogicalPattern(s));
    const patternTypes = {};
    
    patterns.forEach(pattern => {
      patternTypes[pattern.type] = (patternTypes[pattern.type] || 0) + 1;
    });
    
    const dominantPattern = Object.keys(patternTypes).reduce((a, b) => 
      patternTypes[a] > patternTypes[b] ? a : b
    );
    
    if (patternTypes[dominantPattern] > sequences.length * 0.6) {
      insights.push(`Facilidade com padrões de ${dominantPattern}`);
    }
    
    // Analisar complexidade máxima
    const maxComplexity = Math.max(...patterns.map(p => p.complexity));
    
    if (maxComplexity >= 1.8) {
      insights.push('Capaz de lidar com padrões complexos');
    } else if (maxComplexity <= 1.2) {
      insights.push('Limitado a padrões simples');
    }
    
    // Analisar estratégias identificadas
    const strategies = this.identifyReasoningStrategies(data);
    if (strategies.length > 0) {
      insights.push(`Estratégias de raciocínio identificadas: ${strategies.join(', ')}`);
    }
    
    return insights;
  }
}
