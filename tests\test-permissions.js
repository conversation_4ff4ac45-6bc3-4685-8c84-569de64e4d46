/**
 * Teste para verificar se a permissão read:dashboard é reconhecida
 */

import permissionsModule from './src/api/middleware/auth/permissions.js';

console.log('🔍 Testando configuração de permissões...');

try {
  // Verificar se a permissão read:dashboard está disponível
  const permissions = permissionsModule.PERMISSIONS;
  
  if (permissions && permissions.READ_DASHBOARD === 'read:dashboard') {
    console.log('✅ Permissão READ_DASHBOARD encontrada:', permissions.READ_DASHBOARD);
  } else {
    console.log('❌ Permissão READ_DASHBOARD não encontrada');
    console.log('Permissões disponíveis:', Object.keys(permissions || {}));
  }
  
  // Verificar se os roles incluem read:dashboard
  const rolePermissions = permissionsModule.ROLE_PERMISSIONS;
  
  if (rolePermissions) {
    console.log('\n📋 Verificando permissões por role:');
    
    const rolesToCheck = ['admin', 'therapist', 'parent', 'user'];
    
    rolesToCheck.forEach(role => {
      const permissions = rolePermissions[role] || [];
      const hasReadDashboard = permissions.includes('read:dashboard');
      const status = hasReadDashboard ? '✅' : '❌';
      console.log(`${status} ${role}: ${hasReadDashboard ? 'TEM' : 'NÃO TEM'} read:dashboard`);
    });
  }
  
  console.log('\n🎯 Teste concluído com sucesso!');
  
} catch (error) {
  console.error('❌ Erro ao testar permissões:', error.message);
}
