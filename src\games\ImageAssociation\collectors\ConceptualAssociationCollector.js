// ============================================================================
// CONCEPTUAL ASSOCIATION COLLECTOR - ATIVIDADE 5
// Coleta e análise de dados para associação conceitual e pensamento abstrato
// ============================================================================

import { BaseCollector } from '../../../utils/BaseCollector.js';

export class ConceptualAssociationCollector extends BaseCollector {
  constructor() {
    super('ConceptualAssociation');
    
    this.cognitiveMetrics = {
      // Métricas específicas de pensamento conceitual
      abstractThinking: [],
      conceptualFlexibility: [],
      metaphoricalReasoning: [],
      analogicalThinking: [],
      conceptualIntegration: [],
      
      // Padrões conceituais
      conceptualCategories: [],
      semanticNetworks: [],
      conceptualBridging: [],
      abstractionLevels: [],
      
      // Análise de representações mentais
      symbolicThinking: [],
      conceptualMapping: [],
      meaningMaking: [],
      conceptualDepth: []
    };

    this.conceptualDomains = {
      time: {
        category: 'temporal',
        abstractLevel: 'high',
        concepts: ['past', 'future', 'duration', 'sequence', 'moment'],
        symbols: ['⏰', '📅', '⌛', '🕐', '⏳'],
        metaphors: ['river', 'circle', 'arrow', 'wave'],
        properties: ['linear', 'cyclical', 'relative', 'measurable'],
        cognitive_load: 'high'
      },
      freedom: {
        category: 'philosophical',
        abstractLevel: 'very_high',
        concepts: ['liberation', 'choice', 'autonomy', 'independence', 'rights'],
        symbols: ['🕊️', '🗽', '🆓', '🔓', '🌅'],
        metaphors: ['bird', 'open_road', 'breaking_chains', 'soaring'],
        properties: ['subjective', 'cultural', 'political', 'personal'],
        cognitive_load: 'very_high'
      },
      growth: {
        category: 'biological_metaphorical',
        abstractLevel: 'medium',
        concepts: ['development', 'expansion', 'progress', 'maturation', 'evolution'],
        symbols: ['🌱', '📈', '🌳', '🏗️', '💪'],
        metaphors: ['plant', 'building', 'journey', 'transformation'],
        properties: ['gradual', 'directional', 'potential', 'organic'],
        cognitive_load: 'medium'
      },
      connection: {
        category: 'relational',
        abstractLevel: 'medium',
        concepts: ['bond', 'link', 'relationship', 'network', 'unity'],
        symbols: ['🔗', '🌐', '🤝', '💞', '🧩'],
        metaphors: ['bridge', 'web', 'chain', 'fabric'],
        properties: ['reciprocal', 'structural', 'dynamic', 'meaningful'],
        cognitive_load: 'medium'
      },
      balance: {
        category: 'systemic',
        abstractLevel: 'medium',
        concepts: ['equilibrium', 'harmony', 'stability', 'proportion', 'moderation'],
        symbols: ['⚖️', '☯️', '⚡', '🎭', '🌊'],
        metaphors: ['scales', 'tightrope', 'dance', 'equation'],
        properties: ['dynamic', 'oppositional', 'harmonious', 'centered'],
        cognitive_load: 'medium'
      },
      transformation: {
        category: 'processual',
        abstractLevel: 'high',
        concepts: ['change', 'metamorphosis', 'conversion', 'evolution', 'rebirth'],
        symbols: ['🦋', '🔄', '⚡', '🌙', '🔮'],
        metaphors: ['butterfly', 'alchemy', 'phoenix', 'seasons'],
        properties: ['processual', 'irreversible', 'emergent', 'profound'],
        cognitive_load: 'high'
      }
    };

    this.sessionData = {
      startTime: null,
      endTime: null,
      totalConceptualAssociations: 0,
      correctConceptualAssociations: 0,
      incorrectConceptualAssociations: 0,
      averageConceptualProcessingTime: 0,
      conceptsByAccuracy: {},
      conceptualConfusions: {},
      abstractionScore: 0,
      metaphoricalScore: 0,
      difficultyLevel: 'beginner'
    };
  }

  // ========================================================================
  // COLETA DE DADOS DE ASSOCIAÇÃO CONCEITUAL
  // ========================================================================

  collectConceptualAssociation(associationData) {
    const {
      selectedSymbol,
      targetConcept,
      actualConcept,
      responseTime,
      timestamp,
      metaphoricalJustification = null,
      conceptualPath = [],
      abstractionLevel = 'medium'
    } = associationData;

    const isCorrect = targetConcept === actualConcept;
    
    const association = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedSymbol: {
        id: selectedSymbol.id,
        symbol: selectedSymbol.symbol,
        actualConcept: actualConcept,
        abstractLevel: this.conceptualDomains[actualConcept]?.abstractLevel,
        category: this.conceptualDomains[actualConcept]?.category
      },
      targetConcept,
      isCorrect,
      conceptualAnalysis: {
        abstractionLevel: this.conceptualDomains[targetConcept]?.abstractLevel,
        cognitiveLoad: this.conceptualDomains[targetConcept]?.cognitive_load,
        category: this.conceptualDomains[targetConcept]?.category,
        conceptualProperties: this.conceptualDomains[targetConcept]?.properties
      },
      context: {
        metaphoricalJustification,
        conceptualPath,
        requestedAbstractionLevel: abstractionLevel,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };

    this.interactions.push(association);

    // Análises cognitivas especializadas
    this.analyzeAbstractThinking(association);
    this.analyzeMetaphoricalReasoning(association);
    this.analyzeConceptualFlexibility(association);
    this.analyzeSymbolicThinking(association);
    
    // Atualizar métricas de sessão
    this.updateSessionMetrics(association);

    return association;
  }

  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================

  analyzeAbstractThinking(association) {
    const { responseTime, isCorrect, targetConcept, selectedSymbol } = association;
    
    const abstractMetric = {
      timestamp: association.timestamp,
      concept: targetConcept,
      symbol: selectedSymbol.symbol,
      recognized: isCorrect,
      responseTime,
      abstractionLevel: association.conceptualAnalysis.abstractionLevel,
      cognitiveLoad: association.conceptualAnalysis.cognitiveLoad,
      abstractionAccuracy: this.calculateAbstractionAccuracy(association),
      conceptualDistance: this.calculateConceptualDistance(targetConcept, selectedSymbol.actualConcept)
    };

    this.cognitiveMetrics.abstractThinking.push(abstractMetric);

    // Análise de integração conceitual
    const integrationMetric = {
      timestamp: association.timestamp,
      concept: targetConcept,
      integrationComplexity: this.assessIntegrationComplexity(association),
      conceptualBridging: this.assessConceptualBridging(association),
      meaningCoherence: this.assessMeaningCoherence(association)
    };

    this.cognitiveMetrics.conceptualIntegration.push(integrationMetric);
  }

  analyzeMetaphoricalReasoning(association) {
    const { targetConcept, context } = association;
    
    const metaphorMetric = {
      timestamp: association.timestamp,
      concept: targetConcept,
      metaphorProvided: !!context.metaphoricalJustification,
      metaphorQuality: this.assessMetaphorQuality(association),
      analogicalThinking: this.assessAnalogicalThinking(association),
      metaphoricalMapping: this.assessMetaphoricalMapping(association),
      creativityScore: this.assessConceptualCreativity(association)
    };

    this.cognitiveMetrics.metaphoricalReasoning.push(metaphorMetric);
  }

  analyzeConceptualFlexibility(association) {
    const { targetConcept, selectedSymbol, isCorrect } = association;
    
    const flexibilityMetric = {
      timestamp: association.timestamp,
      concept: targetConcept,
      categoryFlexibility: this.assessCategoryFlexibility(association),
      perspectiveShifting: this.assessPerspectiveShifting(association),
      conceptualAdaptation: this.assessConceptualAdaptation(association),
      contextualSensitivity: this.assessContextualSensitivity(association)
    };

    this.cognitiveMetrics.conceptualFlexibility.push(flexibilityMetric);
  }

  analyzeSymbolicThinking(association) {
    const { selectedSymbol, targetConcept, isCorrect } = association;
    
    const symbolicMetric = {
      timestamp: association.timestamp,
      symbol: selectedSymbol.symbol,
      concept: targetConcept,
      symbolicMapping: isCorrect,
      symbolComplexity: this.calculateSymbolComplexity(selectedSymbol.symbol),
      representationalThinking: this.assessRepresentationalThinking(association),
      semanticDepth: this.assessSemanticDepth(association)
    };

    this.cognitiveMetrics.symbolicThinking.push(symbolicMetric);
  }

  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS
  // ========================================================================

  calculateAbstractionAccuracy(association) {
    const { isCorrect, targetConcept, selectedSymbol } = association;
    
    if (!isCorrect) return 0;
    
    const targetLevel = this.conceptualDomains[targetConcept]?.abstractLevel;
    const selectedLevel = this.conceptualDomains[selectedSymbol.actualConcept]?.abstractLevel;
    
    // Precisão baseada no nível de abstração correspondente
    const levelMap = { 'low': 1, 'medium': 2, 'high': 3, 'very_high': 4 };
    const targetLevelNum = levelMap[targetLevel] || 2;
    const selectedLevelNum = levelMap[selectedLevel] || 2;
    
    return Math.max(0, 1 - Math.abs(targetLevelNum - selectedLevelNum) * 0.25);
  }

  calculateConceptualDistance(targetConcept, actualConcept) {
    if (targetConcept === actualConcept) return 0;
    
    const targetDomain = this.conceptualDomains[targetConcept];
    const actualDomain = this.conceptualDomains[actualConcept];
    
    if (!targetDomain || !actualDomain) return 1.0;
    
    // Distância baseada em categoria, nível de abstração e propriedades
    const categoryDistance = targetDomain.category === actualDomain.category ? 0 : 0.4;
    
    const levelMap = { 'low': 1, 'medium': 2, 'high': 3, 'very_high': 4 };
    const targetLevel = levelMap[targetDomain.abstractLevel] || 2;
    const actualLevel = levelMap[actualDomain.abstractLevel] || 2;
    const levelDistance = Math.abs(targetLevel - actualLevel) * 0.15;
    
    const propertyOverlap = targetDomain.properties.filter(prop => 
      actualDomain.properties.includes(prop)
    ).length;
    const propertyDistance = (1 - propertyOverlap / Math.max(targetDomain.properties.length, actualDomain.properties.length)) * 0.3;
    
    const metaphorOverlap = targetDomain.metaphors.filter(metaphor => 
      actualDomain.metaphors.includes(metaphor)
    ).length;
    const metaphorDistance = (1 - metaphorOverlap / Math.max(targetDomain.metaphors.length, actualDomain.metaphors.length)) * 0.15;
    
    return Math.min(1.0, categoryDistance + levelDistance + propertyDistance + metaphorDistance);
  }

  assessIntegrationComplexity(association) {
    const { targetConcept, context } = association;
    const domain = this.conceptualDomains[targetConcept];
    
    if (!domain) return 0.5;
    
    let complexity = 0;
    
    // Complexidade baseada no nível de abstração
    const levelComplexity = {
      'low': 0.2,
      'medium': 0.5,
      'high': 0.8,
      'very_high': 1.0
    };
    complexity += levelComplexity[domain.abstractLevel] || 0.5;
    
    // Complexidade baseada na carga cognitiva
    const loadComplexity = {
      'low': 0.2,
      'medium': 0.5,
      'high': 0.8,
      'very_high': 1.0
    };
    complexity += loadComplexity[domain.cognitive_load] || 0.5;
    
    // Complexidade baseada no número de propriedades
    complexity += (domain.properties.length / 6) * 0.3;
    
    return Math.min(1.0, complexity / 2.3);
  }

  assessConceptualBridging(association) {
    const { context, targetConcept } = association;
    
    if (!context.conceptualPath || context.conceptualPath.length === 0) {
      return 0.3; // Score baixo se não há evidência de bridging
    }
    
    // Avaliar qualidade do caminho conceitual
    const pathLength = context.conceptualPath.length;
    const optimalLength = 3; // Caminho ideal: 2-4 passos
    
    const lengthScore = Math.max(0, 1 - Math.abs(pathLength - optimalLength) * 0.2);
    
    // Avaliar coerência conceitual do caminho
    const coherenceScore = this.assessPathCoherence(context.conceptualPath, targetConcept);
    
    return (lengthScore + coherenceScore) / 2;
  }

  assessMeaningCoherence(association) {
    const { isCorrect, context, targetConcept } = association;
    
    let coherence = isCorrect ? 0.6 : 0.2;
    
    // Se forneceu justificativa metafórica
    if (context.metaphoricalJustification) {
      const metaphorRelevance = this.assessMetaphorRelevance(
        context.metaphoricalJustification, 
        targetConcept
      );
      coherence += metaphorRelevance * 0.3;
    }
    
    // Se forneceu caminho conceitual
    if (context.conceptualPath && context.conceptualPath.length > 0) {
      coherence += 0.1;
    }
    
    return Math.min(1.0, coherence);
  }

  assessMetaphorQuality(association) {
    const { context, targetConcept } = association;
    
    if (!context.metaphoricalJustification) return 0;
    
    const justification = context.metaphoricalJustification.toLowerCase();
    const domain = this.conceptualDomains[targetConcept];
    
    if (!domain) return 0.3;
    
    let quality = 0;
    
    // Verificar se usa metáforas conhecidas do domínio
    const domainMetaphors = domain.metaphors;
    const usesKnownMetaphor = domainMetaphors.some(metaphor => 
      justification.includes(metaphor)
    );
    
    if (usesKnownMetaphor) {
      quality += 0.4;
    }
    
    // Verificar criatividade (uso de novas metáforas)
    const hasNovelMetaphor = !usesKnownMetaphor && justification.length > 10;
    if (hasNovelMetaphor) {
      quality += 0.3;
    }
    
    // Verificar coerência conceitual
    const conceptualWords = domain.concepts;
    const usesConceptualVocabulary = conceptualWords.some(concept => 
      justification.includes(concept)
    );
    
    if (usesConceptualVocabulary) {
      quality += 0.2;
    }
    
    // Verificar propriedades conceituais
    const properties = domain.properties;
    const usesProperties = properties.some(property => 
      justification.includes(property)
    );
    
    if (usesProperties) {
      quality += 0.1;
    }
    
    return Math.min(1.0, quality);
  }

  assessAnalogicalThinking(association) {
    const { context, isCorrect } = association;
    
    if (!context.metaphoricalJustification) return 0.3;
    
    const justification = context.metaphoricalJustification.toLowerCase();
    
    // Indicadores de pensamento analógico
    const analogicalIndicators = [
      'como', 'semelhante', 'parecido', 'igual', 'assim como',
      'lembra', 'representa', 'simboliza', 'reflete', 'espelha'
    ];
    
    const hasAnalogicalLanguage = analogicalIndicators.some(indicator => 
      justification.includes(indicator)
    );
    
    let analogicalScore = 0;
    
    if (hasAnalogicalLanguage) {
      analogicalScore += 0.4;
    }
    
    if (isCorrect) {
      analogicalScore += 0.3;
    }
    
    // Complexidade da analogia (baseada no comprimento e vocabulário)
    if (justification.length > 20) {
      analogicalScore += 0.2;
    }
    
    // Uso de estrutura comparativa
    const hasComparativeStructure = /\b(mais|menos|maior|menor|melhor|pior)\b/.test(justification);
    if (hasComparativeStructure) {
      analogicalScore += 0.1;
    }
    
    return Math.min(1.0, analogicalScore);
  }

  assessMetaphoricalMapping(association) {
    const { targetConcept, selectedSymbol, context, isCorrect } = association;
    
    if (!isCorrect) return 0;
    
    const domain = this.conceptualDomains[targetConcept];
    if (!domain) return 0.5;
    
    let mappingScore = 0.5; // Base score for correct association
    
    // Verificar se o símbolo está nas metáforas conhecidas
    const symbolIsKnownMetaphor = domain.symbols.includes(selectedSymbol.symbol);
    if (symbolIsKnownMetaphor) {
      mappingScore += 0.3;
    }
    
    // Se forneceu justificativa metafórica, avaliar qualidade do mapeamento
    if (context.metaphoricalJustification) {
      const metaphorQuality = this.assessMetaphorQuality(association);
      mappingScore += metaphorQuality * 0.2;
    }
    
    return Math.min(1.0, mappingScore);
  }

  assessConceptualCreativity(association) {
    const { context, targetConcept, selectedSymbol } = association;
    
    let creativity = 0;
    
    // Criatividade na justificativa
    if (context.metaphoricalJustification) {
      const domain = this.conceptualDomains[targetConcept];
      const usesNovelMetaphor = domain && !domain.metaphors.some(metaphor => 
        context.metaphoricalJustification.toLowerCase().includes(metaphor)
      );
      
      if (usesNovelMetaphor) {
        creativity += 0.4;
      }
      
      // Criatividade baseada na originalidade da explicação
      const isOriginal = context.metaphoricalJustification.length > 30;
      if (isOriginal) {
        creativity += 0.3;
      }
    }
    
    // Criatividade no caminho conceitual
    if (context.conceptualPath && context.conceptualPath.length > 3) {
      creativity += 0.2;
    }
    
    // Uso criativo de símbolos não convencionais
    const isUnconventionalSymbol = this.assessSymbolUnconventionality(selectedSymbol.symbol, targetConcept);
    if (isUnconventionalSymbol) {
      creativity += 0.1;
    }
    
    return Math.min(1.0, creativity);
  }

  assessCategoryFlexibility(association) {
    const recentAssociations = this.interactions.slice(-5);
    const categories = recentAssociations.map(a => 
      this.conceptualDomains[a.targetConcept]?.category
    ).filter(Boolean);
    
    const uniqueCategories = new Set(categories);
    
    // Flexibilidade baseada na variedade de categorias utilizadas
    return Math.min(1.0, uniqueCategories.size / Math.min(categories.length, 4));
  }

  assessPerspectiveShifting(association) {
    const { targetConcept, context } = association;
    
    let perspectiveScore = 0.5; // Score base
    
    // Se forneceu múltiplas perspectivas na justificativa
    if (context.metaphoricalJustification) {
      const justification = context.metaphoricalJustification.toLowerCase();
      const perspectiveIndicators = [
        'por outro lado', 'também pode', 'outra forma', 'perspectiva',
        'ponto de vista', 'ângulo', 'aspecto', 'lado'
      ];
      
      const hasMultiplePerspectives = perspectiveIndicators.some(indicator => 
        justification.includes(indicator)
      );
      
      if (hasMultiplePerspectives) {
        perspectiveScore += 0.3;
      }
    }
    
    // Se o conceito requer mudança de perspectiva
    const domain = this.conceptualDomains[targetConcept];
    if (domain && domain.properties.includes('subjective')) {
      perspectiveScore += 0.2;
    }
    
    return Math.min(1.0, perspectiveScore);
  }

  assessConceptualAdaptation(association) {
    const { targetConcept, context } = association;
    
    let adaptationScore = 0.4; // Score base
    
    // Adaptação baseada no nível de abstração requerido
    const requestedLevel = context.requestedAbstractionLevel;
    const conceptLevel = this.conceptualDomains[targetConcept]?.abstractLevel;
    
    const levelMatch = requestedLevel === conceptLevel;
    if (levelMatch) {
      adaptationScore += 0.3;
    }
    
    // Adaptação baseada na complexidade cognitiva
    const domain = this.conceptualDomains[targetConcept];
    if (domain && domain.cognitive_load === 'high' && context.metaphoricalJustification) {
      adaptationScore += 0.2;
    }
    
    // Adaptação ao contexto de dificuldade
    if (context.difficultyLevel === 'advanced' && context.conceptualPath) {
      adaptationScore += 0.1;
    }
    
    return Math.min(1.0, adaptationScore);
  }

  assessContextualSensitivity(association) {
    const { targetConcept, context, responseTime } = association;
    
    let sensitivity = 0.5;
    
    // Sensibilidade ao nível de dificuldade
    const domain = this.conceptualDomains[targetConcept];
    if (domain) {
      const expectedTime = this.getExpectedResponseTime(domain.cognitive_load);
      const timeAppropriate = Math.abs(responseTime - expectedTime) < 3000;
      
      if (timeAppropriate) {
        sensitivity += 0.2;
      }
    }
    
    // Sensibilidade ao nível de abstração requerido
    const providedAbstractionEvidence = context.metaphoricalJustification || context.conceptualPath;
    if (context.requestedAbstractionLevel !== 'low' && providedAbstractionEvidence) {
      sensitivity += 0.2;
    }
    
    // Sensibilidade às demandas cognitivas
    if (domain && domain.cognitive_load === 'very_high' && context.conceptualPath) {
      sensitivity += 0.1;
    }
    
    return Math.min(1.0, sensitivity);
  }

  calculateSymbolComplexity(symbol) {
    const complexityMap = {
      '⏰': 0.4, '📅': 0.3, '⌛': 0.6, '🕐': 0.2, '⏳': 0.5,
      '🕊️': 0.7, '🗽': 0.8, '🆓': 0.3, '🔓': 0.4, '🌅': 0.6,
      '🌱': 0.3, '📈': 0.5, '🌳': 0.4, '🏗️': 0.6, '💪': 0.3,
      '🔗': 0.4, '🌐': 0.7, '🤝': 0.5, '💞': 0.6, '🧩': 0.5,
      '⚖️': 0.6, '☯️': 0.8, '⚡': 0.5, '🎭': 0.7, '🌊': 0.4,
      '🦋': 0.8, '🔄': 0.5, '🌙': 0.6, '🔮': 0.9
    };

    return complexityMap[symbol] || 0.5;
  }

  assessRepresentationalThinking(association) {
    const { selectedSymbol, targetConcept, isCorrect } = association;
    
    let representationalScore = isCorrect ? 0.6 : 0.2;
    
    // Complexidade da representação simbólica
    const symbolComplexity = this.calculateSymbolComplexity(selectedSymbol.symbol);
    representationalScore += symbolComplexity * 0.2;
    
    // Capacidade de mapear conceito abstrato para símbolo concreto
    const domain = this.conceptualDomains[targetConcept];
    if (domain && domain.abstractLevel === 'very_high' && isCorrect) {
      representationalScore += 0.2;
    }
    
    return Math.min(1.0, representationalScore);
  }

  assessSemanticDepth(association) {
    const { context, targetConcept } = association;
    
    let depth = 0.3; // Score base
    
    // Profundidade baseada na justificativa
    if (context.metaphoricalJustification) {
      const justificationLength = context.metaphoricalJustification.length;
      depth += Math.min(0.3, justificationLength / 100);
      
      // Uso de vocabulário conceitual sofisticado
      const domain = this.conceptualDomains[targetConcept];
      if (domain) {
        const usesConceptualVocabulary = domain.concepts.some(concept => 
          context.metaphoricalJustification.toLowerCase().includes(concept)
        );
        
        if (usesConceptualVocabulary) {
          depth += 0.2;
        }
      }
    }
    
    // Profundidade baseada no caminho conceitual
    if (context.conceptualPath && context.conceptualPath.length > 2) {
      depth += 0.2;
    }
    
    return Math.min(1.0, depth);
  }

  assessPathCoherence(conceptualPath, targetConcept) {
    if (!conceptualPath || conceptualPath.length === 0) return 0;
    
    let coherence = 0.3; // Score base
    
    // Verificar se o caminho leva logicamente ao conceito alvo
    const lastStep = conceptualPath[conceptualPath.length - 1];
    const domain = this.conceptualDomains[targetConcept];
    
    if (domain) {
      // Verificar se o último passo está relacionado ao conceito
      const isRelated = domain.concepts.some(concept => 
        lastStep.toLowerCase().includes(concept) || concept.includes(lastStep.toLowerCase())
      );
      
      if (isRelated) {
        coherence += 0.4;
      }
      
      // Verificar progressão lógica
      const hasLogicalProgression = this.assessLogicalProgression(conceptualPath);
      coherence += hasLogicalProgression * 0.3;
    }
    
    return Math.min(1.0, coherence);
  }

  assessLogicalProgression(conceptualPath) {
    if (conceptualPath.length < 2) return 0.5;
    
    // Verificar se há progressão de concreto para abstrato ou similar
    let progressionScore = 0;
    
    for (let i = 0; i < conceptualPath.length - 1; i++) {
      const currentStep = conceptualPath[i];
      const nextStep = conceptualPath[i + 1];
      
      // Simples verificação de conexão semântica
      const hasConnection = this.assessSemanticConnection(currentStep, nextStep);
      if (hasConnection) {
        progressionScore += 1;
      }
    }
    
    return progressionScore / (conceptualPath.length - 1);
  }

  assessSemanticConnection(step1, step2) {
    // Verificação básica de conexão semântica
    const commonWords = step1.toLowerCase().split(' ').filter(word => 
      step2.toLowerCase().includes(word) && word.length > 3
    );
    
    return commonWords.length > 0 || step1.length + step2.length > 10;
  }

  assessMetaphorRelevance(metaphor, targetConcept) {
    const domain = this.conceptualDomains[targetConcept];
    if (!domain) return 0.3;
    
    const metaphorLower = metaphor.toLowerCase();
    
    let relevance = 0;
    
    // Verificar metáforas conhecidas
    const usesKnownMetaphor = domain.metaphors.some(domainMetaphor => 
      metaphorLower.includes(domainMetaphor)
    );
    
    if (usesKnownMetaphor) {
      relevance += 0.5;
    }
    
    // Verificar conceitos relacionados
    const usesRelatedConcepts = domain.concepts.some(concept => 
      metaphorLower.includes(concept)
    );
    
    if (usesRelatedConcepts) {
      relevance += 0.3;
    }
    
    // Verificar propriedades
    const usesProperties = domain.properties.some(property => 
      metaphorLower.includes(property)
    );
    
    if (usesProperties) {
      relevance += 0.2;
    }
    
    return Math.min(1.0, relevance);
  }

  assessSymbolUnconventionality(symbol, targetConcept) {
    const domain = this.conceptualDomains[targetConcept];
    if (!domain) return false;
    
    // Símbolo é não convencional se não está na lista padrão do domínio
    return !domain.symbols.includes(symbol);
  }

  getExpectedResponseTime(cognitiveLoad) {
    const timeMap = {
      'low': 4000,
      'medium': 6000,
      'high': 8000,
      'very_high': 10000
    };
    
    return timeMap[cognitiveLoad] || 6000;
  }

  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================

  updateSessionMetrics(association) {
    const { isCorrect, responseTime, targetConcept } = association;

    this.sessionData.totalConceptualAssociations++;
    
    if (isCorrect) {
      this.sessionData.correctConceptualAssociations++;
    } else {
      this.sessionData.incorrectConceptualAssociations++;
      
      // Rastrear confusões conceituais
      const actualConcept = association.selectedSymbol.actualConcept;
      const confusionKey = `${targetConcept}->${actualConcept}`;
      
      if (!this.sessionData.conceptualConfusions[confusionKey]) {
        this.sessionData.conceptualConfusions[confusionKey] = 0;
      }
      this.sessionData.conceptualConfusions[confusionKey]++;
    }

    // Atualizar precisão por conceito
    if (!this.sessionData.conceptsByAccuracy[targetConcept]) {
      this.sessionData.conceptsByAccuracy[targetConcept] = { total: 0, correct: 0 };
    }
    
    this.sessionData.conceptsByAccuracy[targetConcept].total++;
    if (isCorrect) {
      this.sessionData.conceptsByAccuracy[targetConcept].correct++;
    }

    // Atualizar tempo médio
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageConceptualProcessingTime = totalTime / this.interactions.length;

    // Calcular score de abstração
    this.updateAbstractionScore();
    
    // Calcular score metafórico
    this.updateMetaphoricalScore();
  }

  updateAbstractionScore() {
    const abstractMetrics = this.cognitiveMetrics.abstractThinking;
    
    if (abstractMetrics.length === 0) {
      this.sessionData.abstractionScore = 0;
      return;
    }

    const totalAccuracy = abstractMetrics.reduce((sum, metric) => 
      sum + metric.abstractionAccuracy, 0
    );

    this.sessionData.abstractionScore = totalAccuracy / abstractMetrics.length;
  }

  updateMetaphoricalScore() {
    const metaphorMetrics = this.cognitiveMetrics.metaphoricalReasoning;
    
    if (metaphorMetrics.length === 0) {
      this.sessionData.metaphoricalScore = 0;
      return;
    }

    const totalQuality = metaphorMetrics.reduce((sum, metric) => 
      sum + metric.metaphorQuality, 0
    );
    
    const totalAnalogical = metaphorMetrics.reduce((sum, metric) => 
      sum + metric.analogicalThinking, 0
    );

    this.sessionData.metaphoricalScore = (
      (totalQuality / metaphorMetrics.length) * 0.6 +
      (totalAnalogical / metaphorMetrics.length) * 0.4
    );
  }

  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================

  generateCognitiveReport() {
    return {
      abstractThinking: this.analyzeAbstractThinkingReport(),
      metaphoricalReasoning: this.analyzeMetaphoricalReasoningReport(),
      conceptualFlexibility: this.analyzeConceptualFlexibilityReport(),
      symbolicThinking: this.analyzeSymbolicThinkingReport(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }

  analyzeAbstractThinkingReport() {
    return {
      overallAccuracy: this.sessionData.correctConceptualAssociations / this.sessionData.totalConceptualAssociations,
      abstractionScore: this.sessionData.abstractionScore,
      averageProcessingTime: this.sessionData.averageConceptualProcessingTime,
      abstractionLevelPerformance: this.assessAbstractionLevelPerformance(),
      conceptualDistance: this.calculateAverageMetric(this.cognitiveMetrics.abstractThinking, 'conceptualDistance')
    };
  }

  analyzeMetaphoricalReasoningReport() {
    return {
      metaphoricalScore: this.sessionData.metaphoricalScore,
      metaphorQuality: this.calculateAverageMetric(this.cognitiveMetrics.metaphoricalReasoning, 'metaphorQuality'),
      analogicalThinking: this.calculateAverageMetric(this.cognitiveMetrics.metaphoricalReasoning, 'analogicalThinking'),
      creativityScore: this.calculateAverageMetric(this.cognitiveMetrics.metaphoricalReasoning, 'creativityScore'),
      metaphorUsageRate: this.calculateMetaphorUsageRate()
    };
  }

  analyzeConceptualFlexibilityReport() {
    return {
      categoryFlexibility: this.calculateAverageMetric(this.cognitiveMetrics.conceptualFlexibility, 'categoryFlexibility'),
      perspectiveShifting: this.calculateAverageMetric(this.cognitiveMetrics.conceptualFlexibility, 'perspectiveShifting'),
      conceptualAdaptation: this.calculateAverageMetric(this.cognitiveMetrics.conceptualFlexibility, 'conceptualAdaptation'),
      contextualSensitivity: this.calculateAverageMetric(this.cognitiveMetrics.conceptualFlexibility, 'contextualSensitivity')
    };
  }

  analyzeSymbolicThinkingReport() {
    return {
      symbolicMapping: this.calculateAverageMetric(this.cognitiveMetrics.symbolicThinking, 'symbolicMapping'),
      representationalThinking: this.calculateAverageMetric(this.cognitiveMetrics.symbolicThinking, 'representationalThinking'),
      semanticDepth: this.calculateAverageMetric(this.cognitiveMetrics.symbolicThinking, 'semanticDepth'),
      averageSymbolComplexity: this.calculateAverageMetric(this.cognitiveMetrics.symbolicThinking, 'symbolComplexity')
    };
  }

  assessAbstractionLevelPerformance() {
    const levelPerformance = {};
    
    this.interactions.forEach(interaction => {
      const concept = interaction.targetConcept;
      const level = this.conceptualDomains[concept]?.abstractLevel;
      
      if (!levelPerformance[level]) {
        levelPerformance[level] = { total: 0, correct: 0 };
      }
      
      levelPerformance[level].total++;
      if (interaction.isCorrect) {
        levelPerformance[level].correct++;
      }
    });

    Object.keys(levelPerformance).forEach(level => {
      const data = levelPerformance[level];
      levelPerformance[level].accuracy = data.correct / data.total;
    });

    return levelPerformance;
  }

  calculateMetaphorUsageRate() {
    const metaphorUsage = this.cognitiveMetrics.metaphoricalReasoning.filter(metric => 
      metric.metaphorProvided
    ).length;
    
    return this.interactions.length > 0 ? metaphorUsage / this.interactions.length : 0;
  }

  generateAdaptiveRecommendations() {
    const recommendations = [];
    
    // Análise por conceito
    const conceptPerformance = {};
    Object.entries(this.sessionData.conceptsByAccuracy).forEach(([concept, data]) => {
      conceptPerformance[concept] = data.correct / data.total;
    });

    const weakConcepts = Object.entries(conceptPerformance)
      .filter(([concept, accuracy]) => accuracy < 0.6)
      .map(([concept]) => concept);

    if (weakConcepts.length > 0) {
      recommendations.push({
        type: 'conceptual_training',
        recommendation: `Reforçar conceitos: ${weakConcepts.join(', ')}`,
        confidence: 0.8,
        details: {
          concepts: weakConcepts,
          suggestedActivities: ['metaphor_exploration', 'concept_mapping', 'analogical_reasoning']
        }
      });
    }

    // Análise de pensamento metafórico
    if (this.sessionData.metaphoricalScore < 0.5) {
      recommendations.push({
        type: 'metaphorical_training',
        recommendation: 'Desenvolvimento de raciocínio metafórico',
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.metaphoricalScore,
          suggestedActivities: ['metaphor_creation', 'analogical_mapping', 'creative_expression']
        }
      });
    }

    // Análise de abstração
    if (this.sessionData.abstractionScore < 0.6) {
      recommendations.push({
        type: 'abstraction_training',
        recommendation: 'Exercícios de pensamento abstrato',
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.abstractionScore,
          suggestedActivities: ['abstraction_exercises', 'conceptual_hierarchies', 'symbolic_reasoning']
        }
      });
    }

    return recommendations;
  }

  getActivityScore() {
    if (this.sessionData.totalConceptualAssociations === 0) return 0;
    
    const accuracy = this.sessionData.correctConceptualAssociations / this.sessionData.totalConceptualAssociations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageConceptualProcessingTime - 6000) / 12000);
    const abstractionFactor = this.sessionData.abstractionScore;
    const metaphoricalFactor = this.sessionData.metaphoricalScore;
    
    return Math.round(accuracy * speedFactor * abstractionFactor * metaphoricalFactor * 1000);
  }
}

export default ConceptualAssociationCollector;
