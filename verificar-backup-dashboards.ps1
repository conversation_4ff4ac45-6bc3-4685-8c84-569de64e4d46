# Verificar e testar o suporte a backup em todos os dashboards premium
# <PERSON>ript para o Portal Betina V3

Write-Host "=====================================================" -ForegroundColor Yellow
Write-Host "    VERIFICAÇÃO DE SUPORTE A BACKUP NOS DASHBOARDS    " -ForegroundColor Yellow
Write-Host "=====================================================" -ForegroundColor Yellow
Write-Host ""

# Verificar se o Node.js está instalado
try {
    $nodeVersion = node -v
    Write-Host "✓ Node.js encontrado: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js não encontrado. Por favor, instale o Node.js para continuar." -ForegroundColor Red
    exit 1
}

# Instalar dependências necessárias para o script de teste
Write-Host ""
Write-Host "Instalando dependências necessárias..." -ForegroundColor Cyan
npm install chalk --save-dev

# Executar o script de verificação de suporte
Write-Host ""
Write-Host "Executando verificação de suporte a backup..." -ForegroundColor Cyan
node verificar-suporte-backup-dashboards.js

# Verificar se houve falhas
$exitCode = $LASTEXITCODE
if ($exitCode -ne 0) {
    Write-Host ""
    Write-Host "⚠️ A verificação encontrou problemas. Corrija os problemas indicados e execute este script novamente." -ForegroundColor Yellow
    exit $exitCode
}

# Opção para executar o teste do arquivo de backup
Write-Host ""
Write-Host "Deseja testar um arquivo de backup real? (s/n)" -ForegroundColor Cyan
$testBackup = Read-Host

if ($testBackup -eq "s") {
    # Perguntar pelo caminho do arquivo de backup
    Write-Host ""
    Write-Host "Digite o caminho completo para o arquivo de backup JSON:" -ForegroundColor Cyan
    $backupPath = Read-Host
    
    if (Test-Path $backupPath) {
        Write-Host ""
        Write-Host "✓ Arquivo encontrado. Verificando formato..." -ForegroundColor Green
        
        # Criar um script temporário para testar o backup
        $tempScript = @"
const fs = require('fs');
const path = require('path');

// Carregar o arquivo de backup
try {
    const backupData = JSON.parse(fs.readFileSync('$($backupPath.Replace("\", "\\"))'));
    
    // Verificar formato do backup
    if (!backupData || !backupData.version || !backupData.data) {
        console.log('ERRO: Formato de backup inválido');
        process.exit(1);
    }
    
    // Exibir informações do backup
    console.log('Informações do Backup:');
    console.log('- Versão: ' + backupData.version);
    console.log('- Data de Exportação: ' + backupData.exportDate);
    console.log('- ID do Usuário: ' + backupData.userId);
    
    // Verificar se há erro de servidor
    if (backupData.metadata && backupData.metadata.serverError) {
        console.log('- Contém Erro de Servidor: SIM');
        console.log('- Mensagem de Erro: ' + backupData.metadata.serverError.message);
    } else {
        console.log('- Contém Erro de Servidor: NÃO');
    }
    
    // Verificar dados
    const hasGameData = backupData.data && 
                         backupData.data.gameMetrics && 
                         Object.keys(backupData.data.gameMetrics).length > 0;
    
    const hasUserData = backupData.data && 
                         backupData.data.userDetails && 
                         Object.keys(backupData.data.userDetails).length > 0;
    
    const hasSessionData = backupData.data && 
                           backupData.data.sessionData && 
                           Object.keys(backupData.data.sessionData).length > 0;
    
    console.log('- Contém Dados de Jogos: ' + (hasGameData ? 'SIM' : 'NÃO'));
    console.log('- Contém Dados de Usuário: ' + (hasUserData ? 'SIM' : 'NÃO'));
    console.log('- Contém Dados de Sessão: ' + (hasSessionData ? 'SIM' : 'NÃO'));
    
    // Verificar se há dados suficientes para os dashboards
    if (hasGameData || hasUserData || hasSessionData) {
        console.log('SUCESSO: Backup válido para uso nos dashboards');
        process.exit(0);
    } else {
        console.log('AVISO: Backup válido mas sem dados suficientes para visualização completa');
        process.exit(2);
    }
    
} catch (error) {
    console.log('ERRO: ' + error.message);
    process.exit(1);
}
"@
    
        # Salvar e executar o script temporário
        $tempScriptPath = ".\temp-test-backup.js"
        $tempScript | Out-File -FilePath $tempScriptPath -Encoding UTF8
        
        $result = node $tempScriptPath
        Remove-Item $tempScriptPath
        
        # Verificar o resultado
        if ($result -contains "SUCESSO") {
            Write-Host ""
            Write-Host "✅ O arquivo de backup é válido e pronto para uso nos dashboards." -ForegroundColor Green
            
            # Exibir resumo das informações
            foreach ($line in $result) {
                if ($line -match "^-") {
                    if ($line -match "NÃO$") {
                        Write-Host $line -ForegroundColor Yellow
                    } else {
                        Write-Host $line -ForegroundColor Cyan
                    }
                }
            }
        } elseif ($result -contains "AVISO") {
            Write-Host ""
            Write-Host "⚠️ O arquivo de backup é válido mas pode ter dados incompletos." -ForegroundColor Yellow
            
            # Exibir resumo das informações
            foreach ($line in $result) {
                if ($line -match "^-") {
                    if ($line -match "NÃO$") {
                        Write-Host $line -ForegroundColor Yellow
                    } else {
                        Write-Host $line -ForegroundColor Cyan
                    }
                }
            }
        } else {
            Write-Host ""
            Write-Host "❌ O arquivo de backup não é válido:" -ForegroundColor Red
            foreach ($line in $result) {
                if ($line -match "^ERRO") {
                    Write-Host $line -ForegroundColor Red
                }
            }
        }
    } else {
        Write-Host ""
        Write-Host "❌ Arquivo não encontrado: $backupPath" -ForegroundColor Red
    }
}

# Conclusão
Write-Host ""
Write-Host "=====================================================" -ForegroundColor Yellow
Write-Host "    VERIFICAÇÃO DE SUPORTE A BACKUP CONCLUÍDA        " -ForegroundColor Yellow
Write-Host "=====================================================" -ForegroundColor Yellow

# Próximos passos
Write-Host ""
Write-Host "Próximos passos:" -ForegroundColor Cyan
Write-Host "1. Verifique se todos os dashboards premium estão importando os utilitários de backup"
Write-Host "2. Execute o teste manual abrindo cada dashboard após importar um backup"
Write-Host "3. Verifique se os avisos de backup são exibidos corretamente em cada dashboard"
Write-Host ""
