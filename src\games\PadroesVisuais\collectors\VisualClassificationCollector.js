/**
 * @file VisualClassificationCollector.js
 * @description Coletor especializado para análise de classificação visual
 * @version 3.0.0
 */

export class VisualClassificationCollector {
  constructor() {
    this.name = 'VisualClassificationCollector';
    this.version = '3.0.0';
    this.description = 'Analisa habilidades de classificação e categorização visual';
  }

  async collect(gameState) {
    try {
      const elements = gameState.elementsToClassify || [];
      const playerClassification = gameState.playerClassification || {};
      const correctClassification = gameState.correctClassification || {};
      const classificationCriteria = gameState.classificationCriteria || [];

      return {
        // Métricas de classificação
        classificationAccuracy: this.calculateClassificationAccuracy(correctClassification, playerClassification),
        categoryConsistency: this.assessCategoryConsistency(playerClassification, classificationCriteria),
        criteriaIdentificationScore: this.assessCriteriaIdentification(playerClassification, correctClassification),
        abstractionLevel: this.evaluateAbstractionLevel(classificationCriteria, gameState.difficulty),
        
        // Análise de estratégias cognitivas
        classificationStrategy: this.analyzeClassificationStrategy(gameState.classificationHistory || []),
        categoryFormationAbility: this.assessCategoryFormation(playerClassification),
        conceptualFlexibility: this.evaluateConceptualFlexibility(gameState.sessionAttempts || []),
        
        // Processamento visual
        visualDiscriminationIndex: this.assessVisualDiscrimination(elements, playerClassification),
        perceptualOrganizationScore: this.evaluatePerceptualOrganization(playerClassification),
        featureDetectionAccuracy: this.assessFeatureDetection(elements, playerClassification, correctClassification),
        
        // Funções executivas
        categoricalThinkingIndex: this.assessCategoricalThinking(playerClassification),
        ruleApplicationConsistency: this.evaluateRuleApplication(gameState.classificationHistory || []),
        inhibitionOfIncorrectResponses: this.assessResponseInhibition(gameState),
        
        timestamp: Date.now(),
        sessionId: gameState.sessionId || 'unknown'
      };
    } catch (error) {
      console.error('Erro no VisualClassificationCollector:', error);
      return null;
    }
  }

  calculateClassificationAccuracy(correct, player) {
    if (!Object.keys(correct).length || !Object.keys(player).length) return 0;
    
    let correctClassifications = 0;
    let totalElements = 0;
    
    // Verifica cada elemento classificado
    for (const elementId in correct) {
      totalElements++;
      const correctCategory = correct[elementId];
      const playerCategory = player[elementId];
      
      if (correctCategory === playerCategory) {
        correctClassifications++;
      }
    }
    
    // Penaliza elementos não classificados
    const unclassifiedElements = Object.keys(correct).length - Object.keys(player).length;
    const penaltyScore = Math.max(0, unclassifiedElements) * 0.1;
    
    const accuracy = totalElements > 0 ? (correctClassifications / totalElements) * 100 : 0;
    return Math.max(0, accuracy - penaltyScore);
  }

  assessCategoryConsistency(playerClassification, criteria) {
    if (!Object.keys(playerClassification).length || !criteria.length) return 50;
    
    const categories = this.extractCategories(playerClassification);
    let consistencyScore = 0;
    
    for (const category of categories) {
      const elementsInCategory = this.getElementsInCategory(playerClassification, category);
      const consistency = this.calculateCategoryConsistency(elementsInCategory, criteria);
      consistencyScore += consistency;
    }
    
    return categories.length > 0 ? consistencyScore / categories.length : 0;
  }

  extractCategories(classification) {
    const categories = new Set(Object.values(classification));
    return Array.from(categories);
  }

  getElementsInCategory(classification, category) {
    const elements = [];
    for (const [elementId, elementCategory] of Object.entries(classification)) {
      if (elementCategory === category) {
        elements.push(elementId);
      }
    }
    return elements;
  }

  calculateCategoryConsistency(elementsInCategory, criteria) {
    if (elementsInCategory.length < 2) return 100;
    
    // Analisa se os elementos na categoria compartilham características
    let sharedFeatures = 0;
    let totalComparisons = 0;
    
    for (let i = 0; i < elementsInCategory.length - 1; i++) {
      for (let j = i + 1; j < elementsInCategory.length; j++) {
        const element1 = elementsInCategory[i];
        const element2 = elementsInCategory[j];
        
        const similarity = this.calculateElementSimilarity(element1, element2, criteria);
        sharedFeatures += similarity;
        totalComparisons++;
      }
    }
    
    return totalComparisons > 0 ? (sharedFeatures / totalComparisons) * 100 : 100;
  }

  calculateElementSimilarity(element1, element2, criteria) {
    // Calcula similaridade baseada nos critérios disponíveis
    let similarityScore = 0;
    let criteriaCount = 0;
    
    for (const criterion of criteria) {
      if (this.elementsShareCriterion(element1, element2, criterion)) {
        similarityScore++;
      }
      criteriaCount++;
    }
    
    return criteriaCount > 0 ? similarityScore / criteriaCount : 0;
  }

  elementsShareCriterion(element1, element2, criterion) {
    // Verifica se dois elementos compartilham um critério específico
    switch (criterion.type) {
      case 'shape':
        return element1.shape === element2.shape;
      case 'color':
        return element1.color === element2.color;
      case 'size':
        return element1.size === element2.size;
      case 'pattern':
        return element1.pattern === element2.pattern;
      case 'orientation':
        return element1.orientation === element2.orientation;
      default:
        return false;
    }
  }

  assessCriteriaIdentification(playerClassification, correctClassification) {
    // Avalia se o jogador identificou corretamente os critérios de classificação
    const playerCriteria = this.identifyImpliedCriteria(playerClassification);
    const correctCriteria = this.identifyImpliedCriteria(correctClassification);
    
    const criteriaOverlap = this.calculateCriteriaOverlap(playerCriteria, correctCriteria);
    return criteriaOverlap * 100;
  }

  identifyImpliedCriteria(classification) {
    const categories = this.extractCategories(classification);
    const impliedCriteria = new Set();
    
    for (const category of categories) {
      const elementsInCategory = this.getElementsInCategory(classification, category);
      const categoryFeatures = this.extractCommonFeatures(elementsInCategory);
      
      categoryFeatures.forEach(feature => impliedCriteria.add(feature));
    }
    
    return Array.from(impliedCriteria);
  }

  extractCommonFeatures(elements) {
    if (elements.length < 2) return [];
    
    const commonFeatures = [];
    const featureTypes = ['shape', 'color', 'size', 'pattern', 'orientation'];
    
    for (const featureType of featureTypes) {
      const values = elements.map(element => element[featureType]).filter(val => val);
      const uniqueValues = new Set(values);
      
      if (uniqueValues.size === 1) {
        commonFeatures.push(featureType);
      }
    }
    
    return commonFeatures;
  }

  calculateCriteriaOverlap(criteria1, criteria2) {
    if (criteria1.length === 0 && criteria2.length === 0) return 1;
    if (criteria1.length === 0 || criteria2.length === 0) return 0;
    
    const intersection = criteria1.filter(criterion => criteria2.includes(criterion));
    const union = [...new Set([...criteria1, ...criteria2])];
    
    return intersection.length / union.length;
  }

  evaluateAbstractionLevel(criteria, difficulty) {
    // Avalia nível de abstração baseado na complexidade dos critérios
    const complexityScores = {
      shape: 1,
      color: 1,
      size: 2,
      pattern: 3,
      orientation: 3,
      function: 4,
      conceptual: 5
    };
    
    const totalComplexity = criteria.reduce((sum, criterion) => {
      return sum + (complexityScores[criterion.type] || 1);
    }, 0);
    
    const difficultyMultipliers = {
      easy: 0.5,
      medium: 0.8,
      hard: 1.2
    };
    
    const abstractionScore = totalComplexity * (difficultyMultipliers[difficulty] || 1.0);
    return Math.min(100, abstractionScore * 10);
  }

  analyzeClassificationStrategy(classificationHistory) {
    if (!classificationHistory.length) return 'no_strategy';
    
    const strategyMetrics = {
      systematic: this.assessSystematicApproach(classificationHistory),
      trialError: this.assessTrialErrorApproach(classificationHistory),
      hypothesis: this.assessHypothesisTestingApproach(classificationHistory),
      random: this.assessRandomApproach(classificationHistory)
    };
    
    // Retorna a estratégia com maior score
    const maxStrategy = Object.entries(strategyMetrics).reduce((max, [strategy, score]) => 
      score > max.score ? { strategy, score } : max, { strategy: 'unknown', score: 0 });
    
    return maxStrategy.strategy;
  }

  assessSystematicApproach(history) {
    // Verifica se há abordagem sistemática (processamento sequencial)
    if (history.length < 3) return 0;
    
    let sequentialProcessing = 0;
    for (let i = 1; i < history.length; i++) {
      const currentElement = history[i].elementId;
      const previousElement = history[i - 1].elementId;
      
      // Verifica se elementos foram processados em ordem
      if (this.isSequentialOrder(currentElement, previousElement)) {
        sequentialProcessing++;
      }
    }
    
    return sequentialProcessing / (history.length - 1);
  }

  isSequentialOrder(current, previous) {
    // Verifica se há ordem sequencial nos IDs dos elementos
    const currentNum = parseInt(current.replace(/\D/g, ''));
    const previousNum = parseInt(previous.replace(/\D/g, ''));
    
    return !isNaN(currentNum) && !isNaN(previousNum) && currentNum === previousNum + 1;
  }

  assessTrialErrorApproach(history) {
    // Verifica se há padrão de tentativa e erro
    const corrections = history.filter(entry => entry.action === 'reclassify').length;
    const totalActions = history.length;
    
    return totalActions > 0 ? corrections / totalActions : 0;
  }

  assessHypothesisTestingApproach(history) {
    // Verifica se há teste de hipóteses (pausas para análise)
    if (history.length < 3) return 0;
    
    const analysisTime = history.map(entry => entry.thinkingTime || 0);
    const meanThinkingTime = analysisTime.reduce((sum, time) => sum + time, 0) / analysisTime.length;
    
    // Mais tempo de análise = mais teste de hipóteses
    return Math.min(1, meanThinkingTime / 5000); // 5 segundos como baseline
  }

  assessRandomApproach(history) {
    // Verifica padrão aleatório de classificação
    if (history.length < 4) return 0;
    
    const categoryChanges = this.countCategoryChanges(history);
    const maxPossibleChanges = history.length - 1;
    
    // Muitas mudanças de categoria = abordagem aleatória
    return maxPossibleChanges > 0 ? categoryChanges / maxPossibleChanges : 0;
  }

  countCategoryChanges(history) {
    let changes = 0;
    for (let i = 1; i < history.length; i++) {
      if (history[i].category !== history[i - 1].category) {
        changes++;
      }
    }
    return changes;
  }

  assessCategoryFormation(playerClassification) {
    // Avalia habilidade de formar categorias coerentes
    const categories = this.extractCategories(playerClassification);
    
    if (categories.length === 0) return 0;
    
    let categoryQualityScore = 0;
    
    for (const category of categories) {
      const elementsInCategory = this.getElementsInCategory(playerClassification, category);
      const categorySize = elementsInCategory.length;
      const categoryCoherence = this.assessCategoryCoherence(elementsInCategory);
      
      // Penaliza categorias muito pequenas ou muito grandes
      const sizeScore = this.evaluateCategorySize(categorySize, Object.keys(playerClassification).length);
      const categoryScore = (categoryCoherence + sizeScore) / 2;
      
      categoryQualityScore += categoryScore;
    }
    
    return categoryQualityScore / categories.length;
  }

  assessCategoryCoherence(elements) {
    // Avalia coerência interna da categoria
    if (elements.length < 2) return 50;
    
    const featureConsistency = this.calculateFeatureConsistency(elements);
    return featureConsistency * 100;
  }

  calculateFeatureConsistency(elements) {
    // Calcula consistência de características dentro da categoria
    const features = ['shape', 'color', 'size', 'pattern'];
    let consistencyScore = 0;
    
    for (const feature of features) {
      const values = elements.map(element => element[feature]).filter(val => val);
      const uniqueValues = new Set(values);
      
      // Maior consistência = menos valores únicos
      const featureConsistency = values.length > 0 ? 1 - (uniqueValues.size - 1) / values.length : 0;
      consistencyScore += Math.max(0, featureConsistency);
    }
    
    return consistencyScore / features.length;
  }

  evaluateCategorySize(categorySize, totalElements) {
    // Avalia se o tamanho da categoria é apropriado
    const ratio = categorySize / totalElements;
    
    // Categorias ideais têm entre 20% e 50% dos elementos
    if (ratio >= 0.2 && ratio <= 0.5) {
      return 100;
    } else if (ratio < 0.1 || ratio > 0.8) {
      return 30; // Categorias muito pequenas ou muito grandes
    } else {
      return 70; // Tamanho aceitável
    }
  }

  evaluateConceptualFlexibility(sessionAttempts) {
    if (sessionAttempts.length < 2) return 50;
    
    // Verifica se houve adaptação em diferentes tentativas
    const strategies = sessionAttempts.map(attempt => attempt.strategy || 'unknown');
    const uniqueStrategies = new Set(strategies);
    
    const flexibilityScore = (uniqueStrategies.size / sessionAttempts.length) * 100;
    return Math.min(100, flexibilityScore);
  }

  assessVisualDiscrimination(elements, playerClassification) {
    // Avalia habilidade de discriminação visual
    if (!elements.length || !Object.keys(playerClassification).length) return 50;
    
    const discriminationTasks = this.identifyDiscriminationTasks(elements);
    let discriminationScore = 0;
    
    for (const task of discriminationTasks) {
      const taskScore = this.evaluateDiscriminationTask(task, playerClassification);
      discriminationScore += taskScore;
    }
    
    return discriminationTasks.length > 0 ? discriminationScore / discriminationTasks.length : 50;
  }

  identifyDiscriminationTasks(elements) {
    // Identifica tarefas de discriminação baseadas na similaridade dos elementos
    const tasks = [];
    
    for (let i = 0; i < elements.length - 1; i++) {
      for (let j = i + 1; j < elements.length; j++) {
        const similarity = this.calculateVisualSimilarity(elements[i], elements[j]);
        
        if (similarity > 0.7) { // Elementos similares requerem discriminação
          tasks.push({
            element1: elements[i],
            element2: elements[j],
            difficulty: similarity
          });
        }
      }
    }
    
    return tasks;
  }

  calculateVisualSimilarity(element1, element2) {
    let similarity = 0;
    const features = ['shape', 'color', 'size', 'pattern'];
    
    for (const feature of features) {
      if (element1[feature] === element2[feature]) {
        similarity += 0.25;
      }
    }
    
    return similarity;
  }

  evaluateDiscriminationTask(task, playerClassification) {
    // Avalia se o jogador discriminou corretamente elementos similares
    const category1 = playerClassification[task.element1.id];
    const category2 = playerClassification[task.element2.id];
    
    if (category1 && category2) {
      // Se elementos similares foram colocados em categorias diferentes,
      // indica boa discriminação
      return category1 !== category2 ? 100 : 30;
    }
    
    return 50; // Elementos não classificados
  }

  evaluatePerceptualOrganization(playerClassification) {
    // Avalia organização perceptual
    const categories = this.extractCategories(playerClassification);
    
    if (categories.length === 0) return 0;
    
    let organizationScore = 0;
    
    for (const category of categories) {
      const elements = this.getElementsInCategory(playerClassification, category);
      const categoryOrganization = this.assessCategoryOrganization(elements);
      organizationScore += categoryOrganization;
    }
    
    return organizationScore / categories.length;
  }

  assessCategoryOrganization(elements) {
    // Avalia organização dentro da categoria
    if (elements.length < 2) return 50;
    
    const organizationMetrics = {
      featureGrouping: this.assessFeatureGrouping(elements),
      hierarchicalStructure: this.assessHierarchicalStructure(elements),
      spatialOrganization: this.assessSpatialOrganization(elements)
    };
    
    return (
      organizationMetrics.featureGrouping * 0.4 +
      organizationMetrics.hierarchicalStructure * 0.3 +
      organizationMetrics.spatialOrganization * 0.3
    );
  }

  assessFeatureGrouping(elements) {
    // Avalia agrupamento por características
    const featureGroups = this.groupByFeatures(elements);
    const groupSizes = Object.values(featureGroups).map(group => group.length);
    
    // Grupos mais equilibrados = melhor organização
    const meanSize = groupSizes.reduce((sum, size) => sum + size, 0) / groupSizes.length;
    const variance = groupSizes.reduce((sum, size) => sum + Math.pow(size - meanSize, 2), 0) / groupSizes.length;
    
    return Math.max(0, 100 - variance * 10);
  }

  groupByFeatures(elements) {
    const groups = {};
    
    for (const element of elements) {
      const key = `${element.shape}_${element.color}`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(element);
    }
    
    return groups;
  }

  assessHierarchicalStructure(elements) {
    // Avalia estrutura hierárquica (subcategorias)
    const subCategories = this.identifySubCategories(elements);
    
    if (subCategories.length <= 1) return 30;
    if (subCategories.length > elements.length / 2) return 50; // Muitas subcategorias
    
    return 100; // Número ideal de subcategorias
  }

  identifySubCategories(elements) {
    // Identifica possíveis subcategorias baseadas em características secundárias
    const subCategories = new Set();
    
    for (const element of elements) {
      const subCategoryKey = `${element.size}_${element.pattern}`;
      subCategories.add(subCategoryKey);
    }
    
    return Array.from(subCategories);
  }

  assessSpatialOrganization(elements) {
    // Avalia organização espacial (se relevante)
    if (!elements[0]?.position) return 50;
    
    const positions = elements.map(element => element.position).filter(pos => pos);
    if (positions.length < 2) return 50;
    
    // Verifica se elementos estão agrupados espacialmente
    const spatialClusters = this.identifySpatialClusters(positions);
    const clusteringQuality = this.evaluateClusteringQuality(spatialClusters, positions.length);
    
    return clusteringQuality;
  }

  identifySpatialClusters(positions) {
    // Algoritmo simples de clustering espacial
    const clusters = [];
    const visited = new Set();
    
    for (let i = 0; i < positions.length; i++) {
      if (visited.has(i)) continue;
      
      const cluster = [i];
      visited.add(i);
      
      for (let j = i + 1; j < positions.length; j++) {
        if (visited.has(j)) continue;
        
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) +
          Math.pow(positions[j].y - positions[i].y, 2)
        );
        
        if (distance < 100) { // Distância para considerar mesmo cluster
          cluster.push(j);
          visited.add(j);
        }
      }
      
      clusters.push(cluster);
    }
    
    return clusters;
  }

  evaluateClusteringQuality(clusters, totalPositions) {
    // Avalia qualidade do clustering espacial
    if (clusters.length === 0) return 0;
    if (clusters.length === totalPositions) return 30; // Cada elemento é um cluster
    
    // Clusters ideais têm tamanho razoável
    const clusterSizes = clusters.map(cluster => cluster.length);
    const meanSize = clusterSizes.reduce((sum, size) => sum + size, 0) / clusterSizes.length;
    
    if (meanSize >= 2 && meanSize <= 5) {
      return 100;
    } else {
      return 60;
    }
  }

  assessFeatureDetection(elements, playerClassification, correctClassification) {
    // Avalia detecção de características relevantes
    if (!elements.length) return 50;
    
    const relevantFeatures = this.identifyRelevantFeatures(correctClassification);
    const detectedFeatures = this.identifyDetectedFeatures(playerClassification);
    
    const featureDetectionScore = this.calculateFeatureDetectionAccuracy(relevantFeatures, detectedFeatures);
    return featureDetectionScore * 100;
  }

  identifyRelevantFeatures(correctClassification) {
    // Identifica características relevantes baseadas na classificação correta
    const categories = this.extractCategories(correctClassification);
    const relevantFeatures = new Set();
    
    for (const category of categories) {
      const elementsInCategory = this.getElementsInCategory(correctClassification, category);
      const commonFeatures = this.extractCommonFeatures(elementsInCategory);
      
      commonFeatures.forEach(feature => relevantFeatures.add(feature));
    }
    
    return Array.from(relevantFeatures);
  }

  identifyDetectedFeatures(playerClassification) {
    // Identifica características detectadas pelo jogador
    return this.identifyImpliedCriteria(playerClassification);
  }

  calculateFeatureDetectionAccuracy(relevant, detected) {
    if (relevant.length === 0 && detected.length === 0) return 1;
    if (relevant.length === 0) return 0.5; // Sem características relevantes conhecidas
    
    const intersection = detected.filter(feature => relevant.includes(feature));
    return intersection.length / relevant.length;
  }

  assessCategoricalThinking(playerClassification) {
    // Avalia pensamento categorial
    const categories = this.extractCategories(playerClassification);
    const totalElements = Object.keys(playerClassification).length;
    
    if (totalElements === 0) return 0;
    
    const categoricalMetrics = {
      categoryUtilization: this.assessCategoryUtilization(categories, totalElements),
      categoryDistinctiveness: this.assessCategoryDistinctiveness(categories, playerClassification),
      conceptualClarity: this.assessConceptualClarity(playerClassification)
    };
    
    return (
      categoricalMetrics.categoryUtilization * 0.4 +
      categoricalMetrics.categoryDistinctiveness * 0.3 +
      categoricalMetrics.conceptualClarity * 0.3
    );
  }

  assessCategoryUtilization(categories, totalElements) {
    // Avalia uso apropriado de categorias
    const idealCategoryCount = Math.max(2, Math.min(totalElements / 3, 6));
    const actualCategoryCount = categories.length;
    
    const utilizationScore = 1 - Math.abs(actualCategoryCount - idealCategoryCount) / idealCategoryCount;
    return Math.max(0, utilizationScore * 100);
  }

  assessCategoryDistinctiveness(categories, classification) {
    // Avalia distinção entre categorias
    if (categories.length < 2) return 100;
    
    let distinctivenessScore = 0;
    
    for (let i = 0; i < categories.length - 1; i++) {
      for (let j = i + 1; j < categories.length; j++) {
        const category1Elements = this.getElementsInCategory(classification, categories[i]);
        const category2Elements = this.getElementsInCategory(classification, categories[j]);
        
        const distinctiveness = this.calculateCategoryDistinctiveness(category1Elements, category2Elements);
        distinctivenessScore += distinctiveness;
      }
    }
    
    const comparisons = (categories.length * (categories.length - 1)) / 2;
    return comparisons > 0 ? (distinctivenessScore / comparisons) * 100 : 100;
  }

  calculateCategoryDistinctiveness(elements1, elements2) {
    // Calcula quão distintas são duas categorias
    if (elements1.length === 0 || elements2.length === 0) return 1;
    
    let distinctivenessSum = 0;
    let comparisons = 0;
    
    for (const element1 of elements1) {
      for (const element2 of elements2) {
        const similarity = this.calculateVisualSimilarity(element1, element2);
        distinctivenessSum += 1 - similarity; // Menor similaridade = maior distinção
        comparisons++;
      }
    }
    
    return comparisons > 0 ? distinctivenessSum / comparisons : 1;
  }

  assessConceptualClarity(classification) {
    // Avalia clareza conceitual das categorias
    const categories = this.extractCategories(classification);
    let clarityScore = 0;
    
    for (const category of categories) {
      const elements = this.getElementsInCategory(classification, category);
      const categoryClarity = this.calculateCategoryClarity(elements);
      clarityScore += categoryClarity;
    }
    
    return categories.length > 0 ? (clarityScore / categories.length) * 100 : 0;
  }

  calculateCategoryClarity(elements) {
    // Calcula clareza de uma categoria baseada na coerência interna
    if (elements.length === 0) return 0;
    if (elements.length === 1) return 100;
    
    const featureConsistency = this.calculateFeatureConsistency(elements);
    return featureConsistency;
  }

  evaluateRuleApplication(classificationHistory) {
    // Avalia consistência na aplicação de regras
    if (classificationHistory.length < 3) return 100;
    
    const ruleApplications = this.extractRuleApplications(classificationHistory);
    const consistency = this.calculateRuleConsistency(ruleApplications);
    
    return consistency * 100;
  }

  extractRuleApplications(history) {
    // Extrai aplicações de regras da história de classificação
    const applications = [];
    
    for (const entry of history) {
      if (entry.rule && entry.elementId && entry.category) {
        applications.push({
          rule: entry.rule,
          element: entry.elementId,
          category: entry.category,
          timestamp: entry.timestamp
        });
      }
    }
    
    return applications;
  }

  calculateRuleConsistency(applications) {
    // Calcula consistência na aplicação de regras
    if (applications.length < 2) return 1;
    
    const ruleGroups = {};
    
    // Agrupa aplicações por regra
    for (const application of applications) {
      const rule = application.rule;
      if (!ruleGroups[rule]) {
        ruleGroups[rule] = [];
      }
      ruleGroups[rule].push(application);
    }
    
    let totalConsistency = 0;
    let ruleCount = 0;
    
    for (const [rule, ruleApplications] of Object.entries(ruleGroups)) {
      const ruleConsistency = this.calculateSingleRuleConsistency(ruleApplications);
      totalConsistency += ruleConsistency;
      ruleCount++;
    }
    
    return ruleCount > 0 ? totalConsistency / ruleCount : 1;
  }

  calculateSingleRuleConsistency(ruleApplications) {
    // Calcula consistência para uma regra específica
    if (ruleApplications.length < 2) return 1;
    
    const categories = ruleApplications.map(app => app.category);
    const uniqueCategories = new Set(categories);
    
    // Regra consistente deve levar sempre à mesma categoria
    return uniqueCategories.size === 1 ? 1 : 0.3;
  }

  assessResponseInhibition(gameState) {
    // Avalia inibição de respostas incorretas
    const responseTime = gameState.responseTime || 0;
    const corrections = gameState.corrections || 0;
    const totalResponses = gameState.totalResponses || 1;
    
    // Tempo adequado de resposta (não muito rápido)
    const timeScore = this.evaluateResponseTime(responseTime);
    
    // Poucas correções indicam boa inibição inicial
    const correctionScore = Math.max(0, 100 - (corrections / totalResponses) * 50);
    
    return (timeScore + correctionScore) / 2;
  }

  evaluateResponseTime(responseTime) {
    // Avalia tempo de resposta (muito rápido pode indicar impulsividade)
    const optimalRange = { min: 2000, max: 10000 }; // 2-10 segundos
    
    if (responseTime < optimalRange.min) {
      return 60; // Muito rápido, possível impulsividade
    } else if (responseTime > optimalRange.max) {
      return Math.max(40, 100 - ((responseTime - optimalRange.max) / 1000) * 3);
    } else {
      return 100; // Tempo ideal
    }
  }
}
