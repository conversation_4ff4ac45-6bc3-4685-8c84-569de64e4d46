#!/usr/bin/env node

/**
 * Script para encontrar erro de sintaxe específico no servidor
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

// Arquivos importados no server.js que podem ter erro
const serverImports = [
  'src/api/routes/auth/simple-auth.js',
  'src/api/routes/auth/dashboard-auth.js',
  'src/api/routes/metrics/game-sessions.js',
  'src/api/routes/metrics/interactions.js',
  'src/api/routes/metrics/dashboard.js',
  'src/api/routes/premium/dashboards.js',
  'src/api/routes/public/games.js',
  'src/api/routes/public/health.js',
  'src/api/routes/public/activities.js',
  'src/api/routes/public/metrics.js',
  'src/api/routes/ai-reports.js',
  'src/api/routes/backup/user-backup.js',
  'src/api/routes/dashboard/overview.js',
  'src/api/routes/dashboard/behavior.js',
  'src/api/routes/analytics/insights.js',
  'src/api/routes/analytics/predictions.js',
  'src/api/routes/analytics/trends.js',
  'src/api/routes/debug/metrics-monitor.js'
];

// Função para testar sintaxe de um arquivo
async function testFileSyntax(filePath) {
  try {
    await execAsync(`node -c "${filePath}"`);
    return { success: true, file: filePath };
  } catch (error) {
    return { 
      success: false, 
      file: filePath, 
      error: error.message,
      stderr: error.stderr
    };
  }
}

// Função para testar importação dinâmica
async function testDynamicImport(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    const fileUrl = `file://${fullPath.replace(/\\/g, '/')}`;
    await import(fileUrl);
    return { success: true, file: filePath };
  } catch (error) {
    return { 
      success: false, 
      file: filePath, 
      error: error.message,
      stack: error.stack
    };
  }
}

// Função principal
async function main() {
  console.log('🔍 Procurando por erro de sintaxe nos arquivos do servidor...\n');
  
  const errors = [];
  
  for (const file of serverImports) {
    if (!fs.existsSync(file)) {
      console.log(`⚠️ ${file} - ARQUIVO NÃO EXISTE`);
      continue;
    }
    
    console.log(`📄 Testando ${file}...`);
    
    // Teste 1: Verificação de sintaxe básica
    const syntaxResult = await testFileSyntax(file);
    if (!syntaxResult.success) {
      console.log(`❌ ERRO DE SINTAXE: ${file}`);
      console.log(`   Erro: ${syntaxResult.error}`);
      if (syntaxResult.stderr) {
        console.log(`   Stderr: ${syntaxResult.stderr}`);
      }
      errors.push(syntaxResult);
      continue;
    }
    
    // Teste 2: Teste de importação dinâmica
    const importResult = await testDynamicImport(file);
    if (!importResult.success) {
      console.log(`❌ ERRO DE IMPORTAÇÃO: ${file}`);
      console.log(`   Erro: ${importResult.error}`);
      if (importResult.error.includes('missing ) after argument list')) {
        console.log(`   🎯 ESTE É O ARQUIVO COM O ERRO!`);
        console.log(`   Stack: ${importResult.stack}`);
      }
      errors.push(importResult);
      continue;
    }
    
    console.log(`✅ ${file} - OK`);
  }
  
  if (errors.length === 0) {
    console.log('\n🎉 Todos os arquivos estão corretos!');
  } else {
    console.log(`\n💥 Encontrados ${errors.length} arquivos com problemas:`);
    errors.forEach(error => {
      console.log(`\n📄 ${error.file}:`);
      console.log(`   ${error.error}`);
      if (error.error.includes('missing ) after argument list')) {
        console.log(`   🎯 ESTE ARQUIVO CONTÉM O ERRO DE SINTAXE!`);
      }
    });
  }
}

main().catch(console.error);
