/**
 * @file test-processors-integration.js
 * @description Script para testar a integração dos processadores com seus coletores
 * @version 1.0.0
 */

// Imports de processadores
import GameSpecificProcessors from './src/api/services/processors/GameSpecificProcessors.js';
import ColorMatchProcessors from './src/api/services/processors/games/ColorMatchProcessors.js';
import MemoryGameProcessors from './src/api/services/processors/games/MemoryGameProcessors.js';
import LetterRecognitionProcessors from './src/api/services/processors/games/LetterRecognitionProcessors.js';
import CreativePaintingProcessors from './src/api/services/processors/games/CreativePaintingProcessors.js';
import QuebraCabecaProcessors from './src/api/services/processors/games/QuebraCabecaProcessors.js';
import ImageAssociationProcessors from './src/api/services/processors/games/ImageAssociationProcessors.js';
import MusicalSequenceProcessors from './src/api/services/processors/games/MusicalSequenceProcessors.js';
import PadroesVisuaisProcessors from './src/api/services/processors/games/PadroesVisuaisProcessors.js';
import ContagemNumerosProcessors from './src/api/services/processors/games/ContagemNumerosProcessors.js';

// Imports de hubs de coletores
import { ColorMatchCollectorsHub } from './src/games/ColorMatch/collectors/index.js';
import { MemoryGameCollectorsHub } from './src/games/MemoryGame/collectors/index.js';
import { LetterRecognitionCollectorsHub } from './src/games/LetterRecognition/collectors/index.js';
import { CreativePaintingCollectorsHub } from './src/games/CreativePainting/collectors/index.js';
import { QuebraCabecaCollectorsHub } from './src/games/QuebraCabeca/collectors/index.js';
import { ImageAssociationCollectorsHub } from './src/games/ImageAssociation/collectors/index.js';
import { MusicalSequenceCollectorsHub } from './src/games/MusicalSequence/collectors/index.js';
import { PadroesVisuaisCollectorsHub } from './src/games/PadroesVisuais/collectors/index.js';
import { NumberCountingCollectorsHub } from './src/games/ContagemNumeros/collectors/index.js';

// Detectar ambiente
const isBrowser = typeof window !== 'undefined' && typeof window.document !== 'undefined';
const isNode = typeof process !== 'undefined' && process.versions && process.versions.node;

// Logger adaptado ao ambiente
let logger;
if (isBrowser) {
  logger = {
    info: (...args) => console.info('%c🎮 [TEST-PROCESSOR]', 'color: #2196F3', new Date().toISOString(), ...args),
    error: (...args) => console.error('%c🔴 [TEST-ERROR]', 'color: #F44336', new Date().toISOString(), ...args),
    warn: (...args) => console.warn('%c🟡 [TEST-WARN]', 'color: #FF9800', new Date().toISOString(), ...args),
    debug: (...args) => console.debug('%c⚪ [TEST-DEBUG]', 'color: #9E9E9E', new Date().toISOString(), ...args),
    success: (...args) => console.info('%c✅ [TEST-SUCCESS]', 'color: #4CAF50', new Date().toISOString(), ...args)
  };
} else {
  logger = {
    info: (...args) => console.info('🎮 [TEST-PROCESSOR]', new Date().toISOString(), ...args),
    error: (...args) => console.error('🔴 [TEST-ERROR]', new Date().toISOString(), ...args),
    warn: (...args) => console.warn('🟡 [TEST-WARN]', new Date().toISOString(), ...args),
    debug: (...args) => console.debug('⚪ [TEST-DEBUG]', new Date().toISOString(), ...args),
    success: (...args) => console.info('✅ [TEST-SUCCESS]', new Date().toISOString(), ...args)
  };
}

/**
 * Gera dados simulados de jogo para testes
 * @param {string} gameType - Tipo do jogo
 * @returns {Object} Dados simulados do jogo
 */
function generateMockGameData(gameType) {
  const sessionId = `test-session-${Date.now()}`;
  const userId = `test-user-${Math.floor(Math.random() * 1000)}`;
  const timestamp = new Date().toISOString();

  // Dados base comuns a todos os jogos
  const baseData = {
    sessionId,
    userId,
    gameId: gameType,
    timestamp,
    metrics: {
      accuracy: Math.random() * 100,
      responseTime: Math.floor(Math.random() * 5000) + 500,
      engagement: Math.random() * 100,
      sessionDuration: Math.floor(Math.random() * 300000) + 60000
    },
    attempts: []
  };

  // Gera tentativas genéricas
  for (let i = 0; i < 10; i++) {
    baseData.attempts.push({
      attemptId: `${i+1}`,
      timestamp: new Date(Date.now() - (10-i) * 30000).toISOString(),
      correct: Math.random() > 0.3,
      responseTime: Math.floor(Math.random() * 3000) + 500
    });
  }

  // Adiciona campos específicos com base no tipo de jogo
  switch (gameType) {
    case 'ColorMatch':
      return {
        ...baseData,
        totalColors: 6,
        correctMatches: Math.floor(Math.random() * 15) + 5,
        incorrectMatches: Math.floor(Math.random() * 5),
        difficulty: Math.floor(Math.random() * 3) + 1,
        interactions: baseData.attempts.map(a => ({
          ...a,
          targetColor: ['red', 'blue', 'green', 'yellow', 'purple'][Math.floor(Math.random() * 5)],
          selectedColor: ['red', 'blue', 'green', 'yellow', 'purple'][Math.floor(Math.random() * 5)]
        }))
      };

    case 'MemoryGame':
      return {
        ...baseData,
        totalPairs: 8,
        pairsFound: Math.floor(Math.random() * 8) + 1,
        totalFlips: Math.floor(Math.random() * 30) + 10,
        memorySets: ['animals', 'fruits', 'shapes'],
        timeLimit: 180000
      };

    case 'LetterRecognition':
      return {
        ...baseData,
        totalLetters: 20,
        correctRecognitions: Math.floor(Math.random() * 15) + 5,
        incorrectRecognitions: Math.floor(Math.random() * 5),
        letterCategories: ['vowels', 'consonants'],
        letterAttempts: baseData.attempts.map(a => ({
          ...a,
          targetLetter: String.fromCharCode(65 + Math.floor(Math.random() * 26)),
          userInput: String.fromCharCode(65 + Math.floor(Math.random() * 26))
        }))
      };

    case 'CreativePainting':
      return {
        ...baseData,
        canvasSize: { width: 800, height: 600 },
        colorPalette: ['red', 'blue', 'green', 'yellow', 'black', 'white', 'purple'],
        brushStrokes: Math.floor(Math.random() * 50) + 10,
        colorDiversity: Math.floor(Math.random() * 5) + 1,
        spatialCoverage: Math.random() * 0.8 + 0.2,
        paintActions: baseData.attempts.map(a => ({
          ...a,
          color: ['red', 'blue', 'green', 'yellow', 'purple'][Math.floor(Math.random() * 5)],
          position: { x: Math.random() * 800, y: Math.random() * 600 },
          brushSize: Math.floor(Math.random() * 10) + 5
        }))
      };

    case 'QuebraCabeca':
      return {
        ...baseData,
        totalPieces: 16,
        correctPlacements: Math.floor(Math.random() * 16) + 1,
        incorrectPlacements: Math.floor(Math.random() * 8),
        puzzleTheme: ['animals', 'landscape', 'cartoon'][Math.floor(Math.random() * 3)],
        puzzleDifficulty: Math.floor(Math.random() * 3) + 1,
        pieceActions: baseData.attempts.map(a => ({
          ...a,
          pieceId: `piece-${Math.floor(Math.random() * 16) + 1}`,
          initialPosition: { x: Math.random() * 800, y: Math.random() * 600 },
          finalPosition: { x: Math.random() * 800, y: Math.random() * 600 }
        }))
      };

    case 'ImageAssociation':
      return {
        ...baseData,
        totalPairs: 10,
        correctAssociations: Math.floor(Math.random() * 8) + 2,
        categories: ['animals', 'food', 'tools', 'vehicles'],
        difficultyLevel: Math.floor(Math.random() * 3) + 1,
        associationAttempts: baseData.attempts.map(a => ({
          ...a,
          image1: `img-${Math.floor(Math.random() * 10) + 1}`,
          image2: `img-${Math.floor(Math.random() * 10) + 1}`,
          category: ['animals', 'food', 'tools'][Math.floor(Math.random() * 3)]
        }))
      };

    case 'MusicalSequence':
      return {
        ...baseData,
        sequenceLength: Math.floor(Math.random() * 5) + 3,
        completedSequences: Math.floor(Math.random() * 5) + 1,
        failedSequences: Math.floor(Math.random() * 3),
        audioTones: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],
        sequences: [
          { notes: ['C', 'E', 'G'], correct: true },
          { notes: ['D', 'F', 'A'], correct: Math.random() > 0.5 },
          { notes: ['E', 'G', 'B'], correct: Math.random() > 0.5 }
        ]
      };

    case 'PadroesVisuais':
      return {
        ...baseData,
        patternLength: Math.floor(Math.random() * 5) + 3,
        completedPatterns: Math.floor(Math.random() * 5) + 1,
        failedPatterns: Math.floor(Math.random() * 3),
        visualElements: ['circle', 'square', 'triangle', 'star', 'diamond'],
        patterns: [
          { elements: ['circle', 'square', 'triangle'], correct: true },
          { elements: ['square', 'triangle', 'square'], correct: Math.random() > 0.5 },
          { elements: ['triangle', 'circle', 'diamond'], correct: Math.random() > 0.5 }
        ]
      };

    case 'ContagemNumeros':
      return {
        ...baseData,
        numberRange: { min: 1, max: 20 },
        correctCounts: Math.floor(Math.random() * 12) + 5,
        incorrectCounts: Math.floor(Math.random() * 5),
        countingAttempts: baseData.attempts.map(a => ({
          ...a,
          numberToCount: Math.floor(Math.random() * 20) + 1,
          userAnswer: Math.floor(Math.random() * 20) + 1
        }))
      };

    default:
      return baseData;
  }
}

/**
 * Verifica se os dados retornados pelo coletor são válidos
 * @param {Object} collectorData - Dados retornados pelo coletor
 * @returns {boolean} Verdadeiro se os dados forem válidos
 */
function validateCollectorOutput(collectorData) {
  if (!collectorData) return false;
  
  // Verificação básica para garantir que é um objeto com conteúdo
  if (typeof collectorData !== 'object') return false;
  if (Object.keys(collectorData).length === 0) return false;

  // Verificar se pelo menos uma das propriedades esperadas existe
  const hasRelevantProperties = [
    'data', 'metrics', 'analysis', 'results', 'score', 
    'insights', 'patterns', 'indicators', 'recommendedActions'
  ].some(prop => prop in collectorData);

  return hasRelevantProperties;
}

/**
 * Testa a integração de processadores com seus coletores
 * @param {string} gameType - Tipo do jogo a ser testado
 * @param {Object} processor - Instância do processador
 * @param {Object} collectorsHub - Instância do hub de coletores
 */
async function testProcessorWithCollectors(gameType, processor, collectorsHub) {
  logger.info(`🧪 Testando integração do processador com coletores para ${gameType}...`);
  
  try {
    // 1. Verificar se o hub de coletores tem coletores
    if (!collectorsHub || !collectorsHub.collectors) {
      throw new Error(`Hub de coletores inválido para ${gameType}`);
    }
    
    const collectors = collectorsHub.collectors;
    const collectorNames = Object.keys(collectors);
    
    logger.info(`📊 Encontrados ${collectorNames.length} coletores para ${gameType}:`, collectorNames);

    // 2. Verificar se cada coletor tem o método collect
    for (const name of collectorNames) {
      const collector = collectors[name];
      if (!collector || typeof collector.collect !== 'function') {
        throw new Error(`Coletor ${name} não tem método collect`);
      }
      if (!collector || typeof collector.analyze !== 'function') {
        throw new Error(`Coletor ${name} não tem método analyze`);
      }
    }

    // 3. Gerar dados mockados para o jogo
    const mockGameData = generateMockGameData(gameType);
    logger.debug(`🎲 Dados mockados gerados para ${gameType}:`, { 
      sessionId: mockGameData.sessionId,
      userId: mockGameData.userId,
      attempts: mockGameData.attempts.length 
    });

    // 4. Processar os dados usando o processador
    const processorResult = await processor.processGameData(mockGameData, collectorsHub);
    
    if (!processorResult || processorResult.success === false) {
      throw new Error(`Processamento falhou para ${gameType}: ${processorResult?.error || 'Erro desconhecido'}`);
    }

    logger.success(`✅ Processador ${gameType} executou com sucesso`);

    // 5. Testar coletores individualmente e verificar integração com processador
    logger.info(`🔍 Testando coletores individualmente para ${gameType}...`);
    
    const collectorResults = {};
    const failedCollectors = [];

    for (const name of collectorNames) {
      try {
        const collector = collectors[name];
        const collectorData = await collector.collect(mockGameData);
        
        // Verificar se os dados do coletor são válidos
        const isValid = validateCollectorOutput(collectorData);
        
        collectorResults[name] = {
          success: isValid,
          hasData: !!collectorData,
          dataKeys: collectorData ? Object.keys(collectorData) : []
        };

        if (!isValid) {
          failedCollectors.push(name);
          logger.warn(`⚠️ Coletor ${name} retornou dados inválidos`);
        } else {
          logger.debug(`✓ Coletor ${name} OK`);
        }
      } catch (error) {
        failedCollectors.push(name);
        collectorResults[name] = { 
          success: false, 
          error: error.message 
        };
        logger.error(`❌ Erro no coletor ${name}:`, error.message);
      }
    }

    // 6. Resumo final
    const successCount = Object.values(collectorResults).filter(r => r.success).length;
    const totalCollectors = collectorNames.length;

    logger.info(`📊 Resultado final para ${gameType}:`, {
      totalCollectors,
      successfulCollectors: successCount,
      success: successCount === totalCollectors,
      successRate: `${Math.round((successCount / totalCollectors) * 100)}%`,
      failedCollectors
    });

    return {
      gameType,
      success: successCount === totalCollectors,
      totalCollectors,
      successfulCollectors: successCount,
      failedCollectors,
      collectorResults,
      processorResult: {
        success: !!processorResult,
        hasResult: !!processorResult
      }
    };

  } catch (error) {
    logger.error(`❌ Erro ao testar processador ${gameType}:`, error);
    return {
      gameType,
      success: false,
      error: error.message,
      stack: error.stack
    };
  }
}

/**
 * Função principal que testa todos os processadores e coletores
 */
async function runAllTests() {
  logger.info('🚀 Iniciando testes de integração de processadores com coletores...');

  const testConfigurations = [
    {
      gameType: 'ColorMatch',
      processor: new ColorMatchProcessors(logger),
      collectorsHub: new ColorMatchCollectorsHub()
    },
    {
      gameType: 'MemoryGame',
      processor: new MemoryGameProcessors(logger),
      collectorsHub: new MemoryGameCollectorsHub()
    },
    {
      gameType: 'LetterRecognition',
      processor: new LetterRecognitionProcessors(logger),
      collectorsHub: new LetterRecognitionCollectorsHub()
    },
    {
      gameType: 'CreativePainting',
      processor: new CreativePaintingProcessors(logger),
      collectorsHub: new CreativePaintingCollectorsHub()
    },
    {
      gameType: 'QuebraCabeca',
      processor: new QuebraCabecaProcessors(logger),
      collectorsHub: new QuebraCabecaCollectorsHub()
    },
    {
      gameType: 'ImageAssociation',
      processor: new ImageAssociationProcessors(logger),
      collectorsHub: new ImageAssociationCollectorsHub()
    },
    {
      gameType: 'MusicalSequence',
      processor: new MusicalSequenceProcessors(logger),
      collectorsHub: new MusicalSequenceCollectorsHub()
    },
    {
      gameType: 'PadroesVisuais',
      processor: new PadroesVisuaisProcessors(logger),
      collectorsHub: new PadroesVisuaisCollectorsHub()
    },
    {
      gameType: 'ContagemNumeros',
      processor: new ContagemNumerosProcessors(logger),
      collectorsHub: new NumberCountingCollectorsHub()
    }
  ];

  const results = [];

  // Testar cada configuração
  for (const config of testConfigurations) {
    logger.info(`\n🎮 Testando ${config.gameType}...`);
    const result = await testProcessorWithCollectors(
      config.gameType,
      config.processor,
      config.collectorsHub
    );
    results.push(result);
    
    // Adicionar separador entre jogos
    console.log('\n' + '='.repeat(80) + '\n');
  }

  // Resumo final dos testes
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const totalTests = results.length;

  console.log('\n');
  logger.info('📋 RESUMO FINAL DOS TESTES DE INTEGRAÇÃO');
  console.log('='.repeat(50));
  console.log(`Total de jogos testados: ${totalTests}`);
  console.log(`✅ Integração bem-sucedida: ${successful}`);
  console.log(`❌ Integração com falhas: ${failed}`);
  console.log(`Taxa de sucesso: ${Math.round((successful / totalTests) * 100)}%`);
  console.log('='.repeat(50));

  // Detalhes das falhas, se houver
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    console.log('\n❌ DETALHES DAS FALHAS:');
    failedTests.forEach(test => {
      console.log(`\n- ${test.gameType}:`);
      if (test.error) {
        console.log(`  Erro: ${test.error}`);
      } else if (test.failedCollectors) {
        console.log(`  Coletores com falha (${test.failedCollectors.length}): ${test.failedCollectors.join(', ')}`);
      }
    });
  }
}

// Executar os testes
runAllTests().catch(error => {
  logger.error('❌ Erro ao executar testes:', error);
});
