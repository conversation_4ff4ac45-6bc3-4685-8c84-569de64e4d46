/**
 * 🎯 TEMPLATE DE PADRONIZAÇÃO DE JOGOS - PORTAL BETINA V3
 * Use este template para padronizar todos os jogos do sistema
 */

import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';
import { SystemContext } from '../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import GameStartScreen from '../components/common/GameStartScreen/GameStartScreen.jsx';
import { useUnifiedGameLogic } from '../hooks/useUnifiedGameLogic.js';
import { useMultisensoryIntegration } from '../hooks/useMultisensoryIntegration.js';
import { useTherapeuticOrchestrator } from '../hooks/useTherapeuticOrchestrator.js';

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - [NOME_DO_JOGO]
const ACTIVITY_TYPES = {
  ACTIVITY_1: {
    id: 'activity_1',
    name: 'Atividade 1',
    icon: '🎯',
    description: 'Descrição da atividade 1',
    component: 'Activity1Component'
  },
  ACTIVITY_2: {
    id: 'activity_2',
    name: 'Atividade 2',
    icon: '🎮',
    description: 'Descrição da atividade 2',
    component: 'Activity2Component'
  },
  ACTIVITY_3: {
    id: 'activity_3',
    name: 'Atividade 3',
    icon: '🏆',
    description: 'Descrição da atividade 3',
    component: 'Activity3Component'
  },
  ACTIVITY_4: {
    id: 'activity_4',
    name: 'Atividade 4',
    icon: '⭐',
    description: 'Descrição da atividade 4',
    component: 'Activity4Component'
  },
  ACTIVITY_5: {
    id: 'activity_5',
    name: 'Atividade 5',
    icon: '🚀',
    description: 'Descrição da atividade 5',
    component: 'Activity5Component'
  },
  ACTIVITY_6: {
    id: 'activity_6',
    name: 'Atividade 6',
    icon: '💎',
    description: 'Descrição da atividade 6',
    component: 'Activity6Component'
  }
};

const StandardizedGame = ({ onBack }) => {
  // 🏠 Contexto do sistema
  const { user } = useContext(SystemContext);
  
  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();
  
  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);
  
  // Referência para métricas
  const metricsRef = useRef(null);
  
  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'EASY',
    accuracy: 100,
    roundStartTime: null,
    
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.ACTIVITY_1.id,
    activityCycle: [
      ACTIVITY_TYPES.ACTIVITY_1.id,
      ACTIVITY_TYPES.ACTIVITY_2.id,
      ACTIVITY_TYPES.ACTIVITY_3.id,
      ACTIVITY_TYPES.ACTIVITY_4.id,
      ACTIVITY_TYPES.ACTIVITY_5.id,
      ACTIVITY_TYPES.ACTIVITY_6.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],
    
    // 🎯 Dados específicos de atividades
    activityData: {
      activity1: {},
      activity2: {},
      activity3: {},
      activity4: {},
      activity5: {},
      activity6: {}
    },
    
    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,
    
    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('game_name'); // Substituir pelo nome do jogo

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration('game_name', null); // Adicionar collectorsHub

  // 🎯 Hook orquestrador terapêutico
  const {
    initializeSession: initTherapeutic,
    recordActivity: recordTherapeuticActivity,
    generateReport: generateTherapeuticReport,
    getRecommendations: getTherapeuticRecommendations,
    finalizeSession: finalizeTherapeutic
  } = useTherapeuticOrchestrator();

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }
    
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Toggle TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => !prev);
    if (!ttsActive) {
      speak('TTS ativado');
    }
  }, [ttsActive, speak]);

  // 🎯 FUNÇÕES DE CONTROLE DO JOGO PADRONIZADAS
  
  // Função para iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    try {
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: selectedDifficulty,
        roundStartTime: Date.now()
      }));

      // Inicializar sessão unificada
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: 'game_name',
          difficulty: selectedDifficulty,
          userId: user?.id || 'anonymous'
        });
      }

      // Inicializar sessões dos hooks
      await initMultisensory();
      await initTherapeutic();

      // Gerar primeira atividade
      generateNewRound();

      speak('Jogo iniciado!');
    } catch (error) {
      console.error('Erro ao iniciar jogo:', error);
    }
  }, [startUnifiedSession, initMultisensory, initTherapeutic, user, speak]);

  // Função para trocar atividade
  const changeActivity = useCallback((activityId) => {
    if (gameState.activityRoundCount < 4 && gameState.currentActivity !== activityId) {
      speak(`Complete mais ${4 - gameState.activityRoundCount} rodadas da atividade atual primeiro.`);
      return;
    }

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      activityRoundCount: 0,
      activityIndex: prev.activityCycle.indexOf(activityId)
    }));

    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    speak(`Mudando para: ${activity?.name}`);
    
    generateNewRound();
  }, [gameState.activityRoundCount, gameState.currentActivity, speak]);

  // Função para gerar nova rodada
  const generateNewRound = useCallback(() => {
    // Implementar lógica específica do jogo
  }, [gameState.currentActivity]);

  // Renderizar atividade atual
  const renderCurrentActivity = () => {
    // Implementar renderização específica de cada atividade
    return <div>Atividade em desenvolvimento</div>;
  };

  // Tela de início
  if (gameState.status === 'start') {
    return (
      <GameStartScreen
        gameTitle="Nome do Jogo"
        gameSubtitle="Descrição do jogo"
        gameInstruction="Instruções do jogo"
        gameIcon="🎮"
        onStartGame={startGame}
        onBack={onBack}
      />
    );
  }

  // Interface principal padronizada
  return (
    <div className="gameContainer">
      <div className="gameContent">
        {/* Header do jogo */}
        <div className="gameHeader">
          <h1 className="gameTitle">
            🎮 Nome do Jogo
            <div className="activitySubtitle">
              {Object.values(ACTIVITY_TYPES).find(activity => activity.id === gameState.currentActivity)?.name || 'Atividade Atual'}
            </div>
          </h1>
          <button 
            className={`headerTtsButton ${ttsActive ? 'ttsActive' : 'ttsInactive'}`}
            onClick={toggleTTS}
            title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
            aria-label={ttsActive ? "Desativar TTS" : "Ativar TTS"}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* 🎯 SISTEMA DE ATIVIDADES - PADRÃO MEMORYGAME EXATO */}
        {gameState.status === 'playing' && (
          <>
            {/* Menu de Atividades */}
            <div className="activityMenu">
              {Object.values(ACTIVITY_TYPES).map((activity) => {
                const isCurrentActivity = gameState.currentActivity === activity.id;
                const canSwitch = gameState.activityRoundCount >= 4 || isCurrentActivity;
                
                return (
                  <button
                    key={activity.id}
                    className={`activityButton ${isCurrentActivity ? 'active' : ''}`}
                    onClick={() => changeActivity(activity.id)}
                    title={
                      !canSwitch 
                        ? `Complete ${4 - gameState.activityRoundCount} rodadas da atividade atual primeiro`
                        : activity.description
                    }
                    disabled={!canSwitch}
                  >
                    <span className="activityIcon">{activity.icon}</span>
                    <span className="activityName">{activity.name}</span>
                    {isCurrentActivity && (
                      <span className="activeIndicator">●</span>
                    )}
                  </button>
                );
              })}
            </div>

            {/* Estatísticas do jogo */}
            <div className="gameStats">
              <div className="statCard">
                <div className="statValue">{gameState.round}</div>
                <div className="statLabel">Rodada</div>
              </div>
              <div className="statCard">
                <div className="statValue">{gameState.score}</div>
                <div className="statLabel">Pontos</div>
              </div>
              <div className="statCard">
                <div className="statValue">{gameState.accuracy}%</div>
                <div className="statLabel">Precisão</div>
              </div>
              <div className="statCard">
                <div className="statValue">{gameState.activityRoundCount}/4+</div>
                <div className="statLabel">Progresso</div>
              </div>
            </div>

            {/* Interface da atividade atual */}
            {renderCurrentActivity()}
          </>
        )}
      </div>
    </div>
  );
};

export default StandardizedGame;
