/**
 * @file UnifiedDashboard.jsx
 * @description Componente que integra todos os dashboards do Portal Betina V3
 * @version 3.0.0
 * @premium true
 */

import React, { useState, useEffect } from 'react'
import styles from './UnifiedDashboard.module.css'

const UnifiedDashboard = ({ dashboardData, className, viewMode = 'standard' }) => {
  const [selectedDashboard, setSelectedDashboard] = useState('overview')
  // viewMode recebido como prop: standard, compact, detailed, professional
  const [visualTheme, setVisualTheme] = useState('default') // default, professional, playful
  const [filters, setFilters] = useState({
    timeRange: '30d',
    userId: 'all',
    activityType: 'all'
  })

  // Configuração dos dashboards disponíveis
  const dashboardConfigs = {
    overview: {
      title: 'Visão Geral',
      icon: '📊',
      description: 'Panorama completo de todas as métricas',
      color: '#667eea'
    },
    behavioral: {
      title: 'Análises Comportamentais',
      icon: '🧠',
      description: 'Padrões comportamentais e cognitivos',
      color: '#48bb78'
    },
    games: {
      title: 'Métricas de Jogos',
      icon: '🎮',
      description: 'Performance e progresso nos jogos',
      color: '#ed8936'
    },
    therapeutic: {
      title: 'Relatórios Terapêuticos',
      icon: '🏥',
      description: 'Avaliações e planos terapêuticos',
      color: '#9f7aea'
    },
    progress: {
      title: 'Progressão de Atividades',
      icon: '📈',
      description: 'Evolução temporal das habilidades',
      color: '#e91e63'
    },
    sensory: {
      title: 'Integração Multissensorial',
      icon: '🌈',
      description: 'Análises sensoriais avançadas',
      color: '#607d8b'
    }
  }

  // Simular dados consolidados (será substituído por dados reais)
  const generateConsolidatedData = () => {
    return {
      overview: {
        totalSessions: 247,
        avgPerformance: 82.5,
        improvementRate: 15.8,
        activeGoals: 12,
        completedActivities: 156,
        timeSpent: '45h 32m',
        keyMetrics: [
          { name: 'Atenção Sustentada', value: 85, trend: 'up' },
          { name: 'Flexibilidade Cognitiva', value: 78, trend: 'up' },
          { name: 'Memória de Trabalho', value: 72, trend: 'stable' },
          { name: 'Controle Inibitório', value: 80, trend: 'up' }
        ]
      },
      behavioral: {
        patterns: [
          { type: 'Picos de Atenção', frequency: 'Manhãs (9-11h)', intensity: 'Alta' },
          { type: 'Fadiga Cognitiva', frequency: 'Tardes (14-16h)', intensity: 'Moderada' },
          { type: 'Engajamento Social', frequency: 'Atividades em grupo', intensity: 'Crescente' }
        ],
        adaptations: [
          'Sessões mais curtas no período da tarde',
          'Pausas sensoriais a cada 15 minutos',
          'Reforço positivo visual'
        ]
      },
      games: {
        favoriteGames: ['Memory Match', 'Color Sequence', 'Shape Sorting'],
        difficulty: { current: 'Intermediário', progression: '+2 níveis' },
        achievements: 23,
        streaks: { current: 7, best: 12 }
      },
      therapeutic: {
        currentGoals: [
          'Melhorar atenção sustentada (90% concluído)',
          'Desenvolver habilidades sociais (75% concluído)',
          'Fortalecer coordenação motora (60% concluído)'
        ],
        interventions: [
          'Terapia ocupacional - 2x/semana',
          'Fonoaudiologia - 1x/semana', 
          'Psicopedagogia - 1x/semana'
        ],
        nextSession: '2025-07-16'
      },
      progress: {
        monthlyGrowth: {
          attention: 12,
          memory: 8,
          executive: 15,
          social: 20
        },
        milestones: [
          { skill: 'Sequenciamento', achieved: '2025-07-10', level: 'Básico' },
          { skill: 'Categorização', achieved: '2025-07-08', level: 'Intermediário' }
        ]
      },
      sensory: {
        profile: {
          visual: 'Hiperresponsivo',
          auditory: 'Típico',
          tactile: 'Hiporesponsivo',
          vestibular: 'Busca sensorial'
        },
        strategies: [
          'Ambientes com pouca estimulação visual',
          'Uso de fones com cancelamento de ruído',
          'Atividades proprioceptivas regulares'
        ]
      }
    }
  }

  const [consolidatedData, setConsolidatedData] = useState(generateConsolidatedData())

  // Renderizar dashboard específico
  const renderDashboardContent = () => {
    const data = consolidatedData[selectedDashboard]
    const config = dashboardConfigs[selectedDashboard]

    switch (selectedDashboard) {
      case 'overview':
        return (
          <div className={styles.overviewContent}>
            <div className={styles.metricsGrid}>
              <div className={styles.metricCard}>
                <div className={styles.metricIcon}>🎯</div>
                <div className={styles.metricValue}>{data.totalSessions}</div>
                <div className={styles.metricLabel}>Sessões Totais</div>
              </div>
              <div className={styles.metricCard}>
                <div className={styles.metricIcon}>📊</div>
                <div className={styles.metricValue}>{data.avgPerformance}%</div>
                <div className={styles.metricLabel}>Performance Média</div>
              </div>
              <div className={styles.metricCard}>
                <div className={styles.metricIcon}>📈</div>
                <div className={styles.metricValue}>+{data.improvementRate}%</div>
                <div className={styles.metricLabel}>Melhoria Mensal</div>
              </div>
              <div className={styles.metricCard}>
                <div className={styles.metricIcon}>✅</div>
                <div className={styles.metricValue}>{data.activeGoals}</div>
                <div className={styles.metricLabel}>Objetivos Ativos</div>
              </div>
            </div>

            <div className={styles.keyMetricsSection}>
              <h4 className={styles.sectionTitle}>Métricas Principais</h4>
              <div className={styles.keyMetricsList}>
                {data.keyMetrics.map((metric, index) => (
                  <div key={index} className={styles.keyMetricItem}>
                    <div className={styles.metricName}>{metric.name}</div>
                    <div className={styles.metricProgress}>
                      <div 
                        className={styles.progressBar}
                        style={{ width: `${metric.value}%` }}
                      ></div>
                    </div>
                    <div className={styles.metricValueText}>{metric.value}%</div>
                    <div className={`${styles.trendIcon} ${styles[metric.trend]}`}>
                      {metric.trend === 'up' && '↗️'}
                      {metric.trend === 'down' && '↘️'}
                      {metric.trend === 'stable' && '➡️'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 'behavioral':
        return (
          <div className={styles.behavioralContent}>
            <div className={styles.patternsSection}>
              <h4 className={styles.sectionTitle}>Padrões Identificados</h4>
              <div className={styles.patternsList}>
                {data.patterns.map((pattern, index) => (
                  <div key={index} className={styles.patternCard}>
                    <div className={styles.patternType}>{pattern.type}</div>
                    <div className={styles.patternDetails}>
                      <span>📅 {pattern.frequency}</span>
                      <span>⚡ {pattern.intensity}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className={styles.adaptationsSection}>
              <h4 className={styles.sectionTitle}>Adaptações Recomendadas</h4>
              <div className={styles.adaptationsList}>
                {data.adaptations.map((adaptation, index) => (
                  <div key={index} className={styles.adaptationItem}>
                    <span className={styles.adaptationIcon}>💡</span>
                    <span className={styles.adaptationText}>{adaptation}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 'games':
        return (
          <div className={styles.gamesContent}>
            <div className={styles.gamesGrid}>
              <div className={styles.gameStatsCard}>
                <h4>Jogos Favoritos</h4>
                <div className={styles.favoriteGamesList}>
                  {data.favoriteGames.map((game, index) => (
                    <div key={index} className={styles.favoriteGame}>
                      <span className={styles.gameIcon}>🎮</span>
                      <span>{game}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className={styles.gameStatsCard}>
                <h4>Progressão</h4>
                <div className={styles.difficultyInfo}>
                  <div>Nível Atual: <strong>{data.difficulty.current}</strong></div>
                  <div>Evolução: <strong>{data.difficulty.progression}</strong></div>
                </div>
              </div>

              <div className={styles.gameStatsCard}>
                <h4>Conquistas</h4>
                <div className={styles.achievementsInfo}>
                  <div>🏆 {data.achievements} conquistas</div>
                  <div>🔥 Sequência atual: {data.streaks.current} dias</div>
                  <div>⭐ Melhor sequência: {data.streaks.best} dias</div>
                </div>
              </div>
            </div>
          </div>
        )

      case 'therapeutic':
        return (
          <div className={styles.therapeuticContent}>
            <div className={styles.goalsSection}>
              <h4 className={styles.sectionTitle}>Objetivos Atuais</h4>
              <div className={styles.goalsList}>
                {data.currentGoals.map((goal, index) => (
                  <div key={index} className={styles.goalItem}>
                    <span className={styles.goalIcon}>🎯</span>
                    <span className={styles.goalText}>{goal}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className={styles.interventionsSection}>
              <h4 className={styles.sectionTitle}>Intervenções Ativas</h4>
              <div className={styles.interventionsList}>
                {data.interventions.map((intervention, index) => (
                  <div key={index} className={styles.interventionItem}>
                    <span className={styles.interventionIcon}>🏥</span>
                    <span className={styles.interventionText}>{intervention}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 'progress':
        return (
          <div className={styles.progressContent}>
            <div className={styles.growthSection}>
              <h4 className={styles.sectionTitle}>Crescimento Mensal (%)</h4>
              <div className={styles.growthBars}>
                {Object.entries(data.monthlyGrowth).map(([skill, growth]) => (
                  <div key={skill} className={styles.growthBar}>
                    <div className={styles.skillName}>
                      {skill.charAt(0).toUpperCase() + skill.slice(1)}
                    </div>
                    <div className={styles.growthBarContainer}>
                      <div 
                        className={styles.growthBarFill}
                        style={{ width: `${growth * 5}%` }}
                      ></div>
                    </div>
                    <div className={styles.growthValue}>+{growth}%</div>
                  </div>
                ))}
              </div>
            </div>

            <div className={styles.milestonesSection}>
              <h4 className={styles.sectionTitle}>Marcos Recentes</h4>
              <div className={styles.milestonesList}>
                {data.milestones.map((milestone, index) => (
                  <div key={index} className={styles.milestoneItem}>
                    <div className={styles.milestoneIcon}>🎉</div>
                    <div className={styles.milestoneInfo}>
                      <div className={styles.milestoneSkill}>{milestone.skill}</div>
                      <div className={styles.milestoneDetails}>
                        {milestone.level} • {milestone.achieved}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      case 'sensory':
        return (
          <div className={styles.sensoryContent}>
            <div className={styles.sensoryProfile}>
              <h4 className={styles.sectionTitle}>Perfil Sensorial</h4>
              <div className={styles.sensoryGrid}>
                {Object.entries(data.profile).map(([sense, level]) => (
                  <div key={sense} className={styles.sensoryItem}>
                    <div className={styles.sensoryName}>
                      {sense.charAt(0).toUpperCase() + sense.slice(1)}
                    </div>
                    <div className={`${styles.sensoryLevel} ${styles[level.toLowerCase().replace(' ', '')]}`}>
                      {level}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className={styles.strategiesSection}>
              <h4 className={styles.sectionTitle}>Estratégias Recomendadas</h4>
              <div className={styles.strategiesList}>
                {data.strategies.map((strategy, index) => (
                  <div key={index} className={styles.strategyItem}>
                    <span className={styles.strategyIcon}>🌈</span>
                    <span className={styles.strategyText}>{strategy}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )

      default:
        return <div>Dashboard em desenvolvimento...</div>
    }
  }

  return (
    <div className={`${styles.unifiedContainer} ${className || ''}`}>
      {/* Header com Tabs dos Dashboards */}
      <div className={styles.dashboardTabs}>
        <div className={styles.tabsContainer}>
          {Object.entries(dashboardConfigs).map(([key, config]) => (
            <button
              key={key}
              onClick={() => setSelectedDashboard(key)}
              className={`${styles.dashboardTab} ${selectedDashboard === key ? styles.active : ''}`}
              style={{ '--tab-color': config.color }}
              title={config.description}
            >
              <span className={styles.tabIcon}>{config.icon}</span>
              <span className={styles.tabTitle}>{config.title}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Filtros Unificados */}
      <div className={styles.filtersSection}>
        <div className={styles.filterGroup}>
          <label className={styles.filterLabel}>Período:</label>
          <select 
            value={filters.timeRange}
            onChange={(e) => setFilters(prev => ({ ...prev, timeRange: e.target.value }))}
            className={styles.filterSelect}
          >
            <option value="7d">Últimos 7 dias</option>
            <option value="30d">Últimos 30 dias</option>
            <option value="90d">Últimos 90 dias</option>
            <option value="1y">Último ano</option>
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label className={styles.filterLabel}>Usuário:</label>
          <select 
            value={filters.userId}
            onChange={(e) => setFilters(prev => ({ ...prev, userId: e.target.value }))}
            className={styles.filterSelect}
          >
            <option value="all">Todos</option>
            <option value="current">Usuário Atual</option>
          </select>
        </div>

        <div className={styles.filterGroup}>
          <label className={styles.filterLabel}>Tipo de Atividade:</label>
          <select 
            value={filters.activityType}
            onChange={(e) => setFilters(prev => ({ ...prev, activityType: e.target.value }))}
            className={styles.filterSelect}
          >
            <option value="all">Todas</option>
            <option value="games">Jogos</option>
            <option value="exercises">Exercícios</option>
            <option value="assessments">Avaliações</option>
          </select>
        </div>
      </div>

      {/* Conteúdo do Dashboard Selecionado */}
      <div className={styles.dashboardContent}>
        <div className={styles.contentHeader}>
          <h3 className={styles.contentTitle}>
            <span className={styles.contentIcon}>
              {dashboardConfigs[selectedDashboard].icon}
            </span>
            {dashboardConfigs[selectedDashboard].title}
          </h3>
          <p className={styles.contentDescription}>
            {dashboardConfigs[selectedDashboard].description}
          </p>
        </div>

        <div className={styles.contentBody}>
          {renderDashboardContent()}
        </div>
      </div>
    </div>
  )
}

export default UnifiedDashboard
