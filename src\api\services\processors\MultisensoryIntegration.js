/**
 * @file MultisensoryIntegration.js
 * @description Módulo de integração multissensorial para processadores de jogos
 * @version 1.0.0
 */

import { MultisensoryMetricsCollector } from '../multisensoryAnalysis/multisensoryMetrics.js';

/**
 * @class MultisensoryIntegration
 * @description Classe responsável por integrar dados multissensoriais com processadores de jogos
 */
export class MultisensoryIntegration {
  constructor(logger = null) {
    this.logger = logger || {
      info: (...args) => console.info('🔗 [MULTISENSORY-INTEGRATION]', ...args),
      error: (...args) => console.error('❌ [MULTISENSORY-ERROR]', ...args),
      warn: (...args) => console.warn('⚠️ [MULTISENSORY-WARN]', ...args),
      debug: (...args) => console.debug('🔍 [MULTISENSORY-DEBUG]', ...args)
    };
    
    this.multisensoryCollector = null;
    this.isInitialized = false;
    this.activeSessions = new Map();
  }

  /**
   * Inicializa o coletor multissensorial
   * @returns {Promise<boolean>} True se inicializado com sucesso
   */
  async initialize() {
    try {
      if (this.isInitialized) {
        this.logger.warn('MultisensoryIntegration já inicializado');
        return true;
      }

      this.multisensoryCollector = new MultisensoryMetricsCollector();
      this.isInitialized = true;
      
      this.logger.info('✅ MultisensoryIntegration inicializado com sucesso');
      return true;
    } catch (error) {
      this.logger.error('Erro ao inicializar MultisensoryIntegration:', error);
      return false;
    }
  }

  /**
   * Inicia coleta multissensorial para uma sessão de jogo
   * @param {string} sessionId - ID da sessão
   * @param {string} userId - ID do usuário
   * @param {string} gameType - Tipo do jogo
   * @returns {Promise<Object>} Resultado da inicialização
   */
  async startGameSession(sessionId, userId, gameType) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Iniciar coleta multissensorial
      const result = await this.multisensoryCollector.startMetricsCollection(sessionId, userId, {
        gameType,
        maxBufferSize: 150, // Aumentar buffer para jogos
        flushThreshold: 75
      });

      if (result.success) {
        this.activeSessions.set(sessionId, {
          userId,
          gameType,
          startTime: new Date().toISOString(),
          isActive: true
        });

        this.logger.info(`🎮 Sessão multissensorial iniciada para ${gameType}`, {
          sessionId,
          userId,
          sensorsEnabled: result.sensorsEnabled
        });
      }

      return result;
    } catch (error) {
      this.logger.error('Erro ao iniciar sessão multissensorial:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Para coleta multissensorial e gera relatório
   * @param {string} sessionId - ID da sessão
   * @returns {Promise<Object>} Relatório multissensorial
   */
  async stopGameSession(sessionId) {
    try {
      if (!this.multisensoryCollector || !this.activeSessions.has(sessionId)) {
        this.logger.warn(`Sessão ${sessionId} não encontrada ou não ativa`);
        return { success: false, error: 'Session not found' };
      }

      const sessionInfo = this.activeSessions.get(sessionId);
      const report = await this.multisensoryCollector.stopMetricsCollection();

      if (report.success) {
        sessionInfo.isActive = false;
        sessionInfo.endTime = new Date().toISOString();
        sessionInfo.report = report.report;

        this.logger.info(`🏁 Sessão multissensorial finalizada para ${sessionInfo.gameType}`, {
          sessionId,
          dataPoints: report.totalDataPoints
        });
      }

      return report;
    } catch (error) {
      this.logger.error('Erro ao parar sessão multissensorial:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Processa dados multissensoriais junto com métricas do jogo
   * @param {Object} gameMetrics - Métricas normalizadas do jogo
   * @param {string} sessionId - ID da sessão
   * @returns {Promise<Object>} Análise multissensorial integrada
   */
  async processMultisensoryData(gameMetrics, sessionId) {
    try {
      if (!this.multisensoryCollector || !this.activeSessions.has(sessionId)) {
        this.logger.warn(`Sessão ${sessionId} não encontrada - retornando análise básica`);
        return this.generateBasicMultisensoryAnalysis(gameMetrics);
      }

      // Obter dados multissensoriais atuais
      const currentMetrics = await this.multisensoryCollector.getCurrentMetrics();
      
      if (!currentMetrics) {
        this.logger.warn('Dados multissensoriais não disponíveis - usando análise básica');
        return this.generateBasicMultisensoryAnalysis(gameMetrics);
      }

      // Processar integração com dados do jogo
      const multisensoryAnalysis = await this.multisensoryCollector.processMultisensoryData(
        gameMetrics,
        currentMetrics.mobileSensors
      );

      // Enriquecer com contexto do jogo
      const sessionInfo = this.activeSessions.get(sessionId);
      const enrichedAnalysis = this.enrichAnalysisWithGameContext(
        multisensoryAnalysis,
        gameMetrics,
        sessionInfo
      );

      this.logger.info(`📊 Dados multissensoriais processados para ${sessionInfo.gameType}`, {
        sessionId,
        confidence: enrichedAnalysis.confidence || 0.5
      });

      return enrichedAnalysis;
    } catch (error) {
      this.logger.error('Erro ao processar dados multissensoriais:', error);
      return this.generateBasicMultisensoryAnalysis(gameMetrics);
    }
  }

  /**
   * Gera análise multissensorial básica quando dados completos não estão disponíveis
   * @param {Object} gameMetrics - Métricas do jogo
   * @returns {Object} Análise básica
   */
  generateBasicMultisensoryAnalysis(gameMetrics) {
    return {
      timestamp: new Date().toISOString(),
      sensorData: {
        accelerometer: null,
        gyroscope: null,
        touch: null,
        context: null
      },
      neurodivergencePatterns: {
        stimmingIndicators: { detected: false, confidence: 0.5 },
        sensorySeekingLevel: 0.5,
        regulationPatterns: { selfRegulation: 0.7, externalRegulation: 0.3 },
        anxietyIndicators: { detected: false, level: 0.2 }
      },
      gameCorrelation: {
        performanceCorrelation: this.estimatePerformanceCorrelation(gameMetrics),
        attentionCorrelation: this.estimateAttentionCorrelation(gameMetrics),
        engagementCorrelation: this.estimateEngagementCorrelation(gameMetrics),
        stressCorrelation: this.estimateStressCorrelation(gameMetrics)
      },
      deviceContext: null,
      confidence: 0.3, // Baixa confiança para análise básica
      isBasicAnalysis: true,
      message: 'Análise multissensorial básica - dados de sensores não disponíveis'
    };
  }

  /**
   * Enriquece análise multissensorial com contexto do jogo
   * @param {Object} multisensoryAnalysis - Análise multissensorial
   * @param {Object} gameMetrics - Métricas do jogo
   * @param {Object} sessionInfo - Informações da sessão
   * @returns {Object} Análise enriquecida
   */
  enrichAnalysisWithGameContext(multisensoryAnalysis, gameMetrics, sessionInfo) {
    const enriched = {
      ...multisensoryAnalysis,
      gameContext: {
        gameType: sessionInfo.gameType,
        sessionDuration: this.calculateSessionDuration(sessionInfo.startTime),
        gamePerformance: this.extractGamePerformance(gameMetrics),
        gameEngagement: this.extractGameEngagement(gameMetrics)
      },
      therapeuticInsights: this.generateTherapeuticInsights(
        multisensoryAnalysis,
        gameMetrics,
        sessionInfo.gameType
      )
    };

    // Ajustar confiança baseado no tipo de jogo e dados disponíveis
    enriched.confidence = this.calculateAdjustedConfidence(
      multisensoryAnalysis.confidence || 0.5,
      gameMetrics,
      sessionInfo.gameType
    );

    return enriched;
  }

  /**
   * Calcula duração da sessão
   * @param {string} startTime - Timestamp de início
   * @returns {number} Duração em milissegundos
   */
  calculateSessionDuration(startTime) {
    return Date.now() - new Date(startTime).getTime();
  }

  /**
   * Extrai performance do jogo
   * @param {Object} gameMetrics - Métricas do jogo
   * @returns {Object} Performance extraída
   */
  extractGamePerformance(gameMetrics) {
    return {
      accuracy: gameMetrics.accuracy || 0.5,
      responseTime: gameMetrics.responseTime || 1000,
      efficiency: gameMetrics.efficiency || 0.5,
      consistency: gameMetrics.consistency || 0.5
    };
  }

  /**
   * Extrai engajamento do jogo
   * @param {Object} gameMetrics - Métricas do jogo
   * @returns {Object} Engajamento extraído
   */
  extractGameEngagement(gameMetrics) {
    return {
      sessionTime: gameMetrics.sessionTime || 0,
      interactions: gameMetrics.interactions || 0,
      persistence: gameMetrics.persistence || 0.5,
      adaptability: gameMetrics.adaptability || 0.5
    };
  }

  /**
   * Gera insights terapêuticos integrados
   * @param {Object} multisensoryAnalysis - Análise multissensorial
   * @param {Object} gameMetrics - Métricas do jogo
   * @param {string} gameType - Tipo do jogo
   * @returns {Array} Lista de insights terapêuticos
   */
  generateTherapeuticInsights(multisensoryAnalysis, gameMetrics, gameType) {
    const insights = [];

    // Insights baseados em correlações
    if (multisensoryAnalysis.gameCorrelation) {
      const correlation = multisensoryAnalysis.gameCorrelation;

      if (correlation.stressCorrelation > 0.7) {
        insights.push({
          type: 'stress_pattern',
          severity: 'medium',
          description: 'Padrões de estresse detectados durante o jogo',
          recommendation: `Considerar adaptações no ${gameType} para reduzir ansiedade`
        });
      }

      if (correlation.attentionCorrelation < 0.3) {
        insights.push({
          type: 'attention_difficulty',
          severity: 'high',
          description: 'Dificuldades de atenção detectadas nos dados sensoriais',
          recommendation: 'Implementar estratégias de foco e concentração'
        });
      }

      if (correlation.engagementCorrelation > 0.8) {
        insights.push({
          type: 'high_engagement',
          severity: 'positive',
          description: 'Alto engajamento detectado através de dados multissensoriais',
          recommendation: `Continuar com atividades similares ao ${gameType}`
        });
      }
    }

    // Insights baseados em padrões de neurodivergência
    if (multisensoryAnalysis.neurodivergencePatterns) {
      const patterns = multisensoryAnalysis.neurodivergencePatterns;

      if (patterns.stimmingIndicators && patterns.stimmingIndicators.detected) {
        insights.push({
          type: 'stimming_detected',
          severity: 'informational',
          description: 'Padrões de autorregulação sensorial detectados',
          recommendation: 'Monitorar e respeitar necessidades sensoriais individuais'
        });
      }

      if (patterns.sensorySeekingLevel > 0.7) {
        insights.push({
          type: 'high_sensory_seeking',
          severity: 'medium',
          description: 'Alto nível de busca sensorial detectado',
          recommendation: 'Oferecer atividades com rico input sensorial'
        });
      }
    }

    return insights;
  }

  /**
   * Calcula confiança ajustada baseada no tipo de jogo
   * @param {number} baseConfidence - Confiança base
   * @param {Object} gameMetrics - Métricas do jogo
   * @param {string} gameType - Tipo do jogo
   * @returns {number} Confiança ajustada
   */
  calculateAdjustedConfidence(baseConfidence, gameMetrics, gameType) {
    let adjustedConfidence = baseConfidence;

    // Ajustes baseados na qualidade dos dados do jogo
    if (gameMetrics.dataCompleteness) {
      adjustedConfidence *= gameMetrics.dataCompleteness;
    }

    // Ajustes baseados no tipo de jogo
    switch (gameType) {
      case 'ColorMatch':
      case 'MemoryGame':
        // Jogos com mais interações = mais confiança
        adjustedConfidence *= 1.1;
        break;
      case 'CreativePainting':
        // Pintura tem movimentos mais expressivos
        adjustedConfidence *= 1.2;
        break;
      case 'MusicalSequence':
        // Jogos musicais têm padrões temporais únicos
        adjustedConfidence *= 1.15;
        break;
      default:
        // Manter confiança base
        break;
    }

    return Math.min(1.0, Math.max(0.1, adjustedConfidence));
  }

  // Métodos de estimativa para análise básica
  estimatePerformanceCorrelation(gameMetrics) {
    const performance = gameMetrics.accuracy || 0.5;
    return Math.max(0.1, Math.min(0.9, performance * 0.8 + 0.1));
  }

  estimateAttentionCorrelation(gameMetrics) {
    const consistency = gameMetrics.consistency || 0.5;
    return Math.max(0.1, Math.min(0.9, consistency * 0.7 + 0.2));
  }

  estimateEngagementCorrelation(gameMetrics) {
    const engagement = gameMetrics.engagement || 0.5;
    return Math.max(0.2, Math.min(0.9, engagement * 0.9 + 0.1));
  }

  estimateStressCorrelation(gameMetrics) {
    const errors = gameMetrics.errorRate || 0.2;
    return Math.max(0.1, Math.min(0.8, errors * 1.5));
  }

  /**
   * Obtém status das sessões ativas
   * @returns {Object} Status das sessões
   */
  getActiveSessionsStatus() {
    const activeSessions = Array.from(this.activeSessions.entries())
      .filter(([_, info]) => info.isActive)
      .map(([sessionId, info]) => ({
        sessionId,
        gameType: info.gameType,
        userId: info.userId,
        duration: this.calculateSessionDuration(info.startTime)
      }));

    return {
      totalActive: activeSessions.length,
      sessions: activeSessions,
      isInitialized: this.isInitialized
    };
  }

  /**
   * Limpa sessões inativas
   */
  cleanupInactiveSessions() {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    
    for (const [sessionId, sessionInfo] of this.activeSessions.entries()) {
      const sessionTime = new Date(sessionInfo.startTime).getTime();
      
      if (!sessionInfo.isActive && sessionTime < oneHourAgo) {
        this.activeSessions.delete(sessionId);
        this.logger.debug(`Sessão ${sessionId} removida por inatividade`);
      }
    }
  }
}

export default MultisensoryIntegration;
