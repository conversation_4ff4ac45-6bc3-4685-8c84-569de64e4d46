/**
 * 🔧 SCRIPT DE LIMPEZA DE MÉTODOS DUPLICADOS
 * Remove métodos duplicados do GameSpecificProcessors.js
 */

const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'src', 'api', 'services', 'processors', 'GameSpecificProcessors.js');

// Métodos que sabemos que estão duplicados
const duplicatedMethods = [
  'calculateOverallScore',
  'generateRecommendations',
  'calculateConfidence',
  'calculateProgressRate',
  'estimateTimeToTarget',
  'determineCurrentLevel',
  'determineTargetLevel',
  'identifyMilestone',
  'generateNextSteps',
  'identifyStrengthAreas',
  'identifyDevelopmentAreas',
  'determineCognitiveStyle',
  'generateStyleRecommendations',
  'assessAdaptationNeeds',
  'assessPacingNeeds',
  'assessSupportNeeds',
  'generateImmediateRecommendations',
  'generateShortTermRecommendations',
  'generateLongTermRecommendations',
  'generateEnvironmentalRecommendations'
];

function cleanDuplicatedMethods() {
  if (!fs.existsSync(filePath)) {
    console.log('❌ Arquivo não encontrado:', filePath);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let cleanedContent = content;
  let totalRemovals = 0;

  // Para cada método duplicado, manter apenas a primeira ocorrência
  duplicatedMethods.forEach(methodName => {
    const regex = new RegExp(`(\\s*/\\*\\*[\\s\\S]*?\\*/\\s*)?${methodName}\\s*\\([^{]*\\{[\\s\\S]*?(?=\\n\\s*(\\}\\s*\\n\\s*(/\\*\\*|\\w+\\s*\\(|$)))`, 'g');
    const matches = [...cleanedContent.matchAll(regex)];
    
    if (matches.length > 1) {
      console.log(`🔍 Encontradas ${matches.length} ocorrências de ${methodName}`);
      
      // Manter apenas a primeira ocorrência
      for (let i = 1; i < matches.length; i++) {
        cleanedContent = cleanedContent.replace(matches[i][0], '');
        totalRemovals++;
      }
    }
  });

  // Remover linhas vazias excessivas
  cleanedContent = cleanedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

  // Salvar arquivo limpo
  fs.writeFileSync(filePath, cleanedContent, 'utf8');
  
  console.log(`✅ Limpeza concluída! ${totalRemovals} métodos duplicados removidos.`);
  console.log(`📁 Arquivo: ${filePath}`);
}

cleanDuplicatedMethods();
