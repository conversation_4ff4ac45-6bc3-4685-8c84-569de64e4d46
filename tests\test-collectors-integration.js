/**
 * 🧪 TESTE DE INTEGRAÇÃO DOS COLETORES DOS JOGOS
 * Validação completa da arquitetura de coletores para os 8 jogos funcionais
 * Portal Betina V3
 */

import { ColorMatchCollectorsHub } from './src/games/ColorMatch/collectors/index.js';
import { NumberCountingCollectorsHub } from './src/games/ContagemNumeros/collectors/index.js';
import { ImageAssociationCollectorsHub } from './src/games/ImageAssociation/collectors/index.js';
import { MusicalSequenceCollectorsHub } from './src/games/MusicalSequence/collectors/index.js';
import { QuebraCabecaCollectorsHub } from './src/games/QuebraCabeca/collectors/index.js';
import { CreativePaintingCollectorsHub } from './src/games/CreativePainting/collectors/index.js';
import { LetterRecognitionCollectorsHub } from './src/games/LetterRecognition/collectors/index.js';
import { MemoryGameCollectorsHub } from './src/games/MemoryGame/collectors/index.js';

class CollectorsIntegrationTest {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
    
    this.games = [
      {
        name: 'ColorMatch',
        hub: ColorMatchCollectorsHub,
        expectedCollectors: ['colorPerception', 'visualProcessing', 'attentionalSelectivity', 'colorCognition', 'errorPattern']
      },
      {
        name: 'ContagemNumeros',
        hub: NumberCountingCollectorsHub,
        expectedCollectors: ['numericalCognition', 'attentionFocus', 'visualProcessing', 'mathematicalReasoning', 'errorPattern']
      },
      {
        name: 'ImageAssociation',
        hub: ImageAssociationCollectorsHub,
        expectedCollectors: ['associativeMemory', 'visualProcessing', 'categorization', 'flexibility', 'errorPattern']
      },
      {
        name: 'MusicalSequence',
        hub: MusicalSequenceCollectorsHub,
        expectedCollectors: ['auditoryMemoryCollector', 'musicalPatternCollector', 'sequenceExecutionCollector', 'musicalLearningCollector', 'rhythmPatternCollector', 'errorPatternCollector']
      },
      {
        name: 'QuebraCabeca',
        hub: QuebraCabecaCollectorsHub,
        expectedCollectors: ['spatialReasoning', 'problemSolving', 'visualProcessing', 'motorSkills', 'errorPattern']
      },
      {
        name: 'CreativePainting',
        hub: CreativePaintingCollectorsHub,
        expectedCollectors: ['creativityAnalysis', 'motorSkills', 'emotionalExpression', 'artisticStyle', 'engagementMetrics', 'errorPattern']
      },
      {
        name: 'LetterRecognition',
        hub: LetterRecognitionCollectorsHub,
        expectedCollectors: ['letterRecognition', 'visualAttention', 'phonological', 'learningDifficulties', 'errorPattern']
      },
      {
        name: 'MemoryGame',
        hub: MemoryGameCollectorsHub,
        expectedCollectors: ['visualSpatial', 'attentionFocus', 'cognitiveStrategies', 'memoryDifficulties', 'errorPattern']
      }
    ];
  }

  /**
   * Executa todos os testes de integração
   */
  async runAllTests() {
    console.log('🧪 Iniciando Teste de Integração dos Coletores dos Jogos');
    console.log('='.repeat(60));

    for (const game of this.games) {
      await this.testGameCollectors(game);
    }

    this.printSummary();
    return this.testResults;
  }

  /**
   * Testa os coletores de um jogo específico
   */
  async testGameCollectors(gameConfig) {
    console.log(`\n🎮 Testando coletores do ${gameConfig.name}...`);
    
    try {
      // 1. Testar instanciação do hub
      const hub = new gameConfig.hub();
      this.addResult('pass', `${gameConfig.name}: Hub instanciado com sucesso`);

      // 2. Verificar se todos os coletores esperados estão presentes
      const collectorsProperty = hub.collectors || hub._collectors;
      if (!collectorsProperty) {
        this.addResult('fail', `${gameConfig.name}: Propriedade 'collectors' não encontrada`);
        return;
      }

      // 3. Verificar coletores individuais
      const presentCollectors = Object.keys(collectorsProperty);
      console.log(`   📋 Coletores encontrados: ${presentCollectors.join(', ')}`);

      for (const expectedCollector of gameConfig.expectedCollectors) {
        if (presentCollectors.includes(expectedCollector)) {
          this.addResult('pass', `${gameConfig.name}: Coletor '${expectedCollector}' presente`);
          
          // Testar se o coletor tem método analyze
          const collector = collectorsProperty[expectedCollector];
          if (typeof collector.analyze === 'function') {
            this.addResult('pass', `${gameConfig.name}: Coletor '${expectedCollector}' tem método analyze`);
          } else {
            this.addResult('warning', `${gameConfig.name}: Coletor '${expectedCollector}' não tem método analyze`);
          }
        } else {
          this.addResult('fail', `${gameConfig.name}: Coletor '${expectedCollector}' ausente`);
        }
      }

      // 4. Verificar ErrorPatternCollector especificamente
      await this.testErrorPatternCollector(gameConfig.name, hub);

      // 5. Testar método runCompleteAnalysis
      if (typeof hub.runCompleteAnalysis === 'function') {
        this.addResult('pass', `${gameConfig.name}: Método 'runCompleteAnalysis' presente`);
        
        // Teste básico com dados mock
        const mockData = this.generateMockGameData(gameConfig.name);
        try {
          await hub.runCompleteAnalysis(mockData);
          this.addResult('pass', `${gameConfig.name}: runCompleteAnalysis executado sem erros`);
        } catch (error) {
          this.addResult('warning', `${gameConfig.name}: runCompleteAnalysis gerou erro: ${error.message}`);
        }
      } else {
        this.addResult('fail', `${gameConfig.name}: Método 'runCompleteAnalysis' ausente`);
      }

    } catch (error) {
      this.addResult('fail', `${gameConfig.name}: Erro na instanciação: ${error.message}`);
    }
  }

  /**
   * Testa especificamente o ErrorPatternCollector
   */
  async testErrorPatternCollector(gameName, hub) {
    const collectorsProperty = hub.collectors || hub._collectors;
    const errorCollector = collectorsProperty.errorPattern;

    if (errorCollector) {
      this.addResult('pass', `${gameName}: ErrorPatternCollector presente`);
      
      // Verificar métodos essenciais do ErrorPatternCollector
      const essentialMethods = ['analyze', 'collectError', 'getErrorPatterns'];
      for (const method of essentialMethods) {
        if (typeof errorCollector[method] === 'function') {
          this.addResult('pass', `${gameName}: ErrorPatternCollector.${method} presente`);
        } else {
          this.addResult('warning', `${gameName}: ErrorPatternCollector.${method} ausente`);
        }
      }
    } else {
      this.addResult('fail', `${gameName}: ErrorPatternCollector ausente`);
    }
  }

  /**
   * Gera dados mock para teste
   */
  generateMockGameData(gameName) {
    const baseData = {
      gameId: gameName.toLowerCase(),
      sessionId: 'test-session-' + Date.now(),
      playerId: 'test-player',
      timestamp: new Date().toISOString(),
      duration: 120000,
      score: 85,
      difficulty: 'medium',
      attempts: 5,
      errors: 2,
      interactions: []
    };

    // Dados específicos por jogo
    switch (gameName) {
      case 'ColorMatch':
        return {
          ...baseData,
          colorMatches: 8,
          colorErrors: 2,
          avgResponseTime: 1500,
          colorAccuracy: 0.8
        };
      
      case 'ContagemNumeros':
        return {
          ...baseData,
          numbersCount: 10,
          countingErrors: 1,
          numericalAccuracy: 0.9,
          processingSpeed: 'normal'
        };
      
      case 'MemoryGame':
        return {
          ...baseData,
          memoryPairs: 8,
          memoryErrors: 3,
          memoryAccuracy: 0.75,
          retentionRate: 0.85
        };
      
      default:
        return baseData;
    }
  }

  /**
   * Adiciona resultado do teste
   */
  addResult(type, message) {
    this.testResults[type === 'pass' ? 'passed' : type === 'fail' ? 'failed' : 'warnings']++;
    this.testResults.details.push({
      type,
      message,
      timestamp: new Date().toISOString()
    });

    const icon = type === 'pass' ? '✅' : type === 'fail' ? '❌' : '⚠️';
    console.log(`   ${icon} ${message}`);
  }

  /**
   * Imprime resumo dos testes
   */
  printSummary() {
    console.log('\n' + '=' * 60);
    console.log('📊 RESUMO DOS TESTES DE INTEGRAÇÃO DOS COLETORES');
    console.log('=' * 60);
    console.log(`✅ Testes Aprovados: ${this.testResults.passed}`);
    console.log(`❌ Testes Falharam: ${this.testResults.failed}`);
    console.log(`⚠️  Avisos: ${this.testResults.warnings}`);
    
    const total = this.testResults.passed + this.testResults.failed + this.testResults.warnings;
    const successRate = total > 0 ? ((this.testResults.passed / total) * 100).toFixed(1) : 0;
    
    console.log(`📈 Taxa de Sucesso: ${successRate}%`);
    
    if (this.testResults.failed === 0) {
      console.log('\n🎉 Todos os coletores estão integrados corretamente!');
    } else {
      console.log('\n🔧 Algumas correções são necessárias nos coletores.');
    }
  }
}

// Executar teste se for chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new CollectorsIntegrationTest();
  await test.runAllTests();
}

export { CollectorsIntegrationTest };
