{"requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireOperatorBeforeLineBreak": true, "requireParenthesesAroundIIFE": true, "requireMultipleVarDecl": "onevar", "requireCommaBeforeLineBreak": true, "requireCamelCaseOrUpperCaseIdentifiers": true, "requireDotNotation": true, "requireSpacesInForStatement": true, "maximumLineLength": {"value": 100, "tabSize": 4, "allowUrlComments": true, "allowRegex": true}, "validateQuoteMarks": {"mark": "\"", "escape": true}, "disallowMixedSpacesAndTabs": "smart", "disallowTrailingWhitespace": true, "disallowMultipleLineStrings": true, "disallowTrailingComma": true, "requireSpaceBeforeBlockStatements": true, "requireSpacesInFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpacesInsideObjectBrackets": "all", "requireSpacesInsideArrayBrackets": "all", "requireSpacesInConditionalExpression": true, "requireSpaceAfterBinaryOperators": true, "requireLineFeedAtFileEnd": true, "requireSpaceBeforeBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "requireSpacesInAnonymousFunctionExpression": {"beforeOpeningCurlyBrace": true}, "requireSpacesInNamedFunctionExpression": {"beforeOpeningCurlyBrace": true}, "validateLineBreaks": "LF", "disallowKeywords": ["with"], "disallowKeywordsOnNewLine": ["else"], "disallowSpacesInFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpaceAfterObjectKeys": true, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowSpaceBeforePostfixUnaryOperators": true, "disallowSpaceBeforeBinaryOperators": [",", ":"], "disallowMultipleLineBreaks": true}