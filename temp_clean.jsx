/**
 * 🔢 CONTAGEM DE NÚMEROS V3 - SISTEMA INTEGRADO
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';
import { ContagemNumerosConfig } from './ContagemNumerosConfig.js';
import { ContagemNumerosMetrics } from './ContagemNumerosMetrics.js';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// Hook unificado para integração com backend
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
// Hook específico para análise terapêutica
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🧠 COLETORES AVANÇADOS - Análise cognitiva em tempo real
import { NumberCountingCollectorsHub } from './collectors/index.js';

// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';

// Importa estilos modulares
import styles from './ContagemNumeros.module.css';

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3 - CONTAGEM DE NÚMEROS
const ACTIVITY_TYPES = {
  NUMBER_COUNTING: {
    id: 'number_counting',
    name: 'Contagem Simples',
    icon: '🔢',
    description: 'Conte os objetos na tela e escolha o número correto',
    component: 'NumberCountingActivity'
  },
  SOUND_MATCHING: {
    id: 'sound_matching',
    name: 'Combinação de Sons',
    icon: '🎵',
    description: 'Associe o som do número com a quantidade correta',
    component: 'SoundMatchingActivity'
  },
  NUMBER_ESTIMATION: {
    id: 'number_estimation',
    name: 'Estimativa Numérica',
    icon: '🎯',
    description: 'Estime quantidades sem contar individualmente',
    component: 'NumberEstimationActivity'
  },
  SEQUENCE_COMPLETION: {
    id: 'sequence_completion',
    name: 'Completar Sequência',
    icon: '📝',
    description: 'Complete sequências numéricas crescentes',
    component: 'SequenceCompletionActivity'
  },
  NUMBER_COMPARISON: {
    id: 'number_comparison',
    name: 'Comparação Numérica',
    icon: '⚖️',
    description: 'Compare quantidades e identifique maior/menor',
    component: 'NumberComparisonActivity'
  },
  PATTERN_RECOGNITION: {
    id: 'pattern_recognition',
    name: 'Reconhecimento de Padrões',
    icon: '�',
    description: 'Identifique padrões em sequências numéricas',
    component: 'PatternRecognitionActivity'
  }
};

// 🎯 Configurações expandidas para atividades diversificadas
const DIFFICULTY_CONFIGS = {
  easy: {
    name: 'Fácil',
    range: [1, 5],
    estimationRange: [3, 8],
    sequenceLength: 3,
    comparisonMax: 5,
    patternLength: 4
  },
  medium: {
    name: 'Médio',
    range: [3, 8],
    estimationRange: [5, 12],
    sequenceLength: 4,
    comparisonMax: 8,
    patternLength: 5
  },
  hard: {
    name: 'Avançado',
    range: [6, 12],
    estimationRange: [8, 20],
    sequenceLength: 5,
    comparisonMax: 12,
    patternLength: 6
  }
};

// 🎯 Sequências numéricas para completar
const NUMBER_SEQUENCES = {
  easy: [
    { sequence: [1, 2, 3], missing: 4, options: [4, 5, 6] },
    { sequence: [2, 3, 4], missing: 5, options: [5, 6, 7] },
    { sequence: [3, 4, 5], missing: 6, options: [6, 7, 8] }
  ],
  medium: [
    { sequence: [2, 4, 6], missing: 8, options: [8, 9, 10] },
    { sequence: [1, 3, 5], missing: 7, options: [7, 8, 9] },
    { sequence: [5, 6, 7], missing: 8, options: [8, 9, 10] }
  ],
  hard: [
    { sequence: [3, 6, 9], missing: 12, options: [12, 15, 18] },
    { sequence: [2, 5, 8], missing: 11, options: [11, 14, 17] },
    { sequence: [10, 9, 8], missing: 7, options: [7, 6, 5] }
  ]
};

// 🎯 Padrões numéricos para reconhecimento
const NUMBER_PATTERNS = {
  easy: [
    { pattern: [1, 1, 2, 2], next: [3, 3], description: 'Números dobrados' },
    { pattern: [1, 2, 1, 2], next: [1, 2], description: 'Alternância simples' }
  ],
  medium: [
    { pattern: [1, 2, 3, 1, 2], next: [3], description: 'Sequência repetida' },
    { pattern: [2, 4, 2, 4], next: [2], description: 'Par-par alternado' }
  ],
  hard: [
    { pattern: [1, 3, 5, 7], next: [9], description: 'Números ímpares' },
    { pattern: [2, 4, 8, 16], next: [32], description: 'Dobrar o anterior' }
  ]
};

function ContagemNumerosGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const metricsRef = useRef(new ContagemNumerosMetrics());
  const sessionIdRef = useRef(uuidv4());

  // 🧠 Inicializar coletores avançados de contagem V3
  const [collectorsHub] = useState(() => new NumberCountingCollectorsHub())

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized,
    multisensoryIntegration
  } = useMultisensoryIntegration('number-counting', collectorsHub, {
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
    userId: sessionIdRef.current,
    enableAdvancedMetrics: true,
    enableRealTimeAnalysis: true,
    enableNeurodivergenceSupport: true
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const shouldUseTherapeutic = user?.id && user.id !== 'anonymous' && user.id !== '';
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    userId: shouldUseTherapeutic ? user.id : null 
  });

  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();

  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO V3
  // =====================================================
  
  // Estado do TTS com persistência
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('contagemNumeros_ttsActive');
    return saved !== null ? JSON.parse(saved) : true;
  });
  
  // Função para alternar TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('contagemNumeros_ttsActive', JSON.stringify(newState));
      
      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      
      return newState;
    });
  }, []);
  
  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    // Verificar se TTS está ativo
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }
    
    // Cancelar qualquer fala anterior
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Hook unificado para lógica de jogos - integração com backend
  const {
    startUnifiedSession,
    endUnifiedSession,
    recordInteraction,
    updateMetrics,
    sessionId,
    isSessionActive
  } = useUnifiedGameLogic('contagemnumeros');

  // 🎯 ESTADOS PRINCIPAIS DO JOGO V3 COM SISTEMA DE ATIVIDADES
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);
  const [attemptCount, setAttemptCount] = useState(0);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    score: 0,
    round: 1,
    targetNumber: null,
    selectedAnswer: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: 'easy',
    roundStartTime: null,
    
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.NUMBER_COUNTING.id,
    activityCycle: [
      ACTIVITY_TYPES.NUMBER_COUNTING.id,
      ACTIVITY_TYPES.SOUND_MATCHING.id,
      ACTIVITY_TYPES.NUMBER_ESTIMATION.id,
      ACTIVITY_TYPES.SEQUENCE_COMPLETION.id,
      ACTIVITY_TYPES.NUMBER_COMPARISON.id,
      ACTIVITY_TYPES.PATTERN_RECOGNITION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 10, // Permitir mais rodadas antes de sugerir mudança
    activityRoundCount: 0,
    
    // 🎯 Dados específicos de atividades
    activityData: {
      numberCounting: {
        currentQuestion: null,
        objects: [],
        options: []
      },
      soundMatching: {
        targetNumber: null,
        audioPlayed: false,
        attempts: 0
      },
      numberEstimation: {
        estimationRange: null,
        actualCount: null,
        userEstimate: null
      },
      sequenceCompletion: {
        currentSequence: null,
        userProgress: []
      },
      numberComparison: {
        numbers: [],
        comparisonType: 'greater', // 'greater', 'lesser', 'equal'
        correctAnswer: null
      },
      patternRecognition: {
        currentPattern: null,
        userAnswer: null,
        patternStep: 0
      }
    },
    
    // 🎯 Métricas comportamentais avançadas
    behavioralMetrics: {
      activityPreferences: {},
      responsePatterns: [],
      adaptiveAdjustments: 0,
      engagementLevel: 1.0,
      difficultyProgression: [],
      errorPatterns: {},
      recoveryRate: 1.0,
      attentionSpan: 0,
      cognitiveLoad: 0.5
    },
    
    // 📊 Estatísticas detalhadas por atividade
    activityStats: {
      number_counting: { attempts: 0, correct: 0, averageTime: 0 },
      sound_matching: { attempts: 0, correct: 0, averageTime: 0 },
      number_estimation: { attempts: 0, correct: 0, averageTime: 0 },
      sequence_completion: { attempts: 0, correct: 0, averageTime: 0 },
      number_comparison: { attempts: 0, correct: 0, averageTime: 0 },
      pattern_recognition: { attempts: 0, correct: 0, averageTime: 0 }
    }
  });

  // 🧠 Estados para análise cognitiva avançada
  const [gameStartTime, setGameStartTime] = useState(null);
  const [attemptStartTime, setAttemptStartTime] = useState(null);
  const [gameAttempts, setGameAttempts] = useState([]);
  const [cognitiveAnalysis, setCognitiveAnalysis] = useState(null);
  const [analysisInProgress, setAnalysisInProgress] = useState(false);

  // Instância das métricas
  const [metrics] = useState(() => new ContagemNumerosMetrics());

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metrics && recordInteraction && updateMetrics) {
      metrics.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  // 🧠 FUNÇÃO DE ANÁLISE COGNITIVA AVANÇADA
  const runCognitiveAnalysis = useCallback(async (attempts) => {
    if (analysisInProgress || attempts.length < 3) return;
    
    try {
      setAnalysisInProgress(true);
      
      const gameData = {
        sessionId: sessionId || `session_${Date.now()}`,
        attempts,
        difficulty: gameState.difficulty,
        gameMode: 'standard',
        sessionDuration: Date.now() - gameStartTime,
        totalScore: gameState.score,
        currentLevel: gameState.round
      };
      
      console.log('🧠 Executando análise cognitiva ContagemNumeros...', gameData);
      
      // Executar análise completa dos coletores
      const analysis = await collectorsHub.runCompleteAnalysis(gameData);
      
      setCognitiveAnalysis(analysis);
      
      // Enviar análise para o backend
      if (updateMetrics && analysis && !analysis.error) {
        await updateMetrics({
          type: 'cognitive_analysis',
          gameId: 'contagemnumeros',
          analysis: analysis.integratedAnalysis,
          synthesisMetrics: analysis.synthesisMetrics,
          developmentProfile: analysis.developmentProfile
        });
      }
      
      console.log('✅ Análise cognitiva ContagemNumeros concluída:', analysis);
      
    } catch (error) {
      console.error('❌ Erro na análise cognitiva ContagemNumeros:', error);
    } finally {
      setAnalysisInProgress(false);
    }
  }, [analysisInProgress, sessionId, gameState.difficulty, gameStartTime, gameState.score, gameState.round, collectorsHub, updateMetrics]);

  // 🧠 ANÁLISE FINAL QUANDO JOGO TERMINA
  useEffect(() => {
    // Executar análise final quando o usuário sai do jogo ou quando há dados suficientes
    return () => {
      if (gameAttempts.length >= 3 && !analysisInProgress) {
        runCognitiveAnalysis(gameAttempts);
      }
    };
  }, [gameAttempts.length, gameAttempts, analysisInProgress, runCognitiveAnalysis]);

  // 🎯 FUNÇÕES DE GERAÇÃO DE ATIVIDADES V3
  
  // Gerar conteúdo para atividade atual
  const generateActivityContent = useCallback((activityId, difficulty) => {
    const config = DIFFICULTY_CONFIGS[difficulty];
    
    switch (activityId) {
      case ACTIVITY_TYPES.NUMBER_COUNTING.id:
        return generateNumberCountingActivity(config);
      case ACTIVITY_TYPES.SOUND_MATCHING.id:
        return generateSoundMatchingActivity(config);
      case ACTIVITY_TYPES.NUMBER_ESTIMATION.id:
        return generateEstimationActivity(config);
      case ACTIVITY_TYPES.SEQUENCE_COMPLETION.id:
        return generateSequenceActivity(config);
      case ACTIVITY_TYPES.NUMBER_COMPARISON.id:
        return generateComparisonActivity(config);
      case ACTIVITY_TYPES.PATTERN_RECOGNITION.id:
        return generatePatternActivity(config);
      default:
        return generateNumberCountingActivity(config);
    }
  }, []);

  // Gerar atividade de contagem simples
  const generateNumberCountingActivity = useCallback((config) => {
    const correctCount = Math.floor(Math.random() * (config.range[1] - config.range[0] + 1)) + config.range[0];
    const randomCategory = ContagemNumerosConfig.categories[Math.floor(Math.random() * ContagemNumerosConfig.categories.length)];
    const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
    
    const options = new Set([correctCount]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * (config.range[1] - config.range[0] + 1)) + config.range[0];
      if (wrongOption !== correctCount) {
        options.add(wrongOption);
      }
    }
    
    return {
      type: 'number_counting',
      correctCount,
      object: randomObject,
      category: randomCategory,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Quantos ${randomObject.name.toLowerCase()}s você vê aqui?`
    };
  }, []);

  // Gerar atividade de combinação de sons
  const generateSoundMatchingActivity = useCallback((config) => {
    const targetNumber = Math.floor(Math.random() * (config.range[1] - config.range[0] + 1)) + config.range[0];
    const randomCategory = ContagemNumerosConfig.categories[Math.floor(Math.random() * ContagemNumerosConfig.categories.length)];
    const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
    
    const options = new Set([targetNumber]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * (config.range[1] - config.range[0] + 1)) + config.range[0];
      if (wrongOption !== targetNumber) {
        options.add(wrongOption);
      }
    }
    
    return {
      type: 'sound_matching',
      targetNumber,
      object: randomObject,
      category: randomCategory,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Ouça o número e encontre a quantidade correspondente de ${randomObject.name.toLowerCase()}s`,
      audioText: `Número ${targetNumber}`
    };
  }, []);

  // Gerar atividade de estimativa
  const generateEstimationActivity = useCallback((config) => {
    const actualCount = Math.floor(Math.random() * (config.estimationRange[1] - config.estimationRange[0] + 1)) + config.estimationRange[0];
    const randomCategory = ContagemNumerosConfig.categories[Math.floor(Math.random() * ContagemNumerosConfig.categories.length)];
    const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
    
    // Gerar opções próximas ao valor real para estimativa
    const options = new Set([actualCount]);
    const variance = Math.max(2, Math.floor(actualCount * 0.3));
    
    while (options.size < 4) {
      const variation = Math.floor(Math.random() * variance * 2) - variance;
      const option = actualCount + variation;
      if (option > 0 && option !== actualCount) {
        options.add(option);
      }
    }
    
    return {
      type: 'number_estimation',
      actualCount,
      object: randomObject,
      category: randomCategory,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Estime quantos ${randomObject.name.toLowerCase()}s há (sem contar um por um)`,
      showTime: 3000 // Mostrar por 3 segundos apenas
    };
  }, []);

  // Gerar atividade de sequência
  const generateSequenceActivity = useCallback((config) => {
    const sequences = NUMBER_SEQUENCES[config.name.toLowerCase()] || NUMBER_SEQUENCES.easy;
    const randomSequence = sequences[Math.floor(Math.random() * sequences.length)];
    
    return {
      type: 'sequence_completion',
      sequence: randomSequence.sequence,
      missing: randomSequence.missing,
      options: randomSequence.options,
      instruction: `Complete a sequência numérica: ${randomSequence.sequence.join(', ')}, ?`
    };
  }, []);

  // Gerar atividade de comparação
  const generateComparisonActivity = useCallback((config) => {
    const num1 = Math.floor(Math.random() * config.comparisonMax) + 1;
    const num2 = Math.floor(Math.random() * config.comparisonMax) + 1;
    const randomCategory = ContagemNumerosConfig.categories[Math.floor(Math.random() * ContagemNumerosConfig.categories.length)];
    const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
    
    const comparisonTypes = ['greater', 'lesser'];
    const comparisonType = comparisonTypes[Math.floor(Math.random() * comparisonTypes.length)];
    
    let correctAnswer;
    let instruction;
    
    if (comparisonType === 'greater') {
      correctAnswer = Math.max(num1, num2);
      instruction = `Qual grupo tem MAIS ${randomObject.name.toLowerCase()}s?`;
    } else {
      correctAnswer = Math.min(num1, num2);
      instruction = `Qual grupo tem MENOS ${randomObject.name.toLowerCase()}s?`;
    }
    
    return {
      type: 'number_comparison',
      numbers: [num1, num2],
      correctAnswer,
      comparisonType,
      object: randomObject,
      category: randomCategory,
      options: [num1, num2],
      instruction
    };
  }, []);

  // Gerar atividade de padrões
  const generatePatternActivity = useCallback((config) => {
    const patterns = NUMBER_PATTERNS[config.name.toLowerCase()] || NUMBER_PATTERNS.easy;
    const randomPattern = patterns[Math.floor(Math.random() * patterns.length)];
    
    // Gerar opções erradas para o padrão
    const options = new Set(randomPattern.next);
    while (options.size < 3) {
      const wrongOption = Math.floor(Math.random() * 10) + 1;
      if (!randomPattern.next.includes(wrongOption)) {
        options.add(wrongOption);
      }
    }
    
    return {
      type: 'pattern_recognition',
      pattern: randomPattern.pattern,
      next: randomPattern.next,
      correctAnswer: randomPattern.next[0], // Primeiro elemento da sequência
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Continue o padrão: ${randomPattern.pattern.join(', ')}, ?`,
      description: randomPattern.description
    };
  }, []);

  // 🔄 SISTEMA DE ROTAÇÃO DE ATIVIDADES V3
  const rotateToNextActivity = useCallback(() => {
    setGameState(prev => {
      const nextIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const nextActivity = prev.activityCycle[nextIndex];
      
      // Registrar troca de atividade
      if (recordInteraction) {
        recordInteraction({
          type: 'activity_rotation',
          data: { 
            from: prev.currentActivity, 
            to: nextActivity,
            automatic: true,
            round: prev.round
          }
        });
      }
      
      const newState = {
        ...prev,
        currentActivity: nextActivity,
        activityIndex: nextIndex,
        activityRoundCount: 0,
        round: prev.round + 1
      };
      
      // Gerar novo conteúdo para a atividade
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      // Atualizar dados específicos da atividade
      newState.activityData = {
        ...newState.activityData,
        [newState.currentActivity.replace('_', '')]: activityContent
      };
      
      // Anunciar nova atividade
      const activityName = ACTIVITY_TYPES[nextActivity.toUpperCase().replace('_', '_')]?.name || 'Nova Atividade';
      setTimeout(() => {
        speak(`Nova atividade: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });
      }, 500);
      
      return newState;
    });
  }, [recordInteraction, generateActivityContent, speak]);

  // Verificar se deve sugerir trocar de atividade (não forçar)
  const checkActivityRotation = useCallback(() => {
    setGameState(prev => {
      if (prev.activityRoundCount >= prev.roundsPerActivity) {
        // Apenas sugerir, não forçar a mudança
        console.log(`Sugestão: Você já fez ${prev.activityRoundCount} rodadas desta atividade. Quer tentar uma nova?`);
        // Remover o setTimeout que forçava a mudança
        // setTimeout(rotateToNextActivity, 1000);
      }
      return prev;
    });
  }, [rotateToNextActivity]);

  // 🎉 Feedback sonoro para acertos/erros V3
  const playFeedback = useCallback((isCorrect, selectedAnswer, correctAnswer) => {
    if (isCorrect) {
      speak("Muito bem! Você acertou!", { pitch: 1.3, rate: 0.9 });
    } else {
      const activityName = ACTIVITY_TYPES[gameState.currentActivity.toUpperCase().replace('_', '_')]?.name || 'atividade';
      speak(`Não foi dessa vez. A resposta correta era ${correctAnswer}. Continue praticando esta ${activityName}!`, { 
        pitch: 0.9, 
        rate: 0.7 
      });
    }
  }, [gameState.currentActivity, speak]);

  // Calcular precisão
  const getAccuracy = useCallback(() => {
    if (gameState.totalRounds === 0) return 100;
    const correctAnswers = Object.values(gameState.activityStats).reduce((sum, stat) => sum + stat.correct, 0);
    const totalAttempts = Object.values(gameState.activityStats).reduce((sum, stat) => sum + stat.attempts, 0);
    return totalAttempts > 0 ? Math.round((correctAnswers / totalAttempts) * 100) : 100;
  }, [gameState.activityStats]);

  // 🎯 FUNÇÃO UNIVERSAL DE RESPOSTA V3
  const handleAnswer = useCallback(async (answer) => {
    const responseTime = Date.now() - gameState.roundStartTime;
    let isCorrect = false;
    let correctAnswer = null;

    // Determinar se a resposta está correta baseada na atividade atual
    const currentActivityData = gameState.activityData[gameState.currentActivity.replace('_', '')];
    
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.NUMBER_COUNTING.id:
        correctAnswer = currentActivityData?.correctCount;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.SOUND_MATCHING.id:
        correctAnswer = currentActivityData?.targetNumber;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.NUMBER_ESTIMATION.id:
        correctAnswer = currentActivityData?.actualCount;
        // Estimativa com margem de erro de ±1
        isCorrect = Math.abs(answer - correctAnswer) <= 1;
        break;
      case ACTIVITY_TYPES.SEQUENCE_COMPLETION.id:
        correctAnswer = currentActivityData?.missing;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.NUMBER_COMPARISON.id:
        correctAnswer = currentActivityData?.correctAnswer;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.PATTERN_RECOGNITION.id:
        correctAnswer = currentActivityData?.correctAnswer;
        isCorrect = answer === correctAnswer;
        break;
      default:
        correctAnswer = currentActivityData?.correctCount || answer;
        isCorrect = answer === correctAnswer;
    }

    // 🧠 Registrar tentativa para coletores
    const attemptData = {
      correctAnswer,
      userAnswer: answer,
      isCorrect,
      responseTime,
      difficulty: gameState.difficulty,
      round: gameState.round,
      activity: gameState.currentActivity,
      timestamp: new Date().toISOString(),
      gameState: 'answer_given'
    };

    // Adicionar à lista de tentativas
    const newAttempts = [...gameAttempts, attemptData];
    setGameAttempts(newAttempts);

    // Registrar resposta nas métricas
    if (metrics) {
      metrics.recordAnswer(isCorrect, responseTime, {
        correctAnswer,
        selectedAnswer: answer,
        activity: gameState.currentActivity,
        difficulty: gameState.difficulty,
        round: gameState.round
      });
    }

    // 🔄 Registrar interação multissensorial
    try {
      await recordMultisensoryInteraction('activity_response', {
        selectedAnswer: answer,
        targetAnswer: correctAnswer,
        isCorrect: isCorrect,
        responseTime: responseTime,
        difficulty: gameState.difficulty,
        activity: gameState.currentActivity,
        round: gameState.round
      });
    } catch (error) {
      console.warn('⚠️ Erro ao registrar interação multissensorial:', error);
    }

    // 🧠 Registrar com coletores avançados
    if (collectorsHub) {
      collectorsHub.collectResponseData(attemptData)
        .then((insights) => {
          if (insights) {
            console.log('🧠 Dados coletados pelos coletores V3:', insights);
          }
        })
        .catch(error => console.warn('🧠 Erro na coleta V3:', error));
    }

    // Registrar interação com backend
    if (recordInteraction) {
      recordInteraction({
        type: 'counting_attempt_v3',
        data: attemptData
      });
    }

    // Atualizar estado do jogo
    setGameState(prev => {
      const newState = { ...prev };
      
      // Atualizar estatísticas da atividade atual
      const activityKey = prev.currentActivity;
      if (!newState.activityStats[activityKey]) {
        newState.activityStats[activityKey] = { attempts: 0, correct: 0, averageTime: 0 };
      }
      
      newState.activityStats[activityKey].attempts++;
      if (isCorrect) {
        newState.activityStats[activityKey].correct++;
        newState.score += 10 + (prev.round * 2); // Pontuação progressiva
      }
      
      // Atualizar tempo médio
      const currentStat = newState.activityStats[activityKey];
      currentStat.averageTime = ((currentStat.averageTime * (currentStat.attempts - 1)) + responseTime) / currentStat.attempts;
      
      // Incrementar contador de rodadas da atividade
      newState.activityRoundCount++;
      
      // Atualizar métricas comportamentais
      newState.behavioralMetrics.responsePatterns.push({
        activity: prev.currentActivity,
        response: answer,
        correct: isCorrect,
        time: responseTime,
        timestamp: Date.now()
      });
      
      return newState;
    });

    // 🧠 Executar análise cognitiva a cada 3 tentativas
    if (newAttempts.length % 3 === 0 && newAttempts.length >= 3) {
      runCognitiveAnalysis(newAttempts);
    }

    // 🔊 Feedback sonoro
    setTimeout(() => {
      playFeedback(isCorrect, answer, correctAnswer);
      
      // Próxima rodada após feedback
      setTimeout(() => {
        checkActivityRotation();
        generateNewRound();
      }, isCorrect ? 2000 : 2500);
    }, 500);

  }, [gameState, gameAttempts, metrics, multisensoryIntegration, collectorsHub, recordInteraction, runCognitiveAnalysis, playFeedback, checkActivityRotation]);

  // 🎯 FUNÇÃO PARA GERAR NOVA RODADA V3
  const generateNewRound = useCallback(() => {
    setGameState(prev => {
      const newState = { ...prev };
      newState.roundStartTime = Date.now();
      
      // Gerar novo conteúdo para a atividade atual
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      // Atualizar dados específicos da atividade
      const activityKey = newState.currentActivity.replace('_', '');
      newState.activityData = {
        ...newState.activityData,
        [activityKey]: activityContent
      };
      
      // Anunciar nova rodada se necessário
      setTimeout(() => {
        speak(activityContent.instruction, { rate: 0.8 });
        
        // Para atividades de som, reproduzir o áudio
        if (newState.currentActivity === ACTIVITY_TYPES.SOUND_MATCHING.id) {
          setTimeout(() => {
            speak(activityContent.audioText, { rate: 0.6, pitch: 1.2 });
          }, 1000);
        }
      }, 500);
      
      return newState;
    });
  }, [generateActivityContent, speak]);

  // 🎯 FUNÇÃO PARA TROCAR ATIVIDADE MANUALMENTE
  const switchActivity = useCallback((activityId) => {
    setGameState(prev => {
      const activityIndex = prev.activityCycle.indexOf(activityId);
      if (activityIndex === -1) return prev;
      
      const activityName = ACTIVITY_TYPES[activityId.toUpperCase().replace('_', '_')]?.name || 'Nova Atividade';
      
      // Registrar troca manual de atividade
      if (recordInteraction) {
        recordInteraction({
          type: 'activity_switch',
          data: { 
            from: prev.currentActivity, 
            to: activityId,
            manual: true,
            round: prev.round
          }
        });
      }
      
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityIndex: activityIndex,
        activityRoundCount: 0,
        round: prev.round + 1
      };
      
      // Gerar novo conteúdo
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      newState.activityData = {
        ...newState.activityData,
        [newState.currentActivity.replace('_', '')]: activityContent
      };
      
      // Anunciar atividade
      setTimeout(() => {
        speak(`Atividade alterada para: ${activityName}. ${activityContent.instruction}`, { rate: 0.8 });
      }, 500);
      
      return newState;
    });
  }, [speak, recordInteraction, generateActivityContent]);

  // 🎯 FUNÇÃO PARA INICIAR O JOGO V3
  const startGame = useCallback(async (selectedDifficulty) => {
    // Resetar estado do jogo para V3
    setGameState(prev => ({
      ...prev,
      score: 0,
      round: 1,
      difficulty: selectedDifficulty,
      roundStartTime: Date.now(),
      currentActivity: ACTIVITY_TYPES.NUMBER_COUNTING.id,
      activityIndex: 0,
      activityRoundCount: 0,
      behavioralMetrics: {
        activityPreferences: {},
        responsePatterns: [],
        adaptiveAdjustments: 0,
        engagementLevel: 1.0,
        difficultyProgression: [],
        errorPatterns: {},
        recoveryRate: 1.0,
        attentionSpan: 0,
        cognitiveLoad: 0.5
      },
      activityStats: {
        number_counting: { attempts: 0, correct: 0, averageTime: 0 },
        sound_matching: { attempts: 0, correct: 0, averageTime: 0 },
        number_estimation: { attempts: 0, correct: 0, averageTime: 0 },
        sequence_completion: { attempts: 0, correct: 0, averageTime: 0 },
        number_comparison: { attempts: 0, correct: 0, averageTime: 0 },
        pattern_recognition: { attempts: 0, correct: 0, averageTime: 0 }
      }
    }));
    
    setGameStarted(true);
    setShowStartScreen(false);
    
    // Resetar e inicializar métricas
    if (metrics) {
      metrics.resetSession();
      metrics.startSession(sessionId || `session_${Date.now()}`, user?.id || 'anonymous', selectedDifficulty);
    }
    
    // 🧠 INICIALIZAR COLETORES V3
    setGameStartTime(Date.now());
    setGameAttempts([]);
    setCognitiveAnalysis(null);
    setAnalysisInProgress(false);
    
    // 🔄 Inicializar integração multissensorial V3
    try {
      await initMultisensory(sessionId || `session_${Date.now()}`, {
        difficulty: selectedDifficulty,
        gameMode: 'number_counting_v3',
        activityTypes: Object.keys(ACTIVITY_TYPES),
        userId: user?.id || 'anonymous'
      });
    } catch (error) {
      console.warn('⚠️ Erro ao inicializar sessão multissensorial V3:', error);
    }

    // Iniciar sessão do jogo no backend
    if (startUnifiedSession) {
      startUnifiedSession(selectedDifficulty);
    }
    
    // Gerar primeira rodada
    setTimeout(() => {
      generateNewRound();
    }, 500);
    
    // TTS automático ao iniciar V3
    setTimeout(() => {
      speak(`Bem-vindo ao Contagem de Números V3! Dificuldade: ${selectedDifficulty}. Agora temos 6 atividades diferentes para desenvolver suas habilidades numéricas. Vamos começar!`, {
        rate: 0.8
      });
    }, 1000);
  }, [sessionId, user?.id, metrics, multisensoryIntegration, startUnifiedSession, generateNewRound, speak]);

  // 🏁 FUNÇÃO PARA FINALIZAR SESSÃO
  const finishGameSession = useCallback(async () => {
    try {
      // Finalizar métricas do jogo
      if (metrics) {
        const finalMetrics = metrics.getSessionSummary();
        console.log('📊 Métricas finais do jogo:', finalMetrics);
        
        // Sincronizar com backend se disponível
        if (updateMetrics) {
          await updateMetrics(finalMetrics);
        }
      }
      
      // Executar análise final se houver dados suficientes
      if (gameAttempts.length >= 3 && !analysisInProgress) {
        await runCognitiveAnalysis(gameAttempts);
      }
      
      // 🔄 Finalizar sessão multissensorial
      try {
        if (multisensoryInitialized && multisensoryData) {
          const multisensoryReport = await finalizeMultisensory({
            finalScore: gameState.score,
            finalAccuracy: getAccuracy(),
            totalInteractions: gameState.round - 1,
            sessionDuration: Date.now() - gameStartTime,
            difficulty: gameState.difficulty,
            level: gameState.round
          });
          console.log('🔄 ContagemNumeros: Relatório multissensorial:', multisensoryReport);
        } else {
          console.log('🔄 ContagemNumeros: Relatório multissensorial: null');
        }
      } catch (error) {
        console.warn('⚠️ Erro ao finalizar sessão multissensorial:', error);
      }

      // Finalizar sessão no backend com verificações de segurança
      if (endUnifiedSession && isSessionActive && sessionId) {
        try {
          const result = await endUnifiedSession({
            finalScore: gameState.score,
            accuracy: getAccuracy(),
            totalQuestions: gameState.round - 1,
            completionTime: Date.now()
          });
          
          if (result.success) {
            console.log('✅ Sessão backend finalizada com sucesso');
          } else if (result.gracefulFailure) {
            console.log('ℹ️ Sessão backend já havia sido finalizada');
          } else {
            console.warn('⚠️ Erro ao finalizar sessão backend:', result.error);
          }
        } catch (sessionError) {
          console.warn('⚠️ Erro ao finalizar sessão backend (não crítico):', sessionError.message);
        }
      } else {
        console.log('🔄 Sessão backend não ativa ou já finalizada');
      }
      
      console.log('🏁 Sessão finalizada com sucesso');
    } catch (error) {
      console.error('❌ Erro ao finalizar sessão:', error);
    }
  }, [metrics, updateMetrics, gameAttempts, analysisInProgress, runCognitiveAnalysis, multisensoryIntegration, gameState, getAccuracy, gameStartTime, endUnifiedSession, isSessionActive]);

  // Finalizar sessão quando componente é desmontado
  useEffect(() => {
    return () => {
      if (gameStarted) {
        finishGameSession();
      }
      // Cleanup TTS
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [gameStarted, finishGameSession]);

  // 🎯 Explicar o jogo atual V3
  const explainGame = useCallback(() => {
    const currentActivityData = gameState.activityData[gameState.currentActivity.replace('_', '')];
    if (!currentActivityData) {
      speak('Bem-vindo ao jogo de contagem de números V3! Agora temos 6 atividades diferentes para desenvolver suas habilidades numéricas.');
      return;
    }

    const activityName = ACTIVITY_TYPES[gameState.currentActivity.toUpperCase().replace('_', '_')]?.name || 'Atividade Atual';
    speak(`Atividade atual: ${activityName}. ${currentActivityData.instruction}`);
    
    // Registrar uso de acessibilidade
    if (recordInteraction) {
      recordInteraction({
        type: 'tts_usage_v3',
        data: {
          activity: gameState.currentActivity,
          instruction: currentActivityData.instruction,
          activityName
        }
      });
    }
  }, [gameState, speak, recordInteraction]);

  // 🔊 Repetir a instrução da atividade atual V3
  const repeatInstruction = useCallback(() => {
    const currentActivityData = gameState.activityData[gameState.currentActivity.replace('_', '')];
    if (currentActivityData) {
      speak(currentActivityData.instruction);
    }
  }, [gameState, speak]);

  //  Pronunciar número ao clicar na opção
  const speakNumber = useCallback((number) => {
    speak(number.toString(), { rate: 0.6, pitch: 1.2 });
  }, [speak]);

