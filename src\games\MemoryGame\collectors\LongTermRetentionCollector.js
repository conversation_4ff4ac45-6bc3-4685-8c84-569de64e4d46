/**
 * 🧠 LONG TERM RETENTION COLLECTOR - Portal Betina V3
 * Coletor especializado em análise de retenção de longo prazo
 * Monitora retenção de posições de cartas entre sessões
 */

export class LongTermRetentionCollector {
  constructor() {
    this.name = 'LongTermRetentionCollector';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    this.persistentMemory = this.loadPersistentMemory();
    this.sessionData = {
      currentSession: null,
      retentionTests: [],
      consolidationEvents: []
    };
    
    console.log('🧠 LongTermRetentionCollector inicializado');
  }

  /**
   * Carregar memória persistente (usando variável local ao invés de localStorage)
   */
  loadPersistentMemory() {
    try {
      // Usar backup local ao invés de localStorage (não funciona no Node.js)
      return this.persistentMemoryBackup ? JSON.parse(JSON.stringify(this.persistentMemoryBackup)) : {
        cardPositions: {},
        sessionHistory: [],
        consolidatedMemories: {},
        forgettingCurve: []
      };
    } catch (error) {
      console.warn('🧠 LongTermRetentionCollector: Erro ao carregar dados persistentes', error);
      return {
        cardPositions: {},
        sessionHistory: [],
        consolidatedMemories: {},
        forgettingCurve: []
      };
    }
  }

  /**
   * Salvar memória persistente (usando variável local ao invés de localStorage)
   */
  savePersistentMemory() {
    try {
      // Salvar em variável local - localStorage não funciona no Node.js
      this.persistentMemoryBackup = JSON.parse(JSON.stringify(this.persistentMemory));
    } catch (error) {
      console.warn('🧠 LongTermRetentionCollector: Erro ao salvar dados persistentes', error);
    }
  }

  /**
   * Inicializar nova sessão de coleta
   */
  startSession(sessionId, gameData = {}) {
    this.sessionData.currentSession = {
      sessionId,
      startTime: Date.now(),
      difficulty: gameData.difficulty || 'unknown',
      theme: gameData.theme || 'unknown',
      totalCards: gameData.cardsCount || 0,
      cardLayout: this.extractCardLayout(gameData),
      retentionEvents: [],
      recognitionTests: [],
      consolidationMetrics: {
        recognitionAccuracy: 0,
        timeSinceLastSession: this.calculateTimeSinceLastSession(),
        positionMemoryStrength: 0,
        themeMemoryStrength: 0,
        patternRecognition: 0
      }
    };

    // Testar retenção de sessões anteriores
    this.testPriorSessionRetention();
    
    console.log(`🧠 LongTermRetentionCollector: Nova sessão iniciada - ${sessionId}`);
  }

  /**
   * Extrair layout das cartas para comparação futura
   */
  extractCardLayout(gameData) {
    // Simplificado - em implementação real, extrairia do estado do jogo
    return {
      gridSize: this.calculateGridSize(gameData.cardsCount || 0),
      cardTypes: this.extractCardTypes(gameData),
      positions: {}
    };
  }

  /**
   * Calcular tamanho do grid
   */
  calculateGridSize(cardCount) {
    switch (cardCount) {
      case 4: return { rows: 2, cols: 2 };
      case 6: return { rows: 2, cols: 3 };
      case 8: return { rows: 2, cols: 4 };
      case 12: return { rows: 3, cols: 4 };
      case 16: return { rows: 4, cols: 4 };
      default: return { rows: Math.ceil(Math.sqrt(cardCount)), cols: Math.ceil(Math.sqrt(cardCount)) };
    }
  }

  /**
   * Extrair tipos de cartas do tema
   */
  extractCardTypes(gameData) {
    const themeMapping = {
      'farm_animals': ['pig', 'cow'],
      'tasty_fruits': ['apple', 'banana', 'orange'],
      'transportation': ['car', 'airplane', 'bicycle', 'train'],
      'space_stars': ['star', 'moon', 'rocket', 'planet', 'sun', 'bright_star'],
      'enchanted_garden': ['flower', 'butterfly', 'hibiscus', 'sunflower', 'bee', 'tulip', 'rose', 'daisy']
    };
    
    return themeMapping[gameData.theme] || [];
  }

  /**
   * Calcular tempo desde a última sessão
   */
  calculateTimeSinceLastSession() {
    const history = this.persistentMemory.sessionHistory;
    if (history.length === 0) return 0;
    
    const lastSession = history[history.length - 1];
    return Date.now() - lastSession.endTime;
  }

  /**
   * Testar retenção de sessões anteriores
   */
  testPriorSessionRetention() {
    const session = this.sessionData.currentSession;
    const theme = session.theme;
    const difficulty = session.difficulty;
    
    // Buscar sessões anteriores com mesmo tema/dificuldade
    const relevantSessions = this.persistentMemory.sessionHistory.filter(s => 
      s.theme === theme && s.difficulty === difficulty
    );
    
    if (relevantSessions.length > 0) {
      const lastRelevantSession = relevantSessions[relevantSessions.length - 1];
      const timeGap = Date.now() - lastRelevantSession.endTime;
      
      session.consolidationMetrics.timeSinceLastSession = timeGap;
      
      // Classificar intervalo de retenção
      session.retentionInterval = this.classifyRetentionInterval(timeGap);
      
      console.log(`🧠 LongTermRetentionCollector: Testando retenção após ${timeGap}ms (${session.retentionInterval})`);
    }
  }

  /**
   * Classificar intervalo de retenção
   */
  classifyRetentionInterval(timeGap) {
    const minutes = timeGap / (1000 * 60);
    const hours = timeGap / (1000 * 60 * 60);
    const days = timeGap / (1000 * 60 * 60 * 24);
    
    if (minutes < 30) return 'immediate';
    if (hours < 2) return 'short_delay';
    if (hours < 24) return 'medium_delay';
    if (days < 7) return 'long_delay';
    return 'very_long_delay';
  }

  /**
   * Registrar evento de retenção
   */
  recordInteraction(interactionData) {
    if (!this.sessionData.currentSession) {
      console.warn('🧠 LongTermRetentionCollector: Sessão não iniciada');
      return;
    }

    const timestamp = Date.now();
    const session = this.sessionData.currentSession;
    
    const retentionEvent = {
      timestamp,
      cardId: interactionData.cardId,
      cardPosition: interactionData.position || { row: 0, col: 0, index: 0 },
      isMatch: interactionData.isMatch || false,
      reactionTime: interactionData.reactionTime || 0,
      retentionType: this.determineRetentionType(interactionData),
      priorExposure: this.checkPriorExposure(interactionData),
      recognitionScore: this.calculateRecognitionScore(interactionData)
    };

    // Adicionar ao histórico da sessão
    session.retentionEvents.push(retentionEvent);
    
    // Atualizar posições conhecidas
    this.updateKnownPositions(retentionEvent);
    
    // Atualizar métricas de consolidação
    this.updateConsolidationMetrics(retentionEvent);
    
    console.log('🧠 LongTermRetentionCollector: Evento de retenção registrado', {
      retentionType: retentionEvent.retentionType,
      priorExposure: retentionEvent.priorExposure,
      recognitionScore: retentionEvent.recognitionScore
    });
  }

  /**
   * Determinar tipo de retenção
   */
  determineRetentionType(interactionData) {
    const session = this.sessionData.currentSession;
    const cardPosition = interactionData.position;
    
    // Verificar se a posição foi vista em sessões anteriores
    const positionKey = `${cardPosition.row}-${cardPosition.col}`;
    const knownPositions = this.persistentMemory.cardPositions[session.theme] || {};
    
    if (knownPositions[positionKey]) {
      const lastSeen = knownPositions[positionKey].lastSeen;
      const timeGap = Date.now() - lastSeen;
      
      if (timeGap < 60000) return 'working_memory'; // < 1 minuto
      if (timeGap < 3600000) return 'short_term'; // < 1 hora
      if (timeGap < 86400000) return 'medium_term'; // < 1 dia
      return 'long_term'; // > 1 dia
    }
    
    return 'new_learning';
  }

  /**
   * Verificar exposição prévia
   */
  checkPriorExposure(interactionData) {
    const session = this.sessionData.currentSession;
    const cardPosition = interactionData.position;
    const positionKey = `${cardPosition.row}-${cardPosition.col}`;
    
    const knownPositions = this.persistentMemory.cardPositions[session.theme] || {};
    const positionData = knownPositions[positionKey];
    
    if (!positionData) {
      return {
        hasExposure: false,
        exposureCount: 0,
        lastSeen: null,
        strength: 0
      };
    }
    
    return {
      hasExposure: true,
      exposureCount: positionData.exposureCount || 0,
      lastSeen: positionData.lastSeen,
      strength: positionData.strength || 0
    };
  }

  /**
   * Calcular pontuação de reconhecimento
   */
  calculateRecognitionScore(interactionData) {
    const priorExposure = this.checkPriorExposure(interactionData);
    
    if (!priorExposure.hasExposure) {
      return { score: 0, type: 'new_item' };
    }
    
    const timeGap = Date.now() - priorExposure.lastSeen;
    const reactionTime = interactionData.reactionTime || 5000;
    const isCorrect = interactionData.isMatch || false;
    
    // Score baseado em velocidade e precisão
    let score = 0;
    
    if (isCorrect) {
      score += 0.6; // Bônus por acerto
      
      // Bônus por velocidade (inversamente proporcional ao tempo de reação)
      const speedBonus = Math.max(0, (5000 - reactionTime) / 5000) * 0.3;
      score += speedBonus;
      
      // Bônus por retenção após longo período
      const retentionBonus = this.calculateRetentionBonus(timeGap);
      score += retentionBonus;
    }
    
    return {
      score: Math.min(1.0, score),
      type: isCorrect ? 'recognition_success' : 'recognition_failure',
      components: {
        accuracy: isCorrect ? 0.6 : 0,
        speed: isCorrect ? Math.max(0, (5000 - reactionTime) / 5000) * 0.3 : 0,
        retention: isCorrect ? this.calculateRetentionBonus(timeGap) : 0
      }
    };
  }

  /**
   * Calcular bônus de retenção baseado no tempo
   */
  calculateRetentionBonus(timeGap) {
    const hours = timeGap / (1000 * 60 * 60);
    
    if (hours < 1) return 0.05;
    if (hours < 24) return 0.1;
    if (hours < 168) return 0.15; // 1 semana
    return 0.2; // Mais de 1 semana
  }

  /**
   * Atualizar posições conhecidas
   */
  updateKnownPositions(retentionEvent) {
    const session = this.sessionData.currentSession;
    const theme = session.theme;
    const position = retentionEvent.cardPosition;
    const positionKey = `${position.row}-${position.col}`;
    
    if (!this.persistentMemory.cardPositions[theme]) {
      this.persistentMemory.cardPositions[theme] = {};
    }
    
    const currentData = this.persistentMemory.cardPositions[theme][positionKey] || {
      exposureCount: 0,
      successCount: 0,
      strength: 0,
      firstSeen: retentionEvent.timestamp,
      lastSeen: 0
    };
    
    // Atualizar dados
    currentData.exposureCount++;
    currentData.lastSeen = retentionEvent.timestamp;
    
    if (retentionEvent.isMatch) {
      currentData.successCount++;
    }
    
    // Calcular força da memória (taxa de sucesso ponderada pelo tempo)
    const successRate = currentData.successCount / currentData.exposureCount;
    const recency = this.calculateRecencyFactor(retentionEvent.timestamp - currentData.firstSeen);
    currentData.strength = successRate * recency;
    
    this.persistentMemory.cardPositions[theme][positionKey] = currentData;
    this.savePersistentMemory();
  }

  /**
   * Calcular fator de recência
   */
  calculateRecencyFactor(timeSinceFirst) {
    const days = timeSinceFirst / (1000 * 60 * 60 * 24);
    // Decaimento exponencial suave
    return Math.exp(-days / 30); // Meia-vida de ~30 dias
  }

  /**
   * Atualizar métricas de consolidação
   */
  updateConsolidationMetrics(retentionEvent) {
    const session = this.sessionData.currentSession;
    const metrics = session.consolidationMetrics;
    
    // Atualizar precisão de reconhecimento
    const events = session.retentionEvents;
    const recognitionEvents = events.filter(e => e.retentionType !== 'new_learning');
    
    if (recognitionEvents.length > 0) {
      const successfulRecognitions = recognitionEvents.filter(e => e.isMatch).length;
      metrics.recognitionAccuracy = successfulRecognitions / recognitionEvents.length;
    }
    
    // Atualizar força de memória posicional
    metrics.positionMemoryStrength = this.calculatePositionMemoryStrength();
    
    // Atualizar força de memória temática
    metrics.themeMemoryStrength = this.calculateThemeMemoryStrength();
    
    // Atualizar reconhecimento de padrões
    metrics.patternRecognition = this.calculatePatternRecognition();
  }

  /**
   * Calcular força de memória posicional
   */
  calculatePositionMemoryStrength() {
    const session = this.sessionData.currentSession;
    const theme = session.theme;
    const knownPositions = this.persistentMemory.cardPositions[theme] || {};
    
    const strengths = Object.values(knownPositions).map(pos => pos.strength);
    
    if (strengths.length === 0) return 0;
    
    return strengths.reduce((sum, strength) => sum + strength, 0) / strengths.length;
  }

  /**
   * Calcular força de memória temática
   */
  calculateThemeMemoryStrength() {
    const session = this.sessionData.currentSession;
    const theme = session.theme;
    
    // Buscar histórico de sessões com o mesmo tema
    const themeSessions = this.persistentMemory.sessionHistory.filter(s => s.theme === theme);
    
    if (themeSessions.length === 0) return 0;
    
    // Calcular performance média no tema
    const totalAccuracy = themeSessions.reduce((sum, s) => sum + (s.accuracy || 0), 0);
    return totalAccuracy / themeSessions.length;
  }

  /**
   * Calcular reconhecimento de padrões
   */
  calculatePatternRecognition() {
    const session = this.sessionData.currentSession;
    const events = session.retentionEvents;
    
    if (events.length < 3) return 0;
    
    // Detectar se há melhoria na performance ao longo da sessão
    const firstHalf = events.slice(0, Math.floor(events.length / 2));
    const secondHalf = events.slice(Math.floor(events.length / 2));
    
    const firstHalfAccuracy = firstHalf.filter(e => e.isMatch).length / firstHalf.length;
    const secondHalfAccuracy = secondHalf.filter(e => e.isMatch).length / secondHalf.length;
    
    // Reconhecimento de padrão = melhoria na segunda metade
    return Math.max(0, secondHalfAccuracy - firstHalfAccuracy);
  }

  /**
   * Análise completa dos dados coletados
   */
  async analyze(gameData = {}) {
    const session = this.sessionData.currentSession;
    if (!session) {
      return this.getEmptyAnalysis();
    }

    // Finalizar sessão no histórico
    this.finalizeSessionInHistory(gameData);

    const analysis = {
      collectorName: this.name,
      version: this.version,
      sessionId: session.sessionId,
      timestamp: Date.now(),
      
      // Dados básicos da sessão
      sessionSummary: {
        totalEvents: session.retentionEvents.length,
        sessionDuration: Date.now() - session.startTime,
        difficulty: session.difficulty,
        theme: session.theme,
        retentionInterval: session.retentionInterval || 'first_session',
        completed: gameData.completed || false
      },
      
      // Análise de retenção de longo prazo
      longTermRetention: {
        recognitionAccuracy: session.consolidationMetrics.recognitionAccuracy,
        positionMemoryStrength: session.consolidationMetrics.positionMemoryStrength,
        themeMemoryStrength: session.consolidationMetrics.themeMemoryStrength,
        patternRecognition: session.consolidationMetrics.patternRecognition,
        retentionCurve: this.analyzeLongTermRetentionCurve(),
        consolidationStrength: this.calculateConsolidationStrength()
      },
      
      // Análise de consolidação
      consolidationAnalysis: {
        timeSinceLastSession: session.consolidationMetrics.timeSinceLastSession,
        memoryStability: this.calculateMemoryStability(),
        forgettingRate: this.calculateForgettingRate(),
        learningEfficiency: this.calculateLearningEfficiency(),
        transferCapacity: this.calculateTransferCapacity()
      },
      
      // Análise longitudinal
      longitudinalAnalysis: {
        sessionCount: this.persistentMemory.sessionHistory.length + 1,
        overallProgress: this.calculateOverallProgress(),
        consistencyScore: this.calculateConsistencyScore(),
        improvementRate: this.calculateImprovementRate(),
        retentionTrend: this.analyzeRetentionTrend()
      },
      
      // Padrões de esquecimento
      forgettingPatterns: {
        forgettingCurve: this.analyzeForgettingCurve(),
        criticalPeriods: this.identifyCriticalPeriods(),
        recoveryPatterns: this.analyzeRecoveryPatterns(),
        interferenceEffects: this.analyzeInterferenceEffects()
      },
      
      // Recomendações terapêuticas
      therapeuticRecommendations: this.generateTherapeuticRecommendations(),
      
      // Pontuação de capacidades
      capacityScores: {
        longTermRetention: this.calculateLongTermRetentionScore(),
        memoryConsolidation: this.calculateMemoryConsolidationScore(),
        learningEfficiency: this.calculateLearningEfficiencyScore(),
        knowledgeTransfer: this.calculateKnowledgeTransferScore()
      }
    };

    // Armazenar análise
    this.collectedData.push(analysis);
    
    console.log('🧠 LongTermRetentionCollector: Análise completa gerada', {
      recognitionAccuracy: analysis.longTermRetention.recognitionAccuracy,
      consolidationStrength: analysis.longTermRetention.consolidationStrength,
      therapeuticRecommendations: analysis.therapeuticRecommendations.length
    });

    return analysis;
  }

  /**
   * Finalizar sessão no histórico
   */
  finalizeSessionInHistory(gameData) {
    const session = this.sessionData.currentSession;
    
    const sessionRecord = {
      sessionId: session.sessionId,
      startTime: session.startTime,
      endTime: Date.now(),
      difficulty: session.difficulty,
      theme: session.theme,
      accuracy: this.calculateSessionAccuracy(),
      retentionEvents: session.retentionEvents.length,
      recognitionAccuracy: session.consolidationMetrics.recognitionAccuracy,
      completed: gameData.completed || false
    };
    
    this.persistentMemory.sessionHistory.push(sessionRecord);
    
    // Manter apenas as últimas 50 sessões para evitar acúmulo excessivo
    if (this.persistentMemory.sessionHistory.length > 50) {
      this.persistentMemory.sessionHistory.shift();
    }
    
    this.savePersistentMemory();
  }

  /**
   * Calcular precisão da sessão
   */
  calculateSessionAccuracy() {
    const events = this.sessionData.currentSession.retentionEvents;
    if (events.length === 0) return 0;
    
    const successfulEvents = events.filter(e => e.isMatch).length;
    return successfulEvents / events.length;
  }

  /**
   * Analisar curva de retenção de longo prazo
   */
  analyzeLongTermRetentionCurve() {
    const history = this.persistentMemory.sessionHistory;
    
    if (history.length < 2) {
      return { trend: 'insufficient_data', slope: 0, dataPoints: history.length };
    }
    
    // Analisar tendência de accuracy ao longo das sessões
    const accuracies = history.map(s => s.accuracy || 0);
    const slope = this.calculateLinearRegression(accuracies);
    
    let trend;
    if (slope > 0.02) trend = 'improving';
    else if (slope < -0.02) trend = 'declining';
    else trend = 'stable';
    
    return {
      trend,
      slope,
      dataPoints: history.length,
      currentAccuracy: accuracies[accuracies.length - 1] || 0,
      averageAccuracy: accuracies.reduce((a, b) => a + b, 0) / accuracies.length
    };
  }

  /**
   * Calcular regressão linear simples
   */
  calculateLinearRegression(values) {
    const n = values.length;
    if (n < 2) return 0;
    
    const x = Array.from({ length: n }, (_, i) => i);
    const meanX = x.reduce((a, b) => a + b, 0) / n;
    const meanY = values.reduce((a, b) => a + b, 0) / n;
    
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < n; i++) {
      numerator += (x[i] - meanX) * (values[i] - meanY);
      denominator += (x[i] - meanX) ** 2;
    }
    
    return denominator === 0 ? 0 : numerator / denominator;
  }

  /**
   * Calcular força de consolidação
   */
  calculateConsolidationStrength() {
    const session = this.sessionData.currentSession;
    const metrics = session.consolidationMetrics;
    
    // Força baseada em múltiplos fatores
    const recognitionWeight = 0.4;
    const positionWeight = 0.3;
    const themeWeight = 0.2;
    const patternWeight = 0.1;
    
    return (
      metrics.recognitionAccuracy * recognitionWeight +
      metrics.positionMemoryStrength * positionWeight +
      metrics.themeMemoryStrength * themeWeight +
      metrics.patternRecognition * patternWeight
    );
  }

  /**
   * Calcular estabilidade da memória
   */
  calculateMemoryStability() {
    const theme = this.sessionData.currentSession.theme;
    const knownPositions = this.persistentMemory.cardPositions[theme] || {};
    
    const positions = Object.values(knownPositions);
    if (positions.length === 0) return 0;
    
    // Estabilidade baseada na consistência das forças
    const strengths = positions.map(p => p.strength);
    const avgStrength = strengths.reduce((a, b) => a + b, 0) / strengths.length;
    const variance = strengths.reduce((sum, s) => sum + (s - avgStrength) ** 2, 0) / strengths.length;
    
    // Menor variância = maior estabilidade
    return Math.max(0, 1 - Math.sqrt(variance));
  }

  /**
   * Calcular taxa de esquecimento
   */
  calculateForgettingRate() {
    const history = this.persistentMemory.sessionHistory;
    if (history.length < 3) return 0;
    
    // Analisar declínio na performance entre sessões
    const recentSessions = history.slice(-5);
    const accuracies = recentSessions.map(s => s.accuracy || 0);
    
    let forgettingEvents = 0;
    for (let i = 1; i < accuracies.length; i++) {
      if (accuracies[i] < accuracies[i - 1] * 0.9) { // Declínio > 10%
        forgettingEvents++;
      }
    }
    
    return forgettingEvents / (accuracies.length - 1);
  }

  /**
   * Calcular eficiência de aprendizagem
   */
  calculateLearningEfficiency() {
    const session = this.sessionData.currentSession;
    const events = session.retentionEvents;
    
    if (events.length < 3) return 0;
    
    // Medir melhoria na performance ao longo da sessão
    const firstThird = events.slice(0, Math.floor(events.length / 3));
    const lastThird = events.slice(-Math.floor(events.length / 3));
    
    const initialAccuracy = firstThird.filter(e => e.isMatch).length / firstThird.length;
    const finalAccuracy = lastThird.filter(e => e.isMatch).length / lastThird.length;
    
    return Math.max(0, finalAccuracy - initialAccuracy);
  }

  /**
   * Calcular capacidade de transferência
   */
  calculateTransferCapacity() {
    const currentTheme = this.sessionData.currentSession.theme;
    const allThemes = Object.keys(this.persistentMemory.cardPositions);
    
    if (allThemes.length < 2) return 0;
    
    // Comparar performance entre temas diferentes
    const themePerformances = allThemes.map(theme => {
      const positions = this.persistentMemory.cardPositions[theme] || {};
      const strengths = Object.values(positions).map(p => p.strength);
      return strengths.length > 0 ? strengths.reduce((a, b) => a + b, 0) / strengths.length : 0;
    });
    
    const avgPerformance = themePerformances.reduce((a, b) => a + b, 0) / themePerformances.length;
    const variance = themePerformances.reduce((sum, p) => sum + (p - avgPerformance) ** 2, 0) / themePerformances.length;
    
    // Menor variância entre temas = melhor transferência
    return Math.max(0, 1 - Math.sqrt(variance));
  }

  /**
   * Calcular progresso geral
   */
  calculateOverallProgress() {
    const history = this.persistentMemory.sessionHistory;
    if (history.length < 2) return 0;
    
    const firstSession = history[0];
    const lastSession = history[history.length - 1];
    
    return Math.max(0, (lastSession.accuracy || 0) - (firstSession.accuracy || 0));
  }

  /**
   * Calcular pontuação de consistência
   */
  calculateConsistencyScore() {
    const history = this.persistentMemory.sessionHistory;
    if (history.length < 3) return 0;
    
    const accuracies = history.map(s => s.accuracy || 0);
    const avgAccuracy = accuracies.reduce((a, b) => a + b, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + (acc - avgAccuracy) ** 2, 0) / accuracies.length;
    
    // Consistência = baixa variância
    return Math.max(0, 1 - Math.sqrt(variance));
  }

  /**
   * Calcular taxa de melhoria
   */
  calculateImprovementRate() {
    const curve = this.analyzeLongTermRetentionCurve();
    return Math.max(0, curve.slope);
  }

  /**
   * Analisar tendência de retenção
   */
  analyzeRetentionTrend() {
    const curve = this.analyzeLongTermRetentionCurve();
    return {
      direction: curve.trend,
      strength: Math.abs(curve.slope),
      consistency: this.calculateConsistencyScore()
    };
  }

  /**
   * Analisar curva de esquecimento
   */
  analyzeForgettingCurve() {
    // Análise baseada nos dados persistentes
    const forgettingData = this.persistentMemory.forgettingCurve || [];
    
    if (forgettingData.length < 3) {
      return { pattern: 'insufficient_data', rate: 0 };
    }
    
    // Implementação simplificada - pode ser expandida
    return {
      pattern: 'exponential_decay',
      rate: this.calculateForgettingRate(),
      dataPoints: forgettingData.length
    };
  }

  /**
   * Identificar períodos críticos
   */
  identifyCriticalPeriods() {
    // Identificar quando o esquecimento é mais provável
    return [
      { period: '24_hours', riskLevel: 'high', description: 'Período crítico de consolidação' },
      { period: '1_week', riskLevel: 'medium', description: 'Teste de retenção de médio prazo' },
      { period: '1_month', riskLevel: 'low', description: 'Consolidação de longo prazo' }
    ];
  }

  /**
   * Analisar padrões de recuperação
   */
  analyzeRecoveryPatterns() {
    const efficiency = this.calculateLearningEfficiency();
    
    return {
      recoveryRate: efficiency,
      pattern: efficiency > 0.2 ? 'fast_recovery' : efficiency > 0.1 ? 'moderate_recovery' : 'slow_recovery'
    };
  }

  /**
   * Analisar efeitos de interferência
   */
  analyzeInterferenceEffects() {
    const transferCapacity = this.calculateTransferCapacity();
    
    return {
      interferenceLevel: 1 - transferCapacity,
      type: transferCapacity > 0.7 ? 'minimal_interference' : transferCapacity > 0.4 ? 'moderate_interference' : 'high_interference'
    };
  }

  /**
   * Calcular pontuações de capacidade
   */
  calculateLongTermRetentionScore() {
    const session = this.sessionData.currentSession;
    const recognitionAccuracy = session.consolidationMetrics.recognitionAccuracy;
    const positionStrength = session.consolidationMetrics.positionMemoryStrength;
    const themeStrength = session.consolidationMetrics.themeMemoryStrength;
    
    return Math.min(100, Math.round(
      (recognitionAccuracy * 40 + positionStrength * 30 + themeStrength * 30) * 100
    ));
  }

  calculateMemoryConsolidationScore() {
    const consolidationStrength = this.calculateConsolidationStrength();
    const stability = this.calculateMemoryStability();
    const forgettingRate = this.calculateForgettingRate();
    
    return Math.min(100, Math.round(
      (consolidationStrength * 50 + stability * 30 + (1 - forgettingRate) * 20) * 100
    ));
  }

  calculateLearningEfficiencyScore() {
    const efficiency = this.calculateLearningEfficiency();
    const improvementRate = this.calculateImprovementRate();
    
    return Math.min(100, Math.round((efficiency * 60 + improvementRate * 40) * 100));
  }

  calculateKnowledgeTransferScore() {
    const transferCapacity = this.calculateTransferCapacity();
    const consistency = this.calculateConsistencyScore();
    
    return Math.min(100, Math.round((transferCapacity * 70 + consistency * 30) * 100));
  }

  /**
   * Gerar recomendações terapêuticas
   */
  generateTherapeuticRecommendations() {
    const recommendations = [];
    const session = this.sessionData.currentSession;
    
    // Recomendações baseadas na retenção
    if (session.consolidationMetrics.recognitionAccuracy < 0.6) {
      recommendations.push({
        area: 'long_term_retention',
        priority: 'high',
        title: 'Fortalecer Retenção de Longo Prazo',
        description: 'Baixa precisão no reconhecimento. Implementar técnicas de consolidação.',
        activities: [
          'Revisão espaçada das posições',
          'Técnicas de elaboração semântica',
          'Associações visuais e narrativas'
        ]
      });
    }
    
    // Recomendações baseadas no esquecimento
    const forgettingRate = this.calculateForgettingRate();
    if (forgettingRate > 0.4) {
      recommendations.push({
        area: 'forgetting_prevention',
        priority: 'medium',
        title: 'Reduzir Taxa de Esquecimento',
        description: 'Alta taxa de esquecimento entre sessões.',
        activities: [
          'Intervalos de prática otimizados',
          'Técnicas de recuperação ativa',
          'Consolidação através do sono'
        ]
      });
    }
    
    // Recomendações baseadas na transferência
    const transferCapacity = this.calculateTransferCapacity();
    if (transferCapacity < 0.4) {
      recommendations.push({
        area: 'knowledge_transfer',
        priority: 'medium',
        title: 'Melhorar Transferência de Conhecimento',
        description: 'Dificuldade para generalizar aprendizados entre contextos.',
        activities: [
          'Prática com múltiplos contextos',
          'Identificação de padrões abstratos',
          'Estratégias metacognitivas'
        ]
      });
    }
    
    return recommendations;
  }

  /**
   * Obter análise vazia
   */
  getEmptyAnalysis() {
    return {
      collectorName: this.name,
      version: this.version,
      sessionId: null,
      timestamp: Date.now(),
      error: 'No session data available',
      longTermRetention: null,
      consolidationAnalysis: null,
      longitudinalAnalysis: null,
      forgettingPatterns: null,
      therapeuticRecommendations: [],
      capacityScores: {
        longTermRetention: 0,
        memoryConsolidation: 0,
        learningEfficiency: 0,
        knowledgeTransfer: 0
      }
    };
  }

  /**
   * Finalizar sessão
   */
  endSession() {
    if (this.sessionData.currentSession) {
      console.log(`🧠 LongTermRetentionCollector: Sessão finalizada - ${this.sessionData.currentSession.sessionId}`);
      this.sessionData.currentSession = null;
    }
  }

  /**
   * Obter dados coletados
   */
  getCollectedData() {
    return {
      collectorName: this.name,
      version: this.version,
      isActive: this.isActive,
      totalSessions: this.collectedData.length,
      lastSession: this.sessionData.currentSession,
      persistentMemory: this.persistentMemory,
      collectedData: this.collectedData
    };
  }

  /**
   * Limpar dados
   */
  clearData() {
    this.collectedData = [];
    this.persistentMemory = {
      cardPositions: {},
      sessionHistory: [],
      consolidatedMemories: {},
      forgettingCurve: []
    };
    this.sessionData = {
      currentSession: null,
      retentionTests: [],
      consolidationEvents: []
    };
    this.savePersistentMemory();
    console.log('🧠 LongTermRetentionCollector: Dados limpos');
  }
}
