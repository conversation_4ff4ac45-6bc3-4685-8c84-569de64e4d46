/**
 * 💭 EMOTIONAL REGULATION COLLECTOR - Portal Betina V3
 * Coletor especializado em análise de regulação emocional
 * Monitora capacidade de controle emocional, resiliência e adaptação
 */

export class EmotionalRegulationCollector {
  constructor() {
    this.name = 'EmotionalRegulationCollector';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    this.sessionData = {
      currentSession: null,
      emotionalEvents: [],
      regulationMarkers: [],
      adaptationMetrics: []
    };
    
    console.log('💭 EmotionalRegulationCollector inicializado');
  }

  /**
   * Inicializar nova sessão de coleta
   */
  startSession(sessionId, gameData = {}) {
    this.sessionData.currentSession = {
      sessionId,
      startTime: Date.now(),
      difficulty: gameData.difficulty || 'unknown',
      theme: gameData.theme || 'unknown',
      totalCards: gameData.cardsCount || 0,
      expectedDuration: this.calculateExpectedDuration(gameData),
      emotionalEvents: [],
      emotionalState: {
        baseline: 0.5,
        current: 0.5,
        stability: 1.0,
        lastRegulation: Date.now()
      },
      regulationStrategies: {
        cognitiveReappraisal: 0,
        emotionalSuppression: 0,
        distraction: 0,
        acceptance: 0,
        problemSolving: 0,
        selfSoothing: 0
      },
      stressResponse: {
        threshold: 0.7,
        currentLevel: 0,
        peakLevel: 0,
        recoveryTime: 0,
        adaptationRate: 0
      },
      resilienceMetrics: {
        bounceBackRate: 0,
        persistenceLevel: 0,
        flexibilityScore: 0,
        copingEffectiveness: 0
      },
      emotionalPhases: []
    };
    
    console.log(`💭 EmotionalRegulationCollector: Nova sessão iniciada - ${sessionId}`);
  }

  /**
   * Calcular duração esperada baseada na dificuldade
   */
  calculateExpectedDuration(gameData) {
    const baseTimes = {
      'easy': 90000,     // 1.5 minutos - 8 cartas (4 pares) - Fácil 🚀
      'medium': 150000,  // 2.5 minutos - 12 cartas (6 pares) - Médio 🌈  
      'hard': 240000     // 4 minutos - 16 cartas (8 pares) - Avançado 🏆
    };
    
    return baseTimes[gameData.difficulty] || 150000;
  }

  /**
   * Registrar interação para análise emocional
   */
  recordInteraction(interactionData) {
    if (!this.sessionData.currentSession) {
      console.warn('💭 EmotionalRegulationCollector: Sessão não iniciada');
      return;
    }

    const timestamp = Date.now();
    const session = this.sessionData.currentSession;
    
    const emotionalEvent = {
      timestamp,
      cardId: interactionData.cardId,
      isMatch: interactionData.isMatch || false,
      reactionTime: interactionData.reactionTime || 0,
      sessionProgress: this.calculateSessionProgress(timestamp),
      emotionalState: this.assessEmotionalState(interactionData, timestamp),
      stressLevel: this.calculateStressLevel(interactionData, timestamp),
      regulationAttempt: this.detectRegulationAttempt(interactionData, timestamp),
      adaptationSignals: this.detectAdaptationSignals(interactionData, timestamp),
      copingMechanism: this.identifyCopingMechanism(interactionData, timestamp),
      emotionalVolatility: this.calculateEmotionalVolatility(timestamp)
    };

    // Adicionar ao histórico da sessão
    session.emotionalEvents.push(emotionalEvent);
    
    // Atualizar estado emocional
    this.updateEmotionalState(emotionalEvent);
    
    // Atualizar métricas de estresse
    this.updateStressMetrics(emotionalEvent);
    
    // Atualizar estratégias de regulação
    this.updateRegulationStrategies(emotionalEvent);
    
    // Atualizar métricas de resiliência
    this.updateResilienceMetrics(emotionalEvent);
    
    // Detectar mudanças de fase emocional
    this.detectEmotionalPhaseChanges(emotionalEvent);
    
    console.log('💭 EmotionalRegulationCollector: Evento emocional registrado', {
      emotionalState: emotionalEvent.emotionalState.valence,
      stressLevel: emotionalEvent.stressLevel,
      regulationAttempt: emotionalEvent.regulationAttempt?.strategy
    });
  }

  /**
   * Calcular progresso da sessão
   */
  calculateSessionProgress(timestamp) {
    const session = this.sessionData.currentSession;
    const elapsedTime = timestamp - session.startTime;
    
    return Math.min(1.0, elapsedTime / session.expectedDuration);
  }

  /**
   * Avaliar estado emocional
   */
  assessEmotionalState(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-5);
    
    // Calcular valência emocional
    let valence = 0.5; // neutro
    let arousal = 0.5;  // calmo
    let intensity = 0.5; // moderado
    
    // Análise baseada no resultado da interação
    if (interactionData.isMatch) {
      valence += 0.2; // positivo
      arousal += 0.1; // ligeiramente ativado
      intensity += 0.1;
    } else {
      valence -= 0.15; // negativo
      arousal += 0.2;  // mais ativado
      intensity += 0.15;
    }
    
    // Análise baseada no tempo de reação
    if (interactionData.reactionTime > 5000) {
      valence -= 0.1; // frustração
      arousal += 0.15;
    } else if (interactionData.reactionTime < 1500) {
      valence += 0.1; // confiança
      arousal += 0.05;
    }
    
    // Análise de padrão recente
    if (recentEvents.length >= 3) {
      const recentMatches = recentEvents.filter(e => e.isMatch).length;
      const matchRate = recentMatches / recentEvents.length;
      
      if (matchRate < 0.3) {
        valence -= 0.2; // frustração crescente
        arousal += 0.2;
        intensity += 0.2;
      } else if (matchRate > 0.7) {
        valence += 0.15; // confiança crescente
        arousal -= 0.1;
      }
    }
    
    // Normalizar valores
    valence = Math.max(-1, Math.min(1, valence));
    arousal = Math.max(0, Math.min(1, arousal));
    intensity = Math.max(0, Math.min(1, intensity));
    
    return {
      valence,     // -1 (muito negativo) a +1 (muito positivo)
      arousal,     // 0 (calmo) a 1 (muito ativado)
      intensity,   // 0 (leve) a 1 (intenso)
      category: this.categorizeEmotion(valence, arousal),
      stability: this.calculateEmotionalStability(valence, arousal)
    };
  }

  /**
   * Categorizar emoção baseada em valência e arousal
   */
  categorizeEmotion(valence, arousal) {
    if (valence > 0.3 && arousal > 0.6) return 'excited';      // Empolgado
    if (valence > 0.3 && arousal < 0.4) return 'content';     // Satisfeito
    if (valence < -0.3 && arousal > 0.6) return 'frustrated'; // Frustrado
    if (valence < -0.3 && arousal < 0.4) return 'sad';        // Triste
    if (arousal > 0.7) return 'anxious';                      // Ansioso
    if (arousal < 0.3) return 'calm';                         // Calmo
    
    return 'neutral'; // Neutro
  }

  /**
   * Calcular estabilidade emocional
   */
  calculateEmotionalStability(currentValence, currentArousal) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-5);
    
    if (recentEvents.length < 3) return 1.0;
    
    const valences = recentEvents.map(e => e.emotionalState.valence);
    const arousals = recentEvents.map(e => e.emotionalState.arousal);
    
    valences.push(currentValence);
    arousals.push(currentArousal);
    
    const valenceVariance = this.calculateVariance(valences);
    const arousalVariance = this.calculateVariance(arousals);
    
    const stability = 1 - ((valenceVariance + arousalVariance) / 2);
    return Math.max(0, Math.min(1, stability));
  }

  /**
   * Calcular variância
   */
  calculateVariance(values) {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + (val - mean) ** 2, 0) / values.length;
    return Math.sqrt(variance);
  }

  /**
   * Calcular nível de estresse
   */
  calculateStressLevel(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-10);
    
    let stressLevel = 0;
    
    // Fator de erro consecutivo
    const consecutiveErrors = this.countConsecutiveErrors(recentEvents, interactionData);
    stressLevel += Math.min(0.4, consecutiveErrors * 0.1);
    
    // Fator de tempo de reação
    if (interactionData.reactionTime > 4000) {
      stressLevel += 0.2;
    } else if (interactionData.reactionTime < 1000) {
      stressLevel += 0.1; // pressa pode indicar ansiedade
    }
    
    // Fator de progresso vs expectativa
    const progress = this.calculateSessionProgress(timestamp);
    const expectedMatches = progress * (session.totalCards / 2);
    const actualMatches = session.emotionalEvents.filter(e => e.isMatch).length;
    
    if (actualMatches < expectedMatches * 0.7) {
      stressLevel += 0.2;
    }
    
    // Fator de repetição (memória)
    if (this.hasRepeatedMistakes(recentEvents, interactionData)) {
      stressLevel += 0.15;
    }
    
    return Math.max(0, Math.min(1, stressLevel));
  }

  /**
   * Contar erros consecutivos
   */
  countConsecutiveErrors(recentEvents, currentInteraction) {
    let count = 0;
    
    // Verificar se a interação atual é erro
    if (!currentInteraction.isMatch) count++;
    
    // Contar erros recentes consecutivos
    for (let i = recentEvents.length - 1; i >= 0; i--) {
      if (!recentEvents[i].isMatch) {
        count++;
      } else {
        break;
      }
    }
    
    return count;
  }

  /**
   * Verificar erros repetidos
   */
  hasRepeatedMistakes(recentEvents, currentInteraction) {
    if (currentInteraction.isMatch) return false;
    
    const cardErrors = recentEvents.filter(e => 
      e.cardId === currentInteraction.cardId && !e.isMatch
    );
    
    return cardErrors.length >= 2;
  }

  /**
   * Detectar tentativa de regulação emocional
   */
  detectRegulationAttempt(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-3);
    
    if (recentEvents.length < 2) return null;
    
    const lastEvent = recentEvents[recentEvents.length - 1];
    const timeSinceLastInteraction = timestamp - lastEvent.timestamp;
    
    // Pausa prolongada pode indicar regulação
    if (timeSinceLastInteraction > 3000) {
      return {
        strategy: this.identifyRegulationStrategy(recentEvents, timeSinceLastInteraction),
        duration: timeSinceLastInteraction,
        effectiveness: this.assessRegulationEffectiveness(recentEvents, interactionData),
        trigger: this.identifyRegulationTrigger(recentEvents)
      };
    }
    
    return null;
  }

  /**
   * Identificar estratégia de regulação
   */
  identifyRegulationStrategy(recentEvents, pauseDuration) {
    const hasErrors = recentEvents.some(e => !e.isMatch);
    const highStress = recentEvents.some(e => e.stressLevel > 0.6);
    
    if (pauseDuration > 8000) {
      return 'cognitive_reappraisal'; // Pausa longa = repensar estratégia
    } else if (pauseDuration > 5000 && hasErrors) {
      return 'emotional_suppression'; // Pausa média após erro = controle
    } else if (pauseDuration > 3000 && highStress) {
      return 'self_soothing'; // Pausa para se acalmar
    } else if (hasErrors) {
      return 'problem_solving'; // Pausa para resolver problema
    }
    
    return 'distraction'; // Pausa geral
  }

  /**
   * Avaliar efetividade da regulação
   */
  assessRegulationEffectiveness(recentEvents, currentInteraction) {
    if (recentEvents.length === 0) return 0.5;
    
    const lastEvent = recentEvents[recentEvents.length - 1];
    const stressReduction = Math.max(0, lastEvent.stressLevel - this.calculateStressLevel(currentInteraction, Date.now()));
    const performanceImprovement = currentInteraction.isMatch ? 0.3 : 0;
    
    return Math.min(1, (stressReduction + performanceImprovement) / 2);
  }

  /**
   * Identificar gatilho da regulação
   */
  identifyRegulationTrigger(recentEvents) {
    const errors = recentEvents.filter(e => !e.isMatch).length;
    const avgStress = recentEvents.reduce((sum, e) => sum + e.stressLevel, 0) / recentEvents.length;
    
    if (errors >= 2) return 'multiple_errors';
    if (avgStress > 0.7) return 'high_stress';
    if (errors === 1) return 'single_error';
    
    return 'proactive';
  }

  /**
   * Detectar sinais de adaptação
   */
  detectAdaptationSignals(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    const signals = [];
    
    // Adaptação à dificuldade
    if (this.showsDifficultyAdaptation(interactionData, timestamp)) {
      signals.push('difficulty_adaptation');
    }
    
    // Adaptação estratégica
    if (this.showsStrategyAdaptation(interactionData)) {
      signals.push('strategy_adaptation');
    }
    
    // Adaptação emocional
    if (this.showsEmotionalAdaptation(interactionData)) {
      signals.push('emotional_adaptation');
    }
    
    // Adaptação de ritmo
    if (this.showsPaceAdaptation(interactionData, timestamp)) {
      signals.push('pace_adaptation');
    }
    
    return signals;
  }

  /**
   * Verificar adaptação à dificuldade
   */
  showsDifficultyAdaptation(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    const progress = this.calculateSessionProgress(timestamp);
    
    // Se está na segunda metade do jogo e ainda acertando
    return progress > 0.5 && interactionData.isMatch && interactionData.reactionTime < 4000;
  }

  /**
   * Verificar adaptação estratégica
   */
  showsStrategyAdaptation(interactionData) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-10);
    
    if (recentEvents.length < 5) return false;
    
    // Melhoria no desempenho após uma sequência de erros
    const recentMatches = recentEvents.slice(-5).filter(e => e.isMatch).length;
    const olderMatches = recentEvents.slice(-10, -5).filter(e => e.isMatch).length;
    
    return recentMatches > olderMatches && interactionData.isMatch;
  }

  /**
   * Verificar adaptação emocional
   */
  showsEmotionalAdaptation(interactionData) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-5);
    
    if (recentEvents.length < 3) return false;
    
    // Redução do estresse ao longo do tempo
    const recentStress = recentEvents.slice(-3).reduce((sum, e) => sum + e.stressLevel, 0) / 3;
    const olderStress = recentEvents.slice(0, -3).reduce((sum, e) => sum + e.stressLevel, 0) / Math.max(1, recentEvents.length - 3);
    
    return recentStress < olderStress;
  }

  /**
   * Verificar adaptação de ritmo
   */
  showsPaceAdaptation(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-5);
    
    if (recentEvents.length < 3) return false;
    
    // Estabilização dos tempos de reação
    const recentTimes = recentEvents.map(e => e.reactionTime);
    recentTimes.push(interactionData.reactionTime);
    
    const variance = this.calculateVariance(recentTimes);
    return variance < 1000; // Baixa variabilidade indica adaptação
  }

  /**
   * Identificar mecanismo de enfrentamento
   */
  identifyCopingMechanism(interactionData, timestamp) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-5);
    
    if (recentEvents.length === 0) return 'none';
    
    const hasRecentErrors = recentEvents.filter(e => !e.isMatch).length > 0;
    const avgReactionTime = recentEvents.reduce((sum, e) => sum + e.reactionTime, 0) / recentEvents.length;
    
    if (hasRecentErrors && interactionData.reactionTime > avgReactionTime * 1.5) {
      return 'deliberate_slowing'; // Diminuir velocidade após erro
    } else if (hasRecentErrors && interactionData.reactionTime < avgReactionTime * 0.8) {
      return 'compensatory_speed'; // Acelerar para compensar
    } else if (interactionData.isMatch && recentEvents.every(e => !e.isMatch)) {
      return 'persistence'; // Persistir após erros
    } else if (!hasRecentErrors && interactionData.reactionTime < 2000) {
      return 'confidence_building'; // Construir confiança
    }
    
    return 'maintaining_pace'; // Manter ritmo normal
  }

  /**
   * Calcular volatilidade emocional
   */
  calculateEmotionalVolatility(timestamp) {
    const session = this.sessionData.currentSession;
    const recentEvents = session.emotionalEvents.slice(-8);
    
    if (recentEvents.length < 3) return 0;
    
    const valences = recentEvents.map(e => e.emotionalState.valence);
    const arousals = recentEvents.map(e => e.emotionalState.arousal);
    
    const valenceVolatility = this.calculateVariance(valences);
    const arousalVolatility = this.calculateVariance(arousals);
    
    return (valenceVolatility + arousalVolatility) / 2;
  }

  /**
   * Atualizar estado emocional
   */
  updateEmotionalState(emotionalEvent) {
    const session = this.sessionData.currentSession;
    const state = session.emotionalState;
    
    // Atualizar estado atual
    state.current = emotionalEvent.emotionalState.valence;
    state.stability = emotionalEvent.emotionalState.stability;
    
    // Atualizar tempo da última regulação
    if (emotionalEvent.regulationAttempt) {
      state.lastRegulation = emotionalEvent.timestamp;
    }
  }

  /**
   * Atualizar métricas de estresse
   */
  updateStressMetrics(emotionalEvent) {
    const session = this.sessionData.currentSession;
    const stress = session.stressResponse;
    
    stress.currentLevel = emotionalEvent.stressLevel;
    stress.peakLevel = Math.max(stress.peakLevel, emotionalEvent.stressLevel);
    
    // Calcular taxa de adaptação
    if (session.emotionalEvents.length > 5) {
      const recentAvgStress = session.emotionalEvents.slice(-5)
        .reduce((sum, e) => sum + e.stressLevel, 0) / 5;
      const olderAvgStress = session.emotionalEvents.slice(-10, -5)
        .reduce((sum, e) => sum + e.stressLevel, 0) / 5;
      
      stress.adaptationRate = olderAvgStress - recentAvgStress; // Positivo = melhoria
    }
  }

  /**
   * Atualizar estratégias de regulação
   */
  updateRegulationStrategies(emotionalEvent) {
    const session = this.sessionData.currentSession;
    const strategies = session.regulationStrategies;
    
    if (emotionalEvent.regulationAttempt) {
      const strategy = emotionalEvent.regulationAttempt.strategy;
      const effectiveness = emotionalEvent.regulationAttempt.effectiveness;
      
      // Incrementar uso da estratégia com peso da efetividade
      strategies[strategy] = (strategies[strategy] || 0) + effectiveness;
    }
  }

  /**
   * Atualizar métricas de resiliência
   */
  updateResilienceMetrics(emotionalEvent) {
    const session = this.sessionData.currentSession;
    const resilience = session.resilienceMetrics;
    
    // Bounce back rate - recuperação após estresse
    if (emotionalEvent.stressLevel < 0.3 && session.stressResponse.peakLevel > 0.6) {
      resilience.bounceBackRate += 0.1;
    }
    
    // Persistence level - continuar após dificuldades
    const recentErrors = session.emotionalEvents.slice(-5).filter(e => !e.isMatch).length;
    if (recentErrors >= 2 && emotionalEvent.isMatch) {
      resilience.persistenceLevel += 0.15;
    }
    
    // Flexibility score - adaptação a mudanças
    if (emotionalEvent.adaptationSignals.length > 0) {
      resilience.flexibilityScore += 0.1 * emotionalEvent.adaptationSignals.length;
    }
    
    // Coping effectiveness - efetividade do enfrentamento
    if (emotionalEvent.regulationAttempt) {
      resilience.copingEffectiveness = 
        (resilience.copingEffectiveness + emotionalEvent.regulationAttempt.effectiveness) / 2;
    }
    
    // Normalizar valores
    Object.keys(resilience).forEach(key => {
      resilience[key] = Math.min(1, resilience[key]);
    });
  }

  /**
   * Detectar mudanças de fase emocional
   */
  detectEmotionalPhaseChanges(emotionalEvent) {
    const session = this.sessionData.currentSession;
    const phases = session.emotionalPhases;
    const currentCategory = emotionalEvent.emotionalState.category;
    
    // Primeira fase ou mudança de categoria emocional
    if (phases.length === 0 || 
        phases[phases.length - 1].dominantEmotion !== currentCategory) {
      
      phases.push({
        startTime: emotionalEvent.timestamp,
        startEvent: session.emotionalEvents.length,
        dominantEmotion: currentCategory,
        averageValence: emotionalEvent.emotionalState.valence,
        averageArousal: emotionalEvent.emotionalState.arousal,
        averageStress: emotionalEvent.stressLevel,
        regulationAttempts: emotionalEvent.regulationAttempt ? 1 : 0,
        phaseType: this.classifyEmotionalPhase(emotionalEvent)
      });
    } else {
      // Atualizar fase atual
      const currentPhase = phases[phases.length - 1];
      const eventCount = session.emotionalEvents.length - currentPhase.startEvent + 1;
      
      currentPhase.averageValence = 
        (currentPhase.averageValence * (eventCount - 1) + emotionalEvent.emotionalState.valence) / eventCount;
      currentPhase.averageArousal = 
        (currentPhase.averageArousal * (eventCount - 1) + emotionalEvent.emotionalState.arousal) / eventCount;
      currentPhase.averageStress = 
        (currentPhase.averageStress * (eventCount - 1) + emotionalEvent.stressLevel) / eventCount;
      
      if (emotionalEvent.regulationAttempt) {
        currentPhase.regulationAttempts++;
      }
    }
  }

  /**
   * Classificar fase emocional
   */
  classifyEmotionalPhase(emotionalEvent) {
    const valence = emotionalEvent.emotionalState.valence;
    const arousal = emotionalEvent.emotionalState.arousal;
    const stress = emotionalEvent.stressLevel;
    
    if (stress > 0.7) {
      return 'crisis_management'; // Gestão de crise
    } else if (valence > 0.5 && arousal > 0.6) {
      return 'peak_engagement'; // Engajamento máximo
    } else if (valence < -0.3 && emotionalEvent.regulationAttempt) {
      return 'active_regulation'; // Regulação ativa
    } else if (valence > 0.2 && arousal < 0.4) {
      return 'balanced_state'; // Estado equilibrado
    } else if (valence < -0.2) {
      return 'challenge_phase'; // Fase de desafio
    } else {
      return 'adaptation_phase'; // Fase de adaptação
    }
  }

  /**
   * Análise completa dos dados coletados
   */
  async analyze(gameData = {}) {
    const session = this.sessionData.currentSession;
    if (!session) {
      return this.getEmptyAnalysis();
    }

    const analysis = {
      collectorName: this.name,
      version: this.version,
      sessionId: session.sessionId,
      timestamp: Date.now(),
      
      // Dados básicos da sessão
      sessionSummary: {
        totalEvents: session.emotionalEvents.length,
        sessionDuration: Date.now() - session.startTime,
        difficulty: session.difficulty,
        theme: session.theme,
        completed: gameData.completed || false,
        expectedDuration: session.expectedDuration
      },
      
      // Análise do estado emocional
      emotionalStateAnalysis: {
        baselineValence: session.emotionalState.baseline,
        finalValence: session.emotionalState.current,
        averageValence: this.calculateAverageValence(),
        emotionalRange: this.calculateEmotionalRange(),
        stabilityScore: this.calculateOverallStability(),
        dominantEmotion: this.identifyDominantEmotion(),
        emotionalJourney: this.analyzeEmotionalJourney()
      },
      
      // Análise de estresse
      stressAnalysis: {
        peakStressLevel: session.stressResponse.peakLevel,
        averageStressLevel: this.calculateAverageStress(),
        stressThreshold: session.stressResponse.threshold,
        adaptationRate: session.stressResponse.adaptationRate,
        stressRecoveryTime: this.calculateStressRecoveryTime(),
        stressTriggers: this.identifyStressTriggers(),
        stressResilience: this.calculateStressResilience()
      },
      
      // Análise de regulação emocional
      regulationAnalysis: {
        regulationStrategies: session.regulationStrategies,
        mostUsedStrategy: this.identifyMostUsedStrategy(),
        mostEffectiveStrategy: this.identifyMostEffectiveStrategy(),
        regulationFrequency: this.calculateRegulationFrequency(),
        regulationEffectiveness: this.calculateOverallRegulationEffectiveness(),
        proactiveVsReactive: this.analyzeRegulationTiming()
      },
      
      // Análise de adaptação
      adaptationAnalysis: {
        adaptationSignals: this.analyzeAdaptationSignals(),
        adaptationSpeed: this.calculateAdaptationSpeed(),
        flexibilityScore: session.resilienceMetrics.flexibilityScore,
        learningFromErrors: this.analyzeLearningFromErrors(),
        strategicAdjustments: this.analyzeStrategicAdjustments()
      },
      
      // Análise de resiliência
      resilienceAnalysis: {
        bounceBackRate: session.resilienceMetrics.bounceBackRate,
        persistenceLevel: session.resilienceMetrics.persistenceLevel,
        copingEffectiveness: session.resilienceMetrics.copingEffectiveness,
        emotionalRecovery: this.calculateEmotionalRecovery(),
        stressManagement: this.calculateStressManagement(),
        overallResilience: this.calculateOverallResilience()
      },
      
      // Análise de fases emocionais
      emotionalPhasesAnalysis: {
        totalPhases: session.emotionalPhases.length,
        phases: session.emotionalPhases,
        dominantPhase: this.identifyDominantPhase(),
        phaseTransitions: this.analyzePhaseTransitions(),
        phaseStability: this.calculatePhaseStability()
      },
      
      // Recomendações terapêuticas
      therapeuticRecommendations: this.generateTherapeuticRecommendations(),
      
      // Pontuação de capacidades
      capacityScores: {
        emotionalRegulation: this.calculateEmotionalRegulationScore(),
        stressManagement: this.calculateStressManagementScore(),
        adaptability: this.calculateAdaptabilityScore(),
        resilience: this.calculateResilienceScore()
      }
    };

    // Armazenar análise
    this.collectedData.push(analysis);
    
    console.log('💭 EmotionalRegulationCollector: Análise completa gerada', {
      emotionalRegulation: analysis.capacityScores.emotionalRegulation,
      dominantEmotion: analysis.emotionalStateAnalysis.dominantEmotion,
      therapeuticRecommendations: analysis.therapeuticRecommendations.length
    });

    return analysis;
  }

  /**
   * Calcular valência média
   */
  calculateAverageValence() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    if (events.length === 0) return 0;
    
    const valences = events.map(e => e.emotionalState.valence);
    return valences.reduce((a, b) => a + b, 0) / valences.length;
  }

  /**
   * Calcular amplitude emocional
   */
  calculateEmotionalRange() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    if (events.length === 0) return 0;
    
    const valences = events.map(e => e.emotionalState.valence);
    const maxValence = Math.max(...valences);
    const minValence = Math.min(...valences);
    
    return maxValence - minValence;
  }

  /**
   * Calcular estabilidade geral
   */
  calculateOverallStability() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    if (events.length === 0) return 1;
    
    const stabilities = events.map(e => e.emotionalState.stability);
    return stabilities.reduce((a, b) => a + b, 0) / stabilities.length;
  }

  /**
   * Identificar emoção dominante
   */
  identifyDominantEmotion() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    if (events.length === 0) return 'neutral';
    
    const emotionCounts = {};
    events.forEach(event => {
      const emotion = event.emotionalState.category;
      emotionCounts[emotion] = (emotionCounts[emotion] || 0) + 1;
    });
    
    return Object.entries(emotionCounts)
      .sort(([,a], [,b]) => b - a)[0][0];
  }

  /**
   * Analisar jornada emocional
   */
  analyzeEmotionalJourney() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    if (events.length === 0) return [];
    
    return events.map((event, index) => ({
      timestamp: event.timestamp,
      progress: event.sessionProgress,
      emotion: event.emotionalState.category,
      valence: event.emotionalState.valence,
      arousal: event.emotionalState.arousal,
      stress: event.stressLevel,
      regulation: event.regulationAttempt?.strategy || null
    }));
  }

  /**
   * Calcular estresse médio
   */
  calculateAverageStress() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    if (events.length === 0) return 0;
    
    const stressLevels = events.map(e => e.stressLevel);
    return stressLevels.reduce((a, b) => a + b, 0) / stressLevels.length;
  }

  /**
   * Calcular tempo de recuperação do estresse
   */
  calculateStressRecoveryTime() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    let recoveryTimes = [];
    let highStressStart = null;
    
    events.forEach(event => {
      if (event.stressLevel > 0.7 && !highStressStart) {
        highStressStart = event.timestamp;
      } else if (event.stressLevel < 0.4 && highStressStart) {
        recoveryTimes.push(event.timestamp - highStressStart);
        highStressStart = null;
      }
    });
    
    return recoveryTimes.length > 0 ? 
      recoveryTimes.reduce((a, b) => a + b, 0) / recoveryTimes.length : 0;
  }

  /**
   * Identificar gatilhos de estresse
   */
  identifyStressTriggers() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    const triggers = {
      consecutiveErrors: 0,
      timePresure: 0,
      difficultySpikes: 0,
      memoryFailure: 0
    };
    
    events.forEach((event, index) => {
      if (event.stressLevel > 0.6) {
        const recentEvents = events.slice(Math.max(0, index - 3), index);
        
        if (recentEvents.filter(e => !e.isMatch).length >= 2) {
          triggers.consecutiveErrors++;
        }
        
        if (event.reactionTime < 1500) {
          triggers.timePresure++;
        }
        
        if (this.hasRepeatedMistakes(recentEvents, event)) {
          triggers.memoryFailure++;
        }
      }
    });
    
    return triggers;
  }

  /**
   * Calcular resiliência ao estresse
   */
  calculateStressResilience() {
    const session = this.sessionData.currentSession;
    const avgStress = this.calculateAverageStress();
    const peakStress = session.stressResponse.peakLevel;
    const adaptationRate = Math.max(0, session.stressResponse.adaptationRate);
    
    // Fórmula que considera nível médio, picos e adaptação
    const resilience = 1 - (avgStress * 0.4 + peakStress * 0.3 - adaptationRate * 0.3);
    return Math.max(0, Math.min(1, resilience));
  }

  /**
   * Identificar estratégia mais usada
   */
  identifyMostUsedStrategy() {
    const session = this.sessionData.currentSession;
    const strategies = session.regulationStrategies;
    
    return Object.entries(strategies)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none';
  }

  /**
   * Identificar estratégia mais efetiva
   */
  identifyMostEffectiveStrategy() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents.filter(e => e.regulationAttempt);
    
    if (events.length === 0) return 'none';
    
    const strategyEffectiveness = {};
    
    events.forEach(event => {
      const strategy = event.regulationAttempt.strategy;
      const effectiveness = event.regulationAttempt.effectiveness;
      
      if (!strategyEffectiveness[strategy]) {
        strategyEffectiveness[strategy] = { total: 0, count: 0 };
      }
      
      strategyEffectiveness[strategy].total += effectiveness;
      strategyEffectiveness[strategy].count++;
    });
    
    // Calcular médias
    const avgEffectiveness = {};
    Object.entries(strategyEffectiveness).forEach(([strategy, data]) => {
      avgEffectiveness[strategy] = data.total / data.count;
    });
    
    return Object.entries(avgEffectiveness)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none';
  }

  /**
   * Calcular frequência de regulação
   */
  calculateRegulationFrequency() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    const regulationEvents = events.filter(e => e.regulationAttempt);
    
    return events.length > 0 ? regulationEvents.length / events.length : 0;
  }

  /**
   * Calcular efetividade geral da regulação
   */
  calculateOverallRegulationEffectiveness() {
    const session = this.sessionData.currentSession;
    const regulationEvents = session.emotionalEvents.filter(e => e.regulationAttempt);
    
    if (regulationEvents.length === 0) return 0;
    
    const totalEffectiveness = regulationEvents.reduce((sum, event) => 
      sum + event.regulationAttempt.effectiveness, 0
    );
    
    return totalEffectiveness / regulationEvents.length;
  }

  /**
   * Analisar timing da regulação
   */
  analyzeRegulationTiming() {
    const session = this.sessionData.currentSession;
    const regulationEvents = session.emotionalEvents.filter(e => e.regulationAttempt);
    
    const proactive = regulationEvents.filter(e => 
      e.regulationAttempt.trigger === 'proactive'
    ).length;
    
    const reactive = regulationEvents.length - proactive;
    
    return {
      proactive,
      reactive,
      ratio: regulationEvents.length > 0 ? proactive / regulationEvents.length : 0
    };
  }

  /**
   * Analisar sinais de adaptação
   */
  analyzeAdaptationSignals() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    const signals = {
      difficulty_adaptation: 0,
      strategy_adaptation: 0,
      emotional_adaptation: 0,
      pace_adaptation: 0
    };
    
    events.forEach(event => {
      event.adaptationSignals.forEach(signal => {
        if (signals.hasOwnProperty(signal)) {
          signals[signal]++;
        }
      });
    });
    
    return signals;
  }

  /**
   * Calcular velocidade de adaptação
   */
  calculateAdaptationSpeed() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    if (events.length < 10) return 0;
    
    const firstHalf = events.slice(0, Math.floor(events.length / 2));
    const secondHalf = events.slice(Math.floor(events.length / 2));
    
    const firstHalfSuccess = firstHalf.filter(e => e.isMatch).length / firstHalf.length;
    const secondHalfSuccess = secondHalf.filter(e => e.isMatch).length / secondHalf.length;
    
    return Math.max(0, secondHalfSuccess - firstHalfSuccess);
  }

  /**
   * Analisar aprendizagem com erros
   */
  analyzeLearningFromErrors() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    let errorCorrections = 0;
    let totalErrors = 0;
    
    events.forEach((event, index) => {
      if (!event.isMatch) {
        totalErrors++;
        
        // Verificar se há melhoria nos próximos 3 eventos
        const nextEvents = events.slice(index + 1, index + 4);
        if (nextEvents.some(e => e.isMatch)) {
          errorCorrections++;
        }
      }
    });
    
    return totalErrors > 0 ? errorCorrections / totalErrors : 0;
  }

  /**
   * Analisar ajustes estratégicos
   */
  analyzeStrategicAdjustments() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    if (events.length < 6) return 0;
    
    let adjustments = 0;
    
    for (let i = 5; i < events.length; i++) {
      const recent = events.slice(i - 5, i);
      const recentAvgTime = recent.reduce((sum, e) => sum + e.reactionTime, 0) / recent.length;
      const currentTime = events[i].reactionTime;
      
      // Detectar mudança significativa no padrão
      if (Math.abs(currentTime - recentAvgTime) > recentAvgTime * 0.3) {
        adjustments++;
      }
    }
    
    return adjustments / (events.length - 5);
  }

  /**
   * Calcular recuperação emocional
   */
  calculateEmotionalRecovery() {
    const session = this.sessionData.currentSession;
    const events = session.emotionalEvents;
    
    let recoveries = 0;
    let negativeStates = 0;
    
    events.forEach((event, index) => {
      if (event.emotionalState.valence < -0.3) {
        negativeStates++;
        
        // Verificar recuperação nos próximos eventos
        const nextEvents = events.slice(index + 1, index + 4);
        if (nextEvents.some(e => e.emotionalState.valence > 0)) {
          recoveries++;
        }
      }
    });
    
    return negativeStates > 0 ? recoveries / negativeStates : 1;
  }

  /**
   * Calcular gestão de estresse
   */
  calculateStressManagement() {
    const avgStress = this.calculateAverageStress();
    const stressResilience = this.calculateStressResilience();
    const recoveryTime = this.calculateStressRecoveryTime();
    
    // Combinar métricas (tempo de recuperação normalizado)
    const normalizedRecoveryTime = Math.max(0, 1 - (recoveryTime / 10000)); // 10s como referência
    
    return (stressResilience * 0.5 + (1 - avgStress) * 0.3 + normalizedRecoveryTime * 0.2);
  }

  /**
   * Calcular resiliência geral
   */
  calculateOverallResilience() {
    const session = this.sessionData.currentSession;
    const metrics = session.resilienceMetrics;
    
    return (
      metrics.bounceBackRate * 0.3 +
      metrics.persistenceLevel * 0.25 +
      metrics.flexibilityScore * 0.25 +
      metrics.copingEffectiveness * 0.2
    );
  }

  /**
   * Identificar fase dominante
   */
  identifyDominantPhase() {
    const session = this.sessionData.currentSession;
    const phases = session.emotionalPhases;
    
    if (phases.length === 0) return null;
    
    // Encontrar fase com maior duração
    let dominantPhase = phases[0];
    let maxDuration = 0;
    
    phases.forEach((phase, index) => {
      const endTime = index < phases.length - 1 ? 
        phases[index + 1].startTime : 
        Date.now();
      const duration = endTime - phase.startTime;
      
      if (duration > maxDuration) {
        maxDuration = duration;
        dominantPhase = phase;
      }
    });
    
    return {
      type: dominantPhase.phaseType,
      emotion: dominantPhase.dominantEmotion,
      duration: maxDuration,
      averageValence: dominantPhase.averageValence
    };
  }

  /**
   * Analisar transições de fase
   */
  analyzePhaseTransitions() {
    const session = this.sessionData.currentSession;
    const phases = session.emotionalPhases;
    
    if (phases.length < 2) {
      return { transitions: 0, pattern: 'stable', smoothness: 1 };
    }
    
    const transitions = [];
    let totalValenceChange = 0;
    
    for (let i = 1; i < phases.length; i++) {
      const prev = phases[i - 1];
      const curr = phases[i];
      
      const valenceChange = Math.abs(curr.averageValence - prev.averageValence);
      totalValenceChange += valenceChange;
      
      transitions.push({
        from: prev.phaseType,
        to: curr.phaseType,
        valenceChange
      });
    }
    
    const avgValenceChange = totalValenceChange / transitions.length;
    const smoothness = 1 - Math.min(1, avgValenceChange);
    
    return {
      transitions: transitions.length,
      pattern: this.identifyTransitionPattern(transitions),
      smoothness,
      details: transitions
    };
  }

  /**
   * Identificar padrão de transição
   */
  identifyTransitionPattern(transitions) {
    const patternTypes = transitions.map(t => `${t.from}-${t.to}`);
    const uniquePatterns = new Set(patternTypes);
    
    if (uniquePatterns.size === 1) return 'cyclical';
    if (uniquePatterns.size === transitions.length) return 'progressive';
    
    return 'mixed';
  }

  /**
   * Calcular estabilidade de fase
   */
  calculatePhaseStability() {
    const session = this.sessionData.currentSession;
    const phases = session.emotionalPhases;
    
    if (phases.length === 0) return 1;
    
    const avgRegulationAttempts = phases.reduce((sum, phase) => 
      sum + phase.regulationAttempts, 0
    ) / phases.length;
    
    // Menos tentativas de regulação = maior estabilidade
    return Math.max(0, 1 - (avgRegulationAttempts / 5));
  }

  /**
   * Calcular pontuações de capacidade
   */
  calculateEmotionalRegulationScore() {
    const effectiveness = this.calculateOverallRegulationEffectiveness();
    const frequency = this.calculateRegulationFrequency();
    const stability = this.calculateOverallStability();
    
    return Math.min(100, Math.round((effectiveness * 50 + stability * 30 + frequency * 20) * 100));
  }

  calculateStressManagementScore() {
    const stressManagement = this.calculateStressManagement();
    const resilience = this.calculateStressResilience();
    
    return Math.min(100, Math.round((stressManagement * 60 + resilience * 40) * 100));
  }

  calculateAdaptabilityScore() {
    const adaptationSpeed = this.calculateAdaptationSpeed();
    const flexibilityScore = this.sessionData.currentSession.resilienceMetrics.flexibilityScore;
    const learningFromErrors = this.analyzeLearningFromErrors();
    
    return Math.min(100, Math.round((adaptationSpeed * 40 + flexibilityScore * 30 + learningFromErrors * 30) * 100));
  }

  calculateResilienceScore() {
    const overallResilience = this.calculateOverallResilience();
    const emotionalRecovery = this.calculateEmotionalRecovery();
    
    return Math.min(100, Math.round((overallResilience * 70 + emotionalRecovery * 30) * 100));
  }

  /**
   * Gerar recomendações terapêuticas
   */
  generateTherapeuticRecommendations() {
    const recommendations = [];
    const session = this.sessionData.currentSession;
    
    // Recomendações baseadas no estresse
    const avgStress = this.calculateAverageStress();
    if (avgStress > 0.6) {
      recommendations.push({
        area: 'stress_management',
        priority: 'high',
        title: 'Técnicas de Gestão de Estresse',
        description: 'Níveis elevados de estresse detectados durante a atividade.',
        activities: [
          'Exercícios de respiração profunda',
          'Técnicas de relaxamento muscular progressivo',
          'Mindfulness e meditação guiada',
          'Estratégias de autocontrole emocional'
        ]
      });
    }
    
    // Recomendações baseadas na regulação emocional
    const regulationEffectiveness = this.calculateOverallRegulationEffectiveness();
    if (regulationEffectiveness < 0.5) {
      recommendations.push({
        area: 'emotional_regulation',
        priority: 'medium',
        title: 'Desenvolvimento de Regulação Emocional',
        description: 'Baixa efetividade nas estratégias de regulação emocional.',
        activities: [
          'Treinamento em reestruturação cognitiva',
          'Técnicas de distração adaptativa',
          'Desenvolvimento de autoconsciência emocional',
          'Práticas de autorregulação'
        ]
      });
    }
    
    // Recomendações baseadas na adaptabilidade
    const adaptationSpeed = this.calculateAdaptationSpeed();
    if (adaptationSpeed < 0.3) {
      recommendations.push({
        area: 'adaptability_training',
        priority: 'medium',
        title: 'Treinar Flexibilidade Cognitiva',
        description: 'Dificuldade em adaptar-se a mudanças e desafios.',
        activities: [
          'Exercícios de flexibilidade mental',
          'Treino de resolução de problemas',
          'Atividades de mudança de perspectiva',
          'Jogos de estratégia adaptativa'
        ]
      });
    }
    
    // Recomendações baseadas na resiliência
    const overallResilience = this.calculateOverallResilience();
    if (overallResilience < 0.4) {
      recommendations.push({
        area: 'resilience_building',
        priority: 'high',
        title: 'Fortalecimento da Resiliência',
        description: 'Baixa capacidade de recuperação após adversidades.',
        activities: [
          'Desenvolvimento de pensamento positivo',
          'Técnicas de enfrentamento adaptativo',
          'Fortalecimento da autoeficácia',
          'Construção de rede de apoio emocional'
        ]
      });
    }
    
    return recommendations;
  }

  /**
   * Obter análise vazia
   */
  getEmptyAnalysis() {
    return {
      collectorName: this.name,
      version: this.version,
      sessionId: null,
      timestamp: Date.now(),
      error: 'No session data available',
      emotionalStateAnalysis: null,
      stressAnalysis: null,
      regulationAnalysis: null,
      adaptationAnalysis: null,
      resilienceAnalysis: null,
      emotionalPhasesAnalysis: null,
      therapeuticRecommendations: [],
      capacityScores: {
        emotionalRegulation: 0,
        stressManagement: 0,
        adaptability: 0,
        resilience: 0
      }
    };
  }

  /**
   * Finalizar sessão
   */
  endSession() {
    if (this.sessionData.currentSession) {
      console.log(`💭 EmotionalRegulationCollector: Sessão finalizada - ${this.sessionData.currentSession.sessionId}`);
      this.sessionData.currentSession = null;
    }
  }

  /**
   * Obter dados coletados
   */
  getCollectedData() {
    return {
      collectorName: this.name,
      version: this.version,
      isActive: this.isActive,
      totalSessions: this.collectedData.length,
      lastSession: this.sessionData.currentSession,
      collectedData: this.collectedData
    };
  }

  /**
   * Limpar dados
   */
  clearData() {
    this.collectedData = [];
    this.sessionData = {
      currentSession: null,
      emotionalEvents: [],
      regulationMarkers: [],
      adaptationMetrics: []
    };
    console.log('💭 EmotionalRegulationCollector: Dados limpos');
  }
}
