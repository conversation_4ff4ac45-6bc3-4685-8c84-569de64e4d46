/**
 * @file test-backup-system-real-data.js
 * @description Teste completo do sistema de backup com dados reais
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 INICIANDO TESTE COMPLETO DO SISTEMA DE BACKUP COM DADOS REAIS\n');

/**
 * Teste 1: Verificar se os arquivos foram corrigidos
 */
async function testFileCorrections() {
  console.log('📁 Teste 1: Verificando correções nos arquivos...');
  
  const filesToCheck = [
    'src/api/routes/backup/user-backup.js',
    'src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx',
    'src/utils/realMetrics.js'
  ];
  
  for (const file of filesToCheck) {
    const fullPath = path.join(__dirname, file);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Verificar se ainda tem dados simulados
      const hasSimulatedData = content.includes('// Simular') || 
                               content.includes('simulatedData: true') ||
                               content.includes('Math.random()');
      
      if (hasSimulatedData) {
        console.log(`❌ ${file}: Ainda contém dados simulados`);
      } else {
        console.log(`✅ ${file}: Corrigido para usar dados reais`);
      }
      
      // Verificar funcionalidades específicas
      if (file.includes('user-backup.js')) {
        const hasRealDataFunctions = content.includes('getRealUserProfiles') &&
                                   content.includes('getRealGameMetrics') &&
                                   content.includes('saveBackupToStorage');
        console.log(`   ${hasRealDataFunctions ? '✅' : '❌'} Funções de dados reais implementadas`);
      }
      
      if (file.includes('BackupExportDashboard.jsx')) {
        const hasRealApiCalls = content.includes('await fetch(\'/api/backup/user-data\'') &&
                               !content.includes('simulatedData: true');
        console.log(`   ${hasRealApiCalls ? '✅' : '❌'} Chamadas para API real implementadas`);
      }
      
      if (file.includes('realMetrics.js')) {
        const hasServerIntegration = content.includes('serverMetrics') &&
                                    content.includes('await fetch');
        console.log(`   ${hasServerIntegration ? '✅' : '❌'} Integração com servidor implementada`);
      }
      
    } else {
      console.log(`❌ ${file}: Arquivo não encontrado`);
    }
  }
  
  console.log('');
}

/**
 * Teste 2: Verificar estrutura de dados localStorage
 */
function testLocalStorageStructure() {
  console.log('💾 Teste 2: Verificando estrutura de dados localStorage...');
  
  const expectedKeys = [
    'betina_profiles',
    'betina_user_preferences', 
    'betina_accessibility_settings',
    'gameScores',
    'gameSessions',
    'userProgress',
    'gameMetrics'
  ];
  
  // Simular localStorage para teste
  const mockLocalStorage = {};
  
  // Adicionar dados de exemplo
  mockLocalStorage['betina_profiles'] = JSON.stringify([{
    id: 'user_demo',
    name: 'Usuário Teste',
    email: '<EMAIL>',
    createdAt: new Date().toISOString()
  }]);
  
  mockLocalStorage['gameScores'] = JSON.stringify([
    {
      gameId: 'ColorMatch',
      score: 95,
      accuracy: 85,
      timeSpent: 120000,
      timestamp: new Date().toISOString(),
      userId: 'user_demo'
    }
  ]);
  
  expectedKeys.forEach(key => {
    if (mockLocalStorage[key]) {
      console.log(`✅ ${key}: Estrutura válida`);
      try {
        const data = JSON.parse(mockLocalStorage[key]);
        console.log(`   📊 Contém ${Array.isArray(data) ? data.length : Object.keys(data).length} item(s)`);
      } catch (e) {
        console.log(`   ⚠️ Dados não são JSON válido`);
      }
    } else {
      console.log(`❌ ${key}: Não encontrado (será criado conforme uso)`);
    }
  });
  
  console.log('');
}

/**
 * Teste 3: Verificar APIs de backup
 */
async function testBackupAPIs() {
  console.log('🌐 Teste 3: Verificando APIs de backup...');
  
  const endpoints = [
    { method: 'GET', path: '/api/backup/' },
    { method: 'GET', path: '/api/backup/test' },
    { method: 'GET', path: '/api/backup/status/user_demo' },
    { method: 'POST', path: '/api/backup/user-data' },
    { method: 'POST', path: '/api/backup/validate' }
  ];
  
  console.log('📋 Endpoints esperados:');
  endpoints.forEach(endpoint => {
    console.log(`   ${endpoint.method} ${endpoint.path}`);
  });
  
  console.log('\n💡 Para testar as APIs, execute:');
  console.log('   npm run server');
  console.log('   npm run dev');
  console.log('');
}

/**
 * Teste 4: Verificar integração com dashboard
 */
function testDashboardIntegration() {
  console.log('📊 Teste 4: Verificando integração com dashboard...');
  
  const dashboardPath = path.join(__dirname, 'src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx');
  
  if (fs.existsSync(dashboardPath)) {
    const content = fs.readFileSync(dashboardPath, 'utf8');
    
    const checks = [
      { name: 'Coleta dados do localStorage', test: content.includes('localStorage.getItem') },
      { name: 'Chama API de backup', test: content.includes('fetch(\'/api/backup/') },
      { name: 'Processa dados reais', test: !content.includes('simulatedData: true') },
      { name: 'Valida dados de entrada', test: content.includes('validation') || content.includes('validate') },
      { name: 'Tratamento de erros', test: content.includes('catch') && content.includes('error') }
    ];
    
    checks.forEach(check => {
      console.log(`   ${check.test ? '✅' : '❌'} ${check.name}`);
    });
  } else {
    console.log('❌ Dashboard não encontrado');
  }
  
  console.log('');
}

/**
 * Teste 5: Verificar sistema de métricas reais
 */
function testRealMetricsSystem() {
  console.log('📈 Teste 5: Verificando sistema de métricas reais...');
  
  const metricsPath = path.join(__dirname, 'src/utils/realMetrics.js');
  
  if (fs.existsSync(metricsPath)) {
    const content = fs.readFileSync(metricsPath, 'utf8');
    
    const checks = [
      { name: 'Função getRealMetrics', test: content.includes('export const getRealMetrics') },
      { name: 'Hook useRealMetrics', test: content.includes('export const useRealMetrics') },
      { name: 'Integração com servidor', test: content.includes('serverMetrics') },
      { name: 'Processamento de dados locais', test: content.includes('localStorage') },
      { name: 'Cálculos de progresso', test: content.includes('calculateGameProgress') },
      { name: 'Métricas de IA', test: content.includes('getAIMetrics') }
    ];
    
    checks.forEach(check => {
      console.log(`   ${check.test ? '✅' : '❌'} ${check.name}`);
    });
  } else {
    console.log('❌ Sistema de métricas não encontrado');
  }
  
  console.log('');
}

/**
 * Teste 6: Verificar dados de exemplo
 */
function testSampleData() {
  console.log('📋 Teste 6: Verificando dados de exemplo...');
  
  // Dados de exemplo que deveriam estar sendo coletados
  const sampleGameData = {
    userId: 'user_demo',
    gameId: 'ColorMatch',
    sessionId: Date.now(),
    startTime: new Date().toISOString(),
    endTime: new Date(Date.now() + 120000).toISOString(),
    interactions: [
      { type: 'click', target: 'color-red', timestamp: Date.now(), correct: true },
      { type: 'click', target: 'color-blue', timestamp: Date.now() + 1000, correct: false }
    ],
    finalScore: 85,
    accuracy: 75,
    completionTime: 120000,
    hintsUsed: 2,
    errorsCount: 3
  };
  
  console.log('✅ Estrutura de dados de jogo de exemplo:');
  console.log(`   📊 Score: ${sampleGameData.finalScore}`);
  console.log(`   🎯 Precisão: ${sampleGameData.accuracy}%`);
  console.log(`   ⏱️ Tempo: ${sampleGameData.completionTime/1000}s`);
  console.log(`   🔄 Interações: ${sampleGameData.interactions.length}`);
  
  const sampleUserProfile = {
    id: 'user_demo',
    name: 'Usuário Demo',
    email: '<EMAIL>',
    isPremium: true,
    preferences: {
      theme: 'dark',
      language: 'pt-BR',
      difficulty: 'medium'
    },
    accessibilitySettings: {
      highContrast: false,
      largeText: false,
      reducedMotion: false
    },
    cognitiveProfile: {
      attention: 85,
      memory: 78,
      processing: 82,
      executive: 88
    },
    createdAt: new Date().toISOString(),
    lastLogin: new Date().toISOString()
  };
  
  console.log('\n✅ Estrutura de perfil de usuário de exemplo:');
  console.log(`   👤 Nome: ${sampleUserProfile.name}`);
  console.log(`   💎 Premium: ${sampleUserProfile.isPremium ? 'Sim' : 'Não'}`);
  console.log(`   🧠 Perfil Cognitivo: ${Object.keys(sampleUserProfile.cognitiveProfile).length} métricas`);
  
  console.log('');
}

/**
 * Executar todos os testes
 */
async function runAllTests() {
  console.log('🚀 PORTAL BETINA V3 - TESTE COMPLETO DO SISTEMA DE BACKUP COM DADOS REAIS');
  console.log('='.repeat(80));
  console.log('');
  
  await testFileCorrections();
  testLocalStorageStructure();
  await testBackupAPIs();
  testDashboardIntegration();
  testRealMetricsSystem();
  testSampleData();
  
  console.log('🎉 TESTE COMPLETO FINALIZADO!');
  console.log('');
  console.log('📋 PRÓXIMOS PASSOS:');
  console.log('1. Iniciar o servidor: npm run server');
  console.log('2. Iniciar o frontend: npm run dev');
  console.log('3. Testar backup no dashboard premium');
  console.log('4. Verificar se os dados exportados são reais');
  console.log('5. Validar métricas nos dashboards');
  console.log('');
  console.log('💡 DICA: Acesse http://localhost:5173 e faça login como usuário premium para testar');
}

// Executar testes
runAllTests().catch(console.error);
