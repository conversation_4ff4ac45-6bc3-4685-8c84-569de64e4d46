/**
 * 🔍 AUDITORIA COMPLETA DO BANCO DE DADOS
 * Verifica tabelas, segurança, configuração e acesso aos dados
 * Portal Betina V3
 */

import { Client } from 'pg';

// Configuração do banco
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER || 'betina_user',
  password: process.env.DB_PASSWORD || 'betina_password',
  database: process.env.DB_NAME || 'betina_db'
};

// Lista de todos os jogos funcionais
const GAMES_LIST = [
  'ColorMatch',
  'ContagemNumeros', 
  'ImageAssociation',
  'MemoryGame',
  'MusicalSequence',
  'PadroesVisuais',
  'QuebraCabeca',
  'CreativePainting',
  'LetterRecognition'
];

class DatabaseCompleteAudit {
  constructor() {
    this.client = null;
    this.auditResults = {
      connection: false,
      security: false,
      tables: {
        core: false,
        games: {},
        multisensory: false
      },
      permissions: false,
      data_access: false,
      dashboard_integration: false,
      errors: []
    };
  }

  async runCompleteAudit() {
    console.log('🔍 INICIANDO AUDITORIA COMPLETA DO BANCO DE DADOS');
    console.log('='.repeat(70));

    try {
      // 1. Conexão e configuração
      await this.testConnection();
      await this.checkSecurity();
      
      // 2. Estrutura do banco
      await this.checkCoreTables();
      await this.checkGameTables();
      await this.checkMultisensoryTables();
      
      // 3. Permissões e acesso
      await this.checkPermissions();
      await this.testDataAccess();
      
      // 4. Integração com dashboard
      await this.testDashboardIntegration();
      
      // 5. Relatório final
      this.generateReport();
      
    } catch (error) {
      console.error('💥 ERRO CRÍTICO NA AUDITORIA:', error);
      this.auditResults.errors.push(`Critical audit error: ${error.message}`);
    } finally {
      if (this.client) {
        await this.client.end();
      }
    }

    return this.auditResults;
  }

  async testConnection() {
    console.log('\n🔌 TESTANDO CONEXÃO...');
    
    try {
      this.client = new Client(DB_CONFIG);
      await this.client.connect();
      
      const result = await this.client.query('SELECT version(), current_database(), current_user');
      console.log('✅ Conexão estabelecida');
      console.log(`📊 PostgreSQL: ${result.rows[0].version.split(' ')[1]}`);
      console.log(`🗄️ Database: ${result.rows[0].current_database}`);
      console.log(`👤 User: ${result.rows[0].current_user}`);
      
      this.auditResults.connection = true;
    } catch (error) {
      console.error('❌ Erro na conexão:', error.message);
      this.auditResults.errors.push(`Connection: ${error.message}`);
    }
  }

  async checkSecurity() {
    console.log('\n🔐 VERIFICANDO SEGURANÇA...');
    
    try {
      // Verificar configurações de segurança
      const securityChecks = [
        { query: "SHOW ssl", name: "SSL Status" },
        { query: "SELECT current_setting('log_connections')", name: "Log Connections" },
        { query: "SELECT current_setting('log_statement')", name: "Log Statements" },
        { query: "SELECT current_setting('password_encryption')", name: "Password Encryption" }
      ];

      for (const check of securityChecks) {
        try {
          const result = await this.client.query(check.query);
          console.log(`✅ ${check.name}: ${result.rows[0][Object.keys(result.rows[0])[0]]}`);
        } catch (error) {
          console.log(`⚠️ ${check.name}: Não disponível`);
        }
      }

      // Verificar usuários e permissões
      const usersResult = await this.client.query(`
        SELECT usename, usesuper, usecreatedb, usecanlogin 
        FROM pg_user 
        WHERE usename = current_user
      `);
      
      const user = usersResult.rows[0];
      console.log(`👤 Usuário atual: ${user.usename}`);
      console.log(`🔑 Superuser: ${user.usesuper ? 'Sim' : 'Não'}`);
      console.log(`🗄️ Pode criar DB: ${user.usecreatedb ? 'Sim' : 'Não'}`);
      console.log(`🚪 Pode fazer login: ${user.usecanlogin ? 'Sim' : 'Não'}`);

      this.auditResults.security = true;
    } catch (error) {
      console.error('❌ Erro na verificação de segurança:', error.message);
      this.auditResults.errors.push(`Security: ${error.message}`);
    }
  }

  async checkCoreTables() {
    console.log('\n📋 VERIFICANDO TABELAS PRINCIPAIS...');
    
    const coreTables = [
      'game_sessions',
      'game_metrics',
      'therapeutic_analysis',
      'user_progress',
      'system_logs',
      'user_sessions'
    ];

    try {
      for (const table of coreTables) {
        const result = await this.client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `, [table]);

        const exists = result.rows[0].exists;
        console.log(`${exists ? '✅' : '❌'} ${table}: ${exists ? 'EXISTS' : 'MISSING'}`);
        
        if (!exists) {
          await this.createCoreTable(table);
        }
      }
      
      this.auditResults.tables.core = true;
    } catch (error) {
      console.error('❌ Erro ao verificar tabelas principais:', error.message);
      this.auditResults.errors.push(`Core tables: ${error.message}`);
    }
  }

  async checkGameTables() {
    console.log('\n🎮 VERIFICANDO TABELAS DOS JOGOS...');
    
    try {
      for (const game of GAMES_LIST) {
        const tableName = `${game.toLowerCase()}_metrics`;
        
        const result = await this.client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `, [tableName]);

        const exists = result.rows[0].exists;
        console.log(`${exists ? '✅' : '❌'} ${tableName}: ${exists ? 'EXISTS' : 'MISSING'}`);
        
        this.auditResults.tables.games[game] = exists;
        
        if (!exists) {
          await this.createGameTable(tableName, game);
        }
      }
    } catch (error) {
      console.error('❌ Erro ao verificar tabelas dos jogos:', error.message);
      this.auditResults.errors.push(`Game tables: ${error.message}`);
    }
  }

  async checkMultisensoryTables() {
    console.log('\n🔬 VERIFICANDO TABELAS MULTISSENSORIAIS...');
    
    const multisensoryTables = [
      'multisensory_data',
      'sensor_calibration',
      'sensor_readings',
      'touch_interactions',
      'accelerometer_data',
      'gyroscope_data'
    ];

    try {
      for (const table of multisensoryTables) {
        const result = await this.client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = $1
          );
        `, [table]);

        const exists = result.rows[0].exists;
        console.log(`${exists ? '✅' : '❌'} ${table}: ${exists ? 'EXISTS' : 'MISSING'}`);
        
        if (!exists) {
          await this.createMultisensoryTable(table);
        }
      }
      
      this.auditResults.tables.multisensory = true;
    } catch (error) {
      console.error('❌ Erro ao verificar tabelas multissensoriais:', error.message);
      this.auditResults.errors.push(`Multisensory tables: ${error.message}`);
    }
  }

  async createCoreTable(tableName) {
    const schemas = {
      game_sessions: `
        CREATE TABLE IF NOT EXISTS game_sessions (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) UNIQUE NOT NULL,
          user_id VARCHAR(255) NOT NULL,
          game_id VARCHAR(255) NOT NULL,
          session_data JSONB NOT NULL,
          started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          ended_at TIMESTAMP,
          duration INTEGER,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE INDEX IF NOT EXISTS idx_game_sessions_user_id ON game_sessions(user_id);
        CREATE INDEX IF NOT EXISTS idx_game_sessions_game_id ON game_sessions(game_id);
        CREATE INDEX IF NOT EXISTS idx_game_sessions_started_at ON game_sessions(started_at);
      `,
      game_metrics: `
        CREATE TABLE IF NOT EXISTS game_metrics (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          user_id VARCHAR(255) NOT NULL,
          game_id VARCHAR(255) NOT NULL,
          metrics_data JSONB NOT NULL,
          analysis_data JSONB,
          accuracy DECIMAL(5,2),
          response_time INTEGER,
          engagement_score DECIMAL(5,2),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE INDEX IF NOT EXISTS idx_game_metrics_user_id ON game_metrics(user_id);
        CREATE INDEX IF NOT EXISTS idx_game_metrics_game_id ON game_metrics(game_id);
        CREATE INDEX IF NOT EXISTS idx_game_metrics_created_at ON game_metrics(created_at);
      `,
      therapeutic_analysis: `
        CREATE TABLE IF NOT EXISTS therapeutic_analysis (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          user_id VARCHAR(255) NOT NULL,
          analysis_type VARCHAR(100) NOT NULL,
          analysis_data JSONB NOT NULL,
          confidence_score DECIMAL(3,2),
          recommendations JSONB,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      user_progress: `
        CREATE TABLE IF NOT EXISTS user_progress (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(255) NOT NULL,
          game_id VARCHAR(255) NOT NULL,
          progress_data JSONB NOT NULL,
          milestone_data JSONB,
          last_session TIMESTAMP,
          total_sessions INTEGER DEFAULT 0,
          average_accuracy DECIMAL(5,2),
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE UNIQUE INDEX IF NOT EXISTS idx_user_progress_unique ON user_progress(user_id, game_id);
      `,
      system_logs: `
        CREATE TABLE IF NOT EXISTS system_logs (
          id SERIAL PRIMARY KEY,
          level VARCHAR(20) NOT NULL,
          message TEXT NOT NULL,
          metadata JSONB,
          component VARCHAR(100),
          user_id VARCHAR(255),
          session_id VARCHAR(255),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level);
        CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at);
      `,
      user_sessions: `
        CREATE TABLE IF NOT EXISTS user_sessions (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(255) NOT NULL,
          session_token VARCHAR(255) UNIQUE NOT NULL,
          expires_at TIMESTAMP NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token);
        CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);
      `
    };

    if (schemas[tableName]) {
      await this.client.query(schemas[tableName]);
      console.log(`✅ Tabela ${tableName} criada com sucesso`);
    }
  }

  async createGameTable(tableName, gameName) {
    const schema = `
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id SERIAL PRIMARY KEY,
        session_id VARCHAR(255) NOT NULL,
        user_id VARCHAR(255) NOT NULL,
        game_specific_data JSONB NOT NULL,
        collectors_data JSONB,
        performance_metrics JSONB,
        therapeutic_indicators JSONB,
        multisensory_data JSONB,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
      CREATE INDEX IF NOT EXISTS idx_${tableName}_user_id ON ${tableName}(user_id);
      CREATE INDEX IF NOT EXISTS idx_${tableName}_session_id ON ${tableName}(session_id);
      CREATE INDEX IF NOT EXISTS idx_${tableName}_created_at ON ${tableName}(created_at);
    `;

    await this.client.query(schema);
    console.log(`✅ Tabela ${tableName} criada para ${gameName}`);
  }

  async createMultisensoryTable(tableName) {
    const schemas = {
      multisensory_data: `
        CREATE TABLE IF NOT EXISTS multisensory_data (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          user_id VARCHAR(255) NOT NULL,
          game_id VARCHAR(255) NOT NULL,
          sensor_type VARCHAR(100) NOT NULL,
          sensor_data JSONB NOT NULL,
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          calibration_data JSONB,
          processing_metadata JSONB
        );
        CREATE INDEX IF NOT EXISTS idx_multisensory_session ON multisensory_data(session_id);
        CREATE INDEX IF NOT EXISTS idx_multisensory_sensor_type ON multisensory_data(sensor_type);
      `,
      sensor_calibration: `
        CREATE TABLE IF NOT EXISTS sensor_calibration (
          id SERIAL PRIMARY KEY,
          user_id VARCHAR(255) NOT NULL,
          device_id VARCHAR(255),
          sensor_type VARCHAR(100) NOT NULL,
          calibration_data JSONB NOT NULL,
          accuracy_score DECIMAL(5,2),
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      sensor_readings: `
        CREATE TABLE IF NOT EXISTS sensor_readings (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          sensor_type VARCHAR(50) NOT NULL,
          reading_data JSONB NOT NULL,
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      touch_interactions: `
        CREATE TABLE IF NOT EXISTS touch_interactions (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          x_coordinate INTEGER,
          y_coordinate INTEGER,
          pressure DECIMAL(3,2),
          duration INTEGER,
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      accelerometer_data: `
        CREATE TABLE IF NOT EXISTS accelerometer_data (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          x_axis DECIMAL(10,6),
          y_axis DECIMAL(10,6),
          z_axis DECIMAL(10,6),
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `,
      gyroscope_data: `
        CREATE TABLE IF NOT EXISTS gyroscope_data (
          id SERIAL PRIMARY KEY,
          session_id VARCHAR(255) NOT NULL,
          alpha DECIMAL(10,6),
          beta DECIMAL(10,6),
          gamma DECIMAL(10,6),
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `
    };

    if (schemas[tableName]) {
      await this.client.query(schemas[tableName]);
      console.log(`✅ Tabela multissensorial ${tableName} criada`);
    }
  }

  async checkPermissions() {
    console.log('\n🔑 VERIFICANDO PERMISSÕES...');
    
    try {
      // Verificar permissões nas tabelas principais
      const permissionsQuery = `
        SELECT 
          schemaname,
          tablename,
          tableowner,
          hasinserts,
          hasselects,
          hasupdates,
          hasdeletes
        FROM pg_tables 
        WHERE schemaname = 'public'
        AND tablename LIKE '%game%' OR tablename LIKE '%multisensory%'
        LIMIT 5;
      `;
      
      const result = await this.client.query(permissionsQuery);
      
      console.log('📊 Permissões das tabelas:');
      result.rows.forEach(row => {
        console.log(`  ${row.tablename}: Owner=${row.tableowner}`);
      });
      
      this.auditResults.permissions = true;
    } catch (error) {
      console.error('❌ Erro ao verificar permissões:', error.message);
      this.auditResults.errors.push(`Permissions: ${error.message}`);
    }
  }

  async testDataAccess() {
    console.log('\n📊 TESTANDO ACESSO AOS DADOS...');
    
    try {
      // Inserir dados de teste
      const testSessionId = `test_audit_${Date.now()}`;
      
      await this.client.query(`
        INSERT INTO game_sessions (session_id, user_id, game_id, session_data)
        VALUES ($1, $2, $3, $4)
      `, [testSessionId, 'audit_user', 'ColorMatch', JSON.stringify({ test: true })]);
      
      await this.client.query(`
        INSERT INTO multisensory_data (session_id, user_id, game_id, sensor_type, sensor_data)
        VALUES ($1, $2, $3, $4, $5)
      `, [testSessionId, 'audit_user', 'ColorMatch', 'touch', JSON.stringify({ x: 100, y: 200 })]);
      
      // Testar consultas
      const sessionResult = await this.client.query('SELECT COUNT(*) FROM game_sessions');
      const multisensoryResult = await this.client.query('SELECT COUNT(*) FROM multisensory_data');
      
      console.log(`✅ Sessões no banco: ${sessionResult.rows[0].count}`);
      console.log(`✅ Dados multissensoriais: ${multisensoryResult.rows[0].count}`);
      
      // Limpar dados de teste
      await this.client.query('DELETE FROM game_sessions WHERE session_id = $1', [testSessionId]);
      await this.client.query('DELETE FROM multisensory_data WHERE session_id = $1', [testSessionId]);
      
      this.auditResults.data_access = true;
    } catch (error) {
      console.error('❌ Erro no teste de acesso aos dados:', error.message);
      this.auditResults.errors.push(`Data access: ${error.message}`);
    }
  }

  async testDashboardIntegration() {
    console.log('\n📈 TESTANDO INTEGRAÇÃO COM DASHBOARD...');
    
    try {
      // Simular consulta do dashboard
      const dashboardQuery = `
        SELECT 
          gs.game_id,
          COUNT(gs.id) as total_sessions,
          AVG(gm.accuracy) as avg_accuracy,
          COUNT(md.id) as multisensory_readings
        FROM game_sessions gs
        LEFT JOIN game_metrics gm ON gs.session_id = gm.session_id
        LEFT JOIN multisensory_data md ON gs.session_id = md.session_id
        GROUP BY gs.game_id
        LIMIT 5;
      `;
      
      const result = await this.client.query(dashboardQuery);
      
      console.log('📊 Dados disponíveis para dashboard:');
      result.rows.forEach(row => {
        console.log(`  ${row.game_id}: ${row.total_sessions} sessões, ${row.avg_accuracy || 0}% precisão`);
      });
      
      this.auditResults.dashboard_integration = true;
    } catch (error) {
      console.error('❌ Erro na integração com dashboard:', error.message);
      this.auditResults.errors.push(`Dashboard integration: ${error.message}`);
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(70));
    console.log('📋 RELATÓRIO FINAL DA AUDITORIA');
    console.log('='.repeat(70));
    
    const checks = [
      { name: 'Conexão com Banco', status: this.auditResults.connection },
      { name: 'Segurança', status: this.auditResults.security },
      { name: 'Tabelas Principais', status: this.auditResults.tables.core },
      { name: 'Tabelas Multissensoriais', status: this.auditResults.tables.multisensory },
      { name: 'Permissões', status: this.auditResults.permissions },
      { name: 'Acesso aos Dados', status: this.auditResults.data_access },
      { name: 'Integração Dashboard', status: this.auditResults.dashboard_integration }
    ];
    
    checks.forEach(check => {
      console.log(`${check.status ? '✅' : '❌'} ${check.name}`);
    });
    
    console.log('\n🎮 TABELAS DOS JOGOS:');
    Object.entries(this.auditResults.tables.games).forEach(([game, exists]) => {
      console.log(`${exists ? '✅' : '❌'} ${game}`);
    });
    
    if (this.auditResults.errors.length > 0) {
      console.log('\n🚨 ERROS ENCONTRADOS:');
      this.auditResults.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
    
    const successCount = checks.filter(c => c.status).length;
    const gameSuccessCount = Object.values(this.auditResults.tables.games).filter(Boolean).length;
    
    console.log(`\n📊 RESUMO:`);
    console.log(`Sistema: ${successCount}/${checks.length} (${(successCount/checks.length*100).toFixed(1)}%)`);
    console.log(`Jogos: ${gameSuccessCount}/${GAMES_LIST.length} (${(gameSuccessCount/GAMES_LIST.length*100).toFixed(1)}%)`);
    
    const overallSuccess = successCount === checks.length && gameSuccessCount === GAMES_LIST.length;
    console.log(`\n🎯 STATUS GERAL: ${overallSuccess ? '✅ SISTEMA TOTALMENTE FUNCIONAL' : '⚠️ NECESSITA CORREÇÕES'}`);
  }
}

// Executar auditoria se chamado diretamente
if (typeof window === 'undefined') {
  const isMainModule = process.argv[1] && process.argv[1].includes('database-complete-audit.js');
  
  if (isMainModule) {
    const auditor = new DatabaseCompleteAudit();
    
    auditor.runCompleteAudit()
      .then(results => {
        const success = results.connection && results.security && results.data_access;
        process.exit(success ? 0 : 1);
      })
      .catch(error => {
        console.error('💥 ERRO CRÍTICO NA AUDITORIA:', error);
        process.exit(1);
      });
  }
}

export { DatabaseCompleteAudit };
