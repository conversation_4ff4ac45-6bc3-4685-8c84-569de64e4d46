/**
 * @file test-active-games-filtering.js
 * @description Teste para validar a filtragem de jogos ativos vs. inativos ("warms")
 */

import { getProgressAnalyzer } from './src/api/services/analysis/ProgressAnalyzer.js';
import { logger } from './src/api/services/core/logging/StructuredLogger.js';

// Mock de um orquestrador para testes
const mockOrchestrator = {
  getAvailableGames: async () => [
    { id: 'MemoryGame', name: 'MemoryGame', status: 'active' },
    { id: 'CreativePainting', name: 'CreativePainting', status: 'active' },
    { id: 'LetterRecognition', name: 'LetterRecognition', status: 'active' },
    { id: 'SpatialPuzzle', name: 'SpatialPuzzle', status: 'active' },
    { id: 'MathQuest', name: 'MathQuest', status: 'active' },
    { id: 'AttentionTracker', name: 'AttentionTracker', status: 'active' },
    { id: '<PERSON>Match', name: '<PERSON><PERSON><PERSON>', status: 'active' },
    { id: 'StoryCreator', name: 'StoryC<PERSON>', status: 'active' },
    { id: 'EmotionRecognition', name: 'Em<PERSON>R<PERSON>ognition', status: 'active' },
    { id: 'PatternMatching', name: 'PatternMatching', status: 'warm' }, // Jogo inativo "warm"
    { id: 'SequenceLearning', name: 'SequenceLearning', status: 'warm' } // Jogo inativo "warm"
  ],
  getSessionsByChild: async (childId, startDate, endDate, gameIds) => {
    console.log(`Obtendo sessões para jogos: ${gameIds.join(', ')}`);
    return gameIds.map(gameId => ({
      id: `session-${Math.random().toString(36).substring(2, 9)}`,
      gameId,
      date: new Date().toISOString(),
      score: Math.floor(Math.random() * 100),
      duration: Math.floor(Math.random() * 30) + 10
    }));
  },
  getGoalsByChild: async () => [],
  getMilestonesByChild: async () => [],
  getAssessmentsByChild: async () => []
};

async function runTest() {
  console.log('=== TESTE DE FILTRAGEM DE JOGOS ATIVOS VS. WARMS ===');
  
  try {
    // Inicializar o analisador de progresso
    const progressAnalyzer = getProgressAnalyzer();
    
    // Injetar o orquestrador mock para teste
    progressAnalyzer.systemOrchestrator = mockOrchestrator;
    
    console.log('1. Testando filtro de jogos ativos...');
    const allGames = await mockOrchestrator.getAvailableGames();
    const activeGames = await progressAnalyzer.filterActiveGames(allGames);
    
    console.log(`Total de jogos: ${allGames.length}`);
    console.log(`Jogos ativos: ${activeGames.length}`);
    console.log(`Jogos inativos ("warms"): ${allGames.length - activeGames.length}`);
    console.log('Jogos ativos identificados:', activeGames.map(g => g.id).join(', '));
    
    const warms = allGames.filter(g => !activeGames.some(ag => ag.id === g.id));
    console.log('Jogos "warms" (inativos) excluídos:', warms.map(g => g.id).join(', '));
    
    console.log('\n2. Testando coleta de dados apenas para jogos ativos...');
    const mockChild = { id: 'child-123', name: 'Teste' };
    const startDate = new Date();
    const endDate = new Date();
    
    const progressData = await progressAnalyzer.gatherProgressData(mockChild, startDate, endDate);
    console.log(`Sessões coletadas: ${progressData.sessions.length}`);
    
    // Verificar se não temos sessões de jogos "warms"
    const warmsDetected = progressData.sessions.some(s => 
      warms.some(w => w.id === s.gameId)
    );
    
    console.log(`Jogos "warms" incluídos nas sessões: ${warmsDetected ? 'SIM (ERRO)' : 'NÃO (CORRETO)'}`);
    
    // Verificar jogos únicos nas sessões
    const uniqueGamesInSessions = [...new Set(progressData.sessions.map(s => s.gameId))];
    console.log('Jogos presentes nas sessões:', uniqueGamesInSessions.join(', '));
    
    console.log('\n=== RESULTADO ===');
    const isCorrect = !warmsDetected && activeGames.length === uniqueGamesInSessions.length;
    console.log(`Filtragem de jogos ativos vs. warms funcionando corretamente: ${isCorrect ? 'SIM ✅' : 'NÃO ❌'}`);
    
    if (isCorrect) {
      console.log('Validação concluída: Apenas jogos ativos estão sendo processados!');
      console.log('Os jogos "warms" (PatternMatching e SequenceLearning) estão corretamente excluídos.');
    } else {
      console.log('ALERTA: Verificar a implementação, jogos inativos podem estar sendo incluídos!');
    }
    
  } catch (error) {
    console.error('Erro durante o teste:', error);
  }
}

// Executar o teste
runTest().catch(console.error);
