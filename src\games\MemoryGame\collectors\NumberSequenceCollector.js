/**
 * 🔢 NUMBER SEQUENCE COLLECTOR V3
 * Coletor especializado para análise de sequências numéricas
 * Localização: src/games/MemoryGame/collectors/NumberSequenceCollector.js
 */

export class NumberSequenceCollector {
  constructor() {
    this.id = 'number_sequence_v3';
    this.name = 'Sequência Numérica V3';
    this.version = '3.0.0';
    this.category = 'numerical_processing';
    
    this.data = {
      sequences: [],
      numericalAccuracy: [],
      sequencePatterns: [],
      workingMemorySpan: [],
      arithmeticRelations: [],
      orderMemory: [],
      numericalProcessing: [],
      digitSpan: []
    };
  }

  // Coletar dados de tentativas de sequência numérica
  collect(data) {
    const {
      originalSequence,
      userSequence,
      sequenceType,
      responseTime,
      accuracy,
      difficulty,
      numericalProperties,
      timestamp
    } = data;

    // Análise detalhada da sequência numérica
    const sequenceAnalysis = this.analyzeNumericalSequence(originalSequence, userSequence);
    
    this.data.sequences.push({
      original: originalSequence,
      response: userSequence,
      type: sequenceType,
      properties: numericalProperties,
      analysis: sequenceAnalysis,
      responseTime,
      timestamp
    });

    // Precisão numérica
    this.data.numericalAccuracy.push({
      accuracy,
      sequenceLength: originalSequence.length,
      sequenceType,
      digitAccuracy: sequenceAnalysis.digitAccuracy,
      orderAccuracy: sequenceAnalysis.orderAccuracy,
      timestamp
    });

    // Padrões de sequência
    const patternAnalysis = this.analyzeSequencePatterns(originalSequence, userSequence, sequenceType);
    this.data.sequencePatterns.push(patternAnalysis);

    // Span de memória de trabalho
    const memorySpan = this.calculateWorkingMemorySpan(originalSequence.length, accuracy);
    this.data.workingMemorySpan.push({
      span: memorySpan,
      performance: accuracy,
      sequenceLength: originalSequence.length,
      timestamp
    });

    // Relações aritméticas
    if (sequenceType === 'arithmetic' || sequenceType === 'fibonacci') {
      const arithmeticAnalysis = this.analyzeArithmeticRelations(originalSequence, userSequence);
      this.data.arithmeticRelations.push(arithmeticAnalysis);
    }

    // Memória de ordem
    const orderAnalysis = this.analyzeOrderMemory(originalSequence, userSequence);
    this.data.orderMemory.push(orderAnalysis);

    // Processamento numérico
    const processingAnalysis = this.analyzeNumericalProcessing(originalSequence, userSequence, responseTime);
    this.data.numericalProcessing.push(processingAnalysis);

    // Span de dígitos
    const digitSpanAnalysis = this.analyzeDigitSpan(originalSequence, userSequence);
    this.data.digitSpan.push(digitSpanAnalysis);
  }

  analyzeNumericalSequence(original, response) {
    if (!original || !response) return { similarity: 0, errors: [] };

    const digitAccuracy = this.calculateDigitAccuracy(original, response);
    const orderAccuracy = this.calculateOrderAccuracy(original, response);
    const numericalErrors = this.identifyNumericalErrors(original, response);

    return {
      similarity: this.calculateSequenceSimilarity(original, response),
      digitAccuracy,
      orderAccuracy,
      errors: numericalErrors,
      totalErrors: numericalErrors.length,
      errorRate: numericalErrors.length / original.length,
      preservationScore: this.calculateNumericalPreservation(original, response)
    };
  }

  calculateSequenceSimilarity(seq1, seq2) {
    if (!seq1 || !seq2) return 0;
    
    const maxLength = Math.max(seq1.length, seq2.length);
    let matches = 0;

    for (let i = 0; i < maxLength; i++) {
      if (seq1[i] === seq2[i]) matches++;
    }

    return matches / maxLength;
  }

  calculateDigitAccuracy(original, response) {
    let correctDigits = 0;
    let totalDigits = 0;

    for (let i = 0; i < Math.max(original.length, response.length); i++) {
      if (original[i] !== undefined) {
        totalDigits++;
        if (original[i] === response[i]) {
          correctDigits++;
        }
      }
    }

    return totalDigits > 0 ? correctDigits / totalDigits : 0;
  }

  calculateOrderAccuracy(original, response) {
    if (original.length !== response.length) return 0;

    // Verificar se a ordem relativa foi preservada
    let correctOrder = 0;
    let totalComparisons = 0;

    for (let i = 0; i < original.length - 1; i++) {
      for (let j = i + 1; j < original.length; j++) {
        totalComparisons++;
        
        const origRelation = original[i] < original[j] ? 'less' : 
                            original[i] > original[j] ? 'greater' : 'equal';
        const respRelation = response[i] < response[j] ? 'less' : 
                            response[i] > response[j] ? 'greater' : 'equal';
        
        if (origRelation === respRelation) {
          correctOrder++;
        }
      }
    }

    return totalComparisons > 0 ? correctOrder / totalComparisons : 0;
  }

  identifyNumericalErrors(original, response) {
    const errors = [];
    
    for (let i = 0; i < Math.max(original.length, response.length); i++) {
      if (original[i] !== response[i]) {
        errors.push({
          position: i,
          expected: original[i],
          actual: response[i],
          type: this.categorizeNumericalError(original[i], response[i]),
          magnitude: this.calculateErrorMagnitude(original[i], response[i])
        });
      }
    }

    return errors;
  }

  categorizeNumericalError(expected, actual) {
    if (actual === undefined || actual === null) return 'omission';
    if (expected === undefined || expected === null) return 'insertion';

    const difference = Math.abs(expected - actual);
    
    if (difference === 1) return 'adjacent_substitution';
    if (difference <= 5) return 'near_substitution';
    if (this.isVisuallySimular(expected, actual)) return 'visual_confusion';
    if (this.isPhonologicallySimilar(expected, actual)) return 'phonological_confusion';
    
    return 'random_substitution';
  }

  isVisuallySimular(num1, num2) {
    // Números visualmente similares
    const visualSimilarities = {
      6: [9], 9: [6],
      1: [7], 7: [1],
      3: [8], 8: [3],
      2: [5], 5: [2]
    };

    return visualSimilarities[num1]?.includes(num2) || false;
  }

  isPhonologicallySimilar(num1, num2) {
    // Números fonologicamente similares (em português)
    const phonologicalSimilarities = {
      1: [11], 11: [1],
      2: [12], 12: [2],
      3: [13], 13: [3],
      4: [14], 14: [4],
      5: [15], 15: [5],
      6: [16], 16: [6],
      7: [17], 17: [7],
      8: [18], 18: [8],
      9: [19], 19: [9]
    };

    return phonologicalSimilarities[num1]?.includes(num2) || false;
  }

  calculateErrorMagnitude(expected, actual) {
    if (!expected || !actual) return Infinity;
    return Math.abs(expected - actual);
  }

  calculateNumericalPreservation(original, response) {
    // Calcular quantos números foram preservados, mesmo que fora da ordem
    const originalSet = new Set(original);
    const responseSet = new Set(response);
    
    let preserved = 0;
    originalSet.forEach(num => {
      if (responseSet.has(num)) preserved++;
    });

    return originalSet.size > 0 ? preserved / originalSet.size : 0;
  }

  analyzeSequencePatterns(original, response, sequenceType) {
    const patternRecognition = this.recognizePattern(original, sequenceType);
    const patternReproduction = this.analyzePatternReproduction(original, response, sequenceType);
    
    return {
      type: sequenceType,
      recognitionScore: patternRecognition.score,
      reproductionScore: patternReproduction.score,
      patternDeviations: patternReproduction.deviations,
      understandingLevel: this.assessPatternUnderstanding(patternRecognition, patternReproduction)
    };
  }

  recognizePattern(sequence, type) {
    switch (type) {
      case 'arithmetic':
        return this.analyzeArithmeticPattern(sequence);
      case 'geometric':
        return this.analyzeGeometricPattern(sequence);
      case 'fibonacci':
        return this.analyzeFibonacciPattern(sequence);
      case 'random':
        return { score: 1, pattern: 'random' }; // Random sempre "correto"
      default:
        return { score: 0, pattern: 'unknown' };
    }
  }

  analyzeArithmeticPattern(sequence) {
    if (sequence.length < 2) return { score: 0, pattern: 'insufficient_data' };

    // Calcular diferenças
    const differences = [];
    for (let i = 1; i < sequence.length; i++) {
      differences.push(sequence[i] - sequence[i - 1]);
    }

    // Verificar se é progressão aritmética
    const firstDiff = differences[0];
    const isArithmetic = differences.every(diff => diff === firstDiff);

    return {
      score: isArithmetic ? 1 : 0,
      pattern: isArithmetic ? `arithmetic_${firstDiff}` : 'irregular',
      commonDifference: firstDiff,
      consistency: this.calculateConsistency(differences)
    };
  }

  analyzeGeometricPattern(sequence) {
    if (sequence.length < 2) return { score: 0, pattern: 'insufficient_data' };

    // Calcular razões
    const ratios = [];
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i - 1] !== 0) {
        ratios.push(sequence[i] / sequence[i - 1]);
      }
    }

    if (ratios.length === 0) return { score: 0, pattern: 'division_by_zero' };

    // Verificar se é progressão geométrica
    const firstRatio = ratios[0];
    const tolerance = 0.01;
    const isGeometric = ratios.every(ratio => Math.abs(ratio - firstRatio) < tolerance);

    return {
      score: isGeometric ? 1 : 0,
      pattern: isGeometric ? `geometric_${firstRatio.toFixed(2)}` : 'irregular',
      commonRatio: firstRatio,
      consistency: this.calculateConsistency(ratios)
    };
  }

  analyzeFibonacciPattern(sequence) {
    if (sequence.length < 3) return { score: 0, pattern: 'insufficient_data' };

    // Verificar se segue padrão de Fibonacci (cada número = soma dos dois anteriores)
    let fibonacciScore = 0;
    let totalChecks = 0;

    for (let i = 2; i < sequence.length; i++) {
      totalChecks++;
      if (sequence[i] === sequence[i - 1] + sequence[i - 2]) {
        fibonacciScore++;
      }
    }

    const score = totalChecks > 0 ? fibonacciScore / totalChecks : 0;

    return {
      score,
      pattern: score > 0.8 ? 'fibonacci' : 'irregular',
      matches: fibonacciScore,
      totalChecks
    };
  }

  calculateConsistency(values) {
    if (values.length === 0) return 0;
    if (values.length === 1) return 1;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);

    // Consistência baseada no desvio padrão relativo
    return mean !== 0 ? Math.max(0, 1 - (stdDev / Math.abs(mean))) : 1;
  }

  analyzePatternReproduction(original, response, sequenceType) {
    const expectedPattern = this.recognizePattern(original, sequenceType);
    const actualPattern = this.recognizePattern(response, sequenceType);

    const reproductionScore = this.comparePatterns(expectedPattern, actualPattern);
    const deviations = this.findPatternDeviations(original, response, sequenceType);

    return {
      score: reproductionScore,
      deviations,
      expectedPattern: expectedPattern.pattern,
      actualPattern: actualPattern.pattern
    };
  }

  comparePatterns(pattern1, pattern2) {
    if (pattern1.pattern === pattern2.pattern) {
      // Mesma categoria de padrão
      return Math.min(pattern1.score, pattern2.score);
    }
    
    return 0; // Padrões diferentes
  }

  findPatternDeviations(original, response, sequenceType) {
    const deviations = [];

    switch (sequenceType) {
      case 'arithmetic':
        deviations.push(...this.findArithmeticDeviations(original, response));
        break;
      case 'geometric':
        deviations.push(...this.findGeometricDeviations(original, response));
        break;
      case 'fibonacci':
        deviations.push(...this.findFibonacciDeviations(original, response));
        break;
    }

    return deviations;
  }

  findArithmeticDeviations(original, response) {
    const deviations = [];
    
    if (response.length < 2) return deviations;

    // Calcular diferença esperada
    const expectedDiff = original[1] - original[0];
    
    for (let i = 1; i < response.length; i++) {
      const actualDiff = response[i] - response[i - 1];
      if (actualDiff !== expectedDiff) {
        deviations.push({
          position: i,
          type: 'arithmetic_deviation',
          expected: response[i - 1] + expectedDiff,
          actual: response[i],
          expectedDiff,
          actualDiff
        });
      }
    }

    return deviations;
  }

  findGeometricDeviations(original, response) {
    const deviations = [];
    
    if (response.length < 2 || original[0] === 0) return deviations;

    // Calcular razão esperada
    const expectedRatio = original[1] / original[0];
    
    for (let i = 1; i < response.length; i++) {
      if (response[i - 1] !== 0) {
        const actualRatio = response[i] / response[i - 1];
        if (Math.abs(actualRatio - expectedRatio) > 0.01) {
          deviations.push({
            position: i,
            type: 'geometric_deviation',
            expected: response[i - 1] * expectedRatio,
            actual: response[i],
            expectedRatio,
            actualRatio
          });
        }
      }
    }

    return deviations;
  }

  findFibonacciDeviations(original, response) {
    const deviations = [];
    
    if (response.length < 3) return deviations;

    for (let i = 2; i < response.length; i++) {
      const expectedValue = response[i - 1] + response[i - 2];
      if (response[i] !== expectedValue) {
        deviations.push({
          position: i,
          type: 'fibonacci_deviation',
          expected: expectedValue,
          actual: response[i]
        });
      }
    }

    return deviations;
  }

  assessPatternUnderstanding(recognition, reproduction) {
    const recognitionScore = recognition.score || 0;
    const reproductionScore = reproduction.score || 0;

    if (recognitionScore > 0.8 && reproductionScore > 0.8) return 'excellent';
    if (recognitionScore > 0.6 && reproductionScore > 0.6) return 'good';
    if (recognitionScore > 0.4 || reproductionScore > 0.4) return 'partial';
    return 'poor';
  }

  calculateWorkingMemorySpan(sequenceLength, accuracy) {
    // Span baseado no comprimento e performance
    const baseSpan = sequenceLength * accuracy;
    const performanceBonus = accuracy > 0.8 ? 1 : accuracy > 0.6 ? 0.5 : 0;
    
    return Math.min(15, baseSpan + performanceBonus); // Máximo span de 15
  }

  analyzeArithmeticRelations(original, response) {
    const originalRelations = this.extractArithmeticRelations(original);
    const responseRelations = this.extractArithmeticRelations(response);
    
    const relationPreservation = this.compareArithmeticRelations(originalRelations, responseRelations);

    return {
      originalRelations,
      responseRelations,
      preservation: relationPreservation,
      understandingScore: this.calculateArithmeticUnderstanding(originalRelations, responseRelations)
    };
  }

  extractArithmeticRelations(sequence) {
    const relations = {
      differences: [],
      ratios: [],
      sums: [],
      patterns: []
    };

    // Diferenças consecutivas
    for (let i = 1; i < sequence.length; i++) {
      relations.differences.push(sequence[i] - sequence[i - 1]);
    }

    // Razões (se não há zeros)
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i - 1] !== 0) {
        relations.ratios.push(sequence[i] / sequence[i - 1]);
      }
    }

    // Somas consecutivas
    for (let i = 1; i < sequence.length; i++) {
      relations.sums.push(sequence[i] + sequence[i - 1]);
    }

    return relations;
  }

  compareArithmeticRelations(original, response) {
    let totalRelations = 0;
    let preservedRelations = 0;

    // Comparar diferenças
    const minDiffs = Math.min(original.differences.length, response.differences.length);
    for (let i = 0; i < minDiffs; i++) {
      totalRelations++;
      if (original.differences[i] === response.differences[i]) {
        preservedRelations++;
      }
    }

    // Comparar razões
    const minRatios = Math.min(original.ratios.length, response.ratios.length);
    for (let i = 0; i < minRatios; i++) {
      totalRelations++;
      if (Math.abs(original.ratios[i] - response.ratios[i]) < 0.01) {
        preservedRelations++;
      }
    }

    return totalRelations > 0 ? preservedRelations / totalRelations : 0;
  }

  calculateArithmeticUnderstanding(originalRelations, responseRelations) {
    // Verificar se as relações aritméticas fundamentais foram compreendidas
    const diffConsistency = this.calculateConsistency(responseRelations.differences);
    const ratioConsistency = this.calculateConsistency(responseRelations.ratios);
    
    return (diffConsistency + ratioConsistency) / 2;
  }

  analyzeOrderMemory(original, response) {
    const orderPreservation = this.calculateOrderPreservation(original, response);
    const positionAccuracy = this.calculatePositionAccuracy(original, response);
    const serialPositionCurve = this.analyzeSerialPositionCurve(original, response);

    return {
      orderPreservation,
      positionAccuracy,
      serialPositionCurve,
      orderingStrategy: this.identifyOrderingStrategy(original, response)
    };
  }

  calculateOrderPreservation(original, response) {
    if (original.length !== response.length) return 0;

    // Verificar quantos pares adjacentes mantiveram a ordem relativa
    let preservedPairs = 0;
    let totalPairs = 0;

    for (let i = 0; i < original.length - 1; i++) {
      totalPairs++;
      const origOrder = original[i] < original[i + 1];
      const respOrder = response[i] < response[i + 1];
      
      if (origOrder === respOrder) {
        preservedPairs++;
      }
    }

    return totalPairs > 0 ? preservedPairs / totalPairs : 0;
  }

  calculatePositionAccuracy(original, response) {
    let correctPositions = 0;
    
    for (let i = 0; i < Math.min(original.length, response.length); i++) {
      if (original[i] === response[i]) {
        correctPositions++;
      }
    }

    return original.length > 0 ? correctPositions / original.length : 0;
  }

  analyzeSerialPositionCurve(original, response) {
    const positions = [];
    
    for (let i = 0; i < original.length; i++) {
      positions.push({
        position: i,
        correct: original[i] === response[i],
        primacy: i < original.length * 0.33,
        recency: i > original.length * 0.67
      });
    }

    const primacyItems = positions.filter(p => p.primacy);
    const recencyItems = positions.filter(p => p.recency);
    const middleItems = positions.filter(p => !p.primacy && !p.recency);

    return {
      primacyEffect: primacyItems.length > 0 ? primacyItems.filter(p => p.correct).length / primacyItems.length : 0,
      recencyEffect: recencyItems.length > 0 ? recencyItems.filter(p => p.correct).length / recencyItems.length : 0,
      middlePerformance: middleItems.length > 0 ? middleItems.filter(p => p.correct).length / middleItems.length : 0
    };
  }

  identifyOrderingStrategy(original, response) {
    // Identificar estratégia de ordenação usada
    const preservation = this.calculateOrderPreservation(original, response);
    const positionAccuracy = this.calculatePositionAccuracy(original, response);

    if (positionAccuracy > 0.8) return 'perfect_recall';
    if (preservation > 0.8) return 'order_preservation';
    if (preservation > 0.5) return 'partial_ordering';
    return 'random_or_confused';
  }

  analyzeNumericalProcessing(original, response, responseTime) {
    const processingSpeed = this.calculateProcessingSpeed(original.length, responseTime);
    const accuracySpeed = this.calculateAccuracySpeedTradeoff(original, response, responseTime);
    
    return {
      processingSpeed,
      accuracySpeedTradeoff: accuracySpeed,
      efficiency: this.calculateProcessingEfficiency(original, response, responseTime),
      cognitiveLoad: this.estimateCognitiveLoad(original.length, responseTime)
    };
  }

  calculateProcessingSpeed(sequenceLength, responseTime) {
    const timePerItem = responseTime / sequenceLength;
    
    // Velocidade baseada no tempo por item (menos tempo = maior velocidade)
    return Math.max(0, 100 - (timePerItem / 100)); // Assumindo 100ms como baseline
  }

  calculateAccuracySpeedTradeoff(original, response, responseTime) {
    const accuracy = this.calculateDigitAccuracy(original, response);
    const speed = this.calculateProcessingSpeed(original.length, responseTime);
    
    // Tradeoff ideal seria alta precisão com boa velocidade
    return {
      accuracy,
      speed,
      balance: (accuracy + speed / 100) / 2
    };
  }

  calculateProcessingEfficiency(original, response, responseTime) {
    const accuracy = this.calculateDigitAccuracy(original, response);
    const timePerItem = responseTime / original.length;
    
    // Eficiência como precisão dividida pelo tempo
    return timePerItem > 0 ? (accuracy * 1000) / timePerItem : 0;
  }

  estimateCognitiveLoad(sequenceLength, responseTime) {
    // Carga cognitiva baseada no comprimento e tempo de resposta
    const lengthLoad = Math.min(100, sequenceLength * 8);
    const timeLoad = Math.min(50, responseTime / 1000); // Tempo em segundos
    
    return Math.min(100, lengthLoad + timeLoad);
  }

  analyzeDigitSpan(original, response) {
    const forwardSpan = this.calculateForwardSpan(original, response);
    const digitMemoryCapacity = this.estimateDigitMemoryCapacity(original.length, forwardSpan);
    
    return {
      forwardSpan,
      digitMemoryCapacity,
      spanEfficiency: this.calculateSpanEfficiency(original.length, forwardSpan),
      memoryStrategy: this.identifyMemoryStrategy(original, response)
    };
  }

  calculateForwardSpan(original, response) {
    // Calcular o span baseado na performance
    const accuracy = this.calculateDigitAccuracy(original, response);
    const effectiveSpan = original.length * accuracy;
    
    return Math.floor(effectiveSpan);
  }

  estimateDigitMemoryCapacity(attemptedLength, achievedSpan) {
    // Estimar capacidade de memória de dígitos
    const efficiency = attemptedLength > 0 ? achievedSpan / attemptedLength : 0;
    
    // Capacidade estimada baseada na eficiência e tentativa
    return Math.min(15, Math.round(achievedSpan + (efficiency * 3)));
  }

  calculateSpanEfficiency(attemptedLength, achievedSpan) {
    return attemptedLength > 0 ? achievedSpan / attemptedLength : 0;
  }

  identifyMemoryStrategy(original, response) {
    const accuracy = this.calculateDigitAccuracy(original, response);
    const orderPreservation = this.calculateOrderPreservation(original, response);
    const preservation = this.calculateNumericalPreservation(original, response);

    if (accuracy > 0.9 && orderPreservation > 0.9) return 'perfect_sequential';
    if (orderPreservation > 0.8) return 'order_focused';
    if (preservation > 0.8) return 'item_focused';
    if (accuracy > 0.6) return 'partial_recall';
    return 'struggling';
  }

  // Gerar relatório de análise numérica
  generateReport() {
    const totalAttempts = this.data.sequences.length;
    if (totalAttempts === 0) return null;

    const avgAccuracy = this.data.numericalAccuracy.reduce((sum, item) => sum + item.accuracy, 0) / totalAttempts;
    const avgDigitAccuracy = this.data.numericalAccuracy.reduce((sum, item) => sum + item.digitAccuracy, 0) / totalAttempts;
    const avgOrderAccuracy = this.data.numericalAccuracy.reduce((sum, item) => sum + item.orderAccuracy, 0) / totalAttempts;

    // Análise de padrões
    const patternAnalysis = this.calculatePatternMetrics();
    
    // Análise de span
    const spanAnalysis = this.calculateSpanMetrics();

    return {
      collectorId: this.id,
      totalAttempts,
      avgAccuracy: Math.round(avgAccuracy * 100) / 100,
      avgDigitAccuracy: Math.round(avgDigitAccuracy * 100) / 100,
      avgOrderAccuracy: Math.round(avgOrderAccuracy * 100) / 100,
      patternRecognition: patternAnalysis,
      memorySpan: spanAnalysis,
      recommendations: this.generateNumericalRecommendations(avgAccuracy, patternAnalysis),
      cognitiveInsights: {
        numericalWorkingMemoryCapacity: this.calculateNumericalCapacity(),
        sequentialProcessingAbility: avgOrderAccuracy * 100,
        patternRecognitionSkill: patternAnalysis.averageRecognition * 100,
        digitSpanCapacity: spanAnalysis.estimatedCapacity
      }
    };
  }

  calculatePatternMetrics() {
    if (this.data.sequencePatterns.length === 0) return null;

    const patterns = this.data.sequencePatterns;
    const avgRecognition = patterns.reduce((sum, p) => sum + p.recognitionScore, 0) / patterns.length;
    const avgReproduction = patterns.reduce((sum, p) => sum + p.reproductionScore, 0) / patterns.length;

    return {
      averageRecognition: avgRecognition,
      averageReproduction: avgReproduction,
      overallPatternAbility: (avgRecognition + avgReproduction) / 2
    };
  }

  calculateSpanMetrics() {
    if (this.data.digitSpan.length === 0) return null;

    const spans = this.data.digitSpan;
    const avgSpan = spans.reduce((sum, s) => sum + s.forwardSpan, 0) / spans.length;
    const maxCapacity = Math.max(...spans.map(s => s.digitMemoryCapacity));

    return {
      averageSpan: avgSpan,
      estimatedCapacity: maxCapacity,
      spanEfficiency: spans.reduce((sum, s) => sum + s.spanEfficiency, 0) / spans.length
    };
  }

  calculateNumericalCapacity() {
    if (this.data.workingMemorySpan.length === 0) return 50;

    const recent = this.data.workingMemorySpan.slice(-5);
    const maxSpan = Math.max(...recent.map(item => item.span));
    const avgPerformance = recent.reduce((sum, item) => sum + item.performance, 0) / recent.length;

    return Math.min(100, (maxSpan * 6) + (avgPerformance * 40));
  }

  generateNumericalRecommendations(accuracy, patternAnalysis) {
    const recommendations = [];

    if (accuracy < 0.7) {
      recommendations.push('Praticar com sequências mais curtas');
    }

    if (patternAnalysis && patternAnalysis.averageRecognition < 0.6) {
      recommendations.push('Exercícios de reconhecimento de padrões');
    }

    if (patternAnalysis && patternAnalysis.averageReproduction < 0.6) {
      recommendations.push('Treinar reprodução de sequências');
    }

    return recommendations;
  }

  reset() {
    this.data = {
      sequences: [],
      numericalAccuracy: [],
      sequencePatterns: [],
      workingMemorySpan: [],
      arithmeticRelations: [],
      orderMemory: [],
      numericalProcessing: [],
      digitSpan: []
    };
  }
}

export default NumberSequenceCollector;
