/**
 * 📐 SPATIAL PROCESSING COLLECTOR
 * Coletor especializado em análise de processamento espacial para PadroesVisuais
 * Portal Betina V3
 */

export class SpatialProcessingCollector {
  constructor() {
    this.spatialDomains = {
      visualization: 'visualização espacial',
      orientation: 'orientação espacial',
      rotation: 'rotação mental',
      positioning: 'posicionamento',
      navigation: 'navegação espacial',
      transformation: 'transformação espacial'
    };
    
    this.spatialComplexity = {
      basic: { level: 1, description: 'relações espaciais básicas' },
      intermediate: { level: 2, description: 'transformações moderadas' },
      advanced: { level: 3, description: 'manipulações complexas' },
      expert: { level: 4, description: 'processamento espacial avançado' }
    };
    
    this.spatialDimensions = {
      position: 'posição absoluta',
      relative: 'posição relativa',
      distance: 'distância espacial',
      direction: 'direção',
      scale: 'escala',
      symmetry: 'simetria'
    };
  }
  
  /**
   * <PERSON><PERSON><PERSON>do padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }

  async analyze(data) {
    if (!data || !data.spatialData) {
      console.warn('SpatialProcessingCollector: Dados inválidos recebidos', data);
      return {
        spatialVisualization: 0.7,
        spatialOrientation: 0.7,
        spatialTransformation: 0.7,
        spatialMemory: 0.7,
        spatialReasoning: 0.7,
        spatialNavigation: 0.7,
        spatialAccuracy: 0.7,
        spatialEfficiency: 0.7
      };
    }

    return {
      spatialVisualization: this.assessSpatialVisualization(data),
      spatialOrientation: this.assessSpatialOrientation(data),
      spatialTransformation: this.assessSpatialTransformation(data),
      spatialMemory: this.assessSpatialMemory(data),
      spatialReasoning: this.assessSpatialReasoning(data),
      spatialNavigation: this.assessSpatialNavigation(data),
      spatialAccuracy: this.assessSpatialAccuracy(data),
      spatialEfficiency: this.assessSpatialEfficiency(data),
      spatialStrategies: this.identifySpatialStrategies(data),
      spatialInsights: this.generateSpatialInsights(data)
    };
  }

  assessSpatialVisualization(data) {
    const interactions = data.spatialData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    let visualizationScore = 0;
    let validInteractions = 0;
    
    interactions.forEach(interaction => {
      if (interaction.spatialVisualization) {
        const complexity = this.getSpatialComplexity(interaction);
        const accuracy = interaction.isCorrect ? 1 : 0;
        const processingTime = interaction.responseTime || 2000;
        
        // Avaliar qualidade da visualização espacial
        const visualizationQuality = this.calculateVisualizationQuality(accuracy, processingTime, complexity);
        visualizationScore += visualizationQuality;
        validInteractions++;
      }
    });
    
    if (validInteractions === 0) {
      // Inferir visualização espacial das sequências
      return this.inferVisualizationFromSequences(data);
    }
    
    const avgVisualizationScore = visualizationScore / validInteractions;
    
    // Bonus para visualização consistente
    const consistencyBonus = this.calculateVisualizationConsistency(interactions);
    
    return Math.max(0, Math.min(1, avgVisualizationScore + consistencyBonus));
  }

  assessSpatialOrientation(data) {
    const sequences = data.spatialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let orientationScore = 0;
    
    sequences.forEach(sequence => {
      if (sequence.spatialLayout) {
        const orientationAccuracy = this.calculateOrientationAccuracy(sequence.spatialLayout);
        const layoutComplexity = this.getLayoutComplexity(sequence.spatialLayout);
        
        const orientationQuality = orientationAccuracy * layoutComplexity;
        orientationScore += orientationQuality;
      } else {
        // Inferir orientação da sequência de formas
        const inferredOrientation = this.inferOrientationFromSequence(sequence);
        orientationScore += inferredOrientation;
      }
    });
    
    const avgOrientationScore = orientationScore / sequences.length;
    
    return Math.max(0, Math.min(1, avgOrientationScore));
  }

  assessSpatialTransformation(data) {
    const interactions = data.spatialData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Identificar transformações espaciais
    const transformations = this.identifyTransformations(interactions);
    
    if (transformations.length === 0) return 0.7;
    
    let transformationScore = 0;
    
    transformations.forEach(transform => {
      const accuracy = transform.isCorrect ? 1 : 0;
      const difficulty = this.getTransformationDifficulty(transform.type);
      const processingTime = transform.responseTime || 2000;
      
      // Avaliar qualidade da transformação
      const transformationQuality = this.calculateTransformationQuality(accuracy, processingTime, difficulty);
      transformationScore += transformationQuality;
    });
    
    const avgTransformationScore = transformationScore / transformations.length;
    
    return Math.max(0, Math.min(1, avgTransformationScore));
  }

  assessSpatialMemory(data) {
    const sequences = data.spatialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let spatialMemoryScore = 0;
    
    sequences.forEach(sequence => {
      const memoryLoad = this.calculateSpatialMemoryLoad(sequence);
      const retentionQuality = this.calculateSpatialRetention(sequence);
      const interferenceResistance = this.calculateSpatialInterferenceResistance(sequence);
      
      // Combinar aspectos da memória espacial
      const memoryQuality = (retentionQuality * 0.5) + (interferenceResistance * 0.3) + (memoryLoad * 0.2);
      spatialMemoryScore += memoryQuality;
    });
    
    const avgSpatialMemoryScore = spatialMemoryScore / sequences.length;
    
    return Math.max(0, Math.min(1, avgSpatialMemoryScore));
  }

  assessSpatialReasoning(data) {
    const sequences = data.spatialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let reasoningScore = 0;
    
    sequences.forEach(sequence => {
      const logicalComplexity = this.calculateLogicalComplexity(sequence);
      const reasoningAccuracy = this.calculateReasoningAccuracy(sequence);
      const inferenceQuality = this.assessInferenceQuality(sequence);
      
      // Avaliar qualidade do raciocínio espacial
      const reasoningQuality = (reasoningAccuracy * 0.4) + (logicalComplexity * 0.3) + (inferenceQuality * 0.3);
      reasoningScore += reasoningQuality;
    });
    
    const avgReasoningScore = reasoningScore / sequences.length;
    
    return Math.max(0, Math.min(1, avgReasoningScore));
  }

  assessSpatialNavigation(data) {
    const interactions = data.spatialData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Avaliar padrões de navegação espacial
    const navigationPatterns = this.identifyNavigationPatterns(interactions);
    
    let navigationScore = 0;
    
    navigationPatterns.forEach(pattern => {
      const efficiency = this.calculateNavigationEfficiency(pattern);
      const accuracy = pattern.accuracy || 0.7;
      const pathOptimization = this.assessPathOptimization(pattern);
      
      const navigationQuality = (efficiency * 0.4) + (accuracy * 0.4) + (pathOptimization * 0.2);
      navigationScore += navigationQuality;
    });
    
    if (navigationPatterns.length === 0) return 0.7;
    
    const avgNavigationScore = navigationScore / navigationPatterns.length;
    
    return Math.max(0, Math.min(1, avgNavigationScore));
  }

  assessSpatialAccuracy(data) {
    const interactions = data.spatialData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    let accuracyScores = [];
    
    interactions.forEach(interaction => {
      if (interaction.spatialPosition) {
        const positionAccuracy = this.calculatePositionAccuracy(interaction.spatialPosition);
        const distanceAccuracy = this.calculateDistanceAccuracy(interaction.spatialPosition);
        const directionAccuracy = this.calculateDirectionAccuracy(interaction.spatialPosition);
        
        // Combinar diferentes tipos de precisão espacial
        const overallAccuracy = (positionAccuracy * 0.4) + (distanceAccuracy * 0.3) + (directionAccuracy * 0.3);
        accuracyScores.push(overallAccuracy);
      }
    });
    
    if (accuracyScores.length === 0) {
      // Inferir precisão espacial das sequências
      return this.inferAccuracyFromSequences(data);
    }
    
    const avgAccuracy = accuracyScores.reduce((sum, acc) => sum + acc, 0) / accuracyScores.length;
    
    return Math.max(0, Math.min(1, avgAccuracy));
  }

  assessSpatialEfficiency(data) {
    const interactions = data.spatialData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    let efficiencyScores = [];
    
    interactions.forEach(interaction => {
      const processingTime = interaction.responseTime || 2000;
      const spatialComplexity = this.getSpatialComplexity(interaction);
      const accuracy = interaction.isCorrect ? 1 : 0;
      
      // Calcular eficiência: balancear velocidade, precisão e complexidade
      const efficiency = this.calculateSpatialEfficiency(processingTime, accuracy, spatialComplexity);
      efficiencyScores.push(efficiency);
    });
    
    const avgEfficiency = efficiencyScores.reduce((sum, eff) => sum + eff, 0) / efficiencyScores.length;
    
    // Avaliar melhoria da eficiência ao longo da sessão
    const improvementFactor = this.calculateEfficiencyImprovement(efficiencyScores);
    
    return Math.max(0, Math.min(1, avgEfficiency + improvementFactor));
  }

  // Métodos auxiliares

  getSpatialComplexity(interaction) {
    let complexity = 1.0;
    
    // Complexidade baseada no tipo de forma
    if (interaction.shapeId) {
      const shapeComplexities = {
        circle: 1.0,
        square: 1.1,
        triangle: 1.2,
        diamond: 1.3,
        star: 1.4,
        heart: 1.5
      };
      complexity *= shapeComplexities[interaction.shapeId] || 1.0;
    }
    
    // Complexidade baseada na posição
    if (interaction.position) {
      const positionComplexity = this.calculatePositionComplexity(interaction.position);
      complexity *= positionComplexity;
    }
    
    return Math.min(2.0, complexity);
  }

  calculateVisualizationQuality(accuracy, processingTime, complexity) {
    const timeScore = Math.max(0, Math.min(1, (4000 - processingTime) / 3000));
    const complexityBonus = (complexity - 1) * 0.1;
    
    return (accuracy * 0.6) + (timeScore * 0.4) + complexityBonus;
  }

  calculateVisualizationConsistency(interactions) {
    if (interactions.length < 3) return 0;
    
    const visualizationQualities = interactions
      .filter(i => i.spatialVisualization)
      .map(i => {
        const accuracy = i.isCorrect ? 1 : 0;
        const complexity = this.getSpatialComplexity(i);
        return this.calculateVisualizationQuality(accuracy, i.responseTime || 2000, complexity);
      });
    
    if (visualizationQualities.length === 0) return 0;
    
    const variance = this.calculateVariance(visualizationQualities);
    return Math.max(0, (1 - variance) * 0.15);
  }

  inferVisualizationFromSequences(data) {
    const sequences = data.spatialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let inferredVisualization = 0;
    
    sequences.forEach(sequence => {
      const sequenceComplexity = this.getSequenceVisualComplexity(sequence);
      const accuracy = sequence.isCorrect ? 1 : 0;
      
      inferredVisualization += accuracy * sequenceComplexity;
    });
    
    return Math.max(0, Math.min(1, inferredVisualization / sequences.length));
  }

  calculateOrientationAccuracy(spatialLayout) {
    // Simplificado - calcularia precisão da orientação espacial
    if (!spatialLayout.targetOrientation || !spatialLayout.playerOrientation) return 0.7;
    
    const orientationDifference = Math.abs(spatialLayout.targetOrientation - spatialLayout.playerOrientation);
    const accuracy = Math.max(0, 1 - (orientationDifference / 180)); // Normalizado para 180 graus
    
    return accuracy;
  }

  getLayoutComplexity(spatialLayout) {
    // Complexidade baseada no número de elementos e distribuição
    const elementCount = spatialLayout.elements ? spatialLayout.elements.length : 1;
    const distribution = spatialLayout.distribution || 'simple';
    
    const distributionComplexities = {
      simple: 1.0,
      moderate: 1.2,
      complex: 1.4,
      scattered: 1.6
    };
    
    const complexity = (elementCount / 5) * distributionComplexities[distribution];
    return Math.min(2.0, complexity);
  }

  inferOrientationFromSequence(sequence) {
    // Inferir orientação baseado na sequência de formas
    if (!sequence.targetSequence) return 0.7;
    
    const sequenceLength = sequence.targetSequence.length;
    const uniqueShapes = new Set(sequence.targetSequence).size;
    
    // Sequências com mais variedade requerem melhor orientação espacial
    const orientationDemand = uniqueShapes / sequenceLength;
    const accuracy = sequence.isCorrect ? 1 : 0;
    
    return accuracy * orientationDemand;
  }

  identifyTransformations(interactions) {
    const transformations = [];
    
    for (let i = 1; i < interactions.length; i++) {
      const current = interactions[i];
      const previous = interactions[i - 1];
      
      const transformation = this.detectTransformation(previous, current);
      if (transformation) {
        transformations.push({
          type: transformation.type,
          isCorrect: current.isCorrect,
          responseTime: current.responseTime,
          difficulty: transformation.difficulty
        });
      }
    }
    
    return transformations;
  }

  detectTransformation(previous, current) {
    // Simplificado - detectaria transformações espaciais
    if (previous.shapeId !== current.shapeId) {
      return { type: 'shape_change', difficulty: 1.2 };
    }
    
    if (previous.position && current.position) {
      const distance = this.calculateDistance(previous.position, current.position);
      if (distance > 50) {
        return { type: 'position_change', difficulty: 1.1 };
      }
    }
    
    return null;
  }

  getTransformationDifficulty(transformationType) {
    const difficulties = {
      shape_change: 1.2,
      position_change: 1.1,
      rotation: 1.4,
      scaling: 1.3,
      reflection: 1.5
    };
    
    return difficulties[transformationType] || 1.0;
  }

  calculateTransformationQuality(accuracy, processingTime, difficulty) {
    const timeScore = Math.max(0, Math.min(1, (3000 - processingTime) / 2000));
    const difficultyBonus = (difficulty - 1) * 0.2;
    
    return (accuracy * 0.7) + (timeScore * 0.3) + difficultyBonus;
  }

  calculateSpatialMemoryLoad(sequence) {
    if (!sequence.targetSequence) return 0.5;
    
    const sequenceLength = sequence.targetSequence.length;
    const uniqueElements = new Set(sequence.targetSequence).size;
    
    // Carga baseada em comprimento e variedade
    const lengthLoad = Math.min(1, sequenceLength / 6);
    const varietyLoad = Math.min(1, uniqueElements / 4);
    
    return (lengthLoad + varietyLoad) / 2;
  }

  calculateSpatialRetention(sequence) {
    const showTime = sequence.showTime || 5000;
    const accuracy = sequence.isCorrect ? 1 : 0;
    
    // Retenção é inversamente proporcional ao tempo de exibição
    const retentionChallenge = Math.min(1, showTime / 10000);
    const retentionQuality = accuracy * (1 + retentionChallenge);
    
    return Math.min(1, retentionQuality);
  }

  calculateSpatialInterferenceResistance(sequence) {
    // Simplificado - calcularia resistência à interferência espacial
    const complexity = this.getSequenceVisualComplexity(sequence);
    const accuracy = sequence.isCorrect ? 1 : 0;
    
    // Maior complexidade com acerto indica boa resistência à interferência
    return accuracy * complexity;
  }

  calculateLogicalComplexity(sequence) {
    if (!sequence.targetSequence) return 0.5;
    
    // Analisar padrões lógicos na sequência
    const hasPattern = this.detectLogicalPattern(sequence.targetSequence);
    const sequenceLength = sequence.targetSequence.length;
    
    const patternComplexity = hasPattern ? 1.3 : 1.0;
    const lengthComplexity = Math.min(1.5, sequenceLength / 4);
    
    return Math.min(2.0, patternComplexity * lengthComplexity);
  }

  calculateReasoningAccuracy(sequence) {
    // Precisão do raciocínio baseada na correção da sequência
    return sequence.isCorrect ? 1 : 0;
  }

  assessInferenceQuality(sequence) {
    // Qualidade das inferências espaciais
    if (!sequence.playerSequence || !sequence.targetSequence) return 0.7;
    
    const partialCorrectness = this.calculatePartialCorrectness(sequence);
    const logicalConsistency = this.assessLogicalConsistency(sequence.playerSequence);
    
    return (partialCorrectness * 0.6) + (logicalConsistency * 0.4);
  }

  identifyNavigationPatterns(interactions) {
    const patterns = [];
    
    if (interactions.length < 3) return patterns;
    
    // Identificar padrões de navegação
    for (let i = 0; i < interactions.length - 2; i++) {
      const segment = interactions.slice(i, i + 3);
      const pattern = this.analyzeNavigationSegment(segment);
      
      if (pattern) {
        patterns.push(pattern);
      }
    }
    
    return patterns;
  }

  analyzeNavigationSegment(segment) {
    // Analisar segmento de navegação
    const positions = segment
      .filter(i => i.position)
      .map(i => i.position);
    
    if (positions.length < 2) return null;
    
    const totalDistance = this.calculateTotalDistance(positions);
    const efficiency = this.calculatePathEfficiency(positions);
    const accuracy = segment.filter(i => i.isCorrect).length / segment.length;
    
    return {
      totalDistance,
      efficiency,
      accuracy,
      segmentLength: segment.length
    };
  }

  calculateNavigationEfficiency(pattern) {
    // Eficiência baseada na distância percorrida vs. distância ideal
    return Math.min(1, pattern.efficiency || 0.7);
  }

  assessPathOptimization(pattern) {
    // Avaliação da otimização do caminho
    const efficiency = pattern.efficiency || 0.7;
    const accuracy = pattern.accuracy || 0.7;
    
    // Caminhos mais eficientes com boa precisão indicam otimização
    return (efficiency * 0.6) + (accuracy * 0.4);
  }

  calculatePositionAccuracy(spatialPosition) {
    if (!spatialPosition.target || !spatialPosition.actual) return 0.7;
    
    const distance = this.calculateDistance(spatialPosition.target, spatialPosition.actual);
    const tolerance = spatialPosition.tolerance || 50;
    
    const accuracy = Math.max(0, 1 - (distance / tolerance));
    return accuracy;
  }

  calculateDistanceAccuracy(spatialPosition) {
    // Precisão na percepção de distâncias
    if (!spatialPosition.expectedDistance || !spatialPosition.perceivedDistance) return 0.7;
    
    const distanceError = Math.abs(spatialPosition.expectedDistance - spatialPosition.perceivedDistance);
    const relativeError = distanceError / spatialPosition.expectedDistance;
    
    const accuracy = Math.max(0, 1 - relativeError);
    return accuracy;
  }

  calculateDirectionAccuracy(spatialPosition) {
    // Precisão na percepção de direções
    if (!spatialPosition.expectedDirection || !spatialPosition.perceivedDirection) return 0.7;
    
    const directionError = Math.abs(spatialPosition.expectedDirection - spatialPosition.perceivedDirection);
    const normalizedError = Math.min(directionError, 360 - directionError) / 180; // Normalizado para 180 graus
    
    const accuracy = Math.max(0, 1 - normalizedError);
    return accuracy;
  }

  inferAccuracyFromSequences(data) {
    const sequences = data.spatialData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    const accuracies = sequences.map(s => s.isCorrect ? 1 : 0);
    const avgAccuracy = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    
    return avgAccuracy;
  }

  calculateSpatialEfficiency(processingTime, accuracy, spatialComplexity) {
    const timeScore = Math.max(0, Math.min(1, (3000 - processingTime) / 2000));
    const complexityBonus = (spatialComplexity - 1) * 0.1;
    
    // Balancear velocidade, precisão e complexidade
    return (accuracy * 0.5) + (timeScore * 0.4) + complexityBonus;
  }

  calculateEfficiencyImprovement(efficiencyScores) {
    if (efficiencyScores.length < 4) return 0;
    
    const firstHalf = efficiencyScores.slice(0, Math.floor(efficiencyScores.length / 2));
    const secondHalf = efficiencyScores.slice(Math.floor(efficiencyScores.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, eff) => sum + eff, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, eff) => sum + eff, 0) / secondHalf.length;
    
    const improvement = secondAvg - firstAvg;
    return Math.max(0, Math.min(0.2, improvement)); // Max 0.2 bonus
  }

  // Métodos auxiliares gerais

  calculatePositionComplexity(position) {
    // Complexidade baseada na posição (centro vs. bordas, etc.)
    const centerDistance = this.calculateDistanceFromCenter(position);
    const edgeProximity = this.calculateEdgeProximity(position);
    
    // Posições mais periféricas são mais complexas
    return 1 + (centerDistance * 0.3) + (edgeProximity * 0.2);
  }

  getSequenceVisualComplexity(sequence) {
    if (!sequence.targetSequence) return 1.0;
    
    const uniqueShapes = new Set(sequence.targetSequence).size;
    const sequenceLength = sequence.targetSequence.length;
    
    // Complexidade baseada na variedade e comprimento
    const varietyComplexity = uniqueShapes / 3; // Normalizado para 3 formas únicas
    const lengthComplexity = sequenceLength / 5; // Normalizado para 5 elementos
    
    return Math.min(2.0, (varietyComplexity + lengthComplexity) / 2);
  }

  detectLogicalPattern(sequence) {
    if (sequence.length < 3) return false;
    
    // Detectar padrões simples (repetição, alternância, etc.)
    const hasRepetition = this.detectRepetitionPattern(sequence);
    const hasAlternation = this.detectAlternationPattern(sequence);
    const hasProgression = this.detectProgressionPattern(sequence);
    
    return hasRepetition || hasAlternation || hasProgression;
  }

  detectRepetitionPattern(sequence) {
    // Detectar padrões de repetição
    for (let patternLength = 1; patternLength <= Math.floor(sequence.length / 2); patternLength++) {
      const pattern = sequence.slice(0, patternLength);
      let isRepeating = true;
      
      for (let i = patternLength; i < sequence.length; i++) {
        if (sequence[i] !== pattern[i % patternLength]) {
          isRepeating = false;
          break;
        }
      }
      
      if (isRepeating) return true;
    }
    
    return false;
  }

  detectAlternationPattern(sequence) {
    // Detectar padrões de alternância
    if (sequence.length < 4) return false;
    
    for (let i = 2; i < sequence.length; i++) {
      if (sequence[i] !== sequence[i - 2]) {
        return false;
      }
    }
    
    return true;
  }

  detectProgressionPattern(sequence) {
    // Detectar padrões de progressão (simplificado)
    if (sequence.length < 3) return false;
    
    // Verificar se há uma progressão baseada na ordem das formas
    const shapeOrder = ['circle', 'square', 'triangle', 'diamond', 'star', 'heart'];
    const indices = sequence.map(shape => shapeOrder.indexOf(shape));
    
    if (indices.includes(-1)) return false; // Forma não encontrada
    
    // Verificar progressão crescente ou decrescente
    let increasing = true;
    let decreasing = true;
    
    for (let i = 1; i < indices.length; i++) {
      if (indices[i] <= indices[i - 1]) increasing = false;
      if (indices[i] >= indices[i - 1]) decreasing = false;
    }
    
    return increasing || decreasing;
  }

  calculatePartialCorrectness(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    
    const target = sequence.targetSequence;
    const player = sequence.playerSequence;
    const minLength = Math.min(target.length, player.length);
    
    let correct = 0;
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        correct++;
      }
    }
    
    return correct / target.length;
  }

  assessLogicalConsistency(playerSequence) {
    if (playerSequence.length < 3) return 0.7;
    
    // Verificar se a sequência do jogador tem alguma lógica interna
    const hasInternalLogic = this.detectLogicalPattern(playerSequence);
    
    return hasInternalLogic ? 0.9 : 0.6;
  }

  calculateDistance(pos1, pos2) {
    if (!pos1 || !pos2) return 0;
    
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    
    return Math.sqrt(dx * dx + dy * dy);
  }

  calculateTotalDistance(positions) {
    if (positions.length < 2) return 0;
    
    let totalDistance = 0;
    for (let i = 1; i < positions.length; i++) {
      totalDistance += this.calculateDistance(positions[i - 1], positions[i]);
    }
    
    return totalDistance;
  }

  calculatePathEfficiency(positions) {
    if (positions.length < 2) return 1;
    
    const totalDistance = this.calculateTotalDistance(positions);
    const directDistance = this.calculateDistance(positions[0], positions[positions.length - 1]);
    
    if (totalDistance === 0) return 1;
    
    const efficiency = directDistance / totalDistance;
    return Math.min(1, efficiency);
  }

  calculateDistanceFromCenter(position) {
    // Assumindo um espaço de 800x600
    const center = { x: 400, y: 300 };
    const distance = this.calculateDistance(position, center);
    const maxDistance = Math.sqrt(400 * 400 + 300 * 300);
    
    return distance / maxDistance;
  }

  calculateEdgeProximity(position) {
    // Proximidade às bordas (assumindo espaço 800x600)
    const edges = [
      position.x, // distância da borda esquerda
      800 - position.x, // distância da borda direita
      position.y, // distância da borda superior
      600 - position.y // distância da borda inferior
    ];
    
    const minDistanceToEdge = Math.min(...edges);
    return 1 - (minDistanceToEdge / 100); // Normalizado para 100 pixels
  }

  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  identifySpatialStrategies(data) {
    const strategies = [];
    const interactions = data.spatialData.interactions || [];
    
    if (interactions.length === 0) return strategies;
    
    // Identificar estratégias espaciais
    if (this.detectVisualizationStrategy(interactions)) {
      strategies.push('visualization');
    }
    
    if (this.detectSpatialChunkingStrategy(interactions)) {
      strategies.push('spatial_chunking');
    }
    
    if (this.detectLandmarkStrategy(interactions)) {
      strategies.push('landmark_navigation');
    }
    
    if (this.detectSystematicScanningStrategy(interactions)) {
      strategies.push('systematic_scanning');
    }
    
    return strategies;
  }

  detectVisualizationStrategy(interactions) {
    // Detectar uso de estratégia de visualização
    const visualizationTasks = interactions.filter(i => i.spatialVisualization);
    if (visualizationTasks.length === 0) return false;
    
    const avgAccuracy = visualizationTasks.filter(i => i.isCorrect).length / visualizationTasks.length;
    return avgAccuracy > 0.7;
  }

  detectSpatialChunkingStrategy(interactions) {
    // Detectar agrupamento espacial
    const positions = interactions
      .filter(i => i.position)
      .map(i => i.position);
    
    if (positions.length < 4) return false;
    
    // Verificar se há clusters de interações
    const clusters = this.identifyPositionClusters(positions);
    return clusters.length > 1 && clusters.length < positions.length / 2;
  }

  detectLandmarkStrategy(interactions) {
    // Detectar uso de pontos de referência
    const positions = interactions
      .filter(i => i.position)
      .map(i => i.position);
    
    if (positions.length < 3) return false;
    
    // Verificar se há retorno a posições similares (indicativo de landmarks)
    let landmarkReturns = 0;
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 2; j < positions.length; j++) {
        const distance = this.calculateDistance(positions[i], positions[j]);
        if (distance < 50) { // Dentro de 50 pixels
          landmarkReturns++;
        }
      }
    }
    
    return landmarkReturns > positions.length / 4;
  }

  detectSystematicScanningStrategy(interactions) {
    // Detectar varredura sistemática
    const positions = interactions
      .filter(i => i.position)
      .map(i => i.position);
    
    if (positions.length < 4) return false;
    
    // Verificar se há padrão sistemático nas posições
    const systematicPattern = this.detectSystematicPattern(positions);
    return systematicPattern;
  }

  identifyPositionClusters(positions) {
    // Simplificado - identificaria clusters de posições
    const clusters = [];
    const threshold = 100; // Distância máxima para formar cluster
    
    positions.forEach(pos => {
      let addedToCluster = false;
      
      for (let cluster of clusters) {
        const centerDistance = this.calculateDistance(pos, cluster.center);
        if (centerDistance <= threshold) {
          cluster.positions.push(pos);
          // Recalcular centro
          cluster.center = this.calculateClusterCenter(cluster.positions);
          addedToCluster = true;
          break;
        }
      }
      
      if (!addedToCluster) {
        clusters.push({
          center: pos,
          positions: [pos]
        });
      }
    });
    
    return clusters;
  }

  calculateClusterCenter(positions) {
    const sumX = positions.reduce((sum, pos) => sum + pos.x, 0);
    const sumY = positions.reduce((sum, pos) => sum + pos.y, 0);
    
    return {
      x: sumX / positions.length,
      y: sumY / positions.length
    };
  }

  detectSystematicPattern(positions) {
    // Detectar padrão sistemático (horizontal, vertical, diagonal)
    if (positions.length < 3) return false;
    
    // Verificar movimento horizontal
    const horizontalMovement = this.detectHorizontalPattern(positions);
    const verticalMovement = this.detectVerticalPattern(positions);
    const diagonalMovement = this.detectDiagonalPattern(positions);
    
    return horizontalMovement || verticalMovement || diagonalMovement;
  }

  detectHorizontalPattern(positions) {
    // Detectar movimento predominantemente horizontal
    let horizontalMovements = 0;
    
    for (let i = 1; i < positions.length; i++) {
      const dx = Math.abs(positions[i].x - positions[i - 1].x);
      const dy = Math.abs(positions[i].y - positions[i - 1].y);
      
      if (dx > dy * 2) { // Movimento predominantemente horizontal
        horizontalMovements++;
      }
    }
    
    return horizontalMovements > positions.length / 2;
  }

  detectVerticalPattern(positions) {
    // Detectar movimento predominantemente vertical
    let verticalMovements = 0;
    
    for (let i = 1; i < positions.length; i++) {
      const dx = Math.abs(positions[i].x - positions[i - 1].x);
      const dy = Math.abs(positions[i].y - positions[i - 1].y);
      
      if (dy > dx * 2) { // Movimento predominantemente vertical
        verticalMovements++;
      }
    }
    
    return verticalMovements > positions.length / 2;
  }

  detectDiagonalPattern(positions) {
    // Detectar movimento diagonal
    let diagonalMovements = 0;
    
    for (let i = 1; i < positions.length; i++) {
      const dx = Math.abs(positions[i].x - positions[i - 1].x);
      const dy = Math.abs(positions[i].y - positions[i - 1].y);
      
      const ratio = Math.min(dx, dy) / Math.max(dx, dy);
      if (ratio > 0.5) { // Movimento relativamente diagonal
        diagonalMovements++;
      }
    }
    
    return diagonalMovements > positions.length / 2;
  }

  generateSpatialInsights(data) {
    const insights = [];
    const interactions = data.spatialData.interactions || [];
    const sequences = data.spatialData.sequences || [];
    
    // Analisar processamento espacial geral
    if (interactions.length > 0) {
      const avgAccuracy = interactions.filter(i => i.isCorrect).length / interactions.length;
      
      if (avgAccuracy < 0.6) {
        insights.push('Dificuldades no processamento espacial geral');
      } else if (avgAccuracy > 0.8) {
        insights.push('Excelente capacidade de processamento espacial');
      }
    }
    
    // Analisar complexidade espacial
    if (sequences.length > 0) {
      const complexSequences = sequences.filter(s => this.getSequenceVisualComplexity(s) > 1.5);
      const complexAccuracy = complexSequences.filter(s => s.isCorrect).length / Math.max(1, complexSequences.length);
      
      if (complexAccuracy < 0.5) {
        insights.push('Limitações com tarefas espaciais complexas');
      } else if (complexAccuracy > 0.7) {
        insights.push('Boa adaptação a complexidade espacial');
      }
    }
    
    // Analisar estratégias identificadas
    const strategies = this.identifySpatialStrategies(data);
    if (strategies.length > 0) {
      insights.push(`Estratégias espaciais identificadas: ${strategies.join(', ')}`);
    }
    
    return insights;
  }
}
