/**
 * 🧪 TESTE DO FLUXO COMPLETO DE MÉTRICAS
 * Verifica se coletores → hubs → processadores → analisadores → orquestrador estão funcionando
 * Portal Betina V3
 */

// Imports com tratamento de erro
let ColorMatchCollectorsHub, GameSpecificProcessors, getSystemOrchestrator, getCognitiveAnalyzer;

try {
  const colorMatchModule = await import('../games/ColorMatch/collectors/index.js');
  ColorMatchCollectorsHub = colorMatchModule.ColorMatchCollectorsHub;

  const processorsModule = await import('../api/services/processors/GameSpecificProcessors.js');
  GameSpecificProcessors = processorsModule.GameSpecificProcessors;

  const orchestratorModule = await import('../api/services/core/SystemOrchestrator.js');
  getSystemOrchestrator = orchestratorModule.getSystemOrchestrator;

  const cognitiveModule = await import('../api/services/analysis/CognitiveAnalyzer.js');
  getCognitiveAnalyzer = cognitiveModule.getCognitiveAnalyzer;

  console.log('✅ Todos os módulos importados com sucesso');
} catch (error) {
  console.error('❌ ERRO CRÍTICO NOS IMPORTS:', error.message);
  process.exit(1);
}

/**
 * Teste completo do fluxo de métricas
 */
async function testMetricsFlow() {
  console.log('🧪 INICIANDO TESTE DO FLUXO DE MÉTRICAS');
  console.log('=====================================');

  // Verificar se os módulos foram importados
  if (!ColorMatchCollectorsHub || !GameSpecificProcessors || !getSystemOrchestrator || !getCognitiveAnalyzer) {
    console.error('❌ ERRO: Módulos não foram importados corretamente');
    return { success: false, error: 'Módulos não importados' };
  }

  try {
    // 1. TESTE DOS COLETORES
    console.log('\n1️⃣ TESTANDO COLETORES...');
    const collectorsHub = new ColorMatchCollectorsHub();
    
    const mockGameData = {
      sessionId: 'test_session_001',
      userId: 'test_user',
      gameId: 'ColorMatch',
      sessionDuration: 120000,
      difficulty: 'medium',
      totalColors: 10,
      correctMatches: 8,
      accuracy: 0.8,
      // ✅ CORRIGIDO: Adicionando campos obrigatórios para ColorMatch
      selectedItems: [
        { id: 1, color: 'red', correct: true, responseTime: 1200 },
        { id: 2, color: 'green', correct: false, responseTime: 2100 },
        { id: 3, color: 'yellow', correct: true, responseTime: 900 }
      ],
      targetColors: ['red', 'blue', 'yellow'],
      colorData: {
        interactions: [
          { correct: true, targetColor: 'red', selectedColor: 'red', responseTime: 1200 },
          { correct: false, targetColor: 'blue', selectedColor: 'green', responseTime: 2100 },
          { correct: true, targetColor: 'yellow', selectedColor: 'yellow', responseTime: 900 }
        ]
      }
    };

    const collectorsResult = await collectorsHub.runCompleteAnalysis(mockGameData);
    console.log('✅ Coletores funcionando:', !!collectorsResult);
    console.log('📊 Métricas coletadas:', Object.keys(collectorsResult.collectorsResults || {}));

    // 2. TESTE DOS PROCESSADORES
    console.log('\n2️⃣ TESTANDO PROCESSADORES...');
    const mockDb = {
      save: async (data) => ({ id: Date.now(), ...data }),
      find: async (query) => [],
      update: async (id, data) => ({ id, ...data })
    };

    const gameProcessors = new GameSpecificProcessors(mockDb);
    const processedData = await gameProcessors.processGameData('ColorMatch', mockGameData);
    console.log('✅ Processadores funcionando:', !!processedData);
    console.log('🔄 Dados processados:', !!processedData.therapeuticAnalysis);

    // 3. TESTE DOS ANALISADORES
    console.log('\n3️⃣ TESTANDO ANALISADORES...');
    const cognitiveAnalyzer = await getCognitiveAnalyzer();
    
    // ✅ CORRIGIDO: Dados corretos para análise cognitiva
    const mockAnalysisData = {
      sessionId: 'test_session_001',
      childId: 'test_user',
      gameName: 'ColorMatch',
      gameId: 'ColorMatch',
      duration: 120000,
      accuracy: 0.8,
      responseTime: 1400,
      interactions: [
        { type: 'focus', duration: 5000 },
        { type: 'memory', correct: true, responseTime: 1200 },
        { type: 'visual', correct: false, responseTime: 2100 }
      ],
      metrics: {
        accuracy: 0.8,
        responseTime: 1400,
        consistency: 0.75,
        attention: 0.82
      },
      sessionHistory: []
    };

    // ✅ CORRIGIDO: Usando método correto analyzeCognitiveSession
    const cognitiveResult = await cognitiveAnalyzer.analyzeCognitiveSession(mockAnalysisData);
    console.log('✅ Analisador Cognitivo funcionando:', !!cognitiveResult);
    console.log('🧠 Análise cognitiva:', Object.keys(cognitiveResult || {}));

    // 4. TESTE DO SYSTEM ORCHESTRATOR
    console.log('\n4️⃣ TESTANDO SYSTEM ORCHESTRATOR...');
    const systemOrchestrator = await getSystemOrchestrator(mockDb);
    
    // Injetar SystemOrchestrator no analisador
    if (cognitiveAnalyzer.setSystemOrchestrator) {
      cognitiveAnalyzer.setSystemOrchestrator(systemOrchestrator);
      console.log('✅ SystemOrchestrator injetado no CognitiveAnalyzer');
    }

    const orchestratorResult = await systemOrchestrator.processGameMetrics(
      'test_user',
      'ColorMatch',
      mockGameData
    );
    console.log('✅ SystemOrchestrator funcionando:', !!orchestratorResult);
    console.log('🎯 Resultado orquestrado:', !!orchestratorResult.therapeuticAnalysis);

    // 5. TESTE DE INTEGRAÇÃO COMPLETA
    console.log('\n5️⃣ TESTANDO INTEGRAÇÃO COMPLETA...');
    const fullFlowResult = await testFullFlow(systemOrchestrator, mockGameData);
    console.log('✅ Fluxo completo funcionando:', !!fullFlowResult);

    console.log('\n🎉 TESTE CONCLUÍDO COM SUCESSO!');
    console.log('=====================================');
    
    return {
      collectors: !!collectorsResult,
      processors: !!processedData,
      analyzers: !!cognitiveResult,
      orchestrator: !!orchestratorResult,
      fullFlow: !!fullFlowResult,
      success: true
    };

  } catch (error) {
    console.error('❌ ERRO NO TESTE:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Teste do fluxo completo
 */
async function testFullFlow(orchestrator, gameData) {
  try {
    // Simular uma sessão completa
    const sessionResult = await orchestrator.processGameInput({
      ...gameData,
      timestamp: new Date().toISOString()
    });

    return sessionResult;
  } catch (error) {
    console.error('Erro no fluxo completo:', error);
    return null;
  }
}

// Executar teste se chamado diretamente
if (typeof window === 'undefined') {
  // Verificar se é o arquivo principal sendo executado
  const isMainModule = process.argv[1] && process.argv[1].includes('metrics-flow-test.js');

  if (isMainModule) {
    console.log('🚀 Executando teste do fluxo de métricas...\n');

    testMetricsFlow()
      .then(result => {
        console.log('\n📋 RESULTADO FINAL:', result);
        if (result.success) {
          console.log('✅ TODOS OS TESTES PASSARAM!');
          process.exit(0);
        } else {
          console.log('❌ TESTES FALHARAM!');
          process.exit(1);
        }
      })
      .catch(error => {
        console.error('💥 ERRO CRÍTICO NO TESTE:', error);
        process.exit(1);
      });
  }
}

export { testMetricsFlow };
