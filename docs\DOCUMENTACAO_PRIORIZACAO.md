# Priorização de Desenvolvimento

## Decisão Tomada em 22/06/2025
- **Prioridade atual**: Finalização dos layouts visuais dos 9 jogos
- **Motivação**: 
  - Criar uma base visual sólida antes de implementar lógicas complexas
  - Facilitar a integração futura das funcionalidades de métricas
  - Permitir testes de usabilidade com foco na experiência do usuário

## Tarefas Adiadas
1. Implementação do sistema de métricas para os 9 jogos
2. Integração com algoritmos de sensores
3. Atualização do orquestrador (`OrchestratorAdapter.js`)
4. Desenvolvimento de dashboards avançados

## Progresso Atual
- ✅ `ColorMatchMetrics.js` implementado (pode ser usado como referência futura)
- 🔄 Layouts dos jogos em desenvolvimento

## Próximos Passos
1. Finalizar componentes visuais de todos os jogos
2. Realizar testes de usabilidade
3. Revisar e consolidar o sistema de design
4. Retomar desenvolvimento de lógicas após aprovação dos layouts
