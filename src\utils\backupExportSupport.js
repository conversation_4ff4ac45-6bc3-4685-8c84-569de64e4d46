/**
 * @file backupExportSupport.js
 * @description Suporte para importação e exportação de backup - Portal Betina V3
 * @version 1.0.0
 */

import { isValidBackupFormat, extractGameDataFromBackup } from './backupDataAdapter';

/**
 * Hook para importação de dados de backup
 * @returns {Object} - Funções para importar e validar dados de backup
 */
export const useBackupImport = () => {
  /**
   * Importa dados de backup para o localStorage
   * @param {Object} backupData - Os dados de backup a importar
   * @returns {Object} - Resultado da operação com sucesso e mensagem
   */
  const importBackupData = (backupData) => {
    try {
      if (!isValidBackupFormat(backupData)) {
        return { 
          success: false, 
          message: 'Formato de backup inválido ou incompatível'
        };
      }
      
      // Salvar backup no localStorage para uso por todos os dashboards
      localStorage.setItem('betina_dashboard_backup', JSON.stringify(backupData));
      
      // Extrair dados para verificação
      const extractedData = extractGameDataFromBackup(backupData);
      
      return {
        success: true,
        message: 'Backup importado com sucesso',
        data: extractedData,
        hasServerError: !!backupData.metadata?.serverError,
        serverErrorMessage: backupData.metadata?.serverError?.message
      };
    } catch (error) {
      console.error('Erro ao importar backup:', error);
      return {
        success: false,
        message: `Erro ao importar backup: ${error.message}`
      };
    }
  };
  
  /**
   * Valida dados de backup sem importá-los
   * @param {Object} backupData - Os dados de backup a validar
   * @returns {Object} - Resultado da validação
   */
  const validateBackupData = (backupData) => {
    try {
      if (!backupData) {
        return { 
          valid: false, 
          message: 'Dados de backup ausentes' 
        };
      }
      
      // Verificar formato de backup
      if (!isValidBackupFormat(backupData)) {
        return { 
          valid: false, 
          message: 'Formato de backup inválido ou incompatível' 
        };
      }
      
      // Extrair e verificar dados
      const extractedData = extractGameDataFromBackup(backupData);
      
      // Verificar dados mínimos necessários
      const hasMinimumData = extractedData &&
        (extractedData.performance.gameProgress && Object.keys(extractedData.performance.gameProgress).length > 0);
      
      if (!hasMinimumData) {
        return {
          valid: false,
          message: 'Backup não contém dados mínimos necessários'
        };
      }
      
      // Verificar erro de servidor
      const hasServerError = !!backupData.metadata?.serverError;
      
      return {
        valid: true,
        message: hasServerError ? 
          'Backup válido, mas contém aviso de erro do servidor' : 
          'Backup válido',
        data: extractedData,
        hasServerError,
        serverErrorMessage: backupData.metadata?.serverError?.message
      };
    } catch (error) {
      console.error('Erro ao validar backup:', error);
      return {
        valid: false,
        message: `Erro ao validar backup: ${error.message}`
      };
    }
  };
  
  /**
   * Importa arquivo de backup
   * @param {File} file - O arquivo de backup
   * @returns {Promise} - Promise que resolve para o resultado da importação
   */
  const importBackupFile = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const backupData = JSON.parse(e.target.result);
          const result = importBackupData(backupData);
          resolve(result);
        } catch (error) {
          reject({
            success: false,
            message: `Erro ao processar arquivo: ${error.message}`
          });
        }
      };
      
      reader.onerror = () => {
        reject({
          success: false,
          message: 'Erro ao ler arquivo'
        });
      };
      
      reader.readAsText(file);
    });
  };
  
  return {
    importBackupData,
    validateBackupData,
    importBackupFile
  };
};

/**
 * Verifica se existe backup importado no localStorage
 * @returns {boolean} - Verdadeiro se existir backup importado
 */
export const hasImportedBackup = () => {
  try {
    const backupData = localStorage.getItem('betina_dashboard_backup');
    return !!backupData;
  } catch (error) {
    console.error('Erro ao verificar backup:', error);
    return false;
  }
};

/**
 * Obtém dados de backup importado no localStorage
 * @returns {Object|null} - Dados de backup ou null se não existir
 */
export const getImportedBackup = () => {
  try {
    const backupData = localStorage.getItem('betina_dashboard_backup');
    if (!backupData) return null;
    
    return JSON.parse(backupData);
  } catch (error) {
    console.error('Erro ao obter backup:', error);
    return null;
  }
};

/**
 * Limpa backup importado do localStorage
 * @returns {boolean} - Verdadeiro se a operação foi bem-sucedida
 */
export const clearImportedBackup = () => {
  try {
    localStorage.removeItem('betina_dashboard_backup');
    return true;
  } catch (error) {
    console.error('Erro ao limpar backup:', error);
    return false;
  }
};
