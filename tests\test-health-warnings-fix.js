/**
 * @file test-health-warnings-fix.js
 * @description Teste para verificar se os warnings de health check foram corrigidos
 */

import SystemOrchestrator from './src/api/services/core/SystemOrchestrator.js';

async function testHealthWarningsFix() {
  console.log('=== TESTE DE CORREÇÃO DOS WARNINGS DE HEALTH CHECK ===');
  
  try {
    // Criar instância do SystemOrchestrator com inicialização assíncrona
    console.log('1. Inicializando SystemOrchestrator...');
    const orchestrator = await SystemOrchestrator.getInstance();
    
    // Aguardar um pouco para os health checks serem executados
    console.log('2. Aguardando health checks...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Verificar status dos componentes que estavam falhando
    console.log('3. Verificando status dos componentes...');
    
    const healthService = orchestrator.healthCheck;
    const overallHealth = await healthService.getOverallHealth();
    
    console.log(`Status geral do sistema: ${overallHealth.status}`);
    console.log(`Componentes monitorados: ${Object.keys(overallHealth.components).length}`);
    
    // Verificar especificamente os componentes que estavam com warnings
    const problematicComponents = ['progress_analyzer', 'session_analyzer', 'metrics_validator'];
    
    for (const componentName of problematicComponents) {
      const componentHealth = overallHealth.components[componentName];
      if (componentHealth) {
        console.log(`${componentName}: ${componentHealth.status} (falhas: ${componentHealth.consecutiveFailures})`);
        if (componentHealth.metrics && componentHealth.metrics.initialized !== undefined) {
          console.log(`  - Inicializado: ${componentHealth.metrics.initialized}`);
        }
      } else {
        console.log(`${componentName}: não encontrado`);
      }
    }
    
    // Verificar se ainda temos components em estado crítico
    const criticalComponents = Object.entries(overallHealth.components)
      .filter(([name, health]) => health.status === 'unhealthy' || health.consecutiveFailures >= 3)
      .map(([name]) => name);
    
    console.log('\n=== RESULTADO ===');
    if (criticalComponents.length === 0) {
      console.log('✅ Sucesso: Nenhum componente com falhas críticas detectado!');
      console.log('Os warnings de health check foram corrigidos.');
    } else {
      console.log('❌ Ainda existem componentes com problemas:');
      criticalComponents.forEach(comp => console.log(`  - ${comp}`));
    }
    
  } catch (error) {
    console.error('Erro durante o teste:', error);
  }
}

// Executar o teste
testHealthWarningsFix().catch(console.error);
