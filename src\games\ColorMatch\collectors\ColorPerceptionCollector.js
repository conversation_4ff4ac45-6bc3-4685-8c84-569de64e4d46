/**
 * 🎨 COLOR PERCEPTION COLLECTOR
 * Coletor especializado em análise de percepção e discriminação de cores para ColorMatch
 * Portal Betina V3
 */

export class ColorPerceptionCollector {
  constructor() {
    this.colorSpaces = {
      rgb: 'Red-Green-Blue',
      hsv: 'Hue-Saturation-Value',
      lab: 'CIELAB Color Space'
    };
    
    this.perceptionThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.70,
      poor: 0.50,
      critical: 0.30
    };
    
    this.colorCategories = {
      primary: ['vermelho', 'azul', 'amarelo'],
      secondary: ['verde', 'laranja', 'roxo'],
      tertiary: ['rosa', 'marrom', 'cinza'],
      neutral: ['branco', 'preto']
    };
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de percepção de cores
   */
  collect(data) {
    return this.analyze(data);
  }

  async analyze(data) {
    if (!data || !data.colorData) {
      console.warn('ColorPerceptionCollector: Dados inválidos recebidos', data);
      return {
        colorDiscrimination: 0.7,
        huePerception: 0.7,
        saturationSensitivity: 0.7,
        brightnessDiscrimination: 0.7,
        colorConstancy: 0.7,
        perceptualAccuracy: 0.7,
        colorMemory: 0.7,
        adaptationEfficiency: 0.7
      };
    }

    return {
      colorDiscrimination: this.assessColorDiscrimination(data),
      huePerception: this.assessHuePerception(data),
      saturationSensitivity: this.assessSaturationSensitivity(data),
      brightnessDiscrimination: this.assessBrightnessDiscrimination(data),
      colorConstancy: this.assessColorConstancy(data),
      perceptualAccuracy: this.calculatePerceptualAccuracy(data),
      colorMemory: this.assessColorMemory(data),
      adaptationEfficiency: this.calculateAdaptationEfficiency(data),
      colorConfusionMatrix: this.generateColorConfusionMatrix(data),
      dominantColorPreferences: this.identifyColorPreferences(data)
    };
  }

  assessColorDiscrimination(data) {
    const colorResponses = data.colorData.interactions || [];
    const correctColorMatches = colorResponses.filter(response => response.correct).length;
    const totalColorAttempts = colorResponses.length;
    
    if (totalColorAttempts === 0) return 0.7;
    
    let discriminationScore = correctColorMatches / totalColorAttempts;
    
    // Ajustar baseado na dificuldade das cores
    const difficultColors = this.identifyDifficultColors(colorResponses);
    if (difficultColors.length > 0) {
      const difficultAccuracy = this.calculateDifficultColorAccuracy(colorResponses, difficultColors);
      discriminationScore = (discriminationScore * 0.7) + (difficultAccuracy * 0.3);
    }
    
    // Penalizar confusões sistemáticas
    const systematicErrors = this.identifySystematicColorErrors(colorResponses);
    if (systematicErrors.length > 0) {
      discriminationScore *= (1 - (systematicErrors.length * 0.1));
    }
    
    return Math.max(0, Math.min(1, discriminationScore));
  }

  assessHuePerception(data) {
    const colorResponses = data.colorData.interactions || [];
    const hueErrors = [];
    
    colorResponses.forEach(response => {
      if (!response.correct && response.targetColor && response.selectedColor) {
        const hueDistance = this.calculateHueDistance(response.targetColor, response.selectedColor);
        hueErrors.push(hueDistance);
      }
    });
    
    if (hueErrors.length === 0) return 0.9;
    
    const averageHueError = hueErrors.reduce((sum, error) => sum + error, 0) / hueErrors.length;
    const maxHueError = 180; // Máximo possível no círculo cromático
    
    const hueAccuracy = 1 - (averageHueError / maxHueError);
    
    // Bonus para consistência na percepção de matiz
    const consistencyBonus = this.calculateHueConsistency(colorResponses) * 0.1;
    
    return Math.max(0, Math.min(1, hueAccuracy + consistencyBonus));
  }

  assessSaturationSensitivity(data) {
    const colorResponses = data.colorData.interactions || [];
    const saturationTests = colorResponses.filter(response => 
      response.targetColor && response.selectedColor
    );
    
    if (saturationTests.length === 0) return 0.7;
    
    let saturationScore = 0;
    let saturationTestCount = 0;
    
    saturationTests.forEach(response => {
      const targetSaturation = this.extractSaturation(response.targetColor);
      const selectedSaturation = this.extractSaturation(response.selectedColor);
      
      if (targetSaturation !== null && selectedSaturation !== null) {
        const saturationDifference = Math.abs(targetSaturation - selectedSaturation);
        const sensitivity = 1 - (saturationDifference / 100); // Normalizado para 0-1
        saturationScore += Math.max(0, sensitivity);
        saturationTestCount++;
      }
    });
    
    return saturationTestCount > 0 ? saturationScore / saturationTestCount : 0.7;
  }

  assessBrightnessDiscrimination(data) {
    const colorResponses = data.colorData.interactions || [];
    const brightnessTests = colorResponses.filter(response => 
      response.targetColor && response.selectedColor
    );
    
    if (brightnessTests.length === 0) return 0.7;
    
    let brightnessScore = 0;
    let brightnessTestCount = 0;
    
    brightnessTests.forEach(response => {
      const targetBrightness = this.extractBrightness(response.targetColor);
      const selectedBrightness = this.extractBrightness(response.selectedColor);
      
      if (targetBrightness !== null && selectedBrightness !== null) {
        const brightnessDifference = Math.abs(targetBrightness - selectedBrightness);
        const discrimination = 1 - (brightnessDifference / 100);
        brightnessScore += Math.max(0, discrimination);
        brightnessTestCount++;
      }
    });
    
    return brightnessTestCount > 0 ? brightnessScore / brightnessTestCount : 0.7;
  }

  assessColorConstancy(data) {
    // Avalia a capacidade de perceber cores de forma consistente sob diferentes condições
    const colorResponses = data.colorData.interactions || [];
    const sessionDuration = data.sessionDuration || 0;
    
    // Analisar consistência ao longo do tempo
    const timeSegments = this.divideResponsesByTime(colorResponses, 3);
    
    if (timeSegments.length < 2) return 0.7;
    
    const segmentAccuracies = timeSegments.map(segment => {
      const correct = segment.filter(r => r.correct).length;
      return segment.length > 0 ? correct / segment.length : 0;
    });
    
    // Calcular variabilidade entre segmentos
    const meanAccuracy = segmentAccuracies.reduce((sum, acc) => sum + acc, 0) / segmentAccuracies.length;
    const variance = segmentAccuracies.reduce((sum, acc) => sum + Math.pow(acc - meanAccuracy, 2), 0) / segmentAccuracies.length;
    const constancyScore = 1 - Math.sqrt(variance);
    
    // Ajustar baseado na duração da sessão (sessões mais longas testam melhor a constância)
    const durationFactor = Math.min(1, sessionDuration / 300000); // 5 minutos como referência
    
    return Math.max(0, Math.min(1, constancyScore * (0.7 + 0.3 * durationFactor)));
  }

  calculatePerceptualAccuracy(data) {
    const colorResponses = data.colorData.interactions || [];
    
    if (colorResponses.length === 0) return 0.7;
    
    // Calcular precisão ponderada por dificuldade da cor
    let weightedAccuracy = 0;
    let totalWeight = 0;
    
    colorResponses.forEach(response => {
      const colorDifficulty = this.assessColorDifficulty(response.targetColor);
      const weight = colorDifficulty + 0.5; // Pesos entre 0.5 e 1.5
      
      weightedAccuracy += (response.correct ? 1 : 0) * weight;
      totalWeight += weight;
    });
    
    const baseAccuracy = totalWeight > 0 ? weightedAccuracy / totalWeight : 0.7;
    
    // Bonus por velocidade (precisão mantida com rapidez)
    const avgResponseTime = this.calculateAverageResponseTime(colorResponses);
    const speedBonus = avgResponseTime < 2000 ? 0.1 : avgResponseTime < 4000 ? 0.05 : 0;
    
    return Math.max(0, Math.min(1, baseAccuracy + speedBonus));
  }

  assessColorMemory(data) {
    const colorResponses = data.colorData.interactions || [];
    const repeatColorTests = this.identifyRepeatColorTests(colorResponses);
    
    if (repeatColorTests.length === 0) return 0.7;
    
    let memoryScore = 0;
    let memoryTestCount = 0;
    
    repeatColorTests.forEach(test => {
      // Comparar desempenho na primeira vs. repetições da mesma cor
      const firstAttempt = test.attempts[0];
      const laterAttempts = test.attempts.slice(1);
      
      if (laterAttempts.length > 0) {
        const firstAccuracy = firstAttempt.correct ? 1 : 0;
        const laterAccuracy = laterAttempts.filter(a => a.correct).length / laterAttempts.length;
        
        // Memória boa se desempenho melhora ou se mantém
        const improvement = laterAccuracy - firstAccuracy;
        const memoryIndicator = Math.max(0, 0.5 + improvement);
        
        memoryScore += memoryIndicator;
        memoryTestCount++;
      }
    });
    
    return memoryTestCount > 0 ? memoryScore / memoryTestCount : 0.7;
  }

  calculateAdaptationEfficiency(data) {
    const colorResponses = data.colorData.interactions || [];
    
    if (colorResponses.length < 5) return 0.7;
    
    // Analisar melhoria ao longo da sessão
    const learningCurve = this.calculateLearningCurve(colorResponses);
    
    // Calcular taxa de adaptação
    const initialPerformance = learningCurve.slice(0, 3).reduce((sum, val) => sum + val, 0) / 3;
    const finalPerformance = learningCurve.slice(-3).reduce((sum, val) => sum + val, 0) / 3;
    
    const adaptationRate = finalPerformance - initialPerformance;
    
    // Normalizar entre 0 e 1
    const adaptationScore = 0.5 + (adaptationRate * 0.5);
    
    return Math.max(0, Math.min(1, adaptationScore));
  }

  generateColorConfusionMatrix(data) {
    const colorResponses = data.colorData.interactions || [];
    const confusionMatrix = {};
    
    colorResponses.forEach(response => {
      if (!response.correct && response.targetColor && response.selectedColor) {
        const target = response.targetColor;
        const selected = response.selectedColor;
        
        if (!confusionMatrix[target]) {
          confusionMatrix[target] = {};
        }
        
        confusionMatrix[target][selected] = (confusionMatrix[target][selected] || 0) + 1;
      }
    });
    
    return confusionMatrix;
  }

  identifyColorPreferences(data) {
    const colorResponses = data.colorData.interactions || [];
    const colorCounts = {};
    
    colorResponses.forEach(response => {
      if (response.selectedColor) {
        colorCounts[response.selectedColor] = (colorCounts[response.selectedColor] || 0) + 1;
      }
    });
    
    // Ordenar por frequência
    const sortedColors = Object.entries(colorCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([color, count]) => ({ color, frequency: count }));
    
    return sortedColors;
  }

  // Métodos auxiliares
  calculateHueDistance(color1, color2) {
    // Implementação simplificada - pode ser expandida com conversão RGB->HSV
    const colorMap = {
      'vermelho': 0, 'laranja': 30, 'amarelo': 60, 'verde': 120,
      'azul': 240, 'roxo': 270, 'rosa': 330, 'marrom': 30
    };
    
    const hue1 = colorMap[color1] || 0;
    const hue2 = colorMap[color2] || 0;
    
    const distance = Math.abs(hue1 - hue2);
    return Math.min(distance, 360 - distance);
  }

  calculateHueConsistency(responses) {
    // Mede quão consistente é a percepção de matiz
    const hueErrors = responses
      .filter(r => !r.correct)
      .map(r => this.calculateHueDistance(r.targetColor, r.selectedColor));
    
    if (hueErrors.length === 0) return 1;
    
    const variance = this.calculateVariance(hueErrors);
    return Math.max(0, 1 - (variance / 100));
  }

  extractSaturation(color) {
    // Implementação simplificada - retorna valores estimados
    const saturationMap = {
      'vermelho': 90, 'azul': 85, 'verde': 80, 'amarelo': 95,
      'laranja': 88, 'roxo': 75, 'rosa': 60, 'marrom': 40
    };
    return saturationMap[color] || 70;
  }

  extractBrightness(color) {
    // Implementação simplificada - retorna valores estimados
    const brightnessMap = {
      'amarelo': 90, 'laranja': 75, 'vermelho': 60, 'verde': 55,
      'azul': 45, 'roxo': 40, 'marrom': 30, 'rosa': 80
    };
    return brightnessMap[color] || 50;
  }

  divideResponsesByTime(responses, segments) {
    const sortedResponses = responses.sort((a, b) => 
      new Date(a.timestamp) - new Date(b.timestamp)
    );
    
    const segmentSize = Math.ceil(sortedResponses.length / segments);
    const timeSegments = [];
    
    for (let i = 0; i < segments; i++) {
      const start = i * segmentSize;
      const end = Math.min(start + segmentSize, sortedResponses.length);
      timeSegments.push(sortedResponses.slice(start, end));
    }
    
    return timeSegments;
  }

  assessColorDifficulty(color) {
    // Dificuldade baseada na complexidade perceptual da cor
    const difficultyMap = {
      'vermelho': 0.3, 'azul': 0.4, 'amarelo': 0.2, 'verde': 0.5,
      'laranja': 0.6, 'roxo': 0.8, 'rosa': 0.7, 'marrom': 0.9
    };
    return difficultyMap[color] || 0.5;
  }

  calculateAverageResponseTime(responses) {
    const times = responses
      .filter(r => r.responseTime)
      .map(r => r.responseTime);
    
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 3000;
  }

  identifyRepeatColorTests(responses) {
    const colorTests = {};
    
    responses.forEach((response, index) => {
      const color = response.targetColor;
      if (!colorTests[color]) {
        colorTests[color] = { attempts: [] };
      }
      colorTests[color].attempts.push({ ...response, index });
    });
    
    return Object.values(colorTests).filter(test => test.attempts.length > 1);
  }

  calculateLearningCurve(responses) {
    const windowSize = 3;
    const curve = [];
    
    for (let i = 0; i <= responses.length - windowSize; i++) {
      const window = responses.slice(i, i + windowSize);
      const accuracy = window.filter(r => r.correct).length / window.length;
      curve.push(accuracy);
    }
    
    return curve;
  }

  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  identifyDifficultColors(responses) {
    const colorAccuracy = {};
    
    responses.forEach(response => {
      const color = response.targetColor;
      if (!colorAccuracy[color]) {
        colorAccuracy[color] = { correct: 0, total: 0 };
      }
      colorAccuracy[color].total++;
      if (response.correct) {
        colorAccuracy[color].correct++;
      }
    });
    
    return Object.entries(colorAccuracy)
      .filter(([color, stats]) => stats.total >= 2 && (stats.correct / stats.total) < 0.6)
      .map(([color]) => color);
  }

  calculateDifficultColorAccuracy(responses, difficultColors) {
    const difficultResponses = responses.filter(r => difficultColors.includes(r.targetColor));
    
    if (difficultResponses.length === 0) return 0.5;
    
    const correct = difficultResponses.filter(r => r.correct).length;
    return correct / difficultResponses.length;
  }

  identifySystematicColorErrors(responses) {
    const confusionPairs = {};
    
    responses.forEach(response => {
      if (!response.correct && response.targetColor && response.selectedColor) {
        const pair = `${response.targetColor}->${response.selectedColor}`;
        confusionPairs[pair] = (confusionPairs[pair] || 0) + 1;
      }
    });
    
    // Identificar pares com 3+ confusões sistemáticas
    return Object.entries(confusionPairs)
      .filter(([pair, count]) => count >= 3)
      .map(([pair]) => pair);
  }

  generateColorInsights(results) {
    const insights = [];
    
    if (results.colorDiscrimination < 0.6) {
      insights.push('Dificuldades evidentes na discriminação de cores');
    }
    
    if (results.huePerception < 0.5) {
      insights.push('Limitações na percepção de matizes');
    }
    
    if (results.saturationSensitivity < 0.6) {
      insights.push('Baixa sensibilidade à saturação das cores');
    }
    
    if (results.brightnessDiscrimination < 0.6) {
      insights.push('Dificuldades na discriminação de brilho');
    }
    
    if (results.colorConstancy < 0.5) {
      insights.push('Problemas de constância perceptual de cores');
    }
    
    if (results.colorMemory < 0.6) {
      insights.push('Limitações na memória para cores');
    }
    
    if (Object.keys(results.colorConfusionMatrix || {}).length > 3) {
      insights.push('Padrões significativos de confusão entre cores identificados');
    }
    
    return insights;
  }
}
