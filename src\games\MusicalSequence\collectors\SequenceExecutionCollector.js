/**
 * SequenceExecutionCollector - Coleta dados sobre execução de sequências
 * Analisa precisão, timing, estratégias de execução e controle motor fino
 */
export class SequenceExecutionCollector {
  constructor() {
    this.executionData = [];
    this.timingAnalysis = [];
    this.motorControlData = [];
    this.strategyData = [];
    this.errorPatterns = [];
    this.debugMode = true;
  }

  /**
   * Coleta dados de execução de sequência
   */
  collectExecution(executionData) {
    try {
      if (!executionData || typeof executionData !== 'object') {
        console.warn('SequenceExecutionCollector: Dados de execução inválidos');
        return null;
      }

      const {
        sequence = [],
        playerResponse = [],
        executionTimes = [],
        expectedTimes = [],
        pressureLevels = [],
        coordinationScores = [],
        isCorrect = false,
        totalExecutionTime = 0,
        difficulty = '',
        timestamp = Date.now()
      } = executionData;

      const execution = {
        timestamp,
        sequenceLength: sequence.length,
        executionAccuracy: this.calculateExecutionAccuracy(sequence, playerResponse),
        timingPrecision: this.calculateTimingPrecision(executionTimes, expectedTimes),
        executionFlow: this.analyzeExecutionFlow(executionTimes),
        motorControl: this.assessMotorControl(pressureLevels, coordinationScores),
        executionStrategy: this.identifyExecutionStrategy(executionTimes, sequence),
        errorTypes: this.classifyErrors(sequence, playerResponse),
        consistency: this.measureConsistency(executionTimes),
        efficiency: this.calculateEfficiency(totalExecutionTime, sequence.length),
        isCorrect,
        difficulty,
        hesitations: this.detectHesitations(executionTimes),
        anticipations: this.detectAnticipations(executionTimes, expectedTimes)
      };

      this.executionData.push(execution);

      if (this.debugMode) {
        console.log('⚡ SequenceExecutionCollector - Execução coletada:', execution);
      }

      return execution;
    } catch (error) {
      console.error('Erro no SequenceExecutionCollector.collectExecution:', error);
      return null;
    }
  }

  /**
   * Analisa padrões de timing e ritmo na execução
   */
  analyzeExecutionTiming(timingData) {
    try {
      if (!timingData || typeof timingData !== 'object') {
        console.warn('SequenceExecutionCollector: Dados de timing inválidos');
        return null;
      }

      const {
        interClickIntervals = [],
        expectedIntervals = [],
        totalDuration = 0,
        tempo = 120,
        sequenceLength = 0,
        timestamp = Date.now()
      } = timingData;

      const timingAnalysis = {
        timestamp,
        rhythmicAccuracy: this.calculateRhythmicAccuracy(interClickIntervals, expectedIntervals),
        tempoStability: this.analyzeTempoStability(interClickIntervals),
        timingVariability: this.calculateTimingVariability(interClickIntervals),
        accelerationPattern: this.detectAccelerationPattern(interClickIntervals),
        phrasing: this.analyzePhrasing(interClickIntervals),
        microTiming: this.analyzeMicroTiming(interClickIntervals, expectedIntervals),
        globalTempo: this.calculateGlobalTempo(totalDuration, sequenceLength),
        rubato: this.detectRubato(interClickIntervals, expectedIntervals),
        metricalStress: this.analyzeMetricalStress(interClickIntervals)
      };

      this.timingAnalysis.push(timingAnalysis);

      if (this.debugMode) {
        console.log('🕐 SequenceExecutionCollector - Timing analisado:', timingAnalysis);
      }

      return timingAnalysis;
    } catch (error) {
      console.error('Erro no SequenceExecutionCollector.analyzeExecutionTiming:', error);
      return null;
    }
  }

  /**
   * Coleta dados sobre controle motor e coordenação
   */
  collectMotorControl(motorData) {
    try {
      if (!motorData || typeof motorData !== 'object') {
        console.warn('SequenceExecutionCollector: Dados motores inválidos');
        return null;
      }

      const {
        clickPrecision = [],
        pressureDynamics = [],
        movementSmoothness = 0,
        coordinationIndex = 0,
        fatigue = 0,
        handedness = 'right',
        timestamp = Date.now()
      } = motorData;

      const motorControl = {
        timestamp,
        precisionScore: this.calculatePrecisionScore(clickPrecision),
        dynamicRange: this.analyzeDynamicRange(pressureDynamics),
        motorConsistency: this.assessMotorConsistency(clickPrecision),
        smoothnessIndex: movementSmoothness,
        coordinationLevel: coordinationIndex,
        fatigueLevel: fatigue,
        motorAdaptation: this.analyzeMotorAdaptation(this.motorControlData),
        reactionTime: this.calculateAverageReactionTime(clickPrecision),
        motorMemory: this.assessMotorMemory(clickPrecision),
        handedness
      };

      this.motorControlData.push(motorControl);

      if (this.debugMode) {
        console.log('🤲 SequenceExecutionCollector - Controle motor:', motorControl);
      }

      return motorControl;
    } catch (error) {
      console.error('Erro no SequenceExecutionCollector.collectMotorControl:', error);
      return null;
    }
  }

  /**
   * Identifica estratégias de execução utilizadas
   */
  identifyExecutionStrategies(strategyData) {
    try {
      if (!strategyData || typeof strategyData !== 'object') {
        console.warn('SequenceExecutionCollector: Dados de estratégia inválidos');
        return null;
      }

      const {
        sequence = [],
        playerResponse = [],
        executionTimes = [],
        repeatRequests = 0,
        pauseLocations = [],
        chunkingPatterns = [],
        difficulty = '',
        timestamp = Date.now()
      } = strategyData;

      const strategy = {
        timestamp,
        primaryStrategy: this.identifyPrimaryStrategy(executionTimes, chunkingPatterns),
        chunkingStrategy: this.analyzeChunkingStrategy(chunkingPatterns),
        rehearsalStrategy: this.analyzeRehearsalStrategy(repeatRequests, pauseLocations),
        adaptiveStrategy: this.assessAdaptiveStrategy(this.strategyData),
        metacognition: this.assessMetacognition(pauseLocations, repeatRequests),
        strategicFlexibility: this.measureStrategicFlexibility(this.strategyData),
        planningEffectiveness: this.assessPlanningEffectiveness(sequence, playerResponse),
        errorRecovery: this.analyzeErrorRecovery(sequence, playerResponse, pauseLocations),
        difficultyAdaptation: this.analyzeDifficultyAdaptation(difficulty),
        confidence: this.inferConfidenceLevel(executionTimes, pauseLocations)
      };

      this.strategyData.push(strategy);

      if (this.debugMode) {
        console.log('🎯 SequenceExecutionCollector - Estratégia identificada:', strategy);
      }

      return strategy;
    } catch (error) {
      console.error('Erro no SequenceExecutionCollector.identifyExecutionStrategies:', error);
      return null;
    }
  }

  /**
   * Analisa padrões de erro na execução
   */
  analyzeErrorPatterns(errorData) {
    try {
      if (!errorData || typeof errorData !== 'object') {
        console.warn('SequenceExecutionCollector: Dados de erro inválidos');
        return null;
      }

      const {
        sequence = [],
        playerResponse = [],
        errorLocations = [],
        errorTypes = [],
        recoveryTimes = [],
        repetitionErrors = [],
        timestamp = Date.now()
      } = errorData;

      const errorAnalysis = {
        timestamp,
        errorRate: this.calculateErrorRate(sequence, playerResponse),
        errorDistribution: this.analyzeErrorDistribution(errorLocations),
        errorTypes: this.categorizeErrorTypes(errorTypes),
        persistentErrors: this.identifyPersistentErrors(repetitionErrors),
        errorRecoverySpeed: this.calculateErrorRecoverySpeed(recoveryTimes),
        errorClusters: this.identifyErrorClusters(errorLocations),
        sequentialErrors: this.analyzeSequentialErrors(sequence, playerResponse),
        errorLearning: this.assessErrorLearning(this.errorPatterns),
        errorContext: this.analyzeErrorContext(errorLocations, sequence),
        errorSeverity: this.assessErrorSeverity(errorTypes)
      };

      this.errorPatterns.push(errorAnalysis);

      if (this.debugMode) {
        console.log('❌ SequenceExecutionCollector - Padrões de erro:', errorAnalysis);
      }

      return errorAnalysis;
    } catch (error) {
      console.error('Erro no SequenceExecutionCollector.analyzeErrorPatterns:', error);
      return null;
    }
  }

  // Métodos auxiliares para cálculos de execução
  calculateExecutionAccuracy(sequence, playerResponse) {
    if (!Array.isArray(sequence) || !Array.isArray(playerResponse)) return 0;
    
    let correctCount = 0;
    const minLength = Math.min(sequence.length, playerResponse.length);
    
    for (let i = 0; i < minLength; i++) {
      if (sequence[i] === playerResponse[i]) correctCount++;
    }
    
    const accuracy = correctCount / sequence.length;
    const completeness = minLength / sequence.length;
    
    return { accuracy, completeness, overallScore: accuracy * completeness };
  }

  calculateTimingPrecision(executionTimes, expectedTimes) {
    if (!Array.isArray(executionTimes) || !Array.isArray(expectedTimes)) return 0;
    
    const deviations = [];
    const minLength = Math.min(executionTimes.length, expectedTimes.length);
    
    for (let i = 0; i < minLength; i++) {
      deviations.push(Math.abs(executionTimes[i] - expectedTimes[i]));
    }
    
    const avgDeviation = deviations.reduce((a, b) => a + b, 0) / deviations.length;
    const precision = Math.max(0, 1 - (avgDeviation / 500)); // Normalizado para 500ms
    
    return { precision, avgDeviation, deviations };
  }

  analyzeExecutionFlow(executionTimes) {
    if (!Array.isArray(executionTimes) || executionTimes.length < 3) {
      return { flowScore: 0, interruptions: 0, smoothness: 0 };
    }
    
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i-1]);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((acc, interval) => acc + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    const smoothness = Math.max(0, 1 - (Math.sqrt(variance) / avgInterval));
    
    // Detecta interrupções (pausas longas)
    const threshold = avgInterval * 2;
    const interruptions = intervals.filter(interval => interval > threshold).length;
    
    const flowScore = smoothness * (1 - interruptions / intervals.length);
    
    return { flowScore, interruptions, smoothness, avgInterval };
  }

  assessMotorControl(pressureLevels, coordinationScores) {
    const avgPressure = Array.isArray(pressureLevels) ? 
      pressureLevels.reduce((a, b) => a + b, 0) / pressureLevels.length : 0;
    
    const pressureVariability = Array.isArray(pressureLevels) && pressureLevels.length > 1 ? 
      this.calculateVariability(pressureLevels) : 0;
    
    const avgCoordination = Array.isArray(coordinationScores) ? 
      coordinationScores.reduce((a, b) => a + b, 0) / coordinationScores.length : 0;
    
    return {
      pressureControl: Math.max(0, 1 - pressureVariability),
      averagePressure: avgPressure,
      coordination: avgCoordination,
      motorStability: 1 - pressureVariability,
      fineMotorControl: this.assessFineMotorControl(pressureLevels)
    };
  }

  identifyExecutionStrategy(executionTimes, sequence) {
    if (!Array.isArray(executionTimes) || executionTimes.length < 3) return 'insufficient_data';
    
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i-1]);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = this.calculateVariability(intervals);
    
    // Identifica estratégias baseadas nos padrões temporais
    if (variance < avgInterval * 0.2) return 'steady_rhythm';
    if (this.detectChunking(intervals)) return 'chunking';
    if (this.detectAcceleration(intervals)) return 'acceleration';
    if (this.detectHesitation(intervals, avgInterval)) return 'hesitation';
    
    return 'variable';
  }

  classifyErrors(sequence, playerResponse) {
    if (!Array.isArray(sequence) || !Array.isArray(playerResponse)) return [];
    
    const errors = [];
    const minLength = Math.min(sequence.length, playerResponse.length);
    
    for (let i = 0; i < minLength; i++) {
      if (sequence[i] !== playerResponse[i]) {
        errors.push({
          position: i,
          expected: sequence[i],
          actual: playerResponse[i],
          type: this.categorizeError(sequence[i], playerResponse[i], i, sequence)
        });
      }
    }
    
    // Erros de omissão
    if (playerResponse.length < sequence.length) {
      for (let i = playerResponse.length; i < sequence.length; i++) {
        errors.push({
          position: i,
          expected: sequence[i],
          actual: null,
          type: 'omission'
        });
      }
    }
    
    // Erros de comissão (extras)
    if (playerResponse.length > sequence.length) {
      for (let i = sequence.length; i < playerResponse.length; i++) {
        errors.push({
          position: i,
          expected: null,
          actual: playerResponse[i],
          type: 'commission'
        });
      }
    }
    
    return errors;
  }

  measureConsistency(executionTimes) {
    if (!Array.isArray(executionTimes) || executionTimes.length < 2) return 0;
    
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i-1]);
    }
    
    const variance = this.calculateVariability(intervals);
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    
    return Math.max(0, 1 - (variance / avgInterval));
  }

  calculateEfficiency(totalTime, sequenceLength) {
    if (totalTime === 0 || sequenceLength === 0) return 0;
    
    const expectedTimePerItem = 800; // ms por item (baseado no jogo)
    const expectedTotalTime = sequenceLength * expectedTimePerItem;
    
    return Math.max(0, expectedTotalTime / totalTime);
  }

  detectHesitations(executionTimes) {
    if (!Array.isArray(executionTimes) || executionTimes.length < 3) return [];
    
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i-1]);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const threshold = avgInterval * 1.5;
    
    const hesitations = [];
    intervals.forEach((interval, index) => {
      if (interval > threshold) {
        hesitations.push({
          position: index,
          duration: interval,
          severity: interval / avgInterval
        });
      }
    });
    
    return hesitations;
  }

  detectAnticipations(executionTimes, expectedTimes) {
    if (!Array.isArray(executionTimes) || !Array.isArray(expectedTimes)) return [];
    
    const anticipations = [];
    const minLength = Math.min(executionTimes.length, expectedTimes.length);
    
    for (let i = 0; i < minLength; i++) {
      const anticipation = expectedTimes[i] - executionTimes[i];
      if (anticipation > 100) { // 100ms threshold
        anticipations.push({
          position: i,
          anticipationTime: anticipation,
          severity: anticipation / 200 // Normalizado para 200ms
        });
      }
    }
    
    return anticipations;
  }

  // Métodos auxiliares para análise de timing
  calculateRhythmicAccuracy(intervals, expectedIntervals) {
    if (!Array.isArray(intervals) || !Array.isArray(expectedIntervals)) return 0;
    
    const deviations = [];
    const minLength = Math.min(intervals.length, expectedIntervals.length);
    
    for (let i = 0; i < minLength; i++) {
      deviations.push(Math.abs(intervals[i] - expectedIntervals[i]));
    }
    
    const avgDeviation = deviations.reduce((a, b) => a + b, 0) / deviations.length;
    const tolerance = 100; // ms
    
    return Math.max(0, 1 - (avgDeviation / tolerance));
  }

  analyzeTempoStability(intervals) {
    if (!Array.isArray(intervals) || intervals.length < 3) return 0;
    
    const variance = this.calculateVariability(intervals);
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    
    return Math.max(0, 1 - (variance / avgInterval));
  }

  calculateTimingVariability(intervals) {
    if (!Array.isArray(intervals) || intervals.length === 0) return 0;
    return this.calculateVariability(intervals);
  }

  detectAccelerationPattern(intervals) {
    if (!Array.isArray(intervals) || intervals.length < 3) return 'none';
    
    let accelerating = 0;
    let decelerating = 0;
    
    for (let i = 1; i < intervals.length; i++) {
      if (intervals[i] < intervals[i-1]) accelerating++;
      else if (intervals[i] > intervals[i-1]) decelerating++;
    }
    
    const total = intervals.length - 1;
    if (accelerating / total > 0.6) return 'accelerating';
    if (decelerating / total > 0.6) return 'decelerating';
    
    return 'stable';
  }

  analyzePhrasing(intervals) {
    // Simplificação: detecta agrupamentos baseados em variações temporais
    if (!Array.isArray(intervals) || intervals.length < 4) return { phrases: 0, avgPhraseLength: 0 };
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const threshold = avgInterval * 1.3;
    
    let phrases = 1;
    let currentPhraseLength = 1;
    const phraseLengths = [];
    
    for (let i = 0; i < intervals.length; i++) {
      if (intervals[i] > threshold) {
        phraseLengths.push(currentPhraseLength);
        phrases++;
        currentPhraseLength = 1;
      } else {
        currentPhraseLength++;
      }
    }
    
    phraseLengths.push(currentPhraseLength);
    const avgPhraseLength = phraseLengths.reduce((a, b) => a + b, 0) / phraseLengths.length;
    
    return { phrases, avgPhraseLength, phraseLengths };
  }

  // Métodos auxiliares para análise de estratégias
  identifyPrimaryStrategy(executionTimes, chunkingPatterns) {
    if (!Array.isArray(executionTimes)) return 'unknown';
    
    if (Array.isArray(chunkingPatterns) && chunkingPatterns.length > 0) {
      return 'chunking';
    }
    
    const intervals = [];
    for (let i = 1; i < executionTimes.length; i++) {
      intervals.push(executionTimes[i] - executionTimes[i-1]);
    }
    
    const variance = this.calculateVariability(intervals);
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    
    if (variance < avgInterval * 0.2) return 'steady_execution';
    if (this.detectAcceleration(intervals)) return 'progressive_acceleration';
    
    return 'adaptive';
  }

  analyzeChunkingStrategy(chunkingPatterns) {
    if (!Array.isArray(chunkingPatterns) || chunkingPatterns.length === 0) {
      return { chunkingUsed: false, avgChunkSize: 0, chunkingEfficiency: 0 };
    }
    
    const avgChunkSize = chunkingPatterns.reduce((a, b) => a + b, 0) / chunkingPatterns.length;
    const chunkVariability = this.calculateVariability(chunkingPatterns);
    const efficiency = 1 - chunkVariability;
    
    return {
      chunkingUsed: true,
      avgChunkSize,
      chunkingEfficiency: efficiency,
      chunkConsistency: 1 - chunkVariability
    };
  }

  analyzeRehearsalStrategy(repeatRequests, pauseLocations) {
    return {
      rehearsalFrequency: repeatRequests,
      strategicPauses: Array.isArray(pauseLocations) ? pauseLocations.length : 0,
      rehearsalEfficiency: this.calculateRehearsalEfficiency(repeatRequests),
      pauseStrategy: this.analyzePauseStrategy(pauseLocations)
    };
  }

  // Métodos utilitários
  calculateVariability(values) {
    if (!Array.isArray(values) || values.length === 0) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  detectChunking(intervals) {
    // Detecta padrões de agrupamento baseados em intervalos longos
    if (!Array.isArray(intervals) || intervals.length < 4) return false;
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const longPauses = intervals.filter(interval => interval > avgInterval * 1.5);
    
    return longPauses.length >= 2 && longPauses.length < intervals.length * 0.5;
  }

  detectAcceleration(intervals) {
    if (!Array.isArray(intervals) || intervals.length < 3) return false;
    
    let accelerating = 0;
    for (let i = 1; i < intervals.length; i++) {
      if (intervals[i] < intervals[i-1]) accelerating++;
    }
    
    return accelerating / (intervals.length - 1) > 0.6;
  }

  detectHesitation(intervals, avgInterval) {
    return intervals.some(interval => interval > avgInterval * 2);
  }

  categorizeError(expected, actual, position, sequence) {
    if (actual === null) return 'omission';
    if (expected === null) return 'commission';
    
    // Verifica se é erro de ordem (elemento aparece em posição errada)
    if (sequence.includes(actual)) {
      const correctPosition = sequence.indexOf(actual);
      if (Math.abs(correctPosition - position) === 1) return 'transposition';
      return 'order_error';
    }
    
    return 'substitution';
  }

  assessFineMotorControl(pressureLevels) {
    if (!Array.isArray(pressureLevels) || pressureLevels.length === 0) return 0;
    
    const stability = 1 - this.calculateVariability(pressureLevels);
    const control = Math.min(...pressureLevels) / Math.max(...pressureLevels);
    
    return (stability + control) / 2;
  }

  calculateRehearsalEfficiency(repeatRequests) {
    // Eficiência baseada na frequência de repetições
    if (repeatRequests === 0) return 1; // Nenhuma repetição necessária
    if (repeatRequests <= 2) return 0.8; // Poucas repetições
    if (repeatRequests <= 4) return 0.6; // Repetições moderadas
    return 0.4; // Muitas repetições
  }

  analyzePauseStrategy(pauseLocations) {
    if (!Array.isArray(pauseLocations) || pauseLocations.length === 0) {
      return { strategic: false, pattern: 'none' };
    }
    
    // Verifica se as pausas seguem um padrão estratégico
    const intervals = [];
    for (let i = 1; i < pauseLocations.length; i++) {
      intervals.push(pauseLocations[i] - pauseLocations[i-1]);
    }
    
    const variance = this.calculateVariability(intervals);
    const isStrategic = variance < 2; // Baixa variabilidade indica estratégia
    
    return {
      strategic: isStrategic,
      pattern: isStrategic ? 'regular' : 'irregular',
      pauseFrequency: pauseLocations.length
    };
  }

  // Métodos de análise avançada (implementações simplificadas)
  analyzeMicroTiming(intervals, expectedIntervals) {
    return { microVariations: [], avgMicroDeviation: 0 };
  }

  calculateGlobalTempo(totalDuration, sequenceLength) {
    if (totalDuration === 0 || sequenceLength === 0) return 0;
    return (sequenceLength * 60000) / totalDuration; // BPM aproximado
  }

  detectRubato(intervals, expectedIntervals) {
    return { rubatoPresent: false, expressivity: 0 };
  }

  analyzeMetricalStress(intervals) {
    return { stressPattern: 'none', accentuation: [] };
  }

  assessAdaptiveStrategy(strategyHistory) {
    return strategyHistory.length > 1 ? 0.5 : 0; // Simplificado
  }

  assessMetacognition(pauseLocations, repeatRequests) {
    const metacognitionScore = (Array.isArray(pauseLocations) ? pauseLocations.length : 0) * 0.3 + 
                               repeatRequests * 0.2;
    return Math.min(metacognitionScore, 1);
  }

  measureStrategicFlexibility(strategyHistory) {
    if (!Array.isArray(strategyHistory) || strategyHistory.length < 2) return 0;
    
    const strategies = strategyHistory.map(s => s.primaryStrategy);
    const uniqueStrategies = new Set(strategies).size;
    
    return uniqueStrategies / strategies.length;
  }

  assessPlanningEffectiveness(sequence, playerResponse) {
    const accuracy = this.calculateExecutionAccuracy(sequence, playerResponse);
    return accuracy.overallScore;
  }

  analyzeErrorRecovery(sequence, playerResponse, pauseLocations) {
    const errors = this.classifyErrors(sequence, playerResponse);
    const pausesAfterErrors = Array.isArray(pauseLocations) ? pauseLocations.length : 0;
    
    return {
      errorCount: errors.length,
      recoveryAttempts: pausesAfterErrors,
      recoveryEffectiveness: pausesAfterErrors > 0 ? 0.7 : 0.3
    };
  }

  analyzeDifficultyAdaptation(difficulty) {
    // Simplificado: retorna métricas básicas baseadas na dificuldade
    const difficultyMap = { easy: 0.3, medium: 0.6, hard: 0.9 };
    return difficultyMap[difficulty] || 0.5;
  }

  inferConfidenceLevel(executionTimes, pauseLocations) {
    const hesitations = this.detectHesitations(executionTimes);
    const pauses = Array.isArray(pauseLocations) ? pauseLocations.length : 0;
    
    const confidenceScore = Math.max(0, 1 - (hesitations.length * 0.2 + pauses * 0.1));
    return confidenceScore;
  }

  // Métodos de análise de erro avançados
  calculateErrorRate(sequence, playerResponse) {
    const errors = this.classifyErrors(sequence, playerResponse);
    return errors.length / sequence.length;
  }

  analyzeErrorDistribution(errorLocations) {
    if (!Array.isArray(errorLocations) || errorLocations.length === 0) {
      return { distribution: 'none', clustering: 0 };
    }
    
    // Análise simplificada da distribuição
    const positions = errorLocations.map(e => e.position || e);
    const maxPosition = Math.max(...positions);
    
    const beginning = positions.filter(p => p < maxPosition * 0.33).length;
    const middle = positions.filter(p => p >= maxPosition * 0.33 && p < maxPosition * 0.67).length;
    const end = positions.filter(p => p >= maxPosition * 0.67).length;
    
    return {
      distribution: { beginning, middle, end },
      clustering: this.calculateErrorClustering(positions)
    };
  }

  categorizeErrorTypes(errorTypes) {
    if (!Array.isArray(errorTypes)) return {};
    
    const counts = {};
    errorTypes.forEach(type => {
      counts[type] = (counts[type] || 0) + 1;
    });
    
    return counts;
  }

  identifyPersistentErrors(repetitionErrors) {
    if (!Array.isArray(repetitionErrors)) return [];
    
    const errorCounts = {};
    repetitionErrors.forEach(error => {
      const key = `${error.position}-${error.type}`;
      errorCounts[key] = (errorCounts[key] || 0) + 1;
    });
    
    return Object.entries(errorCounts)
      .filter(([, count]) => count > 1)
      .map(([key, count]) => ({ pattern: key, frequency: count }));
  }

  calculateErrorRecoverySpeed(recoveryTimes) {
    if (!Array.isArray(recoveryTimes) || recoveryTimes.length === 0) return 0;
    
    const avgRecoveryTime = recoveryTimes.reduce((a, b) => a + b, 0) / recoveryTimes.length;
    return Math.max(0, 1 - (avgRecoveryTime / 2000)); // Normalizado para 2s
  }

  identifyErrorClusters(errorLocations) {
    if (!Array.isArray(errorLocations) || errorLocations.length < 2) return [];
    
    const positions = errorLocations.map(e => e.position || e).sort((a, b) => a - b);
    const clusters = [];
    let currentCluster = [positions[0]];
    
    for (let i = 1; i < positions.length; i++) {
      if (positions[i] - positions[i-1] <= 2) { // Erros próximos
        currentCluster.push(positions[i]);
      } else {
        if (currentCluster.length > 1) {
          clusters.push(currentCluster);
        }
        currentCluster = [positions[i]];
      }
    }
    
    if (currentCluster.length > 1) {
      clusters.push(currentCluster);
    }
    
    return clusters;
  }

  analyzeSequentialErrors(sequence, playerResponse) {
    const errors = this.classifyErrors(sequence, playerResponse);
    const sequentialErrors = [];
    
    for (let i = 1; i < errors.length; i++) {
      if (errors[i].position === errors[i-1].position + 1) {
        sequentialErrors.push({
          start: errors[i-1].position,
          end: errors[i].position,
          type: 'sequential'
        });
      }
    }
    
    return sequentialErrors;
  }

  assessErrorLearning(errorHistory) {
    if (!Array.isArray(errorHistory) || errorHistory.length < 2) return 0;
    
    const recentErrors = errorHistory.slice(-3);
    const olderErrors = errorHistory.slice(0, -3);
    
    const recentRate = recentErrors.reduce((sum, e) => sum + e.errorRate, 0) / recentErrors.length;
    const olderRate = olderErrors.length > 0 ? 
      olderErrors.reduce((sum, e) => sum + e.errorRate, 0) / olderErrors.length : recentRate;
    
    return Math.max(0, (olderRate - recentRate) / olderRate); // Melhoria percentual
  }

  analyzeErrorContext(errorLocations, sequence) {
    // Analisa o contexto onde os erros ocorrem
    return {
      contextualFactors: ['sequence_complexity', 'position_effect'],
      contextualAnalysis: 'simplified'
    };
  }

  assessErrorSeverity(errorTypes) {
    if (!Array.isArray(errorTypes)) return 0;
    
    const severityMap = {
      'omission': 0.8,
      'commission': 0.6,
      'substitution': 0.5,
      'transposition': 0.4,
      'order_error': 0.7
    };
    
    const avgSeverity = errorTypes.reduce((sum, type) => sum + (severityMap[type] || 0.5), 0) / errorTypes.length;
    return avgSeverity;
  }

  calculateErrorClustering(positions) {
    if (positions.length < 2) return 0;
    
    let clusterScore = 0;
    for (let i = 1; i < positions.length; i++) {
      if (positions[i] - positions[i-1] <= 2) {
        clusterScore++;
      }
    }
    
    return clusterScore / (positions.length - 1);
  }

  // Métodos auxiliares adicionais
  calculatePrecisionScore(clickPrecision) {
    if (!Array.isArray(clickPrecision) || clickPrecision.length === 0) return 0;
    
    return clickPrecision.reduce((a, b) => a + b, 0) / clickPrecision.length;
  }

  analyzeDynamicRange(pressureDynamics) {
    if (!Array.isArray(pressureDynamics) || pressureDynamics.length === 0) return 0;
    
    const min = Math.min(...pressureDynamics);
    const max = Math.max(...pressureDynamics);
    
    return { range: max - min, min, max, avgPressure: pressureDynamics.reduce((a, b) => a + b, 0) / pressureDynamics.length };
  }

  assessMotorConsistency(clickPrecision) {
    if (!Array.isArray(clickPrecision) || clickPrecision.length === 0) return 0;
    
    return 1 - this.calculateVariability(clickPrecision);
  }

  analyzeMotorAdaptation(motorHistory) {
    if (!Array.isArray(motorHistory) || motorHistory.length < 2) return 0;
    
    const recent = motorHistory.slice(-3);
    const older = motorHistory.slice(0, -3);
    
    const recentPrecision = recent.reduce((sum, m) => sum + m.precisionScore, 0) / recent.length;
    const olderPrecision = older.length > 0 ? 
      older.reduce((sum, m) => sum + m.precisionScore, 0) / older.length : recentPrecision;
    
    return Math.max(0, (recentPrecision - olderPrecision) / olderPrecision);
  }

  calculateAverageReactionTime(clickPrecision) {
    // Simplificado: usa dados de precisão como proxy para tempo de reação
    if (!Array.isArray(clickPrecision) || clickPrecision.length === 0) return 0;
    
    return 500 - (clickPrecision.reduce((a, b) => a + b, 0) / clickPrecision.length) * 200;
  }

  assessMotorMemory(clickPrecision) {
    if (!Array.isArray(clickPrecision) || clickPrecision.length < 2) return 0;
    
    // Verifica se há melhoria na precisão ao longo da sequência
    const firstHalf = clickPrecision.slice(0, Math.floor(clickPrecision.length / 2));
    const secondHalf = clickPrecision.slice(Math.floor(clickPrecision.length / 2));
    
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    
    return Math.max(0, (secondAvg - firstAvg) / firstAvg);
  }

  // Métodos de relatório
  getExecutionReport() {
    return {
      totalExecutions: this.executionData.length,
      avgExecutionAccuracy: this.executionData.reduce((sum, e) => sum + e.executionAccuracy.overallScore, 0) / this.executionData.length || 0,
      avgTimingPrecision: this.executionData.reduce((sum, e) => sum + e.timingPrecision.precision, 0) / this.executionData.length || 0,
      primaryStrategy: this.identifyMostCommonStrategy(),
      motorControlTrend: this.analyzeMotorControlTrend(),
      errorPatternSummary: this.summarizeErrorPatterns()
    };
  }

  identifyMostCommonStrategy() {
    if (this.strategyData.length === 0) return 'unknown';
    
    const strategies = this.strategyData.map(s => s.primaryStrategy);
    const counts = {};
    
    strategies.forEach(strategy => {
      counts[strategy] = (counts[strategy] || 0) + 1;
    });
    
    return Object.entries(counts).sort((a, b) => b[1] - a[1])[0][0];
  }

  analyzeMotorControlTrend() {
    if (this.motorControlData.length < 2) return 'insufficient_data';
    
    const recent = this.motorControlData.slice(-3);
    const older = this.motorControlData.slice(0, 3);
    
    const recentAvg = recent.reduce((sum, m) => sum + m.precisionScore, 0) / recent.length;
    const olderAvg = older.reduce((sum, m) => sum + m.precisionScore, 0) / older.length;
    
    if (recentAvg > olderAvg * 1.1) return 'improving';
    if (recentAvg < olderAvg * 0.9) return 'declining';
    return 'stable';
  }

  summarizeErrorPatterns() {
    if (this.errorPatterns.length === 0) return { totalErrors: 0, commonTypes: [] };
    
    const totalErrors = this.errorPatterns.reduce((sum, e) => sum + e.errorRate, 0);
    const allErrorTypes = this.errorPatterns.flatMap(e => Object.keys(e.errorTypes));
    const errorTypeCounts = {};
    
    allErrorTypes.forEach(type => {
      errorTypeCounts[type] = (errorTypeCounts[type] || 0) + 1;
    });
    
    const commonTypes = Object.entries(errorTypeCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([type]) => type);
    
    return { totalErrors, commonTypes };
  }

  clearData() {
    this.executionData = [];
    this.timingAnalysis = [];
    this.motorControlData = [];
    this.strategyData = [];
    this.errorPatterns = [];
    
    if (this.debugMode) {
      console.log('⚡ SequenceExecutionCollector - Dados limpos');
    }
  }
}
