/**
 * 🔢 CONTAGEM DE NÚMEROS V4 - REESTRUTURADO
 * Portal Betina V3 - Jogo educativo estável seguindo padrões de sucesso
 * Baseado nos padrões dos jogos Musical Sequence e Color Match
 */

import React, { useState, useEffect, useCallback, useContext } from 'react';
import { ContagemNumerosConfig } from './ContagemNumerosConfig.js';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// Hook unificado para integração com backend
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';

// 🧠 COLETORES AVANÇADOS - Análise cognitiva em tempo real
import { NumberCountingCollectorsHub } from './collectors/index.js';

// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';

// Importa estilos modulares
import styles from './ContagemNumeros.module.css';

// 🎯 SISTEMA DE ATIVIDADES SIMPLIFICADO V4 - ESTÁVEL E INFANTIL
const ACTIVITY_TYPES = {
  VISUAL_COUNTING: { 
    id: 'visual_counting', 
    name: 'Contagem Visual', 
    description: 'Conte os objetos que você vê',
    icon: '👁️'
  },
  SIMPLE_ADDITION: { 
    id: 'simple_addition', 
    name: 'Soma Simples', 
    description: 'Some números pequenos',
    icon: '➕'
  },
  NUMBER_RECOGNITION: { 
    id: 'number_recognition', 
    name: 'Reconhecimento', 
    description: 'Encontre o número correto',
    icon: '🔍'
  },
  QUANTITY_COMPARISON: { 
    id: 'quantity_comparison', 
    name: 'Comparação', 
    description: 'Qual tem mais?',
    icon: '⚖️'
  }
};

// 🎯 Configurações simplificadas para crianças
const DIFFICULTY_CONFIGS = {
  easy: {
    name: 'Fácil',
    range: [1, 3],
    maxNumber: 5
  },
  medium: {
    name: 'Médio', 
    range: [1, 5],
    maxNumber: 8
  },
  hard: {
    name: 'Avançado',
    range: [1, 8], 
    maxNumber: 10
  }
};

// 🎮 COMPONENTE PRINCIPAL
const ContagemNumerosGame = ({ onBack }) => {
  const { user, sessionId } = useContext(SystemContext);
  const { ttsActive, speak } = useAccessibilityContext();

  // 🎯 ESTADO SIMPLIFICADO E ESTÁVEL - Baseado nos padrões de sucesso
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'easy',
    roundStartTime: null,
    
    // Sistema de atividades simplificado
    currentActivity: ACTIVITY_TYPES.VISUAL_COUNTING.id,
    activityCycle: [
      ACTIVITY_TYPES.VISUAL_COUNTING.id,
      ACTIVITY_TYPES.SIMPLE_ADDITION.id,
      ACTIVITY_TYPES.NUMBER_RECOGNITION.id,
      ACTIVITY_TYPES.QUANTITY_COMPARISON.id
    ],
    activityIndex: 0,
    
    // Dados específicos de atividades - PERSISTENTES
    activityData: {
      visual_counting: {
        objects: [],
        correctCount: 0,
        options: [],
        instruction: ''
      },
      simple_addition: {
        number1: 0,
        number2: 0,
        correctAnswer: 0,
        options: [],
        instruction: ''
      },
      number_recognition: {
        targetNumber: 0,
        options: [],
        instruction: ''
      },
      quantity_comparison: {
        group1: [],
        group2: [],
        correctAnswer: '',
        instruction: ''
      }
    }
  });

  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);
  const [feedback, setFeedback] = useState(null);

  // 🧠 COLETORES
  const [collectorsHub] = useState(() => new NumberCountingCollectorsHub());

  // 🎯 HOOKS INTEGRADOS
  const {
    startUnifiedSession,
    recordInteraction,
    metrics,
    isSessionActive
  } = useUnifiedGameLogic();

  const {
    initMultisensory,
    multisensoryIntegration
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'contagem_numeros',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsActive,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  // 🎯 FUNÇÃO TTS SIMPLES - Padrão dos jogos de sucesso
  const handleTTSClick = useCallback(() => {
    const currentActivityData = gameState.activityData[gameState.currentActivity];
    const instruction = currentActivityData?.instruction || 'Bem-vindo ao jogo de contagem de números!';
    speak(instruction);
  }, [gameState, speak]);

  // 🎯 GERAR DADOS DA ATIVIDADE - ESTÁVEL E PERSISTENTE
  const generateActivityData = useCallback((activityId, difficulty) => {
    const config = DIFFICULTY_CONFIGS[difficulty];
    
    switch (activityId) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        return generateVisualCountingData(config);
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        return generateSimpleAdditionData(config);
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        return generateNumberRecognitionData(config);
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        return generateQuantityComparisonData(config);
      default:
        return generateVisualCountingData(config);
    }
  }, []);

  // 🎯 GERADOR DE CONTAGEM VISUAL - SIMPLES E ESTÁVEL
  const generateVisualCountingData = useCallback((config) => {
    const correctCount = Math.floor(Math.random() * (config.range[1] - config.range[0] + 1)) + config.range[0];
    const categories = ContagemNumerosConfig.categories;
    const randomCategory = categories[Math.floor(Math.random() * categories.length)];
    const randomObject = randomCategory.objects[Math.floor(Math.random() * randomCategory.objects.length)];
    
    // Criar objetos para exibição - ARRAY SIMPLES
    const objects = Array.from({ length: correctCount }, (_, index) => ({
      id: index,
      emoji: randomObject.emoji,
      name: randomObject.name
    }));
    
    // Criar opções de resposta
    const options = new Set([correctCount]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;
      if (wrongOption !== correctCount && wrongOption > 0) {
        options.add(wrongOption);
      }
    }
    
    return {
      objects,
      correctCount,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Conte quantos ${randomObject.name.toLowerCase()}s você vê na tela`
    };
  }, []);

  // 🎯 GERADOR DE SOMA SIMPLES
  const generateSimpleAdditionData = useCallback((config) => {
    const number1 = Math.floor(Math.random() * config.range[1]) + 1;
    const number2 = Math.floor(Math.random() * (config.range[1] - number1)) + 1;
    const correctAnswer = number1 + number2;
    
    // Criar opções de resposta
    const options = new Set([correctAnswer]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;
      if (wrongOption !== correctAnswer && wrongOption > 0) {
        options.add(wrongOption);
      }
    }
    
    return {
      number1,
      number2,
      correctAnswer,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Quanto é ${number1} + ${number2}?`
    };
  }, []);

  // 🎯 GERADOR DE RECONHECIMENTO DE NÚMERO
  const generateNumberRecognitionData = useCallback((config) => {
    const targetNumber = Math.floor(Math.random() * config.maxNumber) + 1;
    
    // Criar opções de resposta
    const options = new Set([targetNumber]);
    while (options.size < 4) {
      const wrongOption = Math.floor(Math.random() * config.maxNumber) + 1;
      if (wrongOption !== targetNumber) {
        options.add(wrongOption);
      }
    }
    
    return {
      targetNumber,
      options: Array.from(options).sort(() => Math.random() - 0.5),
      instruction: `Encontre o número ${targetNumber}`
    };
  }, []);

  // 🎯 GERADOR DE COMPARAÇÃO DE QUANTIDADE
  const generateQuantityComparisonData = useCallback((config) => {
    const count1 = Math.floor(Math.random() * config.range[1]) + 1;
    const count2 = Math.floor(Math.random() * config.range[1]) + 1;
    
    // Garantir que sejam diferentes
    const finalCount2 = count1 === count2 ? (count2 === 1 ? 2 : count2 - 1) : count2;
    
    const categories = ContagemNumerosConfig.categories;
    const category1 = categories[Math.floor(Math.random() * categories.length)];
    const category2 = categories[Math.floor(Math.random() * categories.length)];
    
    const group1 = Array.from({ length: count1 }, (_, i) => ({
      id: i,
      emoji: category1.objects[0].emoji,
      name: category1.objects[0].name
    }));
    
    const group2 = Array.from({ length: finalCount2 }, (_, i) => ({
      id: i,
      emoji: category2.objects[0].emoji,
      name: category2.objects[0].name
    }));
    
    const correctAnswer = count1 > finalCount2 ? 'left' : 'right';
    
    return {
      group1,
      group2,
      correctAnswer,
      instruction: 'Qual grupo tem mais objetos?'
    };
  }, []);

  // 🎯 INICIAR JOGO - SIMPLES E DIRETO
  const startGame = useCallback(async (selectedDifficulty) => {
    // Gerar dados iniciais da primeira atividade
    const initialData = generateActivityData(ACTIVITY_TYPES.VISUAL_COUNTING.id, selectedDifficulty);

    setGameState(prev => ({
      ...prev,
      status: 'playing',
      difficulty: selectedDifficulty,
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [ACTIVITY_TYPES.VISUAL_COUNTING.id]: initialData
      }
    }));

    setShowStartScreen(false);
    setGameStarted(true);

    // Inicializar sistemas
    if (startUnifiedSession) {
      startUnifiedSession(selectedDifficulty);
    }

    try {
      await initMultisensory(sessionId || `session_${Date.now()}`, {
        difficulty: selectedDifficulty,
        gameMode: 'number_counting_v4',
        userId: user?.id || 'anonymous'
      });
    } catch (error) {
      console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
    }

    // TTS de boas-vindas
    setTimeout(() => {
      speak(`Bem-vindo ao jogo de contagem! ${initialData.instruction}`, { rate: 0.8 });
    }, 1000);
  }, [generateActivityData, startUnifiedSession, initMultisensory, sessionId, user, speak]);

  // 🎯 PROCESSAR RESPOSTA - LÓGICA SIMPLES
  const handleAnswer = useCallback((answer) => {
    const currentActivityData = gameState.activityData[gameState.currentActivity];
    let isCorrect = false;
    let correctAnswer = null;

    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        correctAnswer = currentActivityData.correctCount;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        correctAnswer = currentActivityData.correctAnswer;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        correctAnswer = currentActivityData.targetNumber;
        isCorrect = answer === correctAnswer;
        break;
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        correctAnswer = currentActivityData.correctAnswer;
        isCorrect = answer === correctAnswer;
        break;
      default:
        isCorrect = false;
    }

    // Atualizar pontuação
    setGameState(prev => ({
      ...prev,
      score: isCorrect ? prev.score + 10 : prev.score,
      round: prev.round + 1
    }));

    // Feedback
    setFeedback({
      isCorrect,
      message: isCorrect ? 'Muito bem! 🎉' : `Não foi dessa vez. A resposta era ${correctAnswer}`,
      correctAnswer
    });

    // TTS feedback
    if (isCorrect) {
      speak('Muito bem! Você acertou!', { pitch: 1.3 });
    } else {
      speak(`Não foi dessa vez. A resposta correta era ${correctAnswer}`, { rate: 0.8 });
    }

    // Próxima rodada
    setTimeout(() => {
      setFeedback(null);
      generateNewRound();
    }, 2500);
  }, [gameState, speak]);

  // 🎯 GERAR NOVA RODADA - ESTÁVEL
  const generateNewRound = useCallback(() => {
    const nextActivityIndex = (gameState.activityIndex + 1) % gameState.activityCycle.length;
    const nextActivity = gameState.activityCycle[nextActivityIndex];
    const newData = generateActivityData(nextActivity, gameState.difficulty);

    setGameState(prev => ({
      ...prev,
      currentActivity: nextActivity,
      activityIndex: nextActivityIndex,
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [nextActivity]: newData
      }
    }));

    // TTS da nova instrução
    setTimeout(() => {
      speak(newData.instruction, { rate: 0.8 });
    }, 500);
  }, [gameState, generateActivityData, speak]);

  // 🎯 TROCAR ATIVIDADE - Padrão PadroesVisuais
  const switchActivity = useCallback((activityId) => {
    if (activityId === gameState.currentActivity) return;

    const newData = generateActivityData(activityId, gameState.difficulty);

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      roundStartTime: Date.now(),
      activityData: {
        ...prev.activityData,
        [activityId]: newData
      }
    }));

    // TTS da nova atividade
    setTimeout(() => {
      speak(newData.instruction, { rate: 0.8 });
    }, 500);
  }, [gameState, generateActivityData, speak]);

  // 🎯 RENDERIZAR ATIVIDADE VISUAL COUNTING - ESTÁVEL
  const renderVisualCounting = () => {
    const data = gameState.activityData.visual_counting;

    // Se não há dados, gerar dados iniciais
    if (!data || !data.objects) {
      const initialData = generateActivityData(ACTIVITY_TYPES.VISUAL_COUNTING.id, gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.VISUAL_COUNTING.id]: initialData
        }
      }));
      return <div>Gerando atividade...</div>;
    }

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
        </div>

        {/* Object Display ESTÁVEL - sem position absolute */}
        <div className={styles.objectsDisplay}>
          {data.objects.map((obj, index) => (
            <div
              key={obj.id}
              className={styles.countingObject}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {obj.emoji}
            </div>
          ))}
        </div>

        {/* Opções de resposta */}
        <div className={styles.answerOptions}>
          {data.options.map((option, index) => (
            <button
              key={index}
              className={styles.answerButton}
              onClick={() => handleAnswer(option)}
            >
              <span className={styles.optionNumber}>{option}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  // 🎯 RENDERIZAR SOMA SIMPLES
  const renderSimpleAddition = () => {
    const data = gameState.activityData.simple_addition;

    // Se não há dados, gerar dados iniciais
    if (!data || !data.options) {
      const initialData = generateActivityData(ACTIVITY_TYPES.SIMPLE_ADDITION.id, gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.SIMPLE_ADDITION.id]: initialData
        }
      }));
      return <div>Gerando atividade...</div>;
    }

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
        </div>

        {/* Exibição da soma */}
        <div className={styles.objectsDisplay}>
          <div className={styles.additionDisplay}>
            <span className={styles.additionNumber}>{data.number1}</span>
            <span className={styles.additionOperator}>+</span>
            <span className={styles.additionNumber}>{data.number2}</span>
            <span className={styles.additionOperator}>=</span>
            <span className={styles.additionResult}>?</span>
          </div>
        </div>

        {/* Opções de resposta */}
        <div className={styles.answerOptions}>
          {data.options.map((option, index) => (
            <button
              key={index}
              className={styles.answerButton}
              onClick={() => handleAnswer(option)}
            >
              <span className={styles.optionNumber}>{option}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  // 🎯 RENDERIZAR RECONHECIMENTO DE NÚMERO
  const renderNumberRecognition = () => {
    const data = gameState.activityData.number_recognition;

    // Se não há dados, gerar dados iniciais
    if (!data || !data.options) {
      const initialData = generateActivityData(ACTIVITY_TYPES.NUMBER_RECOGNITION.id, gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.NUMBER_RECOGNITION.id]: initialData
        }
      }));
      return <div>Gerando atividade...</div>;
    }

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
        </div>

        {/* Exibição do número alvo */}
        <div className={styles.objectsDisplay}>
          <div className={styles.targetNumber}>
            {data.targetNumber}
          </div>
        </div>

        {/* Opções de resposta */}
        <div className={styles.answerOptions}>
          {data.options.map((option, index) => (
            <button
              key={index}
              className={styles.answerButton}
              onClick={() => handleAnswer(option)}
            >
              <span className={styles.optionNumber}>{option}</span>
            </button>
          ))}
        </div>
      </div>
    );
  };

  // 🎯 RENDERIZAR COMPARAÇÃO DE QUANTIDADE
  const renderQuantityComparison = () => {
    const data = gameState.activityData.quantity_comparison;

    // Se não há dados, gerar dados iniciais
    if (!data || !data.group1 || !data.group2) {
      const initialData = generateActivityData(ACTIVITY_TYPES.QUANTITY_COMPARISON.id, gameState.difficulty);
      setGameState(prev => ({
        ...prev,
        activityData: {
          ...prev.activityData,
          [ACTIVITY_TYPES.QUANTITY_COMPARISON.id]: initialData
        }
      }));
      return <div>Gerando atividade...</div>;
    }

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>{data.instruction}</h2>
        </div>

        {/* Exibição dos grupos */}
        <div className={styles.objectsDisplay}>
          <div className={styles.comparisonGroups}>
            <button
              className={styles.comparisonGroup}
              onClick={() => handleAnswer('left')}
            >
              <div className={styles.groupLabel}>Grupo A</div>
              <div className={styles.groupObjects}>
                {data.group1.map((obj, index) => (
                  <span key={obj.id} className={styles.groupObject}>
                    {obj.emoji}
                  </span>
                ))}
              </div>
            </button>

            <div className={styles.vsIndicator}>VS</div>

            <button
              className={styles.comparisonGroup}
              onClick={() => handleAnswer('right')}
            >
              <div className={styles.groupLabel}>Grupo B</div>
              <div className={styles.groupObjects}>
                {data.group2.map((obj, index) => (
                  <span key={obj.id} className={styles.groupObject}>
                    {obj.emoji}
                  </span>
                ))}
              </div>
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 🎯 RENDERIZAR INTERFACE PRINCIPAL - Padrão dos jogos de sucesso
  const renderCurrentActivity = () => {
    switch (gameState.currentActivity) {
      case ACTIVITY_TYPES.VISUAL_COUNTING.id:
        return renderVisualCounting();
      case ACTIVITY_TYPES.SIMPLE_ADDITION.id:
        return renderSimpleAddition();
      case ACTIVITY_TYPES.NUMBER_RECOGNITION.id:
        return renderNumberRecognition();
      case ACTIVITY_TYPES.QUANTITY_COMPARISON.id:
        return renderQuantityComparison();
      default:
        return renderVisualCounting();
    }
  };

  // 🎯 TELA DE INÍCIO
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Contagem de Números"
        gameDescription="Aprenda matemática de forma divertida e interativa"
        gameIcon="🔢"
        onStart={startGame}
        onBack={onBack}
        difficulties={[
          { id: 'easy', name: 'Fácil', description: 'Números de 1 a 5', icon: '🟢' },
          { id: 'medium', name: 'Médio', description: 'Números de 1 a 8', icon: '🟡' },
          { id: 'hard', name: 'Avançado', description: 'Números de 1 a 10', icon: '🔴' }
        ]}
      />
    );
  }

  // 🎯 INTERFACE PRINCIPAL - REPLICANDO LAYOUT DO LETTERRECOGNITION
  return (
    <div className={styles.contagemNumerosGame}>
      <div className={styles.gameContent}>
        {/* Header do jogo - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🔢 Contagem de Números V4
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              {Object.values(ACTIVITY_TYPES).find(type => type.id === gameState.currentActivity)?.name || 'Atividade'}
            </div>
          </h1>
          <button
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
            onClick={handleTTSClick}
            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* Header com estatísticas - PADRÃO LETTERRECOGNITION */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{DIFFICULTY_CONFIGS[gameState.difficulty]?.name || gameState.difficulty}</div>
            <div className={styles.statLabel}>Dificuldade</div>
          </div>
        </div>

        {/* Menu de atividades - PADRÃO LETTERRECOGNITION */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => switchActivity(activity.id)}
              title={activity.description}
            >
              <span>{activity.icon}</span>
              <span>{activity.name}</span>
            </button>
          ))}
        </div>

        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameArea}>
          {renderCurrentActivity()}
        </div>

        {/* Controles do jogo - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameControls}>
          <button className={styles.controlButton} onClick={() => speak('Contagem de números. Aprenda matemática de forma divertida contando objetos e resolvendo operações.')}>
            🔊 Explicar
          </button>
          <button className={styles.controlButton} onClick={() => speak('Repita as instruções da atividade atual.')}>
            🔄 Repetir
          </button>
          <button className={styles.controlButton} onClick={() => speak('Teste de acessibilidade ativado.')}>
            🧪 Teste TTS
          </button>
          <button className={styles.controlButton} onClick={() => setShowStartScreen(true)}>
            🔄 Reiniciar
          </button>
          <button className={styles.controlButton} onClick={onBack}>
            ⬅️ Voltar
          </button>
        </div>
      </div>

      {/* Feedback - se houver */}
      {feedback && (
        <div className={`${styles.feedbackOverlay} ${feedback.isCorrect ? styles.success : styles.error}`}>
          <div className={styles.feedbackContent}>
            <div className={styles.feedbackMessage}>{feedback.message}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContagemNumerosGame;
