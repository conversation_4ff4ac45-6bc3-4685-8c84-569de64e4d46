import{j as e}from"./index-BIwBZl_j.js";import{r as s}from"./react-router-BtSsPy6x.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";const r="_container_2uq7c_15",o="_header_2uq7c_37",i="_mainTitle_2uq7c_49",a="_subtitle_2uq7c_65",l="_section_2uq7c_81",n="_sectionTitle_2uq7c_93",t="_sectionContent_2uq7c_123",m="_activeProfileCard_2uq7c_157",c="_activeProfileAvatar_2uq7c_181",u="_activeProfileInfo_2uq7c_205",p="_activeProfileName_2uq7c_213",f="_activeProfileStats_2uq7c_227",N="_statItem_2uq7c_239",d="_statIcon_2uq7c_255",b="_profilesGrid_2uq7c_265",P="_profileCard_2uq7c_279",j="_active_2uq7c_157",v="_profileAvatar_2uq7c_325",h="_profileInfo_2uq7c_353",x="_profileName_2uq7c_363",U="_profileAge_2uq7c_377",g="_profileStats_2uq7c_377",_="_profileDeleteBtn_2uq7c_389",C="_addProfileCard_2uq7c_443",D="_addProfileIcon_2uq7c_475",E="_benefitsList_2uq7c_489",V="_benefitItem_2uq7c_501",q="_benefitEmoji_2uq7c_525",S="_benefitText_2uq7c_535",y="_createProfileOverlay_2uq7c_551",I="_createProfileForm_2uq7c_581",$="_formHeader_2uq7c_607",T="_formTitle_2uq7c_617",A="_formSubtitle_2uq7c_631",k="_formGroup_2uq7c_643",O="_formLabel_2uq7c_651",w="_input_2uq7c_665",G="_avatarSelector_2uq7c_709",F="_avatarOption_2uq7c_723",z="_selected_2uq7c_761",J="_formActions_2uq7c_771",L="_btn_2uq7c_785",M="_btnPrimary_2uq7c_807",B="_btnSecondary_2uq7c_827";function H(){const[H,K]=s.useState([]),[Q,R]=s.useState(null),[W,X]=s.useState(!1),[Y,Z]=s.useState({name:"",age:"",avatar:"👶",preferences:{theme:"default",difficulty:"easy",soundEnabled:!0,animationsEnabled:!0}}),[ee,se]=s.useState({}),[re,oe]=s.useState(!1),ie=async e=>{if(e)try{oe(!0);const r=await fetch(`/api/metrics/game-sessions?userId=${e}`);if(r.ok){const o=await r.json(),i={gamesPlayed:o.sessions?.length||0,totalTime:o.sessions?.reduce((e,s)=>e+(s.duration||0),0)||0,lastPlayed:o.sessions?.length>0?Math.max(...o.sessions.map(e=>new Date(e.timestamp).getTime())):null,favoriteGames:o.favoriteGames||[],achievements:o.achievements||[],avgPerformance:o.avgPerformance||0};se(s=>({...s,[e]:i}));const a=localStorage.getItem("userData");if(a)try{const s=JSON.parse(a);(await fetch("/api/dashboard/link-profile",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("authToken")}`},body:JSON.stringify({profileId:e,dashboardUserId:s.id,profileData:{gamesPlayed:i.gamesPlayed,totalTime:i.totalTime}})})).ok}catch(s){}}}catch(r){}finally{oe(!1)}};s.useEffect(()=>{(()=>{const e=JSON.parse(localStorage.getItem("betina_profiles")||"[]");K(e);const s=localStorage.getItem("betina_active_profile");if(s&&e.length>0){const r=e.find(e=>e.id===s);R(r)}e.forEach(e=>{ie(e.id)})})()},[]),s.useEffect(()=>{Q&&ie(Q.id)},[Q]);const ae=e=>{K(e),localStorage.setItem("betina_profiles",JSON.stringify(e))};return e.jsxDEV("div",{className:r,children:[e.jsxDEV("div",{className:o,children:[e.jsxDEV("h1",{className:i,children:"� Gerenciar Perfis"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:193,columnNumber:9},this),e.jsxDEV("p",{className:a,children:"Crie e gerencie perfis personalizados para toda a família"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:194,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:192,columnNumber:7},this),Q&&e.jsxDEV("section",{className:l,children:[e.jsxDEV("h2",{className:n,children:[e.jsxDEV("span",{children:"⭐"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:203,columnNumber:13},this),"Perfil Ativo"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:202,columnNumber:11},this),e.jsxDEV("div",{className:m,children:[e.jsxDEV("div",{className:c,children:Q.avatar},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:207,columnNumber:13},this),e.jsxDEV("div",{className:u,children:[e.jsxDEV("h3",{className:p,children:Q.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:209,columnNumber:15},this),e.jsxDEV("div",{className:f,children:[e.jsxDEV("div",{className:N,children:[e.jsxDEV("span",{className:d,children:"🎂"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:212,columnNumber:19},this),e.jsxDEV("span",{children:Q.age?`${Q.age} anos`:"Idade não informada"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:213,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:211,columnNumber:17},this),e.jsxDEV("div",{className:N,children:[e.jsxDEV("span",{className:d,children:"🎮"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:216,columnNumber:19},this),e.jsxDEV("span",{children:re?"Carregando...":`${ee[Q.id]?.gamesPlayed||0} jogos jogados`},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:217,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:215,columnNumber:17},this),e.jsxDEV("div",{className:N,children:[e.jsxDEV("span",{className:d,children:"⏰"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:223,columnNumber:19},this),e.jsxDEV("span",{children:ee[Q.id]?.totalTime?`${Math.round(ee[Q.id].totalTime/1e3/60)} min jogados`:`Último acesso: ${new Date(Q.lastUsed).toLocaleDateString()}`},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:224,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:222,columnNumber:17},this),ee[Q.id]?.favoriteGames?.length>0&&e.jsxDEV("div",{className:N,children:[e.jsxDEV("span",{className:d,children:"⭐"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:232,columnNumber:21},this),e.jsxDEV("span",{children:["Jogo favorito: ",ee[Q.id].favoriteGames[0]]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:233,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:231,columnNumber:19},this),ee[Q.id]?.avgPerformance>0&&e.jsxDEV("div",{className:N,children:[e.jsxDEV("span",{className:d,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:238,columnNumber:21},this),e.jsxDEV("span",{children:["Performance média: ",Math.round(ee[Q.id].avgPerformance),"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:239,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:237,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:210,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:208,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:206,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:201,columnNumber:9},this),e.jsxDEV("section",{className:l,children:e.jsxDEV("div",{className:b,children:[H.map(s=>e.jsxDEV("div",{className:`${P} ${Q?.id===s.id?j:""}`,onClick:()=>(e=>{R(e),localStorage.setItem("betina_active_profile",e.id);const s=H.map(s=>s.id===e.id?{...s,lastUsed:(new Date).toISOString()}:s);ae(s),ie(e.id)})(s),children:[e.jsxDEV("div",{className:v,children:s.avatar},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:257,columnNumber:15},this),e.jsxDEV("div",{className:h,children:[e.jsxDEV("h4",{className:x,children:s.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:259,columnNumber:17},this),e.jsxDEV("p",{className:U,children:s.age?`${s.age} anos`:"Idade não informada"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:260,columnNumber:17},this),e.jsxDEV("p",{className:g,children:[s.gamesPlayed||0," jogos jogados"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:263,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:258,columnNumber:15},this),e.jsxDEV("button",{className:_,onClick:e=>{e.stopPropagation(),(e=>{if(confirm("Tem certeza que deseja deletar este perfil?")){const s=H.filter(s=>s.id!==e);ae(s),Q?.id===e&&(R(null),localStorage.removeItem("betina_active_profile"))}})(s.id)},title:"Deletar perfil",children:"🗑️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:267,columnNumber:15},this)]},s.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:252,columnNumber:13},this)),e.jsxDEV("div",{className:`${P} ${C}`,onClick:()=>X(!0),children:[e.jsxDEV("div",{className:D,children:"➕"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:285,columnNumber:13},this),e.jsxDEV("p",{children:"Adicionar Perfil"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:286,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:281,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:250,columnNumber:9},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:249,columnNumber:7},this),e.jsxDEV("section",{className:l,children:[e.jsxDEV("h2",{className:n,children:[e.jsxDEV("span",{children:"💡"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:294,columnNumber:11},this),"Como Funciona?"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:293,columnNumber:9},this),e.jsxDEV("div",{className:t,children:[e.jsxDEV("p",{children:["Os ",e.jsxDEV("strong",{children:"perfis de usuário"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:299,columnNumber:16},this)," permitem que cada criança da família tenha sua própria experiência personalizada. As métricas e progressos são automaticamente vinculados ao responsável que fizer login no dashboard."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:298,columnNumber:11},this),e.jsxDEV("ul",{className:E,children:[e.jsxDEV("li",{className:V,children:[e.jsxDEV("span",{className:q,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:306,columnNumber:15},this),e.jsxDEV("span",{className:S,children:[e.jsxDEV("strong",{children:"Progresso Individual:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:308,columnNumber:17},this)," Cada criança mantém seu próprio histórico de jogos e conquistas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:307,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:305,columnNumber:13},this),e.jsxDEV("li",{className:V,children:[e.jsxDEV("span",{className:q,children:"👨‍👩‍👧‍👦"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:312,columnNumber:15},this),e.jsxDEV("span",{className:S,children:[e.jsxDEV("strong",{children:"Fácil para Crianças:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:314,columnNumber:17},this)," Interface simples, sem necessidade de login ou senhas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:313,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:311,columnNumber:13},this),e.jsxDEV("li",{className:V,children:[e.jsxDEV("span",{className:q,children:"�"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:318,columnNumber:15},this),e.jsxDEV("span",{className:S,children:[e.jsxDEV("strong",{children:"Métricas no Dashboard:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:320,columnNumber:17},this)," Pais/responsáveis acessam relatórios detalhados via dashboard"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:319,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:317,columnNumber:13},this),e.jsxDEV("li",{className:V,children:[e.jsxDEV("span",{className:q,children:"�"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:324,columnNumber:15},this),e.jsxDEV("span",{className:S,children:[e.jsxDEV("strong",{children:"Dados Seguros:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:326,columnNumber:17},this)," Todas as informações ficam salvas localmente no seu dispositivo"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:325,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:323,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:304,columnNumber:11},this),"        "]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:297,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:292,columnNumber:7},this),W&&e.jsxDEV("div",{className:y,children:e.jsxDEV("div",{className:I,children:[e.jsxDEV("div",{className:$,children:[e.jsxDEV("h3",{className:T,children:"➕ Criar Novo Perfil"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:337,columnNumber:15},this),e.jsxDEV("p",{className:A,children:"Adicione um novo membro da família"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:338,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:336,columnNumber:13},this),e.jsxDEV("div",{className:k,children:[e.jsxDEV("label",{htmlFor:"profile-name",className:O,children:"Nome:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:342,columnNumber:15},this),e.jsxDEV("input",{type:"text",id:"profile-name",name:"profile-name",value:Y.name,onChange:e=>Z({...Y,name:e.target.value}),placeholder:"Digite o nome...",maxLength:20,className:w,autoComplete:"given-name"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:343,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:341,columnNumber:13},this),e.jsxDEV("div",{className:k,children:[e.jsxDEV("label",{htmlFor:"profile-age",className:O,children:"Idade (opcional):"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:357,columnNumber:15},this),e.jsxDEV("input",{type:"number",id:"profile-age",name:"profile-age",value:Y.age,onChange:e=>Z({...Y,age:e.target.value}),min:"1",max:"100",placeholder:"Ex: 5",className:w,autoComplete:"age"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:358,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:356,columnNumber:13},this),e.jsxDEV("div",{className:k,children:[e.jsxDEV("label",{htmlFor:"avatar-selector",className:O,children:"Escolha um Avatar:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:373,columnNumber:15},this),e.jsxDEV("div",{className:G,id:"avatar-selector",role:"radiogroup","aria-labelledby":"avatar-selector",children:["👶","👧","👦","🧒","👨","👩","🐱","🐶","🦋","🌟"].map((s,r)=>e.jsxDEV("button",{type:"button",role:"radio","aria-checked":Y.avatar===s,"aria-label":`Avatar ${r+1}: ${s}`,className:`${F} ${Y.avatar===s?z:""}`,onClick:()=>Z({...Y,avatar:s}),children:s},s,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:376,columnNumber:19},this))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:374,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:372,columnNumber:13},this),e.jsxDEV("div",{className:J,children:[e.jsxDEV("button",{className:`${L} ${M}`,onClick:()=>{if(!Y.name.trim())return void alert("Por favor, digite um nome para o perfil");const e={id:Date.now().toString(),...Y,createdAt:(new Date).toISOString(),lastUsed:(new Date).toISOString(),gamesPlayed:0,totalTime:0},s=[...H,e];ae(s),Z({name:"",age:"",avatar:"👶",preferences:{theme:"default",difficulty:"easy",soundEnabled:!0,animationsEnabled:!0}}),X(!1)},children:"✅ Criar Perfil"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:392,columnNumber:15},this),e.jsxDEV("button",{className:`${L} ${B}`,onClick:()=>X(!1),children:"❌ Cancelar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:398,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:391,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:335,columnNumber:11},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:334,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/pages/UserProfiles/UserProfiles.jsx",lineNumber:190,columnNumber:5},this)}export{H as default};
