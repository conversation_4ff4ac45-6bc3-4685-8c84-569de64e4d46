/**
 * 🎯 ATTENTIONAL SELECTIVITY ANALYZER
 * Analisador especializado em seletividade atencional e foco
 * Portal Betina V3 - FASE CRÍTICA
 */

export class AttentionalSelectivityAnalyzer {
  constructor() {
    this.analyzerType = 'AttentionalSelectivity';
    this.version = '3.0.0';
    this.capabilities = {
      focusAssessment: true,
      distractorAnalysis: true,
      attentionalShifting: true,
      sustainedAttention: true,
      selectiveAttention: true
    };
  }

  /**
   * Analisa dados de seletividade atencional
   * @param {Object} attentionalData - Dados do AttentionalSelectivityCollector
   * @param {Object} gameContext - Contexto do jogo
   * @returns {Promise<Object>} Análise de atenção avançada
   */
  async analyze(attentionalData, gameContext = {}) {
    try {
      console.log('🎯 AttentionalSelectivityAnalyzer: Iniciando análise atencional...');

      if (!this.validateAttentionalData(attentionalData)) {
        return this.generateFallbackAnalysis('Dados atencionais insuficientes');
      }

      // Análises principais baseadas nos dados do AttentionalSelectivityCollector
      const focusAnalysis = this.analyzeFocusCapacity(attentionalData);
      const selectivityAnalysis = this.analyzeSelectivity(attentionalData);
      const cognitiveFlexibility = this.analyzeCognitiveFlexibility(attentionalData);
      const attentionalControl = this.analyzeAttentionalControl(attentionalData);
      const therapeuticProfile = this.generateTherapeuticProfile(attentionalData, gameContext);

      const results = {
        // Capacidade de foco
        focusStability: focusAnalysis.stability,
        sustainedAttentionScore: focusAnalysis.sustained,
        focusEndurance: focusAnalysis.endurance,
        attentionalSpan: focusAnalysis.span,
        
        // Seletividade atencional
        selectivityScore: selectivityAnalysis.score,
        distractorResistance: selectivityAnalysis.resistance,
        filteringEfficiency: selectivityAnalysis.filtering,
        prioritizationSkill: selectivityAnalysis.prioritization,
        
        // Flexibilidade cognitiva
        shiftingAbility: cognitiveFlexibility.shifting,
        taskSwitching: cognitiveFlexibility.switching,
        adaptiveControl: cognitiveFlexibility.adaptive,
        setShifting: cognitiveFlexibility.setShifting,
        
        // Controle atencional
        topDownControl: attentionalControl.topDown,
        bottomUpSensitivity: attentionalControl.bottomUp,
        inhibitoryControl: attentionalControl.inhibition,
        executiveAttention: attentionalControl.executive,
        
        // Perfil terapêutico
        attentionalProfile: therapeuticProfile.profile,
        deficitAreas: therapeuticProfile.deficits,
        strengthAreas: therapeuticProfile.strengths,
        interventionTargets: therapeuticProfile.targets,
        
        // Padrões temporais
        attentionalRhythm: this.analyzeAttentionalRhythm(attentionalData),
        fatiguePattern: this.analyzeFatiguePattern(attentionalData),
        peakPerformanceTime: this.identifyPeakPerformance(attentionalData),
        
        // Indicadores clínicos
        adhd_indicators: this.assessADHDIndicators(attentionalData),
        anxiety_indicators: this.assessAnxietyIndicators(attentionalData),
        processing_indicators: this.assessProcessingIndicators(attentionalData),
        
        // Recomendações terapêuticas
        attentionalTraining: this.recommendAttentionalTraining(attentionalData),
        environmentalModifications: this.recommendEnvironmentalMods(attentionalData),
        cognitiveStrategies: this.recommendCognitiveStrategies(attentionalData),
        
        // Métricas de progresso
        improvementPotential: this.assessImprovementPotential(attentionalData),
        trainingReadiness: this.assessTrainingReadiness(attentionalData),
        transferProbability: this.assessTransferProbability(attentionalData),
        
        // Metadados
        analysisTimestamp: new Date().toISOString(),
        analyzerVersion: this.version,
        dataQuality: this.assessAnalysisQuality(attentionalData),
        gameContext: gameContext.gameType || 'unknown'
      };

      console.log('✅ AttentionalSelectivityAnalyzer: Análise concluída:', {
        selectivity: Math.round(results.selectivityScore * 100),
        focus: Math.round(results.focusStability * 100),
        flexibility: Math.round(results.shiftingAbility * 100)
      });

      return results;

    } catch (error) {
      console.error('❌ AttentionalSelectivityAnalyzer: Erro na análise:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de entrada do AttentionalSelectivityCollector
   */
  validateAttentionalData(data) {
    if (!data) return false;
    
    const requiredFields = ['attentionalCapacity', 'selectivityScore', 'focusStability'];
    return requiredFields.some(field => data.hasOwnProperty(field));
  }

  /**
   * Analisa capacidade de foco
   */
  analyzeFocusCapacity(data) {
    return {
      stability: data.focusStability || 0.5,
      sustained: data.sustainedAttention || 0.5,
      endurance: data.attentionalEndurance || 0.5,
      span: data.attentionalSpan || 0.5
    };
  }

  /**
   * Analisa seletividade atencional
   */
  analyzeSelectivity(data) {
    return {
      score: data.selectivityScore || 0.5,
      resistance: data.distractorResistance || 0.5,
      filtering: data.filteringEfficiency || 0.5,
      prioritization: data.prioritizationSkill || 0.5
    };
  }

  /**
   * Analisa flexibilidade cognitiva
   */
  analyzeCognitiveFlexibility(data) {
    return {
      shifting: data.cognitiveFlexibility || 0.5,
      switching: data.taskSwitching || 0.5,
      adaptive: data.adaptiveControl || 0.5,
      setShifting: data.attentionalShifting || 0.5
    };
  }

  /**
   * Analisa controle atencional
   */
  analyzeAttentionalControl(data) {
    return {
      topDown: data.topDownControl || 0.5,
      bottomUp: data.bottomUpSensitivity || 0.5,
      inhibition: data.inhibitoryControl || 0.5,
      executive: data.executiveAttention || 0.5
    };
  }

  /**
   * Gera perfil terapêutico
   */
  generateTherapeuticProfile(data, context) {
    const profile = this.classifyAttentionalProfile(data);
    const deficits = this.identifyDeficitAreas(data);
    const strengths = this.identifyStrengthAreas(data);
    const targets = this.identifyInterventionTargets(deficits, strengths);
    
    return { profile, deficits, strengths, targets };
  }

  /**
   * Classifica perfil atencional
   */
  classifyAttentionalProfile(data) {
    const selectivity = data.selectivityScore || 0.5;
    const focus = data.focusStability || 0.5;
    const flexibility = data.cognitiveFlexibility || 0.5;
    
    if (selectivity > 0.8 && focus > 0.8 && flexibility > 0.8) {
      return 'superior_attention';
    } else if (selectivity > 0.7 && focus > 0.7) {
      return 'strong_selective_attention';
    } else if (focus > 0.7 && flexibility < 0.5) {
      return 'focused_but_rigid';
    } else if (selectivity < 0.5 && focus < 0.5) {
      return 'attention_deficit_pattern';
    } else if (flexibility > 0.7 && selectivity < 0.6) {
      return 'flexible_but_distractible';
    }
    
    return 'mixed_attention_profile';
  }

  /**
   * Identifica áreas de déficit
   */
  identifyDeficitAreas(data) {
    const deficits = [];
    
    if ((data.selectivityScore || 0) < 0.5) deficits.push('selective_attention');
    if ((data.focusStability || 0) < 0.5) deficits.push('sustained_attention');
    if ((data.cognitiveFlexibility || 0) < 0.5) deficits.push('cognitive_flexibility');
    if ((data.distractorResistance || 0) < 0.5) deficits.push('distractor_resistance');
    if ((data.inhibitoryControl || 0) < 0.5) deficits.push('inhibitory_control');
    
    return deficits;
  }

  /**
   * Identifica áreas de força
   */
  identifyStrengthAreas(data) {
    const strengths = [];
    
    if ((data.selectivityScore || 0) > 0.7) strengths.push('selective_attention');
    if ((data.focusStability || 0) > 0.7) strengths.push('sustained_attention');
    if ((data.cognitiveFlexibility || 0) > 0.7) strengths.push('cognitive_flexibility');
    if ((data.distractorResistance || 0) > 0.7) strengths.push('distractor_resistance');
    if ((data.inhibitoryControl || 0) > 0.7) strengths.push('inhibitory_control');
    
    return strengths;
  }

  /**
   * Identifica alvos de intervenção
   */
  identifyInterventionTargets(deficits, strengths) {
    const targets = [];
    
    // Priorizar déficits mais críticos
    if (deficits.includes('selective_attention')) {
      targets.push('Treino de atenção seletiva com estímulos competitivos');
    }
    if (deficits.includes('sustained_attention')) {
      targets.push('Exercícios de atenção sustentada com duração progressiva');
    }
    if (deficits.includes('cognitive_flexibility')) {
      targets.push('Tarefas de alternância de conjuntos mentais');
    }
    
    // Usar forças para compensar fraquezas
    if (strengths.includes('sustained_attention') && deficits.includes('selective_attention')) {
      targets.push('Usar capacidade de foco para fortalecer seletividade');
    }
    
    return targets;
  }

  /**
   * Analisa ritmo atencional
   */
  analyzeAttentionalRhythm(data) {
    // Analisar padrões temporais de atenção
    const variability = data.attentionalVariability || 0.5;
    const stability = data.focusStability || 0.5;
    
    if (variability < 0.3 && stability > 0.7) {
      return 'stable_consistent';
    } else if (variability > 0.7) {
      return 'highly_variable';
    } else if (variability < 0.5 && stability < 0.5) {
      return 'consistently_low';
    }
    
    return 'moderately_variable';
  }

  /**
   * Analisa padrão de fadiga
   */
  analyzeFatiguePattern(data) {
    const endurance = data.attentionalEndurance || 0.5;
    const decline = data.performanceDecline || 0;
    
    if (endurance > 0.8 && decline < 0.2) {
      return 'fatigue_resistant';
    } else if (decline > 0.5) {
      return 'rapid_fatigue';
    } else if (endurance < 0.5) {
      return 'limited_endurance';
    }
    
    return 'moderate_fatigue';
  }

  /**
   * Identifica pico de performance
   */
  identifyPeakPerformance(data) {
    // Analisar quando a atenção está mais forte
    const timeOfDay = data.peakAttentionTime || 'unknown';
    const duration = data.peakDuration || 0;
    
    return {
      timeOfDay,
      duration,
      recommendation: this.generateTimingRecommendation(timeOfDay, duration)
    };
  }

  /**
   * Gera recomendação de timing
   */
  generateTimingRecommendation(timeOfDay, duration) {
    if (timeOfDay === 'morning') {
      return 'Agendar atividades cognitivas complexas pela manhã';
    } else if (timeOfDay === 'afternoon') {
      return 'Aproveitar período vespertino para tarefas atencionais';
    } else if (duration > 30) {
      return 'Sessões de treino podem ser mais longas';
    }
    
    return 'Monitorar padrões atencionais para otimizar timing';
  }

  /**
   * Avalia indicadores de TDAH
   */
  assessADHDIndicators(data) {
    const indicators = [];
    
    if ((data.selectivityScore || 0) < 0.4) indicators.push('poor_selective_attention');
    if ((data.focusStability || 0) < 0.4) indicators.push('difficulty_sustaining_attention');
    if ((data.inhibitoryControl || 0) < 0.4) indicators.push('poor_inhibitory_control');
    if ((data.distractorResistance || 0) < 0.4) indicators.push('high_distractibility');
    
    return {
      indicators,
      riskLevel: this.calculateADHDRisk(indicators),
      recommendation: this.generateADHDRecommendation(indicators)
    };
  }

  /**
   * Calcula risco de TDAH
   */
  calculateADHDRisk(indicators) {
    if (indicators.length >= 3) return 'high';
    if (indicators.length >= 2) return 'moderate';
    if (indicators.length >= 1) return 'low';
    return 'minimal';
  }

  /**
   * Gera recomendação para TDAH
   */
  generateADHDRecommendation(indicators) {
    if (indicators.length >= 3) {
      return 'Considerar avaliação neuropsicológica completa';
    } else if (indicators.length >= 2) {
      return 'Implementar estratégias de treino atencional estruturado';
    }
    return 'Continuar monitoramento de padrões atencionais';
  }

  /**
   * Avalia indicadores de ansiedade
   */
  assessAnxietyIndicators(data) {
    const indicators = [];
    
    if ((data.attentionalVariability || 0) > 0.7) indicators.push('high_attention_variability');
    if ((data.distractorSensitivity || 0) > 0.7) indicators.push('hypervigilance');
    if ((data.performanceAnxiety || 0) > 0.6) indicators.push('performance_anxiety');
    
    return {
      indicators,
      level: indicators.length > 1 ? 'significant' : indicators.length > 0 ? 'mild' : 'minimal'
    };
  }

  /**
   * Avalia indicadores de processamento
   */
  assessProcessingIndicators(data) {
    const speed = data.processingSpeed || 0.5;
    const efficiency = data.processingEfficiency || 0.5;
    
    return {
      speed: speed > 0.7 ? 'fast' : speed < 0.4 ? 'slow' : 'average',
      efficiency: efficiency > 0.7 ? 'high' : efficiency < 0.4 ? 'low' : 'moderate',
      overall: this.calculateOverallProcessing(speed, efficiency)
    };
  }

  /**
   * Calcula processamento geral
   */
  calculateOverallProcessing(speed, efficiency) {
    const combined = (speed * 0.6) + (efficiency * 0.4);
    
    if (combined > 0.75) return 'superior';
    if (combined > 0.6) return 'above_average';
    if (combined > 0.4) return 'average';
    return 'below_average';
  }

  /**
   * Recomenda treino atencional
   */
  recommendAttentionalTraining(data) {
    const recommendations = [];
    
    if ((data.selectivityScore || 0) < 0.6) {
      recommendations.push('Treino de atenção seletiva com paradigma de busca visual');
    }
    if ((data.focusStability || 0) < 0.6) {
      recommendations.push('Exercícios de atenção sustentada com feedback contínuo');
    }
    if ((data.cognitiveFlexibility || 0) < 0.6) {
      recommendations.push('Tarefas de alternância de conjuntos com dificuldade crescente');
    }
    
    return recommendations;
  }

  /**
   * Recomenda modificações ambientais
   */
  recommendEnvironmentalMods(data) {
    const modifications = [];
    
    if ((data.distractorResistance || 0) < 0.5) {
      modifications.push('Reduzir estímulos distratores no ambiente');
    }
    if ((data.focusStability || 0) < 0.5) {
      modifications.push('Criar ambiente estruturado com poucas distrações');
    }
    
    return modifications;
  }

  /**
   * Recomenda estratégias cognitivas
   */
  recommendCognitiveStrategies(data) {
    const strategies = [];
    
    if ((data.selectivityScore || 0) < 0.6) {
      strategies.push('Técnicas de foco direcionado e filtragem atencional');
    }
    if ((data.cognitiveFlexibility || 0) < 0.6) {
      strategies.push('Estratégias de mudança de conjuntos mentais');
    }
    
    return strategies;
  }

  /**
   * Avalia potencial de melhoria
   */
  assessImprovementPotential(data) {
    const baseline = Math.min(
      data.selectivityScore || 0,
      data.focusStability || 0,
      data.cognitiveFlexibility || 0
    );
    
    if (baseline > 0.7) return 'moderate';
    if (baseline > 0.4) return 'high';
    return 'very_high';
  }

  /**
   * Avalia prontidão para treino
   */
  assessTrainingReadiness(data) {
    const motivation = data.engagementLevel || 0.5;
    const stability = data.baselineStability || 0.5;
    
    return (motivation * 0.6) + (stability * 0.4);
  }

  /**
   * Avalia probabilidade de transferência
   */
  assessTransferProbability(data) {
    const flexibility = data.cognitiveFlexibility || 0.5;
    const generalization = data.skillGeneralization || 0.5;
    
    return (flexibility * 0.7) + (generalization * 0.3);
  }

  /**
   * Avalia qualidade da análise
   */
  assessAnalysisQuality(data) {
    let score = 0.5;
    const issues = [];
    
    if (!data.selectivityScore) {
      score -= 0.2;
      issues.push('Dados de seletividade ausentes');
    }
    if (!data.focusStability) {
      score -= 0.2;
      issues.push('Dados de estabilidade de foco ausentes');
    }
    
    return {
      score: Math.max(0, score),
      issues,
      level: score > 0.7 ? 'high' : score > 0.4 ? 'medium' : 'low'
    };
  }

  /**
   * Gera análise de fallback
   */
  generateFallbackAnalysis(errorMessage) {
    return {
      focusStability: 0.5,
      sustainedAttentionScore: 0.5,
      focusEndurance: 0.5,
      attentionalSpan: 0.5,
      selectivityScore: 0.5,
      distractorResistance: 0.5,
      filteringEfficiency: 0.5,
      prioritizationSkill: 0.5,
      shiftingAbility: 0.5,
      taskSwitching: 0.5,
      adaptiveControl: 0.5,
      setShifting: 0.5,
      topDownControl: 0.5,
      bottomUpSensitivity: 0.5,
      inhibitoryControl: 0.5,
      executiveAttention: 0.5,
      attentionalProfile: 'mixed_attention_profile',
      deficitAreas: ['insufficient_data'],
      strengthAreas: [],
      interventionTargets: ['Coletar mais dados atencionais'],
      attentionalRhythm: 'unknown',
      fatiguePattern: 'unknown',
      peakPerformanceTime: { timeOfDay: 'unknown', duration: 0, recommendation: 'Estabelecer linha de base' },
      adhd_indicators: { indicators: [], riskLevel: 'unknown', recommendation: 'Avaliação necessária' },
      anxiety_indicators: { indicators: [], level: 'unknown' },
      processing_indicators: { speed: 'unknown', efficiency: 'unknown', overall: 'unknown' },
      attentionalTraining: ['Estabelecer capacidades baseline'],
      environmentalModifications: ['Avaliar ambiente atual'],
      cognitiveStrategies: ['Definir estratégias baseadas em dados'],
      improvementPotential: 'unknown',
      trainingReadiness: 0.5,
      transferProbability: 0.5,
      analysisTimestamp: new Date().toISOString(),
      analyzerVersion: this.version,
      dataQuality: { score: 0, issues: [errorMessage], level: 'error' },
      gameContext: 'unknown',
      status: 'fallback',
      error: errorMessage
    };
  }
}
