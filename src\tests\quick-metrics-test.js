/**
 * 🚀 TESTE RÁPIDO DE DIAGNÓSTICO - SISTEMA DE MÉTRICAS
 * Identifica problemas específicos no fluxo de métricas
 * Portal Betina V3
 */

console.log('🚀 INICIANDO DIAGNÓSTICO RÁPIDO DO SISTEMA DE MÉTRICAS');
console.log('=====================================================');

/**
 * Teste individual de cada componente
 */
async function quickDiagnostic() {
  const results = {
    imports: false,
    collectors: false,
    processors: false,
    analyzers: false,
    orchestrator: false,
    errors: []
  };

  // 1. TESTE DE IMPORTS
  console.log('\n1️⃣ TESTANDO IMPORTS...');
  try {
    // Teste ColorMatch Collectors
    const colorMatchModule = await import('../games/ColorMatch/collectors/index.js');
    if (colorMatchModule.ColorMatchCollectorsHub) {
      console.log('✅ ColorMatchCollectorsHub importado');
      results.imports = true;
    } else {
      console.log('❌ ColorMatchCollectorsHub não encontrado');
      results.errors.push('ColorMatchCollectorsHub não encontrado');
    }
  } catch (error) {
    console.log('❌ Erro ao importar ColorMatch:', error.message);
    results.errors.push(`Import ColorMatch: ${error.message}`);
  }

  try {
    // Teste GameSpecificProcessors
    const processorsModule = await import('../api/services/processors/GameSpecificProcessors.js');
    if (processorsModule.GameSpecificProcessors) {
      console.log('✅ GameSpecificProcessors importado');
    } else {
      console.log('❌ GameSpecificProcessors não encontrado');
      results.errors.push('GameSpecificProcessors não encontrado');
    }
  } catch (error) {
    console.log('❌ Erro ao importar Processors:', error.message);
    results.errors.push(`Import Processors: ${error.message}`);
  }

  try {
    // Teste SystemOrchestrator
    const orchestratorModule = await import('../api/services/core/SystemOrchestrator.js');
    if (orchestratorModule.getSystemOrchestrator) {
      console.log('✅ SystemOrchestrator importado');
    } else {
      console.log('❌ SystemOrchestrator não encontrado');
      results.errors.push('SystemOrchestrator não encontrado');
    }
  } catch (error) {
    console.log('❌ Erro ao importar SystemOrchestrator:', error.message);
    results.errors.push(`Import SystemOrchestrator: ${error.message}`);
  }

  try {
    // Teste CognitiveAnalyzer
    const cognitiveModule = await import('../api/services/analysis/CognitiveAnalyzer.js');
    if (cognitiveModule.getCognitiveAnalyzer) {
      console.log('✅ CognitiveAnalyzer importado');
    } else {
      console.log('❌ CognitiveAnalyzer não encontrado');
      results.errors.push('CognitiveAnalyzer não encontrado');
    }
  } catch (error) {
    console.log('❌ Erro ao importar CognitiveAnalyzer:', error.message);
    results.errors.push(`Import CognitiveAnalyzer: ${error.message}`);
  }

  // 2. TESTE DE INSTANCIAÇÃO
  console.log('\n2️⃣ TESTANDO INSTANCIAÇÃO...');
  
  try {
    const { ColorMatchCollectorsHub } = await import('../games/ColorMatch/collectors/index.js');
    const collectorsHub = new ColorMatchCollectorsHub();
    console.log('✅ ColorMatchCollectorsHub instanciado');
    results.collectors = true;
  } catch (error) {
    console.log('❌ Erro ao instanciar ColorMatchCollectorsHub:', error.message);
    results.errors.push(`Instanciação ColorMatch: ${error.message}`);
  }

  try {
    const { GameSpecificProcessors } = await import('../api/services/processors/GameSpecificProcessors.js');
    const mockDb = { save: async () => ({}), find: async () => [], update: async () => ({}) };
    const processors = new GameSpecificProcessors(mockDb);
    console.log('✅ GameSpecificProcessors instanciado');
    results.processors = true;
  } catch (error) {
    console.log('❌ Erro ao instanciar GameSpecificProcessors:', error.message);
    results.errors.push(`Instanciação Processors: ${error.message}`);
  }

  try {
    const { getCognitiveAnalyzer } = await import('../api/services/analysis/CognitiveAnalyzer.js');
    const analyzer = await getCognitiveAnalyzer();
    console.log('✅ CognitiveAnalyzer instanciado');
    results.analyzers = true;
  } catch (error) {
    console.log('❌ Erro ao instanciar CognitiveAnalyzer:', error.message);
    results.errors.push(`Instanciação CognitiveAnalyzer: ${error.message}`);
  }

  try {
    const { getSystemOrchestrator } = await import('../api/services/core/SystemOrchestrator.js');
    const mockDb = { save: async () => ({}), find: async () => [], update: async () => ({}) };
    const orchestrator = await getSystemOrchestrator(mockDb);
    console.log('✅ SystemOrchestrator instanciado');
    results.orchestrator = true;
  } catch (error) {
    console.log('❌ Erro ao instanciar SystemOrchestrator:', error.message);
    results.errors.push(`Instanciação SystemOrchestrator: ${error.message}`);
  }

  // 3. RELATÓRIO FINAL
  console.log('\n📋 RELATÓRIO DE DIAGNÓSTICO');
  console.log('============================');
  console.log('Imports:', results.imports ? '✅' : '❌');
  console.log('Collectors:', results.collectors ? '✅' : '❌');
  console.log('Processors:', results.processors ? '✅' : '❌');
  console.log('Analyzers:', results.analyzers ? '✅' : '❌');
  console.log('Orchestrator:', results.orchestrator ? '✅' : '❌');

  if (results.errors.length > 0) {
    console.log('\n🚨 ERROS ENCONTRADOS:');
    results.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }

  const success = results.imports && results.collectors && results.processors && results.analyzers && results.orchestrator;
  console.log('\n🎯 STATUS GERAL:', success ? '✅ FUNCIONANDO' : '❌ COM PROBLEMAS');

  return { success, results };
}

// Executar diagnóstico
quickDiagnostic()
  .then(result => {
    console.log('\n🏁 DIAGNÓSTICO CONCLUÍDO');
    process.exit(result.success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 ERRO CRÍTICO NO DIAGNÓSTICO:', error);
    process.exit(1);
  });
