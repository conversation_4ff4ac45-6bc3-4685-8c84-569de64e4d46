/**
 * 🔗 SEQUENTIAL COLOR COLLECTOR
 * Coletor especializado em análise de sequências e padrões cromáticos temporais
 * Portal Betina V3 - FASE 2.1
 */

export class SequentialColorCollector {
  constructor() {
    this.collectorId = 'sequential-color';
    this.version = '3.0';
    this.isActive = true;
    
    // Dados coletados
    this.sequences = [];
    this.patterns = [];
    this.predictions = [];
    this.rules = [];
    this.transitions = [];
    this.temporalData = [];
    
    // Métricas calculadas
    this.metrics = {
      patternRecognition: 0,
      sequentialPrediction: 0,
      ruleFlexibility: 0,
      temporalProcessing: 0,
      inhibitoryControl: 0,
      patternTransfer: 0,
      sequenceSpan: 0,
      consistency: 0
    };
    
    // Configurações
    this.config = {
      maxSequenceLength: 10,
      patternTypes: ['alternating', 'progressive', 'grouped', 'symmetric', 'complex'],
      ruleTypes: ['simple', 'compound', 'conditional', 'hierarchical'],
      difficultyLevels: ['easy', 'medium', 'hard', 'expert']
    };
    
    this.initializeCollector();
  }
  
  initializeCollector() {
    console.log('🔗 SequentialColorCollector inicializado');
    this.startTime = Date.now();
    this.resetMetrics();
  }
  
  resetMetrics() {
    this.metrics = {
      patternRecognition: 0,
      sequentialPrediction: 0,
      ruleFlexibility: 0,
      temporalProcessing: 0,
      inhibitoryControl: 0,
      patternTransfer: 0,
      sequenceSpan: 0,
      consistency: 0
    };
  }
  
  // Método padronizado de coleta
  collect(data) {
    return this.analyze(data);
  }
  
  // Coleta dados de sequência cromática
  collectSequenceData(data) {
    const sequence = {
      id: `seq_${Date.now()}`,
      timestamp: Date.now(),
      targetSequence: data.targetSequence || [],
      userSequence: data.userSequence || [],
      patternType: data.patternType || 'unknown',
      difficulty: data.difficulty || 'medium',
      accuracy: this.calculateSequenceAccuracy(data.targetSequence, data.userSequence),
      completionTime: data.completionTime || 0,
      errors: this.identifySequenceErrors(data.targetSequence, data.userSequence),
      strategy: data.strategy || 'unknown'
    };
    
    this.sequences.push(sequence);
    this.updateSequenceMetrics();
    console.log('🔗 Dados de sequência coletados:', sequence);
  }
  
  // Coleta dados de reconhecimento de padrões
  collectPatternData(data) {
    const pattern = {
      id: `pat_${Date.now()}`,
      timestamp: Date.now(),
      patternType: data.patternType || 'unknown',
      recognized: data.recognized || false,
      recognitionTime: data.recognitionTime || 0,
      confidence: data.confidence || 0,
      complexity: data.complexity || 'medium',
      contextLength: data.contextLength || 0,
      errors: data.errors || []
    };
    
    this.patterns.push(pattern);
    this.updatePatternRecognition();
    console.log('🔗 Dados de padrão coletados:', pattern);
  }
  
  // Coleta dados de predição sequencial
  collectPredictionData(data) {
    const prediction = {
      id: `pred_${Date.now()}`,
      timestamp: Date.now(),
      sequence: data.sequence || [],
      predicted: data.predicted || '',
      actual: data.actual || '',
      correct: data.predicted === data.actual,
      confidence: data.confidence || 0,
      predictionTime: data.predictionTime || 0,
      lookAhead: data.lookAhead || 1,
      patternType: data.patternType || 'unknown'
    };
    
    this.predictions.push(prediction);
    this.updateSequentialPrediction();
    console.log('🔗 Dados de predição coletados:', prediction);
  }
  
  // Coleta dados de flexibilidade de regras
  collectRuleData(data) {
    const rule = {
      id: `rule_${Date.now()}`,
      timestamp: Date.now(),
      ruleType: data.ruleType || 'simple',
      ruleChange: data.ruleChange || false,
      adaptationTime: data.adaptationTime || 0,
      adaptationAccuracy: data.adaptationAccuracy || 0,
      perseverationErrors: data.perseverationErrors || 0,
      flexibilityScore: this.calculateFlexibilityScore(data)
    };
    
    this.rules.push(rule);
    this.updateRuleFlexibility();
    console.log('🔗 Dados de regra coletados:', rule);
  }
  
  // Coleta dados de transições temporais
  collectTransitionData(data) {
    const transition = {
      id: `trans_${Date.now()}`,
      timestamp: Date.now(),
      fromColor: data.fromColor || '',
      toColor: data.toColor || '',
      transitionTime: data.transitionTime || 0,
      transitionType: data.transitionType || 'unknown',
      smooth: data.smooth !== false,
      anticipation: data.anticipation || 0
    };
    
    this.transitions.push(transition);
    this.updateTemporalProcessing();
    console.log('🔗 Dados de transição coletados:', transition);
  }
  
  // Coleta dados temporais
  collectTemporalData(data) {
    const temporal = {
      id: `temp_${Date.now()}`,
      timestamp: Date.now(),
      sequenceLength: data.sequenceLength || 0,
      processingTime: data.processingTime || 0,
      rhythm: data.rhythm || 'irregular',
      tempo: data.tempo || 'medium',
      synchronization: data.synchronization || 0
    };
    
    this.temporalData.push(temporal);
    console.log('🔗 Dados temporais coletados:', temporal);
  }
  
  // Análise principal
  async analyze(data) {
    try {
      if (!this.validateSequentialData(data)) {
        return this.generateFallbackAnalysis('Dados sequenciais insuficientes');
      }
      
      const sequentialData = this.extractSequentialData(data);
      
      return {
        patternRecognition: this.analyzePatternRecognition(sequentialData),
        sequentialPrediction: this.analyzeSequentialPrediction(sequentialData),
        ruleFlexibility: this.analyzeRuleFlexibility(sequentialData),
        temporalProcessing: this.analyzeTemporalProcessing(sequentialData),
        inhibitoryControl: this.analyzeInhibitoryControl(sequentialData),
        patternTransfer: this.analyzePatternTransfer(sequentialData),
        sequenceCapacity: this.analyzeSequenceCapacity(sequentialData),
        consistency: this.analyzeConsistency(sequentialData),
        overallProfile: this.generateSequentialProfile(sequentialData)
      };
    } catch (error) {
      console.error('Erro na análise sequencial:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }
  
  // Validação de dados
  validateSequentialData(data) {
    if (!data) return false;
    
    const hasSequences = data.sequences || data.colorSequences || data.patterns;
    const hasInteractions = data.interactions || data.responses;
    
    return hasSequences || hasInteractions;
  }
  
  // Extração de dados sequenciais
  extractSequentialData(data) {
    let sequences = [];
    let patterns = [];
    let predictions = [];
    
    // Extrair sequências
    if (data.sequences) {
      sequences = data.sequences;
    } else if (data.colorSequences) {
      sequences = data.colorSequences;
    } else if (data.interactions) {
      sequences = this.reconstructSequencesFromInteractions(data.interactions);
    }
    
    // Extrair padrões
    if (data.patterns) {
      patterns = data.patterns;
    } else {
      patterns = this.extractPatternsFromSequences(sequences);
    }
    
    // Extrair predições
    if (data.predictions) {
      predictions = data.predictions;
    } else {
      predictions = this.extractPredictionsFromInteractions(data.interactions || []);
    }
    
    return {
      sequences,
      patterns,
      predictions,
      difficulty: data.difficulty || 'medium',
      sessionDuration: data.sessionDuration || 0
    };
  }
  
  // Reconstrói sequências das interações
  reconstructSequencesFromInteractions(interactions) {
    const sequences = [];
    let currentSequence = [];
    
    interactions.forEach(interaction => {
      if (interaction.type === 'sequence_start' || interaction.isSequenceStart) {
        if (currentSequence.length > 0) {
          sequences.push(this.createSequenceObject(currentSequence));
        }
        currentSequence = [];
      }
      
      if (interaction.color || interaction.selectedColor) {
        currentSequence.push({
          color: interaction.color || interaction.selectedColor,
          timestamp: interaction.timestamp,
          position: currentSequence.length,
          correct: interaction.correct !== false
        });
      }
    });
    
    if (currentSequence.length > 0) {
      sequences.push(this.createSequenceObject(currentSequence));
    }
    
    return sequences;
  }
  
  // Cria objeto de sequência estruturado
  createSequenceObject(sequenceArray) {
    return {
      id: `seq_${Date.now()}_${Math.random()}`,
      items: sequenceArray,
      length: sequenceArray.length,
      pattern: this.identifyPattern(sequenceArray),
      accuracy: this.calculateItemAccuracy(sequenceArray),
      completionTime: this.calculateCompletionTime(sequenceArray)
    };
  }
  
  // Extrai padrões das sequências
  extractPatternsFromSequences(sequences) {
    return sequences.map(seq => {
      const pattern = this.identifyPattern(seq.items || seq);
      return {
        id: `pat_${seq.id || Date.now()}`,
        type: pattern.type,
        complexity: pattern.complexity,
        recognized: pattern.confidence > 0.7,
        confidence: pattern.confidence
      };
    });
  }
  
  // Extrai predições das interações
  extractPredictionsFromInteractions(interactions) {
    return interactions
      .filter(i => i.type === 'prediction' || i.isPrediction)
      .map(i => ({
        id: `pred_${i.timestamp || Date.now()}`,
        predicted: i.predicted || i.selectedColor,
        actual: i.actual || i.correctColor,
        correct: i.correct !== false,
        confidence: i.confidence || 0.5
      }));
  }
  
  // Análise de reconhecimento de padrões
  analyzePatternRecognition(sequentialData) {
    const { patterns } = sequentialData;
    
    if (patterns.length === 0) {
      return this.getDefaultAnalysis('patternRecognition');
    }
    
    const recognitionRate = patterns.filter(p => p.recognized).length / patterns.length;
    const avgConfidence = patterns.reduce((sum, p) => sum + (p.confidence || 0), 0) / patterns.length;
    const complexityHandling = this.analyzeComplexityHandling(patterns);
    
    return {
      recognitionRate,
      confidence: avgConfidence,
      complexityHandling,
      patternTypes: this.analyzePatternTypes(patterns),
      overallScore: (recognitionRate * 0.5 + avgConfidence * 0.3 + complexityHandling * 0.2),
      level: this.categorizeLevel(recognitionRate),
      recommendations: this.generatePatternRecommendations(recognitionRate, complexityHandling)
    };
  }
  
  // Análise de predição sequencial
  analyzeSequentialPrediction(sequentialData) {
    const { predictions } = sequentialData;
    
    if (predictions.length === 0) {
      return this.getDefaultAnalysis('sequentialPrediction');
    }
    
    const predictionAccuracy = predictions.filter(p => p.correct).length / predictions.length;
    const avgConfidence = predictions.reduce((sum, p) => sum + (p.confidence || 0), 0) / predictions.length;
    const lookAheadCapacity = this.analyzeLookAheadCapacity(predictions);
    
    return {
      accuracy: predictionAccuracy,
      confidence: avgConfidence,
      lookAheadCapacity,
      anticipationSkill: this.analyzeAnticipationSkill(predictions),
      overallScore: (predictionAccuracy * 0.6 + avgConfidence * 0.2 + lookAheadCapacity * 0.2),
      level: this.categorizeLevel(predictionAccuracy),
      recommendations: this.generatePredictionRecommendations(predictionAccuracy, lookAheadCapacity)
    };
  }
  
  // Análise de flexibilidade de regras
  analyzeRuleFlexibility(sequentialData) {
    const { sequences } = sequentialData;
    
    const ruleChanges = this.detectRuleChanges(sequences);
    const adaptationSpeed = this.calculateAdaptationSpeed(ruleChanges);
    const perseverationErrors = this.countPerseverationErrors(sequences);
    const flexibilityScore = this.calculateOverallFlexibility(ruleChanges, perseverationErrors);
    
    return {
      adaptationSpeed,
      flexibilityScore,
      perseverationErrors: perseverationErrors.length,
      ruleChangeHandling: this.analyzeRuleChangeHandling(ruleChanges),
      overallScore: flexibilityScore,
      level: this.categorizeLevel(flexibilityScore),
      recommendations: this.generateFlexibilityRecommendations(flexibilityScore, perseverationErrors.length)
    };
  }
  
  // Análise de processamento temporal
  analyzeTemporalProcessing(sequentialData) {
    const { sequences } = sequentialData;
    
    const rhythmConsistency = this.analyzeRhythmConsistency(sequences);
    const temporalAccuracy = this.analyzeTemporalAccuracy(sequences);
    const synchronization = this.analyzeSynchronization(sequences);
    
    return {
      rhythmConsistency,
      temporalAccuracy,
      synchronization,
      timingPrecision: this.analyzeTimingPrecision(sequences),
      overallScore: (rhythmConsistency * 0.4 + temporalAccuracy * 0.3 + synchronization * 0.3),
      level: this.categorizeLevel(rhythmConsistency),
      recommendations: this.generateTemporalRecommendations(rhythmConsistency, temporalAccuracy)
    };
  }
  
  // Análise de controle inibitório
  analyzeInhibitoryControl(sequentialData) {
    const { sequences } = sequentialData;
    
    const impulseControl = this.analyzeImpulseControl(sequences);
    const conflictResolution = this.analyzeConflictResolution(sequences);
    const responseInhibition = this.analyzeResponseInhibition(sequences);
    
    return {
      impulseControl,
      conflictResolution,
      responseInhibition,
      overallScore: (impulseControl * 0.4 + conflictResolution * 0.3 + responseInhibition * 0.3),
      level: this.categorizeLevel(impulseControl),
      recommendations: this.generateInhibitoryRecommendations(impulseControl, conflictResolution)
    };
  }
  
  // Análise de transferência de padrões
  analyzePatternTransfer(sequentialData) {
    const { sequences, patterns } = sequentialData;
    
    const transferSuccess = this.analyzeTransferSuccess(sequences, patterns);
    const generalization = this.analyzeGeneralization(sequences);
    const abstractionLevel = this.analyzeAbstractionLevel(patterns);
    
    return {
      transferSuccess,
      generalization,
      abstractionLevel,
      overallScore: (transferSuccess * 0.5 + generalization * 0.3 + abstractionLevel * 0.2),
      level: this.categorizeLevel(transferSuccess),
      recommendations: this.generateTransferRecommendations(transferSuccess, generalization)
    };
  }
  
  // Análise de capacidade sequencial
  analyzeSequenceCapacity(sequentialData) {
    const { sequences } = sequentialData;
    
    const maxSpan = this.calculateMaxSequenceSpan(sequences);
    const avgSpan = this.calculateAverageSpan(sequences);
    const spanConsistency = this.calculateSpanConsistency(sequences);
    
    return {
      maxSpan,
      avgSpan,
      consistency: spanConsistency,
      capacity: this.normalizeCapacity(maxSpan),
      level: this.categorizeCapacityLevel(maxSpan),
      recommendations: this.generateCapacityRecommendations(maxSpan, spanConsistency)
    };
  }
  
  // Análise de consistência
  analyzeConsistency(sequentialData) {
    const { sequences } = sequentialData;
    
    const accuracyConsistency = this.calculateAccuracyConsistency(sequences);
    const speedConsistency = this.calculateSpeedConsistency(sequences);
    const strategyConsistency = this.calculateStrategyConsistency(sequences);
    
    return {
      accuracy: accuracyConsistency,
      speed: speedConsistency,
      strategy: strategyConsistency,
      overallScore: (accuracyConsistency * 0.5 + speedConsistency * 0.3 + strategyConsistency * 0.2),
      level: this.categorizeLevel(accuracyConsistency),
      recommendations: this.generateConsistencyRecommendations(accuracyConsistency, speedConsistency)
    };
  }
  
  // Métodos auxiliares para cálculos específicos
  
  calculateSequenceAccuracy(target, user) {
    if (!target || !user || target.length === 0) return 0;
    
    const minLength = Math.min(target.length, user.length);
    let correct = 0;
    
    for (let i = 0; i < minLength; i++) {
      if (target[i] === user[i]) correct++;
    }
    
    return correct / target.length;
  }
  
  identifySequenceErrors(target, user) {
    const errors = [];
    if (!target || !user) return errors;
    
    for (let i = 0; i < Math.max(target.length, user.length); i++) {
      if (target[i] !== user[i]) {
        errors.push({
          position: i,
          expected: target[i] || null,
          actual: user[i] || null,
          type: this.classifySequenceError(target[i], user[i], i)
        });
      }
    }
    
    return errors;
  }
  
  classifySequenceError(expected, actual, position) {
    if (!expected && actual) return 'insertion';
    if (expected && !actual) return 'omission';
    if (expected && actual) return 'substitution';
    return 'unknown';
  }
  
  calculateFlexibilityScore(data) {
    const adaptationTime = data.adaptationTime || 0;
    const adaptationAccuracy = data.adaptationAccuracy || 0;
    const perseverationErrors = data.perseverationErrors || 0;
    
    // Score baseado na rapidez e precisão da adaptação, penalizando perseveração
    const timeScore = Math.max(0, 1 - (adaptationTime / 5000)); // 5s = score 0
    const accuracyScore = adaptationAccuracy;
    const perseverationPenalty = Math.min(0.5, perseverationErrors * 0.1);
    
    return Math.max(0, (timeScore * 0.4 + accuracyScore * 0.6) - perseverationPenalty);
  }
  
  identifyPattern(sequenceItems) {
    if (!sequenceItems || sequenceItems.length < 2) {
      return { type: 'none', complexity: 'simple', confidence: 0 };
    }
    
    const colors = sequenceItems.map(item => item.color || item);
    
    // Detectar padrões alternados
    if (this.isAlternatingPattern(colors)) {
      return { type: 'alternating', complexity: 'simple', confidence: 0.9 };
    }
    
    // Detectar padrões progressivos
    if (this.isProgressivePattern(colors)) {
      return { type: 'progressive', complexity: 'medium', confidence: 0.8 };
    }
    
    // Detectar padrões agrupados
    if (this.isGroupedPattern(colors)) {
      return { type: 'grouped', complexity: 'medium', confidence: 0.7 };
    }
    
    // Detectar padrões simétricos
    if (this.isSymmetricPattern(colors)) {
      return { type: 'symmetric', complexity: 'complex', confidence: 0.8 };
    }
    
    return { type: 'irregular', complexity: 'complex', confidence: 0.3 };
  }
  
  isAlternatingPattern(colors) {
    if (colors.length < 3) return false;
    
    for (let i = 2; i < colors.length; i++) {
      if (colors[i] !== colors[i - 2]) return false;
    }
    return true;
  }
  
  isProgressivePattern(colors) {
    // Implementação simplificada - detectar se há progressão lógica
    return colors.length >= 3 && this.hasLogicalProgression(colors);
  }
  
  hasLogicalProgression(colors) {
    // Verificar se há progressão em alguma dimensão (ex: tom, saturação)
    // Implementação simplificada
    return false;
  }
  
  isGroupedPattern(colors) {
    if (colors.length < 4) return false;
    
    // Detectar repetição de grupos de 2 ou 3 cores
    const groupSize = this.detectGroupSize(colors);
    return groupSize > 1 && colors.length % groupSize === 0;
  }
  
  detectGroupSize(colors) {
    // Tentar detectar tamanho do grupo repetitivo
    for (let size = 2; size <= Math.floor(colors.length / 2); size++) {
      if (this.isRepeatingGroup(colors, size)) return size;
    }
    return 1;
  }
  
  isRepeatingGroup(colors, groupSize) {
    const groups = Math.floor(colors.length / groupSize);
    if (groups < 2) return false;
    
    const firstGroup = colors.slice(0, groupSize);
    
    for (let g = 1; g < groups; g++) {
      const currentGroup = colors.slice(g * groupSize, (g + 1) * groupSize);
      if (!this.arraysEqual(firstGroup, currentGroup)) return false;
    }
    
    return true;
  }
  
  isSymmetricPattern(colors) {
    if (colors.length < 3) return false;
    
    // Verificar se a sequência é palindrômica
    for (let i = 0; i < Math.floor(colors.length / 2); i++) {
      if (colors[i] !== colors[colors.length - 1 - i]) return false;
    }
    
    return true;
  }
  
  arraysEqual(a, b) {
    return a.length === b.length && a.every((val, i) => val === b[i]);
  }
  
  calculateItemAccuracy(items) {
    if (items.length === 0) return 0;
    const correct = items.filter(item => item.correct !== false).length;
    return correct / items.length;
  }
  
  calculateCompletionTime(items) {
    if (items.length < 2) return 0;
    
    const firstTimestamp = items[0].timestamp;
    const lastTimestamp = items[items.length - 1].timestamp;
    
    return lastTimestamp - firstTimestamp;
  }
  
  // Métodos de análise específica
  
  analyzeComplexityHandling(patterns) {
    const complexPatterns = patterns.filter(p => p.complexity === 'complex' || p.complexity === 'advanced');
    if (complexPatterns.length === 0) return 0.5;
    
    const successRate = complexPatterns.filter(p => p.recognized).length / complexPatterns.length;
    return successRate;
  }
  
  analyzePatternTypes(patterns) {
    const types = {};
    patterns.forEach(p => {
      if (!types[p.type]) types[p.type] = { total: 0, recognized: 0 };
      types[p.type].total++;
      if (p.recognized) types[p.type].recognized++;
    });
    
    Object.keys(types).forEach(type => {
      types[type].rate = types[type].recognized / types[type].total;
    });
    
    return types;
  }
  
  analyzeLookAheadCapacity(predictions) {
    // Analisar capacidade de predição a múltiplos passos
    const lookAheadScores = {};
    predictions.forEach(p => {
      const steps = p.lookAhead || 1;
      if (!lookAheadScores[steps]) lookAheadScores[steps] = [];
      lookAheadScores[steps].push(p.correct ? 1 : 0);
    });
    
    let maxLookAhead = 1;
    Object.keys(lookAheadScores).forEach(steps => {
      const avgScore = lookAheadScores[steps].reduce((sum, score) => sum + score, 0) / lookAheadScores[steps].length;
      if (avgScore >= 0.7 && parseInt(steps) > maxLookAhead) {
        maxLookAhead = parseInt(steps);
      }
    });
    
    return maxLookAhead;
  }
  
  analyzeAnticipationSkill(predictions) {
    // Analisar habilidade de antecipação baseada em tempo de predição
    const avgPredictionTime = predictions.reduce((sum, p) => sum + (p.predictionTime || 0), 0) / predictions.length;
    const accuracy = predictions.filter(p => p.correct).length / predictions.length;
    
    // Combinar velocidade e precisão
    const speedScore = Math.max(0, 1 - (avgPredictionTime / 3000)); // 3s = score 0
    return (speedScore * 0.4 + accuracy * 0.6);
  }
  
  detectRuleChanges(sequences) {
    const changes = [];
    
    for (let i = 1; i < sequences.length; i++) {
      const prevPattern = this.identifyPattern(sequences[i-1].items || sequences[i-1]);
      const currPattern = this.identifyPattern(sequences[i].items || sequences[i]);
      
      if (prevPattern.type !== currPattern.type) {
        changes.push({
          position: i,
          from: prevPattern.type,
          to: currPattern.type,
          adaptationTime: this.calculateAdaptationTime(sequences, i),
          adaptationSuccess: this.calculateAdaptationSuccess(sequences, i)
        });
      }
    }
    
    return changes;
  }
  
  calculateAdaptationSpeed(ruleChanges) {
    if (ruleChanges.length === 0) return 0.5;
    
    const avgAdaptationTime = ruleChanges.reduce((sum, change) => sum + change.adaptationTime, 0) / ruleChanges.length;
    return Math.max(0, 1 - (avgAdaptationTime / 5000)); // 5s = score 0
  }
  
  countPerseverationErrors(sequences) {
    const errors = [];
    
    for (let i = 1; i < sequences.length; i++) {
      const prevPattern = this.identifyPattern(sequences[i-1].items || sequences[i-1]);
      const currPattern = this.identifyPattern(sequences[i].items || sequences[i]);
      const currAccuracy = sequences[i].accuracy || this.calculateItemAccuracy(sequences[i].items || sequences[i]);
      
      // Se mudou o padrão mas a pessoa ainda usa o anterior (baixa accuracy)
      if (prevPattern.type !== currPattern.type && currAccuracy < 0.5) {
        errors.push({
          position: i,
          expectedPattern: currPattern.type,
          usedPattern: prevPattern.type
        });
      }
    }
    
    return errors;
  }
  
  calculateOverallFlexibility(ruleChanges, perseverationErrors) {
    if (ruleChanges.length === 0) return 0.5;
    
    const adaptationSuccess = ruleChanges.reduce((sum, change) => sum + change.adaptationSuccess, 0) / ruleChanges.length;
    const perseverationPenalty = Math.min(0.3, perseverationErrors.length * 0.05);
    
    return Math.max(0, adaptationSuccess - perseverationPenalty);
  }
  
  analyzeRuleChangeHandling(ruleChanges) {
    return {
      totalChanges: ruleChanges.length,
      avgAdaptationTime: ruleChanges.reduce((sum, c) => sum + c.adaptationTime, 0) / Math.max(1, ruleChanges.length),
      successRate: ruleChanges.reduce((sum, c) => sum + c.adaptationSuccess, 0) / Math.max(1, ruleChanges.length)
    };
  }
  
  // Métodos de análise temporal
  
  analyzeRhythmConsistency(sequences) {
    const rhythmScores = [];
    
    sequences.forEach(seq => {
      const items = seq.items || seq;
      if (items.length >= 3) {
        const intervals = [];
        for (let i = 1; i < items.length; i++) {
          intervals.push(items[i].timestamp - items[i-1].timestamp);
        }
        
        const avgInterval = intervals.reduce((sum, int) => sum + int, 0) / intervals.length;
        const variance = intervals.reduce((sum, int) => sum + Math.pow(int - avgInterval, 2), 0) / intervals.length;
        const consistency = Math.max(0, 1 - (Math.sqrt(variance) / avgInterval));
        
        rhythmScores.push(consistency);
      }
    });
    
    return rhythmScores.length > 0 ? rhythmScores.reduce((sum, score) => sum + score, 0) / rhythmScores.length : 0.5;
  }
  
  analyzeTemporalAccuracy(sequences) {
    // Analisar precisão temporal nas respostas
    let totalAccuracy = 0;
    let count = 0;
    
    sequences.forEach(seq => {
      const accuracy = seq.accuracy || this.calculateItemAccuracy(seq.items || seq);
      totalAccuracy += accuracy;
      count++;
    });
    
    return count > 0 ? totalAccuracy / count : 0.5;
  }
  
  analyzeSynchronization(sequences) {
    // Analisar sincronização com padrões temporais esperados
    return 0.5; // Implementação simplificada
  }
  
  analyzeTimingPrecision(sequences) {
    // Analisar precisão no timing das respostas
    return 0.5; // Implementação simplificada
  }
  
  // Métodos para controle inibitório
  
  analyzeImpulseControl(sequences) {
    // Analisar controle de impulsos baseado na precisão das primeiras respostas
    let impulseScore = 0;
    let count = 0;
    
    sequences.forEach(seq => {
      const items = seq.items || seq;
      if (items.length > 0) {
        const firstItemCorrect = items[0].correct !== false;
        impulseScore += firstItemCorrect ? 1 : 0;
        count++;
      }
    });
    
    return count > 0 ? impulseScore / count : 0.5;
  }
  
  analyzeConflictResolution(sequences) {
    // Analisar resolução de conflitos em situações ambíguas
    return 0.5; // Implementação simplificada
  }
  
  analyzeResponseInhibition(sequences) {
    // Analisar capacidade de inibir respostas inadequadas
    return 0.5; // Implementação simplificada
  }
  
  // Métodos para transferência de padrões
  
  analyzeTransferSuccess(sequences, patterns) {
    // Analisar sucesso na transferência de padrões aprendidos
    if (patterns.length < 2) return 0.5;
    
    let transferScore = 0;
    let transferAttempts = 0;
    
    for (let i = 1; i < patterns.length; i++) {
      if (patterns[i].type === patterns[i-1].type) {
        transferScore += patterns[i].recognized ? 1 : 0;
        transferAttempts++;
      }
    }
    
    return transferAttempts > 0 ? transferScore / transferAttempts : 0.5;
  }
  
  analyzeGeneralization(sequences) {
    // Analisar capacidade de generalização para novos contextos
    return 0.5; // Implementação simplificada
  }
  
  analyzeAbstractionLevel(patterns) {
    // Analisar nível de abstração no reconhecimento de padrões
    const complexPatterns = patterns.filter(p => p.complexity === 'complex' || p.complexity === 'advanced');
    const simplePatterns = patterns.filter(p => p.complexity === 'simple');
    
    if (patterns.length === 0) return 0.5;
    
    const complexRatio = complexPatterns.length / patterns.length;
    const complexSuccessRate = complexPatterns.length > 0 ? 
      complexPatterns.filter(p => p.recognized).length / complexPatterns.length : 0;
    
    return (complexRatio * 0.5 + complexSuccessRate * 0.5);
  }
  
  // Métodos para capacidade sequencial
  
  calculateMaxSequenceSpan(sequences) {
    if (sequences.length === 0) return 3;
    
    const successfulSequences = sequences.filter(seq => 
      (seq.accuracy || this.calculateItemAccuracy(seq.items || seq)) >= 0.8
    );
    
    if (successfulSequences.length === 0) return 2;
    
    return Math.max(...successfulSequences.map(seq => seq.length || (seq.items && seq.items.length) || 0));
  }
  
  calculateAverageSpan(sequences) {
    if (sequences.length === 0) return 3;
    
    const totalLength = sequences.reduce((sum, seq) => 
      sum + (seq.length || (seq.items && seq.items.length) || 0), 0
    );
    
    return totalLength / sequences.length;
  }
  
  calculateSpanConsistency(sequences) {
    if (sequences.length < 2) return 0.5;
    
    const spans = sequences.map(seq => seq.length || (seq.items && seq.items.length) || 0);
    const avgSpan = spans.reduce((sum, span) => sum + span, 0) / spans.length;
    const variance = spans.reduce((sum, span) => sum + Math.pow(span - avgSpan, 2), 0) / spans.length;
    
    return Math.max(0, 1 - (Math.sqrt(variance) / avgSpan));
  }
  
  normalizeCapacity(maxSpan) {
    // Normalizar span máximo para score 0-1
    return Math.min(1, maxSpan / 8); // 8 = span máximo esperado
  }
  
  // Métodos para consistência
  
  calculateAccuracyConsistency(sequences) {
    if (sequences.length < 2) return 0.5;
    
    const accuracies = sequences.map(seq => 
      seq.accuracy || this.calculateItemAccuracy(seq.items || seq)
    );
    
    const avgAccuracy = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - avgAccuracy, 2), 0) / accuracies.length;
    
    return Math.max(0, 1 - Math.sqrt(variance));
  }
  
  calculateSpeedConsistency(sequences) {
    if (sequences.length < 2) return 0.5;
    
    const speeds = sequences.map(seq => seq.completionTime || 0).filter(speed => speed > 0);
    
    if (speeds.length < 2) return 0.5;
    
    const avgSpeed = speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;
    const variance = speeds.reduce((sum, speed) => sum + Math.pow(speed - avgSpeed, 2), 0) / speeds.length;
    
    return Math.max(0, 1 - (Math.sqrt(variance) / avgSpeed));
  }
  
  calculateStrategyConsistency(sequences) {
    // Analisar consistência nas estratégias usadas
    return 0.5; // Implementação simplificada
  }
  
  // Métodos auxiliares para cálculos específicos
  
  calculateAdaptationTime(sequences, changePosition) {
    if (changePosition >= sequences.length) return 0;
    
    const seq = sequences[changePosition];
    return seq.completionTime || 0;
  }
  
  calculateAdaptationSuccess(sequences, changePosition) {
    if (changePosition >= sequences.length) return 0;
    
    const seq = sequences[changePosition];
    return seq.accuracy || this.calculateItemAccuracy(seq.items || seq);
  }
  
  // Métodos de categorização e geração de recomendações
  
  categorizeLevel(score) {
    if (score >= 0.9) return 'excellent';
    if (score >= 0.75) return 'good';
    if (score >= 0.60) return 'average';
    if (score >= 0.40) return 'poor';
    return 'critical';
  }
  
  categorizeCapacityLevel(span) {
    if (span >= 7) return 'excellent';
    if (span >= 5) return 'good';
    if (span >= 3) return 'average';
    if (span >= 2) return 'poor';
    return 'critical';
  }
  
  getDefaultAnalysis(type) {
    return {
      score: 0.5,
      level: 'average',
      confidence: 0.3,
      recommendations: [`Dados insuficientes para análise de ${type}`]
    };
  }
  
  // Geradores de recomendações
  
  generatePatternRecommendations(recognitionRate, complexityHandling) {
    const recommendations = [];
    
    if (recognitionRate < 0.6) {
      recommendations.push('Praticar reconhecimento de padrões simples');
      recommendations.push('Exercícios com sequências curtas e regulares');
    }
    
    if (complexityHandling < 0.5) {
      recommendations.push('Desenvolvimento gradual de padrões complexos');
      recommendations.push('Usar pistas visuais para auxiliar reconhecimento');
    }
    
    return recommendations.length > 0 ? recommendations : ['Manter prática atual de padrões'];
  }
  
  generatePredictionRecommendations(accuracy, lookAhead) {
    const recommendations = [];
    
    if (accuracy < 0.6) {
      recommendations.push('Exercitar predição de próximo elemento');
      recommendations.push('Praticar com sequências previsíveis');
    }
    
    if (lookAhead < 2) {
      recommendations.push('Desenvolver capacidade de antecipação');
      recommendations.push('Exercícios de predição múltipla');
    }
    
    return recommendations.length > 0 ? recommendations : ['Desenvolver estratégias preditivas'];
  }
  
  generateFlexibilityRecommendations(flexibilityScore, perseverationCount) {
    const recommendations = [];
    
    if (flexibilityScore < 0.6) {
      recommendations.push('Exercícios de mudança de regras');
      recommendations.push('Praticar adaptação a novos padrões');
    }
    
    if (perseverationCount > 2) {
      recommendations.push('Treinar inibição de respostas anteriores');
      recommendations.push('Exercícios de atenção alternada');
    }
    
    return recommendations.length > 0 ? recommendations : ['Manter flexibilidade cognitiva'];
  }
  
  generateTemporalRecommendations(rhythm, accuracy) {
    const recommendations = [];
    
    if (rhythm < 0.6) {
      recommendations.push('Exercícios de ritmo e temporização');
      recommendations.push('Praticar sincronização temporal');
    }
    
    if (accuracy < 0.6) {
      recommendations.push('Melhorar precisão temporal');
      recommendations.push('Exercícios com feedback temporal');
    }
    
    return recommendations.length > 0 ? recommendations : ['Desenvolver processamento temporal'];
  }
  
  generateInhibitoryRecommendations(impulse, conflict) {
    const recommendations = [];
    
    if (impulse < 0.6) {
      recommendations.push('Exercícios de controle inibitório');
      recommendations.push('Praticar pausa antes de responder');
    }
    
    if (conflict < 0.6) {
      recommendations.push('Treinar resolução de conflitos');
      recommendations.push('Exercícios de atenção seletiva');
    }
    
    return recommendations.length > 0 ? recommendations : ['Desenvolver autocontrole'];
  }
  
  generateTransferRecommendations(transfer, generalization) {
    const recommendations = [];
    
    if (transfer < 0.6) {
      recommendations.push('Praticar aplicação de padrões em novos contextos');
      recommendations.push('Exercícios de transferência de aprendizagem');
    }
    
    if (generalization < 0.6) {
      recommendations.push('Desenvolver capacidade de generalização');
      recommendations.push('Explorar variações de padrões conhecidos');
    }
    
    return recommendations.length > 0 ? recommendations : ['Ampliar transferência de conhecimento'];
  }
  
  generateCapacityRecommendations(maxSpan, consistency) {
    const recommendations = [];
    
    if (maxSpan < 4) {
      recommendations.push('Exercícios para expandir span sequencial');
      recommendations.push('Praticar com sequências progressivamente maiores');
    }
    
    if (consistency < 0.6) {
      recommendations.push('Melhorar estabilidade na performance');
      recommendations.push('Prática regular para maior consistência');
    }
    
    return recommendations.length > 0 ? recommendations : ['Manter capacidade sequencial'];
  }
  
  generateConsistencyRecommendations(accuracy, speed) {
    const recommendations = [];
    
    if (accuracy < 0.6) {
      recommendations.push('Focar na estabilidade da precisão');
      recommendations.push('Exercícios de autorregulação');
    }
    
    if (speed < 0.6) {
      recommendations.push('Melhorar consistência temporal');
      recommendations.push('Treinar ritmo de trabalho estável');
    }
    
    return recommendations.length > 0 ? recommendations : ['Manter consistência atual'];
  }
  
  // Geração de perfil sequencial
  generateSequentialProfile(sequentialData) {
    const { sequences, patterns } = sequentialData;
    
    const overallAccuracy = sequences.length > 0 ? 
      sequences.reduce((sum, seq) => sum + (seq.accuracy || 0), 0) / sequences.length : 0.5;
    
    const patternRecognitionRate = patterns.length > 0 ?
      patterns.filter(p => p.recognized).length / patterns.length : 0.5;
    
    const avgSpan = this.calculateAverageSpan(sequences);
    const maxSpan = this.calculateMaxSequenceSpan(sequences);
    
    let profileType = 'developing';
    if (overallAccuracy >= 0.8 && patternRecognitionRate >= 0.8 && maxSpan >= 6) {
      profileType = 'advanced';
    } else if (overallAccuracy >= 0.7 && patternRecognitionRate >= 0.7 && maxSpan >= 4) {
      profileType = 'proficient';
    } else if (overallAccuracy >= 0.5 && patternRecognitionRate >= 0.5 && maxSpan >= 3) {
      profileType = 'typical';
    } else if (overallAccuracy < 0.4 || patternRecognitionRate < 0.4 || maxSpan < 2) {
      profileType = 'challenged';
    }
    
    return {
      profileType,
      overallAccuracy,
      patternRecognitionRate,
      avgSpan,
      maxSpan,
      strengths: this.identifySequentialStrengths(sequentialData),
      challenges: this.identifySequentialChallenges(sequentialData),
      recommendations: this.generateOverallRecommendations(profileType, sequentialData)
    };
  }
  
  identifySequentialStrengths(sequentialData) {
    const strengths = [];
    
    const analysis = this.analyzePatternRecognition(sequentialData);
    if (analysis.recognitionRate >= 0.7) {
      strengths.push('Bom reconhecimento de padrões');
    }
    
    const prediction = this.analyzeSequentialPrediction(sequentialData);
    if (prediction.accuracy >= 0.7) {
      strengths.push('Boa capacidade preditiva');
    }
    
    const flexibility = this.analyzeRuleFlexibility(sequentialData);
    if (flexibility.flexibilityScore >= 0.7) {
      strengths.push('Boa flexibilidade cognitiva');
    }
    
    return strengths.length > 0 ? strengths : ['Potencial para desenvolvimento'];
  }
  
  identifySequentialChallenges(sequentialData) {
    const challenges = [];
    
    const analysis = this.analyzePatternRecognition(sequentialData);
    if (analysis.recognitionRate < 0.5) {
      challenges.push('Dificuldade no reconhecimento de padrões');
    }
    
    const flexibility = this.analyzeRuleFlexibility(sequentialData);
    if (flexibility.flexibilityScore < 0.5) {
      challenges.push('Rigidez cognitiva');
    }
    
    const capacity = this.analyzeSequenceCapacity(sequentialData);
    if (capacity.maxSpan < 3) {
      challenges.push('Span sequencial limitado');
    }
    
    return challenges.length > 0 ? challenges : ['Áreas para aprimoramento'];
  }
  
  generateOverallRecommendations(profileType, sequentialData) {
    const recommendations = [];
    
    switch (profileType) {
      case 'challenged':
        recommendations.push('Começar com sequências muito simples');
        recommendations.push('Usar suporte visual constante');
        recommendations.push('Sessões curtas e frequentes');
        break;
        
      case 'developing':
        recommendations.push('Praticar padrões básicos regularmente');
        recommendations.push('Introduzir complexidade gradualmente');
        recommendations.push('Reforçar sucessos positivos');
        break;
        
      case 'typical':
        recommendations.push('Expandir para padrões mais complexos');
        recommendations.push('Desenvolver estratégias metacognitivas');
        recommendations.push('Praticar transferência entre contextos');
        break;
        
      case 'proficient':
        recommendations.push('Explorar padrões hierárquicos');
        recommendations.push('Desenvolver criação de padrões próprios');
        recommendations.push('Aplicar em problemas reais');
        break;
        
      case 'advanced':
        recommendations.push('Mentoria para outros aprendizes');
        recommendations.push('Explorar padrões abstratos complexos');
        recommendations.push('Pesquisa e criação independente');
        break;
        
      default:
        recommendations.push('Avaliação mais detalhada necessária');
    }
    
    return recommendations;
  }
  
  // Atualização de métricas
  updateSequenceMetrics() {
    if (this.sequences.length > 0) {
      const avgAccuracy = this.sequences.reduce((sum, seq) => sum + seq.accuracy, 0) / this.sequences.length;
      this.metrics.sequenceSpan = this.calculateMaxSequenceSpan(this.sequences);
      this.metrics.consistency = this.calculateAccuracyConsistency(this.sequences);
    }
  }
  
  updatePatternRecognition() {
    if (this.patterns.length > 0) {
      const recognitionRate = this.patterns.filter(p => p.recognized).length / this.patterns.length;
      this.metrics.patternRecognition = recognitionRate;
    }
  }
  
  updateSequentialPrediction() {
    if (this.predictions.length > 0) {
      const accuracy = this.predictions.filter(p => p.correct).length / this.predictions.length;
      this.metrics.sequentialPrediction = accuracy;
    }
  }
  
  updateRuleFlexibility() {
    if (this.rules.length > 0) {
      const avgFlexibility = this.rules.reduce((sum, rule) => sum + rule.flexibilityScore, 0) / this.rules.length;
      this.metrics.ruleFlexibility = avgFlexibility;
    }
  }
  
  updateTemporalProcessing() {
    if (this.transitions.length > 0) {
      const smoothTransitions = this.transitions.filter(t => t.smooth).length / this.transitions.length;
      this.metrics.temporalProcessing = smoothTransitions;
    }
  }
  
  // Análise de fallback
  generateFallbackAnalysis(errorMessage) {
    return {
      patternRecognition: this.getDefaultAnalysis('patternRecognition'),
      sequentialPrediction: this.getDefaultAnalysis('sequentialPrediction'),
      ruleFlexibility: this.getDefaultAnalysis('ruleFlexibility'),
      temporalProcessing: this.getDefaultAnalysis('temporalProcessing'),
      inhibitoryControl: this.getDefaultAnalysis('inhibitoryControl'),
      patternTransfer: this.getDefaultAnalysis('patternTransfer'),
      sequenceCapacity: { maxSpan: 3, avgSpan: 3, level: 'average' },
      consistency: this.getDefaultAnalysis('consistency'),
      overallProfile: {
        profileType: 'unknown',
        recommendations: ['Coletar mais dados para análise sequencial precisa']
      },
      error: true,
      errorMessage
    };
  }
  
  // Exportação de dados
  exportData() {
    return {
      collectorId: this.collectorId,
      version: this.version,
      timestamp: Date.now(),
      sessionDuration: Date.now() - this.startTime,
      data: {
        sequences: this.sequences,
        patterns: this.patterns,
        predictions: this.predictions,
        rules: this.rules,
        transitions: this.transitions,
        temporalData: this.temporalData
      },
      metrics: { ...this.metrics },
      summary: this.generateSummary()
    };
  }
  
  generateSummary() {
    return {
      totalSequences: this.sequences.length,
      averageAccuracy: this.sequences.length > 0 ? 
        this.sequences.reduce((sum, seq) => sum + seq.accuracy, 0) / this.sequences.length : 0,
      patternRecognitionRate: this.patterns.length > 0 ?
        this.patterns.filter(p => p.recognized).length / this.patterns.length : 0,
      predictionAccuracy: this.predictions.length > 0 ?
        this.predictions.filter(p => p.correct).length / this.predictions.length : 0,
      maxSequenceSpan: this.calculateMaxSequenceSpan(this.sequences),
      overallSequentialProfile: this.classifySequentialProfile()
    };
  }
  
  classifySequentialProfile() {
    const avgAccuracy = this.sequences.length > 0 ? 
      this.sequences.reduce((sum, seq) => sum + seq.accuracy, 0) / this.sequences.length : 0.5;
    const patternRate = this.patterns.length > 0 ?
      this.patterns.filter(p => p.recognized).length / this.patterns.length : 0.5;
    const maxSpan = this.calculateMaxSequenceSpan(this.sequences);
    
    if (avgAccuracy >= 0.8 && patternRate >= 0.8 && maxSpan >= 6) {
      return 'excellent_sequential_abilities';
    } else if (avgAccuracy >= 0.7 && patternRate >= 0.7 && maxSpan >= 4) {
      return 'good_sequential_abilities';
    } else if (avgAccuracy >= 0.5 && patternRate >= 0.5 && maxSpan >= 3) {
      return 'average_sequential_abilities';
    } else {
      return 'developing_sequential_abilities';
    }
  }
}

export default SequentialColorCollector;

export class SequentialColorCollector {
  constructor() {
    this.sequenceTypes = {
      linear: 'Sequência Linear',
      circular: 'Sequência Circular',
      alternating: 'Sequência Alternada',
      gradient: 'Sequência Gradiente',
      pattern: 'Sequência com Padrão',
      random: 'Sequência Aleatória'
    };
    
    this.sequenceComplexity = {
      simple: { length: 2, patterns: 1, description: 'Sequência simples' },
      moderate: { length: 3, patterns: 2, description: 'Sequência moderada' },
      complex: { length: 4, patterns: 3, description: 'Sequência complexa' },
      advanced: { length: 5, patterns: 4, description: 'Sequência avançada' },
      expert: { length: 6, patterns: 5, description: 'Sequência especializada' }
    };
    
    this.patternTypes = {
      repetition: 'Repetição (A-A-A)',
      alternation: 'Alternância (A-B-A-B)',
      progression: 'Progressão (A-B-C)',
      symmetry: 'Simetria (A-B-B-A)',
      spiral: 'Espiral cromática',
      complement: 'Cores complementares'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Análise principal de sequências cromáticas
   */
  async analyze(data) {
    try {
      if (!this.validateSequenceData(data)) {
        return this.generateFallbackAnalysis('Dados de sequência insuficientes ou inválidos');
      }

      const sequenceData = this.extractSequenceData(data);

      return {
        sequenceRecognition: this.analyzeSequenceRecognition(sequenceData),
        patternCompletion: this.analyzePatternCompletion(sequenceData),
        sequentialMemory: this.analyzeSequentialMemory(sequenceData),
        colorProgression: this.analyzeColorProgression(sequenceData),
        rhythmicPatterns: this.analyzeRhythmicPatterns(sequenceData),
        sequenceComplexity: this.assessSequenceComplexity(sequenceData),
        predictionAccuracy: this.analyzePredictionAccuracy(sequenceData),
        sequentialProcessing: this.analyzeSequentialProcessing(sequenceData),
        orderSensitivity: this.analyzeOrderSensitivity(sequenceData),
        temporalSequencing: this.analyzeTemporalSequencing(sequenceData)
      };
    } catch (error) {
      console.error('Erro na análise de sequências cromáticas:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de sequência de entrada
   */
  validateSequenceData(data) {
    if (!data) return false;
    
    // Verificar se tem dados de sequência
    const hasSequenceData = data.sequences || data.colorSequences || data.patterns;
    const hasInteractions = data.interactions || data.responses || data.selectedItems;
    
    return hasSequenceData || hasInteractions;
  }

  /**
   * Extrai dados de sequência dos dados de entrada
   */
  extractSequenceData(data) {
    // Extrair sequências de cores
    let colorSequences = [];
    if (data.sequences) {
      colorSequences = data.sequences;
    } else if (data.colorSequences) {
      colorSequences = data.colorSequences;
    } else if (data.interactions) {
      // Reconstruir sequências a partir das interações
      colorSequences = this.reconstructSequencesFromInteractions(data.interactions);
    }

    // Extrair tentativas de completar padrões
    let patternCompletions = [];
    if (data.patternCompletions) {
      patternCompletions = data.patternCompletions;
    } else if (data.responses) {
      patternCompletions = data.responses.filter(r => r.type === 'pattern' || r.type === 'completion');
    }

    // Extrair dados temporais
    const temporalData = {
      sequenceTiming: data.sequenceTiming || [],
      responseIntervals: data.responseIntervals || [],
      sessionDuration: data.sessionDuration || Date.now() - (data.startTime || Date.now())
    };

    return {
      colorSequences,
      patternCompletions,
      temporalData,
      difficulty: data.difficulty || 'medium',
      interactionHistory: data.interactions || []
    };
  }

  /**
   * Reconstrói sequências a partir das interações
   */
  reconstructSequencesFromInteractions(interactions) {
    const sequences = [];
    let currentSequence = [];
    
    interactions.forEach(interaction => {
      if (interaction.type === 'sequence_start' || interaction.isSequenceStart) {
        if (currentSequence.length > 0) {
          sequences.push([...currentSequence]);
        }
        currentSequence = [];
      }
      
      if (interaction.color || interaction.targetColor) {
        currentSequence.push({
          color: interaction.color || interaction.targetColor,
          position: interaction.position || currentSequence.length,
          timestamp: interaction.timestamp || Date.now(),
          correct: interaction.correct !== false,
          userInput: interaction.userInput || false
        });
      }
    });
    
    if (currentSequence.length > 0) {
      sequences.push(currentSequence);
    }
    
    return sequences;
  }

  /**
   * Analisa reconhecimento de sequência
   */
  analyzeSequenceRecognition(sequenceData) {
    const { colorSequences } = sequenceData;
    
    if (colorSequences.length === 0) {
      return this.getDefaultSequenceScore('recognition');
    }

    const recognitionAccuracy = this.calculateRecognitionAccuracy(colorSequences);
    const recognitionSpeed = this.calculateRecognitionSpeed(colorSequences);
    const patternDetection = this.calculatePatternDetection(colorSequences);

    return {
      accuracy: recognitionAccuracy,
      speed: recognitionSpeed,
      patternDetection,
      overallScore: (recognitionAccuracy * 0.4 + recognitionSpeed * 0.3 + patternDetection * 0.3),
      level: this.categorizeSequenceLevel(recognitionAccuracy),
      recommendations: this.generateRecognitionRecommendations(recognitionAccuracy, patternDetection)
    };
  }

  /**
   * Analisa completar padrões
   */
  analyzePatternCompletion(sequenceData) {
    const { patternCompletions, colorSequences } = sequenceData;
    
    // Se não há dados específicos de completar padrões, estimar das sequências
    const completionData = patternCompletions.length > 0 ? patternCompletions : 
                          this.extractCompletionDataFromSequences(colorSequences);

    if (completionData.length === 0) {
      return this.getDefaultSequenceScore('completion');
    }

    const completionAccuracy = this.calculateCompletionAccuracy(completionData);
    const logicalReasoning = this.assessLogicalReasoning(completionData);
    const predictiveAbility = this.assessPredictiveAbility(completionData);

    return {
      accuracy: completionAccuracy,
      logicalReasoning,
      predictiveAbility,
      overallScore: (completionAccuracy * 0.4 + logicalReasoning * 0.3 + predictiveAbility * 0.3),
      level: this.categorizeSequenceLevel(completionAccuracy),
      recommendations: this.generateCompletionRecommendations(completionAccuracy, logicalReasoning)
    };
  }

  /**
   * Analisa memória sequencial
   */
  analyzeSequentialMemory(sequenceData) {
    const { colorSequences } = sequenceData;
    
    const orderAccuracy = this.calculateOrderAccuracy(colorSequences);
    const sequenceRecall = this.calculateSequenceRecall(colorSequences);
    const serialPosition = this.analyzeSerialPositionEffect(colorSequences);

    return {
      orderAccuracy,
      sequenceRecall,
      serialPosition,
      memorySpan: this.calculateSequentialMemorySpan(colorSequences),
      overallScore: (orderAccuracy * 0.4 + sequenceRecall * 0.4 + this.calculateSerialPositionScore(serialPosition) * 0.2),
      level: this.categorizeSequenceLevel(orderAccuracy),
      recommendations: this.generateMemoryRecommendations(orderAccuracy, sequenceRecall)
    };
  }

  /**
   * Analisa progressão de cores
   */
  analyzeColorProgression(sequenceData) {
    const { colorSequences } = sequenceData;
    
    const progressionRecognition = this.calculateProgressionRecognition(colorSequences);
    const gradientUnderstanding = this.calculateGradientUnderstanding(colorSequences);
    const chromaticLogic = this.calculateChromaticLogic(colorSequences);

    return {
      progressionRecognition,
      gradientUnderstanding,
      chromaticLogic,
      colorTheoryApplication: this.assessColorTheoryApplication(colorSequences),
      overallScore: (progressionRecognition * 0.3 + gradientUnderstanding * 0.3 + chromaticLogic * 0.4),
      level: this.categorizeSequenceLevel(progressionRecognition),
      recommendations: this.generateProgressionRecommendations(progressionRecognition, chromaticLogic)
    };
  }

  /**
   * Analisa padrões rítmicos
   */
  analyzeRhythmicPatterns(sequenceData) {
    const { colorSequences, temporalData } = sequenceData;
    
    const rhythmRecognition = this.calculateRhythmRecognition(colorSequences, temporalData);
    const temporalConsistency = this.calculateTemporalConsistency(temporalData);
    const beatingPatterns = this.identifyBeatingPatterns(colorSequences);

    return {
      rhythmRecognition,
      temporalConsistency,
      beatingPatterns,
      rhythmicMemory: this.calculateRhythmicMemory(colorSequences, temporalData),
      overallScore: (rhythmRecognition * 0.4 + temporalConsistency * 0.3 + beatingPatterns * 0.3),
      level: this.categorizeSequenceLevel(rhythmRecognition),
      recommendations: this.generateRhythmicRecommendations(rhythmRecognition, temporalConsistency)
    };
  }

  /**
   * Avalia complexidade da sequência
   */
  assessSequenceComplexity(sequenceData) {
    const { colorSequences } = sequenceData;
    
    const complexityTolerance = this.calculateComplexityTolerance(colorSequences);
    const adaptivePerformance = this.calculateAdaptivePerformance(colorSequences);
    const complexityPreference = this.identifyComplexityPreference(colorSequences);

    return {
      tolerance: complexityTolerance,
      adaptivePerformance,
      preference: complexityPreference,
      optimalComplexity: this.findOptimalComplexity(colorSequences),
      overallScore: (complexityTolerance * 0.5 + adaptivePerformance * 0.5),
      level: this.categorizeSequenceLevel(complexityTolerance),
      recommendations: this.generateComplexityRecommendations(complexityTolerance, complexityPreference)
    };
  }

  /**
   * Analisa precisão de predição
   */
  analyzePredictionAccuracy(sequenceData) {
    const { colorSequences, patternCompletions } = sequenceData;
    
    const predictionSuccess = this.calculatePredictionSuccess(colorSequences, patternCompletions);
    const anticipationAbility = this.calculateAnticipationAbility(colorSequences);
    const errorPrediction = this.analyzeErrorPrediction(colorSequences);

    return {
      predictionSuccess,
      anticipationAbility,
      errorPrediction,
      confidenceLevel: this.calculatePredictionConfidence(colorSequences),
      overallScore: (predictionSuccess * 0.4 + anticipationAbility * 0.3 + (1 - errorPrediction) * 0.3),
      level: this.categorizeSequenceLevel(predictionSuccess),
      recommendations: this.generatePredictionRecommendations(predictionSuccess, anticipationAbility)
    };
  }

  /**
   * Analisa processamento sequencial
   */
  analyzeSequentialProcessing(sequenceData) {
    const { colorSequences, temporalData } = sequenceData;
    
    const processingSpeed = this.calculateSequentialProcessingSpeed(colorSequences, temporalData);
    const processingAccuracy = this.calculateSequentialProcessingAccuracy(colorSequences);
    const processingEfficiency = this.calculateProcessingEfficiency(processingSpeed, processingAccuracy);

    return {
      speed: processingSpeed,
      accuracy: processingAccuracy,
      efficiency: processingEfficiency,
      bottlenecks: this.identifyProcessingBottlenecks(colorSequences, temporalData),
      overallScore: (processingSpeed * 0.3 + processingAccuracy * 0.4 + processingEfficiency * 0.3),
      level: this.categorizeSequenceLevel(processingAccuracy),
      recommendations: this.generateProcessingRecommendations(processingSpeed, processingAccuracy)
    };
  }

  /**
   * Analisa sensibilidade à ordem
   */
  analyzeOrderSensitivity(sequenceData) {
    const { colorSequences } = sequenceData;
    
    const orderImportance = this.calculateOrderImportance(colorSequences);
    const positionSensitivity = this.calculatePositionSensitivity(colorSequences);
    const reversalDetection = this.calculateReversalDetection(colorSequences);

    return {
      orderImportance,
      positionSensitivity,
      reversalDetection,
      sequentialDependency: this.calculateSequentialDependency(colorSequences),
      overallScore: (orderImportance * 0.3 + positionSensitivity * 0.3 + reversalDetection * 0.4),
      level: this.categorizeSequenceLevel(orderImportance),
      recommendations: this.generateOrderRecommendations(orderImportance, positionSensitivity)
    };
  }

  /**
   * Analisa sequenciamento temporal
   */
  analyzeTemporalSequencing(sequenceData) {
    const { colorSequences, temporalData } = sequenceData;
    
    const temporalAccuracy = this.calculateTemporalAccuracy(colorSequences, temporalData);
    const timingConsistency = this.calculateTimingConsistency(temporalData);
    const temporalAnticipation = this.calculateTemporalAnticipation(colorSequences, temporalData);

    return {
      temporalAccuracy,
      timingConsistency,
      temporalAnticipation,
      rhythmicAlignment: this.calculateRhythmicAlignment(colorSequences, temporalData),
      overallScore: (temporalAccuracy * 0.4 + timingConsistency * 0.3 + temporalAnticipation * 0.3),
      level: this.categorizeSequenceLevel(temporalAccuracy),
      recommendations: this.generateTemporalRecommendations(temporalAccuracy, timingConsistency)
    };
  }

  // Métodos auxiliares para cálculos específicos...

  /**
   * Calcula precisão de reconhecimento
   */
  calculateRecognitionAccuracy(sequences) {
    if (sequences.length === 0) return 0.5;
    
    let totalRecognitionScore = 0;
    let recognitionTasks = 0;
    
    sequences.forEach(seq => {
      if (seq.length >= 2) {
        const pattern = this.identifyPattern(seq);
        if (pattern !== 'unknown') {
          const recognitionScore = this.calculatePatternRecognitionScore(seq, pattern);
          totalRecognitionScore += recognitionScore;
          recognitionTasks++;
        }
      }
    });
    
    return recognitionTasks > 0 ? totalRecognitionScore / recognitionTasks : 0.5;
  }

  /**
   * Calcula velocidade de reconhecimento
   */
  calculateRecognitionSpeed(sequences) {
    const recognitionTimes = [];
    
    sequences.forEach(seq => {
      if (seq.length >= 2) {
        const firstRecognitionTime = this.getFirstRecognitionTime(seq);
        if (firstRecognitionTime > 0) {
          recognitionTimes.push(firstRecognitionTime);
        }
      }
    });
    
    if (recognitionTimes.length === 0) return 0.5;
    
    const avgRecognitionTime = recognitionTimes.reduce((sum, time) => sum + time, 0) / recognitionTimes.length;
    
    // Normalizar: 1000ms = 1.0, 500ms = 0.8, 2000ms = 0.2
    return Math.max(0.1, Math.min(1.0, 1.5 - (avgRecognitionTime / 1000)));
  }

  /**
   * Calcula detecção de padrões
   */
  calculatePatternDetection(sequences) {
    let patternsDetected = 0;
    let patternTasks = 0;
    
    sequences.forEach(seq => {
      if (seq.length >= 3) { // Padrões precisam de pelo menos 3 elementos
        const pattern = this.identifyPattern(seq);
        patternTasks++;
        if (pattern !== 'unknown' && pattern !== 'random') {
          patternsDetected++;
        }
      }
    });
    
    return patternTasks > 0 ? patternsDetected / patternTasks : 0.5;
  }

  /**
   * Identifica padrão em uma sequência
   */
  identifyPattern(sequence) {
    if (sequence.length < 2) return 'unknown';
    
    const colors = sequence.map(item => item.color);
    
    // Verificar repetição
    if (this.isRepetitionPattern(colors)) return 'repetition';
    
    // Verificar alternância
    if (this.isAlternationPattern(colors)) return 'alternation';
    
    // Verificar progressão
    if (this.isProgressionPattern(colors)) return 'progression';
    
    // Verificar simetria
    if (this.isSymmetryPattern(colors)) return 'symmetry';
    
    // Verificar gradiente
    if (this.isGradientPattern(colors)) return 'gradient';
    
    return 'unknown';
  }

  /**
   * Verifica padrão de repetição
   */
  isRepetitionPattern(colors) {
    if (colors.length < 2) return false;
    const firstColor = colors[0];
    return colors.slice(1, 3).every(color => color === firstColor);
  }

  /**
   * Verifica padrão de alternância
   */
  isAlternationPattern(colors) {
    if (colors.length < 4) return false;
    return colors[0] === colors[2] && colors[1] === colors[3] && colors[0] !== colors[1];
  }

  /**
   * Verifica padrão de progressão
   */
  isProgressionPattern(colors) {
    if (colors.length < 3) return false;
    
    // Verificar se há uma progressão lógica de cores
    const colorOrder = this.getColorOrder();
    const indices = colors.map(color => colorOrder.indexOf(color));
    
    if (indices.includes(-1)) return false; // Cor não encontrada na ordem
    
    // Verificar se há incremento consistente
    for (let i = 1; i < indices.length; i++) {
      const diff = indices[i] - indices[i-1];
      if (i === 1) {
        var expectedDiff = diff;
      } else if (diff !== expectedDiff) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Verifica padrão de simetria
   */
  isSymmetryPattern(colors) {
    if (colors.length < 3) return false;
    
    // Verificar se a sequência é simétrica
    const middle = Math.floor(colors.length / 2);
    const firstHalf = colors.slice(0, middle);
    const secondHalf = colors.slice(colors.length - middle).reverse();
    
    return firstHalf.every((color, index) => color === secondHalf[index]);
  }

  /**
   * Verifica padrão de gradiente
   */
  isGradientPattern(colors) {
    if (colors.length < 3) return false;
    
    // Verificar se há uma transição gradual entre cores
    for (let i = 1; i < colors.length; i++) {
      if (!this.areColorsRelated(colors[i-1], colors[i])) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Obtém ordem de cores para progressão
   */
  getColorOrder() {
    return [
      '#ff0000', // vermelho
      '#ff8000', // laranja
      '#ffff00', // amarelo
      '#80ff00', // amarelo-verde
      '#00ff00', // verde
      '#00ff80', // verde-azul
      '#00ffff', // ciano
      '#0080ff', // azul-ciano
      '#0000ff', // azul
      '#8000ff', // azul-roxo
      '#ff00ff', // magenta
      '#ff0080'  // magenta-vermelho
    ];
  }

  /**
   * Verifica se duas cores são relacionadas
   */
  areColorsRelated(color1, color2) {
    // Implementação simplificada: cores são relacionadas se estão próximas no espectro
    const order = this.getColorOrder();
    const index1 = order.indexOf(color1);
    const index2 = order.indexOf(color2);
    
    if (index1 === -1 || index2 === -1) return false;
    
    const distance = Math.abs(index1 - index2);
    return distance <= 2 || distance >= order.length - 2; // Próximas ou opostas no círculo
  }

  /**
   * Calcula score de reconhecimento de padrão
   */
  calculatePatternRecognitionScore(sequence, pattern) {
    // Score baseado na complexidade do padrão e na precisão da identificação
    const complexityBonus = {
      'repetition': 0.6,
      'alternation': 0.7,
      'progression': 0.8,
      'symmetry': 0.9,
      'gradient': 0.85
    };
    
    const baseScore = sequence.filter(item => item.correct !== false).length / sequence.length;
    const patternBonus = complexityBonus[pattern] || 0.5;
    
    return baseScore * patternBonus;
  }

  /**
   * Obtém tempo de primeiro reconhecimento
   */
  getFirstRecognitionTime(sequence) {
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i].timestamp && sequence[i-1].timestamp) {
        return sequence[i].timestamp - sequence[i-1].timestamp;
      }
    }
    return 0;
  }

  /**
   * Extrai dados de completar padrões das sequências
   */
  extractCompletionDataFromSequences(sequences) {
    return sequences.filter(seq => 
      seq.some(item => item.userInput === true) // Sequências onde usuário forneceu input
    ).map(seq => ({
      pattern: this.identifyPattern(seq),
      accuracy: this.calculateSequenceAccuracy(seq),
      complexity: this.calculateSequenceComplexity(seq)
    }));
  }

  /**
   * Calcula precisão de completar padrões
   */
  calculateCompletionAccuracy(completionData) {
    if (completionData.length === 0) return 0.5;
    
    const totalAccuracy = completionData.reduce((sum, completion) => sum + completion.accuracy, 0);
    return totalAccuracy / completionData.length;
  }

  /**
   * Avalia raciocínio lógico
   */
  assessLogicalReasoning(completionData) {
    if (completionData.length === 0) return 0.5;
    
    // Avaliar baseado na complexidade dos padrões completados corretamente
    const logicalTasks = completionData.filter(completion => 
      completion.pattern !== 'unknown' && completion.accuracy > 0.7
    );
    
    return logicalTasks.length / completionData.length;
  }

  /**
   * Avalia habilidade preditiva
   */
  assessPredictiveAbility(completionData) {
    if (completionData.length === 0) return 0.5;
    
    // Avaliar baseado na capacidade de predizer o próximo elemento
    const predictiveTasks = completionData.filter(completion => 
      completion.pattern === 'progression' || completion.pattern === 'gradient'
    );
    
    if (predictiveTasks.length === 0) return 0.4; // Penalizar falta de tarefas preditivas
    
    const avgAccuracy = predictiveTasks.reduce((sum, task) => sum + task.accuracy, 0) / predictiveTasks.length;
    return avgAccuracy;
  }

  /**
   * Calcula precisão de ordem
   */
  calculateOrderAccuracy(sequences) {
    if (sequences.length === 0) return 0.5;
    
    let totalOrderScore = 0;
    let orderTasks = 0;
    
    sequences.forEach(seq => {
      if (seq.length >= 2) {
        const orderScore = this.calculateSequenceOrderScore(seq);
        totalOrderScore += orderScore;
        orderTasks++;
      }
    });
    
    return orderTasks > 0 ? totalOrderScore / orderTasks : 0.5;
  }

  /**
   * Calcula recall de sequência
   */
  calculateSequenceRecall(sequences) {
    return this.calculateSequenceAccuracy(sequences);
  }

  /**
   * Analisa efeito de posição serial
   */
  analyzeSerialPositionEffect(sequences) {
    const positionScores = {};
    
    sequences.forEach(seq => {
      seq.forEach((item, index) => {
        if (!positionScores[index]) positionScores[index] = [];
        positionScores[index].push(item.correct !== false ? 1 : 0);
      });
    });
    
    const result = {};
    Object.keys(positionScores).forEach(position => {
      const scores = positionScores[position];
      result[position] = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    });
    
    return result;
  }

  /**
   * Calcula span de memória sequencial
   */
  calculateSequentialMemorySpan(sequences) {
    const correctSequences = sequences.filter(seq => this.calculateSequenceAccuracy(seq) >= 0.8);
    
    if (correctSequences.length === 0) return 3; // Span padrão
    
    return Math.max(...correctSequences.map(seq => seq.length));
  }

  /**
   * Calcula score de posição serial
   */
  calculateSerialPositionScore(serialPosition) {
    const positions = Object.values(serialPosition);
    if (positions.length === 0) return 0.5;
    
    return positions.reduce((sum, score) => sum + score, 0) / positions.length;
  }

  /**
   * Calcula precisão de sequência
   */
  calculateSequenceAccuracy(sequence) {
    if (!sequence || sequence.length === 0) return 0;
    
    const correctItems = sequence.filter(item => item.correct !== false).length;
    return correctItems / sequence.length;
  }

  /**
   * Calcula score de ordem de sequência
   */
  calculateSequenceOrderScore(sequence) {
    let correctOrder = 0;
    
    for (let i = 0; i < sequence.length; i++) {
      if (sequence[i].position === i || sequence[i].order === i) {
        correctOrder++;
      }
    }
    
    return sequence.length > 0 ? correctOrder / sequence.length : 0;
  }

  /**
   * Calcula complexidade de sequência
   */
  calculateSequenceComplexity(sequence) {
    let complexity = sequence.length * 0.2; // Base: comprimento
    
    const pattern = this.identifyPattern(sequence);
    const patternComplexity = {
      'repetition': 0.2,
      'alternation': 0.4,
      'progression': 0.6,
      'symmetry': 0.8,
      'gradient': 0.7,
      'unknown': 0.1
    };
    
    complexity += patternComplexity[pattern] || 0.1;
    
    return Math.min(complexity, 1.0);
  }

  // Métodos adicionais para análises específicas...

  /**
   * Calcula reconhecimento de progressão
   */
  calculateProgressionRecognition(sequences) {
    const progressionSequences = sequences.filter(seq => 
      this.identifyPattern(seq) === 'progression'
    );
    
    if (progressionSequences.length === 0) return 0.4;
    
    return this.calculateSequenceAccuracy(progressionSequences);
  }

  /**
   * Calcula compreensão de gradiente
   */
  calculateGradientUnderstanding(sequences) {
    const gradientSequences = sequences.filter(seq => 
      this.identifyPattern(seq) === 'gradient'
    );
    
    if (gradientSequences.length === 0) return 0.4;
    
    return this.calculateSequenceAccuracy(gradientSequences);
  }

  /**
   * Calcula lógica cromática
   */
  calculateChromaticLogic(sequences) {
    let logicScore = 0;
    let logicTasks = 0;
    
    sequences.forEach(seq => {
      const pattern = this.identifyPattern(seq);
      if (pattern === 'progression' || pattern === 'gradient') {
        const accuracy = this.calculateSequenceAccuracy(seq);
        logicScore += accuracy;
        logicTasks++;
      }
    });
    
    return logicTasks > 0 ? logicScore / logicTasks : 0.5;
  }

  /**
   * Avalia aplicação de teoria de cores
   */
  assessColorTheoryApplication(sequences) {
    // Implementação simplificada baseada em padrões cromáticos avançados
    return 0.5;
  }

  /**
   * Calcula reconhecimento rítmico
   */
  calculateRhythmRecognition(sequences, temporalData) {
    // Implementação simplificada
    return 0.5;
  }

  /**
   * Calcula consistência temporal
   */
  calculateTemporalConsistency(temporalData) {
    const intervals = temporalData.responseIntervals || [];
    
    if (intervals.length < 2) return 0.5;
    
    const variance = this.calculateVariance(intervals);
    return Math.max(0.1, 1 - variance / 1000); // Normalizar variância
  }

  /**
   * Identifica padrões de batida
   */
  identifyBeatingPatterns(sequences) {
    // Implementação simplificada
    return 0.5;
  }

  /**
   * Calcula memória rítmica
   */
  calculateRhythmicMemory(sequences, temporalData) {
    // Implementação simplificada
    return 0.5;
  }

  /**
   * Métodos auxiliares básicos
   */
  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  }

  /**
   * Score padrão para quando não há dados suficientes
   */
  getDefaultSequenceScore(type) {
    return {
      accuracy: 0.5,
      speed: 0.5,
      overallScore: 0.5,
      level: 'average',
      recommendations: [`Dados insuficientes para análise de ${type}`]
    };
  }

  /**
   * Categoriza nível de sequência
   */
  categorizeSequenceLevel(score) {
    if (score >= 0.9) return 'excellent';
    if (score >= 0.75) return 'good';
    if (score >= 0.60) return 'average';
    if (score >= 0.40) return 'poor';
    return 'critical';
  }

  /**
   * Gera recomendações específicas
   */
  generateRecognitionRecommendations(accuracy, detection) {
    const recommendations = [];
    
    if (accuracy < 0.6) {
      recommendations.push('Praticar reconhecimento de padrões simples');
    }
    
    if (detection < 0.6) {
      recommendations.push('Exercitar detecção de padrões cromáticos');
    }
    
    return recommendations.length > 0 ? recommendations : ['Continuar desenvolvendo reconhecimento de padrões'];
  }

  generateCompletionRecommendations(accuracy, reasoning) {
    const recommendations = [];
    
    if (accuracy < 0.6) {
      recommendations.push('Exercitar completar sequências');
    }
    
    if (reasoning < 0.6) {
      recommendations.push('Desenvolver raciocínio lógico com cores');
    }
    
    return recommendations.length > 0 ? recommendations : ['Aprofundar análise de padrões'];
  }

  generateMemoryRecommendations(order, recall) {
    const recommendations = [];
    
    if (order < 0.6) {
      recommendations.push('Praticar memorização de ordem');
    }
    
    if (recall < 0.6) {
      recommendations.push('Exercitar recall de sequências');
    }
    
    return recommendations.length > 0 ? recommendations : ['Manter prática de memória sequencial'];
  }

  generateProgressionRecommendations(recognition, logic) {
    const recommendations = [];
    
    if (recognition < 0.6) {
      recommendations.push('Exercitar progressões cromáticas');
    }
    
    if (logic < 0.6) {
      recommendations.push('Desenvolver lógica de cores');
    }
    
    return recommendations.length > 0 ? recommendations : ['Aprofundar teoria de cores'];
  }

  generateRhythmicRecommendations(rhythm, consistency) {
    const recommendations = [];
    
    if (rhythm < 0.6) {
      recommendations.push('Praticar padrões rítmicos');
    }
    
    if (consistency < 0.6) {
      recommendations.push('Desenvolver consistência temporal');
    }
    
    return recommendations.length > 0 ? recommendations : ['Manter ritmo de aprendizado'];
  }

  // Implementações simplificadas para métodos complexos...
  calculateComplexityTolerance(sequences) { return 0.5; }
  calculateAdaptivePerformance(sequences) { return 0.5; }
  identifyComplexityPreference(sequences) { return 'moderate'; }
  findOptimalComplexity(sequences) { return 'moderate'; }
  generateComplexityRecommendations(tolerance, preference) { 
    return ['Ajustar complexidade baseado no desempenho']; 
  }

  calculatePredictionSuccess(sequences, completions) { return 0.5; }
  calculateAnticipationAbility(sequences) { return 0.5; }
  analyzeErrorPrediction(sequences) { return 0.3; }
  calculatePredictionConfidence(sequences) { return 0.5; }
  generatePredictionRecommendations(success, anticipation) { 
    return ['Desenvolver habilidades preditivas']; 
  }

  calculateSequentialProcessingSpeed(sequences, temporal) { return 0.5; }
  calculateSequentialProcessingAccuracy(sequences) { return 0.5; }
  calculateProcessingEfficiency(speed, accuracy) { return (speed + accuracy) / 2; }
  identifyProcessingBottlenecks(sequences, temporal) { return []; }
  generateProcessingRecommendations(speed, accuracy) { 
    return ['Otimizar velocidade e precisão']; 
  }

  calculateOrderImportance(sequences) { return 0.5; }
  calculatePositionSensitivity(sequences) { return 0.5; }
  calculateReversalDetection(sequences) { return 0.5; }
  calculateSequentialDependency(sequences) { return 0.5; }
  generateOrderRecommendations(importance, sensitivity) { 
    return ['Desenvolver sensibilidade à ordem']; 
  }

  calculateTemporalAccuracy(sequences, temporal) { return 0.5; }
  calculateTimingConsistency(temporal) { return 0.5; }
  calculateTemporalAnticipation(sequences, temporal) { return 0.5; }
  calculateRhythmicAlignment(sequences, temporal) { return 0.5; }
  generateTemporalRecommendations(accuracy, consistency) { 
    return ['Aprimorar coordenação temporal']; 
  }

  /**
   * Gera análise de fallback em caso de erro
   */
  generateFallbackAnalysis(errorMessage) {
    return {
      sequenceRecognition: this.getDefaultSequenceScore('recognition'),
      patternCompletion: this.getDefaultSequenceScore('completion'),
      sequentialMemory: this.getDefaultSequenceScore('memory'),
      colorProgression: this.getDefaultSequenceScore('progression'),
      rhythmicPatterns: this.getDefaultSequenceScore('rhythm'),
      sequenceComplexity: { tolerance: 0.5, level: 'average' },
      predictionAccuracy: this.getDefaultSequenceScore('prediction'),
      sequentialProcessing: this.getDefaultSequenceScore('processing'),
      orderSensitivity: this.getDefaultSequenceScore('order'),
      temporalSequencing: this.getDefaultSequenceScore('temporal'),
      error: true,
      errorMessage,
      recommendations: ['Coletar mais dados para análise precisa de sequências cromáticas']
    };
  }
}

export default SequentialColorCollector;
