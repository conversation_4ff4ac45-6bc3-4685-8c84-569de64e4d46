// ✅ CONFIGURAÇÃO V3 - QUEBRA-CABEÇA COM 6 ATIVIDADES TERAPÊUTICAS
export const QuebraCabecaV3Config = {
  // 🎯 CONFIGURAÇÃO DAS 6 ATIVIDADES V3
  ACTIVITY_CONFIG: {
    FREE_ASSEMBLY: {
      id: 'free_assembly',
      name: '<PERSON><PERSON><PERSON> Liv<PERSON>',
      icon: '🧩',
      description: 'Monte quebra-cabeças emocionais livremente',
      therapeuticFocus: ['motor_coordination', 'spatial_planning', 'problem_solving'],
      cognitiveAreas: ['spatial_processing', 'motor_skills', 'executive_function'],
      difficulties: {
        easy: { 
          pieces: 3, 
          gridSize: '1fr 1fr 1fr',
          timeLimit: 300000,
          helpLevel: 'high',
          distractors: 2
        },
        medium: { 
          pieces: 6, 
          gridSize: '1fr 1fr 1fr',
          timeLimit: 450000,
          helpLevel: 'medium',
          distractors: 4
        },
        hard: { 
          pieces: 9, 
          gridSize: '1fr 1fr 1fr',
          timeLimit: 600000,
          helpLevel: 'low',
          distractors: 6
        }
      },
      metrics: ['completion_time', 'piece_placement_accuracy', 'strategy_efficiency', 'error_patterns']
    },

    GUIDED_ASSEMBLY: {
      id: 'guided_assembly',
      name: '<PERSON><PERSON><PERSON>',
      icon: '🎯',
      description: 'Siga instruções específicas para montar',
      therapeuticFocus: ['instruction_following', 'working_memory', 'sequential_processing'],
      cognitiveAreas: ['auditory_processing', 'memory', 'attention'],
      difficulties: {
        easy: { 
          sequenceLength: 3,
          guidanceType: 'visual',
          pauseBetweenSteps: 3000,
          allowRepeats: true
        },
        medium: { 
          sequenceLength: 5,
          guidanceType: 'visual_audio',
          pauseBetweenSteps: 2000,
          allowRepeats: true
        },
        hard: { 
          sequenceLength: 7,
          guidanceType: 'audio_only',
          pauseBetweenSteps: 1500,
          allowRepeats: false
        }
      },
      metrics: ['instruction_adherence', 'sequence_memory', 'guidance_dependency', 'completion_accuracy']
    },

    ROTATION_RECONSTRUCTION: {
      id: 'rotation_reconstruction',
      name: 'Reconstrução por Rotação',
      icon: '🔄',
      description: 'Monte peças que aparecem rotacionadas',
      therapeuticFocus: ['spatial_transformation', 'mental_rotation', 'visual_processing'],
      cognitiveAreas: ['spatial_reasoning', 'visual_perception', 'cognitive_flexibility'],
      difficulties: {
        easy: { 
          rotationAngles: [90, 180],
          pieceCount: 4,
          rotationPreview: true,
          timePerPiece: 15000
        },
        medium: { 
          rotationAngles: [90, 180, 270],
          pieceCount: 6,
          rotationPreview: false,
          timePerPiece: 12000
        },
        hard: { 
          rotationAngles: [45, 90, 135, 180, 225, 270, 315],
          pieceCount: 8,
          rotationPreview: false,
          timePerPiece: 10000
        }
      },
      metrics: ['rotation_accuracy', 'mental_rotation_speed', 'spatial_transformation_ability', 'error_recovery']
    },

    PIECE_CLASSIFICATION: {
      id: 'piece_classification',
      name: 'Classificação de Peças',
      icon: '🎨',
      description: 'Organize peças por categorias',
      therapeuticFocus: ['categorization', 'visual_discrimination', 'organizational_skills'],
      cognitiveAreas: ['executive_function', 'visual_processing', 'conceptual_thinking'],
      difficulties: {
        easy: { 
          categories: 2,
          piecesPerCategory: 3,
          categoryHints: true,
          sortingCriteria: ['color', 'emotion']
        },
        medium: { 
          categories: 3,
          piecesPerCategory: 4,
          categoryHints: false,
          sortingCriteria: ['emotion', 'context', 'symbol']
        },
        hard: { 
          categories: 4,
          piecesPerCategory: 5,
          categoryHints: false,
          sortingCriteria: ['emotion', 'context', 'symbol', 'theme']
        }
      },
      metrics: ['classification_accuracy', 'sorting_strategy', 'category_consistency', 'processing_speed']
    },

    PATTERN_IDENTIFICATION: {
      id: 'pattern_identification',
      name: 'Identificação de Padrões',
      icon: '🔍',
      description: 'Identifique padrões nos quebra-cabeças',
      therapeuticFocus: ['pattern_recognition', 'logical_reasoning', 'predictive_thinking'],
      cognitiveAreas: ['pattern_processing', 'logical_thinking', 'predictive_analysis'],
      difficulties: {
        easy: { 
          patternLength: 4,
          patternTypes: ['alternating', 'sequential'],
          hintLevel: 'high',
          completionOptions: 2
        },
        medium: { 
          patternLength: 6,
          patternTypes: ['alternating', 'sequential', 'progressive'],
          hintLevel: 'medium',
          completionOptions: 3
        },
        hard: { 
          patternLength: 8,
          patternTypes: ['alternating', 'sequential', 'progressive', 'complex'],
          hintLevel: 'low',
          completionOptions: 4
        }
      },
      metrics: ['pattern_recognition_speed', 'logical_accuracy', 'prediction_success', 'learning_progression']
    },

    COLLABORATIVE_SOLVING: {
      id: 'collaborative_solving',
      name: 'Resolução Colaborativa',
      icon: '🧠',
      description: 'Resolva em equipe com outros jogadores',
      therapeuticFocus: ['social_cooperation', 'communication', 'shared_problem_solving'],
      cognitiveAreas: ['social_cognition', 'communication_skills', 'collaborative_thinking'],
      difficulties: {
        easy: { 
          teamSize: 2,
          communicationLevel: 'high',
          sharedPieces: 4,
          coordinationRequired: 'low'
        },
        medium: { 
          teamSize: 3,
          communicationLevel: 'medium',
          sharedPieces: 6,
          coordinationRequired: 'medium'
        },
        hard: { 
          teamSize: 4,
          communicationLevel: 'minimal',
          sharedPieces: 8,
          coordinationRequired: 'high'
        }
      },
      metrics: ['cooperation_index', 'communication_effectiveness', 'shared_goal_achievement', 'leadership_emergence']
    }
  },

  // 🎭 BIBLIOTECA EXPANDIDA DE EMOÇÕES V3
  EMOTIONS_LIBRARY: {
    basic: [
      {
        id: 'happy',
        name: 'Feliz',
        emoji: '😊',
        pieces: ['😊', '🌞', '🎁', '🎉'],
        context: 'Ganhar um presente',
        color: '#FFD93D',
        complexity: 'basic',
        therapeuticValue: 'positive_emotion_recognition'
      },
      {
        id: 'sad',
        name: 'Triste',
        emoji: '😢',
        pieces: ['😢', '🌧️', '💔', '😔'],
        context: 'Perder um brinquedo',
        color: '#6BB6FF',
        complexity: 'basic',
        therapeuticValue: 'emotional_regulation'
      },
      {
        id: 'surprised',
        name: 'Surpreso',
        emoji: '😲',
        pieces: ['😲', '🎉', '❓', '✨'],
        context: 'Ver algo inesperado',
        color: '#FF6B6B',
        complexity: 'basic',
        therapeuticValue: 'reaction_processing'
      },
      {
        id: 'angry',
        name: 'Bravo',
        emoji: '😠',
        pieces: ['😠', '💥', '🌋', '⚡'],
        context: 'Quando algo não sai como esperado',
        color: '#FF8B94',
        complexity: 'basic',
        therapeuticValue: 'anger_management'
      },
      {
        id: 'calm',
        name: 'Calmo',
        emoji: '😌',
        pieces: ['😌', '🌊', '🕊️', '🌿'],
        context: 'Relaxar na natureza',
        color: '#4ECDC4',
        complexity: 'basic',
        therapeuticValue: 'self_regulation'
      },
      {
        id: 'excited',
        name: 'Animado',
        emoji: '🤩',
        pieces: ['🤩', '🎪', '🎢', '🎊'],
        context: 'Ir ao parque de diversões',
        color: '#A8E6CF',
        complexity: 'basic',
        therapeuticValue: 'positive_anticipation'
      }
    ],

    intermediate: [
      {
        id: 'confused',
        name: 'Confuso',
        emoji: '😕',
        pieces: ['😕', '❓', '🤔', '💭'],
        context: 'Não entender algo',
        color: '#F4A261',
        complexity: 'intermediate',
        therapeuticValue: 'uncertainty_tolerance'
      },
      {
        id: 'proud',
        name: 'Orgulhoso',
        emoji: '😤',
        pieces: ['😤', '🏆', '⭐', '👑'],
        context: 'Conseguir algo difícil',
        color: '#E76F51',
        complexity: 'intermediate',
        therapeuticValue: 'self_esteem'
      },
      {
        id: 'worried',
        name: 'Preocupado',
        emoji: '😰',
        pieces: ['😰', '☁️', '⚠️', '💭'],
        context: 'Pensar em problemas',
        color: '#264653',
        complexity: 'intermediate',
        therapeuticValue: 'anxiety_awareness'
      },
      {
        id: 'love',
        name: 'Amor',
        emoji: '🥰',
        pieces: ['🥰', '💖', '🌹', '✨'],
        context: 'Estar com pessoas queridas',
        color: '#E9C46A',
        complexity: 'intermediate',
        therapeuticValue: 'attachment_recognition'
      }
    ],

    advanced: [
      {
        id: 'jealous',
        name: 'Ciúmes',
        emoji: '😒',
        pieces: ['😒', '👀', '💚', '⚡'],
        context: 'Ver outros com algo que queremos',
        color: '#2A9D8F',
        complexity: 'advanced',
        therapeuticValue: 'complex_emotion_understanding'
      },
      {
        id: 'embarrassed',
        name: 'Envergonhado',
        emoji: '😳',
        pieces: ['😳', '🔥', '👥', '🙈'],
        context: 'Fazer algo constrangedor',
        color: '#F4A261',
        complexity: 'advanced',
        therapeuticValue: 'social_awareness'
      },
      {
        id: 'disappointed',
        name: 'Desapontado',
        emoji: '😞',
        pieces: ['😞', '💔', '😔', '🌧️'],
        context: 'Expectativas não atendidas',
        color: '#264653',
        complexity: 'advanced',
        therapeuticValue: 'expectation_management'
      }
    ]
  },

  // ⚙️ CONFIGURAÇÕES DE DIFICULDADE V3
  DIFFICULTY_CONFIGS: {
    EASY: {
      name: 'Fácil',
      description: 'Ideal para iniciantes - Mais ajuda e tempo',
      color: '#4CAF50',
      icon: '🟢',
      emotionComplexity: 'basic',
      roundsPerActivity: 2,
      timeMultiplier: 1.5,
      helpLevel: 'high',
      feedbackFrequency: 'high'
    },
    MEDIUM: {
      name: 'Médio',
      description: 'Desafio equilibrado - Ajuda moderada',
      color: '#FF9800',
      icon: '🟡',
      emotionComplexity: 'intermediate',
      roundsPerActivity: 3,
      timeMultiplier: 1.0,
      helpLevel: 'medium',
      feedbackFrequency: 'medium'
    },
    HARD: {
      name: 'Difícil',
      description: 'Para especialistas - Mínima assistência',
      color: '#F44336',
      icon: '🔴',
      emotionComplexity: 'advanced',
      roundsPerActivity: 4,
      timeMultiplier: 0.8,
      helpLevel: 'low',
      feedbackFrequency: 'low'
    }
  },

  // 🔄 SISTEMA DE ROTAÇÃO DE ATIVIDADES V3
  ACTIVITY_ROTATION: {
    defaultCycle: [
      'free_assembly',
      'guided_assembly', 
      'rotation_reconstruction',
      'piece_classification',
      'pattern_identification',
      'collaborative_solving'
    ],
    adaptiveCycle: true, // Ajusta baseado na performance
    rotationTriggers: {
      roundsCompleted: 3,
      timeElapsed: 300000, // 5 minutos
      accuracyThreshold: 80,
      engagementDrop: 20
    }
  },

  // 📊 MÉTRICAS TERAPÊUTICAS V3
  THERAPEUTIC_METRICS: {
    spatial_reasoning: {
      components: ['mental_rotation', 'spatial_memory', 'spatial_transformation'],
      weights: { mental_rotation: 0.4, spatial_memory: 0.3, spatial_transformation: 0.3 },
      normalization: 'age_based'
    },
    problem_solving: {
      components: ['strategy_formation', 'error_correction', 'persistence'],
      weights: { strategy_formation: 0.4, error_correction: 0.3, persistence: 0.3 },
      normalization: 'adaptive'
    },
    motor_coordination: {
      components: ['fine_motor', 'hand_eye_coordination', 'movement_precision'],
      weights: { fine_motor: 0.4, hand_eye_coordination: 0.3, movement_precision: 0.3 },
      normalization: 'developmental'
    },
    emotional_recognition: {
      components: ['emotion_identification', 'context_understanding', 'expression_matching'],
      weights: { emotion_identification: 0.4, context_understanding: 0.3, expression_matching: 0.3 },
      normalization: 'clinical'
    },
    social_skills: {
      components: ['cooperation', 'communication', 'leadership'],
      weights: { cooperation: 0.4, communication: 0.3, leadership: 0.3 },
      normalization: 'social_developmental'
    },
    attention_control: {
      components: ['sustained_attention', 'selective_attention', 'attention_switching'],
      weights: { sustained_attention: 0.4, selective_attention: 0.3, attention_switching: 0.3 },
      normalization: 'clinical'
    }
  },

  // 🎯 CONFIGURAÇÕES DO JOGO V3
  GAME_SETTINGS: {
    pointsByDifficulty: {
      EASY: { base: 10, bonus: 5, time_bonus: 3 },
      MEDIUM: { base: 15, bonus: 8, time_bonus: 5 },
      HARD: { base: 20, bonus: 12, time_bonus: 8 }
    },
    completionFeedbackDuration: 3000,
    nextPuzzleDelay: 1000,
    autoSaveInterval: 30000,
    sessionTimeLimit: 1800000, // 30 minutos
    adaptiveDifficultyEnabled: true,
    multisensoryFeedback: true,
    accessibilityMode: false
  },

  // 🎨 ELEMENTOS VISUAIS V3
  VISUAL_ELEMENTS: {
    animations: {
      pieceMovement: 'smooth',
      feedbackDuration: 2000,
      transitionSpeed: 300,
      successAnimation: 'bounce',
      errorAnimation: 'shake'
    },
    themes: {
      default: 'modern_glassmorphism',
      highContrast: 'accessibility_focused',
      childFriendly: 'colorful_rounded'
    },
    sounds: {
      piecePlace: 'soft_click',
      success: 'cheerful_chime',
      error: 'gentle_buzz',
      completion: 'victory_fanfare'
    }
  },

  // 📱 INFORMAÇÕES DO JOGO V3
  GAME_INFO: {
    title: 'Quebra-Cabeça V3',
    subtitle: 'Sistema Completo de 6 Atividades Terapêuticas',
    description: 'Desenvolva habilidades espaciais, emocionais e sociais através de quebra-cabeças especializados',
    icon: '🧩',
    category: 'spatial_emotional_development',
    version: '3.0.0',
    ageRange: '4-12',
    therapeuticApplications: [
      'Desenvolvimento espacial',
      'Reconhecimento emocional', 
      'Coordenação motora fina',
      'Resolução de problemas',
      'Habilidades sociais',
      'Controle executivo'
    ],
    clinicalValidation: true,
    accessibilityCompliant: true
  }
};

// ✅ COMPATIBILIDADE COM VERSÃO ANTERIOR
export const QuebraCabecaConfig = {
  // Manter referências da versão anterior para compatibilidade
  emotions: QuebraCabecaV3Config.EMOTIONS_LIBRARY.basic,
  difficulties: [
    { id: 'easy', name: 'Fácil', pieces: 3, gridSize: 3 },
    { id: 'medium', name: 'Médio', pieces: 6, gridSize: 4 },
    { id: 'hard', name: 'Difícil', pieces: 9, gridSize: 4 }
  ],
  encouragingMessages: [
    'Muito bem! Você reconheceu a emoção! 🌟',
    'Excelente! Continue assim! 🎉',
    'Você está ótimo em montar emoções! 😊',
    'Perfeito! Sua paciência é incrível! ✨',
    'Fantástico! Você entende as emoções! 🧠'
  ],
  gameSettings: QuebraCabecaV3Config.GAME_SETTINGS,
  gameInfo: QuebraCabecaV3Config.GAME_INFO
};
