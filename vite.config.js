import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';

// Mock completo para databaseInstance com todas as funções necessárias
const mockDatabaseInstance = `export default { 
  getInstance: () => Promise.resolve({ 
    mock: true, 
    manager: { 
      query: () => Promise.resolve([]), 
      getStatus: () => ({ status: 'mock', connected: true }),
      store: () => Promise.resolve({ success: true }),
      getDatabaseService: () => ({ 
        query: () => Promise.resolve([]), 
        store: () => Promise.resolve({ success: true }),
        getStatus: () => ({ status: 'mock', connected: true })
      })
    },
    getStatus: () => ({ status: 'mock', connected: true }),
    saveGameMetrics: () => Promise.resolve({ success: true }),
    query: () => Promise.resolve([]),
    getDatabaseService: () => ({ 
      query: () => Promise.resolve([]), 
      store: () => Promise.resolve({ success: true }),
      getStatus: () => ({ status: 'mock', connected: true })
    }),
    resilience: {
      executeWithRetry: (fn) => fn()
    }
  }),
  getStatus: () => ({ status: 'mock', connected: true }),
  saveGameMetrics: () => Promise.resolve({ success: true }),
  query: () => Promise.resolve([]),
  getDatabaseService: () => ({ 
    query: () => Promise.resolve([]), 
    store: () => Promise.resolve({ success: true }),
    getStatus: () => ({ status: 'mock', connected: true })
  }),
  resilience: {
    executeWithRetry: (fn) => fn()
  }
}`;

// Plugin personalizado para resolver módulos que não existem no frontend
const virtualModules = {
  '../../../database/services/databaseInstance.js': mockDatabaseInstance,
  '../../../../database/services/databaseInstance.js': mockDatabaseInstance,
  '../database/services/databaseInstance.js': mockDatabaseInstance,
  '../../database/services/databaseInstance.js': mockDatabaseInstance,
  'database/services/databaseInstance.js': mockDatabaseInstance,
  '/app/database/services/databaseInstance.js': mockDatabaseInstance
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react({
      include: "**/*.{jsx,tsx}",
    }),
    // Plugin para corrigir MIME types
    {
      name: 'mime-fix',
      configureServer(server) {
        server.middlewares.use((req, res, next) => {
          const url = req.url || '';
          
          // Corrigir MIME type para arquivos JSX
          if (url.endsWith('.jsx') || url.endsWith('.js') || url.endsWith('.mjs')) {
            res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
          }
          // Corrigir MIME type para arquivos CSS
          else if (url.endsWith('.css')) {
            res.setHeader('Content-Type', 'text/css; charset=utf-8');
          }
          
          next();
        });
      },
    },
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
    {
      name: 'exclude-preview',
      enforce: 'pre',
      transform(code, id) {
        if (id.includes('/preview/') || id.match(/preview.*\.html/)) {
          console.log(`[vite] Ignorando arquivo de preview: ${id}`);
          return { code: 'export default {};', map: null };
        }
      }
    },
    // Plugin para simular módulos do backend
    {
      name: 'virtual-modules',
      resolveId(id) {
        // Verificar se o ID está diretamente em virtualModules
        if (virtualModules[id]) return id;
        
        // Verificar se o caminho termina com databaseInstance.js
        if (id.includes('databaseInstance.js')) {
          // Encontrar a chave de virtualModules que melhor corresponde
          const keys = Object.keys(virtualModules);
          for (const key of keys) {
            if (id.endsWith(key)) {
              console.log(`[vite] Mapeando ${id} para ${key}`);
              return key;
            }
          }
          // Fallback para o primeiro mock disponível
          console.log(`[vite] Usando fallback para ${id}`);
          return Object.keys(virtualModules)[0];
        }
        return null;
      },
      load(id) {
        if (virtualModules[id]) return virtualModules[id];
        return null;
      }
    }
  ],
  define: {
    // Definir variáveis globais para compatibilidade
    global: 'globalThis',
    'process.env': {
      NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'development'),
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@hooks': resolve(__dirname, 'src/hooks'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@games': resolve(__dirname, 'src/games'),
      '@api': resolve(__dirname, 'src/api'),
    },
  },
  // Otimizações de dependências (unificado)
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'chart.js',
      'react-chartjs-2',
      'framer-motion',
      'styled-components',
      'lodash',
      'date-fns',
      'uuid',
      'axios',
    ],
    exclude: [
      '@api',
      'src/api/services/core/SystemOrchestrator.js',
      'database/services/databaseInstance.js',
      '../../../database/services/databaseInstance.js',
      '../../../../database/services/databaseInstance.js',
      '../database/services/databaseInstance.js',
      '../../database/services/databaseInstance.js',
      'pg',
      'redis',
      'winston',
      'nodemon',
      'express'
    ],
  },
  esbuild: {
    loader: 'jsx',
    include: /src\/.*\.[jt]sx?$/,
    exclude: []
  },
  server: {
    port: 5173,
    host: true,
    middlewareMode: false,
    fs: {
      allow: ['..']
    },
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    minify: 'terser',
    sourcemap: false,
    emptyOutDir: true,
    target: 'es2020',
    cssCodeSplit: true,
    chunkSizeWarningLimit: 500,
    rollupOptions: {
      external: [
        'pg',
        'redis', 
        'winston',
        'express',
        'nodemon',
        'bcrypt',
        'bcryptjs',
        'jsonwebtoken',
        'helmet',
        'compression',
        'cors',
        'rate-limiter-flexible',
        /database\/services\/databaseInstance\.js/,
        /src\/api\/services\/databaseInstance\.js/,
        /database\/services\/DatabaseService.*\.js/,
        /database\/index\.js/,
        /preview\/.*/
      ],
      output: {
        format: 'es',
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        manualChunks: (id) => {
          // React ecosystem
          if (id.includes('react') || id.includes('react-dom')) {
            return 'react-core';
          }
          if (id.includes('react-router')) {
            return 'react-router';
          }
          if (id.includes('@tanstack/react-query')) {
            return 'react-query';
          }
          
          // Chart libraries
          if (id.includes('chart.js')) {
            return 'chart-core';
          }
          if (id.includes('react-chartjs-2')) {
            return 'chart-react';
          }
          
          // UI libraries
          if (id.includes('framer-motion')) {
            return 'framer-motion';
          }
          if (id.includes('styled-components')) {
            return 'styled-components';
          }
          
          // Utility libraries
          if (id.includes('lodash')) {
            return 'lodash';
          }
          if (id.includes('date-fns')) {
            return 'date-utils';
          }
          if (id.includes('uuid')) {
            return 'uuid';
          }
          if (id.includes('axios')) {
            return 'axios';
          }
          
          // Games - separar cada jogo
          if (id.includes('CreativePainting')) {
            return 'games-creative';
          }
          if (id.includes('MemoryGame')) {
            return 'games-memory';
          }
          if (id.includes('PadroesVisuais')) {
            return 'games-patterns';
          }
          if (id.includes('ContagemNumeros')) {
            return 'games-numbers';
          }
          if (id.includes('LetterRecognition')) {
            return 'games-letters';
          }
          if (id.includes('ColorMatch')) {
            return 'games-colors';
          }
          if (id.includes('ImageAssociation')) {
            return 'games-association';
          }
          if (id.includes('MusicalSequence')) {
            return 'games-musical';
          }
          if (id.includes('QuebraCabeca')) {
            return 'games-puzzle';
          }
          
          // Dashboards
          if (id.includes('AdminPanel')) {
            return 'dashboard-admin';
          }
          if (id.includes('NeuropedagogicalDashboard')) {
            return 'dashboard-neuro';
          }
          if (id.includes('DashboardContainer')) {
            return 'dashboard-container';
          }
          
          // Collectors e métricas
          if (id.includes('collectors') || id.includes('Collector')) {
            return 'collectors';
          }
          if (id.includes('metrics') || id.includes('Metrics')) {
            return 'metrics';
          }
          
          // Hooks
          if (id.includes('useMultisensoryIntegration') || id.includes('useUnifiedGameLogic')) {
            return 'hooks-game';
          }
          if (id.includes('hooks/')) {
            return 'hooks-utils';
          }
          
          // Utils e services
          if (id.includes('utils/')) {
            return 'utils';
          }
          if (id.includes('services/')) {
            return 'services';
          }
          if (id.includes('api/')) {
            return 'api';
          }
          
          // Context providers
          if (id.includes('context/')) {
            return 'context';
          }
          
          // Prop validation
          if (id.includes('prop-types')) {
            return 'prop-types';
          }
          
          // Helmet
          if (id.includes('react-helmet')) {
            return 'helmet';
          }
          
          // Vendor libraries
          if (id.includes('node_modules') && !id.includes('react')) {
            return 'vendor';
          }
        },
      },
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        dead_code: true,
        unused: true,
        pure_funcs: ['console.log', 'console.info', 'console.warn'],
        passes: 2,
        unsafe: true,
        unsafe_comps: true,
      },
      mangle: {
        safari10: true,
        properties: {
          regex: /^_/,
        },
      },
      format: {
        comments: false,
      },
    },
  },
});
