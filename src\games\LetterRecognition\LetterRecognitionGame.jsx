/**
 * 📚 LETTER RECOGNITION GAME - Portal Betina V3
 * Layout baseado no padrão ColorMatch
 */

import React, { useState, useEffect, useCallback, useContext, useRef } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { LetterRecognitionConfig } from './LetterRecognitionConfig.js';
import { LetterRecognitionMetrics } from './LetterRecognitionMetrics.js';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// 📚 Importar coletores avançados de reconhecimento de letras
import { LetterRecognitionCollectorsHub } from './collectors/index.js';
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
// 🎯 Importar hook orquestrador terapêutico
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// Importa estilos modulares
import styles from './LetterRecognition.module.css';

// Configurações do jogo
const LETTERS = [
  { id: 'a', letter: 'A', sound: 'A', example: '🐝 Abelha', color: '#FF6B6B' },
  { id: 'b', letter: 'B', sound: 'Bê', example: '⚽ Bola', color: '#4ECDC4' },
  { id: 'c', letter: 'C', sound: 'Cê', example: '🏠 Casa', color: '#45B7D1' },
  { id: 'd', letter: 'D', sound: 'Dê', example: '🎲 Dado', color: '#FFA07A' },
  { id: 'e', letter: 'E', sound: 'É', example: '⭐ Estrela', color: '#98D8C8' },
  { id: 'f', letter: 'F', sound: 'Efe', example: '🌸 Flor', color: '#F7DC6F' },
  { id: 'g', letter: 'G', sound: 'Gê', example: '� Gato', color: '#BB8FCE' },
  { id: 'h', letter: 'H', sound: 'Agá', example: '� Hotel', color: '#85C1E9' }
];

const DIFFICULTIES = {
  EASY: { letters: LETTERS.slice(0, 4), name: 'Fácil' },
  MEDIUM: { letters: LETTERS.slice(0, 6), name: 'Médio' },
  HARD: { letters: LETTERS, name: 'Avançado' }
};

// 🎯 SISTEMA DE ATIVIDADES EXPANDIDO V3
const ACTIVITY_TYPES = {
  LETTER_SELECTION: {
    id: 'letter_selection',
    name: 'Seleção de Letras',
    icon: '🔤',
    description: 'Encontre a letra correta baseada no som e exemplo',
    component: 'LetterSelectionActivity'
  },
  SOUND_MATCHING: {
    id: 'sound_matching',
    name: 'Combinação de Sons',
    icon: '🎵',
    description: 'Associe o som à letra correspondente',
    component: 'SoundMatchingActivity'
  },
  WORD_FORMATION: {
    id: 'word_formation',
    name: 'Formação de Palavras',
    icon: '🔗',
    description: 'Monte palavras usando as letras aprendidas',
    component: 'WordFormationActivity'
  },
  SEQUENCE_RECOGNITION: {
    id: 'sequence_recognition',
    name: 'Reconhecimento de Sequência',
    icon: '📝',
    description: 'Complete sequências alfabéticas',
    component: 'SequenceRecognitionActivity'
  },
  VISUAL_DISCRIMINATION: {
    id: 'visual_discrimination',
    name: 'Discriminação Visual',
    icon: '👁️',
    description: 'Identifique letras com formas similares',
    component: 'VisualDiscriminationActivity'
  }
};

// 🎯 Configuração de palavras para formação
const WORD_BANK = {
  EASY: [
    { word: 'MAMA', emoji: '👩', meaning: 'Mamãe' },
    { word: 'BABA', emoji: '👶', meaning: 'Bebê' },
    { word: 'CACA', emoji: '💩', meaning: 'Cocô' },
    { word: 'DADA', emoji: '🎁', meaning: 'Presente' }
  ],
  MEDIUM: [
    { word: 'BEBE', emoji: '🍼', meaning: 'Bebê' },
    { word: 'FADA', emoji: '🧚', meaning: 'Fada' },
    { word: 'CAFE', emoji: '☕', meaning: 'Café' },
    { word: 'FACE', emoji: '😊', meaning: 'Rosto' }
  ],
  HARD: [
    { word: 'GATO', emoji: '🐱', meaning: 'Gato' },
    { word: 'FOGO', emoji: '🔥', meaning: 'Fogo' },
    { word: 'CASA', emoji: '🏠', meaning: 'Casa' },
    { word: 'BEIJO', emoji: '💋', meaning: 'Beijo' }
  ]
};

// 🎯 Sequências alfabéticas para reconhecimento - EXPANDIDO
const ALPHABET_SEQUENCES = [
  // Sequências simples consecutivas
  { sequence: ['A', 'B', 'C'], missing: 'D', options: ['D', 'E', 'F'] },
  { sequence: ['B', 'C', 'D'], missing: 'E', options: ['E', 'F', 'G'] },
  { sequence: ['C', 'D', 'E'], missing: 'F', options: ['F', 'G', 'H'] },
  { sequence: ['D', 'E', 'F'], missing: 'G', options: ['G', 'H', 'I'] },
  { sequence: ['E', 'F', 'G'], missing: 'H', options: ['H', 'I', 'J'] },
  
  // Sequências saltando uma letra
  { sequence: ['A', 'C', 'E'], missing: 'G', options: ['G', 'F', 'H'] },
  { sequence: ['B', 'D', 'F'], missing: 'H', options: ['H', 'G', 'I'] },
  { sequence: ['C', 'E', 'G'], missing: 'I', options: ['I', 'H', 'J'] },
  
  // Sequências crescentes
  { sequence: ['M', 'N', 'O'], missing: 'P', options: ['P', 'Q', 'R'] },
  { sequence: ['X', 'Y', 'Z'], missing: 'W', options: ['W', 'V', 'U'] },
  
  // Sequências no meio do alfabeto
  { sequence: ['J', 'K', 'L'], missing: 'M', options: ['M', 'N', 'O'] },
  { sequence: ['P', 'Q', 'R'], missing: 'S', options: ['S', 'T', 'U'] }
];

// 🎯 Pares para discriminação fonética
// Removido: PHONETIC_PAIRS agora vem do config

// 🎯 Grupos para discriminação visual
// Removido: VISUAL_GROUPS agora vem do config

function LetterRecognitionGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const metricsRef = useRef(new LetterRecognitionMetrics());
  const sessionIdRef = useRef(uuidv4());

  // 📚 Inicializar coletores avançados de reconhecimento de letras
  const [collectorsHub] = useState(() => new LetterRecognitionCollectorsHub())

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration('letter-recognition', collectorsHub, {
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
    userId: sessionIdRef.current,
    enableAdvancedMetrics: true,
    enableRealTimeAnalysis: true,
    enableNeurodivergenceSupport: true
  });

  // 🎯 Hook orquestrador terapêutico integrado
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    userId: user?.id || 'anonymous'
  });

  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();

  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO
  // =====================================================
  
  // Estado do TTS com persistência
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('letterRecognition_ttsActive');
    return saved !== null ? JSON.parse(saved) : true;
  });
  
  // Função para alternar TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('letterRecognition_ttsActive', JSON.stringify(newState));
      
      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      
      return newState;
    });
  }, []);
  
  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    // Verificar se TTS está ativo
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }
    
    // Cancelar qualquer fala anterior
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('letter_recognition');

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)
  const [analysisResults, setAnalysisResults] = useState(null)
  const [attemptCount, setAttemptCount] = useState(0)

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'menu',
    score: 0,
    round: 1,
    targetLetter: null,
    selectedLetter: null,
    showFeedback: false,
    accuracy: 100,
    totalRounds: 10,
    difficulty: 'EASY',
    availableLetters: DIFFICULTIES.EASY.letters,
    allLetters: DIFFICULTIES.EASY.letters,
    roundStartTime: null,
    
    // 🎯 Sistema de atividades
    currentActivity: ACTIVITY_TYPES.LETTER_SELECTION.id,
    activityCycle: [
      ACTIVITY_TYPES.LETTER_SELECTION.id,
      ACTIVITY_TYPES.SOUND_MATCHING.id,
      ACTIVITY_TYPES.WORD_FORMATION.id,
      ACTIVITY_TYPES.SEQUENCE_RECOGNITION.id,
      ACTIVITY_TYPES.VISUAL_DISCRIMINATION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 2, // Mínimo 2 rodadas por atividade antes de permitir troca
    activityRoundCount: 2, // Começar com 2 para permitir troca imediata
    activitiesCompleted: [], // Lista de atividades já completadas
    
    // 🎯 Dados específicos de atividades
    activityData: {
      wordFormation: {
        currentWord: null,
        placedLetters: [],
        availableLetters: []
      },
      sequenceRecognition: {
        currentSequence: null,
        userProgress: []
      },
      visualDiscrimination: {
        currentGroup: null,
        foundTargets: 0,
        totalTargets: 0
      }
    },
    
    // 🎯 Métricas comportamentais avançadas
    behavioralMetrics: {
      activityPreferences: {},
      responsePatterns: [],
      adaptiveAdjustments: 0,
      engagementLevel: 1.0
    }
  });
  
  const [showStartScreen, setShowStartScreen] = useState(true);

  // 🎯 SISTEMA DE ROTAÇÃO DE ATIVIDADES
  const rotateActivity = useCallback(() => {
    setGameState(prev => {
      const nextActivityIndex = (prev.activityIndex + 1) % prev.activityCycle.length;
      const nextActivity = prev.activityCycle[nextActivityIndex];
      
      // 🔊 Anunciar nova atividade
      const activityName = ACTIVITY_TYPES[nextActivity.toUpperCase()]?.name || 'Nova Atividade';
      speak(`Nova atividade: ${activityName}`, { pitch: 1.2, rate: 0.8 });
      
      return {
        ...prev,
        currentActivity: nextActivity,
        activityIndex: nextActivityIndex,
        activityRoundCount: 0,
        selectedLetter: null,
        showFeedback: false
      };
    });
  }, [speak]);

  // =====================================================
  // 🔊 SISTEMA DE TTS ESPECÍFICO POR ATIVIDADE - ACESSIBILIDADE
  // =====================================================

  // 🔊 TTS específico para cada atividade com instruções claras e direcionadas
  const provideActivityInstructions = useCallback((activity, activityData) => {
    console.log('🔊 Providing TTS instructions for activity:', activity);

    switch (activity) {
      case 'letter_selection':
        if (activityData?.targetLetter) {
          const letter = activityData.targetLetter;
          const instruction = `Encontre a letra ${letter.letter}. ${letter.sound} de ${letter.example.split(' ')[1]}. Clique na letra ${letter.letter}.`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      case 'sound_matching':
        if (activityData?.targetLetter) {
          const letter = activityData.targetLetter;
          const instruction = `Escute o som e encontre a letra correspondente. ${letter.sound}. Procure a letra ${letter.letter}.`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      case 'word_formation':
        if (activityData?.currentWord) {
          const word = activityData.currentWord;
          const instruction = `Monte a palavra ${word.meaning}. ${word.emoji} ${word.meaning}. Use as letras para formar ${word.word}.`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      case 'sequence_recognition':
        if (activityData?.currentSequence) {
          const sequence = activityData.currentSequence;
          const instruction = `Complete a sequência alfabética. ${sequence.sequence.join(', ')}. Qual letra vem depois?`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      case 'visual_discrimination':
        if (activityData?.currentGroup) {
          const group = activityData.currentGroup;
          const instruction = `${group.description}. Clique em todas as letras ${group.target} que você encontrar.`;
          speak(instruction, { rate: 0.7, pitch: 1.1 });
        }
        break;

      default:
        speak("Escolha uma atividade para começar.", { rate: 0.8 });
    }
  }, [speak]);

  // 🎯 Gerador de conteúdo específico por atividade - MELHORADO COM TTS
  const generateActivityContent = useCallback((activity, difficulty) => {
    const difficultyConfig = DIFFICULTIES[difficulty] || DIFFICULTIES.EASY;

    switch (activity) {
      case 'sound_matching':
        // Garantir que sempre gere uma letra diferente da anterior
        let targetLetter;
        do {
          targetLetter = difficultyConfig.letters[Math.floor(Math.random() * difficultyConfig.letters.length)];
        } while (targetLetter === gameState.targetLetter);

        // Criar opções com a letra alvo + 2 distrativas
        const soundOptions = [targetLetter];
        const otherLetters = difficultyConfig.letters.filter(l => l !== targetLetter);
        soundOptions.push(...shuffleArray(otherLetters).slice(0, 2));

        const soundContent = {
          targetLetter,
          options: shuffleArray(soundOptions)
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, soundContent), 500);

        return soundContent;
        
      case 'word_formation':
        const wordBank = WORD_BANK[difficulty] || WORD_BANK.EASY;
        const selectedWord = wordBank[Math.floor(Math.random() * wordBank.length)];

        const wordLetters = selectedWord.word.split('');
        const distractors = generateDistracters(wordLetters, 2);
        const availableLetters = shuffleArray([...wordLetters, ...distractors]);

        console.log('📝 Generated word formation content:', {
          selectedWord,
          wordLetters,
          distractors,
          availableLetters
        });

        const wordContent = {
          currentWord: selectedWord,
          availableLetters,
          placedLetters: new Array(selectedWord.word.length).fill(null)
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, wordContent), 500);

        return wordContent;
        
      case 'sequence_recognition':
        const selectedSequence = ALPHABET_SEQUENCES[Math.floor(Math.random() * ALPHABET_SEQUENCES.length)];

        const sequenceContent = {
          currentSequence: selectedSequence,
          userAnswer: null,
          showOptions: true
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, sequenceContent), 500);

        return sequenceContent;
        
      case 'visual_discrimination':
        // Criar grupo visual simples para teste
        const visualGroups = [
          { target: 'A', items: ['A', 'H', 'V', 'A', 'N', 'A'], description: 'Encontre todas as letras A' },
          { target: 'O', items: ['O', 'C', 'U', 'O', 'Q', 'O'], description: 'Encontre todas as letras O' },
          { target: 'I', items: ['I', 'L', 'T', 'I', 'J', 'I'], description: 'Encontre todas as letras I' },
          { target: 'B', items: ['B', 'P', 'R', 'B', 'D', 'B'], description: 'Encontre todas as letras B' },
          { target: 'E', items: ['E', 'F', 'L', 'E', 'T', 'E'], description: 'Encontre todas as letras E' }
        ];

        const selectedGroup = visualGroups[Math.floor(Math.random() * visualGroups.length)];

        const visualContent = {
          currentGroup: selectedGroup,
          foundTargets: 0,
          totalTargets: 3, // Sempre 3 alvos para encontrar
          selectedItems: [] // Array para rastrear itens selecionados
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, visualContent), 500);

        return visualContent;
        
      default: // letter_selection
        // Garantir letra diferente da anterior
        let newTargetLetter;
        do {
          newTargetLetter = difficultyConfig.letters[Math.floor(Math.random() * difficultyConfig.letters.length)];
        } while (newTargetLetter === gameState.targetLetter);

        // Criar opções com a letra alvo + 2 distrativas
        const letterOptions = [newTargetLetter];
        const otherAvailable = difficultyConfig.letters.filter(l => l !== newTargetLetter);
        letterOptions.push(...shuffleArray(otherAvailable).slice(0, 2));

        const letterContent = {
          targetLetter: newTargetLetter,
          options: shuffleArray(letterOptions)
        };

        // Fornecer instruções TTS específicas
        setTimeout(() => provideActivityInstructions(activity, letterContent), 500);

        return letterContent;
    }
  }, []);

  // 🎯 Função auxiliar para embaralhar arrays
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }

  // 🎯 Função auxiliar para gerar distradores
  function generateDistracters(targetLetters, count) {
    const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    const available = allLetters.filter(letter => !targetLetters.includes(letter));
    return shuffleArray(available).slice(0, count);
  }

  // 🎮 FUNÇÃO SIMPLIFICADA PARA MUDAR ATIVIDADE
  const changeActivity = useCallback((activityId) => {
    console.log('🎯 Changing activity to:', activityId);

    setGameState(prev => {
      console.log('🔄 Current state before change:', {
        currentActivity: prev.currentActivity,
        targetActivity: activityId,
        currentActivityData: prev.activityData
      });

      // Usar a função generateActivityContent para gerar dados corretos
      const activityContent = generateActivityContent(activityId, prev.difficulty);
      console.log('📝 Generated activity content:', activityContent);

      let newActivityData = { ...prev.activityData };
      
      // Atualizar dados específicos da atividade usando generateActivityContent
      switch (activityId) {
        case 'word_formation':
          newActivityData.wordFormation = activityContent;
          break;
        case 'sequence_recognition':
          newActivityData.sequenceRecognition = activityContent;
          break;
        case 'visual_discrimination':
          newActivityData.visualDiscrimination = activityContent;
          break;
        case 'sound_matching':
          // Para sound_matching, atualizar targetLetter também
          return {
            ...prev,
            currentActivity: activityId,
            activityRoundCount: 0,
            selectedLetter: null,
            showFeedback: false,
            roundStartTime: Date.now(),
            targetLetter: activityContent.targetLetter,
            availableLetters: activityContent.options,
            activityData: newActivityData
          };
        default: // letter_selection
          return {
            ...prev,
            currentActivity: activityId,
            activityRoundCount: 0,
            selectedLetter: null,
            showFeedback: false,
            roundStartTime: Date.now(),
            targetLetter: activityContent.targetLetter,
            availableLetters: activityContent.options,
            activityData: newActivityData
          };
      }
      
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 0,
        selectedLetter: null,
        showFeedback: false,
        roundStartTime: Date.now(),
        activityData: newActivityData
      };
      
      console.log('✅ New state after change:', { 
        currentActivity: newState.currentActivity,
        activityData: newState.activityData
      });
      
      return newState;
    });
  }, [generateActivityContent]);

  // 🎯 Função auxiliar para embaralhar arrays
  function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }

  // 🎯 Função auxiliar para gerar distradores
  function generateDistracters(targetLetters, count) {
    const allLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    const available = allLetters.filter(letter => !targetLetters.includes(letter));
    return shuffleArray(available).slice(0, count);
  }

  // 🎯 NOVA FUNÇÃO DE GERAÇÃO DE RODADAS COM SISTEMA DE ATIVIDADES
  const generateNewRound = useCallback(() => {
    console.log('🎯 Gerando nova rodada...', { 
      currentActivity: gameState.currentActivity,
      activityRoundCount: gameState.activityRoundCount,
      round: gameState.round 
    });
    
    setGameState(prev => {
      // 🚫 REMOVIDO: Rotação automática de atividades - agora é controlada pelo usuário
      // O usuário precisa completar pelo menos 4 rodadas antes de poder trocar atividade
      
      console.log('🔄 Incrementando rodada da atividade:', { 
        currentActivity: prev.currentActivity,
        activityRoundCount: prev.activityRoundCount + 1,
        roundsPerActivity: prev.roundsPerActivity 
      });
      
      let newState = { 
        ...prev, 
        activityRoundCount: prev.activityRoundCount + 1 // Incrementar contador de rodadas
      };
      
      // Gerar conteúdo específico da atividade (sem rotação automática)
      const activityContent = generateActivityContent(newState.currentActivity, newState.difficulty);
      
      console.log('📝 Conteúdo gerado para atividade:', { 
        activity: newState.currentActivity, 
        content: activityContent 
      });
      
      // Atualizar estado baseado na atividade atual
      switch (newState.currentActivity) {
        case 'word_formation':
          newState.activityData.wordFormation = {
            currentWord: activityContent.currentWord,
            placedLetters: activityContent.placedLetters,
            availableLetters: activityContent.availableLetters
          };
          break;
          
        case 'sequence_recognition':
          newState.activityData.sequenceRecognition = {
            currentSequence: activityContent.currentSequence,
            userProgress: []
          };
          break;
          
        case 'visual_discrimination':
          newState.activityData.visualDiscrimination = {
            currentGroup: activityContent.currentGroup,
            foundTargets: activityContent.foundTargets,
            totalTargets: activityContent.totalTargets
          };
          break;
          
        default: // letter_selection e sound_matching
          newState.targetLetter = activityContent.targetLetter;
          newState.availableLetters = activityContent.options || 
            shuffleArray([...DIFFICULTIES[newState.difficulty].letters]).slice(0, 3);
      }
      
      return {
        ...newState,
        selectedLetter: null,
        showFeedback: false,
        roundStartTime: Date.now()
      };
    });
  }, [generateActivityContent, speak]); // Removidas dependências do gameState que causavam loop

  // Inicializar jogo com letra aleatória - CORRIGIDO
  useEffect(() => {
    if (!showStartScreen) {
      // Hub de coletores será usado apenas para coletar dados
      generateNewRound();
    }
    
    // Cleanup TTS ao desmontar
    return () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [showStartScreen]); // Removido generateNewRound para evitar loop infinito
  // 🎯 HANDLER PRINCIPAL ATUALIZADO PARA SISTEMA DE ATIVIDADES
  const handleLetterSelect = async (letterId) => {
    if (gameState.selectedLetter) return;

    setGameState(prev => ({ ...prev, selectedLetter: letterId }));
    
    const isCorrect = letterId === gameState.targetLetter.id;
    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;

    // 🔊 Pronunciar a letra selecionada primeiro
    speakLetter(letterId);

    // 📚 Coletar dados com sistema V3 expandido
    try {
      if (!collectorsHub.sessionActive && sessionIdRef.current) {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          gameMode: 'letter_recognition_v3',
          activityType: gameState.currentActivity,
          difficulty: gameState.difficulty
        });
      }

      // 📚 Registrar com coletores V3 - dados completos
      await collectorsHub.collectComprehensiveData({
        // Dados básicos
        targetLetter: gameState.targetLetter,
        selectedLetter: LETTERS.find(l => l.id === letterId),
        isCorrect,
        responseTime: reactionTime,
        sessionId: sessionIdRef.current,
        userId: user?.id || 'anonymous',
        
        // Dados V3 específicos
        activityType: gameState.currentActivity,
        activityRound: gameState.activityRoundCount || 0,
        activitiesCompleted: gameState.activitiesCompleted || [],
        
        // Dados da atividade atual
        activityData: gameState.activityData,
        
        // Métricas comportamentais V3
        behavioralMetrics: {
          ...gameState.behavioralMetrics,
          reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), reactionTime],
          accuracy: [...(gameState.behavioralMetrics.accuracy || []), isCorrect ? 1 : 0],
          attentionSpan: Date.now() - gameState.roundStartTime,
          currentLevel: gameState.currentLevel,
          totalAttempts: gameState.round,
          activitySpecific: {
            ...gameState.behavioralMetrics.activitySpecific,
            // Adicionar métricas específicas baseadas na atividade atual
            ...(gameState.currentActivity === 'word_formation' && {
              wordBuildingTime: reactionTime,
              letterSequenceAccuracy: gameState.activityData?.wordAccuracy || 0
            }),
            ...(gameState.currentActivity === 'visual_discrimination' && {
              visualScanTime: reactionTime,
              targetDetectionRate: gameState.activityData?.detectionRate || 0
            }),
            ...(gameState.currentActivity === 'sound_matching' && {
              audioProcessingTime: reactionTime,
              phoneticAccuracy: isCorrect ? 1 : 0
            }),
            ...(gameState.currentActivity === 'sequence_recognition' && {
              sequenceCompletionTime: reactionTime,
              patternRecognition: isCorrect ? 1 : 0
            })
          }
        },
        
        // Comportamento do jogador
        playerBehavior: {
          hesitationTime: reactionTime > 3000 ? reactionTime - 3000 : 0,
          confidence: reactionTime < 1000 ? 'high' : reactionTime < 3000 ? 'medium' : 'low',
          attemptPattern: gameState.round % 5 === 0 ? 'milestone' : 'regular'
        }
      });
    } catch (error) {
      console.warn('📚 LetterRecognition V3: Erro ao coletar dados:', error);
    }
    
    // 🔄 Registrar interação multissensorial V3
    if (multisensoryInitialized) {
      await recordMultisensoryInteraction('game_interaction', {
        interactionType: 'user_action',
        gameSpecificData: {
          targetLetter: gameState.targetLetter.id,
          selectedLetter: letterId,
          isCorrect,
          round: gameState.round,
          score: gameState.score,
          responseTime: reactionTime,
          activityType: gameState.currentActivity,
          activityRound: gameState.activityRoundCount || 0
        },
        multisensoryProcessing: {
          linguisticProcessing: { 
            letterRecognition: 0.8, 
            phonologicalAwareness: 0.7, 
            linguisticMemory: 0.7,
            activitySpecificSkill: calculateActivitySpecificSkill(gameState.currentActivity, isCorrect)
          },
          cognitiveProcessing: { 
            accuracy: isCorrect ? 1.0 : 0.0, 
            processingSpeed: reactionTime < 3000 ? 0.8 : 0.5, 
            adaptability: 0.7,
            activityComplexity: getActivityComplexity(gameState.currentActivity)
          },
          behavioralProcessing: { 
            interactionCount: gameState.round, 
            averageResponseTime: reactionTime, 
            consistency: 0.8,
            activityProgression: (gameState.activitiesCompleted?.length || 0) / 6
          }
        }
      });
    }

    // 🎯 Usar handlers universais V3
    if (isCorrect) {
      handleCorrectAnswer();
    } else {
      handleIncorrectAnswer();
    }
  };

  const startGame = useCallback(async (selectedDifficulty) => {
    console.log('🚀 STARTING GAME WITH DIFFICULTY:', selectedDifficulty);

    try {
      // PRIMEIRO: Esconder a tela inicial IMEDIATAMENTE
      setShowStartScreen(false);
      
      const difficultyKey = selectedDifficulty.toUpperCase();
      const difficultyConfig = DIFFICULTIES[difficultyKey];

      console.log('🎯 Difficulty config:', difficultyConfig);

      // Inicializar dados da atividade atual
      const initialActivityContent = generateActivityContent('letter_selection', difficultyKey);

      console.log('📝 Initial activity content:', initialActivityContent);

      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: difficultyKey,
        availableLetters: difficultyConfig.letters,
        allLetters: difficultyConfig.letters,
        score: 0,
        round: 1,
        accuracy: 100,
        targetLetter: initialActivityContent.targetLetter,
        roundStartTime: Date.now(),
        currentActivity: 'letter_selection',
        // Inicializar dados de todas as atividades
        activityData: {
          letterSelection: initialActivityContent,
          wordFormation: generateActivityContent('word_formation', difficultyKey),
          sequenceRecognition: generateActivityContent('sequence_recognition', difficultyKey),
          visualDiscrimination: generateActivityContent('visual_discrimination', difficultyKey),
          soundMatching: generateActivityContent('sound_matching', difficultyKey)
        }
      }));

      console.log('✅ Game started successfully!');

      // 📚 Inicializar coletores de reconhecimento de letras
      try {
        if (collectorsHub && typeof collectorsHub.initializeSession === 'function') {
          await collectorsHub.initializeSession(sessionIdRef.current, {
            difficulty: difficultyKey,
            availableLetters: difficultyConfig.letters.map(l => l.id),
            gameMode: 'letter_recognition'
          });
          console.log('📚 LetterRecognition: Coletores inicializados com sucesso');
        } else {
          console.log('📚 LetterRecognition: Coletores já ativos');
        }
      } catch (error) {
        console.warn('⚠️ LetterRecognition: Erro ao inicializar coletores:', error);
      }

      // 🔄 Inicializar integração multissensorial
      try {
        await initMultisensory(sessionIdRef.current, {
          difficulty: difficultyKey,
          gameMode: 'letter_recognition',
          availableLetters: difficultyConfig.letters.map(l => l.id),
          userId: user?.id || 'anonymous'
        });
      } catch (error) {
        console.warn('⚠️ Erro ao inicializar sessão multissensorial:', error);
      }
      
      // Iniciar sessão unificada Portal Betina V3
      if (portalReady) {
        startUnifiedSession({
          gameType: 'LetterRecognition',
          difficulty: difficultyKey,
          userId: user?.id || 'anonymous'
        });
      }
      
      // TTS automático ao iniciar
      setTimeout(() => {
        speak(`Bem-vindo ao Reconhecimento de Letras! Dificuldade: ${difficultyConfig.name}. Ouça o som da letra e encontre a letra correta. Vamos começar!`, {
          rate: 0.8
        });
      }, 1000);

    } catch (error) {
      console.error('❌ Error starting game:', error);
    }
  }, [initMultisensory, startUnifiedSession, portalReady, user, speak, generateActivityContent, collectorsHub]);
  const restartGame = () => {
    setGameState({
      status: 'menu',
      score: 0,
      round: 1,
      targetLetter: null,
      selectedLetter: null,
      showFeedback: false,
      accuracy: 100,
      totalRounds: 10,
      difficulty: 'EASY',
      availableLetters: DIFFICULTIES.EASY.letters,
      allLetters: DIFFICULTIES.EASY.letters,
      roundStartTime: null,
      
      // 🎯 Sistema de atividades
      currentActivity: ACTIVITY_TYPES.LETTER_SELECTION.id,
      activityCycle: [
        ACTIVITY_TYPES.LETTER_SELECTION.id,
        ACTIVITY_TYPES.SOUND_MATCHING.id,
        ACTIVITY_TYPES.WORD_FORMATION.id,
        ACTIVITY_TYPES.SEQUENCE_RECOGNITION.id,
        ACTIVITY_TYPES.VISUAL_DISCRIMINATION.id
      ],
      activityIndex: 0,
      roundsPerActivity: 2,
      activityRoundCount: 2, // Permitir troca imediata
      activitiesCompleted: [],
      
      // 🎯 Dados específicos de atividades
      activityData: {
        wordFormation: {
          currentWord: null,
          placedLetters: [],
          availableLetters: []
        },
        sequenceRecognition: {
          currentSequence: null,
          userProgress: []
        },
        visualDiscrimination: {
          currentGroup: null,
          foundTargets: 0,
          totalTargets: 0
        }
      },
      
      // 🎯 Métricas comportamentais avançadas
      behavioralMetrics: {
        activityPreferences: {},
        responsePatterns: [],
        adaptiveAdjustments: 0,
        engagementLevel: 1.0
      }
    });
    setShowStartScreen(true);
  };

  // 🔄 Detectar quando o jogo é completado e finalizar sessão multissensorial
  useEffect(() => {
    if (gameState.round > 10 && !showStartScreen) { // Assumindo que 10 é o número máximo de rounds
      const finalizeMultisensorySession = async () => {
        try {
          const multisensoryReport = await finalizeMultisensory({
            finalScore: gameState.score,
            finalAccuracy: gameState.accuracy,
            totalInteractions: gameState.round - 1,
            sessionDuration: Date.now() - (gameState.startTime || Date.now()),
            difficulty: gameState.difficulty
          });
          console.log('🔄 LetterRecognition: Relatório multissensorial final:', multisensoryReport);
        } catch (error) {
          console.warn('⚠️ LetterRecognition: Erro ao finalizar sessão multissensorial:', error);
        }
      };

      finalizeMultisensorySession();
    }
  }, [gameState.round, showStartScreen, multisensoryInitialized, gameState.score, gameState.accuracy, gameState.difficulty, gameState.startTime]);

  // 🔊 Explicar o jogo atual - ACESSIBILIDADE MELHORADA
  const explainGame = useCallback(() => {
    let explanation = "";

    switch (gameState.currentActivity) {
      case 'letter_selection':
        const letter = gameState.targetLetter;
        if (letter) {
          explanation = `Atividade: Encontrar Letra. Você deve encontrar a letra ${letter.letter}. ${letter.sound} de ${letter.example.split(' ')[1]}. Clique na letra ${letter.letter} quando encontrá-la.`;
        } else {
          explanation = "Atividade: Encontrar Letra. Procure a letra indicada e clique nela quando encontrar.";
        }
        break;

      case 'sound_matching':
        const soundLetter = gameState.activityData?.soundMatching?.targetLetter;
        if (soundLetter) {
          explanation = `Atividade: Combinação de Sons. Escute o som ${soundLetter.sound} e encontre a letra ${soundLetter.letter}. Clique na letra correta.`;
        } else {
          explanation = "Atividade: Combinação de Sons. Escute o som e encontre a letra correspondente.";
        }
        break;

      case 'word_formation':
        const wordData = gameState.activityData.wordFormation;
        if (wordData?.currentWord) {
          explanation = `Atividade: Formação de Palavras. Monte a palavra ${wordData.currentWord.meaning}. ${wordData.currentWord.emoji} ${wordData.currentWord.meaning}. Clique nas letras para formar ${wordData.currentWord.word}.`;
        } else {
          explanation = "Atividade: Formação de Palavras. Monte palavras clicando nas letras na ordem correta.";
        }
        break;

      case 'sequence_recognition':
        const sequenceData = gameState.activityData.sequenceRecognition;
        if (sequenceData?.currentSequence) {
          explanation = `Atividade: Sequência Alfabética. Complete a sequência ${sequenceData.currentSequence.sequence.join(', ')}. Qual letra vem depois?`;
        } else {
          explanation = "Atividade: Sequência Alfabética. Complete sequências de letras do alfabeto.";
        }
        break;

      case 'visual_discrimination':
        const visualData = gameState.activityData.visualDiscrimination;
        if (visualData?.currentGroup) {
          explanation = `Atividade: Discriminação Visual. ${visualData.currentGroup.description}. Clique em todas as letras ${visualData.currentGroup.target} que encontrar.`;
        } else {
          explanation = "Atividade: Discriminação Visual. Encontre todas as letras iguais à letra alvo.";
        }
        break;

      default:
        explanation = "Bem-vindo ao jogo de reconhecimento de letras! Aqui você vai aprender o alfabeto de forma divertida. Use o menu para escolher diferentes atividades.";
    }

    speak(explanation);

    // Registrar uso de acessibilidade
    if (portalReady) {
      recordInteraction({
        type: 'tts_usage',
        data: {
          text: explanation,
          activity: gameState.currentActivity,
          round: gameState.round
        }
      });
    }
  }, [gameState.currentActivity, gameState.targetLetter, gameState.activityData, gameState.round, speak, portalReady, recordInteraction]);

  // 🔊 Repetir a instrução da atividade atual
  const repeatInstruction = useCallback(() => {
    // Fornecer instruções específicas baseadas na atividade atual
    provideActivityInstructions(gameState.currentActivity, gameState.activityData[gameState.currentActivity]);
  }, [gameState.currentActivity, gameState.activityData, provideActivityInstructions]);

  // 🧪 Teste de acessibilidade - simula criança neurodivergente usando apenas TTS
  const testAccessibility = useCallback(() => {
    const testMessage = `
      Teste de Acessibilidade Ativado!

      Simulando criança neurodivergente usando apenas instruções de áudio.

      Atividade atual: ${ACTIVITY_TYPES[gameState.currentActivity]?.name || 'Desconhecida'}

      Instruções sendo fornecidas automaticamente...
    `;

    speak(testMessage, { rate: 0.8 });

    // Fornecer instruções específicas após 2 segundos
    setTimeout(() => {
      provideActivityInstructions(gameState.currentActivity, gameState.activityData[gameState.currentActivity]);
    }, 3000);

  }, [gameState.currentActivity, gameState.activityData, speak, provideActivityInstructions]);

  // 🎉 Feedback sonoro para acertos/erros
  const playFeedback = useCallback((isCorrect, selectedLetter) => {
    if (isCorrect) {
      speak("Muito bem! Você acertou!", { pitch: 1.3, rate: 0.9 });
    } else {
      const targetLetter = gameState.targetLetter;
      const selectedLetterData = LETTERS.find(l => l.id === selectedLetter);
      speak(`Não foi dessa vez. Você escolheu ${selectedLetterData?.sound}, mas a resposta correta é ${targetLetter?.sound}`, { 
        pitch: 0.9, 
        rate: 0.7 
      });
    }
  }, [gameState.targetLetter, speak]);

  // 🎵 Pronunciar letra ao clicar na opção
  const speakLetter = useCallback((letterId) => {
    const letter = LETTERS.find(l => l.id === letterId);
    if (letter) {
      speak(letter.sound, { rate: 0.6, pitch: 1.2 });
    }
  }, [speak]);

  // ==================== MÉTODOS AUXILIARES V3 ====================

  /**
   * Calcula habilidade específica da atividade
   */
  const calculateActivitySpecificSkill = (activityType, isCorrect) => {
    const baseScore = isCorrect ? 0.8 : 0.3;
    
    const skillMultipliers = {
      'letter_selection': 1.0,
      'sound_matching': 1.2,
      'word_formation': 1.5,
      'sequence_recognition': 1.3,
      'visual_discrimination': 1.4
    };
    
    return baseScore * (skillMultipliers[activityType] || 1.0);
  };

  /**
   * Obtém complexidade da atividade
   */
  const getActivityComplexity = (activityType) => {
    const complexityMap = {
      'letter_selection': 0.3,
      'sound_matching': 0.5,
      'word_formation': 0.8,
      'sequence_recognition': 0.6,
      'visual_discrimination': 0.7
    };
    
    return complexityMap[activityType] || 0.5;
  };

  /**
   * Atualiza métricas comportamentais específicas da atividade
   */
  const updateActivitySpecificMetrics = (activityType, isCorrect, responseTime) => {
    const updates = {};
    
    switch (activityType) {
      case 'word_formation':
        updates.wordBuildingTime = [...(gameState.behavioralMetrics.activitySpecific?.wordBuildingTime || []), responseTime];
        updates.letterSequenceAccuracy = [...(gameState.behavioralMetrics.activitySpecific?.letterSequenceAccuracy || []), isCorrect ? 1 : 0];
        break;
      
      case 'visual_discrimination':
        updates.visualScanTime = [...(gameState.behavioralMetrics.activitySpecific?.visualScanTime || []), responseTime];
        updates.targetDetectionRate = [...(gameState.behavioralMetrics.activitySpecific?.targetDetectionRate || []), isCorrect ? 1 : 0];
        break;
      
      case 'sound_matching':
        updates.audioProcessingTime = [...(gameState.behavioralMetrics.activitySpecific?.audioProcessingTime || []), responseTime];
        updates.phoneticAccuracy = [...(gameState.behavioralMetrics.activitySpecific?.phoneticAccuracy || []), isCorrect ? 1 : 0];
        break;
      
      case 'sequence_recognition':
        updates.sequenceCompletionTime = [...(gameState.behavioralMetrics.activitySpecific?.sequenceCompletionTime || []), responseTime];
        updates.patternRecognition = [...(gameState.behavioralMetrics.activitySpecific?.patternRecognition || []), isCorrect ? 1 : 0];
        break;
      
      default:
        // letter_selection ou outras atividades
        updates.basicProcessingTime = [...(gameState.behavioralMetrics.activitySpecific?.basicProcessingTime || []), responseTime];
        break;
    }
    
    return updates;
  };

  // ==================== HANDLERS ESPECÍFICOS DAS ATIVIDADES V3 ====================

  /**
   * 🔗 Handler para seleção de letras na formação de palavras - CORRIGIDO
   */
  const handleWordLetterSelect = async (letter, letterIndex) => {
    console.log('🎯 Word Letter Select:', { letter, letterIndex, selectedLetter: gameState.selectedLetter });
    
    if (gameState.selectedLetter) {
      console.log('⏳ Already processing a selection, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const wordData = gameState.activityData.wordFormation;
    const currentWord = wordData.currentWord;
    
    // Encontrar próxima posição vazia
    const placedLetters = [...(wordData.placedLetters || [])];
    let nextEmptyIndex = placedLetters.findIndex(slot => slot === null || slot === undefined);
    
    if (nextEmptyIndex === -1) {
      console.log('⚠️ Word already complete');
      return; // Palavra já completa
    }
    
    const expectedLetter = currentWord.word[nextEmptyIndex];
    const isCorrect = letter.toUpperCase() === expectedLetter.toUpperCase();
    
    console.log('🔍 Word formation check:', {
      letter: letter.toUpperCase(),
      expected: expectedLetter.toUpperCase(),
      position: nextEmptyIndex,
      isCorrect
    });
    
    if (isCorrect) {
      // Colocar a letra na posição correta
      placedLetters[nextEmptyIndex] = letter.toUpperCase();
      
      setGameState(prev => ({
        ...prev,
        selectedLetter: letter,
        activityData: {
          ...prev.activityData,
          wordFormation: {
            ...prev.activityData.wordFormation,
            placedLetters
          }
        }
      }));

      // Verificar se a palavra está completa
      const isWordComplete = placedLetters.every(slot => slot !== null && slot !== undefined);
      if (isWordComplete) {
        console.log('✅ Word completed!');
        setTimeout(() => {
          handleCorrectAnswer();
        }, 1000); // Delay para mostrar a palavra completa
      } else {
        // Reset selection after successful placement
        setTimeout(() => {
          setGameState(prev => ({ ...prev, selectedLetter: null }));
        }, 500);
      }
    } else {
      console.log('❌ Wrong letter for word formation');
      setGameState(prev => ({ ...prev, selectedLetter: letter }));
      handleIncorrectAnswer();
    }
  };

  /**
   * 📝 Handler para reconhecimento de sequência
   */
  const handleSequenceSelect = async (letter) => {
    console.log('🎯 Sequence Select:', { letter, selectedLetter: gameState.selectedLetter });
    
    if (gameState.selectedLetter) {
      console.log('⏳ Already processing a selection, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const sequenceData = gameState.activityData.sequenceRecognition;
    const currentSequence = sequenceData.currentSequence;
    const expectedLetter = currentSequence?.missing;
    const isCorrect = letter.toUpperCase() === expectedLetter?.toUpperCase();

    console.log('🔍 Sequence check:', {
      letter: letter.toUpperCase(),
      expected: expectedLetter?.toUpperCase(),
      isCorrect
    });

    setGameState(prev => ({
      ...prev,
      selectedLetter: letter
    }));

    if (isCorrect) {
      console.log('✅ Correct sequence letter!');
      setTimeout(() => {
        handleCorrectAnswer();
      }, 1000);
    } else {
      console.log('❌ Wrong sequence letter');
      setTimeout(() => {
        handleIncorrectAnswer();
      }, 1000);
    }
  };

  /**
   * 👁️ Handler para discriminação visual
   */
  const handleVisualSelect = async (letter, itemIndex) => {
    console.log('🎯 Visual Select:', { letter, itemIndex, selectedLetter: gameState.selectedLetter });
    
    // Para discriminação visual, permitimos seleções múltiplas das letras alvo
    const selectionKey = `${letter}-${itemIndex}`;
    
    // Verificar se este item já foi selecionado
    const visualData = gameState.activityData.visualDiscrimination;
    if (visualData.selectedItems?.includes(selectionKey)) {
      console.log('⚠️ Item already selected, skipping...');
      return;
    }

    const reactionTime = gameState.roundStartTime ? (Date.now() - gameState.roundStartTime) : 0;
    const currentGroup = visualData.currentGroup;
    const targetLetter = currentGroup?.target;
    const isCorrect = letter.toUpperCase() === targetLetter?.toUpperCase();

    console.log('🔍 Visual discrimination check:', {
      letter: letter.toUpperCase(),
      target: targetLetter?.toUpperCase(),
      isCorrect,
      currentFound: visualData.foundTargets
    });

    setGameState(prev => ({
      ...prev,
      selectedLetter: selectionKey,
      activityData: {
        ...prev.activityData,
        visualDiscrimination: {
          ...prev.activityData.visualDiscrimination,
          foundTargets: isCorrect ? prev.activityData.visualDiscrimination.foundTargets + 1 : prev.activityData.visualDiscrimination.foundTargets,
          selectedItems: [...(prev.activityData.visualDiscrimination.selectedItems || []), selectionKey]
        }
      }
    }));

    if (isCorrect) {
      // Verificar se encontrou todas as letras alvo
      const newFoundCount = visualData.foundTargets + 1;
      console.log(`✅ Correct target found! (${newFoundCount}/${visualData.totalTargets})`);

      if (newFoundCount >= visualData.totalTargets) {
        console.log('🎉 All targets found!');
        setTimeout(() => {
          handleCorrectAnswer();
        }, 1500);
      } else {
        // Reset selection para permitir próxima seleção
        setTimeout(() => {
          setGameState(prev => ({ ...prev, selectedLetter: null }));
        }, 500);
      }
    } else {
      console.log('❌ Wrong letter selected in visual discrimination');
      // Para seleções incorretas, apenas marcar como incorreta mas permitir continuar
      setTimeout(() => {
        setGameState(prev => ({ ...prev, selectedLetter: null }));
      }, 1000);
    }
  };

  /**
   * Função auxiliar para coletar dados específicos da atividade
   */
  const collectActivityData = async (data) => {
    try {
      if (!collectorsHub.sessionActive && sessionIdRef.current) {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          gameMode: 'letter_recognition_v3',
          activityType: data.activityType,
          difficulty: gameState.difficulty
        });
      }

      // Preparar métricas comportamentais específicas
      const activitySpecificMetrics = updateActivitySpecificMetrics(
        data.activityType, 
        data.isCorrect, 
        data.responseTime
      );

      await collectorsHub.collectComprehensiveData({
        // Dados básicos
        targetLetter: { id: data.targetLetter, letter: data.targetLetter },
        selectedLetter: { id: data.selectedLetter, letter: data.selectedLetter },
        isCorrect: data.isCorrect,
        responseTime: data.responseTime,
        sessionId: sessionIdRef.current,
        userId: user?.id || 'anonymous',
        
        // Dados V3 específicos
        activityType: data.activityType,
        activityRound: gameState.activityRoundCount || 0,
        activitiesCompleted: gameState.activitiesCompleted || [],
        
        // Dados da atividade atual
        activityData: {
          ...gameState.activityData,
          ...data.activitySpecificData
        },
        
        // Métricas comportamentais V3
        behavioralMetrics: {
          ...gameState.behavioralMetrics,
          reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), data.responseTime],
          accuracy: [...(gameState.behavioralMetrics.accuracy || []), data.isCorrect ? 1 : 0],
          attentionSpan: Date.now() - gameState.roundStartTime,
          activitySpecific: {
            ...gameState.behavioralMetrics.activitySpecific,
            ...activitySpecificMetrics
          }
        },
        
        // Comportamento do jogador
        playerBehavior: {
          hesitationTime: data.responseTime > 3000 ? data.responseTime - 3000 : 0,
          confidence: data.responseTime < 1000 ? 'high' : data.responseTime < 3000 ? 'medium' : 'low',
          activityEngagement: calculateActivityEngagement(data.activityType, data.responseTime)
        }
      });

      // Registrar interação multissensorial
      if (multisensoryInitialized) {
        await recordMultisensoryInteraction('activity_interaction', {
          interactionType: 'activity_specific',
          gameSpecificData: {
            activityType: data.activityType,
            targetLetter: data.targetLetter,
            selectedLetter: data.selectedLetter,
            isCorrect: data.isCorrect,
            responseTime: data.responseTime,
            ...data.activitySpecificData
          },
          multisensoryProcessing: {
            linguisticProcessing: {
              activitySpecificSkill: calculateActivitySpecificSkill(data.activityType, data.isCorrect),
              complexityHandling: getActivityComplexity(data.activityType)
            },
            cognitiveProcessing: {
              accuracy: data.isCorrect ? 1.0 : 0.0,
              processingSpeed: data.responseTime < 3000 ? 0.8 : 0.5,
              activityAdaptation: calculateActivityAdaptation(data.activityType, gameState.activityRoundCount || 0)
            },
            behavioralProcessing: {
              activityEngagement: calculateActivityEngagement(data.activityType, data.responseTime),
              progressionRate: (gameState.activitiesCompleted?.length || 0) / 6
            }
          }
        });
      }
    } catch (error) {
      console.warn(`📚 LetterRecognition V3: Erro ao coletar dados da atividade ${data.activityType}:`, error);
    }
  };

  // ==================== FUNÇÕES AUXILIARES ESPECÍFICAS ====================

  /**
   * Obtém letra esperada na sequência
   */
  const getExpectedSequenceLetter = (sequence, missingIndex) => {
    if (!sequence || sequence.length === 0) return null;
    
    if (missingIndex === 0 && sequence.length > 1) {
      return String.fromCharCode(sequence[1].charCodeAt(0) - 1);
    } else if (missingIndex === sequence.length) {
      return String.fromCharCode(sequence[sequence.length - 1].charCodeAt(0) + 1);
    } else if (missingIndex > 0 && missingIndex < sequence.length) {
      // Interpolação para posição no meio
      const prevChar = sequence[missingIndex - 1];
      const nextChar = sequence[missingIndex + 1];
      return String.fromCharCode(Math.round((prevChar.charCodeAt(0) + nextChar.charCodeAt(0)) / 2));
    }
    
    return null;
  };

  /**
   * Identifica padrão da sequência
   */
  const identifySequencePattern = (sequence) => {
    if (sequence.length < 2) return 'simple';
    
    const differences = [];
    for (let i = 1; i < sequence.length; i++) {
      differences.push(sequence[i].charCodeAt(0) - sequence[i-1].charCodeAt(0));
    }
    
    if (differences.every(diff => diff === 1)) return 'consecutive';
    if (differences.every(diff => diff === differences[0] && diff > 1)) return 'skip';
    if (differences.every(diff => diff < 0)) return 'reverse';
    return 'complex';
  };

  /**
   * Calcula similaridade fonética
   */
  const calculatePhoneticSimilarity = (sounds) => {
    if (!sounds || sounds.length < 2) return 0.5;
    
    const sound1 = sounds[0]?.toLowerCase();
    const sound2 = sounds[1]?.toLowerCase();
    
    // Análise simplificada de similaridade fonética
    if (sound1 === sound2) return 1.0;
    
    const similarPairs = [
      ['b', 'p'], ['d', 't'], ['g', 'k'], ['f', 'v'], ['s', 'z'], ['m', 'n']
    ];
    
    const areSimilar = similarPairs.some(pair => 
      (pair[0] === sound1 && pair[1] === sound2) || 
      (pair[1] === sound1 && pair[0] === sound2)
    );
    
    return areSimilar ? 0.8 : 0.3;
  };

  /**
   * Obtém complexidade auditiva
   */
  const getAuditoryComplexity = (phoneticPair) => {
    const sounds = phoneticPair.sounds || [];
    const similarity = calculatePhoneticSimilarity(sounds);
    
    // Maior complexidade = maior similaridade (mais difícil de discriminar)
    return similarity;
  };

  /**
   * Calcula engajamento na atividade
   */
  const calculateActivityEngagement = (activityType, responseTime) => {
    const optimalTimes = {
      'letter_selection': 2000,
      'sound_matching': 3000,
      'word_formation': 5000,
      'sequence_recognition': 4000,
      'visual_discrimination': 6000
    };
    
    const optimalTime = optimalTimes[activityType] || 3000;
    const timeRatio = Math.abs(responseTime - optimalTime) / optimalTime;
    
    return Math.max(0.2, 1 - timeRatio);
  };

  /**
   * Calcula adaptação à atividade
   */
  const calculateActivityAdaptation = (activityType, activityRound) => {
    // Adaptação melhora com rounds na mesma atividade
    const adaptationBonus = Math.min(activityRound * 0.1, 0.3);
    const baseAdaptation = 0.5;
    
    return Math.min(baseAdaptation + adaptationBonus, 1.0);
  };

  // ==================== HANDLERS DE RESPOSTA ====================

  /**
   * ✅ Handler para resposta correta
   */
  const handleCorrectAnswer = useCallback(() => {
    setGameState(prev => {
      const newScore = prev.score + 10;
      const newRound = prev.round + 1;
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / 10) + 1;
      const newAccuracy = Math.round((correctAnswers / totalAttempts) * 100);

      return {
        ...prev,
        selectedLetter: prev.targetLetter?.id || prev.selectedLetter,
        showFeedback: true,
        score: newScore,
        round: newRound,
        accuracy: newAccuracy,
        activityRoundCount: prev.activityRoundCount + 1
      };
    });

    speak("Muito bem! Resposta correta!", { pitch: 1.3, rate: 0.9 });
    
    // ✅ Avanço automático para próxima pergunta (não próximo jogo)
    setTimeout(() => {
      console.log('🎯 Avançando para próxima pergunta automaticamente...');
      setGameState(prev => ({
        ...prev,
        showFeedback: false,
        selectedLetter: null // Reset selection
      }));
      generateNewRound();
    }, 1500); // Tempo reduzido para progressão mais rápida
  }, [speak, generateNewRound]);

  /**
   * ❌ Handler para resposta incorreta
   */
  const handleIncorrectAnswer = useCallback(() => {
    setGameState(prev => {
      const totalAttempts = prev.round;
      const correctAnswers = Math.floor(prev.score / 10);
      const newAccuracy = totalAttempts > 0 ? Math.round((correctAnswers / totalAttempts) * 100) : 100;

      return {
        ...prev,
        showFeedback: true,
        accuracy: newAccuracy,
        round: prev.round + 1,
        activityRoundCount: prev.activityRoundCount + 1
      };
    });

    speak("Tente novamente! Você consegue!", { pitch: 1.0, rate: 0.8 });
    setTimeout(() => {
      setGameState(prev => ({ ...prev, showFeedback: false }));
      generateNewRound();
    }, 1500); // Tempo reduzido para progressão mais rápida
  }, [speak, generateNewRound]);

  // ==================== COLETORES DE DADOS DAS ATIVIDADES ====================

  /**
   * Inicializa a sessão do coletor para reconhecimento de letras
   */
  const initializeCollectors = async () => {
    try {
      if (collectorsHub && typeof collectorsHub.initializeSession === 'function') {
        await collectorsHub.initializeSession(sessionIdRef.current, {
          difficulty: gameState.difficulty,
          availableLetters: gameState.availableLetters.map(l => l.id),
          gameMode: 'letter_recognition'
        });
        console.log('📚 LetterRecognition: Coletores inicializados com sucesso');
      }
    } catch (error) {
      console.warn('⚠️ LetterRecognition: Erro ao inicializar coletores:', error);
    }
  };

  /**
   * Registra interação com os coletores de dados
   */
  const registerDataCollectors = async (isCorrect, reactionTime) => {
    try {
      if (collectorsHub.sessionActive) {
        await collectorsHub.collectComprehensiveData({
          // Dados básicos
          targetLetter: gameState.targetLetter,
          selectedLetter: LETTERS.find(l => l.id === gameState.selectedLetter),
          isCorrect,
          responseTime: reactionTime,
          sessionId: sessionIdRef.current,
          userId: user?.id || 'anonymous',
          
          // Dados V3 específicos
          activityType: gameState.currentActivity,
          activityRound: gameState.activityRoundCount || 0,
          activitiesCompleted: gameState.activitiesCompleted || [],
          
          // Dados da atividade atual
          activityData: gameState.activityData,
          
          // Métricas comportamentais V3
          behavioralMetrics: {
            ...gameState.behavioralMetrics,
            reactionTime: [...(gameState.behavioralMetrics.reactionTime || []), reactionTime],
            accuracy: [...(gameState.behavioralMetrics.accuracy || []), isCorrect ? 1 : 0],
            attentionSpan: Date.now() - gameState.roundStartTime,
            currentLevel: gameState.currentLevel,
            totalAttempts: gameState.round,
            activitySpecific: {
              ...gameState.behavioralMetrics.activitySpecific,
              // Adicionar métricas específicas baseadas na atividade atual
              ...(gameState.currentActivity === 'word_formation' && {
                wordBuildingTime: reactionTime,
                letterSequenceAccuracy: gameState.activityData?.wordAccuracy || 0
              }),
              ...(gameState.currentActivity === 'visual_discrimination' && {
                visualScanTime: reactionTime,
                targetDetectionRate: gameState.activityData?.detectionRate || 0
              }),
              ...(gameState.currentActivity === 'sound_matching' && {
                audioProcessingTime: reactionTime,
                phoneticAccuracy: isCorrect ? 1 : 0
              }),
              ...(gameState.currentActivity === 'sequence_recognition' && {
                sequenceCompletionTime: reactionTime,
                patternRecognition: isCorrect ? 1 : 0
              })
            }
          },
          
          // Comportamento do jogador
          playerBehavior: {
            hesitationTime: reactionTime > 3000 ? reactionTime - 3000 : 0,
            confidence: reactionTime < 1000 ? 'high' : reactionTime < 3000 ? 'medium' : 'low',
            attemptPattern: gameState.round % 5 === 0 ? 'milestone' : 'regular'
          }
        });
      }
    } catch (error) {
      console.warn('📚 LetterRecognition V3: Erro ao registrar interação:', error);
    }
  };

  // Resto do código...

  // 🎯 RENDERIZADOR PRINCIPAL DE ATIVIDADES
  const renderCurrentActivity = () => {
    switch (gameState.currentActivity) {
      case 'letter_selection':
        return renderLetterSelection();
      case 'sound_matching':
        return renderSoundMatching();
      case 'word_formation':
        return renderWordFormation();
      case 'sequence_recognition':
        return renderSequenceRecognition();
      case 'visual_discrimination':
        return renderVisualDiscrimination();
      default:
        return renderLetterSelection();
    }
  };

  // 🔤 COMPONENTE: Seleção de Letras - LAYOUT MEMORYGAME (funcionalidade original)
  const renderLetterSelection = () => (
    <div className={styles.gameArea}>
      {gameState.targetLetter && (
        <div className={styles.soundActivity}>
          <div className={styles.soundIndicator}>🔊</div>
          <h3>Encontre a letra: <strong>{gameState.targetLetter.letter}</strong></h3>
          <p className={styles.activityTip}>
            Som: "{gameState.targetLetter.sound}" | Exemplo: {gameState.targetLetter.example}
          </p>
          {ttsActive && (
            <button
              className={styles.soundButton}
              onClick={repeatInstruction}
              title="Repetir instrução"
            >
              🔊 Repetir Som
            </button>
          )}
        </div>
      )}

      <div className={styles.lettersGrid}>
        {gameState.availableLetters.map((letter) => (
          <div
            key={letter.id}
            className={`${styles.letterCard} ${
              gameState.selectedLetter === letter.id ? 'selected' : ''
            } ${
              gameState.selectedLetter === letter.id && gameState.selectedLetter === gameState.targetLetter.id ? 'correct' : ''
            } ${
              gameState.selectedLetter === letter.id && gameState.selectedLetter !== gameState.targetLetter.id ? 'incorrect' : ''
            }`}
            onClick={() => handleLetterSelect(letter.id)}
            onMouseEnter={() => {
              if (ttsActive && !gameState.selectedLetter) {
                speakLetter(letter.id);
              }
            }}
            onTouchStart={() => {
              if (ttsActive && !gameState.selectedLetter) {
                speakLetter(letter.id);
              }
            }}
            title={`Letra ${letter.letter} - ${letter.sound}`}
          >
            <div className={styles.letterContent}>{letter.letter}</div>
            <div className={styles.letterLabel}>{letter.sound}</div>
          </div>
        ))}
      </div>
    </div>
  );

  // 🎵 COMPONENTE: Combinação de Sons - LAYOUT MEMORYGAME (funcionalidade original)
  const renderSoundMatching = () => (
    <div className={styles.gameArea}>
      <div className={styles.soundActivity}>
        <div className={styles.soundIndicator}>🎵</div>
        <h3>Que letra faz este som?</h3>
        <button
          className={styles.soundButton}
          onClick={() => {
            if (gameState.targetLetter) {
              speak(gameState.targetLetter.sound, { rate: 0.7, pitch: 1.1 });
            }
          }}
        >
          🔊 Tocar Som
        </button>
        <p className={styles.activityTip}>
          Ouça o som e encontre a letra correspondente
        </p>
      </div>

      <div className={styles.lettersGrid}>
        {gameState.availableLetters.map((letter) => (
          <div
            key={letter.id}
            className={`${styles.letterCard} ${gameState.selectedLetter === letter.id ? 'selected' : ''}`}
            onClick={() => handleLetterSelect(letter.id)}
            onMouseEnter={() => {
              if (ttsActive && !gameState.selectedLetter) {
                speak(letter.sound, { rate: 0.6, pitch: 1.1 });
              }
            }}
            onTouchStart={() => {
              if (ttsActive && !gameState.selectedLetter) {
                speak(letter.sound, { rate: 0.6, pitch: 1.1 });
              }
            }}
            title={`Letra ${letter.letter} - Som ${letter.sound}`}
          >
            <div className={styles.letterContent}>{letter.letter}</div>
            <div className={styles.letterLabel}>/{letter.sound.toLowerCase()}/</div>
          </div>
        ))}
      </div>
    </div>
  );

  // 🔗 COMPONENTE: Formação de Palavras - CORRIGIDO COM LAYOUT PADRÃO
  const renderWordFormation = () => {
    const wordData = gameState.activityData.wordFormation;
    
    console.log('🔤 Rendering Word Formation - Debug:', {
      wordData,
      gameState: gameState.activityData,
      currentActivity: gameState.currentActivity
    });
    
    // Verificar se dados estão inicializados
    if (!wordData || !wordData.currentWord) {
      console.log('🚨 Word formation data not initialized:', wordData);
      return (
        <div className={styles.wordFormationActivity}>
          <div className={styles.activityInstruction}>
            <h3>🔄 Carregando formação de palavras...</h3>
            <p>Aguarde enquanto preparamos as palavras para você!</p>
          </div>
        </div>
      );
    }

    const placedLetters = wordData.placedLetters || [];
    const nextEmptyIndex = placedLetters.findIndex(slot => slot === null || slot === undefined);
    const isWordComplete = placedLetters.every(slot => slot !== null && slot !== undefined);

    console.log('🔤 Rendering word formation:', {
      currentWord: wordData.currentWord,
      placedLetters,
      availableLetters: wordData.availableLetters,
      nextEmptyIndex,
      isWordComplete
    });

    return (
      <div className={styles.gameArea}>
        <div className={styles.soundActivity}>
          <div className={styles.soundIndicator}>{wordData.currentWord.emoji}</div>
          <h3>Monte a palavra: <strong>{wordData.currentWord.word}</strong></h3>
          <p className={styles.activityTip}>
            {wordData.currentWord.meaning} - Clique nas letras na ordem correta
          </p>
          {ttsEnabled && (
            <button
              className={styles.soundButton}
              onClick={() => {
                speak(`Monte a palavra ${wordData.currentWord.word}. ${wordData.currentWord.meaning}. Clique nas letras na ordem correta.`);
              }}
              title="Ouvir instrução"
              aria-label="Ouvir como formar a palavra"
            >
              🔊 Ouvir Instrução
            </button>
          )}
        </div>

        {/* Slots da palavra - padrão MemoryGame */}
        <div className={styles.lettersGrid} style={{ marginBottom: '2rem' }}>
          {wordData.currentWord.word.split('').map((correctLetter, index) => (
            <div
              key={index}
              className={`${styles.letterCard} ${
                placedLetters[index] ? 'correct' : ''
              } ${
                index === nextEmptyIndex ? 'active' : ''
              }`}
              style={{
                background: placedLetters[index]
                  ? 'var(--success-bg)'
                  : index === nextEmptyIndex
                    ? 'rgba(255, 193, 7, 0.2)'
                    : 'var(--card-background)',
                border: placedLetters[index]
                  ? '2px solid var(--success-border)'
                  : index === nextEmptyIndex
                    ? '2px solid #FFC107'
                    : '2px dashed rgba(255, 255, 255, 0.5)',
                minHeight: '80px',
                cursor: 'default'
              }}
            >
              <div className={styles.letterContent}>
                {placedLetters[index] || '_'}
              </div>
              <div className={styles.letterLabel}>
                Posição {index + 1}
              </div>
            </div>
          ))}
        </div>

        {/* Letras disponíveis - padrão MemoryGame */}
        <div className={styles.lettersGrid}>
          {wordData.availableLetters.map((letter, letterIndex) => (
            <div
              key={`${letter}-${letterIndex}`}
              className={`${styles.letterCard}`}
              onClick={() => handleWordLetterSelect(letter, letterIndex)}
              onMouseEnter={() => {
                if (ttsActive) {
                  speak(letter, { rate: 0.6, pitch: 1.1 });
                }
              }}
              onTouchStart={() => {
                if (ttsActive) {
                  speak(letter, { rate: 0.6, pitch: 1.1 });
                }
              }}
              title={`Letra ${letter}`}
              style={{
                cursor: 'pointer'
              }}
            >
              <div className={styles.letterContent}>{letter}</div>
              <div className={styles.letterLabel}>
                Clique para usar
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 📝 COMPONENTE: Reconhecimento de Sequência - SEGUINDO PADRÃO MEMORYGAME
  const renderSequenceRecognition = () => {
    const sequenceData = gameState.activityData.sequenceRecognition;
    const currentSequence = sequenceData?.currentSequence;

    console.log('🔍 Rendering sequence recognition:', { sequenceData, currentSequence });

    if (!currentSequence) {
      return (
        <div className={styles.gameArea}>
          <div className={styles.soundActivity}>
            <div className={styles.soundIndicator}>📝</div>
            <h3>Carregando sequência...</h3>
            <p className={styles.activityTip}>Dados: {JSON.stringify(sequenceData)}</p>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.gameArea}>
        <div className={styles.soundActivity}>
          <div className={styles.soundIndicator}>📝</div>
          <h3>Complete a sequência alfabética:</h3>

          {/* Sequência ampliada e mais visível */}
          <div className={styles.sequenceDisplay}>
            {currentSequence.sequence.map((letter, index) => (
              <span key={index} className={styles.sequenceLetter}>
                {letter}
              </span>
            ))}
            <span className={styles.sequenceLetter} style={{
              color: '#FFD700',
              fontSize: '3rem',
              animation: 'pulse 1.5s infinite'
            }}>
              ?
            </span>
          </div>

          <p className={styles.activityTip}>
            Qual letra vem depois de {currentSequence.sequence.join(', ')}?
          </p>
        </div>

        <div className={styles.lettersGrid}>
          {currentSequence.options.map((option) => (
            <div
              key={option}
              className={`${styles.letterCard} ${
                sequenceData?.userAnswer === option ? styles.selected : ''
              }`}
              onClick={() => {
                // Atualizar resposta do usuário
                setGameState(prev => ({
                  ...prev,
                  activityData: {
                    ...prev.activityData,
                    sequenceRecognition: {
                      ...prev.activityData.sequenceRecognition,
                      userAnswer: option
                    }
                  }
                }));

                // Feedback TTS específico e progressão do jogo
                if (option === currentSequence.missing) {
                  speak(`Correto! ${option} vem depois de ${currentSequence.sequence.join(', ')}.`, { rate: 0.8 });

                  // Atualizar pontuação e avançar para próxima rodada
                  setTimeout(() => {
                    setGameState(prev => ({
                      ...prev,
                      score: prev.score + 10,
                      round: prev.round + 1,
                      selectedLetter: null,
                      showFeedback: false
                    }));

                    // Gerar nova rodada após feedback
                    setTimeout(() => {
                      generateNewRound();
                    }, 500);
                  }, 1500);

                } else {
                  speak(`${option} selecionado. Pense na ordem do alfabeto. A resposta correta é ${currentSequence.missing}.`, { rate: 0.8 });

                  // Mostrar resposta correta e avançar
                  setTimeout(() => {
                    setGameState(prev => ({
                      ...prev,
                      round: prev.round + 1,
                      selectedLetter: null,
                      showFeedback: false
                    }));

                    // Gerar nova rodada após mostrar resposta
                    setTimeout(() => {
                      generateNewRound();
                    }, 500);
                  }, 2000);
                }
              }}
              onMouseEnter={() => {
                if (ttsActive && !sequenceData?.userAnswer) {
                  speak(`Letra ${option}`, { rate: 0.7, pitch: 1.1 });
                }
              }}
              onTouchStart={() => {
                if (ttsActive && !sequenceData?.userAnswer) {
                  speak(`Letra ${option}`, { rate: 0.7, pitch: 1.1 });
                }
              }}
              title={`Letra ${option}`}
            >
              <div className={styles.letterContent}>{option}</div>
              <div className={styles.letterLabel}>
                {sequenceData?.userAnswer === option ?
                  (option === currentSequence.missing ? '✅ Correto!' : '❌ Incorreto') :
                  'Opção'
                }
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 👁️ COMPONENTE: Discriminação Visual - SEGUINDO PADRÃO MEMORYGAME
  const renderVisualDiscrimination = () => {
    const visualData = gameState.activityData.visualDiscrimination;
    const currentGroup = visualData?.currentGroup;

    console.log('🔍 Rendering visual discrimination:', { visualData, currentGroup });

    if (!currentGroup) {
      return (
        <div className={styles.gameArea}>
          <div className={styles.soundActivity}>
            <div className={styles.soundIndicator}>👁️</div>
            <h3>Carregando atividade visual...</h3>
            <p className={styles.activityTip}>Dados: {JSON.stringify(visualData)}</p>
          </div>
        </div>
      );
    }

    return (
      <div className={styles.gameArea}>
        <div className={styles.soundActivity}>
          <div className={styles.soundIndicator}>👁️</div>
          <h3>Encontre todas as letras: <strong>{currentGroup.target}</strong></h3>
          <p className={styles.activityTip}>
            {currentGroup.description} - Encontradas: {visualData.foundTargets}/{visualData.totalTargets}
          </p>

          {/* Botão para repetir instrução */}
          {ttsActive && (
            <button
              className={styles.repeatButton}
              onClick={() => provideActivityInstructions('visual_discrimination', visualData)}
              title="Repetir instrução"
            >
              🔊 Repetir
            </button>
          )}
        </div>

        <div className={styles.lettersGrid}>
          {currentGroup.items.map((item, index) => {
            const itemKey = `${item}-${index}`;
            const isSelected = visualData.selectedItems?.includes(itemKey);
            const isTarget = item === currentGroup.target;
            const isCorrectSelection = isSelected && isTarget;

            return (
              <div
                key={itemKey}
                className={`${styles.letterCard} ${
                  isCorrectSelection ? 'correct' : isSelected ? 'incorrect' : ''
                }`}
                onClick={() => handleVisualSelect(item, index)}
                onMouseEnter={() => {
                  if (ttsActive && !isSelected) {
                    speak(item, { rate: 0.6, pitch: 1.1 });
                  }
                }}
                onTouchStart={() => {
                  if (ttsActive && !isSelected) {
                    speak(item, { rate: 0.6, pitch: 1.1 });
                  }
                }}
                title={`Letra ${item}`}
                style={{
                  opacity: isSelected ? 0.7 : 1,
                  cursor: isSelected ? 'not-allowed' : 'pointer'
                }}
              >
                <div className={styles.letterContent}>{item}</div>
                <div className={styles.letterLabel}>
                  {isCorrectSelection ? '✅ Encontrado!' : isSelected ? '❌ Errado' : isTarget ? 'Alvo' : 'Distrator'}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // 🎯 RENDERIZAÇÃO PRINCIPAL - SEGUINDO PADRÃO DOS OUTROS JOGOS
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Reconhecimento de Letras"
        gameDescription="Aprenda o alfabeto de forma divertida e interativa"
        gameIcon="🔤"
        onStart={startGame}
        onBack={onBack}
        difficulties={[
          { id: 'easy', name: 'Fácil', description: '4 letras básicas', icon: '🟢' },
          { id: 'medium', name: 'Médio', description: '6 letras variadas', icon: '🟡' },
          { id: 'hard', name: 'Avançado', description: '8 letras completas', icon: '🔴' }
        ]}
      />
    );
  }

  return (
    <div className={styles.letterRecognitionGame}>
      <div className={styles.gameContent}>
        {/* Header do jogo - padrão Contagem de Números */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🔤 Reconhecimento de Letras V3
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              {ACTIVITY_TYPES[gameState.currentActivity.toUpperCase()]?.name || 'Seleção de Letras'}
            </div>
          </h1>
          <button
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
            onClick={toggleTTS}
            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* Header com estatísticas - padrão MemoryGame */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.accuracy}%</div>
            <div className={styles.statLabel}>Precisão</div>
          </div>
        </div>

        {/* Menu de atividades - padrão MemoryGame */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => changeActivity(activity.id)}
            >
              <span>{activity.icon}</span>
              <span>{activity.name}</span>
            </button>
          ))}
        </div>

        {/* Renderização da atividade atual */}
        {gameState.currentActivity === 'letter_selection' && renderLetterSelection()}
        {gameState.currentActivity === 'sound_matching' && renderSoundMatching()}
        {gameState.currentActivity === 'word_formation' && renderWordFormation()}
        {gameState.currentActivity === 'sequence_recognition' && renderSequenceRecognition()}
        {gameState.currentActivity === 'visual_discrimination' && renderVisualDiscrimination()}

        {/* Controles do jogo - padrão MemoryGame com acessibilidade */}
        <div className={styles.gameControls}>
          <button className={styles.controlButton} onClick={explainGame}>
            🔊 Explicar
          </button>
          <button className={styles.controlButton} onClick={repeatInstruction}>
            🔄 Repetir
          </button>
          <button className={styles.controlButton} onClick={testAccessibility}>
            🧪 Teste TTS
          </button>
          <button className={styles.controlButton} onClick={restartGame}>
            🔄 Reiniciar
          </button>
          <button className={styles.controlButton} onClick={onBack}>
            ⬅️ Voltar
          </button>
        </div>
      </div>
    </div>
  );
}

export default LetterRecognitionGame;
