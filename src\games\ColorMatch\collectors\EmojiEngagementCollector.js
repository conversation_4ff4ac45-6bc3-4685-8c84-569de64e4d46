/**
 * 😊 EMOJI ENGAGEMENT COLLECTOR
 * Coletor especializado em análise de engajamento e interação com diferentes categorias de emojis
 * Portal Betina V3 - FASE 2.1
 */

export class EmojiEngagementCollector {
  constructor() {
    this.emojiCategories = {
      fruits: {
        name: '<PERSON><PERSON><PERSON>',
        emojis: ['🍎', '🍌', '🍊', '🍇', '🥝', '🍓', '🥭', '🍑'],
        cognitive_load: 'low',
        familiarity: 'high',
        visual_complexity: 'medium'
      },
      animals: {
        name: 'Anima<PERSON>',
        emojis: ['🐱', '🐶', '🐸', '🦄', '🐘', '🦁', '🐯', '🐻'],
        cognitive_load: 'medium',
        familiarity: 'high',
        visual_complexity: 'medium'
      },
      objects: {
        name: 'Objetos',
        emojis: ['⚽', '🎈', '🎁', '🚗', '✈️', '🏠', '⭐', '🌙'],
        cognitive_load: 'medium',
        familiarity: 'medium',
        visual_complexity: 'high'
      },
      nature: {
        name: '<PERSON>za',
        emojis: ['🌸', '🌳', '🌊', '⛰️', '🌈', '☀️', '❄️', '🔥'],
        cognitive_load: 'high',
        familiarity: 'medium',
        visual_complexity: 'high'
      }
    };
    
    this.engagementMetrics = {
      preference: 'Preferência',
      attention_duration: 'Duração da Atenção',
      interaction_quality: 'Qualidade da Interação',
      emotional_response: 'Resposta Emocional',
      learning_efficiency: 'Eficiência de Aprendizado'
    };
    
    this.interactionTypes = {
      selection: 'Seleção',
      exploration: 'Exploração',
      recognition: 'Reconhecimento',
      categorization: 'Categorização',
      memory: 'Memória'
    };
    
    this.developmentalFactors = {
      visual_processing: 'Processamento Visual',
      semantic_understanding: 'Compreensão Semântica',
      cultural_familiarity: 'Familiaridade Cultural',
      cognitive_flexibility: 'Flexibilidade Cognitiva'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Análise principal do engajamento com emojis
   */
  async analyze(data) {
    try {
      console.log('😊 EmojiEngagementCollector: Iniciando análise de engajamento...');
      
      if (!this.validateEmojiData(data)) {
        return this.generateFallbackAnalysis('Dados de emoji inválidos');
      }

      const emojiData = this.extractEmojiData(data);
      
      // Análises principais
      const categoryEngagement = this.analyzeCategoryEngagement(emojiData);
      const preferenceProfile = this.analyzePreferenceProfile(emojiData);
      const attentionPatterns = this.analyzeAttentionPatterns(emojiData);
      const interactionQuality = this.analyzeInteractionQuality(emojiData);
      const emotionalResponse = this.analyzeEmotionalResponse(emojiData);
      const learningEfficiency = this.analyzeLearningEfficiency(emojiData);

      const results = {
        // Engajamento por categoria
        fruitsEngagement: categoryEngagement.fruits,
        animalsEngagement: categoryEngagement.animals,
        objectsEngagement: categoryEngagement.objects,
        natureEngagement: categoryEngagement.nature,
        
        // Perfil de preferência
        preferredCategory: preferenceProfile.preferred,
        leastPreferred: preferenceProfile.least,
        preferenceStrength: preferenceProfile.strength,
        preferenceConsistency: preferenceProfile.consistency,
        
        // Padrões de atenção
        attentionSpan: attentionPatterns.span,
        focusStability: attentionPatterns.stability,
        distractibility: attentionPatterns.distractibility,
        sustainedAttention: attentionPatterns.sustained,
        
        // Qualidade da interação
        interactionDepth: interactionQuality.depth,
        explorationBehavior: interactionQuality.exploration,
        taskPersistence: interactionQuality.persistence,
        adaptiveBehavior: interactionQuality.adaptive,
        
        // Resposta emocional
        positiveAffect: emotionalResponse.positive,
        engagement Level: emotionalResponse.engagement,
        motivationalState: emotionalResponse.motivation,
        frustrationTolerance: emotionalResponse.frustration,
        
        // Eficiência de aprendizado
        categorizationSkill: learningEfficiency.categorization,
        recognitionSpeed: learningEfficiency.recognition,
        memoryRetention: learningEfficiency.memory,
        transferLearning: learningEfficiency.transfer,
        
        // Análises específicas por emoji
        individualEmojiAnalysis: this.analyzeIndividualEmojis(emojiData),
        complexityTolerance: this.analyzeComplexityTolerance(emojiData),
        noveltyResponse: this.analyzeNoveltyResponse(emojiData),
        
        // Métricas de desenvolvimento
        visualMaturity: this.assessVisualMaturity(emojiData),
        semanticDevelopment: this.assessSemanticDevelopment(emojiData),
        culturalAlignment: this.assessCulturalAlignment(emojiData),
        
        // Padrões temporais
        engagementEvolution: this.analyzeEngagementEvolution(emojiData),
        sessionProgression: this.analyzeSessionProgression(emojiData),
        fatiguePatterns: this.analyzeFatiguePatterns(emojiData),
        
        // Comparações e benchmarks
        ageAppropriate: this.assessAgeAppropriateness(categoryEngagement, data.userAge),
        developmentalStage: this.assessDevelopmentalStage(categoryEngagement),
        engagementLevel: this.categorizeEngagementLevel(categoryEngagement),
        
        // Contexto da análise
        totalInteractions: emojiData.interactions.length,
        uniqueEmojis: emojiData.uniqueEmojis.length,
        categoryDistribution: emojiData.categoryDistribution,
        sessionDuration: emojiData.sessionDuration,
        
        // Recomendações específicas
        recommendations: this.generateEmojiRecommendations({
          categoryEngagement, preferenceProfile, attentionPatterns,
          interactionQuality, emotionalResponse, learningEfficiency
        }),
        
        // Metadados
        analysisTimestamp: new Date().toISOString(),
        collectorVersion: '2.1.0',
        dataQuality: this.assessDataQuality(emojiData)
      };

      console.log('✅ EmojiEngagementCollector: Análise concluída:', {
        preferred: results.preferredCategory,
        engagement: results.engagementLevel,
        attention: Math.round(results.attentionSpan),
        quality: Math.round(results.interactionDepth * 100)
      });

      return results;

    } catch (error) {
      console.error('❌ EmojiEngagementCollector: Erro na análise:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de emoji de entrada
   */
  validateEmojiData(data) {
    if (!data) return false;
    
    // Verificar se há dados de interação com emojis
    const hasEmojiData = data.selectedItems || data.interactions || data.gridItems;
    const hasEmojiInfo = data.emojis || data.targetEmojis || data.gridItems;
    
    return hasEmojiData && hasEmojiInfo;
  }

  /**
   * Extrai dados de emoji dos dados de entrada
   */
  extractEmojiData(data) {
    let interactions = [];
    let emojis = [];
    let categories = [];
    let timestamps = [];

    // Extrair das diferentes estruturas possíveis
    if (data.selectedItems && Array.isArray(data.selectedItems)) {
      interactions = data.selectedItems.map((item, index) => ({
        index,
        emoji: item.emoji || item.icon || item.content || '❓',
        category: this.categorizeEmoji(item.emoji || item.icon || item.content || '❓'),
        correct: item.correct || false,
        timestamp: item.timestamp || Date.now(),
        responseTime: item.responseTime || 0,
        interactionType: this.determineInteractionType(item),
        gridPosition: item.gridPosition || item.position || index
      }));
      
      emojis = interactions.map(i => i.emoji);
      categories = interactions.map(i => i.category);
      timestamps = interactions.map(i => i.timestamp);
    }

    // Extrair emojis do grid se disponível
    if (data.gridItems) {
      const gridEmojis = data.gridItems.map(item => item.emoji || item.icon || item.content || '❓');
      if (emojis.length === 0) {
        emojis = gridEmojis;
        categories = gridEmojis.map(emoji => this.categorizeEmoji(emoji));
      }
    }

    // Análise de distribuição de categorias
    const categoryDistribution = this.calculateCategoryDistribution(categories);
    const uniqueEmojis = [...new Set(emojis)].filter(emoji => emoji !== '❓');

    return {
      interactions,
      emojis,
      categories,
      timestamps,
      categoryDistribution,
      uniqueEmojis,
      difficulty: data.difficulty || 'medium',
      sessionDuration: data.sessionDuration || 0,
      userAge: data.userAge || null,
      totalInteractions: interactions.length
    };
  }

  /**
   * Categoriza emoji
   */
  categorizeEmoji(emoji) {
    for (const [category, categoryData] of Object.entries(this.emojiCategories)) {
      if (categoryData.emojis.includes(emoji)) {
        return category;
      }
    }
    return 'unknown';
  }

  /**
   * Determina tipo de interação
   */
  determineInteractionType(item) {
    // Lógica simplificada para determinar tipo de interação
    if (item.correct !== undefined) return 'selection';
    if (item.responseTime > 0) return 'recognition';
    return 'exploration';
  }

  /**
   * Analisa engajamento por categoria
   */
  analyzeCategoryEngagement(emojiData) {
    const { interactions } = emojiData;
    
    const categoryAnalysis = {};
    
    // Inicializar análise para cada categoria
    Object.keys(this.emojiCategories).forEach(category => {
      categoryAnalysis[category] = {
        interactions: 0,
        correct: 0,
        totalTime: 0,
        averageResponseTime: 0,
        accuracy: 0,
        engagement: 0,
        preference: 0
      };
    });

    // Processar interações
    interactions.forEach(interaction => {
      const category = interaction.category;
      if (category !== 'unknown' && categoryAnalysis[category]) {
        const analysis = categoryAnalysis[category];
        analysis.interactions++;
        if (interaction.correct) analysis.correct++;
        if (interaction.responseTime > 0) {
          analysis.totalTime += interaction.responseTime;
        }
      }
    });

    // Calcular métricas finais
    Object.keys(categoryAnalysis).forEach(category => {
      const analysis = categoryAnalysis[category];
      if (analysis.interactions > 0) {
        analysis.accuracy = analysis.correct / analysis.interactions;
        analysis.averageResponseTime = analysis.totalTime / analysis.interactions;
        
        // Engagement baseado em precisão e tempo de resposta
        const timeScore = Math.max(0, 1 - (analysis.averageResponseTime / 5000));
        analysis.engagement = (analysis.accuracy * 0.7 + timeScore * 0.3);
        
        // Preferência baseada na frequência relativa
        analysis.preference = analysis.interactions / interactions.length;
      }
    });

    return categoryAnalysis;
  }

  /**
   * Analisa perfil de preferência
   */
  analyzePreferenceProfile(emojiData) {
    const categoryEngagement = this.analyzeCategoryEngagement(emojiData);
    
    // Encontrar categoria preferida e menos preferida
    const categoryScores = Object.entries(categoryEngagement)
      .filter(([_, data]) => data.interactions > 0)
      .sort(([_, a], [__, b]) => b.engagement - a.engagement);
    
    if (categoryScores.length === 0) {
      return {
        preferred: 'unknown',
        least: 'unknown',
        strength: 0,
        consistency: 0
      };
    }

    const preferred = categoryScores[0][0];
    const least = categoryScores[categoryScores.length - 1][0];
    
    // Força da preferência
    const preferredScore = categoryScores[0][1].engagement;
    const averageScore = categoryScores.reduce((sum, [_, data]) => sum + data.engagement, 0) / categoryScores.length;
    const strength = preferredScore - averageScore;
    
    // Consistência da preferência
    const consistency = this.calculatePreferenceConsistency(emojiData, preferred);

    return {
      preferred,
      least,
      strength,
      consistency,
      categoryRanking: categoryScores.map(([cat, _]) => cat)
    };
  }

  /**
   * Calcula consistência da preferência
   */
  calculatePreferenceConsistency(emojiData, preferredCategory) {
    const { interactions } = emojiData;
    
    if (interactions.length < 5) return 0.5;
    
    // Dividir em segmentos temporais
    const segmentSize = Math.max(3, Math.floor(interactions.length / 3));
    const segments = [];
    
    for (let i = 0; i < interactions.length; i += segmentSize) {
      const segment = interactions.slice(i, i + segmentSize);
      const categoryCount = {};
      
      segment.forEach(interaction => {
        const cat = interaction.category;
        categoryCount[cat] = (categoryCount[cat] || 0) + 1;
      });
      
      const mostFrequent = Object.entries(categoryCount)
        .sort(([_, a], [__, b]) => b - a)[0];
      
      segments.push(mostFrequent ? mostFrequent[0] : 'unknown');
    }
    
    // Consistência como proporção de segmentos onde categoria preferida domina
    const consistentSegments = segments.filter(cat => cat === preferredCategory).length;
    return consistentSegments / segments.length;
  }

  /**
   * Analisa padrões de atenção
   */
  analyzeAttentionPatterns(emojiData) {
    const { interactions, timestamps } = emojiData;
    
    if (interactions.length < 3) {
      return {
        span: 0,
        stability: 0.5,
        distractibility: 0.5,
        sustained: 0.5
      };
    }

    // Span de atenção baseado em tempo de resposta
    const responseTimes = interactions
      .map(i => i.responseTime)
      .filter(rt => rt > 0);
    
    const attentionSpan = responseTimes.length > 0
      ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length
      : 0;

    // Estabilidade da atenção
    const focusStability = this.calculateFocusStability(responseTimes);
    
    // Distrabilidade
    const distractibility = this.calculateDistractibility(interactions);
    
    // Atenção sustentada
    const sustainedAttention = this.calculateSustainedAttention(interactions);

    return {
      span: attentionSpan,
      stability: focusStability,
      distractibility,
      sustained: sustainedAttention
    };
  }

  /**
   * Calcula estabilidade do foco
   */
  calculateFocusStability(responseTimes) {
    if (responseTimes.length < 3) return 0.5;
    
    const variance = this.calculateVariance(responseTimes);
    const mean = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    
    const coefficientOfVariation = mean > 0 ? variance / mean : 0;
    return Math.max(0, 1 - Math.min(1, coefficientOfVariation));
  }

  /**
   * Calcula distrabilidade
   */
  calculateDistractibility(interactions) {
    // Baseado em mudanças frequentes entre categorias
    let categoryChanges = 0;
    
    for (let i = 1; i < interactions.length; i++) {
      if (interactions[i].category !== interactions[i - 1].category) {
        categoryChanges++;
      }
    }
    
    return categoryChanges / Math.max(interactions.length - 1, 1);
  }

  /**
   * Calcula atenção sustentada
   */
  calculateSustainedAttention(interactions) {
    // Baseado na capacidade de manter performance ao longo da sessão
    if (interactions.length < 5) return 0.5;
    
    const firstHalf = interactions.slice(0, Math.floor(interactions.length / 2));
    const secondHalf = interactions.slice(Math.floor(interactions.length / 2));
    
    const firstAccuracy = firstHalf.filter(i => i.correct).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter(i => i.correct).length / secondHalf.length;
    
    // Atenção sustentada mantém ou melhora performance
    return Math.max(0, Math.min(1, secondAccuracy / Math.max(firstAccuracy, 0.1)));
  }

  /**
   * Analisa qualidade da interação
   */
  analyzeInteractionQuality(emojiData) {
    const { interactions } = emojiData;
    
    // Profundidade da interação
    const depth = this.calculateInteractionDepth(interactions);
    
    // Comportamento exploratório
    const exploration = this.calculateExplorationBehavior(interactions);
    
    // Persistência na tarefa
    const persistence = this.calculateTaskPersistence(interactions);
    
    // Comportamento adaptativo
    const adaptive = this.calculateAdaptiveBehavior(interactions);

    return {
      depth,
      exploration,
      persistence,
      adaptive
    };
  }

  /**
   * Calcula profundidade da interação
   */
  calculateInteractionDepth(interactions) {
    // Baseado na variedade de emojis e categorias exploradas
    const uniqueEmojis = new Set(interactions.map(i => i.emoji)).size;
    const uniqueCategories = new Set(interactions.map(i => i.category)).size;
    
    const emojiDepth = Math.min(1, uniqueEmojis / 8);
    const categoryDepth = Math.min(1, uniqueCategories / 4);
    
    return (emojiDepth * 0.6 + categoryDepth * 0.4);
  }

  /**
   * Calcula comportamento exploratório
   */
  calculateExplorationBehavior(interactions) {
    // Baseado na distribuição de atenção entre diferentes elementos
    const emojiCounts = {};
    interactions.forEach(interaction => {
      const emoji = interaction.emoji;
      emojiCounts[emoji] = (emojiCounts[emoji] || 0) + 1;
    });
    
    const counts = Object.values(emojiCounts);
    const entropy = this.calculateEntropy(counts);
    const maxEntropy = Math.log2(counts.length);
    
    return maxEntropy > 0 ? entropy / maxEntropy : 0;
  }

  /**
   * Calcula persistência na tarefa
   */
  calculateTaskPersistence(interactions) {
    // Baseado na continuidade sem desistir
    const completionRate = interactions.filter(i => i.correct !== undefined).length / interactions.length;
    return completionRate;
  }

  /**
   * Calcula comportamento adaptativo
   */
  calculateAdaptiveBehavior(interactions) {
    // Baseado na melhoria de performance após erros
    let adaptiveInstances = 0;
    let opportunities = 0;
    
    for (let i = 1; i < interactions.length; i++) {
      const prev = interactions[i - 1];
      const curr = interactions[i];
      
      if (prev.correct === false && curr.correct !== undefined) {
        opportunities++;
        if (curr.correct) {
          adaptiveInstances++;
        }
      }
    }
    
    return opportunities > 0 ? adaptiveInstances / opportunities : 0.5;
  }

  /**
   * Analisa resposta emocional
   */
  analyzeEmotionalResponse(emojiData) {
    const { interactions } = emojiData;
    
    // Afeto positivo baseado em engagement
    const positiveAffect = this.calculatePositiveAffect(interactions);
    
    // Nível de engajamento
    const engagementLevel = this.calculateEngagementLevel(interactions);
    
    // Estado motivacional
    const motivation = this.calculateMotivationalState(interactions);
    
    // Tolerância à frustração
    const frustration = this.calculateFrustrationTolerance(interactions);

    return {
      positive: positiveAffect,
      engagement: engagementLevel,
      motivation,
      frustration
    };
  }

  /**
   * Calcula afeto positivo
   */
  calculatePositiveAffect(interactions) {
    // Baseado na rapidez e precisão das respostas
    const accuracy = interactions.filter(i => i.correct).length / interactions.length;
    const avgResponseTime = interactions
      .filter(i => i.responseTime > 0)
      .reduce((sum, i) => sum + i.responseTime, 0) / 
      Math.max(interactions.filter(i => i.responseTime > 0).length, 1);
    
    const speedScore = Math.max(0, 1 - (avgResponseTime / 3000));
    return (accuracy * 0.7 + speedScore * 0.3);
  }

  /**
   * Calcula nível de engajamento
   */
  calculateEngagementLevel(interactions) {
    // Baseado na consistência e duração da participação
    const completionRate = interactions.filter(i => i.correct !== undefined).length / interactions.length;
    const consistencyScore = this.calculateFocusStability(
      interactions.map(i => i.responseTime).filter(rt => rt > 0)
    );
    
    return (completionRate * 0.6 + consistencyScore * 0.4);
  }

  /**
   * Calcula estado motivacional
   */
  calculateMotivationalState(interactions) {
    // Baseado na melhoria ao longo da sessão
    if (interactions.length < 4) return 0.5;
    
    const firstQuarter = interactions.slice(0, Math.floor(interactions.length / 4));
    const lastQuarter = interactions.slice(-Math.floor(interactions.length / 4));
    
    const firstAccuracy = firstQuarter.filter(i => i.correct).length / firstQuarter.length;
    const lastAccuracy = lastQuarter.filter(i => i.correct).length / lastQuarter.length;
    
    const improvement = lastAccuracy - firstAccuracy;
    return Math.max(0, Math.min(1, 0.5 + improvement));
  }

  /**
   * Calcula tolerância à frustração
   */
  calculateFrustrationTolerance(interactions) {
    // Baseado na recuperação após erros consecutivos
    let consecutiveErrors = 0;
    let maxConsecutiveErrors = 0;
    let recoveries = 0;
    
    interactions.forEach(interaction => {
      if (interaction.correct === false) {
        consecutiveErrors++;
        maxConsecutiveErrors = Math.max(maxConsecutiveErrors, consecutiveErrors);
      } else if (interaction.correct === true) {
        if (consecutiveErrors > 0) {
          recoveries++;
        }
        consecutiveErrors = 0;
      }
    });
    
    // Tolerância baseada na capacidade de se recuperar
    return maxConsecutiveErrors > 0 ? Math.min(1, recoveries / maxConsecutiveErrors) : 1;
  }

  /**
   * Analisa eficiência de aprendizado
   */
  analyzeLearningEfficiency(emojiData) {
    const { interactions } = emojiData;
    
    // Habilidade de categorização
    const categorization = this.calculateCategorizationSkill(interactions);
    
    // Velocidade de reconhecimento
    const recognition = this.calculateRecognitionSpeed(interactions);
    
    // Retenção de memória
    const memory = this.calculateMemoryRetention(interactions);
    
    // Transferência de aprendizado
    const transfer = this.calculateTransferLearning(interactions);

    return {
      categorization,
      recognition,
      memory,
      transfer
    };
  }

  /**
   * Calcula habilidade de categorização
   */
  calculateCategorizationSkill(interactions) {
    // Baseado na precisão consistente dentro de categorias
    const categoryAccuracy = {};
    
    interactions.forEach(interaction => {
      const cat = interaction.category;
      if (!categoryAccuracy[cat]) {
        categoryAccuracy[cat] = { correct: 0, total: 0 };
      }
      categoryAccuracy[cat].total++;
      if (interaction.correct) {
        categoryAccuracy[cat].correct++;
      }
    });
    
    const accuracies = Object.values(categoryAccuracy)
      .filter(data => data.total > 0)
      .map(data => data.correct / data.total);
    
    return accuracies.length > 0 
      ? accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length 
      : 0;
  }

  /**
   * Calcula velocidade de reconhecimento
   */
  calculateRecognitionSpeed(interactions) {
    const responseTimes = interactions
      .filter(i => i.correct && i.responseTime > 0)
      .map(i => i.responseTime);
    
    if (responseTimes.length === 0) return 0;
    
    const avgResponseTime = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    return Math.max(0, 1 - (avgResponseTime / 3000));
  }

  /**
   * Calcula retenção de memória
   */
  calculateMemoryRetention(interactions) {
    // Baseado na performance com emojis repetidos
    const emojiPerformance = {};
    
    interactions.forEach(interaction => {
      const emoji = interaction.emoji;
      if (!emojiPerformance[emoji]) {
        emojiPerformance[emoji] = [];
      }
      emojiPerformance[emoji].push(interaction.correct);
    });
    
    let totalRetention = 0;
    let emojiCount = 0;
    
    Object.values(emojiPerformance).forEach(performances => {
      if (performances.length > 1) {
        const improvement = performances[performances.length - 1] >= performances[0] ? 1 : 0;
        totalRetention += improvement;
        emojiCount++;
      }
    });
    
    return emojiCount > 0 ? totalRetention / emojiCount : 0.5;
  }

  /**
   * Calcula transferência de aprendizado
   */
  calculateTransferLearning(interactions) {
    // Baseado na generalização entre categorias similares
    const categoryOrder = [];
    interactions.forEach(interaction => {
      if (!categoryOrder.includes(interaction.category)) {
        categoryOrder.push(interaction.category);
      }
    });
    
    if (categoryOrder.length < 2) return 0.5;
    
    // Verificar se performance melhora em categorias subsequentes
    let transferInstances = 0;
    let opportunities = 0;
    
    for (let i = 1; i < categoryOrder.length; i++) {
      const prevCat = categoryOrder[i - 1];
      const currCat = categoryOrder[i];
      
      const prevPerf = this.getCategoryPerformance(interactions, prevCat);
      const currPerf = this.getCategoryPerformance(interactions, currCat);
      
      if (prevPerf !== null && currPerf !== null) {
        opportunities++;
        if (currPerf >= prevPerf * 0.8) { // Transferência positiva
          transferInstances++;
        }
      }
    }
    
    return opportunities > 0 ? transferInstances / opportunities : 0.5;
  }

  /**
   * Obtém performance de categoria
   */
  getCategoryPerformance(interactions, category) {
    const categoryInteractions = interactions.filter(i => i.category === category);
    if (categoryInteractions.length === 0) return null;
    
    const correct = categoryInteractions.filter(i => i.correct).length;
    return correct / categoryInteractions.length;
  }

  // Continuação com métodos auxiliares e análises específicas...

  /**
   * Analisa emojis individuais
   */
  analyzeIndividualEmojis(emojiData) {
    const { interactions } = emojiData;
    
    const emojiAnalysis = {};
    
    interactions.forEach(interaction => {
      const emoji = interaction.emoji;
      if (!emojiAnalysis[emoji]) {
        emojiAnalysis[emoji] = {
          interactions: 0,
          correct: 0,
          totalResponseTime: 0,
          category: interaction.category,
          accuracy: 0,
          averageResponseTime: 0,
          preference: 0
        };
      }
      
      const analysis = emojiAnalysis[emoji];
      analysis.interactions++;
      if (interaction.correct) analysis.correct++;
      if (interaction.responseTime > 0) {
        analysis.totalResponseTime += interaction.responseTime;
      }
    });
    
    // Calcular métricas finais
    Object.values(emojiAnalysis).forEach(analysis => {
      if (analysis.interactions > 0) {
        analysis.accuracy = analysis.correct / analysis.interactions;
        analysis.averageResponseTime = analysis.totalResponseTime / analysis.interactions;
        analysis.preference = analysis.interactions / interactions.length;
      }
    });
    
    return emojiAnalysis;
  }

  /**
   * Analisa tolerância à complexidade
   */
  analyzeComplexityTolerance(emojiData) {
    const categoryEngagement = this.analyzeCategoryEngagement(emojiData);
    
    // Complexidade baseada nas características das categorias
    const complexityScores = {
      fruits: 1, // Baixa complexidade
      animals: 2, // Média complexidade
      objects: 3, // Alta complexidade
      nature: 3 // Alta complexidade
    };
    
    let totalComplexity = 0;
    let totalEngagement = 0;
    let weightedSum = 0;
    
    Object.entries(categoryEngagement).forEach(([category, data]) => {
      if (data.interactions > 0) {
        const complexity = complexityScores[category] || 2;
        const weight = data.interactions;
        
        totalComplexity += complexity * weight;
        totalEngagement += data.engagement * weight;
        weightedSum += weight;
      }
    });
    
    const avgComplexity = weightedSum > 0 ? totalComplexity / weightedSum : 2;
    const avgEngagement = weightedSum > 0 ? totalEngagement / weightedSum : 0.5;
    
    return {
      tolerance: avgEngagement,
      preferredComplexity: avgComplexity,
      adaptability: this.calculateComplexityAdaptability(categoryEngagement)
    };
  }

  /**
   * Calcula adaptabilidade à complexidade
   */
  calculateComplexityAdaptability(categoryEngagement) {
    const complexityLevels = {
      low: ['fruits'],
      medium: ['animals'],
      high: ['objects', 'nature']
    };
    
    let adaptabilityScore = 0;
    let levelCount = 0;
    
    Object.entries(complexityLevels).forEach(([level, categories]) => {
      const levelEngagement = categories
        .filter(cat => categoryEngagement[cat] && categoryEngagement[cat].interactions > 0)
        .map(cat => categoryEngagement[cat].engagement);
      
      if (levelEngagement.length > 0) {
        const avgEngagement = levelEngagement.reduce((sum, eng) => sum + eng, 0) / levelEngagement.length;
        adaptabilityScore += avgEngagement;
        levelCount++;
      }
    });
    
    return levelCount > 0 ? adaptabilityScore / levelCount : 0.5;
  }

  /**
   * Analisa resposta à novidade
   */
  analyzeNoveltyResponse(emojiData) {
    const { interactions } = emojiData;
    
    // Analisar performance com emojis pela primeira vez vs. repetidos
    const emojiEncounters = {};
    const noveltyResponses = [];
    const familiarityResponses = [];
    
    interactions.forEach(interaction => {
      const emoji = interaction.emoji;
      
      if (!emojiEncounters[emoji]) {
        emojiEncounters[emoji] = 0;
        noveltyResponses.push({
          correct: interaction.correct,
          responseTime: interaction.responseTime
        });
      } else {
        familiarityResponses.push({
          correct: interaction.correct,
          responseTime: interaction.responseTime
        });
      }
      
      emojiEncounters[emoji]++;
    });
    
    const noveltyAccuracy = noveltyResponses.filter(r => r.correct).length / Math.max(noveltyResponses.length, 1);
    const familiarityAccuracy = familiarityResponses.filter(r => r.correct).length / Math.max(familiarityResponses.length, 1);
    
    const noveltyRT = noveltyResponses
      .filter(r => r.responseTime > 0)
      .reduce((sum, r) => sum + r.responseTime, 0) / Math.max(noveltyResponses.filter(r => r.responseTime > 0).length, 1);
    
    const familiarityRT = familiarityResponses
      .filter(r => r.responseTime > 0)
      .reduce((sum, r) => sum + r.responseTime, 0) / Math.max(familiarityResponses.filter(r => r.responseTime > 0).length, 1);
    
    return {
      noveltyAccuracy,
      familiarityAccuracy,
      noveltyResponseTime: noveltyRT,
      familiarityResponseTime: familiarityRT,
      adaptationRate: familiarityAccuracy - noveltyAccuracy,
      speedImprovement: noveltyRT - familiarityRT
    };
  }

  /**
   * Avalia maturidade visual
   */
  assessVisualMaturity(emojiData) {
    const complexityTolerance = this.analyzeComplexityTolerance(emojiData);
    const categoryEngagement = this.analyzeCategoryEngagement(emojiData);
    
    // Maturidade baseada na capacidade de lidar com emojis complexos
    const highComplexityCategories = ['objects', 'nature'];
    const highComplexityEngagement = highComplexityCategories
      .filter(cat => categoryEngagement[cat] && categoryEngagement[cat].interactions > 0)
      .map(cat => categoryEngagement[cat].engagement);
    
    const avgHighComplexityEngagement = highComplexityEngagement.length > 0
      ? highComplexityEngagement.reduce((sum, eng) => sum + eng, 0) / highComplexityEngagement.length
      : 0;
    
    return (complexityTolerance.tolerance * 0.6 + avgHighComplexityEngagement * 0.4);
  }

  /**
   * Avalia desenvolvimento semântico
   */
  assessSemanticDevelopment(emojiData) {
    const learningEfficiency = this.analyzeLearningEfficiency(emojiData);
    
    // Desenvolvimento baseado na categorização e transferência
    return (learningEfficiency.categorization * 0.6 + learningEfficiency.transfer * 0.4);
  }

  /**
   * Avalia alinhamento cultural
   */
  assessCulturalAlignment(emojiData) {
    const categoryEngagement = this.analyzeCategoryEngagement(emojiData);
    
    // Categorias com alta familiaridade cultural
    const familiarCategories = ['fruits', 'animals'];
    const familiarEngagement = familiarCategories
      .filter(cat => categoryEngagement[cat] && categoryEngagement[cat].interactions > 0)
      .map(cat => categoryEngagement[cat].engagement);
    
    return familiarEngagement.length > 0
      ? familiarEngagement.reduce((sum, eng) => sum + eng, 0) / familiarEngagement.length
      : 0.5;
  }

  /**
   * Analisa evolução do engajamento
   */
  analyzeEngagementEvolution(emojiData) {
    const { interactions } = emojiData;
    
    if (interactions.length < 6) {
      return {
        trend: 'insufficient_data',
        stability: 0.5,
        improvement: 0
      };
    }
    
    // Dividir em segmentos temporais
    const segmentSize = Math.floor(interactions.length / 3);
    const segments = [
      interactions.slice(0, segmentSize),
      interactions.slice(segmentSize, segmentSize * 2),
      interactions.slice(segmentSize * 2)
    ];
    
    const segmentEngagements = segments.map(segment => {
      const correct = segment.filter(i => i.correct).length;
      return correct / segment.length;
    });
    
    const trend = this.calculateTrend(segmentEngagements);
    const stability = this.calculateStability(segmentEngagements);
    const improvement = segmentEngagements[2] - segmentEngagements[0];
    
    return {
      trend: trend > 0.1 ? 'improving' : trend < -0.1 ? 'declining' : 'stable',
      stability,
      improvement
    };
  }

  /**
   * Analisa progressão da sessão
   */
  analyzeSessionProgression(emojiData) {
    const { interactions } = emojiData;
    
    // Analisar mudanças na performance ao longo da sessão
    const windowSize = Math.max(3, Math.floor(interactions.length / 5));
    const progressionPoints = [];
    
    for (let i = 0; i <= interactions.length - windowSize; i++) {
      const window = interactions.slice(i, i + windowSize);
      const accuracy = window.filter(int => int.correct).length / window.length;
      progressionPoints.push(accuracy);
    }
    
    return {
      initialPerformance: progressionPoints[0] || 0,
      finalPerformance: progressionPoints[progressionPoints.length - 1] || 0,
      peakPerformance: Math.max(...progressionPoints),
      consistency: this.calculateStability(progressionPoints),
      overallTrend: this.calculateTrend(progressionPoints)
    };
  }

  /**
   * Analisa padrões de fadiga
   */
  analyzeFatiguePatterns(emojiData) {
    const { interactions } = emojiData;
    
    if (interactions.length < 8) {
      return {
        fatigueDetected: false,
        fatigueIndex: 0,
        resilience: 0.5
      };
    }
    
    // Comparar início e fim da sessão
    const firstQuarter = interactions.slice(0, Math.floor(interactions.length / 4));
    const lastQuarter = interactions.slice(-Math.floor(interactions.length / 4));
    
    const initialAccuracy = firstQuarter.filter(i => i.correct).length / firstQuarter.length;
    const finalAccuracy = lastQuarter.filter(i => i.correct).length / lastQuarter.length;
    
    const initialRT = firstQuarter
      .filter(i => i.responseTime > 0)
      .reduce((sum, i) => sum + i.responseTime, 0) / Math.max(firstQuarter.filter(i => i.responseTime > 0).length, 1);
    
    const finalRT = lastQuarter
      .filter(i => i.responseTime > 0)
      .reduce((sum, i) => sum + i.responseTime, 0) / Math.max(lastQuarter.filter(i => i.responseTime > 0).length, 1);
    
    const accuracyDecline = initialAccuracy - finalAccuracy;
    const speedDecline = finalRT - initialRT;
    
    const fatigueIndex = Math.max(0, (accuracyDecline * 0.6 + (speedDecline / 1000) * 0.4));
    const fatigueDetected = fatigueIndex > 0.2;
    const resilience = Math.max(0, 1 - fatigueIndex);
    
    return {
      fatigueDetected,
      fatigueIndex,
      resilience,
      accuracyDecline,
      speedDecline
    };
  }

  /**
   * Funções auxiliares
   */
  calculateCategoryDistribution(categories) {
    const distribution = {};
    categories.forEach(category => {
      distribution[category] = (distribution[category] || 0) + 1;
    });
    return distribution;
  }

  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  calculateEntropy(counts) {
    const total = counts.reduce((sum, count) => sum + count, 0);
    if (total === 0) return 0;
    
    return -counts.reduce((entropy, count) => {
      if (count === 0) return entropy;
      const probability = count / total;
      return entropy + probability * Math.log2(probability);
    }, 0);
  }

  calculateTrend(values) {
    if (values.length < 2) return 0;
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    return secondAvg - firstAvg;
  }

  calculateStability(values) {
    if (values.length < 2) return 0.5;
    
    const variance = this.calculateVariance(values);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    
    const coefficientOfVariation = mean > 0 ? Math.sqrt(variance) / mean : 0;
    return Math.max(0, 1 - Math.min(1, coefficientOfVariation));
  }

  // Métodos de avaliação e categorização

  assessAgeAppropriateness(categoryEngagement, userAge) {
    if (!userAge) return { appropriate: 'unknown' };
    
    // Expectativas baseadas na idade
    const expectedEngagement = Math.max(0.5, Math.min(0.9, 0.5 + (userAge - 4) * 0.08));
    
    const avgEngagement = Object.values(categoryEngagement)
      .filter(data => data.interactions > 0)
      .reduce((sum, data) => sum + data.engagement, 0) / 
      Math.max(Object.values(categoryEngagement).filter(data => data.interactions > 0).length, 1);
    
    const appropriate = avgEngagement >= expectedEngagement * 0.8;
    
    return {
      appropriate,
      expected: expectedEngagement,
      actual: avgEngagement,
      ratio: avgEngagement / expectedEngagement
    };
  }

  assessDevelopmentalStage(categoryEngagement) {
    // Baseado na distribuição de engajamento entre categorias
    const engagementLevels = Object.values(categoryEngagement)
      .filter(data => data.interactions > 0)
      .map(data => data.engagement);
    
    const avgEngagement = engagementLevels.reduce((sum, eng) => sum + eng, 0) / Math.max(engagementLevels.length, 1);
    const consistency = this.calculateStability(engagementLevels);
    
    if (avgEngagement >= 0.8 && consistency >= 0.7) return 'advanced';
    if (avgEngagement >= 0.7 && consistency >= 0.6) return 'proficient';
    if (avgEngagement >= 0.6) return 'developing';
    if (avgEngagement >= 0.5) return 'emerging';
    return 'beginning';
  }

  categorizeEngagementLevel(categoryEngagement) {
    const totalInteractions = Object.values(categoryEngagement)
      .reduce((sum, data) => sum + data.interactions, 0);
    
    const avgEngagement = Object.values(categoryEngagement)
      .filter(data => data.interactions > 0)
      .reduce((sum, data) => sum + data.engagement, 0) / 
      Math.max(Object.values(categoryEngagement).filter(data => data.interactions > 0).length, 1);
    
    if (totalInteractions >= 10 && avgEngagement >= 0.8) return 'high';
    if (totalInteractions >= 6 && avgEngagement >= 0.6) return 'medium';
    if (totalInteractions >= 3) return 'low';
    return 'minimal';
  }

  assessDataQuality(emojiData) {
    let qualityScore = 1.0;
    const issues = [];

    if (emojiData.interactions.length < 5) {
      qualityScore -= 0.3;
      issues.push('Poucos dados de interação para análise robusta');
    }

    if (emojiData.uniqueEmojis.length < 3) {
      qualityScore -= 0.2;
      issues.push('Variedade limitada de emojis');
    }

    const unknownEmojis = emojiData.interactions.filter(i => i.emoji === '❓').length;
    if (unknownEmojis > 0) {
      qualityScore -= 0.2;
      issues.push(`${unknownEmojis} emojis não identificados`);
    }

    const invalidCategories = emojiData.interactions.filter(i => i.category === 'unknown').length;
    if (invalidCategories > emojiData.interactions.length * 0.3) {
      qualityScore -= 0.2;
      issues.push('Muitas categorias não identificadas');
    }

    return {
      score: Math.max(0, qualityScore),
      issues,
      level: qualityScore > 0.8 ? 'high' : qualityScore > 0.5 ? 'medium' : 'low'
    };
  }

  generateEmojiRecommendations(analysisResults) {
    const recommendations = [];
    const { 
      categoryEngagement, preferenceProfile, attentionPatterns,
      interactionQuality, emotionalResponse, learningEfficiency 
    } = analysisResults;

    // Recomendações baseadas em preferências
    if (preferenceProfile.strength > 0.3) {
      recommendations.push({
        category: 'preference_utilization',
        priority: 'medium',
        recommendation: `Usar categoria preferida (${preferenceProfile.preferred}) como motivador para outras atividades`,
        rationale: 'Forte preferência detectada que pode ser aproveitada'
      });
    }

    // Recomendações baseadas em atenção
    if (attentionPatterns.distractibility > 0.7) {
      recommendations.push({
        category: 'attention_support',
        priority: 'high',
        recommendation: 'Reduzir estímulos distratores e focar em uma categoria por vez',
        rationale: 'Alta distrabilidade detectada'
      });
    }

    // Recomendações baseadas em qualidade de interação
    if (interactionQuality.exploration < 0.5) {
      recommendations.push({
        category: 'exploration_enhancement',
        priority: 'medium',
        recommendation: 'Encorajar exploração de diferentes emojis com recompensas',
        rationale: 'Baixo comportamento exploratório'
      });
    }

    // Recomendações baseadas em resposta emocional
    if (emotionalResponse.motivation < 0.5) {
      recommendations.push({
        category: 'motivation_boost',
        priority: 'high',
        recommendation: 'Implementar sistema de recompensas e feedback positivo',
        rationale: 'Baixo estado motivacional detectado'
      });
    }

    // Recomendações baseadas em eficiência de aprendizado
    if (learningEfficiency.categorization < 0.6) {
      recommendations.push({
        category: 'categorization_training',
        priority: 'medium',
        recommendation: 'Focar em exercícios específicos de categorização',
        rationale: 'Habilidade de categorização pode ser melhorada'
      });
    }

    // Recomendações baseadas em engajamento por categoria
    const weakestCategory = Object.entries(categoryEngagement)
      .filter(([_, data]) => data.interactions > 0)
      .sort(([_, a], [__, b]) => a.engagement - b.engagement)[0];
    
    if (weakestCategory && weakestCategory[1].engagement < 0.5) {
      recommendations.push({
        category: 'category_support',
        priority: 'low',
        recommendation: `Fornecer apoio adicional para categoria ${weakestCategory[0]}`,
        rationale: `Baixo engajamento nesta categoria (${Math.round(weakestCategory[1].engagement * 100)}%)`
      });
    }

    return recommendations.length > 0 ? recommendations : [{
      category: 'maintenance',
      priority: 'low',
      recommendation: 'Manter variedade atual de emojis e continuar monitoramento',
      rationale: 'Engajamento com emojis dentro dos parâmetros esperados'
    }];
  }

  generateFallbackAnalysis(errorMessage) {
    return {
      fruitsEngagement: { interactions: 0, engagement: 0 },
      animalsEngagement: { interactions: 0, engagement: 0 },
      objectsEngagement: { interactions: 0, engagement: 0 },
      natureEngagement: { interactions: 0, engagement: 0 },
      preferredCategory: 'unknown',
      leastPreferred: 'unknown',
      preferenceStrength: 0,
      preferenceConsistency: 0.5,
      attentionSpan: 0,
      focusStability: 0.5,
      distractibility: 0.5,
      sustainedAttention: 0.5,
      interactionDepth: 0,
      explorationBehavior: 0.5,
      taskPersistence: 0.5,
      adaptiveBehavior: 0.5,
      positiveAffect: 0.5,
      engagementLevel: 0.5,
      motivationalState: 0.5,
      frustrationTolerance: 0.5,
      categorizationSkill: 0.5,
      recognitionSpeed: 0.5,
      memoryRetention: 0.5,
      transferLearning: 0.5,
      individualEmojiAnalysis: {},
      complexityTolerance: { tolerance: 0.5 },
      noveltyResponse: {},
      visualMaturity: 0.5,
      semanticDevelopment: 0.5,
      culturalAlignment: 0.5,
      engagementEvolution: { trend: 'unknown' },
      sessionProgression: {},
      fatiguePatterns: { fatigueDetected: false },
      ageAppropriate: { appropriate: 'unknown' },
      developmentalStage: 'unknown',
      engagementLevel: 'unknown',
      totalInteractions: 0,
      uniqueEmojis: 0,
      categoryDistribution: {},
      sessionDuration: 0,
      recommendations: [{
        category: 'data_collection',
        priority: 'high',
        recommendation: 'Melhorar coleta de dados de emoji',
        rationale: `Erro na análise: ${errorMessage}`
      }],
      analysisTimestamp: new Date().toISOString(),
      collectorVersion: '2.1.0',
      dataQuality: { score: 0, issues: [errorMessage], level: 'error' },
      status: 'fallback',
      error: errorMessage
    };
  }
}
