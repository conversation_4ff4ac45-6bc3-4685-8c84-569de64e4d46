/**
 * <PERSON><PERSON><PERSON><PERSON> Completa dos Hooks - Portal Betina V3
 * Como os hooks são processados e sua integração com processadores/orquestradores
 */

import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';

const HOOKS_DIR = './src/hooks';
const GAMES_DIR = './src/games';
const PROCESSORS_DIR = './src/api/services/processors';

function analyzeHooksArchitecture() {
  console.log('🔍 ANÁLISE COMPLETA DOS HOOKS - PORTAL BETINA V3\n');
  
  // 1. Listar todos os hooks disponíveis
  console.log('📁 HOOKS DISPONÍVEIS:');
  const hooks = readdirSync(HOOKS_DIR).filter(file => file.endsWith('.js'));
  hooks.forEach((hook, index) => {
    console.log(`${index + 1}. ${hook}`);
  });
  
  // 2. Analisar quais hooks são usados nos jogos
  console.log('\n🎮 HOOKS USADOS NOS JOGOS:');
  const gamesUsage = analyzeGamesHookUsage();
  
  // 3. Analisar integração com processadores
  console.log('\n⚙️ INTEGRAÇÃO HOOKS → PROCESSADORES:');
  analyzeHooksProcessorIntegration();
  
  // 4. Analisar fluxo de dados
  console.log('\n🔄 FLUXO DE DADOS DOS HOOKS:');
  analyzeDataFlow();
  
  // 5. Status de cada hook
  console.log('\n📊 STATUS DOS HOOKS:');
  analyzeHooksStatus();
}

function analyzeGamesHookUsage() {
  const games = readdirSync(GAMES_DIR).filter(dir => 
    readdirSync(join(GAMES_DIR, dir)).some(file => file.endsWith('Game.jsx'))
  );
  
  const hookUsage = {};
  
  games.forEach(game => {
    const gameFile = join(GAMES_DIR, game, `${game}Game.jsx`);
    try {
      const content = readFileSync(gameFile, 'utf8');
      const usedHooks = [];
      
      // Analisar imports de hooks
      const hookImports = content.match(/import.*use\w+.*from.*hooks/g) || [];
      hookImports.forEach(importLine => {
        const hookMatch = importLine.match(/use\w+/g);
        if (hookMatch) {
          usedHooks.push(...hookMatch);
        }
      });
      
      hookUsage[game] = usedHooks;
      console.log(`   ${game}: ${usedHooks.join(', ')}`);
    } catch (error) {
      console.log(`   ${game}: Erro ao analisar (${error.message})`);
    }
  });
  
  return hookUsage;
}

function analyzeHooksProcessorIntegration() {
  const hookIntegrations = {
    'useUnifiedGameLogic.js': {
      description: 'Integra com PortalBetinaV3 → SystemOrchestrator',
      processorsConnection: 'Indireta via PortalBetinaV3.processGameData()',
      dataFlow: 'Hook → Portal → Orchestrator → Processadores'
    },
    'useTherapeuticOrchestrator.js': {
      description: 'Conecta diretamente ao SystemOrchestrator',
      processorsConnection: 'Direta via SystemOrchestrator.processTherapeuticData()',
      dataFlow: 'Hook → SystemOrchestrator → Processadores Específicos'
    },
    'useMultisensoryIntegration.js': {
      description: 'Integra com MultisensoryMetricsCollector (REFATORADO)',
      processorsConnection: 'Direta com MultisensoryMetricsCollector',
      dataFlow: 'Hook → MultisensoryCollector → Análise Sensorial'
    },
    'useGameSession.js': {
      description: 'Gerencia sessões de jogo',
      processorsConnection: 'Via SessionService → Processadores',
      dataFlow: 'Hook → SessionService → Processadores'
    },
    'useGameMetrics.js': {
      description: 'Coleta e processa métricas de jogo',
      processorsConnection: 'Direta com MetricsService',
      dataFlow: 'Hook → MetricsService → Processadores'
    }
  };
  
  Object.entries(hookIntegrations).forEach(([hook, info]) => {
    console.log(`\n   📌 ${hook}:`);
    console.log(`      → ${info.description}`);
    console.log(`      → Conexão: ${info.processorsConnection}`);
    console.log(`      → Fluxo: ${info.dataFlow}`);
  });
}

function analyzeDataFlow() {
  console.log(`
   🔄 FLUXO PRINCIPAL DE DADOS:
   
   1. JOGOS (React Components)
      ↓ [usam hooks]
   2. HOOKS LAYER
      ↓ [integram com]
   3. ORCHESTRATION LAYER
      ├── SystemOrchestrator (useTherapeuticOrchestrator)
      ├── PortalBetinaV3 (useUnifiedGameLogic)
      ├── MultisensoryCollector (useMultisensoryIntegration)
      └── SessionService (useGameSession)
      ↓ [processam via]
   4. PROCESSORS LAYER
      ├── GameSpecificProcessors (ColorMatch, Memory, etc.)
      ├── AnalysisEngines (Predictive, Advanced)
      └── MetricsCollectors (Multisensory, Game)
      ↓ [geram]
   5. DATABASE & REPORTS
      ├── Métricas processadas
      ├── Relatórios terapêuticos
      └── Recomendações
  `);
}

function analyzeHooksStatus() {
  const hooksStatus = {
    '✅ ATIVOS E INTEGRADOS': [
      'useUnifiedGameLogic.js - Usado em TODOS os jogos',
      'useTherapeuticOrchestrator.js - Usado em TODOS os jogos',
      'useMultisensoryIntegration.js - Usado em TODOS os jogos (REFATORADO)',
      'useAccessibility.js - Usado em templates'
    ],
    '🔄 ATIVOS MAS LIMITADOS': [
      'useGameSession.js - Usado em alguns jogos',
      'useGameMetrics.js - Usado em métricas específicas',
      'useSystemEvents.js - Sistema de eventos'
    ],
    '⚠️ POUCO UTILIZADOS': [
      'useUserProfile.js - Perfis de usuário',
      'useRealTimeDashboardAnalytics.js - Dashboard específico',
      'useGameOrchestrator.js - Orquestração específica',
      'useGameFeedbackIntegration.js - Feedback específico'
    ],
    '🚧 INFRAESTRUTURA': [
      'useSystemOrchestrator.js - Base do sistema',
      'useResilientDatabase.js - Persistência de dados'
    ]
  };
  
  Object.entries(hooksStatus).forEach(([status, hooks]) => {
    console.log(`\n   ${status}:`);
    hooks.forEach(hook => {
      console.log(`      → ${hook}`);
    });
  });
}

function generateRecommendations() {
  console.log(`
   🎯 RECOMENDAÇÕES PARA OTIMIZAÇÃO DOS HOOKS:
   
   1. HOOKS CORE (mantém como estão):
      → useUnifiedGameLogic: Base de todos os jogos
      → useTherapeuticOrchestrator: Análise terapêutica
      → useMultisensoryIntegration: Sensores (recém refatorado)
   
   2. HOOKS A CONSOLIDAR:
      → useGameSession + useGameMetrics → useGameManager
      → useGameOrchestrator → integrar ao useUnifiedGameLogic
   
   3. HOOKS A REVISAR:
      → useUserProfile: verificar uso real
      → useRealTimeDashboardAnalytics: pode ser específico do dashboard
      → useGameFeedbackIntegration: consolidar feedback
   
   4. INTEGRAÇÃO COM PROCESSADORES:
      → ✅ DIRETA: useMultisensoryIntegration → MultisensoryCollector
      → ✅ VIA ORCHESTRATOR: useTherapeuticOrchestrator → SystemOrchestrator → Processors
      → ✅ VIA PORTAL: useUnifiedGameLogic → PortalBetinaV3 → Processors
   
   5. PADRÃO ATUAL:
      → Hooks NÃO se conectam diretamente aos processadores
      → Sempre passam por camadas de orquestração
      → Processadores são chamados pelos orquestradores, não pelos hooks
  `);
}

// Executar análise
try {
  analyzeHooksArchitecture();
  generateRecommendations();
} catch (error) {
  console.error('❌ Erro na análise:', error.message);
}
