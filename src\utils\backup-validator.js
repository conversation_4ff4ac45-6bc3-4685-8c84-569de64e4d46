/**
 * @file backup-validator.js
 * @description Validador e corretor automático de backups do Portal Betina V3
 * @version 3.1.0
 */

class BackupValidator {
  constructor() {
    this.requiredFields = {
      root: ['version', 'exportDate', 'userId', 'data', 'metadata'],
      userProfiles: ['id', 'name', 'age', 'avatar', 'preferences', 'createdAt'],
      gameProgress: [], // Dinâmico baseado nos jogos
      gameMetrics: ['totalGamesPlayed', 'averageSessionTime'],
      accessibilitySettings: ['textToSpeech', 'highContrast', 'reducedMotion'],
      sessionData: ['lastLoginDate'],
      metadata: ['source', 'totalItems', 'categories']
    }

    this.supportedGames = [
      'ColorMatch',
      'MemoryGame', 
      'QuebraCabeca',
      'ImageAssociation',
      'MusicalSequence',
      'LetterRecognition',
      'CreativePainting',
      'PadroesVisuais',
      'ContagemNumeros'
    ]

    this.validDifficulties = ['easy', 'medium', 'hard']
  }

  /**
   * Valida um backup completo
   * @param {Object} backup - Dados do backup
   * @returns {Object} Resultado da validação
   */
  validateBackup(backup) {
    const results = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
      fixedData: null
    }

    try {
      // Validação da estrutura raiz
      this._validateRootStructure(backup, results)
      
      // Validação dos dados específicos
      this._validateUserProfiles(backup.data?.userProfiles, results)
      this._validateGameProgress(backup.data?.gameProgress, results)
      this._validateGameMetrics(backup.data?.gameMetrics, results)
      this._validateAccessibilitySettings(backup.data?.accessibilitySettings, results)
      this._validateSessionData(backup.data?.sessionData, results)
      this._validateMetadata(backup.metadata, results)

      // Gerar dados corrigidos se necessário
      if (results.errors.length > 0 || results.warnings.length > 0) {
        results.fixedData = this._generateFixedBackup(backup, results)
      }

    } catch (error) {
      results.isValid = false
      results.errors.push(`Erro crítico na validação: ${error.message}`)
    }

    return results
  }

  /**
   * Valida estrutura raiz do backup
   */
  _validateRootStructure(backup, results) {
    this.requiredFields.root.forEach(field => {
      if (!backup.hasOwnProperty(field)) {
        results.errors.push(`Campo obrigatório ausente: ${field}`)
        results.isValid = false
      }
    })

    // Validar versão
    if (backup.version && backup.version < '3.0.0') {
      results.errors.push(`Versão não suportada: ${backup.version}. Mínimo: 3.0.0`)
      results.isValid = false
    }

    // Validar data de exportação
    if (backup.exportDate && !this._isValidDate(backup.exportDate)) {
      results.errors.push(`Data de exportação inválida: ${backup.exportDate}`)
    }

    // Validar userId
    if (!backup.userId || backup.userId.trim() === '') {
      results.errors.push('userId não pode estar vazio')
      results.isValid = false
    }
  }

  /**
   * Valida perfis de usuário
   */
  _validateUserProfiles(userProfiles, results) {
    if (!userProfiles) {
      results.warnings.push('userProfiles ausente - será criado perfil padrão')
      return
    }

    if (Array.isArray(userProfiles)) {
      userProfiles.forEach((profile, index) => {
        this.requiredFields.userProfiles.forEach(field => {
          if (!profile.hasOwnProperty(field)) {
            results.warnings.push(`userProfiles[${index}].${field} ausente`)
          }
        })

        // Validações específicas
        if (profile.age && isNaN(parseInt(profile.age))) {
          results.warnings.push(`userProfiles[${index}].age deve ser numérico`)
        }

        if (profile.createdAt && !this._isValidDate(profile.createdAt)) {
          results.warnings.push(`userProfiles[${index}].createdAt com data inválida`)
        }
      })
    } else if (typeof userProfiles === 'object') {
      // Formato único (não array)
      this.requiredFields.userProfiles.forEach(field => {
        if (!userProfiles.hasOwnProperty(field)) {
          results.warnings.push(`userProfiles.${field} ausente`)
        }
      })
    }
  }

  /**
   * Valida progresso dos jogos
   */
  _validateGameProgress(gameProgress, results) {
    if (!gameProgress) {
      results.warnings.push('gameProgress ausente')
      return
    }

    Object.keys(gameProgress).forEach(gameKey => {
      const sessions = gameProgress[gameKey]
      
      if (Array.isArray(sessions)) {
        sessions.forEach((session, index) => {
          // Validar dificuldade
          if (session.difficulty && typeof session.difficulty === 'object' && Object.keys(session.difficulty).length === 0) {
            results.errors.push(`gameProgress.${gameKey}[${index}].difficulty está vazio (objeto vazio)`)
            results.isValid = false
          }

          if (session.difficulty && typeof session.difficulty === 'string' && !this.validDifficulties.includes(session.difficulty)) {
            results.warnings.push(`gameProgress.${gameKey}[${index}].difficulty inválida: ${session.difficulty}`)
          }

          // Validar métricas numéricas
          ['correctCount', 'timeSpent', 'accuracy', 'score'].forEach(metric => {
            if (session.hasOwnProperty(metric) && isNaN(Number(session[metric]))) {
              results.warnings.push(`gameProgress.${gameKey}[${index}].${metric} deve ser numérico`)
            }
          })

          // Validar timestamp
          if (session.timestamp && !this._isValidDate(session.timestamp)) {
            results.warnings.push(`gameProgress.${gameKey}[${index}].timestamp inválido`)
          }
        })
      }
    })
  }

  /**
   * Valida métricas dos jogos
   */
  _validateGameMetrics(gameMetrics, results) {
    if (!gameMetrics || Object.keys(gameMetrics).length === 0) {
      results.warnings.push('gameMetrics vazio - serão criadas métricas padrão')
      return
    }

    this.requiredFields.gameMetrics.forEach(field => {
      if (!gameMetrics.hasOwnProperty(field)) {
        results.suggestions.push(`Adicionar ${field} em gameMetrics`)
      }
    })
  }

  /**
   * Valida configurações de acessibilidade
   */
  _validateAccessibilitySettings(settings, results) {
    if (!settings) {
      results.warnings.push('accessibilitySettings ausente')
      return
    }

    this.requiredFields.accessibilitySettings.forEach(field => {
      if (!settings.hasOwnProperty(field)) {
        results.suggestions.push(`Adicionar ${field} em accessibilitySettings`)
      }
    })
  }

  /**
   * Valida dados de sessão
   */
  _validateSessionData(sessionData, results) {
    if (!sessionData || Object.keys(sessionData).length === 0) {
      results.warnings.push('sessionData vazio')
      return
    }

    this.requiredFields.sessionData.forEach(field => {
      if (!sessionData.hasOwnProperty(field)) {
        results.suggestions.push(`Adicionar ${field} em sessionData`)
      }
    })
  }

  /**
   * Valida metadados
   */
  _validateMetadata(metadata, results) {
    if (!metadata) {
      results.errors.push('metadata ausente')
      results.isValid = false
      return
    }

    this.requiredFields.metadata.forEach(field => {
      if (!metadata.hasOwnProperty(field)) {
        results.warnings.push(`metadata.${field} ausente`)
      }
    })
  }

  /**
   * Gera backup corrigido
   */
  _generateFixedBackup(originalBackup, validationResults) {
    const fixed = JSON.parse(JSON.stringify(originalBackup))

    // Corrigir campos obrigatórios ausentes
    if (!fixed.version) fixed.version = '3.1.0'
    if (!fixed.exportDate) fixed.exportDate = new Date().toISOString()
    if (!fixed.data) fixed.data = {}
    if (!fixed.metadata) fixed.metadata = {}

    // Corrigir userProfiles
    if (!fixed.data.userProfiles) {
      fixed.data.userProfiles = [{
        id: Date.now().toString(),
        name: 'Usuário',
        age: '5',
        avatar: '👤',
        preferences: {
          theme: 'default',
          difficulty: 'easy',
          soundEnabled: true,
          animationsEnabled: true
        },
        createdAt: new Date().toISOString(),
        lastUsed: new Date().toISOString(),
        gamesPlayed: 0,
        totalTime: 0
      }]
    }

    // Corrigir gameProgress - difficulty vazia
    if (fixed.data.gameProgress) {
      Object.keys(fixed.data.gameProgress).forEach(gameKey => {
        const sessions = fixed.data.gameProgress[gameKey]
        if (Array.isArray(sessions)) {
          sessions.forEach(session => {
            if (session.difficulty && typeof session.difficulty === 'object' && Object.keys(session.difficulty).length === 0) {
              session.difficulty = 'easy' // Valor padrão
            }
          })
        }
      })
    }

    // Corrigir gameMetrics vazio
    if (!fixed.data.gameMetrics || Object.keys(fixed.data.gameMetrics).length === 0) {
      fixed.data.gameMetrics = {
        totalGamesPlayed: 0,
        averageSessionTime: 60000,
        improvementTrend: 'stable',
        lastWeekActivity: 0
      }
    }

    // Corrigir sessionData vazio
    if (!fixed.data.sessionData || Object.keys(fixed.data.sessionData).length === 0) {
      fixed.data.sessionData = {
        currentStreak: 0,
        longestStreak: 0,
        weeklyGoal: 15,
        weeklyProgress: 0,
        lastLoginDate: new Date().toISOString()
      }
    }

    // Corrigir accessibilitySettings
    if (!fixed.data.accessibilitySettings) {
      fixed.data.accessibilitySettings = {
        textToSpeech: true,
        highContrast: false,
        reducedMotion: false,
        colorScheme: 'default',
        dyslexiaFriendly: false,
        fontSize: 'medium',
        soundEnabled: true,
        autoRead: false
      }
    }

    // Corrigir metadata
    if (!fixed.metadata.source) fixed.metadata.source = 'manual_backup'
    if (!fixed.metadata.totalItems) fixed.metadata.totalItems = this._countItems(fixed.data)
    if (!fixed.metadata.categories) fixed.metadata.categories = Object.keys(fixed.data)

    return fixed
  }

  /**
   * Conta itens no backup
   */
  _countItems(data) {
    let count = 0
    Object.values(data).forEach(value => {
      if (Array.isArray(value)) {
        count += value.length
      } else if (value && typeof value === 'object') {
        count += Object.keys(value).length
      } else {
        count++
      }
    })
    return count
  }

  /**
   * Valida se uma string é uma data válida
   */
  _isValidDate(dateString) {
    const date = new Date(dateString)
    return date instanceof Date && !isNaN(date)
  }
}

// Exportar para uso
if (typeof module !== 'undefined' && module.exports) {
  module.exports = BackupValidator
} else if (typeof window !== 'undefined') {
  window.BackupValidator = BackupValidator
}

// Exemplo de uso:
/*
const validator = new BackupValidator()
const resultado = validator.validateBackup(dadosDoBackup)

if (!resultado.isValid) {
  console.error('Backup inválido:', resultado.errors)
  console.log('Dados corrigidos:', resultado.fixedData)
} else {
  console.log('Backup válido!')
}
*/
