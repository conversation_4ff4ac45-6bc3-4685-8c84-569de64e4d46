{"name": "sanitize-html", "version": "2.17.0", "description": "Clean up user-submitted HTML, preserving allowlisted elements and allowlisted attributes on a per-element basis", "sideEffects": false, "main": "index.js", "files": ["index.js"], "scripts": {"test": "npx eslint . && mocha test/test.js"}, "repository": {"type": "git", "url": "https://github.com/apostrophecms/sanitize-html.git"}, "keywords": ["html", "parser", "sanitizer", "sanitize"], "author": "Apostrophe Technologies, Inc.", "license": "MIT", "dependencies": {"deepmerge": "^4.2.2", "escape-string-regexp": "^4.0.0", "htmlparser2": "^8.0.0", "is-plain-object": "^5.0.0", "parse-srcset": "^1.0.2", "postcss": "^8.3.11"}, "devDependencies": {"eslint": "^7.3.1", "eslint-config-apostrophe": "^3.4.0", "eslint-config-standard": "^14.1.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "mocha": "^10.2.0", "sinon": "^9.0.2"}}