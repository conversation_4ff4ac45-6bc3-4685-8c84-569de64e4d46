/**
 * 👁️ VISUAL PROCESSING ANALYZER
 * Analisador especializado em processamento visual e espacial
 * Portal Betina V3 - FASE CRÍTICA
 */

export class VisualProcessingAnalyzer {
  constructor() {
    this.analyzerType = 'VisualProcessing';
    this.version = '3.0.0';
    this.capabilities = {
      spatialProcessing: true,
      visualMemory: true,
      patternRecognition: true,
      visualMotorIntegration: true,
      perceptualSpeed: true
    };
  }

  /**
   * Analisa dados de processamento visual
   * @param {Object} visualData - Dados do VisualProcessingCollector
   * @param {Object} gameContext - Contexto do jogo
   * @returns {Promise<Object>} Análise de processamento visual avançada
   */
  async analyze(visualData, gameContext = {}) {
    try {
      console.log('👁️ VisualProcessingAnalyzer: Iniciando análise visual...');

      if (!this.validateVisualData(visualData)) {
        return this.generateFallbackAnalysis('Dados visuais insuficientes');
      }

      // Análises principais baseadas nos dados do VisualProcessingCollector
      const spatialAnalysis = this.analyzeSpatialProcessing(visualData);
      const perceptualAnalysis = this.analyzePerceptualAbilities(visualData);
      const visualMemoryAnalysis = this.analyzeVisualMemory(visualData);
      const integrationAnalysis = this.analyzeVisualMotorIntegration(visualData);
      const therapyProfile = this.generateTherapyProfile(visualData, gameContext);

      const results = {
        // Processamento espacial
        spatialAwareness: spatialAnalysis.awareness,
        spatialOrientation: spatialAnalysis.orientation,
        spatialVisualization: spatialAnalysis.visualization,
        spatialRelations: spatialAnalysis.relations,
        
        // Capacidades perceptuais
        visualDiscrimination: perceptualAnalysis.discrimination,
        figureGroundDiscrimination: perceptualAnalysis.figureGround,
        visualClosure: perceptualAnalysis.closure,
        patternRecognition: perceptualAnalysis.patterns,
        
        // Memória visual
        visualMemorySpan: visualMemoryAnalysis.span,
        visualSequencing: visualMemoryAnalysis.sequencing,
        visualRecall: visualMemoryAnalysis.recall,
        visualRecognition: visualMemoryAnalysis.recognition,
        
        // Integração visuo-motora
        handEyeCoordination: integrationAnalysis.coordination,
        visualMotorSpeed: integrationAnalysis.speed,
        visualMotorAccuracy: integrationAnalysis.accuracy,
        visuoSpatialSkills: integrationAnalysis.visuospatial,
        
        // Velocidade perceptual
        processingSpeed: this.analyzeProcessingSpeed(visualData),
        reactionTime: this.analyzeReactionTime(visualData),
        scanningSpeed: this.analyzeScanningSpeed(visualData),
        
        // Perfil terapêutico
        visualProfile: therapyProfile.profile,
        strengthAreas: therapyProfile.strengths,
        deficitAreas: therapyProfile.deficits,
        interventionPriorities: therapyProfile.priorities,
        
        // Análises específicas
        colorProcessing: this.analyzeColorProcessing(visualData),
        contrastSensitivity: this.analyzeContrastSensitivity(visualData),
        depthPerception: this.analyzeDepthPerception(visualData),
        
        // Padrões de desenvolvimento
        developmentalLevel: this.assessDevelopmentalLevel(visualData),
        visualMaturity: this.assessVisualMaturity(visualData),
        ageAppropriate: this.assessAgeAppropriateness(visualData, gameContext),
        
        // Indicadores clínicos
        dyslexia_indicators: this.assessDyslexiaIndicators(visualData),
        visualProcessingDisorder_indicators: this.assessVPDIndicators(visualData),
        autism_visualTraits: this.assessAutismVisualTraits(visualData),
        
        // Recomendações terapêuticas
        visualTherapy: this.recommendVisualTherapy(visualData),
        adaptiveStrategies: this.recommendAdaptiveStrategies(visualData),
        environmentalMods: this.recommendEnvironmentalMods(visualData),
        
        // Habilidades acadêmicas relacionadas
        readingReadiness: this.assessReadingReadiness(visualData),
        writingReadiness: this.assessWritingReadiness(visualData),
        mathReadiness: this.assessMathReadiness(visualData),
        
        // Análise de progresso
        improvementTrajectory: this.analyzeImprovementTrajectory(visualData),
        therapyResponsePrediction: this.predictTherapyResponse(visualData),
        skillTransferPotential: this.assessSkillTransfer(visualData),
        
        // Metadados
        analysisTimestamp: new Date().toISOString(),
        analyzerVersion: this.version,
        dataQuality: this.assessAnalysisQuality(visualData),
        gameContext: gameContext.gameType || 'unknown'
      };

      console.log('✅ VisualProcessingAnalyzer: Análise concluída:', {
        spatial: Math.round(results.spatialAwareness * 100),
        perceptual: Math.round(results.visualDiscrimination * 100),
        memory: Math.round(results.visualMemorySpan * 100)
      });

      return results;

    } catch (error) {
      console.error('❌ VisualProcessingAnalyzer: Erro na análise:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de entrada do VisualProcessingCollector
   */
  validateVisualData(data) {
    if (!data) return false;
    
    const requiredFields = ['spatialAwareness', 'visualDiscrimination', 'visualMemory'];
    return requiredFields.some(field => data.hasOwnProperty(field));
  }

  /**
   * Analisa processamento espacial
   */
  analyzeSpatialProcessing(data) {
    return {
      awareness: data.spatialAwareness || 0.5,
      orientation: data.spatialOrientation || 0.5,
      visualization: data.spatialVisualization || 0.5,
      relations: data.spatialRelations || 0.5
    };
  }

  /**
   * Analisa capacidades perceptuais
   */
  analyzePerceptualAbilities(data) {
    return {
      discrimination: data.visualDiscrimination || 0.5,
      figureGround: data.figureGroundDiscrimination || 0.5,
      closure: data.visualClosure || 0.5,
      patterns: data.patternRecognition || 0.5
    };
  }

  /**
   * Analisa memória visual
   */
  analyzeVisualMemory(data) {
    return {
      span: data.visualMemory || 0.5,
      sequencing: data.visualSequencing || 0.5,
      recall: data.visualRecall || 0.5,
      recognition: data.visualRecognition || 0.5
    };
  }

  /**
   * Analisa integração visuo-motora
   */
  analyzeVisualMotorIntegration(data) {
    return {
      coordination: data.handEyeCoordination || 0.5,
      speed: data.visualMotorSpeed || 0.5,
      accuracy: data.visualMotorAccuracy || 0.5,
      visuospatial: data.visuoSpatialSkills || 0.5
    };
  }

  /**
   * Gera perfil terapêutico
   */
  generateTherapyProfile(data, context) {
    const profile = this.classifyVisualProfile(data);
    const strengths = this.identifyVisualStrengths(data);
    const deficits = this.identifyVisualDeficits(data);
    const priorities = this.prioritizeInterventions(deficits, strengths);
    
    return { profile, strengths, deficits, priorities };
  }

  /**
   * Classifica perfil visual
   */
  classifyVisualProfile(data) {
    const spatial = data.spatialAwareness || 0.5;
    const discrimination = data.visualDiscrimination || 0.5;
    const memory = data.visualMemory || 0.5;
    const motor = data.handEyeCoordination || 0.5;
    
    const average = (spatial + discrimination + memory + motor) / 4;
    
    if (average > 0.8) {
      return 'superior_visual_processing';
    } else if (spatial > 0.7 && discrimination > 0.7) {
      return 'strong_visuospatial';
    } else if (memory > 0.7 && discrimination > 0.7) {
      return 'strong_visual_memory';
    } else if (motor > 0.7) {
      return 'strong_visual_motor';
    } else if (average < 0.4) {
      return 'visual_processing_challenges';
    } else if (spatial < 0.4) {
      return 'spatial_processing_deficit';
    } else if (discrimination < 0.4) {
      return 'perceptual_processing_deficit';
    }
    
    return 'mixed_visual_profile';
  }

  /**
   * Identifica forças visuais
   */
  identifyVisualStrengths(data) {
    const strengths = [];
    
    if ((data.spatialAwareness || 0) > 0.7) strengths.push('spatial_awareness');
    if ((data.visualDiscrimination || 0) > 0.7) strengths.push('visual_discrimination');
    if ((data.visualMemory || 0) > 0.7) strengths.push('visual_memory');
    if ((data.handEyeCoordination || 0) > 0.7) strengths.push('visual_motor_integration');
    if ((data.patternRecognition || 0) > 0.7) strengths.push('pattern_recognition');
    if ((data.visualClosure || 0) > 0.7) strengths.push('visual_closure');
    
    return strengths;
  }

  /**
   * Identifica déficits visuais
   */
  identifyVisualDeficits(data) {
    const deficits = [];
    
    if ((data.spatialAwareness || 0) < 0.5) deficits.push('spatial_awareness');
    if ((data.visualDiscrimination || 0) < 0.5) deficits.push('visual_discrimination');
    if ((data.visualMemory || 0) < 0.5) deficits.push('visual_memory');
    if ((data.handEyeCoordination || 0) < 0.5) deficits.push('visual_motor_integration');
    if ((data.figureGroundDiscrimination || 0) < 0.5) deficits.push('figure_ground_discrimination');
    if ((data.visualClosure || 0) < 0.5) deficits.push('visual_closure');
    
    return deficits;
  }

  /**
   * Prioriza intervenções
   */
  prioritizeInterventions(deficits, strengths) {
    const priorities = [];
    
    // Prioridades baseadas em impacto funcional
    if (deficits.includes('visual_discrimination')) {
      priorities.push('Treino de discriminação visual com estímulos graduados');
    }
    if (deficits.includes('spatial_awareness')) {
      priorities.push('Atividades de orientação espacial e mapeamento');
    }
    if (deficits.includes('visual_memory')) {
      priorities.push('Exercícios de memória visual sequencial');
    }
    if (deficits.includes('visual_motor_integration')) {
      priorities.push('Coordenação visuo-motora com feedback visual');
    }
    
    // Usar forças para compensar fraquezas
    if (strengths.includes('pattern_recognition') && deficits.includes('visual_memory')) {
      priorities.push('Usar reconhecimento de padrões para fortalecer memória visual');
    }
    
    return priorities;
  }

  /**
   * Analisa velocidade de processamento
   */
  analyzeProcessingSpeed(data) {
    const speed = data.processingSpeed || 0.5;
    const efficiency = data.processingEfficiency || 0.5;
    
    return {
      speed,
      efficiency,
      combined: (speed * 0.6) + (efficiency * 0.4)
    };
  }

  /**
   * Analisa tempo de reação
   */
  analyzeReactionTime(data) {
    const simple = data.simpleReactionTime || 0.5;
    const choice = data.choiceReactionTime || 0.5;
    const visual = data.visualReactionTime || 0.5;
    
    return {
      simple,
      choice,
      visual,
      average: (simple + choice + visual) / 3
    };
  }

  /**
   * Analisa velocidade de varredura
   */
  analyzeScanningSpeed(data) {
    const scanning = data.visualScanningSpeed || 0.5;
    const search = data.visualSearchSpeed || 0.5;
    
    return {
      scanning,
      search,
      efficiency: (scanning + search) / 2
    };
  }

  /**
   * Analisa processamento de cores
   */
  analyzeColorProcessing(data) {
    return {
      discrimination: data.colorDiscrimination || 0.5,
      memory: data.colorMemory || 0.5,
      constancy: data.colorConstancy || 0.5,
      preference: data.colorPreference || 'unknown'
    };
  }

  /**
   * Analisa sensibilidade ao contraste
   */
  analyzeContrastSensitivity(data) {
    return {
      high_contrast: data.highContrastSensitivity || 0.5,
      low_contrast: data.lowContrastSensitivity || 0.5,
      adaptation: data.contrastAdaptation || 0.5
    };
  }

  /**
   * Analisa percepção de profundidade
   */
  analyzeDepthPerception(data) {
    return {
      stereopsis: data.stereopsis || 0.5,
      monocular_cues: data.monocularDepthCues || 0.5,
      depth_discrimination: data.depthDiscrimination || 0.5
    };
  }

  /**
   * Avalia nível de desenvolvimento
   */
  assessDevelopmentalLevel(data) {
    const spatial = data.spatialAwareness || 0.5;
    const visual = data.visualDiscrimination || 0.5;
    const motor = data.handEyeCoordination || 0.5;
    
    const average = (spatial + visual + motor) / 3;
    
    if (average > 0.8) return 'advanced';
    if (average > 0.6) return 'age_appropriate';
    if (average > 0.4) return 'emerging';
    return 'delayed';
  }

  /**
   * Avalia maturidade visual
   */
  assessVisualMaturity(data) {
    const complexity = data.complexVisualProcessing || 0.5;
    const integration = data.visualIntegration || 0.5;
    
    return {
      level: (complexity + integration) / 2,
      readiness: complexity > 0.6 && integration > 0.6
    };
  }

  /**
   * Avalia adequação à idade
   */
  assessAgeAppropriateness(data, context) {
    const expectedLevel = this.getExpectedLevel(context.age);
    const actualLevel = this.assessDevelopmentalLevel(data);
    
    return {
      expected: expectedLevel,
      actual: actualLevel,
      appropriate: this.compareToExpected(actualLevel, expectedLevel)
    };
  }

  /**
   * Obtém nível esperado para idade
   */
  getExpectedLevel(age) {
    if (!age) return 'unknown';
    
    if (age < 5) return 'emerging';
    if (age < 8) return 'developing';
    if (age < 12) return 'age_appropriate';
    return 'mature';
  }

  /**
   * Compara com nível esperado
   */
  compareToExpected(actual, expected) {
    const levels = ['delayed', 'emerging', 'developing', 'age_appropriate', 'advanced', 'mature'];
    const actualIndex = levels.indexOf(actual);
    const expectedIndex = levels.indexOf(expected);
    
    if (actualIndex >= expectedIndex) return 'appropriate';
    if (actualIndex >= expectedIndex - 1) return 'slightly_below';
    return 'significantly_below';
  }

  /**
   * Avalia indicadores de dislexia
   */
  assessDyslexiaIndicators(data) {
    const indicators = [];
    
    if ((data.visualSequencing || 0) < 0.5) indicators.push('poor_visual_sequencing');
    if ((data.visualMemory || 0) < 0.5) indicators.push('weak_visual_memory');
    if ((data.figureGroundDiscrimination || 0) < 0.5) indicators.push('figure_ground_difficulty');
    if ((data.visualTracking || 0) < 0.5) indicators.push('tracking_problems');
    
    return {
      indicators,
      riskLevel: this.calculateDyslexiaRisk(indicators),
      recommendation: this.generateDyslexiaRecommendation(indicators)
    };
  }

  /**
   * Calcula risco de dislexia
   */
  calculateDyslexiaRisk(indicators) {
    if (indicators.length >= 3) return 'high';
    if (indicators.length >= 2) return 'moderate';
    if (indicators.length >= 1) return 'low';
    return 'minimal';
  }

  /**
   * Gera recomendação para dislexia
   */
  generateDyslexiaRecommendation(indicators) {
    if (indicators.length >= 3) {
      return 'Avaliação completa de dislexia recomendada';
    } else if (indicators.length >= 2) {
      return 'Monitoramento de habilidades de leitura necessário';
    }
    return 'Desenvolvimento visual típico para leitura';
  }

  /**
   * Avalia indicadores de distúrbio de processamento visual
   */
  assessVPDIndicators(data) {
    const indicators = [];
    
    if ((data.spatialAwareness || 0) < 0.4) indicators.push('poor_spatial_processing');
    if ((data.visualDiscrimination || 0) < 0.4) indicators.push('discrimination_deficit');
    if ((data.visualMemory || 0) < 0.4) indicators.push('visual_memory_deficit');
    if ((data.handEyeCoordination || 0) < 0.4) indicators.push('visual_motor_deficit');
    
    return {
      indicators,
      severity: this.calculateVPDSeverity(indicators),
      domains: this.identifyAffectedDomains(indicators)
    };
  }

  /**
   * Calcula severidade do distúrbio de processamento visual
   */
  calculateVPDSeverity(indicators) {
    if (indicators.length >= 3) return 'severe';
    if (indicators.length >= 2) return 'moderate';
    if (indicators.length >= 1) return 'mild';
    return 'no_significant_deficit';
  }

  /**
   * Identifica domínios afetados
   */
  identifyAffectedDomains(indicators) {
    const domains = [];
    
    if (indicators.includes('poor_spatial_processing')) domains.push('spatial');
    if (indicators.includes('discrimination_deficit')) domains.push('perceptual');
    if (indicators.includes('visual_memory_deficit')) domains.push('memory');
    if (indicators.includes('visual_motor_deficit')) domains.push('motor');
    
    return domains;
  }

  /**
   * Avalia traços visuais do autismo
   */
  assessAutismVisualTraits(data) {
    const traits = [];
    
    if ((data.detailOrientation || 0) > 0.7) traits.push('enhanced_detail_focus');
    if ((data.globalProcessing || 0) < 0.5) traits.push('weak_global_processing');
    if ((data.visualSystematizing || 0) > 0.7) traits.push('strong_systematizing');
    if ((data.sensorySeekingVisual || 0) > 0.7) traits.push('visual_seeking');
    
    return {
      traits,
      profile: this.generateAutismVisualProfile(traits)
    };
  }

  /**
   * Gera perfil visual do autismo
   */
  generateAutismVisualProfile(traits) {
    if (traits.includes('enhanced_detail_focus') && traits.includes('weak_global_processing')) {
      return 'local_processing_bias';
    } else if (traits.includes('strong_systematizing')) {
      return 'systematizing_strength';
    } else if (traits.includes('visual_seeking')) {
      return 'sensory_seeking_profile';
    }
    return 'typical_visual_profile';
  }

  /**
   * Recomenda terapia visual
   */
  recommendVisualTherapy(data) {
    const recommendations = [];
    
    if ((data.spatialAwareness || 0) < 0.6) {
      recommendations.push('Terapia de orientação espacial e navegação');
    }
    if ((data.visualDiscrimination || 0) < 0.6) {
      recommendations.push('Exercícios de discriminação visual graduados');
    }
    if ((data.handEyeCoordination || 0) < 0.6) {
      recommendations.push('Terapia de coordenação visuo-motora');
    }
    if ((data.visualMemory || 0) < 0.6) {
      recommendations.push('Treino de memória visual sequencial');
    }
    
    return recommendations;
  }

  /**
   * Recomenda estratégias adaptativas
   */
  recommendAdaptiveStrategies(data) {
    const strategies = [];
    
    if ((data.visualDiscrimination || 0) < 0.6) {
      strategies.push('Usar marcadores visuais distintos');
    }
    if ((data.spatialAwareness || 0) < 0.6) {
      strategies.push('Organização espacial com pontos de referência');
    }
    if ((data.visualMemory || 0) < 0.6) {
      strategies.push('Chunking visual e associações');
    }
    
    return strategies;
  }

  /**
   * Recomenda modificações ambientais
   */
  recommendEnvironmentalMods(data) {
    const modifications = [];
    
    if ((data.figureGroundDiscrimination || 0) < 0.5) {
      modifications.push('Reduzir desordem visual no ambiente');
    }
    if ((data.contrastSensitivity || 0) < 0.5) {
      modifications.push('Aumentar contraste em materiais');
    }
    if ((data.lightingSensitivity || 0) > 0.7) {
      modifications.push('Controlar iluminação do ambiente');
    }
    
    return modifications;
  }

  /**
   * Avalia prontidão para leitura
   */
  assessReadingReadiness(data) {
    const tracking = data.visualTracking || 0.5;
    const sequencing = data.visualSequencing || 0.5;
    const discrimination = data.visualDiscrimination || 0.5;
    
    const readiness = (tracking * 0.4) + (sequencing * 0.3) + (discrimination * 0.3);
    
    return {
      score: readiness,
      level: readiness > 0.7 ? 'ready' : readiness > 0.5 ? 'emerging' : 'not_ready',
      areas_to_develop: this.identifyReadingAreas(data)
    };
  }

  /**
   * Identifica áreas a desenvolver para leitura
   */
  identifyReadingAreas(data) {
    const areas = [];
    
    if ((data.visualTracking || 0) < 0.6) areas.push('visual_tracking');
    if ((data.visualSequencing || 0) < 0.6) areas.push('visual_sequencing');
    if ((data.visualDiscrimination || 0) < 0.6) areas.push('letter_discrimination');
    
    return areas;
  }

  /**
   * Avalia prontidão para escrita
   */
  assessWritingReadiness(data) {
    const motor = data.handEyeCoordination || 0.5;
    const spatial = data.spatialAwareness || 0.5;
    const visual = data.visualMotorSpeed || 0.5;
    
    const readiness = (motor * 0.5) + (spatial * 0.3) + (visual * 0.2);
    
    return {
      score: readiness,
      level: readiness > 0.7 ? 'ready' : readiness > 0.5 ? 'developing' : 'needs_support'
    };
  }

  /**
   * Avalia prontidão para matemática
   */
  assessMathReadiness(data) {
    const spatial = data.spatialAwareness || 0.5;
    const patterns = data.patternRecognition || 0.5;
    const visual = data.visualDiscrimination || 0.5;
    
    const readiness = (spatial * 0.5) + (patterns * 0.3) + (visual * 0.2);
    
    return {
      score: readiness,
      level: readiness > 0.7 ? 'strong' : readiness > 0.5 ? 'adequate' : 'needs_development'
    };
  }

  /**
   * Analisa trajetória de melhoria
   */
  analyzeImprovementTrajectory(data) {
    const baseline = data.baselinePerformance || 0.5;
    const current = data.currentPerformance || 0.5;
    const trend = data.improvementTrend || 0;
    
    return {
      improvement: current - baseline,
      rate: trend,
      projection: this.projectFutureImprovement(current, trend)
    };
  }

  /**
   * Projeta melhoria futura
   */
  projectFutureImprovement(current, trend) {
    const projected = current + (trend * 3); // 3 meses
    
    if (projected > 0.8) return 'excellent_prognosis';
    if (projected > 0.6) return 'good_prognosis';
    if (projected > 0.4) return 'fair_prognosis';
    return 'guarded_prognosis';
  }

  /**
   * Prevê resposta à terapia
   */
  predictTherapyResponse(data) {
    const plasticity = data.neuralPlasticity || 0.5;
    const motivation = data.engagementLevel || 0.5;
    const baseline = data.baselinePerformance || 0.5;
    
    const response = (plasticity * 0.4) + (motivation * 0.3) + ((1 - baseline) * 0.3);
    
    return {
      prediction: response,
      level: response > 0.7 ? 'excellent' : response > 0.5 ? 'good' : 'moderate'
    };
  }

  /**
   * Avalia potencial de transferência de habilidades
   */
  assessSkillTransfer(data) {
    const generalization = data.skillGeneralization || 0.5;
    const flexibility = data.cognitiveFlexibility || 0.5;
    
    return {
      potential: (generalization + flexibility) / 2,
      domains: this.identifyTransferDomains(data)
    };
  }

  /**
   * Identifica domínios de transferência
   */
  identifyTransferDomains(data) {
    const domains = [];
    
    if ((data.spatialAwareness || 0) > 0.6) domains.push('spatial_tasks');
    if ((data.visualDiscrimination || 0) > 0.6) domains.push('perceptual_tasks');
    if ((data.handEyeCoordination || 0) > 0.6) domains.push('motor_tasks');
    
    return domains;
  }

  /**
   * Avalia qualidade da análise
   */
  assessAnalysisQuality(data) {
    let score = 0.5;
    const issues = [];
    
    if (!data.spatialAwareness) {
      score -= 0.15;
      issues.push('Dados espaciais ausentes');
    }
    if (!data.visualDiscrimination) {
      score -= 0.15;
      issues.push('Dados de discriminação visual ausentes');
    }
    if (!data.visualMemory) {
      score -= 0.1;
      issues.push('Dados de memória visual ausentes');
    }
    if (!data.handEyeCoordination) {
      score -= 0.1;
      issues.push('Dados de coordenação visuo-motora ausentes');
    }
    
    return {
      score: Math.max(0, score),
      issues,
      level: score > 0.7 ? 'high' : score > 0.4 ? 'medium' : 'low'
    };
  }

  /**
   * Gera análise de fallback
   */
  generateFallbackAnalysis(errorMessage) {
    return {
      spatialAwareness: 0.5,
      spatialOrientation: 0.5,
      spatialVisualization: 0.5,
      spatialRelations: 0.5,
      visualDiscrimination: 0.5,
      figureGroundDiscrimination: 0.5,
      visualClosure: 0.5,
      patternRecognition: 0.5,
      visualMemorySpan: 0.5,
      visualSequencing: 0.5,
      visualRecall: 0.5,
      visualRecognition: 0.5,
      handEyeCoordination: 0.5,
      visualMotorSpeed: 0.5,
      visualMotorAccuracy: 0.5,
      visuoSpatialSkills: 0.5,
      processingSpeed: { speed: 0.5, efficiency: 0.5, combined: 0.5 },
      reactionTime: { simple: 0.5, choice: 0.5, visual: 0.5, average: 0.5 },
      scanningSpeed: { scanning: 0.5, search: 0.5, efficiency: 0.5 },
      visualProfile: 'mixed_visual_profile',
      strengthAreas: [],
      deficitAreas: ['insufficient_data'],
      interventionPriorities: ['Coletar dados visuais baseline'],
      colorProcessing: { discrimination: 0.5, memory: 0.5, constancy: 0.5, preference: 'unknown' },
      contrastSensitivity: { high_contrast: 0.5, low_contrast: 0.5, adaptation: 0.5 },
      depthPerception: { stereopsis: 0.5, monocular_cues: 0.5, depth_discrimination: 0.5 },
      developmentalLevel: 'unknown',
      visualMaturity: { level: 0.5, readiness: false },
      ageAppropriate: { expected: 'unknown', actual: 'unknown', appropriate: 'unknown' },
      dyslexia_indicators: { indicators: [], riskLevel: 'unknown', recommendation: 'Avaliação necessária' },
      visualProcessingDisorder_indicators: { indicators: [], severity: 'unknown', domains: [] },
      autism_visualTraits: { traits: [], profile: 'unknown' },
      visualTherapy: ['Estabelecer capacidades baseline'],
      adaptiveStrategies: ['Avaliar necessidades visuais'],
      environmentalMods: ['Analisar ambiente visual'],
      readingReadiness: { score: 0.5, level: 'unknown', areas_to_develop: [] },
      writingReadiness: { score: 0.5, level: 'unknown' },
      mathReadiness: { score: 0.5, level: 'unknown' },
      improvementTrajectory: { improvement: 0, rate: 0, projection: 'unknown' },
      therapyResponsePrediction: { prediction: 0.5, level: 'unknown' },
      skillTransferPotential: { potential: 0.5, domains: [] },
      analysisTimestamp: new Date().toISOString(),
      analyzerVersion: this.version,
      dataQuality: { score: 0, issues: [errorMessage], level: 'error' },
      gameContext: 'unknown',
      status: 'fallback',
      error: errorMessage
    };
  }
}
