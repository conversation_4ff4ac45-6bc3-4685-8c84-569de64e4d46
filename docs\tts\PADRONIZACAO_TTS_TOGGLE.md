# 🔊 Padronização do Toggle TTS nos Jogos

## 📋 Visão Geral
Este documento descreve como implementar o sistema de toggle TTS (Text-to-Speech) padronizado em todos os jogos do Portal Betina V3, baseado na implementação de referência do jogo **Padrões Visuais**.

## 🎯 Objetivo
Permitir que crianças neurodivergentes tenham controle total sobre o áudio dos jogos, podendo ativar/desativar o TTS conforme sua preferência, especialmente para aquelas que não gostam de volume ou vozes.

## ⚠️ Correções Importantes Implementadas
- **✅ TTS não inicia automaticamente**: Corrigido timing de execução - TTS executa APÓS carregamento da fase
- **✅ TTS continua após sair do jogo**: Implementado cleanup automático via useEffect
- **✅ Indicadores visuais desnecessários**: Removidos para manter interface limpa
- **✅ Estados visuais inconsistentes**: Padronizados cores e comportamentos
- **✅ Preferências não persistem**: localStorage implementado corretamente

## 🔧 Implementação Padrão

### 1. **Estados Necessários**
Adicionar no início do componente do jogo:

```jsx
// Estados TTS (adicionar aos estados existentes)
const [ttsActive, setTtsActive] = useState(true) // TTS ativo por padrão
```

### 2. **Função Toggle TTS**
Adicionar antes do `return` do componente:

```jsx
// Função para toggle do TTS
const toggleTTS = () => {
  setTtsActive(prev => {
    const newState = !prev;
    console.log(`🔊 TTS ${newState ? 'ativado' : 'desativado'}`);
    
    // Se desativando, parar qualquer fala em andamento
    if (!newState && 'speechSynthesis' in window) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
    }
    
    // Salvar preferência no localStorage
    localStorage.setItem('[NOME_DO_JOGO]TTS', newState.toString());
    
    return newState;
  });
};
```

### 3. **Carregar Preferência do localStorage**
Adicionar useEffect para carregar a preferência:

```jsx
// Carregar preferência do TTS do localStorage
useEffect(() => {
  const savedTTSState = localStorage.getItem('[NOME_DO_JOGO]TTS');
  if (savedTTSState !== null) {
    setTtsActive(savedTTSState === 'true');
  }
}, []);

// Cleanup do TTS quando componente for desmontado
useEffect(() => {
  return () => {
    // Parar qualquer TTS em andamento ao sair do jogo
    if ('speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    }
    console.log('🔇 TTS parado ao sair do jogo [NOME_DO_JOGO]');
  };
}, []);
```

### 4. **Modificar Função `speak()`**
Adicionar verificação no início da função `speak()`:

```jsx
const speak = (text, options = {}) => {
  // Verificar se TTS está ativo no jogo
  if (!ttsActive) {
    console.log('🔇 TTS desativado - não reproduzindo áudio');
    return;
  }
  
  // ...resto da função speak existente
};
```

### 5. **Botão TTS Toggle no Header**
Substituir o botão TTS existente no header:

```jsx
{/* Botão TTS Toggle */}
<button 
  className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : styles.ttsInactive}`}
  onClick={toggleTTS}
  title={ttsActive ? "Desativar TTS" : "Ativar TTS"}
  aria-label={ttsActive ? "Desativar Text-to-Speech" : "Ativar Text-to-Speech"}
>
  {ttsActive ? '🔊' : '🔇'}
</button>
```

### 6. **Desabilitar Outros Botões TTS**
Adicionar `disabled={!ttsActive || ...}` em todos os botões TTS:

```jsx
{/* Exemplo de botão TTS desabilitado */}
<button 
  className={`${styles.ttsButton} ${styles.ttsExplain}`}
  onClick={explainGame}
  disabled={!ttsActive || (isSpeaking && currentSpeechButton !== 'explain')}
  title={ttsActive ? "Explicar jogo" : "TTS desativado"}
>
  <span className={styles.ttsIcon}>❓</span>
  <span className={styles.ttsLabel}>Explicar</span>
</button>
```

### 7. **TTS Automático no Início do Jogo**
Adicionar TTS de boas-vindas na função `startGame`:

```jsx
const startGame = async (selectedDifficulty) => {
  // ...configurações iniciais
  
  setTimeout(() => {
    // Carregar primeira fase/nível
    loadCurrentPhase(); // ou generateNewLevel()
    
    // TTS de boas-vindas APÓS carregamento
    const welcomeMessage = `Jogo iniciado! Dificuldade ${selectedDifficulty}. Prepare-se para a primeira sequência.`;
    speak(welcomeMessage, {
      rate: 0.9,
      onEnd: () => console.log('Boas-vindas anunciadas')
    });
  }, 100);
};
```

### 8. **Indicador de Status (REMOVIDO)**
~~Não adicionar indicadores visuais de status - manter interface limpa~~

### 9. **Estilos CSS Necessários**
Adicionar no arquivo `.module.css` do jogo:

```css
/* Estados específicos do botão TTS */
.headerTtsButton.ttsActive {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.4);
  color: white;
}

.headerTtsButton.ttsActive:hover {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.6);
}

.headerTtsButton.ttsInactive {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.4);
  color: white;
  opacity: 0.7;
}

.headerTtsButton.ttsInactive:hover {
  background: rgba(244, 67, 54, 0.3);
  border-color: rgba(244, 67, 54, 0.6);
  opacity: 1;
}

/* Botões TTS para instruções */
.instructionsTTS {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.ttsButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
  font-size: 0.8rem;
  min-width: 80px;
}

.ttsButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.ttsIcon {
  font-size: 1.2rem;
  display: block;
}

.ttsLabel {
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Botões TTS desabilitados */
.ttsButton:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: rgba(128, 128, 128, 0.1);
  border-color: rgba(128, 128, 128, 0.2);
}

.ttsButton:disabled:hover {
  background: rgba(128, 128, 128, 0.1);
  border-color: rgba(128, 128, 128, 0.2);
  transform: none;
}
```

## 📋 Notas de Implementação

### ✅ **Funcionalidades Essenciais Implementadas:**
1. **Toggle TTS no header** - Controle visual claro (🔊/🔇)
2. **TTS automático no início** - Anuncia dificuldade selecionada
3. **Cleanup automático** - Para TTS ao sair do jogo
4. **Persistência de preferência** - Salva no localStorage
5. **Estados visuais consistentes** - Verde (ativo) / Vermelho (inativo)
6. **Desabilitação de botões** - Botões TTS ficam inativos quando desabilitado

### 🎯 **Comportamento Esperado:**
- **Início do jogo**: TTS ativo por padrão, fala automaticamente
- **Toggle do botão**: Alterna instantaneamente entre ativo/inativo
- **Saída do jogo**: TTS para automaticamente
- **Retorno ao jogo**: Preferência é mantida (ativo/inativo)

### 🔧 **Para Desenvolvedores:**
- Seguir exatamente o padrão dos jogos de referência
- Testar especialmente o cleanup ao navegar entre páginas
- Verificar se localStorage está funcionando corretamente
- Certificar-se de que TTS inicia automaticamente quando ativo

---

**📌 Importante**: Este documento reflete as correções implementadas nos jogos Padrões Visuais e Associação de Imagens. Use como referência definitiva para os próximos jogos.

## 📝 Checklist de Implementação

### Para cada jogo, seguir esta ordem:

- [ ] **1. Adicionar estado `ttsActive`**
- [ ] **2. Implementar função `toggleTTS()`**
- [ ] **3. Adicionar useEffect para carregar preferência + cleanup TTS**
- [ ] **4. Modificar função `speak()` com verificação**
- [ ] **5. Alterar botão TTS no header para toggle**
- [ ] **6. Desabilitar outros botões TTS quando inativo**
- [ ] **7. Adicionar TTS automático no início do jogo**
- [ ] **8. Adicionar estilos CSS necessários**
- [ ] **9. Testar funcionamento completo**
- [ ] **10. Verificar persistência no localStorage**
- [ ] **11. Verificar cleanup do TTS ao sair do jogo**

## 🎮 Jogos para Padronizar

### 🔲 Pendentes:
- [ ] **MusicalSequence** (`src/games/MusicalSequence/`)
- [ ] **MemoryGame** (`src/games/MemoryGame/`)
- [ ] **LetterRecognition** (`src/games/LetterRecognition/`)
- [ ] **ContagemNumeros** (`src/games/ContagemNumeros/`)
- [ ] **ColorMatch** (`src/games/ColorMatch/`)
- [ ] **CreativePainting** (`src/games/CreativePainting/`)
- [ ] **QuebraCabeca** (`src/games/QuebraCabeca/`)

### ✅ Concluído:
- [x] **PadroesVisuais** (`src/games/PadroesVisuais/`) - ✅ Referência implementada + Correções
- [x] **ImageAssociation** (`src/games/ImageAssociation/`) - ✅ Padronização completa + Correções

## 🔍 Pontos de Atenção

### 1. **Nomes de localStorage**
- Cada jogo deve ter sua própria chave no localStorage
- Formato: `[NOME_DO_JOGO]TTS` (ex: `musicalSequenceTTS`, `memoryGameTTS`)

### 2. **Estados existentes**
- Verificar se os jogos já têm `isSpeaking` e `currentSpeechButton`
- Se não tiverem, adicionar também

### 3. **Posicionamento do botão**
- Manter sempre no canto superior direito do header
- Usar a classe `headerTtsButton` existente como base

### 4. **Funções TTS existentes**
- Identificar todas as funções que usam `speak()`
- Verificar se há funções como `explainGame()`, `announceSequence()`, etc.

### 5. **Botões TTS existentes**
- Listar todos os botões que fazem TTS
- Aplicar a propriedade `disabled={!ttsActive || ...}` em todos

## 🎯 Benefícios

### Para Crianças Neurodivergentes:
- ✅ **Controle total** sobre o áudio
- ✅ **Redução de sobrecarga sensorial**
- ✅ **Experiência personalizada**
- ✅ **Preferências mantidas** entre sessões

### Para Desenvolvedores:
- ✅ **Código padronizado** em todos os jogos
- ✅ **Manutenção facilitada**
- ✅ **Comportamento consistente**
- ✅ **Acessibilidade aprimorada**

## 📁 Arquivos de Referência

### Principal:
- `src/games/PadroesVisuais/PadroesVisuaisGame.jsx` - Implementação JavaScript
- `src/games/PadroesVisuais/PadroesVisuais.module.css` - Estilos CSS

### Seções importantes:
- **Linhas 227-228**: Estado `ttsActive`
- **Linhas 24-30**: Verificação na função `speak()`
- **Linhas 132-146**: Função `toggleTTS()`
- **Linhas 148-153**: useEffect para carregar preferência
- **Linhas 906-912**: Botão toggle no header
- **Linhas 941-945**: Indicador de status

## 🚀 Próximos Passos

1. **Implementar no MusicalSequence** (mais similar ao PadroesVisuais)
2. **Testar funcionamento completo**
3. **Aplicar nos demais jogos em ordem de prioridade**
4. **Documentar peculiaridades de cada jogo**
5. **Criar testes automatizados**

---

## 📋 Status de Implementação

### ✅ Jogos Padronizados
- **Padrões Visuais** - ✅ Completo
- **Associação de Imagens** - ✅ Completo  
- **Correspondência de Cores (ColorMatch)** - ✅ Completo
- **Sequência Musical (MusicalSequence)** - ✅ Completo
- **Jogo da Memória (MemoryGame)** - ✅ Completo
- **Contagem de Números (ContagemNumeros)** - ✅ Completo
- **Reconhecimento de Letras (LetterRecognition)** - ✅ Completo

### 🔄 Jogos Pendentes
- Demais jogos do portal (a serem identificados e padronizados conforme necessário)

### 🎯 Resumo das Implementações
Todos os jogos padronizados seguem o mesmo padrão:
- Toggle TTS no header (🔊/🔇)
- Persistência via localStorage
- TTS automático ao iniciar o jogo
- Cleanup automático ao sair
- Feedbacks sonoros em eventos importantes
- Interface limpa sem indicadores visuais desnecessários

---

**📌 Importante**: Este documento deve ser atualizado conforme encontramos particularidades em cada jogo durante a implementação.
