export const ImageAssociationConfig = {
  // Associações lógicas expandidas por categorias e dificuldades
  associations: [
    {
      phase: 1,
      difficulty: 'EASY',
      category: 'animais-básicos',
      main: { emoji: '🐶', label: '<PERSON><PERSON><PERSON><PERSON>' },
      correct: { emoji: '🦴', label: 'Osso' },
      explanation: 'Cachorros adoram roer ossos!',
      options: [
        { emoji: '🦴', label: 'Osso' },
        { emoji: '🐱', label: 'Gato' },
        { emoji: '🌸', label: 'Flor' },
        { emoji: '🚗', label: 'Carro' }
      ]
    },
    {
      phase: 2,
      difficulty: 'EASY',
      category: 'natureza-básica',
      main: { emoji: '🐟', label: 'Peixe' },
      correct: { emoji: '💧', label: 'Água' },
      explanation: 'Peixes vivem na água!',
      options: [
        { emoji: '🔥', label: 'Fogo' },
        { emoji: '💧', label: 'Água' },
        { emoji: '🍕', label: 'Pizza' },
        { emoji: '✈️', label: 'Avião' }
      ]
    },
    {
      phase: 3,
      difficulty: 'EASY',
      category: 'alimentos-origem',
      main: { emoji: '🥛', label: 'Leite' },
      correct: { emoji: '🐄', label: 'Vaca' },
      explanation: 'O leite vem da vaca!',
      options: [
        { emoji: '🐄', label: 'Vaca' },
        { emoji: '🐧', label: 'Pinguim' },
        { emoji: '🎈', label: 'Balão' },
        { emoji: '🍎', label: 'Maçã' }
      ]
    },
    {
      phase: 4,
      difficulty: 'MEDIUM',
      category: 'insetos-plantas',
      main: { emoji: '🐝', label: 'Abelha' },
      correct: { emoji: '🌸', label: 'Flor' },
      explanation: 'Abelhas coletam néctar das flores!',
      options: [
        { emoji: '🌸', label: 'Flor' },
        { emoji: '🐟', label: 'Peixe' },
        { emoji: '🏠', label: 'Casa' },
        { emoji: '⚽', label: 'Bola' }
      ]
    },
    {
      phase: 5,
      difficulty: 'MEDIUM',
      category: 'corpo-função',
      main: { emoji: '👁️', label: 'Olho' },
      correct: { emoji: '👓', label: 'Óculos' },
      explanation: 'Óculos ajudam os olhos a enxergar melhor!',
      options: [
        { emoji: '👓', label: 'Óculos' },
        { emoji: '🦷', label: 'Dente' },
        { emoji: '🎧', label: 'Fone' },
        { emoji: '🧠', label: 'Cérebro' }
      ]
    },
    {
      phase: 6,
      difficulty: 'MEDIUM',
      category: 'profissões-ferramentas',
      main: { emoji: '👨‍⚕️', label: 'Médico' },
      correct: { emoji: '🩺', label: 'Estetoscópio' },
      explanation: 'Médicos usam estetoscópio para auscultar!',
      options: [
        { emoji: '🩺', label: 'Estetoscópio' },
        { emoji: '🔨', label: 'Martelo' },
        { emoji: '📚', label: 'Livro' },
        { emoji: '🎨', label: 'Pincel' }
      ]
    },
    {
      phase: 7,
      difficulty: 'MEDIUM',
      category: 'tempo-ação',
      main: { emoji: '🌙', label: 'Noite' },
      correct: { emoji: '😴', label: 'Dormir' },
      explanation: 'À noite é hora de dormir!',
      options: [
        { emoji: '😴', label: 'Dormir' },
        { emoji: '🏃', label: 'Correr' },
        { emoji: '🍽️', label: 'Comer' },
        { emoji: '📖', label: 'Estudar' }
      ]
    },
    {
      phase: 8,
      difficulty: 'HARD',
      category: 'emoções-expressões',
      main: { emoji: '😢', label: 'Tristeza' },
      correct: { emoji: '🤗', label: 'Abraço' },
      explanation: 'Um abraço pode consolar a tristeza!',
      options: [
        { emoji: '🤗', label: 'Abraço' },
        { emoji: '🎉', label: 'Festa' },
        { emoji: '⚽', label: 'Futebol' },
        { emoji: '🍰', label: 'Bolo' }
      ]
    },
    {
      phase: 9,
      difficulty: 'HARD',
      category: 'causas-efeitos',
      main: { emoji: '🌧️', label: 'Chuva' },
      correct: { emoji: '☂️', label: 'Guarda-chuva' },
      explanation: 'Usamos guarda-chuva para nos proteger da chuva!',
      options: [
        { emoji: '☂️', label: 'Guarda-chuva' },
        { emoji: '🕶️', label: 'Óculos de sol' },
        { emoji: '🏖️', label: 'Praia' },
        { emoji: '🔥', label: 'Fogo' }
      ]
    },
    {
      phase: 10,
      difficulty: 'HARD',
      category: 'elementos-opostos',
      main: { emoji: '☀️', label: 'Sol' },
      correct: { emoji: '🌙', label: 'Lua' },
      explanation: 'Sol e Lua são opostos que se complementam!',
      options: [
        { emoji: '🌙', label: 'Lua' },
        { emoji: '⭐', label: 'Estrela' },
        { emoji: '🌈', label: 'Arco-íris' },
        { emoji: '☁️', label: 'Nuvem' }
      ]
    }
  ],

  // Mensagens de encorajamento por dificuldade
  encouragementMessages: {
    EASY: [
      'Muito bem! Você está aprendendo! 🌟',
      'Excelente! Continue assim! 👏',
      'Perfeito! Você entendeu! ✨'
    ],
    MEDIUM: [
      'Ótimo raciocínio! Você está evoluindo! 🧠',
      'Incrível! Seu pensamento está se desenvolvendo! 🚀',
      'Fantástico! Você está pensando como um especialista! 💪'
    ],
    HARD: [
      'Extraordinário! Pensamento avançado! 🎓',
      'Brilhante! Você dominou conceitos complexos! 🌟',
      'Excepcional! Raciocínio de alto nível! 🏆'
    ]
  },

  // Configurações de dificuldade
  difficulties: [
    { id: 'EASY', name: 'Fácil', phases: [1, 2, 3] },
    { id: 'MEDIUM', name: 'Médio', phases: [4, 5, 6, 7] },
    { id: 'HARD', name: 'Difícil', phases: [8, 9, 10] }
  ],

  // Configurações do jogo
  gameSettings: {
    pointsByDifficulty: {
      EASY: 10,
      MEDIUM: 15,
      HARD: 20
    },
    successFeedbackDuration: 1000,
    errorFeedbackDuration: 2000,
    explanationDelay: 100
  },

  // Categorias de associação
  associationCategories: {
    'animais-básicos': 'Animais e suas características básicas',
    'natureza-básica': 'Elementos naturais e ambientes',
    'alimentos-origem': 'Alimentos e suas origens',
    'insetos-plantas': 'Relações entre insetos e plantas',
    'corpo-função': 'Partes do corpo e funções',
    'profissões-ferramentas': 'Profissões e suas ferramentas',
    'tempo-ação': 'Períodos do dia e ações relacionadas',
    'emoções-expressões': 'Emoções e formas de expressá-las',
    'causas-efeitos': 'Relações de causa e efeito',
    'elementos-opostos': 'Elementos opostos e complementares'
  },

  // Informações do jogo
  gameInfo: {
    title: 'Associação de Imagens',
    description: 'Conecte ideias e descubra relações',
    icon: '🔗',
    category: 'logic',
    ageRange: '4-10',
    skills: ['raciocínio lógico', 'associação de ideias', 'pensamento crítico', 'conexões conceituais']
  }
}
