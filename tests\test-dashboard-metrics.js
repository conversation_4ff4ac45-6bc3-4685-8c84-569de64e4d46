/**
 * @file test-dashboard-metrics.js
 * @description Script para gerar métricas de teste e alimentar as dashboards
 */

import { getSystemOrchestrator } from './src/api/services/core/SystemOrchestrator.js';
import { logger } from './src/api/services/core/logging/StructuredLogger.js';

// Configurações do teste
const TEST_CONFIG = {
  childrenCount: 3,
  sessionsPerChild: 10,
  gamesPerSession: 2,
  intervalBetweenSessions: 1000, // 1 segundo
  sessionDuration: 30000, // 30 segundos
};

// Dados de crianças fictícias
const TEST_CHILDREN = [
  {
    id: 'child-001',
    name: '<PERSON> <PERSON>',
    age: 6,
    conditions: ['autism', 'attention_deficit'],
    preferences: ['ColorMatch', 'MemoryGame', 'CreativePainting']
  },
  {
    id: 'child-002', 
    name: '<PERSON>',
    age: 8,
    conditions: ['cognitive_delay'],
    preferences: ['MusicalSequence', 'PadroesVisuais', 'QuebraCabe<PERSON>']
  },
  {
    id: 'child-003',
    name: '<PERSON>',
    age: 5,
    conditions: ['motor_skills'],
    preferences: ['ImageAssociation', 'ContagemNumeros', 'LetterRecognition']
  }
];

// Jogos disponíveis com configurações específicas
const AVAILABLE_GAMES = [
  {
    id: 'ColorMatch',
    name: 'Combinação de Cores',
    type: 'cognitive',
    difficulty: 'easy',
    avgDuration: 180000, // 3 minutos
    successRate: 0.75
  },
  {
    id: 'MemoryGame',
    name: 'Jogo da Memória',
    type: 'cognitive',
    difficulty: 'medium',
    avgDuration: 240000, // 4 minutos
    successRate: 0.65
  },
  {
    id: 'CreativePainting',
    name: 'Pintura Criativa',
    type: 'creative',
    difficulty: 'easy',
    avgDuration: 300000, // 5 minutos
    successRate: 0.85
  },
  {
    id: 'MusicalSequence',
    name: 'Sequência Musical',
    type: 'auditory',
    difficulty: 'medium',
    avgDuration: 200000, // 3.3 minutos
    successRate: 0.70
  },
  {
    id: 'PadroesVisuais',
    name: 'Padrões Visuais',
    type: 'cognitive',
    difficulty: 'hard',
    avgDuration: 350000, // 5.8 minutos
    successRate: 0.55
  },
  {
    id: 'QuebraCabeca',
    name: 'Quebra-Cabeça',
    type: 'spatial',
    difficulty: 'hard',
    avgDuration: 400000, // 6.6 minutos
    successRate: 0.60
  },
  {
    id: 'ImageAssociation',
    name: 'Associação de Imagens',
    type: 'cognitive',
    difficulty: 'medium',
    avgDuration: 220000, // 3.6 minutos
    successRate: 0.80
  },
  {
    id: 'ContagemNumeros',
    name: 'Contagem de Números',
    type: 'mathematical',
    difficulty: 'easy',
    avgDuration: 150000, // 2.5 minutos
    successRate: 0.90
  },
  {
    id: 'LetterRecognition',
    name: 'Reconhecimento de Letras',
    type: 'language',
    difficulty: 'easy',
    avgDuration: 180000, // 3 minutos
    successRate: 0.85
  }
];

/**
 * Gera métricas realistas para uma sessão de jogo
 */
function generateGameMetrics(child, game, sessionNumber) {
  // Simular progresso ao longo das sessões
  const progressFactor = Math.min(1 + (sessionNumber * 0.1), 1.5);
  
  // Fatores de dificuldade baseados na condição da criança
  let difficultyAdjustment = 1;
  if (child.conditions.includes('autism')) {
    difficultyAdjustment *= (game.type === 'social' ? 0.7 : 1.1);
  }
  if (child.conditions.includes('attention_deficit')) {
    difficultyAdjustment *= (game.avgDuration > 240000 ? 0.8 : 1.0);
  }
  if (child.conditions.includes('cognitive_delay')) {
    difficultyAdjustment *= (game.difficulty === 'hard' ? 0.6 : 0.9);
  }
  if (child.conditions.includes('motor_skills')) {
    difficultyAdjustment *= (game.type === 'spatial' ? 0.7 : 1.0);
  }

  // Calcular métricas base
  const baseAccuracy = game.successRate * difficultyAdjustment * progressFactor;
  const accuracy = Math.max(0.1, Math.min(1.0, baseAccuracy + (Math.random() - 0.5) * 0.2));
  
  const score = Math.round(accuracy * 100);
  const responseTime = Math.round(1000 + (Math.random() * 2000) + (1 - accuracy) * 1000);
  const duration = Math.round(game.avgDuration * (0.8 + Math.random() * 0.4));
  
  // Métricas de engajamento
  const engagement = Math.max(0.1, Math.min(1.0, accuracy * 0.8 + 0.2 + (Math.random() - 0.5) * 0.1));
  const attention = Math.max(0.1, Math.min(1.0, engagement * 0.9 + (Math.random() - 0.5) * 0.1));
  
  // Detecção de padrões de erro
  const errorPatterns = [];
  if (accuracy < 0.5) {
    errorPatterns.push('low_accuracy');
  }
  if (responseTime > 3000) {
    errorPatterns.push('slow_response');
  }
  if (engagement < 0.5) {
    errorPatterns.push('low_engagement');
  }

  return {
    childId: child.id,
    childName: child.name,
    sessionId: `session-${child.id}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
    gameId: game.id,
    gameName: game.name,
    gameType: game.type,
    timestamp: new Date().toISOString(),
    startTime: new Date(Date.now() - duration).toISOString(),
    endTime: new Date().toISOString(),
    duration: duration,
    score: score,
    accuracy: Math.round(accuracy * 100) / 100,
    responseTime: responseTime,
    engagement: Math.round(engagement * 100) / 100,
    attention: Math.round(attention * 100) / 100,
    difficulty: game.difficulty,
    errorPatterns: errorPatterns,
    interactions: Math.round(duration / 5000), // Interação a cada 5 segundos
    completionRate: accuracy > 0.7 ? 1 : (accuracy > 0.4 ? 0.5 : 0),
    childAge: child.age,
    childConditions: child.conditions,
    sessionNumber: sessionNumber,
    
    // Métricas específicas por tipo de jogo
    gameSpecificMetrics: generateGameSpecificMetrics(game, accuracy, child)
  };
}

/**
 * Gera métricas específicas do jogo
 */
function generateGameSpecificMetrics(game, accuracy, child) {
  const metrics = {};
  
  switch (game.id) {
    case 'ColorMatch':
      metrics.colorsMatched = Math.round(accuracy * 20);
      metrics.incorrectMatches = Math.round((1 - accuracy) * 10);
      metrics.colorAccuracy = accuracy;
      break;
      
    case 'MemoryGame':
      metrics.pairsFound = Math.round(accuracy * 12);
      metrics.memorySpan = Math.round(3 + accuracy * 4);
      metrics.visualMemoryScore = accuracy;
      break;
      
    case 'CreativePainting':
      metrics.brushStrokes = Math.round(50 + Math.random() * 100);
      metrics.colorsUsed = Math.round(3 + Math.random() * 5);
      metrics.creativityScore = Math.random() * 0.4 + 0.6;
      break;
      
    case 'MusicalSequence':
      metrics.notesPlayed = Math.round(accuracy * 15);
      metrics.rhythmAccuracy = accuracy;
      metrics.auditoryProcessing = Math.round(accuracy * 100) / 100;
      break;
      
    case 'PadroesVisuais':
      metrics.patternsCompleted = Math.round(accuracy * 8);
      metrics.spatialReasoning = accuracy;
      metrics.logicalThinking = Math.round(accuracy * 100) / 100;
      break;
      
    case 'QuebraCabeca':
      metrics.piecesPlaced = Math.round(accuracy * 25);
      metrics.spatialOrientation = accuracy;
      metrics.problemSolving = Math.round(accuracy * 100) / 100;
      break;
      
    case 'ImageAssociation':
      metrics.associationsMade = Math.round(accuracy * 10);
      metrics.conceptualThinking = accuracy;
      metrics.categoryRecognition = Math.round(accuracy * 100) / 100;
      break;
      
    case 'ContagemNumeros':
      metrics.numbersCountted = Math.round(accuracy * 20);
      metrics.numericalSkills = accuracy;
      metrics.mathematicalReasoning = Math.round(accuracy * 100) / 100;
      break;
      
    case 'LetterRecognition':
      metrics.lettersRecognized = Math.round(accuracy * 15);
      metrics.languageSkills = accuracy;
      metrics.readingReadiness = Math.round(accuracy * 100) / 100;
      break;
  }
  
  return metrics;
}

/**
 * Função principal para gerar dados de teste
 */
async function generateTestMetrics() {
  console.log('🎯 INICIANDO GERAÇÃO DE MÉTRICAS PARA DASHBOARD');
  console.log('=' .repeat(60));
  
  try {
    // Inicializar o SystemOrchestrator
    console.log('\n1. Inicializando SystemOrchestrator...');
    const systemOrchestrator = getSystemOrchestrator();
    
    // Aguardar inicialização
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('2. Verificando estado do sistema...');
    const healthStatus = systemOrchestrator.getSystemHealth();
    console.log(`Estado do sistema: ${healthStatus.status}`);
    
    console.log('\n3. Iniciando geração de métricas de teste...');
    console.log(`Configuração: ${TEST_CONFIG.childrenCount} crianças, ${TEST_CONFIG.sessionsPerChild} sessões cada`);
    
    let totalMetricsGenerated = 0;
    const startTime = Date.now();
    
    // Para cada criança
    for (let childIndex = 0; childIndex < TEST_CHILDREN.length; childIndex++) {
      const child = TEST_CHILDREN[childIndex];
      console.log(`\n👶 Processando criança: ${child.name} (${child.age} anos)`);
      
      // Gerar sessões para essa criança
      for (let sessionIndex = 1; sessionIndex <= TEST_CONFIG.sessionsPerChild; sessionIndex++) {
        console.log(`  📊 Sessão ${sessionIndex}/${TEST_CONFIG.sessionsPerChild}`);
        
        // Selecionar jogos para esta sessão (preferências da criança + alguns aleatórios)
        const sessionGames = [];
        
        // Adicionar jogos preferidos
        const preferredGames = AVAILABLE_GAMES.filter(g => child.preferences.includes(g.id));
        if (preferredGames.length > 0) {
          sessionGames.push(preferredGames[Math.floor(Math.random() * preferredGames.length)]);
        }
        
        // Adicionar jogo aleatório se necessário
        if (sessionGames.length < TEST_CONFIG.gamesPerSession) {
          const remainingGames = AVAILABLE_GAMES.filter(g => !sessionGames.find(sg => sg.id === g.id));
          const randomGame = remainingGames[Math.floor(Math.random() * remainingGames.length)];
          if (randomGame) {
            sessionGames.push(randomGame);
          }
        }
        
        // Gerar métricas para cada jogo da sessão
        for (const game of sessionGames) {
          const metrics = generateGameMetrics(child, game, sessionIndex);
          
          try {
            // Processar métricas através do SystemOrchestrator
            await systemOrchestrator.processGameMetrics(metrics);
            totalMetricsGenerated++;
            
            console.log(`    🎮 ${game.name}: Score ${metrics.score}, Accuracy ${(metrics.accuracy * 100).toFixed(1)}%`);
          } catch (error) {
            console.error(`    ❌ Erro ao processar ${game.name}:`, error.message);
          }
          
          // Pequena pausa entre jogos
          await new Promise(resolve => setTimeout(resolve, 200));
        }
        
        // Pausa entre sessões
        await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.intervalBetweenSessions));
      }
    }
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log('\n' + '=' .repeat(60));
    console.log('📈 RESUMO DA GERAÇÃO DE MÉTRICAS');
    console.log('=' .repeat(60));
    console.log(`✅ Total de métricas geradas: ${totalMetricsGenerated}`);
    console.log(`⏱️  Tempo total: ${duration.toFixed(2)} segundos`);
    console.log(`🎯 Métricas por segundo: ${(totalMetricsGenerated / duration).toFixed(2)}`);
    
    console.log('\n📊 Estatísticas por criança:');
    TEST_CHILDREN.forEach((child, index) => {
      const metricsPerChild = TEST_CONFIG.sessionsPerChild * TEST_CONFIG.gamesPerSession;
      console.log(`👶 ${child.name}: ${metricsPerChild} métricas esperadas`);
    });
    
    console.log('\n🎮 Jogos utilizados:');
    AVAILABLE_GAMES.forEach(game => {
      console.log(`  ${game.name} (${game.type}, ${game.difficulty})`);
    });
    
    console.log('\n✅ Geração de métricas concluída!');
    console.log('🌐 As métricas estão disponíveis nas dashboards em:');
    console.log('   http://localhost:5173/dashboard');
    console.log('   http://localhost:5173/progress');
    console.log('   http://localhost:5173/analytics');
    
  } catch (error) {
    console.error('❌ Erro durante a geração de métricas:', error);
    throw error;
  }
}

// Executar geração de métricas
if (import.meta.url === `file://${process.argv[1]}`) {
  generateTestMetrics()
    .then(() => {
      console.log('\n🎉 Processo concluído com sucesso!');
    })
    .catch(error => {
      console.error('\n💥 Processo falhou:', error);
      process.exit(1);
    });
}

export { generateTestMetrics, TEST_CHILDREN, AVAILABLE_GAMES };
