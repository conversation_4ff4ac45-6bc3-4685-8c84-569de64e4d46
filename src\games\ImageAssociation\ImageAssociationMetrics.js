import { CognitiveAssociationEngine } from '../../api/algorithms/CognitiveAssociationEngine.js'

export const ImageAssociationMetrics = {
  cognitiveEngine: new CognitiveAssociationEngine(),
  sessionAttempts: [],

  // Registrar início do jogo
  startGame: (difficulty) => {
    const metrics = {
      gameId: 'image-association',
      sessionId: `association_${Date.now()}`,
      startTime: new Date().toISOString(),
      difficulty,
      userId: 'anonymous', // Será substituído por ID real
      device: navigator.userAgent
    }

    // Resetar tentativas da sessão
    this.sessionAttempts = []
    console.log('Image Association Game Started:', metrics)
    return metrics
  },
  // Registrar tentativa de associação
  recordAssociationAttempt: (attempt) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      phase: attempt.phase,
      difficulty: attempt.difficulty,
      category: attempt.category,
      mainItem: attempt.mainItem,
      correctAnswer: attempt.correctAnswer,
      userAnswer: attempt.userAnswer,
      isCorrect: attempt.isCorrect,
      responseTime: attempt.responseTime,
      attempts: attempt.attempts, // tentativas nesta fase
      explanation: attempt.explanation,
      targetColor: attempt.targetColor || 'unknown'
    }

    // Adicionar à lista de tentativas da sessão
    this.sessionAttempts.push(metrics)

    // ATIVAÇÃO: Análise cognitiva em tempo real
    if (this.sessionAttempts.length >= 5) {
      const cognitiveAnalysis = this.analyzeCognitivePatterns()
      console.log('🧠 Análise Cognitiva:', cognitiveAnalysis)
    }

    console.log('Image Association Attempt:', metrics)
    return metrics
  },

  // Registrar visualização de explicação
  recordExplanationView: (explanationData) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'explanation_viewed',
      phase: explanationData.phase,
      category: explanationData.category,
      explanation: explanationData.explanation,
      timeSpent: explanationData.timeSpent
    }

    console.log('Image Association Explanation Viewed:', metrics)
    return metrics
  },

  // Registrar progressão de fase
  recordPhaseProgression: (progressData) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'phase_completed',
      completedPhase: progressData.completedPhase,
      nextPhase: progressData.nextPhase,
      difficulty: progressData.difficulty,
      attemptsInPhase: progressData.attemptsInPhase,
      successInPhase: progressData.successInPhase
    }

    console.log('Image Association Phase Progression:', metrics)
    return metrics
  },
  // Registrar final do jogo
  endGame: (gameData) => {
    const metrics = {
      sessionId: gameData.sessionId,
      endTime: new Date().toISOString(),
      totalTime: gameData.totalTime,
      totalPhases: gameData.totalPhases,
      completedPhases: gameData.completedPhases,
      totalScore: gameData.totalScore,
      accuracy: gameData.accuracy,
      averageResponseTime: gameData.averageResponseTime,
      difficulty: gameData.difficulty,
      finalLevel: gameData.finalLevel,
      starsEarned: gameData.starsEarned,
      categoriesExplored: gameData.categoriesExplored,
      completed: gameData.completed
    }

    // ATIVAÇÃO: Análise final completa
    const finalAnalysis = this.analyzeCognitivePatterns()
    if (finalAnalysis) {
      console.log('🎯 ANÁLISE FINAL COGNITIVA:', finalAnalysis)
      metrics.cognitiveAnalysis = finalAnalysis
    }

    console.log('Image Association Game Ended:', metrics)
    return metrics
  },

  // Calcular estatísticas do jogo
  calculateStats: (attempts) => {
    if (!attempts || attempts.length === 0) {
      return {
        accuracy: 0,
        averageTime: 0,
        totalAttempts: 0,
        correctAssociations: 0
      }
    }

    const correctAttempts = attempts.filter(attempt => attempt.isCorrect)
    const totalTime = attempts.reduce((sum, attempt) => sum + attempt.responseTime, 0)

    return {
      accuracy: (correctAttempts.length / attempts.length) * 100,
      averageTime: totalTime / attempts.length,
      totalAttempts: attempts.length,
      correctAssociations: correctAttempts.length,
      phasesCompleted: [...new Set(attempts.map(a => a.phase))].length,
      categoriesMastered: calculateCategoriesMastered(attempts),
      difficultyProgression: calculateDifficultyProgression(attempts),
      conceptualGrowth: calculateConceptualGrowth(attempts)
    }
  },

  // Analisar padrões de associação
  analyzeAssociationPatterns: (attempts) => {
    const errorsByCategory = attempts
      .filter(attempt => !attempt.isCorrect)
      .reduce((acc, error) => {
        acc[error.category] = (acc[error.category] || 0) + 1
        return acc
      }, {})

    const errorsByDifficulty = attempts
      .filter(attempt => !attempt.isCorrect)
      .reduce((acc, error) => {
        acc[error.difficulty] = (acc[error.difficulty] || 0) + 1
        return acc
      }, {})

    const categoryAccuracy = {}
    const categoriesAttempted = [...new Set(attempts.map(a => a.category))]

    categoriesAttempted.forEach(category => {
      const categoryAttempts = attempts.filter(a => a.category === category)
      const categoryCorrect = categoryAttempts.filter(a => a.isCorrect).length
      categoryAccuracy[category] = (categoryCorrect / categoryAttempts.length) * 100
    })

    return {
      errorsByCategory,
      errorsByDifficulty,
      categoryAccuracy,
      strongestCategories: getStrongestCategories(categoryAccuracy),
      weakestCategories: getWeakestCategories(categoryAccuracy),
      suggestions: generateAssociationSuggestions(attempts, errorsByCategory, categoryAccuracy)
    }
  },

  // ATIVAÇÃO: Análise cognitiva profunda
  analyzeCognitivePatterns: () => {
    if (this.sessionAttempts.length === 0) return null

    const gameMetrics = {
      attempts: this.sessionAttempts.map(attempt => ({
        targetColor: attempt.targetColor,
        isCorrect: attempt.isCorrect,
        responseTime: attempt.responseTime,
        timestamp: new Date(attempt.timestamp).getTime(),
        pattern: attempt.category
      }))
    }

    try {
      const colorAnalysis = this.cognitiveEngine.analyzeColorAccuracyPattern(gameMetrics)
      const neuropsychProfile = this.cognitiveEngine.analyzeNeuropsychologicalPattern(gameMetrics)
      const fatiguePattern = this.cognitiveEngine.analyzeFatigueAlgorithm(gameMetrics)
      const anomalies = this.cognitiveEngine.detectAnomalousPatterns(gameMetrics)

      return {
        timestamp: new Date().toISOString(),
        cognitiveProfile: colorAnalysis.cognitiveProfile,
        neuralProfile: neuropsychProfile,
        fatigueAnalysis: fatiguePattern,
        anomalies,
        insights: colorAnalysis.insights,
        totalAttempts: this.sessionAttempts.length
      }
    } catch (error) {
      console.error('❌ Erro na análise cognitiva:', error)
      return null
    }
  }
}

// Funções auxiliares para análise
function calculateCategoriesMastered (attempts) {
  const categoryStats = {}

  attempts.forEach(attempt => {
    if (!categoryStats[attempt.category]) {
      categoryStats[attempt.category] = { correct: 0, total: 0 }
    }

    categoryStats[attempt.category].total++
    if (attempt.isCorrect) {
      categoryStats[attempt.category].correct++
    }
  })

  const masteredCategories = Object.entries(categoryStats)
    .filter(([category, stats]) => {
      const accuracy = stats.correct / stats.total
      return accuracy >= 0.8 && stats.total >= 2 // 80% de precisão com pelo menos 2 tentativas
    })
    .map(([category]) => category)

  return {
    total: Object.keys(categoryStats).length,
    mastered: masteredCategories.length,
    masteredList: masteredCategories
  }
}

function calculateDifficultyProgression (attempts) {
  const difficulties = attempts.map(a => a.difficulty)
  return {
    EASY: difficulties.filter(d => d === 'EASY').length,
    MEDIUM: difficulties.filter(d => d === 'MEDIUM').length,
    HARD: difficulties.filter(d => d === 'HARD').length
  }
}

function calculateConceptualGrowth (attempts) {
  if (attempts.length < 6) return 0

  // Dividir em terços para avaliar crescimento
  const firstThird = attempts.slice(0, Math.floor(attempts.length / 3))
  const lastThird = attempts.slice(-Math.floor(attempts.length / 3))

  const firstAccuracy = firstThird.filter(a => a.isCorrect).length / firstThird.length
  const lastAccuracy = lastThird.filter(a => a.isCorrect).length / lastThird.length

  return ((lastAccuracy - firstAccuracy) / firstAccuracy) * 100
}

function getStrongestCategories (categoryAccuracy) {
  return Object.entries(categoryAccuracy)
    .filter(([, accuracy]) => accuracy >= 80)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 3)
    .map(([category, accuracy]) => ({ category, accuracy }))
}

function getWeakestCategories (categoryAccuracy) {
  return Object.entries(categoryAccuracy)
    .filter(([, accuracy]) => accuracy < 60)
    .sort(([, a], [, b]) => a - b)
    .slice(0, 3)
    .map(([category, accuracy]) => ({ category, accuracy }))
}

function generateAssociationSuggestions (attempts, errorsByCategory, categoryAccuracy) {
  const suggestions = []

  if (attempts.length === 0) {
    suggestions.push('Comece jogando para receber sugestões personalizadas!')
    return suggestions
  }

  const accuracy = attempts.filter(a => a.isCorrect).length / attempts.length

  // Sugestões baseadas na precisão geral
  if (accuracy < 0.5) {
    suggestions.push('Comece com associações básicas e pense na função ou relação direta')
    suggestions.push('Observe bem as características de cada item antes de escolher')
  } else if (accuracy < 0.7) {
    suggestions.push('Você está progredindo! Tente pensar em como os itens se relacionam')
    suggestions.push('Considere diferentes tipos de relação: função, origem, ambiente, uso')
  } else if (accuracy > 0.9) {
    suggestions.push('Excelente raciocínio! Tente níveis mais difíceis para novos desafios')
    suggestions.push('Você domina as associações! Explore categorias mais complexas')
  }

  // Sugestões baseadas em categorias problemáticas
  const problemCategories = Object.entries(errorsByCategory)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 2)
    .map(([category]) => category)

  if (problemCategories.length > 0) {
    suggestions.push(`Foque mais atenção nas categorias: ${problemCategories.join(', ')}`)
  }

  // Sugestões baseadas em pontos fortes
  const strongCategories = getStrongestCategories(categoryAccuracy)
  if (strongCategories.length > 0) {
    suggestions.push(`Você se destaca em: ${strongCategories.map(c => c.category).join(', ')}`)
  }

  return suggestions.length > 0 ? suggestions : ['Continue praticando para desenvolver seu raciocínio lógico!']
}
