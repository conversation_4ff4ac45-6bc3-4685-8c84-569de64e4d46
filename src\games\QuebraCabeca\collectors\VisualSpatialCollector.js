/**
 * 👁️ COLETOR VISUAL-ESPACIAL
 * Analisa capacidades visuais e espaciais específicas
 */

export class VisualSpatialCollector {
  constructor() {
    this.collectorId = 'visual-spatial-collector';
    this.version = '3.0.0';
    this.initialized = true;
  }
  
  async collect(gameData) {
    try {
      const metrics = gameData.metrics || {};
      const interactions = gameData.interactions || [];
      
      const visualSpatialScore = this.calculateVisualSpatialScore(metrics);
      const patternRecognition = this.assessPatternRecognition(metrics);
      const spatialOrientation = this.assessSpatialOrientation(interactions);
      
      return {
        score: visualSpatialScore,
        patternRecognition,
        spatialOrientation,
        insights: ['Habilidades visual-espaciais avaliadas'],
        recommendations: visualSpatialScore < 0.6 
          ? ['Exercícios de visualização espacial']
          : ['Quebra-cabeças 3D avançados']
      };
    } catch (error) {
      console.error('👁️ Erro no VisualSpatialCollector:', error);
      return { score: 0.5, error: error.message };
    }
  }
  
  calculateVisualSpatialScore(metrics) {
    const visualAccuracy = metrics.visualAccuracy || 0.5;
    const spatialAccuracy = metrics.spatialAccuracy || 0.5;
    const processingSpeed = this.calculateProcessingSpeed(metrics);
    return (visualAccuracy + spatialAccuracy + processingSpeed) / 3;
  }
  
  calculateProcessingSpeed(metrics) {
    const averageTime = metrics.averageResponseTime || 3000;
    return Math.max(0, Math.min(1, (5000 - averageTime) / 5000));
  }
  
  assessPatternRecognition(metrics) {
    return metrics.patternRecognitionScore || 0.5;
  }
  
  assessSpatialOrientation(interactions) {
    if (interactions.length === 0) return 0.5;
    
    const correctOrientations = interactions.filter(i => i.correctOrientation).length;
    return correctOrientations / interactions.length;
  }
}
