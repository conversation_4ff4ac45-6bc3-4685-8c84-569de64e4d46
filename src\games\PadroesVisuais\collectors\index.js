/**
 * 🎯 HUB INTEGRADOR DOS COLETORES - PADRÕES VISUAIS V3
 * Orquestrador central dos coletores especializados para PadroesVisuais
 * Portal Betina V3
 */

import { PatternRecognitionCollector } from './PatternRecognitionCollector.js';
import { VisualMemoryCollector } from './VisualMemoryCollector.js';
import { SpatialProcessingCollector } from './SpatialProcessingCollector.js';
import { SequentialReasoningCollector } from './SequentialReasoningCollector.js';
import { VisualSequenceCollector } from './VisualSequenceCollector.js';
import { SpatialPatternCollector } from './SpatialPatternCollector.js';
import { ColorPatternCollector } from './ColorPatternCollector.js';
import { GeometricPatternCollector } from './GeometricPatternCollector.js';
import { TemporalPatternCollector } from './TemporalPatternCollector.js';
import { ErrorPatternCollector } from './ErrorPatternCollector.js';

// Coletores V3 especializados para as 6 atividades
import { SequenceReproductionCollector } from './SequenceReproductionCollector.js';
import { PatternCompletionCollector } from './PatternCompletionCollector.js';
import { PatternConstructionCollector } from './PatternConstructionCollector.js';
import { VisualClassificationCollector } from './VisualClassificationCollector.js';
import { PatternTransformationCollector } from './PatternTransformationCollector.js';
import { AnomalyDetectionCollector } from './AnomalyDetectionCollector.js';

export class PadroesVisuaisCollectorsHub {
  constructor() {
    this.gameType = 'PadroesVisuais';
    this.version = '3.0.0';
    
    // Coletores base (compatibilidade com versões anteriores)
    this.collectors = {
      patternRecognition: new PatternRecognitionCollector(),
      visualMemory: new VisualMemoryCollector(),
      spatialProcessing: new SpatialProcessingCollector(),
      sequentialReasoning: new SequentialReasoningCollector(),
      visualSequence: new VisualSequenceCollector(),
      spatialPattern: new SpatialPatternCollector(),
      colorPattern: new ColorPatternCollector(),
      geometricPattern: new GeometricPatternCollector(),
      temporalPattern: new TemporalPatternCollector(),
      errorPattern: new ErrorPatternCollector()
    };
    
    // Coletores V3 especializados para as 6 atividades
    this.v3Collectors = {
      'reproduction-sequences': new SequenceReproductionCollector(),
      'pattern-completion': new PatternCompletionCollector(),
      'pattern-construction': new PatternConstructionCollector(),
      'visual-classification': new VisualClassificationCollector(),
      'pattern-transformation': new PatternTransformationCollector(),
      'anomaly-detection': new AnomalyDetectionCollector()
    };
    
    this.activeSession = null;
    this.sessionData = {
      sequences: [],
      interactions: [],
      metrics: {},
      startTime: null,
      currentActivity: null,
      activityData: {}
    };
    
    this.analysisConfig = {
      minDataPoints: 3,
      analysisThreshold: 5,
      realTimeAnalysis: true,
      deepAnalysisInterval: 10,
      useV3Collectors: true
    };
  }

  /**
   * Inicializar sessão de coleta
   */
  initializeSession(sessionId, gameConfig = {}) {
    console.log(`🎨 Sessão ${this.gameType} ${sessionId} iniciada`);
    
    this.activeSession = sessionId;
    this.sessionData = {
      sessionId,
      gameType: this.gameType,
      sequences: [],
      interactions: [],
      metrics: {},
      startTime: Date.now(),
      gameConfig,
      analysis: {
        patternRecognition: {},
        visualMemory: {},
        spatialProcessing: {},
        sequentialReasoning: {}
      }
    };
    
    return {
      success: true,
      sessionId,
      collectorsActive: Object.keys(this.collectors).length,
      gameType: this.gameType
    };
  }

  /**
   * Coletar dados de sequência de padrões visuais
   */
  collectSequenceData(sequenceData) {
    if (!this.activeSession) {
      console.warn('Tentativa de coleta sem sessão ativa');
      return false;
    }
    
    const processedSequence = this.processSequenceData(sequenceData);
    this.sessionData.sequences.push(processedSequence);
    
    // Análise em tempo real se configurada
    if (this.analysisConfig.realTimeAnalysis && 
        this.sessionData.sequences.length % this.analysisConfig.analysisThreshold === 0) {
      this.performRealtimeAnalysis();
    }
    
    return true;
  }

  /**
   * Coletar dados de interação específica
   */
  collectInteractionData(interactionData) {
    if (!this.activeSession) {
      console.warn('Tentativa de coleta sem sessão ativa');
      return false;
    }
    
    const processedInteraction = this.processInteractionData(interactionData);
    this.sessionData.interactions.push(processedInteraction);
    
    return true;
  }

  /**
   * Processar dados de sequência
   */
  processSequenceData(sequenceData) {
    const processed = {
      ...sequenceData,
      timestamp: Date.now(),
      sessionId: this.activeSession,
      processingTime: sequenceData.responseTime || 0,
      
      // Dados para análise de padrões
      patternData: {
        targetSequence: sequenceData.targetSequence,
        playerSequence: sequenceData.playerSequence,
        isCorrect: sequenceData.isCorrect,
        difficulty: sequenceData.difficulty,
        sequenceLength: sequenceData.targetSequence?.length || 0,
        showTime: sequenceData.showTime || 5000,
        responseTime: sequenceData.responseTime || 2000
      },
      
      // Dados para análise de memória visual
      memoryData: {
        sequences: [sequenceData],
        interactions: this.sessionData.interactions,
        retentionPeriod: sequenceData.showTime || 5000,
        memoryLoad: sequenceData.targetSequence?.length || 0
      },
      
      // Dados para análise espacial
      spatialData: {
        sequences: [sequenceData],
        interactions: this.sessionData.interactions,
        spatialLayout: sequenceData.spatialLayout,
        visualComplexity: this.calculateVisualComplexity(sequenceData)
      },
      
      // Dados para análise de raciocínio sequencial
      sequentialData: {
        sequences: [sequenceData],
        temporalPattern: this.identifyTemporalPattern(sequenceData),
        logicalStructure: this.analyzeLogicalStructure(sequenceData)
      }
    };
    
    return processed;
  }

  /**
   * Processar dados de interação
   */
  processInteractionData(interactionData) {
    const processed = {
      ...interactionData,
      timestamp: Date.now(),
      sessionId: this.activeSession,
      
      // Enriquecer com dados contextuais
      context: {
        sequencePosition: this.sessionData.sequences.length,
        interactionPosition: this.sessionData.interactions.length,
        sessionProgress: this.calculateSessionProgress()
      },
      
      // Análise visual
      visualProcessing: {
        shapeComplexity: this.getShapeComplexity(interactionData.shapeId),
        processingTime: interactionData.responseTime || 2000,
        visualAccuracy: interactionData.isCorrect
      },
      
      // Análise espacial
      spatialVisualization: {
        position: interactionData.position,
        spatialAccuracy: this.calculateSpatialAccuracy(interactionData),
        spatialComplexity: this.calculateSpatialComplexity(interactionData)
      }
    };
    
    return processed;
  }

  /**
   * Análise em tempo real
   */
  async performRealtimeAnalysis() {
    console.log('🎨 Iniciando análise em tempo real do PadroesVisuais...');
    
    const currentData = this.prepareAnalysisData();
    const quickAnalysis = await this.runQuickAnalysis(currentData);
    
    // Armazenar resultados da análise rápida
    this.sessionData.metrics.realtime = {
      timestamp: Date.now(),
      analysis: quickAnalysis,
      dataPoints: this.sessionData.sequences.length + this.sessionData.interactions.length
    };
    
    console.log('✅ Análise em tempo real finalizada');
    return quickAnalysis;
  }

  /**
   * Análise completa da sessão
   */
  async performCompleteAnalysis() {
    if (!this.activeSession || this.sessionData.sequences.length === 0) {
      console.warn('Dados insuficientes para análise completa');
      return null;
    }
    
    console.log('🎨 Iniciando análise completa do PadroesVisuais...');
    const startTime = Date.now();
    
    const analysisData = this.prepareAnalysisData();
    
    // Executar todos os coletores em paralelo
    const analysisPromises = [
      this.collectors.patternRecognition.analyze(analysisData),
      this.collectors.visualMemory.analyze(analysisData),
      this.collectors.spatialProcessing.analyze(analysisData),
      this.collectors.sequentialReasoning.analyze(analysisData)
    ];
    
    const [
      patternAnalysis,
      memoryAnalysis,
      spatialAnalysis,
      reasoningAnalysis
    ] = await Promise.all(analysisPromises);
    
    // Integrar resultados
    const integratedAnalysis = this.integrateAnalysisResults({
      patternRecognition: patternAnalysis,
      visualMemory: memoryAnalysis,
      spatialProcessing: spatialAnalysis,
      sequentialReasoning: reasoningAnalysis
    });
    
    const analysisTime = Date.now() - startTime;
    console.log(`✅ Análise completa finalizada em ${analysisTime}ms`);
    
    return integratedAnalysis;
  }

  /**
   * Preparar dados para análise
   */
  prepareAnalysisData() {
    return {
      sessionId: this.activeSession,
      gameType: this.gameType,
      
      // Dados específicos para cada coletor
      patternData: {
        sequences: this.sessionData.sequences,
        interactions: this.sessionData.interactions,
        patterns: this.extractPatterns(),
        complexity: this.calculateOverallComplexity()
      },
      
      memoryData: {
        sequences: this.sessionData.sequences,
        interactions: this.sessionData.interactions,
        memoryMetrics: this.calculateMemoryMetrics(),
        retentionData: this.extractRetentionData()
      },
      
      spatialData: {
        sequences: this.sessionData.sequences,
        interactions: this.sessionData.interactions,
        spatialMetrics: this.calculateSpatialMetrics(),
        visualComplexity: this.calculateVisualComplexityMetrics()
      },
      
      sequentialData: {
        sequences: this.sessionData.sequences,
        temporalPatterns: this.extractTemporalPatterns(),
        logicalStructures: this.extractLogicalStructures(),
        reasoningMetrics: this.calculateReasoningMetrics()
      }
    };
  }

  /**
   * Análise rápida para tempo real
   */
  async runQuickAnalysis(data) {
    const quickMetrics = {
      patternRecognition: this.calculateQuickPatternMetrics(data),
      visualMemory: this.calculateQuickMemoryMetrics(data),
      spatialProcessing: this.calculateQuickSpatialMetrics(data),
      sequentialReasoning: this.calculateQuickReasoningMetrics(data)
    };
    
    return quickMetrics;
  }

  /**
   * Integrar resultados de todos os coletores
   */
  integrateAnalysisResults(analyses) {
    const integrated = {
      sessionId: this.activeSession,
      gameType: this.gameType,
      timestamp: Date.now(),
      
      // Análises individuais
      individualAnalyses: analyses,
      
      // Métricas integradas
      integratedMetrics: this.calculateIntegratedMetrics(analyses),
      
      // Insights cruzados
      crossAnalysisInsights: this.generateCrossAnalysisInsights(analyses),
      
      // Recomendações
      recommendations: this.generateRecommendations(analyses),
      
      // Resumo da sessão
      sessionSummary: this.generateSessionSummary(analyses)
    };
    
    return integrated;
  }

  /**
   * Calcular métricas integradas
   */
  calculateIntegratedMetrics(analyses) {
    const integrated = {
      // Capacidade geral de padrões visuais
      overallPatternCapacity: this.calculateOverallPatternCapacity(analyses),
      
      // Eficiência de processamento
      processingEfficiency: this.calculateProcessingEfficiency(analyses),
      
      // Consistência entre domínios
      crossDomainConsistency: this.calculateCrossDomainConsistency(analyses),
      
      // Adaptabilidade
      adaptability: this.calculateAdaptability(analyses),
      
      // Potencial de desenvolvimento
      developmentPotential: this.calculateDevelopmentPotential(analyses)
    };
    
    return integrated;
  }

  /**
   * Gerar insights cruzados
   */
  generateCrossAnalysisInsights(analyses) {
    const insights = [];
    
    // Relacionar reconhecimento de padrões com memória visual
    const patternMemoryCorrelation = this.analyzePatternMemoryCorrelation(
      analyses.patternRecognition,
      analyses.visualMemory
    );
    
    if (patternMemoryCorrelation.strength > 0.7) {
      insights.push('Forte correlação entre reconhecimento de padrões e memória visual');
    }
    
    // Relacionar processamento espacial com raciocínio sequencial
    const spatialReasoningCorrelation = this.analyzeSpatialReasoningCorrelation(
      analyses.spatialProcessing,
      analyses.sequentialReasoning
    );
    
    if (spatialReasoningCorrelation.strength > 0.7) {
      insights.push('Excelente integração entre processamento espacial e raciocínio sequencial');
    }
    
    // Identificar pontos fortes e fracos
    const strengths = this.identifyStrengths(analyses);
    const weaknesses = this.identifyWeaknesses(analyses);
    
    if (strengths.length > 0) {
      insights.push(`Pontos fortes: ${strengths.join(', ')}`);
    }
    
    if (weaknesses.length > 0) {
      insights.push(`Áreas para desenvolvimento: ${weaknesses.join(', ')}`);
    }
    
    return insights;
  }

  /**
   * Gerar recomendações
   */
  generateRecommendations(analyses) {
    const recommendations = [];
    
    // Recomendações baseadas em padrões
    if (analyses.patternRecognition.sequenceRecognition < 0.6) {
      recommendations.push({
        domain: 'pattern_recognition',
        priority: 'high',
        suggestion: 'Exercícios de reconhecimento de sequências simples',
        activities: ['Padrões de repetição', 'Sequências de cores', 'Jogos de memória visual']
      });
    }
    
    // Recomendações baseadas em memória
    if (analyses.visualMemory.shortTermMemory < 0.6) {
      recommendations.push({
        domain: 'visual_memory',
        priority: 'medium',
        suggestion: 'Fortalecimento da memória visual de curto prazo',
        activities: ['Jogos de memória', 'Sequências curtas', 'Exercícios de retenção']
      });
    }
    
    // Recomendações baseadas em processamento espacial
    if (analyses.spatialProcessing.spatialVisualization < 0.6) {
      recommendations.push({
        domain: 'spatial_processing',
        priority: 'medium',
        suggestion: 'Desenvolvimento de habilidades de visualização espacial',
        activities: ['Quebra-cabeças', 'Jogos de rotação', 'Exercícios de orientação']
      });
    }
    
    // Recomendações baseadas em raciocínio
    if (analyses.sequentialReasoning.logicalSequencing < 0.6) {
      recommendations.push({
        domain: 'sequential_reasoning',
        priority: 'high',
        suggestion: 'Fortalecimento do raciocínio lógico sequencial',
        activities: ['Sequências lógicas', 'Padrões numéricos', 'Jogos de estratégia']
      });
    }
    
    return recommendations;
  }

  /**
   * Gerar relatório final
   */
  generateSessionReport() {
    const report = {
      sessionInfo: {
        sessionId: this.activeSession,
        gameType: this.gameType,
        startTime: this.sessionData.startTime,
        endTime: Date.now(),
        duration: Date.now() - this.sessionData.startTime,
        totalSequences: this.sessionData.sequences.length,
        totalInteractions: this.sessionData.interactions.length
      },
      
      dataCollected: {
        sequences: this.sessionData.sequences,
        interactions: this.sessionData.interactions,
        metrics: this.sessionData.metrics
      },
      
      collectorsUsed: Object.keys(this.collectors),
      
      analysisReadiness: {
        sufficientData: this.sessionData.sequences.length >= this.analysisConfig.minDataPoints,
        dataQuality: this.assessDataQuality(),
        recommendAnalysis: this.sessionData.sequences.length >= this.analysisConfig.analysisThreshold
      }
    };
    
    return report;
  }

  // Métodos auxiliares

  calculateVisualComplexity(sequenceData) {
    if (!sequenceData.targetSequence) return 1.0;
    
    const uniqueShapes = new Set(sequenceData.targetSequence).size;
    const sequenceLength = sequenceData.targetSequence.length;
    
    return Math.min(2.0, (uniqueShapes + sequenceLength) / 5);
  }

  identifyTemporalPattern(sequenceData) {
    // Simplificado - identificaria padrões temporais
    return {
      type: 'sequential',
      complexity: sequenceData.targetSequence?.length || 1,
      rhythm: 'regular'
    };
  }

  analyzeLogicalStructure(sequenceData) {
    // Simplificado - analisaria estrutura lógica
    return {
      hasPattern: true,
      patternType: 'sequence',
      logicalComplexity: 1.2
    };
  }

  getShapeComplexity(shapeId) {
    const complexities = {
      circle: 1.0,
      square: 1.1,
      triangle: 1.2,
      diamond: 1.3,
      star: 1.4,
      heart: 1.5
    };
    
    return complexities[shapeId] || 1.0;
  }

  calculateSpatialAccuracy(interactionData) {
    // Simplificado - calcularia precisão espacial
    return interactionData.isCorrect ? 1.0 : 0.0;
  }

  calculateSpatialComplexity(interactionData) {
    return this.getShapeComplexity(interactionData.shapeId || 'circle');
  }

  calculateSessionProgress() {
    const maxSequences = 10; // Exemplo
    return Math.min(1, this.sessionData.sequences.length / maxSequences);
  }

  extractPatterns() {
    return this.sessionData.sequences.map(s => ({
      sequence: s.patternData.targetSequence,
      difficulty: s.patternData.difficulty,
      success: s.patternData.isCorrect
    }));
  }

  calculateOverallComplexity() {
    const complexities = this.sessionData.sequences.map(s => s.spatialData.visualComplexity);
    return complexities.length > 0 
      ? complexities.reduce((sum, c) => sum + c, 0) / complexities.length 
      : 1.0;
  }

  calculateMemoryMetrics() {
    const sequences = this.sessionData.sequences;
    return {
      avgRetentionTime: sequences.reduce((sum, s) => sum + (s.memoryData.retentionPeriod || 5000), 0) / Math.max(1, sequences.length),
      avgMemoryLoad: sequences.reduce((sum, s) => sum + (s.memoryData.memoryLoad || 3), 0) / Math.max(1, sequences.length),
      successRate: sequences.filter(s => s.patternData.isCorrect).length / Math.max(1, sequences.length)
    };
  }

  extractRetentionData() {
    return this.sessionData.sequences.map(s => ({
      retentionPeriod: s.memoryData.retentionPeriod,
      memoryLoad: s.memoryData.memoryLoad,
      success: s.patternData.isCorrect
    }));
  }

  calculateSpatialMetrics() {
    const interactions = this.sessionData.interactions;
    return {
      avgProcessingTime: interactions.reduce((sum, i) => sum + (i.visualProcessing?.processingTime || 2000), 0) / Math.max(1, interactions.length),
      spatialAccuracy: interactions.filter(i => i.visualProcessing?.visualAccuracy).length / Math.max(1, interactions.length),
      avgComplexity: interactions.reduce((sum, i) => sum + (i.spatialVisualization?.spatialComplexity || 1), 0) / Math.max(1, interactions.length)
    };
  }

  calculateVisualComplexityMetrics() {
    const sequences = this.sessionData.sequences;
    return {
      avgVisualComplexity: sequences.reduce((sum, s) => sum + s.spatialData.visualComplexity, 0) / Math.max(1, sequences.length),
      maxComplexity: Math.max(...sequences.map(s => s.spatialData.visualComplexity), 1),
      complexityRange: this.calculateComplexityRange(sequences)
    };
  }

  calculateComplexityRange(sequences) {
    const complexities = sequences.map(s => s.spatialData.visualComplexity);
    return {
      min: Math.min(...complexities, 1),
      max: Math.max(...complexities, 1),
      range: Math.max(...complexities, 1) - Math.min(...complexities, 1)
    };
  }

  extractTemporalPatterns() {
    return this.sessionData.sequences.map(s => s.sequentialData.temporalPattern);
  }

  extractLogicalStructures() {
    return this.sessionData.sequences.map(s => s.sequentialData.logicalStructure);
  }

  calculateReasoningMetrics() {
    const sequences = this.sessionData.sequences;
    return {
      logicalAccuracy: sequences.filter(s => s.patternData.isCorrect).length / Math.max(1, sequences.length),
      avgResponseTime: sequences.reduce((sum, s) => sum + s.patternData.responseTime, 0) / Math.max(1, sequences.length),
      complexityHandling: this.calculateComplexityHandling(sequences)
    };
  }

  calculateComplexityHandling(sequences) {
    const complexSequences = sequences.filter(s => s.spatialData.visualComplexity > 1.5);
    return complexSequences.length > 0
      ? complexSequences.filter(s => s.patternData.isCorrect).length / complexSequences.length
      : 0.7;
  }

  // Métodos rápidos para análise em tempo real

  calculateQuickPatternMetrics(data) {
    const sequences = data.patternData.sequences;
    return {
      recognitionRate: sequences.filter(s => s.patternData.isCorrect).length / Math.max(1, sequences.length),
      avgComplexity: data.patternData.complexity,
      totalPatterns: sequences.length
    };
  }

  calculateQuickMemoryMetrics(data) {
    const sequences = data.memoryData.sequences;
    return {
      retentionRate: sequences.filter(s => s.patternData.isCorrect).length / Math.max(1, sequences.length),
      avgMemoryLoad: data.memoryData.memoryMetrics.avgMemoryLoad,
      memorySpan: Math.max(...sequences.map(s => s.memoryData.memoryLoad), 3)
    };
  }

  calculateQuickSpatialMetrics(data) {
    const interactions = data.spatialData.interactions;
    return {
      spatialAccuracy: interactions.filter(i => i.visualProcessing?.visualAccuracy).length / Math.max(1, interactions.length),
      processingSpeed: data.spatialData.spatialMetrics.avgProcessingTime,
      visualComplexity: data.spatialData.visualComplexity.avgVisualComplexity
    };
  }

  calculateQuickReasoningMetrics(data) {
    const sequences = data.sequentialData.sequences;
    return {
      logicalAccuracy: data.sequentialData.reasoningMetrics.logicalAccuracy,
      reasoningSpeed: data.sequentialData.reasoningMetrics.avgResponseTime,
      patternComplexity: sequences.length > 0 ? sequences[sequences.length - 1].sequentialData.logicalStructure.logicalComplexity : 1
    };
  }

  // Métricas integradas

  calculateOverallPatternCapacity(analyses) {
    const weights = {
      patternRecognition: 0.3,
      visualMemory: 0.25,
      spatialProcessing: 0.25,
      sequentialReasoning: 0.2
    };
    
    const scores = {
      patternRecognition: analyses.patternRecognition.sequenceRecognition || 0.7,
      visualMemory: analyses.visualMemory.shortTermMemory || 0.7,
      spatialProcessing: analyses.spatialProcessing.spatialVisualization || 0.7,
      sequentialReasoning: analyses.sequentialReasoning.logicalSequencing || 0.7
    };
    
    return Object.keys(weights).reduce((sum, domain) => 
      sum + (scores[domain] * weights[domain]), 0
    );
  }

  calculateProcessingEfficiency(analyses) {
    const speeds = [
      analyses.patternRecognition.recognitionSpeed || 0.7,
      analyses.visualMemory.retentionQuality || 0.7,
      analyses.spatialProcessing.spatialEfficiency || 0.7,
      analyses.sequentialReasoning.temporalReasoning || 0.7
    ];
    
    return speeds.reduce((sum, speed) => sum + speed, 0) / speeds.length;
  }

  calculateCrossDomainConsistency(analyses) {
    const scores = [
      analyses.patternRecognition.sequenceRecognition || 0.7,
      analyses.visualMemory.shortTermMemory || 0.7,
      analyses.spatialProcessing.spatialVisualization || 0.7,
      analyses.sequentialReasoning.logicalSequencing || 0.7
    ];
    
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    
    // Menor variância = maior consistência
    return Math.max(0, 1 - Math.sqrt(variance));
  }

  calculateAdaptability(analyses) {
    const adaptabilityScores = [
      analyses.patternRecognition.adaptiveRecognition || 0.7,
      analyses.visualMemory.interferenceResistance || 0.7,
      analyses.spatialProcessing.spatialEfficiency || 0.7,
      analyses.sequentialReasoning.inductiveReasoning || 0.7
    ];
    
    return adaptabilityScores.reduce((sum, score) => sum + score, 0) / adaptabilityScores.length;
  }

  calculateDevelopmentPotential(analyses) {
    // Baseado na variabilidade e pontos fortes
    const strengths = this.identifyStrengths(analyses);
    const consistency = this.calculateCrossDomainConsistency(analyses);
    
    // Alto potencial se há pontos fortes e boa consistência
    const strengthBonus = strengths.length * 0.1;
    const consistencyBonus = consistency * 0.2;
    
    return Math.min(1, 0.6 + strengthBonus + consistencyBonus);
  }

  // Análises de correlação

  analyzePatternMemoryCorrelation(patternAnalysis, memoryAnalysis) {
    const patternScore = patternAnalysis.sequenceRecognition || 0.7;
    const memoryScore = memoryAnalysis.shortTermMemory || 0.7;
    
    const correlation = 1 - Math.abs(patternScore - memoryScore);
    
    return {
      strength: correlation,
      relationship: correlation > 0.7 ? 'strong' : correlation > 0.5 ? 'moderate' : 'weak'
    };
  }

  analyzeSpatialReasoningCorrelation(spatialAnalysis, reasoningAnalysis) {
    const spatialScore = spatialAnalysis.spatialVisualization || 0.7;
    const reasoningScore = reasoningAnalysis.logicalSequencing || 0.7;
    
    const correlation = 1 - Math.abs(spatialScore - reasoningScore);
    
    return {
      strength: correlation,
      relationship: correlation > 0.7 ? 'strong' : correlation > 0.5 ? 'moderate' : 'weak'
    };
  }

  identifyStrengths(analyses) {
    const strengths = [];
    const threshold = 0.8;
    
    if (analyses.patternRecognition.sequenceRecognition >= threshold) {
      strengths.push('reconhecimento_padroes');
    }
    
    if (analyses.visualMemory.shortTermMemory >= threshold) {
      strengths.push('memoria_visual');
    }
    
    if (analyses.spatialProcessing.spatialVisualization >= threshold) {
      strengths.push('processamento_espacial');
    }
    
    if (analyses.sequentialReasoning.logicalSequencing >= threshold) {
      strengths.push('raciocinio_sequencial');
    }
    
    return strengths;
  }

  identifyWeaknesses(analyses) {
    const weaknesses = [];
    const threshold = 0.6;
    
    if (analyses.patternRecognition.sequenceRecognition < threshold) {
      weaknesses.push('reconhecimento_padroes');
    }
    
    if (analyses.visualMemory.shortTermMemory < threshold) {
      weaknesses.push('memoria_visual');
    }
    
    if (analyses.spatialProcessing.spatialVisualization < threshold) {
      weaknesses.push('processamento_espacial');
    }
    
    if (analyses.sequentialReasoning.logicalSequencing < threshold) {
      weaknesses.push('raciocinio_sequencial');
    }
    
    return weaknesses;
  }

  generateSessionSummary(analyses) {
    return {
      overallPerformance: this.calculateOverallPatternCapacity(analyses),
      keyStrengths: this.identifyStrengths(analyses),
      developmentAreas: this.identifyWeaknesses(analyses),
      consistency: this.calculateCrossDomainConsistency(analyses),
      adaptability: this.calculateAdaptability(analyses),
      sessionQuality: this.assessDataQuality()
    };
  }

  assessDataQuality() {
    const totalDataPoints = this.sessionData.sequences.length + this.sessionData.interactions.length;
    const completenessScore = Math.min(1, totalDataPoints / 20); // 20 pontos ideais
    
    const varietyScore = Math.min(1, this.sessionData.sequences.length / 10); // 10 sequências ideais
    
    return (completenessScore + varietyScore) / 2;
  }

  /**
   * Coleta dados usando coletores V3 especializados
   */
  async collectV3Data(gameState) {
    if (!gameState.currentActivity) {
      console.warn('Atividade atual não especificada para coleta V3');
      return null;
    }

    const collector = this.v3Collectors[gameState.currentActivity];
    if (!collector) {
      console.warn(`Coletor V3 não encontrado para atividade: ${gameState.currentActivity}`);
      return null;
    }

    try {
      const data = await collector.collect(gameState);
      
      // Armazena dados da atividade
      if (!this.sessionData.activityData[gameState.currentActivity]) {
        this.sessionData.activityData[gameState.currentActivity] = [];
      }
      this.sessionData.activityData[gameState.currentActivity].push({
        timestamp: Date.now(),
        data: data
      });

      return data;
    } catch (error) {
      console.error(`Erro na coleta V3 para ${gameState.currentActivity}:`, error);
      return null;
    }
  }

  /**
   * Coleta dados complementares usando coletores base relevantes
   */
  async collectComplementaryData(gameState) {
    const relevantCollectors = this.getRelevantBaseCollectors(gameState.currentActivity);
    const complementaryData = {};

    for (const [name, collector] of Object.entries(relevantCollectors)) {
      try {
        const data = await collector.collect(gameState);
        if (data) {
          complementaryData[name] = data;
        }
      } catch (error) {
        console.error(`Erro no coletor complementar ${name}:`, error);
      }
    }

    return complementaryData;
  }

  /**
   * Obtém coletores base relevantes para uma atividade específica
   */
  getRelevantBaseCollectors(activity) {
    const relevantCollectors = {};
    
    switch (activity) {
      case 'reproduction-sequences':
        relevantCollectors.visualMemory = this.collectors.visualMemory;
        relevantCollectors.visualSequence = this.collectors.visualSequence;
        relevantCollectors.temporalPattern = this.collectors.temporalPattern;
        break;
      case 'pattern-completion':
        relevantCollectors.patternRecognition = this.collectors.patternRecognition;
        relevantCollectors.sequentialReasoning = this.collectors.sequentialReasoning;
        relevantCollectors.geometricPattern = this.collectors.geometricPattern;
        break;
      case 'pattern-construction':
        relevantCollectors.spatialPattern = this.collectors.spatialPattern;
        relevantCollectors.spatialProcessing = this.collectors.spatialProcessing;
        relevantCollectors.geometricPattern = this.collectors.geometricPattern;
        break;
      case 'visual-classification':
        relevantCollectors.colorPattern = this.collectors.colorPattern;
        relevantCollectors.patternRecognition = this.collectors.patternRecognition;
        break;
      case 'pattern-transformation':
        relevantCollectors.spatialPattern = this.collectors.spatialPattern;
        relevantCollectors.spatialProcessing = this.collectors.spatialProcessing;
        break;
      case 'anomaly-detection':
        relevantCollectors.errorPattern = this.collectors.errorPattern;
        relevantCollectors.patternRecognition = this.collectors.patternRecognition;
        break;
      default:
        // Retorna todos os coletores base se atividade não reconhecida
        return this.collectors;
    }
    
    return relevantCollectors;
  }

  /**
   * Coleta dados completa (V3 + complementares)
   */
  async collectCompleteData(gameState) {
    const collectedData = {};

    // Determina se deve usar coletores V3
    const useV3Collectors = gameState.currentActivity && this.analysisConfig.useV3Collectors;
    
    if (useV3Collectors) {
      // Coleta dados V3 especializados
      const v3Data = await this.collectV3Data(gameState);
      if (v3Data) {
        collectedData.v3 = v3Data;
        collectedData.activity = gameState.currentActivity;
      }
      
      // Coleta dados complementares dos coletores base
      const complementaryData = await this.collectComplementaryData(gameState);
      if (Object.keys(complementaryData).length > 0) {
        collectedData.complementary = complementaryData;
      }
    } else {
      // Usa apenas coletores base (compatibilidade)
      for (const [name, collector] of Object.entries(this.collectors)) {
        try {
          const data = await collector.collect(gameState);
          if (data) {
            collectedData[name] = data;
          }
        } catch (error) {
          console.error(`Erro no coletor ${name}:`, error);
        }
      }
    }
    
    return collectedData;
  }

  /**
   * Análise integrada V3
   */
  async performV3Analysis(sessionData) {
    const analysis = {
      overview: {},
      activitySpecific: {},
      crossActivity: {},
      therapeuticInsights: {}
    };

    // Análise por atividade
    for (const [activity, data] of Object.entries(sessionData.activityData)) {
      if (data.length > 0) {
        analysis.activitySpecific[activity] = this.analyzeActivityData(activity, data);
      }
    }

    // Análise cross-atividade
    if (Object.keys(analysis.activitySpecific).length > 1) {
      analysis.crossActivity = this.performCrossActivityAnalysis(analysis.activitySpecific);
    }

    // Insights terapêuticos
    analysis.therapeuticInsights = this.generateTherapeuticInsights(analysis);

    // Overview geral
    analysis.overview = this.generateOverview(analysis);

    return analysis;
  }

  analyzeActivityData(activity, dataPoints) {
    const latestData = dataPoints[dataPoints.length - 1].data;
    const trend = this.calculateTrend(dataPoints);
    
    return {
      activity,
      latestMetrics: latestData,
      trend,
      dataPoints: dataPoints.length,
      avgPerformance: this.calculateAveragePerformance(dataPoints),
      strongPoints: this.identifyActivityStrengths(activity, dataPoints),
      improvementAreas: this.identifyImprovementAreas(activity, dataPoints)
    };
  }

  calculateTrend(dataPoints) {
    if (dataPoints.length < 2) return 'stable';
    
    const recent = dataPoints.slice(-3);
    const older = dataPoints.slice(0, -3);
    
    if (recent.length === 0 || older.length === 0) return 'stable';
    
    const recentAvg = recent.reduce((sum, dp) => sum + (dp.data.accuracy || 0), 0) / recent.length;
    const olderAvg = older.reduce((sum, dp) => sum + (dp.data.accuracy || 0), 0) / older.length;
    
    const difference = recentAvg - olderAvg;
    
    if (difference > 10) return 'improving';
    if (difference < -10) return 'declining';
    return 'stable';
  }

  calculateAveragePerformance(dataPoints) {
    if (dataPoints.length === 0) return 0;
    
    const totalAccuracy = dataPoints.reduce((sum, dp) => sum + (dp.data.accuracy || 0), 0);
    return totalAccuracy / dataPoints.length;
  }

  identifyActivityStrengths(activity, dataPoints) {
    // Identifica pontos fortes baseados nas métricas da atividade
    const strengths = [];
    const latestData = dataPoints[dataPoints.length - 1]?.data;
    
    if (!latestData) return strengths;
    
    // Critérios específicos por atividade
    switch (activity) {
      case 'reproduction-sequences':
        if (latestData.sequentialProcessingSpeed > 80) strengths.push('Velocidade de processamento sequencial');
        if (latestData.visualMemorySpan > 75) strengths.push('Capacidade de memória visual');
        break;
      case 'pattern-completion':
        if (latestData.logicalReasoningScore > 80) strengths.push('Raciocínio lógico');
        if (latestData.abstractThinkingScore > 75) strengths.push('Pensamento abstrato');
        break;
      case 'pattern-construction':
        if (latestData.spatialOrganizationScore > 80) strengths.push('Organização espacial');
        if (latestData.planningQuality > 75) strengths.push('Qualidade de planejamento');
        break;
      case 'visual-classification':
        if (latestData.categoricalThinkingIndex > 80) strengths.push('Pensamento categorial');
        if (latestData.visualDiscriminationIndex > 75) strengths.push('Discriminação visual');
        break;
      case 'pattern-transformation':
        if (latestData.mentalRotationAbility > 80) strengths.push('Rotação mental');
        if (latestData.spatialVisualizationIndex > 75) strengths.push('Visualização espacial');
        break;
      case 'anomaly-detection':
        if (latestData.detectionSensitivity > 80) strengths.push('Sensibilidade de detecção');
        if (latestData.visualAttentionScope > 75) strengths.push('Escopo de atenção visual');
        break;
    }
    
    return strengths;
  }

  identifyImprovementAreas(activity, dataPoints) {
    // Identifica áreas de melhoria baseadas nas métricas da atividade
    const improvements = [];
    const latestData = dataPoints[dataPoints.length - 1]?.data;
    
    if (!latestData) return improvements;
    
    // Critérios específicos por atividade
    switch (activity) {
      case 'reproduction-sequences':
        if (latestData.reproductionAccuracy < 60) improvements.push('Precisão de reprodução');
        if (latestData.consistencyScore < 50) improvements.push('Consistência de desempenho');
        break;
      case 'pattern-completion':
        if (latestData.patternRecognitionAccuracy < 60) improvements.push('Reconhecimento de padrões');
        if (latestData.strategicConsistency < 50) improvements.push('Consistência estratégica');
        break;
      case 'pattern-construction':
        if (latestData.constructionAccuracy < 60) improvements.push('Precisão de construção');
        if (latestData.constructionEfficiency < 50) improvements.push('Eficiência de construção');
        break;
      case 'visual-classification':
        if (latestData.classificationAccuracy < 60) improvements.push('Precisão de classificação');
        if (latestData.categoryConsistency < 50) improvements.push('Consistência categorial');
        break;
      case 'pattern-transformation':
        if (latestData.transformationAccuracy < 60) improvements.push('Precisão de transformação');
        if (latestData.ruleApplicationAccuracy < 50) improvements.push('Aplicação de regras');
        break;
      case 'anomaly-detection':
        if (latestData.detectionAccuracy < 60) improvements.push('Precisão de detecção');
        if (latestData.falsePositiveRate > 30) improvements.push('Redução de falsos positivos');
        break;
    }
    
    return improvements;
  }

  performCrossActivityAnalysis(activityAnalyses) {
    const crossAnalysis = {
      cognitiveProfile: this.buildCognitiveProfile(activityAnalyses),
      transferSkills: this.identifyTransferSkills(activityAnalyses),
      consistencyMetrics: this.calculateConsistencyAcrossActivities(activityAnalyses)
    };
    
    return crossAnalysis;
  }

  buildCognitiveProfile(activityAnalyses) {
    const profile = {
      visualProcessing: 0,
      spatialAbilities: 0,
      executiveFunctions: 0,
      memoryCapacity: 0,
      attentionControl: 0
    };
    
    let count = 0;
    
    for (const [activity, analysis] of Object.entries(activityAnalyses)) {
      const metrics = analysis.latestMetrics;
      
      switch (activity) {
        case 'reproduction-sequences':
          profile.memoryCapacity += metrics.visualMemorySpan || 0;
          profile.attentionControl += metrics.attentionSustainedScore || 0;
          break;
        case 'pattern-completion':
          profile.visualProcessing += metrics.logicalReasoningScore || 0;
          profile.executiveFunctions += metrics.abstractThinkingScore || 0;
          break;
        case 'pattern-construction':
          profile.spatialAbilities += metrics.spatialOrganizationScore || 0;
          profile.executiveFunctions += metrics.executivePlanningScore || 0;
          break;
        case 'visual-classification':
          profile.visualProcessing += metrics.visualDiscriminationIndex || 0;
          profile.executiveFunctions += metrics.categoricalThinkingIndex || 0;
          break;
        case 'pattern-transformation':
          profile.spatialAbilities += metrics.spatialVisualizationIndex || 0;
          profile.visualProcessing += metrics.mentalRotationAbility || 0;
          break;
        case 'anomaly-detection':
          profile.attentionControl += metrics.visualAttentionScope || 0;
          profile.visualProcessing += metrics.perceptualSensitivity || 0;
          break;
      }
      count++;
    }
    
    // Normaliza scores
    if (count > 0) {
      Object.keys(profile).forEach(key => {
        profile[key] = profile[key] / count;
      });
    }
    
    return profile;
  }

  identifyTransferSkills(activityAnalyses) {
    const transferSkills = [];
    const activities = Object.keys(activityAnalyses);
    
    // Identifica habilidades que se transferem entre atividades
    if (activities.includes('pattern-construction') && activities.includes('pattern-transformation')) {
      const constructionSpatial = activityAnalyses['pattern-construction'].latestMetrics.spatialOrganizationScore || 0;
      const transformationSpatial = activityAnalyses['pattern-transformation'].latestMetrics.spatialVisualizationIndex || 0;
      
      if (Math.abs(constructionSpatial - transformationSpatial) < 20) {
        transferSkills.push('Habilidades espaciais consistentes');
      }
    }
    
    if (activities.includes('visual-classification') && activities.includes('anomaly-detection')) {
      const classificationVisual = activityAnalyses['visual-classification'].latestMetrics.visualDiscriminationIndex || 0;
      const detectionVisual = activityAnalyses['anomaly-detection'].latestMetrics.perceptualSensitivity || 0;
      
      if (Math.abs(classificationVisual - detectionVisual) < 20) {
        transferSkills.push('Discriminação visual consistente');
      }
    }
    
    return transferSkills;
  }

  calculateConsistencyAcrossActivities(activityAnalyses) {
    const accuracies = Object.values(activityAnalyses).map(analysis => analysis.avgPerformance);
    
    if (accuracies.length < 2) return { consistency: 100, variance: 0 };
    
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    const consistency = Math.max(0, 100 - Math.sqrt(variance));
    
    return { consistency, variance: Math.sqrt(variance) };
  }

  generateTherapeuticInsights(analysis) {
    const insights = {
      primaryStrengths: [],
      primaryChallenges: [],
      recommendations: [],
      cognitiveProfile: analysis.crossActivity?.cognitiveProfile || {}
    };
    
    // Identifica forças primárias
    if (analysis.crossActivity?.cognitiveProfile) {
      const profile = analysis.crossActivity.cognitiveProfile;
      const sortedAbilities = Object.entries(profile).sort((a, b) => b[1] - a[1]);
      
      insights.primaryStrengths = sortedAbilities.slice(0, 2).map(([ability, score]) => ({
        ability,
        score: Math.round(score)
      }));
      
      insights.primaryChallenges = sortedAbilities.slice(-2).map(([ability, score]) => ({
        ability,
        score: Math.round(score)
      }));
    }
    
    // Gera recomendações
    insights.recommendations = this.generateRecommendations(analysis);
    
    return insights;
  }

  generateOverview(analysis) {
    return {
      totalActivities: Object.keys(analysis.activitySpecific).length,
      overallPerformance: this.calculateOverallPerformance(analysis),
      keyInsights: this.extractKeyInsights(analysis),
      progressionStatus: this.assessProgressionStatus(analysis)
    };
  }

  calculateOverallPerformance(analysis) {
    const activities = Object.values(analysis.activitySpecific);
    if (activities.length === 0) return 0;
    
    const totalPerformance = activities.reduce((sum, activity) => sum + activity.avgPerformance, 0);
    return Math.round(totalPerformance / activities.length);
  }

  extractKeyInsights(analysis) {
    const insights = [];
    
    // Insights baseados no perfil cognitivo
    if (analysis.therapeuticInsights?.primaryStrengths?.length > 0) {
      const topStrength = analysis.therapeuticInsights.primaryStrengths[0];
      insights.push(`Força principal: ${topStrength.ability} (${topStrength.score}%)`);
    }
    
    if (analysis.therapeuticInsights?.primaryChallenges?.length > 0) {
      const topChallenge = analysis.therapeuticInsights.primaryChallenges[0];
      insights.push(`Área de foco: ${topChallenge.ability} (${topChallenge.score}%)`);
    }
    
    // Insights de transferência
    if (analysis.crossActivity?.transferSkills?.length > 0) {
      insights.push(`Habilidades transferíveis identificadas: ${analysis.crossActivity.transferSkills.length}`);
    }
    
    return insights;
  }

  assessProgressionStatus(analysis) {
    const trends = Object.values(analysis.activitySpecific).map(activity => activity.trend);
    const improvingCount = trends.filter(trend => trend === 'improving').length;
    const decliningCount = trends.filter(trend => trend === 'declining').length;
    
    if (improvingCount > decliningCount) return 'progressing';
    if (decliningCount > improvingCount) return 'needs_attention';
    return 'stable';
  }

  // Método principal para coleta de dados atualizado
  async collectDataPoint(gameState) {
    if (!this.activeSession) {
      console.warn('⚠️ Tentativa de coleta sem sessão ativa');
      return null;
    }
    
    try {
      // Usa nova coleta completa que inclui V3
      const collectedData = await this.collectCompleteData(gameState);
      
      if (collectedData && Object.keys(collectedData).length > 0) {
        // Armazena ponto de dados
        this.sessionData.interactions.push({
          timestamp: Date.now(),
          gameState: this.sanitizeGameState(gameState),
          collectedData,
          activity: gameState.currentActivity
        });
        
        // Realiza análise em tempo real se configurado
        if (this.analysisConfig.realTimeAnalysis) {
          await this.performRealtimeAnalysis(collectedData, gameState);
        }
        
        return collectedData;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Erro na coleta de dados:', error);
      return null;
    }
  }
}

/**
 * Factory function para criar instância dos coletores
 * Função esperada pelos processadores do sistema
 */
export function createCollectors() {
  return new PadroesVisuaisCollectorsHub();
}

/**
 * Função alternativa para obter coletores
 * Compatibilidade com diferentes padrões
 */
export function getCollectors() {
  return new PadroesVisuaisCollectorsHub();
}
