# 🏗️ Portal Betina V3 - Mapa Arquitetural de Serviços

## 📋 Visão Geral da Arquitetura

O Portal Betina V3 possui uma arquitetura em camadas bem definida, onde cada tipo de arquivo tem uma responsabilidade específica:

### 🔴 **ARQUIVOS DE SISTEMA** (Core/Orchestration)
- **Função**: Orquestrar, gerenciar e coordenar o sistema
- **Localização**: `src/api/services/core/`, `src/api/services/orchestration/`
- **Responsabilidade**: Controle de fluxo, inicialização, coordenação entre módulos

### 🔵 **ARQUIVOS DE MÉTRICAS/PROCESSAMENTO** (Metrics/Analysis/Algorithms)
- **Função**: Coletar, processar e analisar dados
- **Localização**: `src/api/services/metrics/`, `src/api/services/analysis/`, `src/api/services/algorithms/`, `src/api/algorithms/`
- **Responsabilidade**: Processamento de dados, análises cognitivas, predições

---

## 🗂️ Estrutura Detalhada por Pasta

### 🔴 **MÓDULOS DE SISTEMA** (System Files)

#### **`src/api/services/core/`**
```
SystemOrchestrator.js    ← 🎯 ORQUESTRADOR PRINCIPAL
CacheService.js          ← Cache e performance
DatabaseService.js       ← Acesso ao banco
SessionManager.js        ← Gerenciamento de sessões
QueueService.js          ← Filas de processamento
EmailService.js          ← Serviços de email
```

**Responsabilidades:**
- **SystemOrchestrator**: Coordena TODOS os módulos do sistema
- **CacheService**: Otimização de performance
- **DatabaseService**: Persistência de dados
- **SessionManager**: Controle de sessões de usuário

---

### 🔵 **MÓDULOS DE MÉTRICAS/PROCESSAMENTO** (Processing Files)

#### **`src/api/services/metrics/`**
```
MetricsService.js         ← 📊 COLETOR PRINCIPAL DE MÉTRICAS
MetricsCollector.js       ← Coleta de dados
performanceAnalyzer.js    ← Análise de performance
ErrorPatternAnalyzer.js   ← Análise de padrões de erro
```

#### **`src/api/services/analysis/`**
```
AnalysisOrchestrator.js   ← 🧠 ORQUESTRADOR DE ANÁLISES
BehavioralAnalyzer.js     ← Análise comportamental
CognitiveAnalyzer.js      ← Análise cognitiva
ProgressAnalyzer.js       ← Análise de progresso
SessionAnalyzer.js        ← Análise de sessão
TherapeuticAnalyzer.js    ← Análise terapêutica
```

#### **`src/api/services/algorithms/`**
```
PredictiveAnalysisEngine.js  ← 🔮 PREDIÇÕES E ANÁLISES AVANÇADAS
AdvancedMetricsEngine.js     ← MÉTRICAS AVANÇADAS
```

#### **`src/api/algorithms/`**
```
CognitiveAssociationEngine.js ← 🧮 ALGORITMO COGNITIVO PRINCIPAL
```

---

## 🔄 Fluxo de Dados e Interações

### **Exemplo 1: SystemOrchestrator usando Algoritmos**

```javascript
// ARQUIVO DE SISTEMA usa ARQUIVO DE ALGORITMO
// SystemOrchestrator.js (SISTEMA)
import { PredictiveAnalysisEngine } from '../algorithms/PredictiveAnalysisEngine.js'

class SystemOrchestrator {
  async initializeTherapeuticSystems() {
    // Sistema inicializa algoritmo
    this.therapeuticSystems.predictiveAnalysisEngine = new PredictiveAnalysisEngine()
    await this.therapeuticSystems.predictiveAnalysisEngine.initialize()
  }
  
  async runPredictiveAnalysis() {
    // Sistema usa algoritmo para processar
    const prediction = this.existingSystems.predictiveAnalysisEngine.predict(recentMetrics)
    return prediction
  }
}
```

### **Exemplo 2: MetricsService usando Algoritmos**

```javascript
// ARQUIVO DE MÉTRICAS usa ARQUIVO DE ALGORITMO
// MetricsService.js (MÉTRICAS)
import { CognitiveAssociationEngine } from '../algorithms/CognitiveAssociationEngine.js'
import { PredictiveAnalysisEngine } from './algorithms/PredictiveAnalysisEngine.js'

class MetricsService {
  constructor() {
    // Métricas inicializa algoritmos
    this.cognitiveEngine = new CognitiveAssociationEngine()
    this.predictiveEngine = new PredictiveAnalysisEngine()
  }
  
  async analyzeUserBehavior(data) {
    // Métricas usa algoritmos para análise
    const cognitiveAnalysis = await this.cognitiveEngine.analyze(data)
    const prediction = await this.predictiveEngine.predict(data)
    return { cognitiveAnalysis, prediction }
  }
}
```

### **Exemplo 3: SystemOrchestrator coordenando Serviços de Métricas**

```javascript
// ARQUIVO DE SISTEMA coordena ARQUIVOS DE MÉTRICAS
// SystemOrchestrator.js (SISTEMA)
import { metricsService } from '../metrics/index.js'
import { getAnalysisOrchestrator } from '../analysis/index.js'

class SystemOrchestrator {
  async processGameSession(sessionData) {
    // 1. Sistema pede para métricas coletarem dados
    const metrics = await metricsService.collectMetrics(sessionData)
    
    // 2. Sistema pede para análise processar
    const analysisOrchestrator = getAnalysisOrchestrator()
    const analysis = await analysisOrchestrator.analyze(metrics)
    
    // 3. Sistema coordena resultado final
    return this.consolidateResults(metrics, analysis)
  }
}
```

---

## 🎯 **DIFERENÇA PRINCIPAL**

### 🔴 **ARQUIVOS DE SISTEMA** → **COORDENAM**
- **O QUE FAZEM**: Orquestram, inicializam, coordenam, gerenciam
- **COMO FUNCIONAM**: Chamam outros módulos, controlam fluxo
- **EXEMPLO**: SystemOrchestrator chama MetricsService para coletar dados

### 🔵 **ARQUIVOS DE MÉTRICAS/PROCESSAMENTO** → **PROCESSAM**
- **O QUE FAZEM**: Coletam, analisam, processam, calculam
- **COMO FUNCIONAM**: Recebem dados, aplicam algoritmos, retornam resultados
- **EXEMPLO**: MetricsService usa CognitiveAssociationEngine para analisar cognição

---

## 📊 Mapa de Responsabilidades

| **Tipo** | **Pasta** | **Função Principal** | **Usado Por** |
|----------|-----------|---------------------|---------------|
| 🔴 **Sistema** | `core/` | Orquestrar e coordenar | Frontend, API Routes |
| 🔵 **Métricas** | `metrics/` | Coletar e medir | SystemOrchestrator, Games |
| 🔵 **Análise** | `analysis/` | Analisar e interpretar | SystemOrchestrator, Metrics |
| 🔵 **Algoritmos** | `algorithms/` | Processar e calcular | Metrics, Analysis, Core |
| 🔴 **Jogos** | `game/` | Coordenar coletores | SystemOrchestrator |
| 🔴 **Sessões** | `sessions/` | Gerenciar sessões | SystemOrchestrator |

---

## 🔗 Centralização via `src/api/services/index.js`

O arquivo `index.js` principal **NÃO exporta algoritmos diretamente**, mas sim **serviços que usam algoritmos**:

```javascript
// index.js exporta SERVIÇOS, não algoritmos
export { metricsService } from './metrics/index.js'           // ← Usa algoritmos
export { getSystemOrchestrator } from './core/index.js'      // ← Coordena algoritmos
export { getAnalysisOrchestrator } from './analysis/index.js' // ← Usa algoritmos

// Os algoritmos são usados DENTRO dos serviços, não exportados diretamente
```

---

## ✅ **Status Atual da Arquitetura**

### **✅ FUNCIONANDO CORRETAMENTE:**
- SystemOrchestrator coordenando todos os módulos
- MetricsService coletando e processando dados
- Algoritmos sendo usados pelos serviços apropriados
- Coletores de jogos integrados ao sistema
- Centralização de exportações funcionando

### **✅ ARQUITETURA LIMPA:**
- Separação clara entre sistema e processamento
- Fluxo de dados bem definido
- Reutilização de algoritmos por múltiplos serviços
- Padrão singleton para performance

---

## 🎯 **RESUMO EXECUTIVO**

A arquitetura do Portal Betina V3 segue o padrão:

1. **ARQUIVOS DE SISTEMA** (vermelho 🔴) = **COORDENADORES**
   - Orquestram o fluxo
   - Gerenciam recursos
   - Controlam inicialização

2. **ARQUIVOS DE PROCESSAMENTO** (azul 🔵) = **PROCESSADORES**
   - Executam algoritmos
   - Analisam dados
   - Geram insights

3. **FLUXO**: Sistema → chama → Processamento → usa → Algoritmos → retorna → Resultados

A integração está funcionando corretamente, com todos os imports/exports corretos e uma arquitetura modular bem estruturada.
