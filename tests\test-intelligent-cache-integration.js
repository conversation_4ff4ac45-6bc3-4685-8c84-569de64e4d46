/**
 * @file test-intelligent-cache-integration.js
 * @description Teste de integração do IntelligentCache nos serviços principais
 * @version 1.0.0
 */

import { getCache } from './src/api/services/core/cache/IntelligentCache.js';
import { getHealthCheckService } from './src/api/services/core/health/HealthCheckService.js';

console.log('🧪 Iniciando teste de integração do IntelligentCache...\n');

// 1. Testar Cache Standalone
console.log('📦 1. Testando IntelligentCache standalone...');
const cache = getCache({
  maxSize: 100,
  defaultTTL: 60000,
  strategy: 'LRU'
});

// Adicionar dados de teste
cache.set('test_key_1', { data: 'valor teste 1' }, { tags: ['teste', 'demo'] });
cache.set('test_key_2', { data: 'valor teste 2' }, { tags: ['teste', 'demo'] });
cache.set('large_data', { data: 'x'.repeat(20000) }, { tags: ['large'], compress: true });

console.log('✅ Dados adicionados ao cache');
console.log('📊 Métricas iniciais:', cache.getMetrics());

// Testar recuperação
const retrieved = cache.get('test_key_1');
console.log('📥 Dado recuperado:', retrieved);

// Testar invalidação por tags
const removed = cache.invalidateByTags('teste');
console.log(`🗑️ Entradas removidas por tag: ${removed}`);

console.log('📊 Métricas finais:', cache.getMetrics());
console.log('🏥 Status de saúde do cache:', cache.getHealthStatus());

// 2. Testar integração com HealthCheckService
console.log('\n🏥 2. Testando integração com HealthCheckService...');

const healthService = getHealthCheckService({
  checkInterval: 10000, // 10 segundos para teste
  cpuThreshold: 80,
  memoryThreshold: 85
});

// Aguardar inicialização
setTimeout(async () => {
  console.log('🔍 Executando health check...');
  
  const healthResult = await healthService.runHealthCheck();
  console.log('📋 Resultado do health check:', {
    overall: healthResult.overall,
    components: Object.keys(healthResult.components),
    cache: healthResult.cache
  });
  
  // Testar cache do health check
  console.log('\n📥 Testando cache do health check...');
  const cachedHealth = await healthService.runHealthCheck();
  console.log('⚡ Health check em cache (deve ser mais rápido)');
  
  // Obter métricas do cache do health service
  const healthCacheMetrics = healthService.getCacheMetrics();
  console.log('📊 Métricas do cache do HealthService:', healthCacheMetrics);
  
  // Testar invalidação
  console.log('\n🗑️ Testando invalidação de cache...');
  const removed = healthService.invalidateHealthCache();
  console.log(`Entradas removidas: ${removed}`);
  
  // 3. Testar performance
  console.log('\n⚡ 3. Testando performance do cache...');
  
  const startTime = Date.now();
  
  // Adicionar muitos dados
  for (let i = 0; i < 50; i++) {
    cache.set(`perf_test_${i}`, {
      id: i,
      timestamp: Date.now(),
      data: `Performance test data ${i}`,
      metadata: { test: true, index: i }
    }, {
      tags: ['performance', `batch_${Math.floor(i / 10)}`],
      ttl: 30000
    });
  }
  
  const setTime = Date.now() - startTime;
  console.log(`📝 50 entradas adicionadas em ${setTime}ms`);
  
  // Testar recuperação
  const retrieveStart = Date.now();
  let hits = 0;
  
  for (let i = 0; i < 50; i++) {
    const result = cache.get(`perf_test_${i}`);
    if (result) hits++;
  }
  
  const retrieveTime = Date.now() - retrieveStart;
  console.log(`📥 50 recuperações em ${retrieveTime}ms (${hits} hits)`);
  
  // Métricas finais
  const finalMetrics = cache.getMetrics();
  console.log('\n📊 Métricas finais do teste:', finalMetrics);
  
  // Estatísticas detalhadas
  const stats = cache.getStats();
  console.log('📈 Estatísticas detalhadas:', {
    hitRate: stats.metrics.hitRate,
    size: stats.metrics.size,
    utilizationRate: stats.metrics.utilizationRate,
    topKeys: stats.topKeys.slice(0, 5)
  });
  
  // 4. Demonstração de invalidação inteligente
  console.log('\n🎯 4. Demonstração de invalidação inteligente...');
  
  // Invalidar por padrão
  const patternRemoved = cache.invalidateByPattern(/perf_test_[0-2]\d/);
  console.log(`🗑️ Removidas por padrão: ${patternRemoved}`);
  
  // Invalidar por tags
  const tagRemoved = cache.invalidateByTags(['performance']);
  console.log(`🗑️ Removidas por tag: ${tagRemoved}`);
  
  console.log('\n✅ Teste de integração concluído com sucesso!');
  console.log('📊 Métricas finais:', cache.getMetrics());
  
  // Parar monitoramento
  healthService.stopMonitoring();
  
}, 2000);

// 5. Testar comportamento em caso de erro
console.log('\n❌ 5. Testando comportamento em caso de erro...');

try {
  // Tentar operação inválida
  const invalidResult = cache.get(null);
  console.log('Resultado de operação inválida:', invalidResult);
} catch (error) {
  console.log('✅ Erro capturado corretamente:', error.message);
}

// 6. Testar export/import de cache
console.log('\n💾 6. Testando export/import de cache...');

// Adicionar alguns dados
cache.set('export_test_1', { value: 'test 1' });
cache.set('export_test_2', { value: 'test 2' });

// Exportar
const exported = cache.export();
console.log('📤 Dados exportados:', {
  entries: exported.cache.length,
  timestamp: exported.timestamp
});

// Limpar e importar
cache.clear();
console.log('🗑️ Cache limpo');

const importResult = cache.import(exported);
console.log('📥 Import result:', importResult);
console.log('📊 Métricas pós-import:', cache.getMetrics());

console.log('\n🎉 Todos os testes de integração concluídos!');
