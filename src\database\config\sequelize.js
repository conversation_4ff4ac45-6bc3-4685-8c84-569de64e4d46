/**
 * 📊 CONFIGURAÇÃO SEQUELIZE - Portal Betina V3
 * Configuração do ORM para PostgreSQL
 */

import { Sequelize } from 'sequelize';
import { createLogger } from '../../api/utils/logger.js';

const logger = createLogger('database:sequelize');

// Validação de variáveis de ambiente obrigatórias
const requiredEnvVars = ['DB_USER', 'DB_PASSWORD', 'DB_NAME', 'DB_HOST'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  const errorMessage = `❌ Variáveis de ambiente obrigatórias não definidas: ${missingEnvVars.join(', ')}`;
  logger.error(errorMessage);
  throw new Error(errorMessage);
}

// Configuração do banco de dados
const config = {
  development: {
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    logging: (msg) => logger.debug(msg),
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000
    }
  },
  production: {
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    host: process.env.DB_HOST || 'postgres_geral',
    port: process.env.DB_PORT || 5432,
    dialect: 'postgres',
    logging: false,
    pool: {
      max: 20,
      min: 5,
      acquire: 30000,
      idle: 10000
    },
    ssl: process.env.NODE_ENV === 'production' ? {
      require: true,
      rejectUnauthorized: false
    } : false
  }
};

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// Instância do Sequelize
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  dbConfig
);

// Teste de conexão
async function testConnection() {
  try {
    await sequelize.authenticate();
    logger.info('✅ Conexão com PostgreSQL estabelecida com sucesso');
    return true;
  } catch (error) {
    logger.error('❌ Erro ao conectar com PostgreSQL:', error);
    return false;
  }
}

// Sincronização do banco (apenas em desenvolvimento)
async function syncDatabase() {
  try {
    if (env === 'development') {
      await sequelize.sync({ alter: true });
      logger.info('✅ Modelos sincronizados com o banco');
    }
  } catch (error) {
    logger.error('❌ Erro ao sincronizar modelos:', error);
    throw error;
  }
}

export { sequelize, testConnection, syncDatabase };
