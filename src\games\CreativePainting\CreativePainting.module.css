/**
 * @file CreativePainting.module.css
 * @description Estilos modulares para Pintura Criativa - Padrão Unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do CreativePainting */
.creativePaintingGame {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* CONTAINER PRINCIPAL DO JOGO */
.gameContainer {
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
}

/* HEADER PADRÃO */
.gameHeader {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  text-align: center;
  position: relative;
}

.gameTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.gameSubtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: 20px;
}

.gameStats {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.statItem {
  background: rgba(255, 255, 255, 0.15);
  padding: 12px 20px;
  border-radius: 15px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.statValue {
  font-size: 1.5rem;
  font-weight: 700;
  display: block;
}

.statLabel {
  font-size: 0.9rem;
  opacity: 0.8;
}

.backButton {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.gameTitle {
  font-size: 2rem;
  font-weight: 800;
  text-align: center;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Área de pintura */
.paintingArea {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
}

.canvasContainer {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
}

.canvas {
  width: 100%;
  height: 400px;
  background: white;
  border-radius: 12px;
  cursor: crosshair;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}

/* Paleta de cores */
.colorPalette {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 1.5rem;
  width: 200px;
}

.paletteTitle {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-align: center;
}

.colorGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.colorButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.colorButton:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.colorButton.active {
  border-width: 4px;
  border-color: #FECA57;
  transform: scale(1.15);
}

/* Ferramentas */
.toolsSection {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 1rem;
}

.toolsTitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.brushSizes {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  justify-content: center;
}

.brushSize {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.brushSize:hover {
  background: rgba(255, 255, 255, 0.3);
}

.brushSize.active {
  background: rgba(255, 206, 84, 0.3);
  border-color: #FECA57;
}

.brushDot {
  border-radius: 50%;
  background: white;
}

.toolButtons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toolButton {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  text-align: center;
}

.toolButton:hover {
  background: rgba(255, 255, 255, 0.25);
}

/* Controles do jogo */
.gameControls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.controlButton {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  font-weight: 600;
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.saveButton {
  background: rgba(76, 175, 80, 0.2);
  border-color: rgba(76, 175, 80, 0.4);
}

.saveButton:hover {
  background: rgba(76, 175, 80, 0.3);
}

.clearButton {
  background: rgba(244, 67, 54, 0.2);
  border-color: rgba(244, 67, 54, 0.4);
}

.clearButton:hover {
  background: rgba(244, 67, 54, 0.3);
}

/* Galeria de desenhos */
.gallery {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  margin-top: 2rem;
}

.galleryTitle {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
}

.galleryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
}

.galleryItem {
  background: white;
  border-radius: 12px;
  padding: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.galleryItem:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.galleryThumbnail {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
}

/* Mensagens de feedback */
.successMessage {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(76, 175, 80, 0.95);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-weight: 600;
  z-index: 1000;
  animation: slideIn 0.5s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .creativePaintingGame {
    padding: 0.5rem;
  }
  
  .paintingArea {
    flex-direction: column;
    gap: 1rem;
  }
  
  .colorPalette {
    width: 100%;
  }
  
  .colorGrid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .canvas {
    height: 300px;
  }
  
  .gameTitle {
    font-size: 1.5rem;
  }
  
  .gameControls {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .gameHeader {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .colorGrid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .canvas {    height: 250px;
  }
  
  .galleryGrid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Estilos para o botão de voltar e badge de dificuldade */
.backButton {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-50%) translateX(-2px);
}

.difficultyBadge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  margin-left: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Ajuste para o gameTitle para acomodar o botão de voltar */
.gameTitle {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  flex: 1;
}

.gameHeader {
  position: relative;
  display: flex;
  align-items: center;
  padding: 2rem;
}

/* =====================================================
   🎯 ESTILOS PARA ATIVIDADES TERAPÊUTICAS V3
   ===================================================== */

/* Seletor de atividades terapêuticas */
.activitySelector {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  backdrop-filter: var(--card-blur);
}

.activityTitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.activityGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.activityBtn {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

.activityBtn:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.activityBtn.active {
  background: var(--success-bg);
  border: 2px solid var(--success-border);
  transform: scale(1.02);
}

.activityIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.activityName {
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  margin-bottom: 0.3rem;
}

.activityFocus {
  font-size: 0.7rem;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

/* Área de atividade específica */
.activityArea {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: var(--card-blur);
}

.therapeuticActivity {
  width: 100%;
}

.activityHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.activityHeader h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.activityHeader p {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Canvas terapêutico */
.therapeuticCanvas {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: white;
  cursor: crosshair;
  display: block;
  margin: 0 auto;
}

/* Análise de preferência cromática */
.colorAnalysisArea {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.expandedColorPalette {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

.analysisColorBtn {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.analysisColorBtn:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.6);
}

.colorUsageCount {
  position: absolute;
  top: -8px;
  right: -8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

.emotionalCanvas {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
}

/* Métricas em tempo real */
.realTimeMetrics,
.motorMetrics,
.spatialMetrics,
.creativityMetrics,
.attentionMetrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.metricCard {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  backdrop-filter: blur(5px);
}

.metricCard span:first-child {
  display: block;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
}

.metricCard span:last-child {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #4CAF50;
}

/* Rastreamento de precisão motora */
.motorTrackingArea {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.guidedCanvas {
  position: relative;
  display: flex;
  justify-content: center;
}

.guidedPaths {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.pathOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Mapeamento de organização espacial */
.spatialMappingArea {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.spatialCanvas {
  position: relative;
  display: flex;
  justify-content: center;
}

.spatialGrid {
  position: absolute;
  top: 0;
  left: 0;
  width: 600px;
  height: 400px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  pointer-events: none;
  opacity: 0.3;
}

.gridCell {
  border: 1px dashed rgba(255, 255, 255, 0.3);
}

/* Perfil de expressão criativa */
.creativeProfilingArea {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.creativeCanvas {
  display: flex;
  justify-content: center;
}

/* Medição de foco atencional */
.attentionMeasurementArea {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.focusTask {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.taskInstruction {
  font-size: 1.1rem;
  font-weight: 600;
  color: #FFD700;
  text-align: center;
  padding: 1rem;
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.4);
  border-radius: 8px;
}

/* =====================================================
   🎨 ESTILOS PARA ATIVIDADES FUNCIONAIS V3
   ===================================================== */

/* Atividade de pintura geral */
.paintingActivity {
  width: 100%;
}

/* Pintura Livre */
.freePaintingArea {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.colorPalette {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

.colorPalette h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.colorGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
  gap: 0.5rem;
}

.colorBtn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.colorBtn:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.6);
}

.colorBtn.active {
  border-color: #FFD700;
  border-width: 4px;
  transform: scale(1.1);
}

.brushControls {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

.brushControls h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.brushSizeControl {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.brushSizeControl label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.brushSlider {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: rgba(255, 255, 255, 0.2);
  outline: none;
  cursor: pointer;
}

.canvasContainer {
  display: flex;
  justify-content: center;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 16px;
  padding: 2rem;
  backdrop-filter: var(--card-blur);
}

.paintingCanvas {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: white;
  cursor: crosshair;
}

.advancedCanvas {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: white;
  cursor: crosshair;
}

.actionControls {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.clearBtn, .saveBtn, .undoBtn, .redoBtn {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

.clearBtn:hover {
  background: var(--error-bg);
  border-color: var(--error-border);
}

.saveBtn:hover {
  background: var(--success-bg);
  border-color: var(--success-border);
}

.undoBtn:hover, .redoBtn:hover {
  background: rgba(33, 150, 243, 0.3);
  border-color: rgba(33, 150, 243, 0.5);
}

.undoBtn:disabled, .redoBtn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Pintura Assistida */
.assistedPaintingArea {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.templateSelector {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

.templateSelector h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.templateGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.templateBtn {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

.templateBtn:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.2);
}

.templateBtn.active {
  background: var(--success-bg);
  border-color: var(--success-border);
}

.templateIcon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.templateName {
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
}

.assistedCanvas {
  position: relative;
  display: inline-block;
}

.clickableAreas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.clickableArea {
  pointer-events: all;
  transition: all 0.3s ease;
}

.clickableArea:hover {
  background: rgba(255, 255, 255, 0.1);
}

.progressSection {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

.progressSection h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.progressBar {
  width: 100%;
  height: 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
  transition: width 0.3s ease;
}

/* Canvas de Pintura */
.canvasPaintingArea {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.toolBar {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: var(--card-blur);
}

.toolBar h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.toolGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
  gap: 0.5rem;
}

.toolBtn {
  width: 60px;
  height: 60px;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

.toolBtn:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.2);
}

.toolBtn.active {
  background: var(--success-bg);
  border-color: var(--success-border);
}

.advancedControls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.controlGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.controlGroup label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.brushTypeSelect {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 6px;
  padding: 0.5rem;
  color: white;
  backdrop-filter: var(--card-blur);
}

/* Responsividade para atividades funcionais */
@media (max-width: 768px) {
  .activityGrid {
    grid-template-columns: 1fr;
  }

  .colorGrid {
    grid-template-columns: repeat(5, 1fr);
  }

  .colorBtn {
    width: 40px;
    height: 40px;
  }

  .paintingCanvas, .advancedCanvas {
    width: 100%;
    max-width: 400px;
    height: 300px;
  }

  .templateGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .toolGrid {
    grid-template-columns: repeat(3, 1fr);
  }

  .advancedControls {
    grid-template-columns: 1fr;
  }

  .actionControls {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

/* =====================================================
   🎯 ESTILOS PADRÃO LETTERRECOGNITION - OBRIGATÓRIOS
   ===================================================== */

.creativePaintingGame {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

.gameContent {
  max-width: 1200px;
  margin: 0 auto;
}

/* Header do jogo - PADRÃO LETTERRECOGNITION */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.headerTtsButton.ttsActive {
  background: var(--success-bg);
  border-color: var(--success-border);
}

/* Estatísticas do jogo - PADRÃO LETTERRECOGNITION */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.25);
}

.statValue {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 0.25rem;
}

.statLabel {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

/* Menu de atividades - PADRÃO LETTERRECOGNITION */
.activityMenu {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 0 1rem;
  flex-wrap: wrap;
}

.activityButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  min-width: 80px;
  backdrop-filter: var(--card-blur);
  color: white;
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.activityButton.active {
  background: var(--success-bg);
  border-color: var(--success-border);
  color: white;
}

/* Controles do jogo - CENTRALIZADOS E SEPARADOS */
.gameControls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2rem;
  margin-top: 2rem;
  padding: 0 1rem;
  flex-wrap: wrap;
}

.controlsGroup {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
}

.controlButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: var(--card-blur);
}

.controlButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}
