import React from 'react';
import PropTypes from 'prop-types';
import { Alert, AlertTitle } from '@material-ui/lab';
import { Box, IconButton, Tooltip } from '@material-ui/core';
import HelpOutlineIcon from '@material-ui/icons/HelpOutline';
import CloudDownloadIcon from '@material-ui/icons/CloudDownload';

/**
 * Componente de aviso para dados de backup
 * @param {Object} props - Propriedades do componente
 * @returns {React.Component}
 */
const BackupWarningBanner = ({ 
  isBackupData = false, 
  hasServerError = false, 
  serverErrorMessage = '', 
  onImportClick = null
}) => {
  if (!isBackupData) return null;
  
  const severity = hasServerError ? 'warning' : 'info';
  
  return (
    <Box mb={2}>
      <Alert 
        severity={severity}
        variant="outlined" 
        style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between'
        }}
      >
        <div>
          <AlertTitle>
            {hasServerError 
              ? 'Aviso: Dados de Backup com Erro de Servidor' 
              : 'Informação: Visualizando Dados de Backup'}
          </AlertTitle>
          {hasServerError 
            ? `Você está visualizando dados de backup que foram exportados quando houve erro de conexão com o servidor. 
               Alguns dados podem estar incompletos. ${serverErrorMessage ? `Erro: ${serverErrorMessage}` : ''}` 
            : 'Você está visualizando dados de um backup importado. Para ver dados atualizados, faça login normalmente.'}
          
          <Tooltip title="Os dados de backup podem não refletir o estado atual do usuário e podem ter informações incompletas.">
            <IconButton size="small" color="inherit">
              <HelpOutlineIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </div>
        
        {onImportClick && (
          <Tooltip title="Importar outro backup">
            <IconButton 
              size="small" 
              color="primary" 
              onClick={onImportClick}
              style={{ marginLeft: '16px' }}
            >
              <CloudDownloadIcon />
            </IconButton>
          </Tooltip>
        )}
      </Alert>
    </Box>
  );
};

BackupWarningBanner.propTypes = {
  isBackupData: PropTypes.bool,
  hasServerError: PropTypes.bool,
  serverErrorMessage: PropTypes.string,
  onImportClick: PropTypes.func
};

export default BackupWarningBanner;
