/**
 * 🧠 SEQUENTIAL MEMORY COLLECTOR
 * Coletor especializado em análise de memória sequencial
 * Portal Betina V3 - MemoryGame
 */

export class SequentialMemoryCollector {
  constructor() {
    this.collectorType = 'SequentialMemory';
    this.version = '3.0.0';
    this.capabilities = {
      sequenceTracking: true,
      orderAnalysis: true,
      temporalMemory: true,
      serialRecall: true,
      workingMemory: true
    };
  }

  /**
   * Analisa dados de memória sequencial
   * @param {Object} gameData - Dados da sessão do MemoryGame
   * @returns {Promise<Object>} Dados de memória sequencial analisados
   */
  async analyze(gameData) {
    try {
      console.log('🧠 SequentialMemoryCollector: Iniciando coleta...');

      if (!this.validateGameData(gameData)) {
        return this.generateFallbackData('Dados de jogo inválidos');
      }

      // Análises principais de memória sequencial
      const sequenceData = {
        // Capacidade de sequência
        sequenceSpan: this.analyzeSequenceSpan(gameData),
        orderAccuracy: this.analyzeOrderAccuracy(gameData),
        serialPosition: this.analyzeSerialPositionEffect(gameData),
        temporalOrder: this.analyzeTemporalOrder(gameData),
        
        // Memória de trabalho
        workingMemoryCapacity: this.analyzeWorkingMemoryCapacity(gameData),
        cognitiveLoad: this.analyzeCognitiveLoad(gameData),
        attentionalControl: this.analyzeAttentionalControl(gameData),
        
        // Padrões de erro
        sequenceErrors: this.analyzeSequenceErrors(gameData),
        orderConfusions: this.analyzeOrderConfusions(gameData),
        proactiveInterference: this.analyzeProactiveInterference(gameData),
        
        // Estratégias cognitivas
        encodingStrategies: this.analyzeEncodingStrategies(gameData),
        rehearsalPatterns: this.analyzeRehearsalPatterns(gameData),
        chunkingAbility: this.analyzeChunkingAbility(gameData),
        
        // Desenvolvimento temporal
        learningCurve: this.analyzeLearningCurve(gameData),
        forgettingPattern: this.analyzeForgettingPattern(gameData),
        retentionStability: this.analyzeRetentionStability(gameData),
        
        // Metadados
        collectionTimestamp: new Date().toISOString(),
        collectorVersion: this.version,
        dataQuality: this.assessDataQuality(gameData)
      };

      console.log('✅ SequentialMemoryCollector: Coleta concluída', {
        sequenceSpan: sequenceData.sequenceSpan,
        orderAccuracy: Math.round(sequenceData.orderAccuracy * 100),
        workingMemory: Math.round(sequenceData.workingMemoryCapacity * 100)
      });

      return sequenceData;

    } catch (error) {
      console.error('❌ SequentialMemoryCollector: Erro na coleta:', error);
      return this.generateFallbackData(error.message);
    }
  }

  /**
   * Valida dados de entrada
   */
  validateGameData(data) {
    if (!data) return false;
    return data.cards && data.flips && data.matches;
  }

  /**
   * Analisa amplitude de sequência
   */
  analyzeSequenceSpan(gameData) {
    const sequences = this.extractSequences(gameData);
    const maxSequence = Math.max(...sequences.map(seq => seq.length));
    const avgSequence = sequences.reduce((sum, seq) => sum + seq.length, 0) / sequences.length;
    
    return {
      maximum: maxSequence,
      average: avgSequence,
      normalized: Math.min(avgSequence / 7, 1) // Normalizado para span típico
    };
  }

  /**
   * Extrai sequências das jogadas
   */
  extractSequences(gameData) {
    const sequences = [];
    let currentSequence = [];
    
    gameData.flips.forEach((flip, index) => {
      currentSequence.push(flip);
      
      // Fim de sequência quando encontra par ou atinge limite
      if (this.isSequenceEnd(flip, gameData.flips[index + 1])) {
        sequences.push([...currentSequence]);
        currentSequence = [];
      }
    });
    
    if (currentSequence.length > 0) {
      sequences.push(currentSequence);
    }
    
    return sequences;
  }

  /**
   * Verifica fim de sequência
   */
  isSequenceEnd(currentFlip, nextFlip) {
    return !nextFlip || currentFlip.matched || currentFlip.timeGap > 3000;
  }

  /**
   * Analisa precisão de ordem
   */
  analyzeOrderAccuracy(gameData) {
    const sequences = this.extractSequences(gameData);
    let correctOrders = 0;
    let totalOrders = 0;
    
    sequences.forEach(sequence => {
      if (sequence.length >= 2) {
        for (let i = 1; i < sequence.length; i++) {
          totalOrders++;
          if (this.isCorrectOrder(sequence[i-1], sequence[i])) {
            correctOrders++;
          }
        }
      }
    });
    
    return totalOrders > 0 ? correctOrders / totalOrders : 0.5;
  }

  /**
   * Verifica se ordem está correta
   */
  isCorrectOrder(flip1, flip2) {
    // Estratégia ótima: tentar formar pares conhecidos
    if (flip1.cardId && flip2.cardId) {
      return flip1.pairId === flip2.pairId;
    }
    return Math.random() > 0.5; // Fallback probabilístico
  }

  /**
   * Analisa efeito de posição serial
   */
  analyzeSerialPositionEffect(gameData) {
    const sequences = this.extractSequences(gameData);
    const positionAccuracy = { beginning: [], middle: [], end: [] };
    
    sequences.forEach(sequence => {
      if (sequence.length >= 3) {
        sequence.forEach((flip, index) => {
          const position = this.getSerialPosition(index, sequence.length);
          const accuracy = flip.matched ? 1 : 0;
          positionAccuracy[position].push(accuracy);
        });
      }
    });
    
    return {
      primacy: this.calculateAverage(positionAccuracy.beginning),
      middle: this.calculateAverage(positionAccuracy.middle),
      recency: this.calculateAverage(positionAccuracy.end),
      curve: this.generateSerialPositionCurve(positionAccuracy)
    };
  }

  /**
   * Determina posição serial
   */
  getSerialPosition(index, length) {
    if (index < length * 0.3) return 'beginning';
    if (index > length * 0.7) return 'end';
    return 'middle';
  }

  /**
   * Gera curva de posição serial
   */
  generateSerialPositionCurve(positionData) {
    const beginning = this.calculateAverage(positionData.beginning);
    const middle = this.calculateAverage(positionData.middle);
    const end = this.calculateAverage(positionData.end);
    
    if (beginning > middle && end > middle) {
      return 'u_shaped'; // Típico
    } else if (beginning > middle && beginning > end) {
      return 'primacy_dominant';
    } else if (end > beginning && end > middle) {
      return 'recency_dominant';
    }
    return 'flat';
  }

  /**
   * Analisa ordem temporal
   */
  analyzeTemporalOrder(gameData) {
    const timeIntervals = [];
    
    for (let i = 1; i < gameData.flips.length; i++) {
      const interval = gameData.flips[i].timestamp - gameData.flips[i-1].timestamp;
      timeIntervals.push(interval);
    }
    
    return {
      averageInterval: this.calculateAverage(timeIntervals),
      intervalVariability: this.calculateVariability(timeIntervals),
      temporalPattern: this.identifyTemporalPattern(timeIntervals)
    };
  }

  /**
   * Identifica padrão temporal
   */
  identifyTemporalPattern(intervals) {
    const avgInterval = this.calculateAverage(intervals);
    const variability = this.calculateVariability(intervals);
    
    if (variability < 0.3) {
      return 'consistent_rhythm';
    } else if (variability > 0.7) {
      return 'highly_variable';
    } else if (this.hasIncreasingPattern(intervals)) {
      return 'slowing_down';
    } else if (this.hasDecreasingPattern(intervals)) {
      return 'speeding_up';
    }
    return 'moderately_variable';
  }

  /**
   * Verifica padrão crescente
   */
  hasIncreasingPattern(intervals) {
    let increases = 0;
    for (let i = 1; i < intervals.length; i++) {
      if (intervals[i] > intervals[i-1]) increases++;
    }
    return increases > intervals.length * 0.6;
  }

  /**
   * Verifica padrão decrescente
   */
  hasDecreasingPattern(intervals) {
    let decreases = 0;
    for (let i = 1; i < intervals.length; i++) {
      if (intervals[i] < intervals[i-1]) decreases++;
    }
    return decreases > intervals.length * 0.6;
  }

  /**
   * Analisa capacidade de memória de trabalho
   */
  analyzeWorkingMemoryCapacity(gameData) {
    const simultaneousCards = this.getMaxSimultaneousCards(gameData);
    const complexityHandling = this.analyzeComplexityHandling(gameData);
    
    return {
      capacity: Math.min(simultaneousCards / 7, 1), // Normalizado
      efficiency: complexityHandling,
      combined: (simultaneousCards / 7 * 0.6) + (complexityHandling * 0.4)
    };
  }

  /**
   * Obtém máximo de cartas simultâneas lembradas
   */
  getMaxSimultaneousCards(gameData) {
    let maxRemembered = 0;
    let currentRemembered = new Set();
    
    gameData.flips.forEach(flip => {
      if (flip.matched) {
        currentRemembered.add(flip.cardId);
        maxRemembered = Math.max(maxRemembered, currentRemembered.size);
      } else if (flip.wasRevealed) {
        // Carta foi revelada mas não combinada - teste de memória
        currentRemembered.add(flip.cardId);
      }
    });
    
    return maxRemembered;
  }

  /**
   * Analisa manipulação de complexidade
   */
  analyzeComplexityHandling(gameData) {
    const totalCards = gameData.cards.length;
    const completionTime = gameData.duration || 0;
    const errors = gameData.flips.filter(flip => !flip.matched).length;
    
    // Eficiência baseada em tempo e erros vs complexidade
    const timeEfficiency = totalCards > 0 ? Math.max(0, 1 - (completionTime / (totalCards * 2000))) : 0;
    const errorEfficiency = gameData.flips.length > 0 ? 1 - (errors / gameData.flips.length) : 0;
    
    return (timeEfficiency * 0.4) + (errorEfficiency * 0.6);
  }

  /**
   * Analisa carga cognitiva
   */
  analyzeCognitiveLoad(gameData) {
    const difficulty = gameData.difficulty || 'medium';
    const performanceDecline = this.calculatePerformanceDecline(gameData);
    
    const loadFactors = {
      easy: 0.4,
      medium: 0.6,
      hard: 0.8
    };
    
    const baseLoad = loadFactors[difficulty] || 0.6;
    const adjustedLoad = baseLoad + (performanceDecline * 0.3);
    
    return Math.min(adjustedLoad, 1);
  }

  /**
   * Calcula declínio de performance
   */
  calculatePerformanceDecline(gameData) {
    if (!gameData.flips || gameData.flips.length < 4) return 0;
    
    const firstHalf = gameData.flips.slice(0, Math.floor(gameData.flips.length / 2));
    const secondHalf = gameData.flips.slice(Math.floor(gameData.flips.length / 2));
    
    const firstHalfAccuracy = firstHalf.filter(flip => flip.matched).length / firstHalf.length;
    const secondHalfAccuracy = secondHalf.filter(flip => flip.matched).length / secondHalf.length;
    
    return Math.max(0, firstHalfAccuracy - secondHalfAccuracy);
  }

  /**
   * Analisa controle atencional
   */
  analyzeAttentionalControl(gameData) {
    const distractedFlips = gameData.flips.filter(flip => flip.hasDistraction || flip.longPause);
    const totalFlips = gameData.flips.length;
    
    const controlScore = totalFlips > 0 ? 1 - (distractedFlips.length / totalFlips) : 0.5;
    
    return {
      score: controlScore,
      distractibility: distractedFlips.length / totalFlips,
      focus_stability: this.calculateFocusStability(gameData)
    };
  }

  /**
   * Calcula estabilidade de foco
   */
  calculateFocusStability(gameData) {
    const intervals = [];
    for (let i = 1; i < gameData.flips.length; i++) {
      intervals.push(gameData.flips[i].timestamp - gameData.flips[i-1].timestamp);
    }
    
    const variability = this.calculateVariability(intervals);
    return Math.max(0, 1 - variability);
  }

  /**
   * Analisa erros de sequência
   */
  analyzeSequenceErrors(gameData) {
    const errors = gameData.flips.filter(flip => !flip.matched);
    const errorTypes = {
      position_errors: 0,
      order_errors: 0,
      memory_lapses: 0,
      interference_errors: 0
    };
    
    errors.forEach(error => {
      const errorType = this.classifySequenceError(error, gameData);
      errorTypes[errorType]++;
    });
    
    return errorTypes;
  }

  /**
   * Classifica tipo de erro de sequência
   */
  classifySequenceError(error, gameData) {
    if (error.wasRecentlyViewed) return 'memory_lapses';
    if (error.confusedWithSimilar) return 'interference_errors';
    if (error.wrongPosition) return 'position_errors';
    return 'order_errors';
  }

  /**
   * Analisa confusões de ordem
   */
  analyzeOrderConfusions(gameData) {
    const confusions = [];
    
    gameData.flips.forEach((flip, index) => {
      if (!flip.matched && flip.targetCard) {
        const confusionType = this.identifyOrderConfusion(flip, index, gameData);
        if (confusionType) confusions.push(confusionType);
      }
    });
    
    return {
      total: confusions.length,
      types: this.countConfusionTypes(confusions),
      pattern: this.identifyConfusionPattern(confusions)
    };
  }

  /**
   * Identifica confusão de ordem
   */
  identifyOrderConfusion(flip, index, gameData) {
    // Implementação simplificada
    if (index > 0 && gameData.flips[index-1].cardId === flip.targetCard) {
      return 'adjacent_confusion';
    }
    if (flip.distanceFromTarget === 1) {
      return 'position_confusion';
    }
    return null;
  }

  /**
   * Conta tipos de confusão
   */
  countConfusionTypes(confusions) {
    const counts = {};
    confusions.forEach(type => {
      counts[type] = (counts[type] || 0) + 1;
    });
    return counts;
  }

  /**
   * Identifica padrão de confusão
   */
  identifyConfusionPattern(confusions) {
    if (confusions.length === 0) return 'no_confusions';
    
    const adjacentCount = confusions.filter(c => c === 'adjacent_confusion').length;
    const positionCount = confusions.filter(c => c === 'position_confusion').length;
    
    if (adjacentCount > positionCount) return 'serial_order_difficulty';
    if (positionCount > adjacentCount) return 'spatial_position_difficulty';
    return 'mixed_confusion_pattern';
  }

  /**
   * Analisa interferência proativa
   */
  analyzeProactiveInterference(gameData) {
    const sessions = gameData.previousSessions || [];
    if (sessions.length === 0) return { level: 0, impact: 'none' };
    
    const currentPerformance = this.calculateSessionPerformance(gameData);
    const previousPerformance = this.calculateAveragePerformance(sessions);
    
    const interferenceLevel = Math.max(0, previousPerformance - currentPerformance);
    
    return {
      level: interferenceLevel,
      impact: this.classifyInterferenceImpact(interferenceLevel),
      adaptation: this.assessInterferenceAdaptation(gameData)
    };
  }

  /**
   * Calcula performance da sessão
   */
  calculateSessionPerformance(gameData) {
    const accuracy = gameData.matches.length / (gameData.cards.length / 2);
    const efficiency = gameData.duration > 0 ? 1 / (gameData.duration / 1000) : 0;
    return (accuracy * 0.7) + (efficiency * 0.3);
  }

  /**
   * Calcula performance média
   */
  calculateAveragePerformance(sessions) {
    if (sessions.length === 0) return 0;
    const total = sessions.reduce((sum, session) => sum + this.calculateSessionPerformance(session), 0);
    return total / sessions.length;
  }

  /**
   * Classifica impacto da interferência
   */
  classifyInterferenceImpact(level) {
    if (level < 0.1) return 'minimal';
    if (level < 0.3) return 'moderate';
    if (level < 0.5) return 'significant';
    return 'severe';
  }

  /**
   * Avalia adaptação à interferência
   */
  assessInterferenceAdaptation(gameData) {
    const performance = [];
    const chunkSize = Math.floor(gameData.flips.length / 4);
    
    for (let i = 0; i < 4; i++) {
      const chunk = gameData.flips.slice(i * chunkSize, (i + 1) * chunkSize);
      const chunkAccuracy = chunk.filter(flip => flip.matched).length / chunk.length;
      performance.push(chunkAccuracy);
    }
    
    // Verifica se há melhoria ao longo do tempo
    const firstHalf = (performance[0] + performance[1]) / 2;
    const secondHalf = (performance[2] + performance[3]) / 2;
    
    return secondHalf > firstHalf ? 'adaptive' : 'non_adaptive';
  }

  /**
   * Analisa estratégias de codificação
   */
  analyzeEncodingStrategies(gameData) {
    const strategies = {
      visual: this.detectVisualEncoding(gameData),
      verbal: this.detectVerbalEncoding(gameData),
      spatial: this.detectSpatialEncoding(gameData),
      semantic: this.detectSemanticEncoding(gameData)
    };
    
    const dominant = this.identifyDominantStrategy(strategies);
    
    return {
      strategies,
      dominant,
      efficiency: strategies[dominant],
      flexibility: this.assessStrategyFlexibility(strategies)
    };
  }

  /**
   * Detecta codificação visual
   */
  detectVisualEncoding(gameData) {
    const visualMatches = gameData.matches.filter(match => 
      match.basedon === 'visual_similarity' || match.category === 'visual'
    );
    return visualMatches.length / gameData.matches.length;
  }

  /**
   * Detecta codificação verbal
   */
  detectVerbalEncoding(gameData) {
    const verbalMatches = gameData.matches.filter(match => 
      match.basedOn === 'name_similarity' || match.strategy === 'verbal'
    );
    return verbalMatches.length / gameData.matches.length;
  }

  /**
   * Detecta codificação espacial
   */
  detectSpatialEncoding(gameData) {
    const spatialMatches = gameData.matches.filter(match => 
      match.basedOn === 'position_memory' || match.strategy === 'spatial'
    );
    return spatialMatches.length / gameData.matches.length;
  }

  /**
   * Detecta codificação semântica
   */
  detectSemanticEncoding(gameData) {
    const semanticMatches = gameData.matches.filter(match => 
      match.basedOn === 'category_similarity' || match.strategy === 'semantic'
    );
    return semanticMatches.length / gameData.matches.length;
  }

  /**
   * Identifica estratégia dominante
   */
  identifyDominantStrategy(strategies) {
    let maxStrategy = 'visual';
    let maxValue = strategies.visual;
    
    Object.entries(strategies).forEach(([strategy, value]) => {
      if (value > maxValue) {
        maxValue = value;
        maxStrategy = strategy;
      }
    });
    
    return maxStrategy;
  }

  /**
   * Avalia flexibilidade de estratégia
   */
  assessStrategyFlexibility(strategies) {
    const values = Object.values(strategies);
    const max = Math.max(...values);
    const min = Math.min(...values);
    
    // Flexibilidade alta quando estratégias são equilibradas
    return 1 - (max - min);
  }

  /**
   * Analisa padrões de ensaio
   */
  analyzeRehearsalPatterns(gameData) {
    const rehearsals = this.detectRehearsalBehavior(gameData);
    
    return {
      frequency: rehearsals.length / gameData.flips.length,
      effectiveness: this.calculateRehearsalEffectiveness(rehearsals, gameData),
      strategy: this.classifyRehearsalStrategy(rehearsals)
    };
  }

  /**
   * Detecta comportamento de ensaio
   */
  detectRehearsalBehavior(gameData) {
    const rehearsals = [];
    
    gameData.flips.forEach((flip, index) => {
      if (flip.revisitedCard && flip.timeSpent > 1000) {
        rehearsals.push({
          cardId: flip.cardId,
          timeSpent: flip.timeSpent,
          position: index
        });
      }
    });
    
    return rehearsals;
  }

  /**
   * Calcula efetividade do ensaio
   */
  calculateRehearsalEffectiveness(rehearsals, gameData) {
    if (rehearsals.length === 0) return 0;
    
    let successfulRehearsals = 0;
    
    rehearsals.forEach(rehearsal => {
      const laterMatch = gameData.matches.find(match => 
        match.cards.includes(rehearsal.cardId) && 
        match.timestamp > rehearsal.timestamp
      );
      if (laterMatch) successfulRehearsals++;
    });
    
    return successfulRehearsals / rehearsals.length;
  }

  /**
   * Classifica estratégia de ensaio
   */
  classifyRehearsalStrategy(rehearsals) {
    if (rehearsals.length === 0) return 'no_rehearsal';
    
    const avgTimeSpent = rehearsals.reduce((sum, r) => sum + r.timeSpent, 0) / rehearsals.length;
    
    if (avgTimeSpent > 3000) return 'deep_rehearsal';
    if (avgTimeSpent > 1500) return 'moderate_rehearsal';
    return 'quick_rehearsal';
  }

  /**
   * Analisa habilidade de agrupamento
   */
  analyzeChunkingAbility(gameData) {
    const chunks = this.identifyChunks(gameData);
    
    return {
      chunkCount: chunks.length,
      averageChunkSize: this.calculateAverageChunkSize(chunks),
      chunkingEfficiency: this.calculateChunkingEfficiency(chunks, gameData),
      strategy: this.identifyChunkingStrategy(chunks)
    };
  }

  /**
   * Identifica chunks
   */
  identifyChunks(gameData) {
    const chunks = [];
    let currentChunk = [];
    
    gameData.flips.forEach((flip, index) => {
      currentChunk.push(flip);
      
      // Fim de chunk por pausa longa ou mudança de estratégia
      if (this.isChunkBoundary(flip, gameData.flips[index + 1])) {
        chunks.push([...currentChunk]);
        currentChunk = [];
      }
    });
    
    if (currentChunk.length > 0) {
      chunks.push(currentChunk);
    }
    
    return chunks;
  }

  /**
   * Verifica limite de chunk
   */
  isChunkBoundary(currentFlip, nextFlip) {
    if (!nextFlip) return true;
    
    const timeDiff = nextFlip.timestamp - currentFlip.timestamp;
    return timeDiff > 3000 || currentFlip.strategyChange;
  }

  /**
   * Calcula tamanho médio de chunk
   */
  calculateAverageChunkSize(chunks) {
    if (chunks.length === 0) return 0;
    const totalSize = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    return totalSize / chunks.length;
  }

  /**
   * Calcula eficiência de agrupamento
   */
  calculateChunkingEfficiency(chunks, gameData) {
    if (chunks.length === 0) return 0;
    
    let successfulChunks = 0;
    
    chunks.forEach(chunk => {
      const chunkSuccess = this.evaluateChunkSuccess(chunk, gameData);
      if (chunkSuccess) successfulChunks++;
    });
    
    return successfulChunks / chunks.length;
  }

  /**
   * Avalia sucesso do chunk
   */
  evaluateChunkSuccess(chunk, gameData) {
    const chunkMatches = chunk.filter(flip => flip.matched).length;
    return chunkMatches / chunk.length > 0.5;
  }

  /**
   * Identifica estratégia de agrupamento
   */
  identifyChunkingStrategy(chunks) {
    const avgSize = this.calculateAverageChunkSize(chunks);
    
    if (avgSize < 2) return 'no_chunking';
    if (avgSize < 4) return 'small_chunks';
    if (avgSize < 7) return 'optimal_chunks';
    return 'large_chunks';
  }

  /**
   * Analisa curva de aprendizagem
   */
  analyzeLearningCurve(gameData) {
    const sessions = [gameData, ...(gameData.previousSessions || [])];
    const performances = sessions.map(session => this.calculateSessionPerformance(session));
    
    return {
      trend: this.identifyLearningTrend(performances),
      rate: this.calculateLearningRate(performances),
      plateau: this.detectPlateau(performances),
      projection: this.projectFuturePerformance(performances)
    };
  }

  /**
   * Identifica tendência de aprendizagem
   */
  identifyLearningTrend(performances) {
    if (performances.length < 2) return 'insufficient_data';
    
    const first = performances[0];
    const last = performances[performances.length - 1];
    const improvement = last - first;
    
    if (improvement > 0.1) return 'improving';
    if (improvement < -0.1) return 'declining';
    return 'stable';
  }

  /**
   * Calcula taxa de aprendizagem
   */
  calculateLearningRate(performances) {
    if (performances.length < 2) return 0;
    
    let totalChange = 0;
    for (let i = 1; i < performances.length; i++) {
      totalChange += performances[i] - performances[i-1];
    }
    
    return totalChange / (performances.length - 1);
  }

  /**
   * Detecta platô
   */
  detectPlateau(performances) {
    if (performances.length < 3) return false;
    
    const recent = performances.slice(-3);
    const variance = this.calculateVariance(recent);
    
    return variance < 0.05; // Baixa variabilidade indica platô
  }

  /**
   * Projeta performance futura
   */
  projectFuturePerformance(performances) {
    if (performances.length < 2) return 'unknown';
    
    const trend = this.identifyLearningTrend(performances);
    const rate = this.calculateLearningRate(performances);
    
    if (trend === 'improving' && rate > 0.05) return 'continued_improvement';
    if (trend === 'declining') return 'needs_intervention';
    if (this.detectPlateau(performances)) return 'plateau_reached';
    return 'gradual_improvement';
  }

  /**
   * Analisa padrão de esquecimento
   */
  analyzeForgettingPattern(gameData) {
    const retentionTests = this.extractRetentionTests(gameData);
    
    return {
      forgettingRate: this.calculateForgettingRate(retentionTests),
      curve: this.identifyForgettingCurve(retentionTests),
      consolidation: this.assessMemoryConsolidation(retentionTests)
    };
  }

  /**
   * Extrai testes de retenção
   */
  extractRetentionTests(gameData) {
    const tests = [];
    
    gameData.flips.forEach(flip => {
      if (flip.retentionTest) {
        tests.push({
          delay: flip.retentionDelay,
          success: flip.matched,
          cardType: flip.cardType
        });
      }
    });
    
    return tests;
  }

  /**
   * Calcula taxa de esquecimento
   */
  calculateForgettingRate(retentionTests) {
    if (retentionTests.length === 0) return 0;
    
    const initialMemory = retentionTests.filter(test => test.delay < 10000);
    const delayedMemory = retentionTests.filter(test => test.delay >= 10000);
    
    const initialSuccess = initialMemory.filter(test => test.success).length / initialMemory.length;
    const delayedSuccess = delayedMemory.filter(test => test.success).length / delayedMemory.length;
    
    return Math.max(0, initialSuccess - delayedSuccess);
  }

  /**
   * Identifica curva de esquecimento
   */
  identifyForgettingCurve(retentionTests) {
    const forgettingRate = this.calculateForgettingRate(retentionTests);
    
    if (forgettingRate < 0.1) return 'minimal_forgetting';
    if (forgettingRate < 0.3) return 'gradual_forgetting';
    if (forgettingRate < 0.5) return 'rapid_forgetting';
    return 'severe_forgetting';
  }

  /**
   * Avalia consolidação da memória
   */
  assessMemoryConsolidation(retentionTests) {
    const longTermTests = retentionTests.filter(test => test.delay > 30000);
    if (longTermTests.length === 0) return 'unknown';
    
    const longTermSuccess = longTermTests.filter(test => test.success).length / longTermTests.length;
    
    if (longTermSuccess > 0.8) return 'excellent';
    if (longTermSuccess > 0.6) return 'good';
    if (longTermSuccess > 0.4) return 'fair';
    return 'poor';
  }

  /**
   * Analisa estabilidade de retenção
   */
  analyzeRetentionStability(gameData) {
    const sessions = [gameData, ...(gameData.previousSessions || [])];
    const retentionScores = sessions.map(session => this.calculateRetentionScore(session));
    
    return {
      stability: this.calculateRetentionStability(retentionScores),
      consistency: this.assessRetentionConsistency(retentionScores),
      trajectory: this.identifyRetentionTrajectory(retentionScores)
    };
  }

  /**
   * Calcula score de retenção
   */
  calculateRetentionScore(gameData) {
    const totalCards = gameData.cards.length;
    const rememberedCards = gameData.matches.length * 2;
    return rememberedCards / totalCards;
  }

  /**
   * Calcula estabilidade de retenção
   */
  calculateRetentionStability(scores) {
    if (scores.length < 2) return 0.5;
    
    const variance = this.calculateVariance(scores);
    return Math.max(0, 1 - variance);
  }

  /**
   * Avalia consistência de retenção
   */
  assessRetentionConsistency(scores) {
    if (scores.length < 3) return 'insufficient_data';
    
    const variability = this.calculateVariability(scores);
    
    if (variability < 0.2) return 'highly_consistent';
    if (variability < 0.4) return 'moderately_consistent';
    if (variability < 0.6) return 'somewhat_inconsistent';
    return 'highly_inconsistent';
  }

  /**
   * Identifica trajetória de retenção
   */
  identifyRetentionTrajectory(scores) {
    if (scores.length < 2) return 'unknown';
    
    const trend = this.identifyLearningTrend(scores);
    const stability = this.calculateRetentionStability(scores);
    
    if (trend === 'improving' && stability > 0.7) return 'improving_stable';
    if (trend === 'declining' && stability < 0.5) return 'declining_unstable';
    if (stability > 0.8) return 'stable_retention';
    return 'variable_retention';
  }

  /**
   * Funções auxiliares
   */
  calculateAverage(array) {
    if (array.length === 0) return 0;
    return array.reduce((sum, val) => sum + val, 0) / array.length;
  }

  calculateVariance(array) {
    if (array.length === 0) return 0;
    const mean = this.calculateAverage(array);
    const squaredDiffs = array.map(val => Math.pow(val - mean, 2));
    return this.calculateAverage(squaredDiffs);
  }

  calculateVariability(array) {
    if (array.length === 0) return 0;
    const mean = this.calculateAverage(array);
    const variance = this.calculateVariance(array);
    return mean > 0 ? Math.sqrt(variance) / mean : 0;
  }

  /**
   * Avalia qualidade dos dados
   */
  assessDataQuality(gameData) {
    let score = 1.0;
    const issues = [];

    if (!gameData.flips || gameData.flips.length < 4) {
      score -= 0.3;
      issues.push('Poucos dados de flip');
    }

    if (!gameData.matches || gameData.matches.length === 0) {
      score -= 0.4;
      issues.push('Nenhum match registrado');
    }

    if (!gameData.duration || gameData.duration < 10000) {
      score -= 0.2;
      issues.push('Sessão muito curta');
    }

    return {
      score: Math.max(0, score),
      issues,
      level: score > 0.7 ? 'high' : score > 0.4 ? 'medium' : 'low'
    };
  }

  /**
   * Gera dados de fallback
   */
  generateFallbackData(reason) {
    return {
      sequenceSpan: { maximum: 3, average: 2.5, normalized: 0.36 },
      orderAccuracy: 0.5,
      serialPosition: { primacy: 0.5, middle: 0.4, recency: 0.6, curve: 'u_shaped' },
      temporalOrder: { averageInterval: 2000, intervalVariability: 0.5, temporalPattern: 'moderately_variable' },
      workingMemoryCapacity: { capacity: 0.5, efficiency: 0.5, combined: 0.5 },
      cognitiveLoad: 0.6,
      attentionalControl: { score: 0.5, distractibility: 0.3, focus_stability: 0.6 },
      sequenceErrors: { position_errors: 1, order_errors: 1, memory_lapses: 1, interference_errors: 0 },
      orderConfusions: { total: 2, types: {}, pattern: 'mixed_confusion_pattern' },
      proactiveInterference: { level: 0.2, impact: 'minimal', adaptation: 'unknown' },
      encodingStrategies: { 
        strategies: { visual: 0.6, verbal: 0.3, spatial: 0.5, semantic: 0.4 },
        dominant: 'visual',
        efficiency: 0.6,
        flexibility: 0.4
      },
      rehearsalPatterns: { frequency: 0.3, effectiveness: 0.5, strategy: 'moderate_rehearsal' },
      chunkingAbility: { chunkCount: 3, averageChunkSize: 2.5, chunkingEfficiency: 0.5, strategy: 'small_chunks' },
      learningCurve: { trend: 'stable', rate: 0.02, plateau: false, projection: 'gradual_improvement' },
      forgettingPattern: { forgettingRate: 0.3, curve: 'gradual_forgetting', consolidation: 'fair' },
      retentionStability: { stability: 0.6, consistency: 'moderately_consistent', trajectory: 'stable_retention' },
      collectionTimestamp: new Date().toISOString(),
      collectorVersion: this.version,
      dataQuality: { score: 0, issues: [reason], level: 'error' },
      status: 'fallback',
      error: reason
    };
  }
}
