// Importar o hub de coletores avançados
import { MusicalSequenceCollectorsHub } from './collectors/index.js';

// Instância global do hub de coletores
let collectorsHub = null;

export const MusicalSequenceMetrics = {
  // Registrar início do jogo
  startGame: (difficulty) => {
    const metrics = {
      gameId: 'musical-sequence',
      sessionId: `music_${Date.now()}`,
      startTime: new Date().toISOString(),
      difficulty,
      userId: 'anonymous', // Será substituído por ID real
      device: navigator.userAgent
    }

    console.log('Musical Sequence Game Started:', metrics)
    return metrics
  },

  // Registrar tentativa de sequência
  recordSequenceAttempt: (attempt) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      sequenceNumber: attempt.sequenceNumber,
      sequenceLength: attempt.sequenceLength,
      targetSequence: attempt.targetSequence.map(note => note.id),
      playerSequence: attempt.playerSequence.map(note => note.id),
      isCorrect: attempt.isCorrect,
      responseTime: attempt.responseTime,
      difficulty: attempt.difficulty,
      level: attempt.level,
      errors: attempt.errors || 0,
      partialCorrect: attempt.partialCorrect, // quantas notas estavam corretas antes do erro
      notesMatched: attempt.notesMatched
    }

    console.log('Musical Sequence Attempt:', metrics)
    return metrics
  },

  // Registrar reprodução de nota individual
  recordNotePlay: (noteData) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'note_played',
      noteId: noteData.noteId,
      noteName: noteData.noteName,
      frequency: noteData.frequency,
      context: noteData.context, // 'sequence_play' ou 'user_input'
      sequencePosition: noteData.sequencePosition,
      isCorrectPosition: noteData.isCorrectPosition
    }

    console.log('Musical Note Played:', metrics)
    return metrics
  },

  // Registrar repetição de sequência
  recordSequenceRepeat: () => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'sequence_repeated'
    }

    console.log('Musical Sequence Repeated:', metrics)
    return metrics
  },

  // Registrar final do jogo
  endGame: (gameData) => {
    const metrics = {
      sessionId: gameData.sessionId,
      endTime: new Date().toISOString(),
      totalTime: gameData.totalTime,
      totalSequences: gameData.totalSequences,
      correctSequences: gameData.correctSequences,
      totalScore: gameData.totalScore,
      finalLevel: gameData.finalLevel,
      accuracy: gameData.accuracy,
      averageSequenceLength: gameData.averageSequenceLength,
      difficulty: gameData.difficulty,
      sequenceRepeats: gameData.sequenceRepeats || 0,
      completed: gameData.completed
    }

    console.log('Musical Sequence Game Ended:', metrics)
    return metrics
  },

  // Calcular estatísticas do jogo
  calculateStats: (attempts) => {
    if (!attempts || attempts.length === 0) {
      return {
        accuracy: 0,
        averageSequenceLength: 0,
        totalAttempts: 0,
        correctSequences: 0
      }
    }

    const correctAttempts = attempts.filter(attempt => attempt.isCorrect)
    const totalSequenceLength = attempts.reduce((sum, attempt) => sum + attempt.sequenceLength, 0)

    return {
      accuracy: (correctAttempts.length / attempts.length) * 100,
      averageSequenceLength: totalSequenceLength / attempts.length,
      totalAttempts: attempts.length,
      correctSequences: correctAttempts.length,
      longestSequence: Math.max(...attempts.map(a => a.sequenceLength)),
      improvementRate: calculateImprovementRate(attempts),
      noteAccuracy: calculateNoteAccuracy(attempts),
      memorySpan: calculateMemorySpan(attempts)
    }
  },

  // Analisar padrões de aprendizado musical
  analyzeMusicPatterns: (attempts, noteData) => {
    const errorsByPosition = attempts
      .filter(attempt => !attempt.isCorrect)
      .reduce((acc, error) => {
        const errorPosition = error.partialCorrect || 0
        acc[errorPosition] = (acc[errorPosition] || 0) + 1
        return acc
      }, {})

    const notePreferences = noteData.reduce((acc, note) => {
      acc[note.noteId] = (acc[note.noteId] || 0) + 1
      return acc
    }, {})

    const difficultyProgression = attempts.reduce((acc, attempt) => {
      acc[attempt.difficulty] = (acc[attempt.difficulty] || 0) + 1
      return acc
    }, {})

    return {
      errorsByPosition,
      notePreferences,
      difficultyProgression,
      suggestions: generateMusicSuggestions(attempts, errorsByPosition)
    }
  },

  // Inicializar coletores avançados
  initializeAdvancedCollectors: (config = {}) => {
    try {
      collectorsHub = new MusicalSequenceCollectorsHub();
      
      const sessionConfig = {
        sessionId: `musical_${Date.now()}`,
        gameConfig: config.gameConfig || {},
        difficulty: config.difficulty || 'medium',
        playerProfile: config.playerProfile || {},
        enabledCollectors: config.enabledCollectors || ['memory', 'pattern', 'execution', 'learning'],
        ...config
      };

      const result = collectorsHub.startSession(sessionConfig);
      
      console.log('🎵 MusicalSequence - Coletores avançados inicializados:', result);
      return result;
    } catch (error) {
      console.error('Erro ao inicializar coletores avançados:', error);
      return { success: false, error: error.message };
    }
  },

  // Obter instância do hub de coletores
  getCollectorsHub: () => {
    return collectorsHub;
  },

  // Processar interação com coleta avançada
  recordAdvancedInteraction: (interactionData) => {
    try {
      if (!collectorsHub) {
        console.warn('Coletores avançados não inicializados');
        return null;
      }

      // Enriquece dados de interação com contexto do jogo
      const enrichedData = {
        ...interactionData,
        timestamp: Date.now(),
        gameContext: 'musical_sequence'
      };

      const result = collectorsHub.processInteraction(enrichedData);
      
      if (result && collectorsHub.debugMode) {
        console.log('🎵 Interação avançada processada:', {
          sessionId: result.sessionId,
          collectors: Object.keys(result.collectionResults || {}),
          insights: Object.keys(result.integratedResults || {})
        });
      }

      return result;
    } catch (error) {
      console.error('Erro no processamento de interação avançada:', error);
      return null;
    }
  },

  // Método híbrido que combina métricas básicas com coleta avançada
  recordSequenceAttemptAdvanced: (attempt) => {
    try {
      // Métricas básicas (existentes)
      const basicMetrics = {
        timestamp: new Date().toISOString(),
        sequenceNumber: attempt.sequenceNumber,
        sequenceLength: attempt.sequenceLength,
        targetSequence: Array.isArray(attempt.targetSequence) ? 
          attempt.targetSequence.map(note => typeof note === 'object' ? note.id : note) : 
          attempt.targetSequence,
        playerSequence: Array.isArray(attempt.playerSequence) ? 
          attempt.playerSequence.map(note => typeof note === 'object' ? note.id : note) : 
          attempt.playerSequence,
        isCorrect: attempt.isCorrect,
        responseTime: attempt.responseTime,
        difficulty: attempt.difficulty,
        level: attempt.level,
        errors: attempt.errors || 0,
        partialCorrect: attempt.partialCorrect,
        notesMatched: attempt.notesMatched
      };

      // Coleta avançada (se disponível)
      let advancedMetrics = null;
      if (collectorsHub) {
        advancedMetrics = MusicalSequenceMetrics.recordAdvancedInteraction({
          sequence: basicMetrics.targetSequence,
          playerResponse: basicMetrics.playerSequence,
          sequenceLength: basicMetrics.sequenceLength,
          isCorrect: basicMetrics.isCorrect,
          responseTime: basicMetrics.responseTime,
          difficulty: basicMetrics.difficulty,
          currentLevel: basicMetrics.level,
          accuracy: basicMetrics.isCorrect ? 1 : 0,
          partialCorrect: basicMetrics.partialCorrect,
          errorsCommitted: basicMetrics.errors,
          attemptNumber: basicMetrics.sequenceNumber,
          instruments: attempt.instruments || [],
          timings: attempt.timings || [],
          expectedTimings: attempt.expectedTimings || [],
          executionTimes: attempt.executionTimes || [],
          interClickIntervals: attempt.interClickIntervals || [],
          // Dados adicionais para análise avançada
          maxCorrectSequence: attempt.maxCorrectSequence,
          consistentSpan: attempt.consistentSpan,
          improvementRate: attempt.improvementRate,
          timeBetweenNotes: attempt.timeBetweenNotes,
          tempo: attempt.tempo || 120,
          sequenceType: attempt.sequenceType || 'random',
          clickPrecision: attempt.clickPrecision || [],
          pressureDynamics: attempt.pressureDynamics || [],
          repeatRequests: attempt.repeatRequests || 0,
          pauseLocations: attempt.pauseLocations || [],
          chunkingPatterns: attempt.chunkingPatterns || []
        });
      }

      console.log('🎵 Musical Sequence Attempt (Enhanced):', basicMetrics);
      
      return {
        basic: basicMetrics,
        advanced: advancedMetrics,
        timestamp: basicMetrics.timestamp
      };
    } catch (error) {
      console.error('Erro no registro de tentativa avançada:', error);
      
      // Fallback para métricas básicas
      const fallbackMetrics = {
        timestamp: new Date().toISOString(),
        sequenceNumber: attempt.sequenceNumber,
        sequenceLength: attempt.sequenceLength,
        targetSequence: attempt.targetSequence?.map?.(note => note.id) || attempt.targetSequence,
        playerSequence: attempt.playerSequence?.map?.(note => note.id) || attempt.playerSequence,
        isCorrect: attempt.isCorrect,
        responseTime: attempt.responseTime,
        difficulty: attempt.difficulty,
        level: attempt.level,
        errors: attempt.errors || 0,
        partialCorrect: attempt.partialCorrect,
        notesMatched: attempt.notesMatched,
        error: error.message
      };
      
      console.log('Musical Sequence Attempt (Fallback):', fallbackMetrics);
      return { basic: fallbackMetrics, advanced: null };
    }
  },

  // Gerar relatório completo com dados avançados
  generateComprehensiveReport: () => {
    try {
      let report = {
        timestamp: new Date().toISOString(),
        reportType: 'comprehensive',
        basicMetrics: {
          // Métricas básicas seriam coletadas aqui
          message: 'Métricas básicas disponíveis'
        }
      };

      // Adiciona dados avançados se disponíveis
      if (collectorsHub) {
        const advancedReport = collectorsHub.getComprehensiveReport();
        report.advancedMetrics = advancedReport;
        report.collectorsActive = collectorsHub.getStatus();
      } else {
        report.advancedMetrics = { message: 'Coletores avançados não disponíveis' };
      }

      console.log('🎵 Relatório comprehensivo gerado:', {
        hasBasicMetrics: !!report.basicMetrics,
        hasAdvancedMetrics: !!report.advancedMetrics?.session,
        collectorsStatus: report.collectorsActive?.isEnabled
      });

      return report;
    } catch (error) {
      console.error('Erro ao gerar relatório comprehensivo:', error);
      return {
        timestamp: new Date().toISOString(),
        error: error.message,
        fallback: true
      };
    }
  },

  // Obter recomendações personalizadas
  getPersonalizedRecommendations: () => {
    try {
      if (!collectorsHub) {
        return [
          'Continue praticando regularmente para melhorar sua memória musical',
          'Tente sequências de diferentes complexidades',
          'Use a função de repetir sequência quando necessário'
        ];
      }

      const report = collectorsHub.getComprehensiveReport();
      
      // Combina recomendações de diferentes coletores
      const recommendations = [];
      
      if (report.memory?.recommendations) {
        recommendations.push(...report.memory.recommendations);
      }
      
      if (report.patterns?.recommendations) {
        recommendations.push(...report.patterns.recommendations);
      }
      
      if (report.execution?.recommendations) {
        recommendations.push(...report.execution.recommendations);
      }
      
      if (report.learning?.recommendations) {
        recommendations.push(...report.learning.recommendations);
      }

      // Remove duplicatas e limita quantidade
      const uniqueRecommendations = [...new Set(recommendations)].slice(0, 5);
      
      return uniqueRecommendations.length > 0 ? uniqueRecommendations : [
        'Continue praticando para desenvolver suas habilidades musicais',
        'Explore diferentes níveis de dificuldade',
        'Mantenha consistência na prática'
      ];
    } catch (error) {
      console.error('Erro ao obter recomendações personalizadas:', error);
      return ['Continue praticando regularmente'];
    }
  },

  // Controlar coletores
  enableAdvancedCollector: (collectorName) => {
    if (collectorsHub) {
      return collectorsHub.enableCollector(collectorName);
    }
    return false;
  },

  disableAdvancedCollector: (collectorName) => {
    if (collectorsHub) {
      return collectorsHub.disableCollector(collectorName);
    }
    return false;
  },

  // Finalizar sessão com dados completos
  endGameAdvanced: (gameData) => {
    try {
      // Métricas básicas de fim de jogo
      const basicEndMetrics = {
        sessionId: gameData.sessionId,
        endTime: new Date().toISOString(),
        totalTime: gameData.totalTime,
        totalSequences: gameData.totalSequences,
        correctSequences: gameData.correctSequences,
        totalScore: gameData.totalScore,
        finalLevel: gameData.finalLevel,
        accuracy: gameData.accuracy,
        averageSequenceLength: gameData.averageSequenceLength,
        difficulty: gameData.difficulty,
        sequenceRepeats: gameData.sequenceRepeats || 0,
        completed: gameData.completed
      };

      // Finalizar sessão avançada se disponível
      let advancedEndMetrics = null;
      if (collectorsHub) {
        advancedEndMetrics = collectorsHub.endSession();
      }

      console.log('🎵 Musical Sequence Game Ended (Enhanced):', basicEndMetrics);

      return {
        basic: basicEndMetrics,
        advanced: advancedEndMetrics,
        summary: MusicalSequenceMetrics.generateGameSummary(basicEndMetrics, advancedEndMetrics)
      };
    } catch (error) {
      console.error('Erro ao finalizar jogo avançado:', error);
      
      // Fallback para métricas básicas
      const fallbackMetrics = {
        sessionId: gameData.sessionId,
        endTime: new Date().toISOString(),
        totalTime: gameData.totalTime,
        totalSequences: gameData.totalSequences,
        correctSequences: gameData.correctSequences,
        totalScore: gameData.totalScore,
        finalLevel: gameData.finalLevel,
        accuracy: gameData.accuracy,
        averageSequenceLength: gameData.averageSequenceLength,
        difficulty: gameData.difficulty,
        sequenceRepeats: gameData.sequenceRepeats || 0,
        completed: gameData.completed,
        error: error.message
      };
      
      console.log('Musical Sequence Game Ended (Fallback):', fallbackMetrics);
      return { basic: fallbackMetrics, advanced: null };
    }
  },

  // Gerar resumo do jogo
  generateGameSummary: (basicMetrics, advancedMetrics) => {
    try {
      const summary = {
        performance: {
          accuracy: basicMetrics.accuracy || 0,
          totalSequences: basicMetrics.totalSequences || 0,
          finalLevel: basicMetrics.finalLevel || 1,
          completionStatus: basicMetrics.completed ? 'completed' : 'partial'
        },
        timing: {
          totalTime: basicMetrics.totalTime || 0,
          averageSequenceTime: basicMetrics.totalTime && basicMetrics.totalSequences ? 
            basicMetrics.totalTime / basicMetrics.totalSequences : 0
        }
      };

      // Adiciona insights avançados se disponíveis
      if (advancedMetrics && advancedMetrics.summary) {
        summary.advanced = {
          keyInsights: advancedMetrics.summary.keyInsights || [],
          dataQuality: advancedMetrics.summary.dataQuality || 0,
          recommendations: advancedMetrics.summary.recommendations || []
        };
      }

      return summary;
    } catch (error) {
      console.error('Erro ao gerar resumo do jogo:', error);
      return {
        performance: { accuracy: 0, totalSequences: 0, finalLevel: 1 },
        timing: { totalTime: 0 },
        error: error.message
      };
    }
  },

  // Configurar modo debug
  setDebugMode: (enabled) => {
    if (collectorsHub) {
      collectorsHub.setDebugMode(enabled);
    }
    console.log(`🎵 MusicalSequenceMetrics - Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  },

  // Status dos coletores
  getCollectorsStatus: () => {
    if (collectorsHub) {
      return collectorsHub.getStatus();
    }
    return { 
      isEnabled: false, 
      message: 'Coletores avançados não inicializados' 
    };
  }
}

// Funções auxiliares para análise musical
function calculateImprovementRate (attempts) {
  if (attempts.length < 6) return 0

  const firstThird = attempts.slice(0, Math.floor(attempts.length / 3))
  const lastThird = attempts.slice(-Math.floor(attempts.length / 3))

  const firstAccuracy = firstThird.filter(a => a.isCorrect).length / firstThird.length
  const lastAccuracy = lastThird.filter(a => a.isCorrect).length / lastThird.length

  return ((lastAccuracy - firstAccuracy) / firstAccuracy) * 100
}

function calculateNoteAccuracy (attempts) {
  const noteStats = {}

  attempts.forEach(attempt => {
    attempt.targetSequence.forEach((noteId, index) => {
      if (!noteStats[noteId]) {
        noteStats[noteId] = { correct: 0, total: 0 }
      }

      noteStats[noteId].total++

      if (attempt.playerSequence[index] === noteId) {
        noteStats[noteId].correct++
      }
    })
  })

  Object.keys(noteStats).forEach(noteId => {
    noteStats[noteId].accuracy = (noteStats[noteId].correct / noteStats[noteId].total) * 100
  })

  return noteStats
}

function calculateMemorySpan (attempts) {
  const correctSequences = attempts.filter(a => a.isCorrect)
  if (correctSequences.length === 0) return 0

  // Maior sequência consecutiva correta
  let maxSpan = 0
  let currentSpan = 0

  attempts.forEach(attempt => {
    if (attempt.isCorrect) {
      currentSpan = Math.max(currentSpan, attempt.sequenceLength)
    } else {
      maxSpan = Math.max(maxSpan, currentSpan)
      currentSpan = 0
    }
  })

  return Math.max(maxSpan, currentSpan)
}

function generateMusicSuggestions (attempts, errorsByPosition) {
  const suggestions = []

  if (attempts.length === 0) {
    suggestions.push('Comece jogando para receber sugestões musicais!')
    return suggestions
  }

  const accuracy = attempts.filter(a => a.isCorrect).length / attempts.length

  if (accuracy < 0.4) {
    suggestions.push('Comece com sequências mais curtas e foque em ouvir cada nota')
    suggestions.push('Use a função "Repetir Sequência" sempre que precisar')
  } else if (accuracy < 0.7) {
    suggestions.push('Tente identificar padrões nas sequências musicais')
    suggestions.push('Pratique cantarolando as notas mentalmente')
  } else if (accuracy > 0.9) {
    suggestions.push('Excelente memória musical! Tente um nível mais difícil')
    suggestions.push('Você está pronto para sequências mais longas')
  }

  // Sugestões baseadas em posições de erro
  const mostErrorPosition = Object.entries(errorsByPosition)
    .sort(([, a], [, b]) => b - a)[0]

  if (mostErrorPosition) {
    const position = parseInt(mostErrorPosition[0])
    if (position === 0) {
      suggestions.push('Foque mais atenção na primeira nota da sequência')
    } else if (position >= 2) {
      suggestions.push('Sua memória de curto prazo está boa, pratique sequências mais longas')
    }
  }

  return suggestions.length > 0 ? suggestions : ['Continue praticando para desenvolver sua memória musical!']
}
