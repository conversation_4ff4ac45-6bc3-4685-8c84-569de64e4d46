/**
 * @file permission.js
 * @description Middleware de permissões para controle de acesso
 */

import { createError } from '../error/errorHandler.js';

/**
 * Middleware para verificar permissões específicas
 * @param {string|Array} requiredPermissions - Permissões necessárias
 * @returns {Function} Middleware function
 */
export function requirePermission(requiredPermissions) {
  return (req, res, next) => {
    try {
      const userPermissions = req.user?.permissions || [];
      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
      
      const hasPermission = permissions.some(permission => 
        userPermissions.includes(permission) || userPermissions.includes('admin')
      );
      
      if (!hasPermission) {
        throw createError(403, 'Permissão insuficiente');
      }
      
      next();
    } catch (error) {
      next(error);
    }
  };
}

/**
 * Middleware para verificar se é admin
 */
export function requireAdmin(req, res, next) {
  return requirePermission('admin')(req, res, next);
}

/**
 * Middleware para verificar se é terapeuta ou admin
 */
export function requireTherapist(req, res, next) {
  return requirePermission(['therapist', 'admin'])(req, res, next);
}

/**
 * Middleware para verificar se é pai/responsável ou admin
 */
export function requireParent(req, res, next) {
  return requirePermission(['parent', 'admin'])(req, res, next);
}

export default {
  requirePermission,
  requireAdmin,
  requireTherapist,
  requireParent
};
