/**
 * 🎯 ESTIMATION SKILLS COLLECTOR V3
 * Coletor especializado em análise de habilidades de estimativa numérica
 * Portal Betina V3
 */

export class EstimationSkillsCollector {
  constructor() {
    this.estimationThresholds = {
      excellent: 0.95,  // Estimativas quase perfeitas
      good: 0.85,       // Boa capacidade de estimativa
      average: 0.70,    // Estimativas razoáveis
      poor: 0.50,       // Dificuldade em estimar
      critical: 0.30    // Estimativas muito distantes
    };
    
    this.estimationSkills = {
      visualEstimation: 'Estimativa visual de quantidades',
      numericalApproximation: 'Aproximação numérica sem contagem',
      magnitudeComparison: 'Comparação de magnitudes',
      quantityPerception: 'Percepção de quantidade',
      spatialNumerosity: 'Numerosidade espacial'
    };
    
    this.toleranceByDifficulty = {
      easy: 1,    // ±1 para números de 1-5
      medium: 1,  // ±1 para números de 3-8
      hard: 2     // ±2 para números de 6-12
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  
  /**
   * Análise principal das habilidades de estimativa
   */
  async analyze(data) {
    if (!data || !data.numberEstimation) {
      console.warn('EstimationSkillsCollector: Dados de estimativa não encontrados');
      return this.getDefaultAnalysis();
    }

    const estimationData = data.numberEstimation;
    
    // Calcular precisão das estimativas
    const estimationAccuracy = this.calculateEstimationAccuracy(estimationData);
    
    // Analisar padrões de erro
    const errorPatterns = this.analyzeErrorPatterns(estimationData);
    
    // Avaliar progresso temporal
    const temporalProgress = this.analyzeTemporalProgress(estimationData);
    
    // Detectar estratégias de estimativa
    const estimationStrategies = this.detectEstimationStrategies(estimationData);
    
    // Avaliar confiança nas estimativas
    const confidenceLevel = this.assessConfidenceLevel(estimationData);
    
    // Calcular índice de habilidade geral
    const skillIndex = this.calculateSkillIndex(estimationData);

    const analysis = {
      timestamp: new Date().toISOString(),
      collector: 'EstimationSkillsCollector',
      version: '3.0.0',
      
      // Métricas principais
      estimationAccuracy,
      skillIndex,
      confidenceLevel,
      
      // Análises detalhadas
      errorPatterns,
      temporalProgress,
      estimationStrategies,
      
      // Habilidades específicas
      skillAssessment: {
        visualEstimation: this.assessVisualEstimation(estimationData),
        numericalApproximation: this.assessNumericalApproximation(estimationData),
        magnitudeComparison: this.assessMagnitudeComparison(estimationData),
        quantityPerception: this.assessQuantityPerception(estimationData),
        spatialNumerosity: this.assessSpatialNumerosity(estimationData)
      },
      
      // Recomendações
      recommendations: this.generateRecommendations(estimationData, skillIndex),
      
      // Metadados
      metadata: {
        totalAttempts: estimationData.length || 0,
        validAttempts: estimationData.filter(attempt => attempt.isValid).length || 0,
        averageError: this.calculateAverageError(estimationData),
        maxError: this.calculateMaxError(estimationData),
        minError: this.calculateMinError(estimationData)
      }
    };

    return analysis;
  }

  /**
   * Calcular precisão das estimativas
   */
  calculateEstimationAccuracy(data) {
    if (!data || data.length === 0) return 0.5;
    
    let correctEstimations = 0;
    
    data.forEach(attempt => {
      if (attempt.userEstimate && attempt.actualCount) {
        const error = Math.abs(attempt.userEstimate - attempt.actualCount);
        const tolerance = this.toleranceByDifficulty[attempt.difficulty] || 1;
        
        if (error <= tolerance) {
          correctEstimations++;
        }
      }
    });
    
    return data.length > 0 ? correctEstimations / data.length : 0.5;
  }

  /**
   * Analisar padrões de erro
   */
  analyzeErrorPatterns(data) {
    const errors = [];
    const biases = {
      overestimation: 0, // Tendência a superestimar
      underestimation: 0, // Tendência a subestimar
      systematic: false,  // Erro sistemático
      random: false       // Erro aleatório
    };

    data.forEach(attempt => {
      if (attempt.userEstimate && attempt.actualCount) {
        const error = attempt.userEstimate - attempt.actualCount;
        errors.push(error);
        
        if (error > 0) biases.overestimation++;
        else if (error < 0) biases.underestimation++;
      }
    });

    // Detectar viés sistemático
    const totalAttempts = data.length;
    if (totalAttempts > 0) {
      const overestimationRate = biases.overestimation / totalAttempts;
      const underestimationRate = biases.underestimation / totalAttempts;
      
      biases.systematic = Math.max(overestimationRate, underestimationRate) > 0.7;
      biases.random = Math.abs(overestimationRate - underestimationRate) < 0.2;
    }

    return {
      averageError: errors.length > 0 ? errors.reduce((a, b) => a + b, 0) / errors.length : 0,
      errorVariance: this.calculateVariance(errors),
      biases,
      errorDistribution: this.analyzeErrorDistribution(errors)
    };
  }

  /**
   * Avaliar progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (data.length < 3) return { trend: 'insufficient_data', improvement: 0 };
    
    const firstHalf = data.slice(0, Math.floor(data.length / 2));
    const secondHalf = data.slice(Math.floor(data.length / 2));
    
    const firstAccuracy = this.calculateEstimationAccuracy(firstHalf);
    const secondAccuracy = this.calculateEstimationAccuracy(secondHalf);
    
    const improvement = secondAccuracy - firstAccuracy;
    
    let trend = 'stable';
    if (improvement > 0.1) trend = 'improving';
    else if (improvement < -0.1) trend = 'declining';
    
    return {
      trend,
      improvement,
      firstHalfAccuracy: firstAccuracy,
      secondHalfAccuracy: secondAccuracy,
      consistencyScore: this.calculateConsistency(data)
    };
  }

  /**
   * Detectar estratégias de estimativa
   */
  detectEstimationStrategies(data) {
    const strategies = {
      visualChunking: false,    // Agrupamento visual
      roundNumbers: false,      // Uso de números redondos
      anchoring: false,         // Ancoragem em números conhecidos
      systematicCounting: false // Contagem parcial sistemática
    };

    // Detectar uso de números redondos
    const roundNumberUsage = data.filter(attempt => 
      attempt.userEstimate && attempt.userEstimate % 5 === 0
    ).length / data.length;
    
    strategies.roundNumbers = roundNumberUsage > 0.6;

    // Detectar ancoragem (estimativas sempre próximas de valores específicos)
    const estimates = data.map(attempt => attempt.userEstimate).filter(Boolean);
    const mostCommonEstimate = this.findMostCommon(estimates);
    const anchoringRate = estimates.filter(est => Math.abs(est - mostCommonEstimate) <= 2).length / estimates.length;
    
    strategies.anchoring = anchoringRate > 0.5;

    return strategies;
  }

  /**
   * Avaliar nível de confiança
   */
  assessConfidenceLevel(data) {
    // Baseado na consistência das estimativas e tempo de resposta
    const responseTimes = data.map(attempt => attempt.responseTime).filter(Boolean);
    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length || 0;
    
    const consistency = this.calculateConsistency(data);
    const timeConfidence = averageTime < 5000 ? 0.8 : averageTime < 8000 ? 0.6 : 0.4;
    
    return (consistency + timeConfidence) / 2;
  }

  /**
   * Calcular índice de habilidade geral
   */
  calculateSkillIndex(data) {
    const accuracy = this.calculateEstimationAccuracy(data);
    const consistency = this.calculateConsistency(data);
    const confidence = this.assessConfidenceLevel(data);
    
    return (accuracy * 0.5) + (consistency * 0.3) + (confidence * 0.2);
  }

  /**
   * Avaliar estimativa visual
   */
  assessVisualEstimation(data) {
    // Analisar performance baseada apenas na visualização
    const visualAttempts = data.filter(attempt => attempt.showTime <= 4000);
    return this.calculateEstimationAccuracy(visualAttempts);
  }

  /**
   * Avaliar aproximação numérica
   */
  assessNumericalApproximation(data) {
    // Avaliar precisão em aproximações sem contagem exata
    const approximationScore = data.reduce((score, attempt) => {
      if (attempt.userEstimate && attempt.actualCount) {
        const error = Math.abs(attempt.userEstimate - attempt.actualCount);
        const relativeError = error / attempt.actualCount;
        return score + Math.max(0, 1 - relativeError);
      }
      return score;
    }, 0);
    
    return data.length > 0 ? approximationScore / data.length : 0.5;
  }

  /**
   * Avaliar comparação de magnitudes
   */
  assessMagnitudeComparison(data) {
    // Avaliar habilidade de comparar diferentes magnitudes
    const magnitudes = data.map(attempt => attempt.actualCount).filter(Boolean);
    const uniqueMagnitudes = [...new Set(magnitudes)];
    
    if (uniqueMagnitudes.length < 2) return 0.5;
    
    // Performance varia com diferentes magnitudes?
    const performanceByMagnitude = {};
    uniqueMagnitudes.forEach(magnitude => {
      const attemptsForMagnitude = data.filter(attempt => attempt.actualCount === magnitude);
      performanceByMagnitude[magnitude] = this.calculateEstimationAccuracy(attemptsForMagnitude);
    });
    
    const performances = Object.values(performanceByMagnitude);
    const averagePerformance = performances.reduce((a, b) => a + b, 0) / performances.length;
    
    return averagePerformance;
  }

  /**
   * Avaliar percepção de quantidade
   */
  assessQuantityPerception(data) {
    // Avaliar precisão na percepção inicial de quantidades
    const quickResponses = data.filter(attempt => attempt.responseTime < 3000);
    return this.calculateEstimationAccuracy(quickResponses);
  }

  /**
   * Avaliar numerosidade espacial
   */
  assessSpatialNumerosity(data) {
    // Avaliar capacidade de processar numerosidade em arranjos espaciais
    return this.calculateEstimationAccuracy(data); // Simplificado para esta versão
  }

  /**
   * Gerar recomendações
   */
  generateRecommendations(data, skillIndex) {
    const recommendations = [];
    
    if (skillIndex < 0.6) {
      recommendations.push({
        type: 'improvement',
        priority: 'high',
        message: 'Pratique mais atividades de estimativa com números menores',
        activities: ['number_estimation_easy', 'visual_quantity_games']
      });
    }
    
    const errorPatterns = this.analyzeErrorPatterns(data);
    if (errorPatterns.biases.overestimation > errorPatterns.biases.underestimation) {
      recommendations.push({
        type: 'strategy',
        priority: 'medium',
        message: 'Tente olhar mais atentamente antes de estimar - você tende a superestimar',
        activities: ['careful_observation', 'comparison_games']
      });
    }
    
    if (this.assessConfidenceLevel(data) < 0.5) {
      recommendations.push({
        type: 'confidence',
        priority: 'medium',
        message: 'Confie mais nas suas primeiras impressões',
        activities: ['quick_estimation', 'intuitive_counting']
      });
    }
    
    return recommendations;
  }

  /**
   * Funções auxiliares
   */
  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    const variance = numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
    return variance;
  }

  calculateConsistency(data) {
    const errors = data.map(attempt => {
      if (attempt.userEstimate && attempt.actualCount) {
        return Math.abs(attempt.userEstimate - attempt.actualCount);
      }
      return null;
    }).filter(Boolean);
    
    if (errors.length === 0) return 0.5;
    
    const variance = this.calculateVariance(errors);
    return Math.max(0, 1 - (variance / 10)); // Normalizado
  }

  analyzeErrorDistribution(errors) {
    const distribution = { negative: 0, zero: 0, positive: 0 };
    
    errors.forEach(error => {
      if (error < 0) distribution.negative++;
      else if (error === 0) distribution.zero++;
      else distribution.positive++;
    });
    
    return distribution;
  }

  findMostCommon(array) {
    const frequency = {};
    array.forEach(item => frequency[item] = (frequency[item] || 0) + 1);
    return Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b, 0);
  }

  calculateAverageError(data) {
    const errors = data.map(attempt => {
      if (attempt.userEstimate && attempt.actualCount) {
        return Math.abs(attempt.userEstimate - attempt.actualCount);
      }
      return null;
    }).filter(Boolean);
    
    return errors.length > 0 ? errors.reduce((a, b) => a + b, 0) / errors.length : 0;
  }

  calculateMaxError(data) {
    const errors = data.map(attempt => {
      if (attempt.userEstimate && attempt.actualCount) {
        return Math.abs(attempt.userEstimate - attempt.actualCount);
      }
      return null;
    }).filter(Boolean);
    
    return errors.length > 0 ? Math.max(...errors) : 0;
  }

  calculateMinError(data) {
    const errors = data.map(attempt => {
      if (attempt.userEstimate && attempt.actualCount) {
        return Math.abs(attempt.userEstimate - attempt.actualCount);
      }
      return null;
    }).filter(Boolean);
    
    return errors.length > 0 ? Math.min(...errors) : 0;
  }

  getDefaultAnalysis() {
    return {
      timestamp: new Date().toISOString(),
      collector: 'EstimationSkillsCollector',
      version: '3.0.0',
      estimationAccuracy: 0.5,
      skillIndex: 0.5,
      confidenceLevel: 0.5,
      errorPatterns: { averageError: 0, errorVariance: 0, biases: {} },
      temporalProgress: { trend: 'insufficient_data', improvement: 0 },
      estimationStrategies: {},
      skillAssessment: {},
      recommendations: [],
      metadata: { totalAttempts: 0, validAttempts: 0 }
    };
  }
}
