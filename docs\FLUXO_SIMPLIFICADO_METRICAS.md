# Fluxo Simplificado de Métricas no Portal Betina V3

Este documento descreve o fluxo simplificado de métricas no Portal Betina V3, focando apenas nos componentes essenciais para o processamento de métricas dos jogos.

```mermaid
graph TD
    A[Jogos] -->|Interações do Usuário| B[Coletores de Métricas]
    B -->|Métricas Brutas| C[API Endpoints]
    C -->|Dados Recebidos| D[MetricsService]
    D -->|Métricas Processadas| E[SystemOrchestrator]
    E -->|Métricas Analisadas| F[DatabaseService]
    F -->|Dados Armazenados| G[Dashboards]

    subgraph "Jogos"
        A1[ColorMatch]
        A2[QuebraCabeca]
        A3[NumberCounting]
        A4[PadroesVisuais]
    end

    subgraph "Coletores Específicos"
        B1[ColorPerceptionCollector]
        B2[VisualProcessingCollector]
        B3[NumericalCognitionCollector]
        B4[PatternRecognitionCollector]
    end

    subgraph "Análise no Orquestrador"
        E1[CognitiveAnalyzer]
        E2[BehavioralAnalyzer]
        E3[TherapeuticAnalyzer]
        E4[SessionAnalyzer]
        E5[ProgressAnalyzer]
    end
```

## 1. Fluxo Principal de Dados

1. **Jogos** → Geram dados de interações do usuário
2. **Coletores** → Processam esses dados em métricas específicas por jogo
3. **API Endpoints** → Recebem dados via rotas `/api/metrics/*`
4. **MetricsService** → Normaliza e enriquece os dados
5. **SystemOrchestrator** → Analisa e processa as métricas
6. **DatabaseService** → Armazena os dados processados
7. **Dashboards** → Visualizam os dados para usuários finais

## 2. Componentes Essenciais Mantidos

### 2.1 Serviços Centrais

- **MetricsService** - Processamento central de métricas
- **SystemOrchestrator** - Coordenação do fluxo de dados
- **DatabaseService** - Armazenamento e recuperação de dados

### 2.2 Analisadores

- **CognitiveAnalyzer** - Análise cognitiva básica
- **BehavioralAnalyzer** - Análise comportamental
- **TherapeuticAnalyzer** - Análise terapêutica
- **SessionAnalyzer** - Análise de sessões
- **ProgressAnalyzer** - Análise de progresso

### 2.3 Coletores de Métricas nos Jogos

Cada jogo tem seus coletores especializados:

1. **ColorMatch**:
   - ColorPerceptionCollector
   - VisualProcessingCollector
   - AttentionalSelectivityCollector
   - ColorCognitionCollector

2. **QuebraCabeca**:
   - VisualProcessingCollector
   - SpatialReasoningCollector
   - ProblemSolvingCollector
   - MotorSkillsCollector

3. **PadroesVisuais**:
   - VisualMemoryCollector
   - SpatialProcessingCollector
   - SequentialReasoningCollector
   - PatternRecognitionCollector

4. **NumberCounting**:
   - VisualProcessingCollector
   - NumericalCognitionCollector
   - MathematicalReasoningCollector
   - AttentionFocusCollector

## 3. Métodos Principais do SystemOrchestrator

Estes são os métodos essenciais para o fluxo de métricas:

1. **processGameInput** - Processa entrada de dados dos jogos
2. **refineMetricsData** - Refina dados brutos de métricas
3. **collectTherapeuticMetrics** - Coleta métricas terapêuticas
4. **processTherapeuticData** - Processa dados terapêuticos
5. **storeTherapeuticData** - Armazena dados no banco de dados
6. **prepareDashboardData** - Prepara dados para visualização

## 4. Configurações Otimizadas

As configurações foram ajustadas para manter apenas os módulos essenciais:

```javascript
this.config = {
  // MÓDULOS ESSENCIAIS (fluxo principal de métricas)
  enableMetricsService: true,             // Serviço principal de métricas
  enableCognitiveAnalysis: true,          // Análise cognitiva básica
  enableTherapeuticIntegration: true,     // Processamento terapêutico
  enableGameUsageTracking: true,          // Tracking de uso dos jogos
  
  // MÓDULOS DE SUPORTE (opcionais, mas úteis)
  enableErrorPatternAnalyzer: true,       // Análise de padrões de erro
  enableSessionIntegration: true,         // Integração de sessões
  
  // MÓDULOS DE INTERFACE (necessários para acessibilidade e UX)
  enableAccessibilityIntegration: true,   // Recursos de acessibilidade
  
  // Todos os outros módulos avançados foram desabilitados
}
```
