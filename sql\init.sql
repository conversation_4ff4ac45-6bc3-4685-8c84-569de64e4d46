-- ====================================
-- SCHEMA COMPLETO DO BANCO DE DADOS - CORRIGIDO PARA POSTGRESQL
-- Portal Betina V3 - 2025-07-03T01:55:55.615Z
-- Total de métricas analisadas: 2555
-- Métricas únicas: 2155
-- Jogos analisados: 9
-- ====================================

-- Extensões necessárias para PostgreSQL
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Função para atualizar timestamp automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ====================================
-- TABELA DE USUÁRIOS - SISTEMA DE AUTENTICAÇÃO
-- ====================================

-- Tabela de usuários para autenticação
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email VARCHAR(254) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  username VARCHAR(30) UNIQUE NOT NULL,
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  date_of_birth DATE,
  avatar VARCHAR(255),
  phone VARCHAR(20),
  role VARCHAR(20) NOT NULL DEFAULT 'parent' CHECK (role IN ('admin', 'therapist', 'parent', 'child')),
  permissions TEXT[] DEFAULT ARRAY[]::TEXT[],
  status VARCHAR(30) NOT NULL DEFAULT 'pending_verification' CHECK (status IN ('active', 'inactive', 'suspended', 'pending_verification')),
  is_email_verified BOOLEAN DEFAULT FALSE,
  email_verification_token VARCHAR(255),
  password_reset_token VARCHAR(255),
  password_reset_expires TIMESTAMP,
  therapeutic_profile JSONB DEFAULT '{}',
  assigned_therapist UUID,
  assigned_children UUID[] DEFAULT ARRAY[]::UUID[],
  parent_guardian UUID,
  last_login TIMESTAMP,
  last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  refresh_tokens JSONB DEFAULT '[]',
  login_attempts INTEGER DEFAULT 0,
  lock_until TIMESTAMP,
  audit_log JSONB DEFAULT '[]',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP
);

-- Trigger para atualizar updated_at automaticamente na tabela users
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Índices para otimização da tabela users
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_password_reset_token ON users(password_reset_token);
CREATE INDEX IF NOT EXISTS idx_users_email_verification_token ON users(email_verification_token);

-- Tabela principal de sessões de jogos
CREATE TABLE IF NOT EXISTS game_sessions (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) UNIQUE NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  user_id VARCHAR(255) NOT NULL,
  child_id VARCHAR(255),
  difficulty_level VARCHAR(50),
  game_mode VARCHAR(100),
  session_duration INTEGER,
  completion_status VARCHAR(50),
  total_score INTEGER,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  metadata JSONB
);

-- Trigger para atualizar updated_at automaticamente
CREATE TRIGGER update_game_sessions_updated_at BEFORE UPDATE ON game_sessions FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Tabela de métricas gerais (base para todos os jogos)
CREATE TABLE IF NOT EXISTS metrics_general (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  metric_type VARCHAR(100) NOT NULL,
  metric_category VARCHAR(100),
  metric_name VARCHAR(200),
  metric_value DECIMAL(10,4),
  metric_text VARCHAR(500),
  metric_json JSONB,
  processing_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  processor_version VARCHAR(50),
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas cognitive
CREATE TABLE IF NOT EXISTS metrics_cognitive (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  attention_score DECIMAL(10,4),
  attention_level VARCHAR(50),
  attention_data JSONB,
  memory_score DECIMAL(10,4),
  memory_level VARCHAR(50),
  memory_data JSONB,
  processing_score DECIMAL(10,4),
  processing_level VARCHAR(50),
  processing_data JSONB,
  executive_score DECIMAL(10,4),
  executive_level VARCHAR(50),
  executive_data JSONB,
  working_memory_score DECIMAL(10,4),
  working_memory_data JSONB,
  cognitive_flexibility_score DECIMAL(10,4),
  cognitive_flexibility_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas visual_perception
CREATE TABLE IF NOT EXISTS metrics_visual_perception (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  visual_accuracy DECIMAL(10,4),
  visual_processing_speed DECIMAL(10,4),
  visual_attention DECIMAL(10,4),
  color_discrimination_score DECIMAL(10,4),
  color_discrimination_data JSONB,
  pattern_recognition_score DECIMAL(10,4),
  pattern_recognition_data JSONB,
  visual_memory_score DECIMAL(10,4),
  visual_memory_data JSONB,
  spatial_processing_score DECIMAL(10,4),
  spatial_processing_data JSONB,
  visual_scanning_efficiency DECIMAL(10,4),
  visual_scanning_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas auditory_processing
CREATE TABLE IF NOT EXISTS metrics_auditory_processing (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  auditory_memory_score DECIMAL(10,4),
  auditory_memory_data JSONB,
  sequential_processing_score DECIMAL(10,4),
  sequential_processing_data JSONB,
  rhythm_perception_score DECIMAL(10,4),
  rhythm_perception_data JSONB,
  musical_pattern_score DECIMAL(10,4),
  musical_pattern_data JSONB,
  auditory_attention_score DECIMAL(10,4),
  auditory_attention_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas language_processing
CREATE TABLE IF NOT EXISTS metrics_language_processing (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  letter_recognition_score DECIMAL(10,4),
  letter_recognition_data JSONB,
  phonological_awareness_score DECIMAL(10,4),
  phonological_awareness_data JSONB,
  visual_linguistic_score DECIMAL(10,4),
  visual_linguistic_data JSONB,
  dyslexia_indicators_score DECIMAL(10,4),
  dyslexia_indicators_data JSONB,
  reading_readiness_score DECIMAL(10,4),
  reading_readiness_data JSONB,
  linguistic_processing_score DECIMAL(10,4),
  linguistic_processing_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas numerical_cognition
CREATE TABLE IF NOT EXISTS metrics_numerical_cognition (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  number_recognition_score DECIMAL(10,4),
  number_recognition_data JSONB,
  counting_ability_score DECIMAL(10,4),
  counting_ability_data JSONB,
  mathematical_reasoning_score DECIMAL(10,4),
  mathematical_reasoning_data JSONB,
  numerical_processing_speed DECIMAL(10,4),
  numerical_processing_data JSONB,
  number_sense_score DECIMAL(10,4),
  number_sense_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas spatial_reasoning
CREATE TABLE IF NOT EXISTS metrics_spatial_reasoning (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  spatial_visualization_score DECIMAL(10,4),
  spatial_visualization_data JSONB,
  mental_rotation_score DECIMAL(10,4),
  mental_rotation_data JSONB,
  spatial_memory_score DECIMAL(10,4),
  spatial_memory_data JSONB,
  problem_solving_score DECIMAL(10,4),
  problem_solving_data JSONB,
  spatial_orientation_score DECIMAL(10,4),
  spatial_orientation_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas motor_skills
CREATE TABLE IF NOT EXISTS metrics_motor_skills (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  fine_motor_score DECIMAL(10,4),
  fine_motor_level VARCHAR(50),
  fine_motor_data JSONB,
  gross_motor_score DECIMAL(10,4),
  gross_motor_level VARCHAR(50),
  gross_motor_data JSONB,
  coordination_score DECIMAL(10,4),
  coordination_level VARCHAR(50),
  coordination_data JSONB,
  gesture_score DECIMAL(10,4),
  gesture_level VARCHAR(50),
  gesture_data JSONB,
  motor_planning_score DECIMAL(10,4),
  motor_planning_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas therapeutic_analysis
CREATE TABLE IF NOT EXISTS metrics_therapeutic_analysis (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  progress_score DECIMAL(10,4),
  progress_level VARCHAR(50),
  progress_data JSONB,
  difficulties_score DECIMAL(10,4),
  difficulties_level VARCHAR(50),
  difficulties_data JSONB,
  recommendations_score DECIMAL(10,4),
  recommendations_level VARCHAR(50),
  recommendations_data JSONB,
  interventions_score DECIMAL(10,4),
  interventions_level VARCHAR(50),
  interventions_data JSONB,
  therapeutic_goals_data JSONB,
  milestone_progress_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela de métricas behavioral_patterns
CREATE TABLE IF NOT EXISTS metrics_behavioral_patterns (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  response_time_avg DECIMAL(10,4),
  response_time_data JSONB,
  accuracy_patterns DECIMAL(10,4),
  error_patterns_data JSONB,
  engagement_score DECIMAL(10,4),
  engagement_data JSONB,
  attention_patterns DECIMAL(10,4),
  behavioral_adaptations_data JSONB,
  strategy_usage_data JSONB,
  cognitive_style_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo ColorMatch
CREATE TABLE IF NOT EXISTS metrics_colormatch (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo ContagemNumeros
CREATE TABLE IF NOT EXISTS metrics_contagemnumeros (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo ImageAssociation
CREATE TABLE IF NOT EXISTS metrics_imageassociation (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo LetterRecognition
CREATE TABLE IF NOT EXISTS metrics_letterrecognition (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo MemoryGame
CREATE TABLE IF NOT EXISTS metrics_memorygame (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo MusicalSequence
CREATE TABLE IF NOT EXISTS metrics_musicalsequence (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo PadroesVisuais
CREATE TABLE IF NOT EXISTS metrics_padroesvisuais (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo QuebraCabeca
CREATE TABLE IF NOT EXISTS metrics_quebracabeca (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- Tabela específica do jogo GameSpecific
CREATE TABLE IF NOT EXISTS metrics_gamespecific (
  id SERIAL PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  game_id VARCHAR(100) NOT NULL,
  game_specific_score DECIMAL(10,4),
  game_specific_data JSONB,
  collector_results_data JSONB,
  hub_analysis_data JSONB,
  specific_analysis_data JSONB,
  therapeutic_indicators_data JSONB,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES game_sessions(session_id) ON DELETE CASCADE
);

-- =====================================
-- ÍNDICES PARA OTIMIZAÇÃO DE PERFORMANCE
-- =====================================

-- Índices para game_sessions
CREATE INDEX IF NOT EXISTS idx_sessions_game_user ON game_sessions(game_id, user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_timestamp ON game_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_sessions_child ON game_sessions(child_id);
CREATE INDEX IF NOT EXISTS idx_sessions_session_id ON game_sessions(session_id);

-- Índices para metrics_general
CREATE INDEX IF NOT EXISTS idx_metrics_session ON metrics_general(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_type ON metrics_general(metric_type);
CREATE INDEX IF NOT EXISTS idx_metrics_category ON metrics_general(metric_category);
CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON metrics_general(processing_timestamp);

-- Índices para tabelas de métricas gerais
CREATE INDEX IF NOT EXISTS idx_metrics_cognitive_session ON metrics_cognitive(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_cognitive_game ON metrics_cognitive(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_visual_perception_session ON metrics_visual_perception(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_visual_perception_game ON metrics_visual_perception(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_auditory_processing_session ON metrics_auditory_processing(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_auditory_processing_game ON metrics_auditory_processing(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_language_processing_session ON metrics_language_processing(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_language_processing_game ON metrics_language_processing(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_numerical_cognition_session ON metrics_numerical_cognition(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_numerical_cognition_game ON metrics_numerical_cognition(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_spatial_reasoning_session ON metrics_spatial_reasoning(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_spatial_reasoning_game ON metrics_spatial_reasoning(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_motor_skills_session ON metrics_motor_skills(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_motor_skills_game ON metrics_motor_skills(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_therapeutic_analysis_session ON metrics_therapeutic_analysis(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_therapeutic_analysis_game ON metrics_therapeutic_analysis(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_behavioral_patterns_session ON metrics_behavioral_patterns(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_behavioral_patterns_game ON metrics_behavioral_patterns(game_id);

-- Índices para tabelas específicas dos jogos
CREATE INDEX IF NOT EXISTS idx_metrics_colormatch_session ON metrics_colormatch(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_colormatch_game ON metrics_colormatch(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_contagemnumeros_session ON metrics_contagemnumeros(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_contagemnumeros_game ON metrics_contagemnumeros(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_imageassociation_session ON metrics_imageassociation(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_imageassociation_game ON metrics_imageassociation(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_letterrecognition_session ON metrics_letterrecognition(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_letterrecognition_game ON metrics_letterrecognition(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_memorygame_session ON metrics_memorygame(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_memorygame_game ON metrics_memorygame(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_musicalsequence_session ON metrics_musicalsequence(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_musicalsequence_game ON metrics_musicalsequence(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_padroesvisuais_session ON metrics_padroesvisuais(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_padroesvisuais_game ON metrics_padroesvisuais(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_quebracabeca_session ON metrics_quebracabeca(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_quebracabeca_game ON metrics_quebracabeca(game_id);
CREATE INDEX IF NOT EXISTS idx_metrics_gamespecific_session ON metrics_gamespecific(session_id);
CREATE INDEX IF NOT EXISTS idx_metrics_gamespecific_game ON metrics_gamespecific(game_id);

-- =====================================
-- VIEWS PARA CONSULTAS FACILITADAS
-- =====================================

-- View para sessões completas com métricas
CREATE OR REPLACE VIEW view_complete_session AS
SELECT
  gs.session_id,
  gs.game_id,
  gs.user_id,
  gs.child_id,
  gs.difficulty_level,
  gs.session_duration,
  gs.total_score,
  gs.created_at,
  COUNT(mg.id) as total_metrics,
  AVG(mg.metric_value) as avg_metric_value
FROM game_sessions gs
LEFT JOIN metrics_general mg ON gs.session_id = mg.session_id
GROUP BY gs.session_id, gs.game_id, gs.user_id, gs.child_id, gs.difficulty_level, gs.session_duration, gs.total_score, gs.created_at;

-- View para performance por jogo
CREATE OR REPLACE VIEW view_game_performance AS
SELECT
  game_id,
  COUNT(DISTINCT session_id) as total_sessions,
  COUNT(DISTINCT user_id) as unique_users,
  AVG(total_score) as avg_score,
  AVG(session_duration) as avg_duration
FROM game_sessions
GROUP BY game_id;

-- View para progresso do usuário
CREATE OR REPLACE VIEW view_user_progress AS
SELECT
  user_id,
  game_id,
  COUNT(session_id) as sessions_played,
  MAX(total_score) as best_score,
  AVG(total_score) as avg_score,
  MAX(created_at) as last_played
FROM game_sessions
GROUP BY user_id, game_id;

-- View para análise terapêutica agregada
CREATE OR REPLACE VIEW view_therapeutic_analysis AS
SELECT
  ta.session_id,
  gs.game_id,
  gs.user_id,
  gs.child_id,
  AVG(ta.progress_score) as avg_progress_score,
  AVG(ta.difficulties_score) as avg_difficulties_score,
  AVG(ta.recommendations_score) as avg_recommendations_score,
  COUNT(ta.id) as total_therapeutic_entries
FROM metrics_therapeutic_analysis ta
JOIN game_sessions gs ON ta.session_id = gs.session_id
GROUP BY ta.session_id, gs.game_id, gs.user_id, gs.child_id;

-- View para análise comportamental
CREATE OR REPLACE VIEW view_behavioral_analysis AS
SELECT
  bp.session_id,
  gs.game_id,
  gs.user_id,
  AVG(bp.response_time_avg) as avg_response_time,
  AVG(bp.accuracy_patterns) as avg_accuracy,
  AVG(bp.engagement_score) as avg_engagement,
  AVG(bp.attention_patterns) as avg_attention
FROM metrics_behavioral_patterns bp
JOIN game_sessions gs ON bp.session_id = gs.session_id
GROUP BY bp.session_id, gs.game_id, gs.user_id;

-- =====================================
-- COMENTÁRIOS E DOCUMENTAÇÃO
-- =====================================

-- Comentários nas tabelas principais
COMMENT ON TABLE game_sessions IS 'Tabela principal contendo informações de todas as sessões de jogos';
COMMENT ON TABLE metrics_general IS 'Métricas gerais aplicáveis a todos os jogos';
COMMENT ON TABLE metrics_cognitive IS 'Métricas específicas de desenvolvimento cognitivo';
COMMENT ON TABLE metrics_visual_perception IS 'Métricas de percepção visual e processamento';
COMMENT ON TABLE metrics_auditory_processing IS 'Métricas de processamento auditivo e musical';
COMMENT ON TABLE metrics_language_processing IS 'Métricas de processamento linguístico e reconhecimento';
COMMENT ON TABLE metrics_numerical_cognition IS 'Métricas de cognição numérica e matemática';
COMMENT ON TABLE metrics_spatial_reasoning IS 'Métricas de raciocínio espacial e resolução de problemas';
COMMENT ON TABLE metrics_motor_skills IS 'Métricas de habilidades motoras finas e grossas';
COMMENT ON TABLE metrics_therapeutic_analysis IS 'Análises terapêuticas e recomendações';
COMMENT ON TABLE metrics_behavioral_patterns IS 'Padrões comportamentais e estratégias cognitivas';

-- Comentários nas tabelas específicas dos jogos
COMMENT ON TABLE metrics_colormatch IS 'Métricas específicas do jogo ColorMatch';
COMMENT ON TABLE metrics_contagemnumeros IS 'Métricas específicas do jogo ContagemNumeros';
COMMENT ON TABLE metrics_imageassociation IS 'Métricas específicas do jogo ImageAssociation';
COMMENT ON TABLE metrics_letterrecognition IS 'Métricas específicas do jogo LetterRecognition';
COMMENT ON TABLE metrics_memorygame IS 'Métricas específicas do jogo MemoryGame';
COMMENT ON TABLE metrics_musicalsequence IS 'Métricas específicas do jogo MusicalSequence';
COMMENT ON TABLE metrics_padroesvisuais IS 'Métricas específicas do jogo PadroesVisuais';
COMMENT ON TABLE metrics_quebracabeca IS 'Métricas específicas do jogo QuebraCabeca';
COMMENT ON TABLE metrics_gamespecific IS 'Métricas genéricas para processamento de jogos';

-- =====================================
-- DADOS DE TESTE INICIAIS
-- =====================================

-- ====================================
-- USUÁRIOS PADRÃO PARA TESTE
-- ====================================

-- Inserir usuários de teste com senhas criptografadas (bcrypt)
-- Senha padrão para todos: 'admin123' (hash bcrypt com salt rounds 12)
INSERT INTO users (
  id, 
  email, 
  password, 
  username, 
  first_name, 
  last_name, 
  role, 
  permissions, 
  status, 
  is_email_verified
) VALUES 
(
  uuid_generate_v4(),
  '<EMAIL>',
  '$2b$12$LQv3c1yqBwEHFqAiAGcUCeAqWx.8liI6.WlKbFJUOXxlqG7X.sNhG', -- admin123
  'admin',
  'Administrador',
  'Sistema',
  'admin',
  ARRAY['read:dashboard', 'read:analytics', 'read:children', 'read:sessions', 'read:reports', 'read:users', 'read:system_status', 'write:children', 'write:sessions', 'write:reports', 'write:users', 'write:system_config', 'delete:children', 'delete:sessions', 'delete:reports', 'delete:users', 'manage:system', 'manage:users', 'manage:billing', 'manage:permissions', 'export:data', 'import:data', 'view:sensitive', 'modify:critical', 'audit:access', 'view:basic_dashboards', 'view:premium_dashboards', 'customize:dashboards'],
  'active',
  true
),
(
  uuid_generate_v4(),
  '<EMAIL>',
  '$2b$12$LQv3c1yqBwEHFqAiAGcUCeAqWx.8liI6.WlKbFJUOXxlqG7X.sNhG', -- admin123
  'terapeuta',
  'Dr. Maria',
  'Silva',
  'therapist',
  ARRAY['read:dashboard', 'read:analytics', 'read:children', 'read:sessions', 'read:reports', 'read:system_status', 'write:children', 'write:sessions', 'write:reports', 'delete:sessions', 'export:data', 'view:sensitive', 'view:basic_dashboards', 'view:premium_dashboards'],
  'active',
  true
),
(
  uuid_generate_v4(),
  '<EMAIL>',
  '$2b$12$LQv3c1yqBwEHFqAiAGcUCeAqWx.8liI6.WlKbFJUOXxlqG7X.sNhG', -- admin123
  'pai',
  'João',
  'Santos',
  'parent',
  ARRAY['read:dashboard', 'read:children', 'read:sessions', 'read:reports', 'export:data', 'view:basic_dashboards'],
  'active',
  true
),        (
          uuid_generate_v4(),
          '<EMAIL>',
          '$2b$12$LQv3c1yqBwEHFqAiAGcUCeAqWx.8liI6.WlKbFJUOXxlqG7X.sNhG', -- admin123
          'usuario',
          'Ana',
          'Costa',
          'parent',
          ARRAY['read:dashboard', 'read:children', 'read:sessions', 'view:basic_dashboards'],
          'active',
          true
        )
ON CONFLICT (email) DO NOTHING;

-- Inserir uma sessão de exemplo para teste
INSERT INTO game_sessions (session_id, game_id, user_id, child_id, total_score, metadata) 
VALUES ('test-session-1', 'ColorMatch', 'user123', 'child456', 85, '{"test": true}')
ON CONFLICT (session_id) DO NOTHING;

-- Inserir métrica de exemplo
INSERT INTO metrics_general (session_id, game_id, metric_type, metric_value) 
VALUES ('test-session-1', 'ColorMatch', 'accuracy', 0.85)
ON CONFLICT DO NOTHING;

-- =====================================
-- QUERIES DE VERIFICAÇÃO
-- =====================================

-- Verificar todas as tabelas criadas
-- SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND (tablename LIKE 'game_%' OR tablename LIKE 'metrics_%') ORDER BY tablename;

-- Verificar todas as views criadas
-- SELECT viewname FROM pg_views WHERE schemaname = 'public' AND viewname LIKE 'view_%' ORDER BY viewname;

-- Verificar todos os índices criados
-- SELECT indexname FROM pg_indexes WHERE schemaname = 'public' AND indexname LIKE 'idx_%' ORDER BY indexname;

-- =====================================
-- DOCUMENTAÇÃO DAS MÉTRICAS COLETADAS
-- =====================================

/*
RESUMO FINAL:
- 21 TABELAS CRIADAS (1 usuários + 1 principal + 10 métricas gerais + 9 específicas dos jogos)
- TODAS AS CORREÇÕES APLICADAS:
  ✅ JSON → JSONB (PostgreSQL otimizado)
  ✅ Foreign Keys com ON DELETE CASCADE
  ✅ Índices completos para performance
  ✅ Triggers para updated_at automático
  ✅ Views para dashboards
  ✅ Comentários e documentação
  ✅ Queries de verificação incluídas
  ✅ Extensões UUID habilitadas
  ✅ CREATE OR REPLACE VIEW (PostgreSQL compatível)
  ✅ TABELA USERS com sistema de autenticação completo
  ✅ USUÁRIOS PADRÃO para teste (admin, terapeuta, pai, usuario)

TABELAS PRINCIPAIS:
1. users - Sistema de autenticação e autorização
2. game_sessions - Sessões de jogos

USUÁRIOS DE TESTE CRIADOS:
- <EMAIL> (senha: admin123) - Administrador completo
- <EMAIL> (senha: admin123) - Terapeuta com permissões profissionais
- <EMAIL> (senha: admin123) - Pai/responsável com acesso limitado
- <EMAIL> (senha: admin123) - Usuário básico

JOGOS COBERTOS:
1. ColorMatch - Discriminação visual e cores
2. ContagemNumeros - Cognição numérica
3. ImageAssociation - Associação conceitual
4. LetterRecognition - Reconhecimento de letras
5. MemoryGame - Memória e atenção
6. MusicalSequence - Processamento auditivo
7. PadroesVisuais - Padrões visuais
8. QuebraCabeca - Raciocínio espacial
9. GameSpecific - Processamento genérico

TOTAL DE CAMPOS ÚNICOS: 2155+
TOTAL DE MÉTRICAS ANALISADAS: 2555+
*/
