/**
 * 📝 SEQUENCE ANALYSIS COLLECTOR V3
 * Coletor especializado em análise de habilidades de sequência numérica
 * Portal Betina V3
 */

export class SequenceAnalysisCollector {
  constructor() {
    this.sequenceThresholds = {
      excellent: 0.95,
      good: 0.85,
      average: 0.70,
      poor: 0.50,
      critical: 0.30
    };
    
    this.sequenceTypes = {
      ascending: 'Sequências crescentes simples',
      descending: 'Sequências decrescentes',
      evenNumbers: 'Sequências de números pares',
      oddNumbers: 'Sequências de números ímpares',
      arithmetic: 'Progressões aritméticas',
      geometric: 'Progressões geométricas',
      fibonacci: 'Sequências tipo Fibonacci',
      complex: 'Padrões complexos'
    };
    
    this.cognitiveSkills = {
      patternRecognition: 'Reconhecimento de padrões',
      logicalReasoning: 'Raciocínio lógico',
      sequentialMemory: 'Memória sequencial',
      abstractThinking: 'Pensamento abstrato',
      ruleInference: 'Inferência de regras'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }
  
  /**
   * Análise principal das habilidades de sequência
   */
  async analyze(data) {
    if (!data || !data.sequenceCompletion) {
      console.warn('SequenceAnalysisCollector: Dados de sequência não encontrados');
      return this.getDefaultAnalysis();
    }

    const sequenceData = data.sequenceCompletion;
    
    // Analisar precisão por tipo de sequência
    const accuracyByType = this.analyzeAccuracyByType(sequenceData);
    
    // Detectar estratégias de resolução
    const solvingStrategies = this.detectSolvingStrategies(sequenceData);
    
    // Avaliar complexidade cognitiva
    const cognitiveComplexity = this.assessCognitiveComplexity(sequenceData);
    
    // Analisar progresso temporal
    const temporalProgress = this.analyzeTemporalProgress(sequenceData);
    
    // Detectar padrões de erro
    const errorPatterns = this.analyzeErrorPatterns(sequenceData);
    
    // Avaliar velocidade de processamento
    const processingSpeed = this.assessProcessingSpeed(sequenceData);
    
    // Calcular índice de habilidade sequencial
    const sequentialSkillIndex = this.calculateSequentialSkillIndex(sequenceData);

    const analysis = {
      timestamp: new Date().toISOString(),
      collector: 'SequenceAnalysisCollector',
      version: '3.0.0',
      
      // Métricas principais
      overallAccuracy: this.calculateOverallAccuracy(sequenceData),
      sequentialSkillIndex,
      processingSpeed,
      
      // Análises detalhadas
      accuracyByType,
      solvingStrategies,
      cognitiveComplexity,
      temporalProgress,
      errorPatterns,
      
      // Habilidades cognitivas específicas
      cognitiveSkills: {
        patternRecognition: this.assessPatternRecognition(sequenceData),
        logicalReasoning: this.assessLogicalReasoning(sequenceData),
        sequentialMemory: this.assessSequentialMemory(sequenceData),
        abstractThinking: this.assessAbstractThinking(sequenceData),
        ruleInference: this.assessRuleInference(sequenceData)
      },
      
      // Análise de dificuldade
      difficultyAnalysis: this.analyzeDifficultyProgression(sequenceData),
      
      // Recomendações
      recommendations: this.generateRecommendations(sequenceData, sequentialSkillIndex),
      
      // Metadados
      metadata: {
        totalSequences: sequenceData.length || 0,
        uniqueTypes: this.countUniqueTypes(sequenceData),
        averageLength: this.calculateAverageLength(sequenceData),
        complexityDistribution: this.analyzeComplexityDistribution(sequenceData)
      }
    };

    return analysis;
  }

  /**
   * Analisar precisão por tipo de sequência
   */
  analyzeAccuracyByType(data) {
    const accuracyByType = {};
    
    // Agrupar por tipo de sequência
    const groupedData = this.groupBySequenceType(data);
    
    Object.keys(groupedData).forEach(type => {
      const typeData = groupedData[type];
      const correct = typeData.filter(attempt => attempt.isCorrect).length;
      accuracyByType[type] = {
        accuracy: typeData.length > 0 ? correct / typeData.length : 0,
        attempts: typeData.length,
        averageTime: this.calculateAverageTime(typeData),
        difficulty: this.assessTypeDifficulty(typeData)
      };
    });
    
    return accuracyByType;
  }

  /**
   * Detectar estratégias de resolução
   */
  detectSolvingStrategies(data) {
    const strategies = {
      immediateRecognition: false,  // Reconhecimento imediato do padrão
      systematicAnalysis: false,    // Análise sistemática passo a passo
      trialAndError: false,         // Tentativa e erro
      ruleApplication: false,       // Aplicação consciente de regras
      visualPattern: false          // Reconhecimento visual de padrão
    };

    // Detectar reconhecimento imediato (respostas muito rápidas e corretas)
    const quickCorrect = data.filter(attempt => 
      attempt.isCorrect && attempt.responseTime < 3000
    ).length;
    strategies.immediateRecognition = quickCorrect / data.length > 0.6;

    // Detectar análise sistemática (tempo moderado, alta precisão)
    const systematicAttempts = data.filter(attempt => 
      attempt.responseTime >= 3000 && attempt.responseTime <= 8000 && attempt.isCorrect
    ).length;
    strategies.systematicAnalysis = systematicAttempts / data.length > 0.5;

    // Detectar tentativa e erro (múltiplas respostas incorretas seguidas de correção)
    strategies.trialAndError = this.detectTrialAndErrorPattern(data);

    return strategies;
  }

  /**
   * Avaliar complexidade cognitiva
   */
  assessCognitiveComplexity(data) {
    const complexityScores = data.map(attempt => {
      let complexity = 0;
      
      // Complexidade baseada no tipo de sequência
      if (attempt.sequenceType) {
        switch (attempt.sequenceType) {
          case 'crescente_simples':
          case 'decrescente_simples':
            complexity += 1;
            break;
          case 'pares':
          case 'ímpares':
            complexity += 2;
            break;
          case 'múltiplos_3':
          case 'soma_3':
            complexity += 3;
            break;
          case 'fibonacci':
          case 'multiplicação':
            complexity += 4;
            break;
          default:
            complexity += 2;
        }
      }
      
      // Complexidade baseada no comprimento da sequência
      if (attempt.sequenceLength) {
        complexity += Math.max(0, attempt.sequenceLength - 3);
      }
      
      // Complexidade baseada na magnitude dos números
      if (attempt.maxNumber) {
        complexity += attempt.maxNumber > 10 ? 1 : 0;
      }
      
      return {
        attempt: attempt,
        complexity: complexity,
        solved: attempt.isCorrect
      };
    });

    return {
      averageComplexity: complexityScores.reduce((sum, item) => sum + item.complexity, 0) / complexityScores.length,
      complexityVsPerformance: this.analyzeComplexityVsPerformance(complexityScores),
      adaptiveCapacity: this.assessAdaptiveCapacity(complexityScores)
    };
  }

  /**
   * Analisar progresso temporal
   */
  analyzeTemporalProgress(data) {
    if (data.length < 3) return { trend: 'insufficient_data', improvement: 0 };
    
    const segments = this.divideIntoSegments(data, 3);
    const segmentAccuracies = segments.map(segment => this.calculateAccuracy(segment));
    
    const trend = this.calculateTrend(segmentAccuracies);
    const improvement = segmentAccuracies[segmentAccuracies.length - 1] - segmentAccuracies[0];
    
    return {
      trend,
      improvement,
      segmentAccuracies,
      learningRate: this.calculateLearningRate(segmentAccuracies),
      stability: this.calculateStability(segmentAccuracies)
    };
  }

  /**
   * Analisar padrões de erro
   */
  analyzeErrorPatterns(data) {
    const errors = data.filter(attempt => !attempt.isCorrect);
    
    const errorTypes = {
      offByOne: 0,          // Erro de ±1
      patternMisunderstanding: 0, // Não entendeu o padrão
      calculationError: 0,   // Erro de cálculo
      randomGuess: 0         // Resposta aleatória
    };

    errors.forEach(error => {
      if (error.userAnswer && error.correctAnswer) {
        const difference = Math.abs(error.userAnswer - error.correctAnswer);
        
        if (difference === 1) {
          errorTypes.offByOne++;
        } else if (difference > 5) {
          errorTypes.randomGuess++;
        } else {
          errorTypes.calculationError++;
        }
      }
    });

    return {
      totalErrors: errors.length,
      errorTypes,
      errorRate: data.length > 0 ? errors.length / data.length : 0,
      criticalErrors: this.identifyCriticalErrors(errors),
      errorProgression: this.analyzeErrorProgression(data)
    };
  }

  /**
   * Avaliar velocidade de processamento
   */
  assessProcessingSpeed(data) {
    const responseTimes = data.map(attempt => attempt.responseTime).filter(Boolean);
    
    if (responseTimes.length === 0) return { speed: 'unknown', score: 0.5 };
    
    const averageTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const medianTime = this.calculateMedian(responseTimes);
    
    let speedCategory = 'average';
    let speedScore = 0.5;
    
    if (averageTime < 4000) {
      speedCategory = 'fast';
      speedScore = 0.8;
    } else if (averageTime < 7000) {
      speedCategory = 'average';
      speedScore = 0.6;
    } else {
      speedCategory = 'slow';
      speedScore = 0.4;
    }
    
    return {
      averageTime,
      medianTime,
      speedCategory,
      speedScore,
      consistency: this.calculateTimeConsistency(responseTimes)
    };
  }

  /**
   * Calcular índice de habilidade sequencial
   */
  calculateSequentialSkillIndex(data) {
    const accuracy = this.calculateOverallAccuracy(data);
    const speed = this.assessProcessingSpeed(data).speedScore;
    const complexity = this.assessCognitiveComplexity(data).adaptiveCapacity || 0.5;
    const consistency = this.calculateConsistency(data);
    
    return (accuracy * 0.4) + (speed * 0.2) + (complexity * 0.2) + (consistency * 0.2);
  }

  /**
   * Avaliar habilidades cognitivas específicas
   */
  assessPatternRecognition(data) {
    // Capacidade de reconhecer padrões rapidamente
    const quickCorrect = data.filter(attempt => 
      attempt.isCorrect && attempt.responseTime < 5000
    ).length;
    
    return data.length > 0 ? quickCorrect / data.length : 0.5;
  }

  assessLogicalReasoning(data) {
    // Capacidade de aplicar lógica a sequências complexas
    const complexSequences = data.filter(attempt => 
      attempt.sequenceType && 
      ['múltiplos_3', 'soma_3', 'fibonacci', 'multiplicação'].includes(attempt.sequenceType)
    );
    
    return this.calculateAccuracy(complexSequences);
  }

  assessSequentialMemory(data) {
    // Capacidade de lembrar e trabalhar com sequências longas
    const longSequences = data.filter(attempt => 
      attempt.sequenceLength && attempt.sequenceLength >= 4
    );
    
    return this.calculateAccuracy(longSequences);
  }

  assessAbstractThinking(data) {
    // Capacidade de trabalhar com padrões abstratos
    const abstractPatterns = data.filter(attempt => 
      attempt.sequenceType && 
      ['fibonacci', 'multiplicação', 'progressão_ímpar'].includes(attempt.sequenceType)
    );
    
    return this.calculateAccuracy(abstractPatterns);
  }

  assessRuleInference(data) {
    // Capacidade de inferir regras a partir de exemplos
    const inferenceScore = data.reduce((score, attempt, index) => {
      if (index === 0) return score;
      
      // Se acertou após ver exemplos similares
      const similarPrevious = data.slice(0, index).filter(prev => 
        prev.sequenceType === attempt.sequenceType
      );
      
      if (similarPrevious.length > 0 && attempt.isCorrect) {
        return score + 1;
      }
      
      return score;
    }, 0);
    
    return data.length > 1 ? inferenceScore / (data.length - 1) : 0.5;
  }

  /**
   * Gerar recomendações
   */
  generateRecommendations(data, skillIndex) {
    const recommendations = [];
    
    if (skillIndex < 0.6) {
      recommendations.push({
        type: 'improvement',
        priority: 'high',
        message: 'Pratique sequências mais simples para fortalecer a base',
        activities: ['simple_sequences', 'counting_games']
      });
    }
    
    const accuracyByType = this.analyzeAccuracyByType(data);
    
    // Identificar tipos de sequência com baixa performance
    Object.keys(accuracyByType).forEach(type => {
      if (accuracyByType[type].accuracy < 0.5 && accuracyByType[type].attempts >= 2) {
        recommendations.push({
          type: 'specific_skill',
          priority: 'medium',
          message: `Pratique mais sequências do tipo: ${this.sequenceTypes[type] || type}`,
          activities: [`${type}_practice`, 'pattern_games']
        });
      }
    });
    
    const processingSpeed = this.assessProcessingSpeed(data);
    if (processingSpeed.speedCategory === 'slow') {
      recommendations.push({
        type: 'speed',
        priority: 'low',
        message: 'Tente resolver mais rapidamente - confie na sua primeira impressão',
        activities: ['speed_sequences', 'quick_pattern_games']
      });
    }
    
    return recommendations;
  }

  /**
   * Funções auxiliares
   */
  calculateOverallAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter(attempt => attempt.isCorrect).length;
    return correct / data.length;
  }

  groupBySequenceType(data) {
    const grouped = {};
    data.forEach(attempt => {
      const type = attempt.sequenceType || 'unknown';
      if (!grouped[type]) grouped[type] = [];
      grouped[type].push(attempt);
    });
    return grouped;
  }

  calculateAverageTime(data) {
    const times = data.map(attempt => attempt.responseTime).filter(Boolean);
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0;
  }

  calculateAccuracy(data) {
    if (!data || data.length === 0) return 0;
    const correct = data.filter(attempt => attempt.isCorrect).length;
    return correct / data.length;
  }

  calculateMedian(numbers) {
    const sorted = numbers.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? 
      (sorted[middle - 1] + sorted[middle]) / 2 : 
      sorted[middle];
  }

  calculateConsistency(data) {
    if (data.length < 2) return 0.5;
    
    const accuracies = this.divideIntoSegments(data, 3).map(segment => this.calculateAccuracy(segment));
    const variance = this.calculateVariance(accuracies);
    
    return Math.max(0, 1 - variance);
  }

  calculateVariance(numbers) {
    if (numbers.length === 0) return 0;
    const mean = numbers.reduce((a, b) => a + b, 0) / numbers.length;
    return numbers.reduce((sum, num) => sum + Math.pow(num - mean, 2), 0) / numbers.length;
  }

  divideIntoSegments(data, numSegments) {
    const segmentSize = Math.floor(data.length / numSegments);
    const segments = [];
    
    for (let i = 0; i < numSegments; i++) {
      const start = i * segmentSize;
      const end = i === numSegments - 1 ? data.length : (i + 1) * segmentSize;
      segments.push(data.slice(start, end));
    }
    
    return segments.filter(segment => segment.length > 0);
  }

  getDefaultAnalysis() {
    return {
      timestamp: new Date().toISOString(),
      collector: 'SequenceAnalysisCollector',
      version: '3.0.0',
      overallAccuracy: 0.5,
      sequentialSkillIndex: 0.5,
      processingSpeed: { speed: 'unknown', score: 0.5 },
      accuracyByType: {},
      solvingStrategies: {},
      cognitiveComplexity: {},
      temporalProgress: { trend: 'insufficient_data', improvement: 0 },
      errorPatterns: { totalErrors: 0, errorRate: 0 },
      cognitiveSkills: {},
      recommendations: [],
      metadata: { totalSequences: 0, uniqueTypes: 0 }
    };
  }
}
