/**
 * @file MCPIntegration.jsx
 * @description Componente de preparação para integração MCP/N8n
 * @version 3.0.0
 * @premium true
 */

import React, { useState, useEffect } from 'react'
import styles from './MCPIntegration.module.css'

const MCPIntegration = ({ onStatusChange, className }) => {
  const [mcpStatus, setMcpStatus] = useState('disconnected')
  const [mcpConfig, setMcpConfig] = useState({
    endpoint: '',
    apiKey: '',
    webhookUrl: '',
    enabled: false
  })
  const [testResults, setTestResults] = useState(null)
  const [isLoading, setIsLoading] = useState(false)

  // Configurações do MCP via environment variables
  const defaultMcpConfig = {
    endpoint: process.env.REACT_APP_MCP_ENDPOINT || 'https://your-n8n-instance.com/webhook/mcp-integration',
    knowledgeBaseUrl: process.env.REACT_APP_MCP_KNOWLEDGE_BASE_URL || 'https://your-n8n-instance.com/webhook/knowledge-base',
    apiKey: process.env.REACT_APP_MCP_API_KEY || '',
    enabled: process.env.REACT_APP_MCP_ENABLED === 'true',
    description: 'Endpoint para integração com N8n e base de conhecimento sobre TEA/TDAH',
    capabilities: [
      'Análise de dados comportamentais',
      'Recomendações terapêuticas',
      'Base de conhecimento TEA/TDAH',
      'Respostas contextualizadas',
      'Integração com dashboard'
    ]
  }

  useEffect(() => {
    // Priorizar configurações do environment, depois localStorage
    const envConfig = {
      endpoint: process.env.REACT_APP_MCP_ENDPOINT || '',
      apiKey: process.env.REACT_APP_MCP_API_KEY || '',
      webhookUrl: process.env.REACT_APP_MCP_KNOWLEDGE_BASE_URL || '',
      enabled: process.env.REACT_APP_MCP_ENABLED === 'true'
    }

    // Se há configurações no env, usar elas
    if (envConfig.endpoint) {
      setMcpConfig(envConfig)
      if (envConfig.enabled && envConfig.endpoint) {
        checkMcpConnection(envConfig)
      }
    } else {
      // Caso contrário, carregar do localStorage
      const savedConfig = localStorage.getItem('mcp_config')
      if (savedConfig) {
        try {
          const config = JSON.parse(savedConfig)
          setMcpConfig(config)
          if (config.enabled && config.endpoint) {
            checkMcpConnection(config)
          }
        } catch (error) {
          console.error('Erro ao carregar configuração MCP:', error)
        }
      }
    }
  }, [])

  useEffect(() => {
    // Notificar mudanças de status para componentes pai
    if (onStatusChange) {
      onStatusChange(mcpStatus, mcpConfig)
    }
  }, [mcpStatus, mcpConfig, onStatusChange])

  const checkMcpConnection = async (config = mcpConfig) => {
    if (!config.endpoint) {
      setMcpStatus('not_configured')
      return
    }

    setIsLoading(true)
    setMcpStatus('connecting')

    try {
      // Simular teste de conexão (substituir pela implementação real)
      const testPayload = {
        type: 'health_check',
        timestamp: new Date().toISOString(),
        source: 'portal_betina_dashboard'
      }

      // Na implementação real, fazer requisição HTTP para o endpoint MCP
      console.log('Testando conexão MCP:', config.endpoint, testPayload)
      
      // Simular resposta (remover quando implementar integração real)
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const mockResponse = {
        status: 'success',
        capabilities: defaultMcpConfig.capabilities,
        knowledgeBase: {
          tea_entries: 1247,
          tdah_entries: 893,
          strategies_count: 456,
          last_updated: new Date().toISOString()
        }
      }

      setTestResults(mockResponse)
      setMcpStatus('connected')
      
    } catch (error) {
      console.error('Erro na conexão MCP:', error)
      setMcpStatus('error')
      setTestResults({ error: error.message })
    } finally {
      setIsLoading(false)
    }
  }

  const saveMcpConfig = () => {
    try {
      localStorage.setItem('mcp_config', JSON.stringify(mcpConfig))
      checkMcpConnection()
    } catch (error) {
      console.error('Erro ao salvar configuração MCP:', error)
    }
  }

  const sendTestMessage = async () => {
    if (mcpStatus !== 'connected') return

    setIsLoading(true)
    try {
      const testMessage = {
        type: 'chat_query',
        message: 'Teste de integração do Portal Betina V3',
        context: {
          dashboard_data: {
            user_progress: '75%',
            session_count: 12,
            current_activities: ['memory_game', 'color_match']
          }
        },
        timestamp: new Date().toISOString()
      }

      // Na implementação real, enviar para o endpoint MCP
      console.log('Enviando mensagem de teste:', testMessage)
      
      // Simular resposta
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const mockResponse = {
        response: 'Conexão estabelecida com sucesso! Base de conhecimento TEA/TDAH carregada e pronta para responder sobre desenvolvimento neurodivergente.',
        confidence: 0.95,
        knowledge_sources: ['TEA Clinical Guidelines', 'TDAH Treatment Protocols', 'Neurodevelopment Research']
      }

      setTestResults(prev => ({
        ...prev,
        test_message: mockResponse
      }))

    } catch (error) {
      console.error('Erro no teste de mensagem:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = () => {
    switch (mcpStatus) {
      case 'connected': return '🟢'
      case 'connecting': return '🟡'
      case 'error': return '🔴'
      case 'not_configured': return '⚫'
      default: return '⚪'
    }
  }

  const getStatusText = () => {
    switch (mcpStatus) {
      case 'connected': return 'Conectado e Pronto'
      case 'connecting': return 'Conectando...'
      case 'error': return 'Erro de Conexão'
      case 'not_configured': return 'Não Configurado'
      default: return 'Desconectado'
    }
  }

  return (
    <div className={`${styles.mcpContainer} ${className || ''}`}>
      {/* Header */}
      <div className={styles.mcpHeader}>
        <h3 className={styles.mcpTitle}>
          <span className={styles.mcpIcon}>🔗</span>
          Integração MCP/N8n
        </h3>
        <div className={styles.statusBadge}>
          <span className={styles.statusIcon}>{getStatusIcon()}</span>
          <span className={styles.statusText}>{getStatusText()}</span>
        </div>
      </div>

      {/* Configuração */}
      <div className={styles.configSection}>
        <h4 className={styles.sectionTitle}>⚙️ Configuração</h4>
        
        {/* Informações sobre Environment Variables */}
        <div className={styles.envInfo}>
          <div className={styles.envStatus}>
            <strong>📋 Status das Variáveis de Ambiente:</strong>
            <div className={styles.envList}>
              <div className={`${styles.envItem} ${process.env.REACT_APP_MCP_ENDPOINT ? styles.envSet : styles.envNotSet}`}>
                <span>REACT_APP_MCP_ENDPOINT:</span>
                <span>{process.env.REACT_APP_MCP_ENDPOINT ? '✅ Configurado' : '❌ Não definido'}</span>
              </div>
              <div className={`${styles.envItem} ${process.env.REACT_APP_MCP_ENABLED ? styles.envSet : styles.envNotSet}`}>
                <span>REACT_APP_MCP_ENABLED:</span>
                <span>{process.env.REACT_APP_MCP_ENABLED || '❌ Não definido'}</span>
              </div>
              <div className={`${styles.envItem} ${process.env.REACT_APP_MCP_API_KEY ? styles.envSet : styles.envNotSet}`}>
                <span>REACT_APP_MCP_API_KEY:</span>
                <span>{process.env.REACT_APP_MCP_API_KEY ? '✅ Configurada' : '⚠️ Opcional'}</span>
              </div>
            </div>
          </div>
          
          <div className={styles.envInstructions}>
            <p><strong>💡 Para configurar via .env:</strong></p>
            <ol>
              <li>Edite o arquivo <code>.env</code> na raiz do projeto</li>
              <li>Defina <code>REACT_APP_MCP_ENDPOINT</code> com sua URL N8n</li>
              <li>Configure <code>REACT_APP_MCP_ENABLED=true</code></li>
              <li>Reinicie o servidor de desenvolvimento</li>
            </ol>
          </div>
        </div>
        
        <div className={styles.configForm}>
          <div className={styles.inputGroup}>
            <label className={styles.inputLabel}>Endpoint N8n:</label>
            <input
              type="url"
              value={mcpConfig.endpoint}
              onChange={(e) => setMcpConfig(prev => ({ ...prev, endpoint: e.target.value }))}
              placeholder={defaultMcpConfig.endpoint}
              className={styles.configInput}
              disabled={!!process.env.REACT_APP_MCP_ENDPOINT}
            />
            {process.env.REACT_APP_MCP_ENDPOINT && (
              <small className={styles.envNote}>🔒 Configurado via environment variable</small>
            )}
          </div>

          <div className={styles.inputGroup}>
            <label className={styles.inputLabel}>API Key (Opcional):</label>
            <input
              type="password"
              value={mcpConfig.apiKey}
              onChange={(e) => setMcpConfig(prev => ({ ...prev, apiKey: e.target.value }))}
              placeholder="Sua chave de API N8n"
              className={styles.configInput}
              disabled={!!process.env.REACT_APP_MCP_API_KEY}
            />
            {process.env.REACT_APP_MCP_API_KEY && (
              <small className={styles.envNote}>🔒 Configurada via environment variable</small>
            )}
          </div>

          <div className={styles.inputGroup}>
            <label className={styles.inputLabel}>Webhook URL (Opcional):</label>
            <input
              type="url"
              value={mcpConfig.webhookUrl}
              onChange={(e) => setMcpConfig(prev => ({ ...prev, webhookUrl: e.target.value }))}
              placeholder="URL para receber notificações"
              className={styles.configInput}
            />
          </div>

          <div className={styles.checkboxGroup}>
            <label className={styles.checkboxLabel}>
              <input
                type="checkbox"
                checked={mcpConfig.enabled}
                onChange={(e) => setMcpConfig(prev => ({ ...prev, enabled: e.target.checked }))}
                className={styles.checkbox}
              />
              Habilitar integração MCP
            </label>
          </div>

          <div className={styles.configActions}>
            <button 
              onClick={saveMcpConfig}
              className={styles.saveButton}
              disabled={isLoading}
            >
              💾 Salvar Configuração
            </button>
            
            <button 
              onClick={() => checkMcpConnection()}
              className={styles.testButton}
              disabled={isLoading || !mcpConfig.endpoint}
            >
              🔄 Testar Conexão
            </button>
          </div>
        </div>
      </div>

      {/* Capacidades */}
      <div className={styles.capabilitiesSection}>
        <h4 className={styles.sectionTitle}>🚀 Capacidades MCP</h4>
        <div className={styles.capabilitiesList}>
          {defaultMcpConfig.capabilities.map((capability, index) => (
            <div key={index} className={styles.capabilityItem}>
              <span className={styles.capabilityIcon}>✓</span>
              <span className={styles.capabilityText}>{capability}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Resultados do Teste */}
      {testResults && (
        <div className={styles.resultsSection}>
          <h4 className={styles.sectionTitle}>📊 Resultados do Teste</h4>
          
          {testResults.status === 'success' && (
            <div className={styles.successResults}>
              <div className={styles.resultItem}>
                <strong>Status:</strong> ✅ Conexão estabelecida
              </div>
              <div className={styles.resultItem}>
                <strong>Base de Conhecimento TEA:</strong> {testResults.knowledgeBase?.tea_entries || 0} entradas
              </div>
              <div className={styles.resultItem}>
                <strong>Base de Conhecimento TDAH:</strong> {testResults.knowledgeBase?.tdah_entries || 0} entradas
              </div>
              <div className={styles.resultItem}>
                <strong>Estratégias Disponíveis:</strong> {testResults.knowledgeBase?.strategies_count || 0}
              </div>
              
              {mcpStatus === 'connected' && (
                <button 
                  onClick={sendTestMessage}
                  className={styles.testMessageButton}
                  disabled={isLoading}
                >
                  💬 Enviar Mensagem de Teste
                </button>
              )}
            </div>
          )}

          {testResults.test_message && (
            <div className={styles.testMessageResult}>
              <h5>Resposta do Teste:</h5>
              <p>{testResults.test_message.response}</p>
              <div className={styles.responseMetadata}>
                <span>Confiança: {Math.round(testResults.test_message.confidence * 100)}%</span>
                <span>Fontes: {testResults.test_message.knowledge_sources?.length || 0}</span>
              </div>
            </div>
          )}

          {testResults.error && (
            <div className={styles.errorResults}>
              <strong>Erro:</strong> {testResults.error}
            </div>
          )}
        </div>
      )}

      {/* Instruções */}
      <div className={styles.instructionsSection}>
        <h4 className={styles.sectionTitle}>📖 Instruções de Setup</h4>
        <div className={styles.instructionsList}>
          <div className={styles.instructionStep}>
            <span className={styles.stepNumber}>1</span>
            <div className={styles.stepContent}>
              <strong>Configure seu N8n:</strong>
              <p>Crie um workflow no N8n com webhook para receber dados do dashboard</p>
            </div>
          </div>
          
          <div className={styles.instructionStep}>
            <span className={styles.stepNumber}>2</span>
            <div className={styles.stepContent}>
              <strong>Adicione a base de conhecimento:</strong>
              <p>Importe dados sobre TEA, TDAH e neurodivergência no seu MCP</p>
            </div>
          </div>
          
          <div className={styles.instructionStep}>
            <span className={styles.stepNumber}>3</span>
            <div className={styles.stepContent}>
              <strong>Configure o endpoint:</strong>
              <p>Cole o URL do webhook N8n no campo "Endpoint N8n" acima</p>
            </div>
          </div>
          
          <div className={styles.instructionStep}>
            <span className={styles.stepNumber}>4</span>
            <div className={styles.stepContent}>
              <strong>Teste a integração:</strong>
              <p>Use os botões "Testar Conexão" e "Enviar Mensagem de Teste"</p>
            </div>
          </div>
        </div>
      </div>

      {/* Status Footer */}
      <div className={styles.statusFooter}>
        <div className={styles.integrationInfo}>
          <span className={styles.integrationIcon}>🔗</span>
          <div>
            <strong>MCP Integration Ready</strong>
            <p>Preparado para conectar com sua instância N8n</p>
          </div>
        </div>
        {isLoading && (
          <div className={styles.loadingIndicator}>
            <span className={styles.spinner}></span>
            <span>Processando...</span>
          </div>
        )}
      </div>
    </div>
  )
}

export default MCPIntegration
