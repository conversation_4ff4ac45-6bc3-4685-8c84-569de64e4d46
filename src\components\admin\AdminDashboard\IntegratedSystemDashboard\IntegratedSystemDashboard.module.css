/**
 * @file IntegratedSystemDashboard.module.css
 * @description Estilos modulares para Dashboard do Sistema Integrado (Admin)
 * @version 3.0.0
 */

/* Container principal do dashboard */
.dashboardContainer {
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

/* Header do dashboard */
.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.dashboardTitle {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleIcon {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  padding: 8px;
  border-radius: 8px;
  color: white;
  font-size: 20px;
}

.adminBadge {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Controles do dashboard */
.dashboardControls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.refreshButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.lastUpdate {
  font-size: 12px;
  color: #718096;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Grid de métricas do sistema */
.systemMetricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.systemMetricCard {
  background: linear-gradient(135deg, #ffffff 0%, #f7fafc 100%);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.systemMetricCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.systemMetricCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.systemMetricCard.users::before {
  background: linear-gradient(90deg, #4299e1, #3182ce);
}

.systemMetricCard.sessions::before {
  background: linear-gradient(90deg, #48bb78, #38a169);
}

.systemMetricCard.performance::before {
  background: linear-gradient(90deg, #ed8936, #dd6b20);
}

.systemMetricCard.storage::before {
  background: linear-gradient(90deg, #9f7aea, #805ad5);
}

.systemMetricCard.errors::before {
  background: linear-gradient(90deg, #f56565, #e53e3e);
}

.systemMetricHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.systemMetricTitle {
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  margin: 0;
}

.systemMetricIcon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.systemMetricIcon.users {
  background: linear-gradient(135deg, #4299e1, #3182ce);
}

.systemMetricIcon.sessions {
  background: linear-gradient(135deg, #48bb78, #38a169);
}

.systemMetricIcon.performance {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.systemMetricIcon.storage {
  background: linear-gradient(135deg, #9f7aea, #805ad5);
}

.systemMetricIcon.errors {
  background: linear-gradient(135deg, #f56565, #e53e3e);
}

.systemMetricValue {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 8px 0;
  line-height: 1;
}

.systemMetricUnit {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.systemMetricTrend {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 8px;
}

.trendPositive {
  color: #48bb78;
}

.trendNegative {
  color: #f56565;
}

.trendNeutral {
  color: #718096;
}

/* Seção de monitoramento em tempo real */
.realtimeSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.realtimeHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.realtimeTitle {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.realtimeStatus {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #48bb78;
  font-weight: 500;
}

.statusIndicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #48bb78;
  animation: pulse 2s infinite;
}

.statusIndicator.offline {
  background: #f56565;
}

.statusIndicator.warning {
  background: #ed8936;
}

.realtimeGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.realtimeCard {
  background: #f7fafc;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.realtimeCardTitle {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.realtimeValue {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 4px 0;
}

.realtimeDescription {
  font-size: 12px;
  color: #718096;
}

/* Grid de gráficos do sistema */
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chartCard {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.chartTitle {
  font-size: 18px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chartContainer {
  position: relative;
  height: 300px;
}

.chartContainer.line {
  height: 280px;
}

.chartContainer.bar {
  height: 320px;
}

.chartContainer.doughnut {
  height: 250px;
}

/* Seção de logs do sistema */
.logsSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.logsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.logsTitle {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.logsFilters {
  display: flex;
  gap: 8px;
}

.logLevelFilter {
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logLevelFilter.active {
  background: #e53e3e;
  color: white;
  border-color: #e53e3e;
}

.logLevelFilter:hover {
  border-color: #e53e3e;
}

.logsList {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.logItem {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  display: flex;
  gap: 12px;
}

.logItem:last-child {
  border-bottom: none;
}

.logTimestamp {
  color: #718096;
  white-space: nowrap;
  font-weight: 500;
}

.logLevel {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

.logLevel.info {
  background: #bee3f8;
  color: #2c5282;
}

.logLevel.warning {
  background: #fbd38d;
  color: #c05621;
}

.logLevel.error {
  background: #fed7d7;
  color: #c53030;
}

.logLevel.debug {
  background: #e6fffa;
  color: #234e52;
}

.logMessage {
  flex: 1;
  color: #2d3748;
  word-break: break-word;
}

/* Seção de alertas do sistema */
.alertsSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.alertsTitle {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
}

.alertsList {
  display: grid;
  gap: 12px;
}

.alertItem {
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.alertItem.critical {
  background: #fed7d7;
  border-left-color: #e53e3e;
}

.alertItem.warning {
  background: #fbd38d;
  border-left-color: #ed8936;
}

.alertItem.info {
  background: #bee3f8;
  border-left-color: #4299e1;
}

.alertIcon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.alertIcon.critical {
  background: #e53e3e;
}

.alertIcon.warning {
  background: #ed8936;
}

.alertIcon.info {
  background: #4299e1;
}

.alertContent {
  flex: 1;
}

.alertTitle {
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 4px 0;
}

.alertMessage {
  font-size: 13px;
  color: #4a5568;
  line-height: 1.4;
  margin-bottom: 4px;
}

.alertTime {
  font-size: 11px;
  color: #718096;
}

/* Estados de loading e erro */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 16px;
}

.loadingText {
  color: #4a5568;
  font-size: 16px;
}

.errorContainer {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 16px;
    margin-bottom: 16px;
  }

  .dashboardHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .dashboardControls {
    justify-content: center;
  }

  .dashboardTitle {
    font-size: 24px;
    text-align: center;
  }

  .systemMetricsGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .chartsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chartContainer {
    height: 250px;
  }

  .realtimeGrid {
    grid-template-columns: 1fr;
  }

  .logsFilters {
    flex-wrap: wrap;
  }

  .logItem {
    flex-direction: column;
    gap: 8px;
  }

  .alertItem {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .dashboardContainer {
    padding: 12px;
  }

  .dashboardTitle {
    font-size: 20px;
  }

  .systemMetricValue {
    font-size: 28px;
  }

  .chartContainer {
    height: 200px;
  }

  .systemMetricsGrid {
    grid-template-columns: 1fr;
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.dashboardContainer {
  animation: fadeIn 0.5s ease-out;
}

.systemMetricCard {
  animation: slideDown 0.6s ease-out;
}

.chartCard,
.realtimeSection,
.logsSection,
.alertsSection {
  animation: fadeIn 0.6s ease-out;
}

.logItem,
.alertItem {
  animation: slideDown 0.3s ease-out;
}

/* Acessibilidade */
.refreshButton:focus-visible,
.logLevelFilter:focus-visible {
  outline: 2px solid #e53e3e;
  outline-offset: 2px;
}

/* Tema escuro (futuro) */
@media (prefers-color-scheme: dark) {
  .dashboardContainer {
    background-color: #1a202c;
    color: #e2e8f0;
  }

  .realtimeSection,
  .logsSection,
  .alertsSection,
  .chartCard {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .systemMetricCard {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-color: #4a5568;
  }

  .dashboardTitle,
  .realtimeTitle,
  .logsTitle,
  .alertsTitle,
  .chartTitle,
  .systemMetricValue {
    color: #e2e8f0;
  }

  .realtimeCard {
    background-color: #4a5568;
    border-color: #718096;
  }

  .logsList {
    background-color: #4a5568;
    border-color: #718096;
  }

  .logItem {
    border-color: #718096;
  }

  .logLevelFilter {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }
}
