/**
 * 🎨 COLOR PATTERN COLLECTOR - Padr<PERSON><PERSON> Visuais
 * Coleta especializada de dados sobre padrões de cores
 * Portal Betina V3
 */

export class ColorPatternCollector {
  constructor() {
    this.collectorId = 'color_pattern';
    this.isActive = true;
    this.colorMetrics = {
      colorRecognition: [],
      colorSequences: [],
      colorDiscrimination: [],
      colorMemory: [],
      colorAssociation: []
    };
  }
  
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Método principal de análise
   * @param {Object} data - Dados do jogo
   * @returns {Object} - Análise de padrões de cores
   */
  async analyze(data) {
    try {
      if (!data || !data.gameData) {
        console.warn('ColorPatternCollector: Dados incompletos recebidos');
        return {
          colorRecognitionAccuracy: 0.5,
          colorSequenceSkills: 'medium',
          colorDiscrimination: 0.5,
          colorMemoryCapacity: 0.5,
          colorAssociationSkills: 0.5,
          overallColorScore: 0.5
        };
      }

      const colorData = this.collectColorData(data.gameData, data.playerBehavior);
      
      return {
        colorRecognitionAccuracy: this.calculateColorRecognition(colorData),
        colorSequenceSkills: this.assessColorSequenceSkills(colorData),
        colorDiscrimination: this.assessColorDiscrimination(colorData),
        colorMemoryCapacity: this.assessColorMemory(colorData),
        colorAssociationSkills: this.assessColorAssociation(colorData),
        overallColorScore: this.calculateOverallColorScore(colorData)
      };
    } catch (error) {
      console.error('Erro no ColorPatternCollector.analyze:', error);
      return {
        colorRecognitionAccuracy: 0.5,
        colorSequenceSkills: 'medium',
        colorDiscrimination: 0.5,
        colorMemoryCapacity: 0.5,
        colorAssociationSkills: 0.5,
        overallColorScore: 0.5
      };
    }
  }

  collectColorData(gameData, playerBehavior) {
    return {
      timestamp: Date.now(),
      sessionId: gameData.sessionId,
      colorInteractions: this.analyzeColorInteractions(gameData, playerBehavior),
      sequenceData: this.analyzeColorSequences(gameData, playerBehavior),
      discriminationData: this.analyzeColorDiscrimination(gameData, playerBehavior),
      memoryData: this.analyzeColorMemory(gameData, playerBehavior),
      associationData: this.analyzeColorAssociation(gameData, playerBehavior)
    };
  }

  analyzeColorInteractions(gameData, playerBehavior) {
    return {
      accuracy: playerBehavior?.accuracy || 0.5,
      responseTime: playerBehavior?.responseTime || 1000,
      colorChoices: gameData?.colorChoices || [],
      correctColors: gameData?.correctColors || []
    };
  }

  analyzeColorSequences(gameData, playerBehavior) {
    return {
      sequenceAccuracy: 0.5,
      sequenceLength: gameData?.sequence?.length || 3,
      sequenceComplexity: 0.5,
      sequenceSpeed: 0.5
    };
  }

  analyzeColorDiscrimination(gameData, playerBehavior) {
    return {
      discriminationAccuracy: 0.5,
      discriminationSensitivity: 0.5,
      discriminationSpeed: 0.5
    };
  }

  analyzeColorMemory(gameData, playerBehavior) {
    return {
      shortTermColorMemory: 0.5,
      colorMemoryCapacity: 0.5,
      colorMemoryRetention: 0.5
    };
  }

  analyzeColorAssociation(gameData, playerBehavior) {
    return {
      colorMeaning: 0.5,
      colorEmotion: 0.5,
      colorCategorization: 0.5
    };
  }

  calculateColorRecognition(colorData) {
    return colorData.colorInteractions?.accuracy || 0.5;
  }

  assessColorSequenceSkills(colorData) {
    const score = colorData.sequenceData?.sequenceAccuracy || 0.5;
    if (score >= 0.8) return 'high';
    if (score >= 0.6) return 'medium';
    return 'low';
  }

  assessColorDiscrimination(colorData) {
    return colorData.discriminationData?.discriminationAccuracy || 0.5;
  }

  assessColorMemory(colorData) {
    return colorData.memoryData?.colorMemoryCapacity || 0.5;
  }

  assessColorAssociation(colorData) {
    return colorData.associationData?.colorCategorization || 0.5;
  }

  calculateOverallColorScore(colorData) {
    const recognition = this.calculateColorRecognition(colorData);
    const discrimination = this.assessColorDiscrimination(colorData);
    const memory = this.assessColorMemory(colorData);
    const association = this.assessColorAssociation(colorData);
    
    return (recognition + discrimination + memory + association) / 4;
  }

  updateColorMetrics(colorData) {
    this.colorMetrics.colorRecognition.push(colorData.colorInteractions);
    this.colorMetrics.colorSequences.push(colorData.sequenceData);
    this.colorMetrics.colorDiscrimination.push(colorData.discriminationData);
    this.colorMetrics.colorMemory.push(colorData.memoryData);
    this.colorMetrics.colorAssociation.push(colorData.associationData);
  }

  getColorMetrics() {
    return this.colorMetrics;
  }

  reset() {
    this.colorMetrics = {
      colorRecognition: [],
      colorSequences: [],
      colorDiscrimination: [],
      colorMemory: [],
      colorAssociation: []
    };
  }
}
