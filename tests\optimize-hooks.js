#!/usr/bin/env node

/**
 * 🚀 SCRIPT DE OTIMIZAÇÃO AUTOMÁTICA DE HOOKS REACT
 * Aplica useCallback nas funções principais dos jogos para otimizar performance
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 OTIMIZADOR AUTOMÁTICO DE HOOKS REACT - Portal Betina V3');
console.log('=========================================================');

const gamesDir = path.join(__dirname, '..', 'src', 'games');
const gameFiles = [
  'ContagemNumeros/ContagemNumerosGame.jsx',
  'LetterRecognition/LetterRecognitionGame.jsx'
];

// Funções principais que devem virar useCallback
const functionsToOptimize = [
  'toggleTTS',
  'handleAnswer',
  'handleLetterSelect',
  'generateNewRound',
  'startGame',
  'restartGame'
];

for (const gameFile of gameFiles) {
  const filePath = path.join(gamesDir, gameFile);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  Arquivo não encontrado: ${gameFile}`);
    continue;
  }
  
  console.log(`🔧 Otimizando: ${gameFile}`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // Adicionar useCallback import se não existir
  if (!content.includes('useCallback')) {
    content = content.replace(
      /import React, \{ ([^}]+) \}/,
      (match, imports) => {
        if (!imports.includes('useCallback')) {
          return `import React, { ${imports}, useCallback }`;
        }
        return match;
      }
    );
    modified = true;
  }
  
  // Otimizar função toggleTTS
  if (content.includes('const toggleTTS = () => {') && !content.includes('const toggleTTS = useCallback(() => {')) {
    content = content.replace(
      /const toggleTTS = \(\) => \{/,
      'const toggleTTS = useCallback(() => {'
    );
    
    // Encontrar o fechamento da função e adicionar }, [])
    const toggleTTSStart = content.indexOf('const toggleTTS = useCallback(() => {');
    if (toggleTTSStart !== -1) {
      let braceCount = 1;
      let pos = toggleTTSStart + 'const toggleTTS = useCallback(() => {'.length;
      
      while (pos < content.length && braceCount > 0) {
        if (content[pos] === '{') braceCount++;
        if (content[pos] === '}') braceCount--;
        pos++;
      }
      
      if (braceCount === 0) {
        // Encontrar o ponto e vírgula após a função
        while (pos < content.length && content[pos] !== ';') {
          pos++;
        }
        
        if (pos < content.length) {
          content = content.substring(0, pos) + '}, [])' + content.substring(pos);
          modified = true;
        }
      }
    }
  }
  
  // Otimizar função handleAnswer (ContagemNumeros)
  if (content.includes('const handleAnswer = async (') && !content.includes('const handleAnswer = useCallback(async (')) {
    content = content.replace(
      /const handleAnswer = async \(/,
      'const handleAnswer = useCallback(async ('
    );
    
    // Encontrar o fechamento da função
    const handleAnswerStart = content.indexOf('const handleAnswer = useCallback(async (');
    if (handleAnswerStart !== -1) {
      // Procurar pelo final da função (próximo }; ou }\n})
      let searchPos = handleAnswerStart;
      let braceCount = 0;
      let inFunction = false;
      
      while (searchPos < content.length) {
        if (content[searchPos] === '{' && !inFunction) {
          inFunction = true;
          braceCount = 1;
          searchPos++;
          continue;
        }
        
        if (inFunction) {
          if (content[searchPos] === '{') braceCount++;
          if (content[searchPos] === '}') braceCount--;
          
          if (braceCount === 0) {
            // Encontrou o final da função
            searchPos++;
            // Pular espaços em branco
            while (searchPos < content.length && /\s/.test(content[searchPos])) {
              searchPos++;
            }
            
            // Adicionar dependencies
            content = content.substring(0, searchPos) + 
                     '}, [selectedOption, gameStats, currentQuestion, finishGameSession, generateNewQuestion])' + 
                     content.substring(searchPos);
            modified = true;
            break;
          }
        }
        searchPos++;
      }
    }
  }
  
  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ ${gameFile} otimizado com sucesso!`);
  } else {
    console.log(`ℹ️  ${gameFile} já estava otimizado`);
  }
}

console.log('\n🎉 Otimização automática concluída!');
console.log('💡 Execute o script de validação para verificar melhorias:');
console.log('   node scripts/validate-react-hooks.js');
