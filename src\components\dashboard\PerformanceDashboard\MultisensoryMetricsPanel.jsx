/**
 * @file MultisensoryMetricsPanel.jsx
 * @description Painel para visualização de métricas multissensoriais no PerformanceDashboard
 * @version 1.1.0 - Enhanced with Tailwind CSS and improved error handling
 */

import React, { useState, useEffect, useCallback, memo } from 'react';
import PropTypes from 'prop-types';
import { 
  Chart as ChartJS, 
  RadialLinearScale, 
  PointElement, 
  LineElement, 
  Filler, 
  Tooltip, 
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
} from 'chart.js';
import { Radar, Line, Bar } from 'react-chartjs-2';
import styles from './styles.module.css';

// Register Chart.js components
ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

/**
 * Componente para exibição de métricas multissensoriais
 */
const MultisensoryMetricsPanel = memo(({ userId, gameType, sessionData }) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [metricsData, setMetricsData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [retryCount, setRetryCount] = useState(0);
  const [deviceSensors, setDeviceSensors] = useState({
    accelerometer: false,
    gyroscope: false,
    orientation: false,
    touch: false
  });

  const MAX_RETRIES = 3;

  // Verificar sensores disponíveis no dispositivo
  useEffect(() => {
    const checkDeviceSensors = () => {
      const sensors = {
        accelerometer: 'DeviceMotionEvent' in window,
        gyroscope: 'DeviceOrientationEvent' in window,
        orientation: screen.orientation !== undefined,
        touch: 'ontouchstart' in window || navigator.maxTouchPoints > 0
      };
      setDeviceSensors(sensors);
    };

    checkDeviceSensors();
  }, []);

  // Carregar dados multissensoriais
  const loadMultisensoryData = useCallback(async () => {
    if (!userId) return;

    try {
      setIsLoading(true);
      setError(null);

      // Use sessionData if provided (dados reais da sessão)
      if (sessionData?.sensorMetrics) {
        setMetricsData(sessionData);
        return;
      }

      // Tentar buscar dados reais do MultisensoryMetricsCollector
      try {
        // Importar o coletor de métricas multissensoriais
        const { MultisensoryMetricsCollector } = await import('../../../api/services/multisensoryAnalysis/multisensoryMetrics.js');
        const collector = MultisensoryMetricsCollector.getInstance ? 
                          MultisensoryMetricsCollector.getInstance() : 
                          new MultisensoryMetricsCollector();
        
        // Buscar métricas do usuário específico
        const userMetrics = await collector.getUserMetrics?.(userId, gameType) || 
                           await collector.getMetricsForUser?.(userId, gameType) ||
                           collector.getSessionData?.(userId, gameType);
        
        if (userMetrics && Object.keys(userMetrics).length > 0) {
          // Adicionar timestamp para controle de cache
          userMetrics.timestamp = new Date().toISOString();
          setMetricsData(userMetrics);
          
          // Salvar dados reais no localStorage com timestamp
          localStorage.setItem(
            `multisensory_real_${userId}_${gameType || 'all'}`, 
            JSON.stringify(userMetrics)
          );
          return;
        }
      } catch (collectorError) {
        console.warn('MultisensoryMetricsCollector não disponível:', collectorError);
      }

      // Tentar buscar dados reais do AI Brain
      try {
        const { AIBrainOrchestrator } = await import('../../../api/services/ai/AIBrainOrchestrator.js');
        const aiBrain = new AIBrainOrchestrator();
        
        // Buscar métricas multissensoriais reais do usuário
        const realMetrics = await aiBrain.getMultisensoryMetrics(userId, gameType);
        
        if (realMetrics && Object.keys(realMetrics).length > 0) {
          realMetrics.timestamp = new Date().toISOString();
          setMetricsData(realMetrics);
          return;
        }
      } catch (aiBrainError) {
        console.warn('AI Brain não disponível:', aiBrainError);
      }

      // Tentar buscar do localStorage apenas se foram dados reais salvos anteriormente
      const cachedData = localStorage.getItem(`multisensory_real_${userId}_${gameType || 'all'}`);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        // Verificar se os dados não são muito antigos (ex: máximo 1 hora)
        const dataAge = Date.now() - new Date(parsedData.timestamp || 0).getTime();
        if (dataAge < 3600000) { // 1 hora em ms
          setMetricsData(parsedData);
          return;
        }
      }

      // Se não há dados reais disponíveis, definir como null
      setMetricsData(null);
      setError('Nenhum dado multissensorial real encontrado. Execute alguns jogos com sensores habilitados para gerar dados.');
      
    } catch (err) {
      console.error('Erro ao carregar dados multissensoriais:', err);
      if (retryCount < MAX_RETRIES) {
        setRetryCount(prev => prev + 1);
        setTimeout(() => loadMultisensoryData(), 2000);
      } else {
        setError('Falha ao carregar dados após várias tentativas');
      }
    } finally {
      setIsLoading(false);
    }
  }, [userId, gameType, sessionData, retryCount]);

  useEffect(() => {
    const controller = new AbortController();
    loadMultisensoryData();

    return () => controller.abort();
  }, [loadMultisensoryData]);

  const handleTabChange = useCallback((newValue) => {
    setCurrentTab(newValue);
  }, []);

  if (!metricsData && !isLoading) {
    const availableSensors = Object.values(deviceSensors).filter(Boolean).length;
    const totalSensors = Object.keys(deviceSensors).length;
    
    return (
      <div className={styles.metricsPanelRoot} role="region" aria-label="Painel de métricas multissensoriais">
        <div className={styles.metricsHeader}>
          <h3 className={styles.metricsTitle}>Métricas Multissensoriais</h3>
        </div>
        <div className={styles.metricsDivider}></div>
        <div className={styles.metricsEmptyState}>
          <span className={styles.icon} role="img" aria-label="Ícone de dispositivo">📱</span>
          <p style={{ marginTop: '12px', fontSize: '16px', fontWeight: '600' }}>
            Aguardando dados multissensoriais reais
          </p>
          <p style={{ marginTop: '8px', fontSize: '14px', color: '#64748b', textAlign: 'center', maxWidth: '400px' }}>
            {error || 'Execute jogos com sensores multissensoriais habilitados para gerar dados de interação. O sistema coleta automaticamente dados de acelerômetro, giroscópio, toque avançado e orientação durante as sessões de jogo.'}
          </p>
          
          <div style={{ marginTop: '16px', padding: '12px', backgroundColor: '#f8fafc', borderRadius: '8px', fontSize: '14px' }}>
            <p style={{ fontWeight: '600', marginBottom: '8px' }}>
              📊 Status dos Sensores do Dispositivo: {availableSensors}/{totalSensors}
            </p>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '4px', fontSize: '13px' }}>
              <span style={{ color: deviceSensors.accelerometer ? '#22c55e' : '#ef4444' }}>
                {deviceSensors.accelerometer ? '✅' : '❌'} Acelerômetro
              </span>
              <span style={{ color: deviceSensors.gyroscope ? '#22c55e' : '#ef4444' }}>
                {deviceSensors.gyroscope ? '✅' : '❌'} Giroscópio
              </span>
              <span style={{ color: deviceSensors.orientation ? '#22c55e' : '#ef4444' }}>
                {deviceSensors.orientation ? '✅' : '❌'} Orientação
              </span>
              <span style={{ color: deviceSensors.touch ? '#22c55e' : '#ef4444' }}>
                {deviceSensors.touch ? '✅' : '❌'} Touch Avançado
              </span>
            </div>
          </div>
          
          <button
            onClick={loadMultisensoryData}
            className={styles.metricsButton}
            disabled={isLoading}
            aria-label="Verificar novamente por dados reais"
          >
            Verificar dados reais
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.metricsPanelRoot} role="region" aria-label="Painel de métricas multissensoriais">
      {isLoading && (
        <div className={styles.metricsLoadingOverlay}>
          <div className={styles.metricsSpinner}></div>
        </div>
      )}

      <div className={styles.metricsHeader}>
        <h3 className={styles.metricsTitle}>Métricas Multissensoriais</h3>
        <button
          onClick={() => alert(`
🔬 SISTEMA DE MÉTRICAS MULTISSENSORIAIS

Este painel exibe dados REAIS coletados durante suas sessões de jogo:

📱 SENSORES MONITORADOS:
• Acelerômetro: movimentos do dispositivo
• Giroscópio: rotação e orientação  
• Touch avançado: pressão e precisão de toque
• Orientação: mudanças de posição da tela

📊 MÉTRICAS ANALISADAS:
• Precisão de toque e tempo de reação
• Estabilidade no manuseio do dispositivo
• Consistência nos movimentos
• Coordenação motora fina

🎯 COMO GERAR DADOS:
1. Jogue qualquer um dos games disponíveis
2. Mantenha os sensores do dispositivo habilitados
3. Os dados são coletados automaticamente durante o jogo
4. As métricas aparecem aqui após algumas sessões

⚡ INTEGRAÇÃO COM AI BRAIN:
Os dados são processados pelo sistema de IA para gerar insights sobre padrões motores e desenvolvimento neurocognitivo.

❗ IMPORTANTE: Este sistema usa apenas dados reais - não há simulações ou dados fictícios.
          `)}
          className={styles.metricsButtonSecondary}
          aria-label="Informações sobre métricas"
        >
          <span className={styles.icon} role="img" aria-label="Ícone de informação">ℹ️</span>
          Sobre estas métricas
        </button>
      </div>

      <div className={styles.metricsDivider}></div>

      <div className={styles.metricsTabs} role="tablist">
        {['Visão Geral', 'Interação', 'Sensores'].map((tab, index) => (
          <button
            key={tab}
            className={`${styles.metricsTab} ${currentTab === index ? styles.active : ''}`}
            onClick={() => handleTabChange(index)}
            role="tab"
            aria-selected={currentTab === index}
            aria-controls={`panel-${index}`}
          >
            <span className={styles.icon} role="img" aria-label={`Ícone de ${tab}`}>
              {index === 0 ? '📊' : index === 1 ? '👆' : '📱'}
            </span>
            {tab}
          </button>
        ))}
      </div>

      <div id={`panel-${currentTab}`} role="tabpanel" className={styles.tabContent}>
        {currentTab === 0 && (
          <div>
            <h4 style={{ fontSize: '18px', fontWeight: '500', color: '#1f2937', marginBottom: '16px' }}>Resumo de Métricas Multissensoriais</h4>
            <div className={styles.metricsGrid}>
              <MetricCard title="Sessões" value={metricsData?.summary?.sessions || 0} suffix="total" />
              <MetricCard title="Pontos de Dados" value={metricsData?.summary?.dataPoints || 0} suffix="coletados" />
              <MetricCard title="Sensores Disponíveis" value={metricsData?.summary?.sensorsAvailable || 0} suffix="de 4" />
              <MetricCard title="Estabilidade" value={metricsData?.deviceHandling?.stability || 0} suffix="%" color="#3b82f6" />
            </div>
            {metricsData?.aggregatedMetrics && (
              <div className={styles.metricsChart}>
                <p style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>Métricas de Interação Agregadas</p>
                <Radar
                  data={prepareRadarData(metricsData.aggregatedMetrics)}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: { stepSize: 20 }
                      }
                    },
                    plugins: {
                      legend: { position: 'top' },
                      tooltip: { mode: 'index' }
                    }
                  }}
                />
              </div>
            )}
          </div>
        )}

        {currentTab === 1 && (
          <div>
            <h4 style={{ fontSize: '18px', fontWeight: '500', color: '#1f2937', marginBottom: '16px' }}>Métricas de Interação</h4>
            <div className={styles.metricsGrid}>
              <MetricCard title="Precisão de Toque" value={metricsData?.touchInteractions?.accuracy || 0} suffix="%" />
              <MetricCard title="Tempo de Reação" value={metricsData?.touchInteractions?.reactionTime || 0} suffix="ms" />
              <MetricCard title="Consistência" value={metricsData?.touchInteractions?.consistency || 0} suffix="%" />
              <MetricCard title="Controle Fino" value={metricsData?.touchInteractions?.fineControl || 0} suffix="pts" />
            </div>
            <div className={styles.metricsInfoBox}>
              <p style={{ fontSize: '14px', color: '#374151', marginBottom: '8px' }}>
                <span className={styles.icon} role="img" aria-label="Ícone de informação">ℹ️</span>
                As métricas de interação são baseadas na análise de padrões de toque, pressão e tempo de resposta durante as atividades.
              </p>
              <p style={{ fontSize: '14px', color: '#6b7280' }}>
                Uma maior consistência e precisão de toque podem indicar melhor coordenação motora fina.
              </p>
            </div>
            {metricsData?.touchInteractions?.history && (
              <div className={styles.metricsChart}>
                <p style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>Evolução da Precisão de Toque</p>
                <Line
                  data={prepareLineData(metricsData.touchInteractions.history, 'Precisão (%)')}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true,
                        max: 100
                      }
                    },
                    plugins: {
                      legend: { position: 'top' },
                      tooltip: { mode: 'index' }
                    }
                  }}
                />
              </div>
            )}
          </div>
        )}

        {currentTab === 2 && (
          <div>
            <h4 style={{ fontSize: '18px', fontWeight: '500', color: '#1f2937', marginBottom: '16px' }}>Métricas de Sensores</h4>
            <div className={styles.metricsGrid}>
              <MetricCard
                title="Acelerômetro"
                value={metricsData?.deviceSensors?.accelerometer ? 'Ativo' : 'Inativo'}
                color={metricsData?.deviceSensors?.accelerometer ? '#22c55e' : '#64748b'}
              />
              <MetricCard
                title="Giroscópio"
                value={metricsData?.deviceSensors?.gyroscope ? 'Ativo' : 'Inativo'}
                color={metricsData?.deviceSensors?.gyroscope ? '#22c55e' : '#64748b'}
              />
              <MetricCard
                title="Orientação"
                value={metricsData?.deviceSensors?.orientation ? 'Ativo' : 'Inativo'}
                color={metricsData?.deviceSensors?.orientation ? '#22c55e' : '#64748b'}
              />
              <MetricCard
                title="Touch Avançado"
                value={metricsData?.deviceSensors?.advancedTouch ? 'Ativo' : 'Inativo'}
                color={metricsData?.deviceSensors?.advancedTouch ? '#22c55e' : '#64748b'}
              />
            </div>
            <div className={styles.metricsInfoBox}>
              <p style={{ fontSize: '14px', color: '#374151', marginBottom: '8px' }}>
                <span className={styles.icon} role="img" aria-label="Ícone de informação">ℹ️</span>
                Os sensores disponíveis dependem do dispositivo utilizado. Nem todos os dispositivos possuem todos os sensores.
              </p>
              <p style={{ fontSize: '14px', color: '#6b7280' }}>
                Para uma experiência multissensorial completa, recomenda-se o uso de um dispositivo com acelerômetro e giroscópio.
              </p>
            </div>
            {metricsData?.deviceHandling?.steadiness && (
              <div className={styles.metricsChart}>
                <p style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>Estabilidade de Manuseio do Dispositivo</p>
                <Bar
                  data={prepareBarData(metricsData.deviceHandling.steadiness, 'Estabilidade')}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      y: {
                        beginAtZero: true
                      }
                    },
                    plugins: {
                      legend: { position: 'top' },
                      tooltip: { mode: 'index' }
                    }
                  }}
                />
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

/**
 * Componente para cartão de métrica individual
 */
const MetricCard = ({ title, value, suffix = '', color = 'inherit' }) => {
  return (
    <div className={styles.metricCard}>
      <div className={styles.metricLabel}>{title}</div>
      <div className={styles.metricValue} style={{ color: color !== 'inherit' ? color : undefined }}>
        {value} {suffix && <span style={{ fontSize: '14px', fontWeight: 'normal' }}>{suffix}</span>}
      </div>
    </div>
  );
};

/**
 * Preparar dados para o gráfico radar
 */
const prepareRadarData = (metrics) => {
  if (!metrics) return { labels: [], datasets: [] };
  return {
    labels: ['Precisão', 'Tempo de Reação', 'Controle', 'Consistência', 'Coordenação'],
    datasets: [
      {
        label: 'Usuário',
        data: [
          metrics.accuracy,
          metrics.reactionTime,
          metrics.control,
          metrics.consistency,
          metrics.coordination
        ],
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2,
      },
      {
        label: 'Média',
        data: [
          metrics.avgAccuracy,
          metrics.avgReactionTime,
          metrics.avgControl,
          metrics.avgConsistency,
          metrics.avgCoordination
        ],
        backgroundColor: 'rgba(148, 163, 184, 0.2)',
        borderColor: 'rgba(148, 163, 184, 1)',
        borderWidth: 2,
      }
    ]
  };
};

/**
 * Preparar dados para gráfico de linha
 */
const prepareLineData = (history, label) => {
  return {
    labels: history.map(item => item.date),
    datasets: [
      {
        label,
        data: history.map(item => item.value),
        fill: false,
        backgroundColor: 'rgba(59, 130, 246, 0.2)',
        borderColor: 'rgba(59, 130, 246, 1)',
        tension: 0.4
      }
    ]
  };
};

/**
 * Preparar dados para gráfico de barras
 */
const prepareBarData = (data, label) => {
  return {
    labels: Object.keys(data),
    datasets: [
      {
        label,
        data: Object.values(data),
        backgroundColor: [
          'rgba(59, 130, 246, 0.7)',
          'rgba(34, 197, 94, 0.7)',
          'rgba(239, 68, 68, 0.7)',
          'rgba(168, 85, 247, 0.7)',
        ],
        borderWidth: 1
      }
    ]
  };
};

MultisensoryMetricsPanel.propTypes = {
  userId: PropTypes.string.isRequired,
  gameType: PropTypes.string,
  sessionData: PropTypes.object
};

MetricCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  suffix: PropTypes.string,
  color: PropTypes.string
};

export default MultisensoryMetricsPanel;