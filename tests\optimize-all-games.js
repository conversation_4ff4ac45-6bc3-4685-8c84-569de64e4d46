#!/usr/bin/env node

/**
 * 🎯 OTIMIZADOR DE HOOKS PARA TODOS OS JOGOS
 * Script para aplicar useCallback em funções críticas de todos os jogos
 */

import fs from 'fs';
import path from 'path';

const GAMES_DIRECTORY = './src/games';

// Função para detectar se uma função deve ser envolvida com useCallback
const shouldWrapWithCallback = (funcName, lineContent) => {
  // Lista de padrões que devem ser envolvidos com useCallback
  const patterns = [
    /const\s+(\w+)\s*=\s*\([^)]*\)\s*=>/,  // Arrow functions
    /function\s+(\w+)\s*\(/,               // Function declarations
  ];
  
  // Funções específicas que sempre devem ser useCallback
  const priorityFunctions = [
    'speak', 'explainGame', 'getAccuracy', 'startGame', 'restartGame',
    'toggleTTS', 'handleItemSelect', 'nextColor', 'nextPhase', 'handleCorrectSequence',
    'handleIncorrectSequence', 'generateSequence', 'showSequence', 'handleDragStart',
    'handleDragEnd', 'handleDrop', 'recordInteraction', 'performCognitiveAnalysis',
    'calculateMetrics', 'announceStats', 'playFeedback', 'repeatInstruction',
    'giveHint', 'announceCurrentColor', 'formatTime', 'shuffleArray',
    'handleRestart', 'handleLetterSelect', 'generateNewRound'
  ];
  
  return priorityFunctions.includes(funcName);
};

// Função para aplicar useCallback a uma função
const wrapWithCallback = (content, funcName, dependencies = []) => {
  const patterns = [
    // Arrow function const
    {
      regex: new RegExp(`(\\s*const\\s+${funcName}\\s*=\\s*)([^\\n]*=>\\s*{[\\s\\S]*?^\\s*})`, 'm'),
      replacement: `$1useCallback($2, [${dependencies.join(', ')}])`
    },
    // Function expression
    {
      regex: new RegExp(`(\\s*const\\s+${funcName}\\s*=\\s*)([^\\n]*=>\\s*[^{][^\\n]*)`, 'm'),
      replacement: `$1useCallback($2, [${dependencies.join(', ')}])`
    }
  ];
  
  for (const pattern of patterns) {
    if (pattern.regex.test(content)) {
      return content.replace(pattern.regex, pattern.replacement);
    }
  }
  
  return content;
};

// Função para garantir que useCallback está importado
const ensureUseCallbackImport = (content) => {
  const importRegex = /import\s+React,?\s*{\s*([^}]+)\s*}\s+from\s+['"]react['"]/;
  const match = content.match(importRegex);
  
  if (match) {
    const hooks = match[1].split(',').map(h => h.trim());
    if (!hooks.includes('useCallback')) {
      hooks.push('useCallback');
      const newImport = `import React, { ${hooks.join(', ')} } from 'react'`;
      return content.replace(importRegex, newImport);
    }
  }
  
  return content;
};

// Função para otimizar um arquivo de jogo
const optimizeGameFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const filename = path.basename(filePath);
    
    console.log(`🔧 Otimizando: ${filename}`);
    
    // Garantir que useCallback está importado
    content = ensureUseCallbackImport(content);
    
    // Lista de funções para aplicar useCallback baseado no arquivo
    const functionsToOptimize = [];
    
    if (filename.includes('MusicalSequence')) {
      functionsToOptimize.push(
        { name: 'toggleTTS', deps: [] },
        { name: 'explainGame', deps: ['currentSequence', 'speak'] },
        { name: 'getAccuracy', deps: ['gameStats'] },
        { name: 'startGame', deps: ['difficulty', 'setGameStarted'] },
        { name: 'handleBackToMenu', deps: ['onBack'] }
      );
    } else if (filename.includes('ColorMatch')) {
      functionsToOptimize.push(
        { name: 'toggleTTS', deps: [] },
        { name: 'speak', deps: ['ttsActive'] },
        { name: 'explainGame', deps: ['speak'] },
        { name: 'startGame', deps: ['difficulty', 'setGameStarted'] },
        { name: 'restartGame', deps: ['setGameStarted', 'setGameStats'] }
      );
    } else if (filename.includes('MemoryGame')) {
      functionsToOptimize.push(
        { name: 'toggleTTS', deps: [] },
        { name: 'shuffleArray', deps: [] },
        { name: 'formatTime', deps: [] },
        { name: 'getGridCols', deps: [] },
        { name: 'handleRestart', deps: ['generateCards'] },
        { name: 'startGame', deps: ['difficulty'] },
        { name: 'restartGame', deps: ['generateCards'] }
      );
    } else if (filename.includes('PadroesVisuais')) {
      functionsToOptimize.push(
        { name: 'toggleTTS', deps: [] },
        { name: 'speak', deps: ['ttsActive'] },
        { name: 'explainGame', deps: ['speak'] },
        { name: 'startGame', deps: ['difficulty'] },
        { name: 'generateSequence', deps: ['currentLevel'] },
        { name: 'showSequence', deps: ['currentSequence'] }
      );
    } else if (filename.includes('QuebraCabeca')) {
      functionsToOptimize.push(
        { name: 'speak', deps: ['ttsActive'] },
        { name: 'getAccuracy', deps: ['gameStats'] },
        { name: 'startGame', deps: ['difficulty'] },
        { name: 'generateNewPuzzle', deps: ['currentLevel'] },
        { name: 'handleDragStart', deps: [] },
        { name: 'handleDrop', deps: ['currentPuzzle'] }
      );
    }
    
    // Aplicar useCallback às funções identificadas
    let modified = false;
    functionsToOptimize.forEach(({ name, deps }) => {
      const beforeContent = content;
      content = wrapWithCallback(content, name, deps);
      if (content !== beforeContent) {
        console.log(`   ✅ Adicionado useCallback em ${name}`);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`   💾 Arquivo salvo com otimizações`);
    } else {
      console.log(`   ℹ️ Nenhuma otimização necessária`);
    }
    
    return modified;
    
  } catch (error) {
    console.error(`❌ Erro ao processar ${filePath}:`, error.message);
    return false;
  }
};

// Função principal
const main = () => {
  console.log('🚀 INICIANDO OTIMIZAÇÃO DE HOOKS EM TODOS OS JOGOS');
  console.log('================================================');
  
  const gameFiles = [
    './src/games/MusicalSequence/MusicalSequenceGame.jsx',
    './src/games/ColorMatch/ColorMatchGame.jsx',
    './src/games/MemoryGame/MemoryGame.jsx',
    './src/games/PadroesVisuais/PadroesVisuaisGame.jsx',
    './src/games/QuebraCabeca/QuebraCabecaGame.jsx',
    './src/games/CreativePainting/CreativePaintingGame.jsx'
  ];
  
  let totalOptimized = 0;
  
  gameFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const wasOptimized = optimizeGameFile(file);
      if (wasOptimized) totalOptimized++;
    } else {
      console.log(`⚠️ Arquivo não encontrado: ${file}`);
    }
  });
  
  console.log('\n✨ OTIMIZAÇÃO CONCLUÍDA');
  console.log(`📊 Total de arquivos otimizados: ${totalOptimized}`);
  console.log('\n🔧 Próximos passos:');
  console.log('1. Executar validação de hooks: node scripts/validate-react-hooks.js');
  console.log('2. Testar compilação: node scripts/test-build.js');
  console.log('3. Validar jogos: node scripts/test-games.js');
};

main();
