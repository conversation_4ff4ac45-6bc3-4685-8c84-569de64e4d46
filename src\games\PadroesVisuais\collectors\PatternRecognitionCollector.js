/**
 * 🎨 PATTERN RECOGNITION COLLECTOR
 * Coletor especializado em análise de reconhecimento de padrões para PadroesVisuais
 * Portal Betina V3
 */

export class PatternRecognitionCollector {
  constructor() {
    this.patternTypes = {
      sequential: 'sequencial',
      spatial: 'espacial',
      temporal: 'temporal',
      geometric: 'geométrico',
      color: 'cromático',
      repetitive: 'repetitivo'
    };
    
    this.recognitionComplexity = {
      simple: { level: 1, description: 'padrões básicos' },
      moderate: { level: 2, description: 'padrões intermediários' },
      complex: { level: 3, description: 'padrões avan<PERSON>' },
      expert: { level: 4, description: 'padrões especializados' }
    };
    
    this.shapeCategories = {
      geometric: ['triangle', 'square', 'diamond'],
      symbolic: ['star', 'heart'],
      basic: ['circle']
    };
  }
  
  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dad<PERSON> do jogo a serem analisados
   * @returns {Object} - <PERSON>sul<PERSON><PERSON> da <PERSON>
   */
  collect(data) {
    return this.analyze(data);
  }

  async analyze(data) {
    if (!data || !data.patternData) {
      console.warn('PatternRecognitionCollector: Dados inválidos recebidos', data);
      return {
        sequenceRecognition: 0.7,
        patternIdentification: 0.7,
        visualAnalysis: 0.7,
        complexityHandling: 0.7,
        adaptiveRecognition: 0.7,
        patternMemory: 0.7,
        recognitionSpeed: 0.7,
        errorRecovery: 0.7
      };
    }

    return {
      sequenceRecognition: this.assessSequenceRecognition(data),
      patternIdentification: this.assessPatternIdentification(data),
      visualAnalysis: this.assessVisualAnalysis(data),
      complexityHandling: this.assessComplexityHandling(data),
      adaptiveRecognition: this.assessAdaptiveRecognition(data),
      patternMemory: this.assessPatternMemory(data),
      recognitionSpeed: this.assessRecognitionSpeed(data),
      errorRecovery: this.assessErrorRecovery(data),
      patternInsights: this.generatePatternInsights(data)
    };
  }

  assessSequenceRecognition(data) {
    const sequences = data.patternData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let totalScore = 0;
    let validSequences = 0;
    
    sequences.forEach(sequence => {
      if (sequence.targetSequence && sequence.playerSequence) {
        const accuracy = this.calculateSequenceAccuracy(sequence.targetSequence, sequence.playerSequence);
        const lengthFactor = Math.min(1, sequence.targetSequence.length / 3); // Bonus para sequências maiores
        const difficultyBonus = this.getDifficultyMultiplier(sequence.difficulty);
        
        const sequenceScore = accuracy * lengthFactor * difficultyBonus;
        totalScore += sequenceScore;
        validSequences++;
      }
    });
    
    const avgScore = validSequences > 0 ? totalScore / validSequences : 0.7;
    
    // Bonus para consistência
    const consistencyBonus = this.calculateConsistency(sequences);
    
    return Math.max(0, Math.min(1, avgScore + consistencyBonus));
  }

  assessPatternIdentification(data) {
    const interactions = data.patternData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Analisar capacidade de identificar padrões específicos
    const patternTypes = this.identifyPatternTypes(interactions);
    let identificationScore = 0;
    
    Object.keys(this.patternTypes).forEach(type => {
      if (patternTypes[type]) {
        const typeAccuracy = patternTypes[type].correct / patternTypes[type].total;
        const complexityWeight = patternTypes[type].avgComplexity;
        
        identificationScore += typeAccuracy * complexityWeight;
      }
    });
    
    // Normalizar pelo número de tipos testados
    const testedTypes = Object.keys(patternTypes).length;
    const normalizedScore = testedTypes > 0 ? identificationScore / testedTypes : 0.7;
    
    return Math.max(0, Math.min(1, normalizedScore));
  }

  assessVisualAnalysis(data) {
    const interactions = data.patternData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Avaliar capacidade de análise visual
    let visualScore = 0;
    let totalAnalyses = 0;
    
    interactions.forEach(interaction => {
      if (interaction.visualProcessing) {
        const scanTime = interaction.responseTime || 2000;
        const accuracy = interaction.isCorrect ? 1 : 0;
        
        // Análise de tempo vs precisão
        const efficiency = this.calculateVisualEfficiency(scanTime, accuracy);
        const shapeComplexity = this.getShapeComplexity(interaction.shapeId);
        
        visualScore += efficiency * shapeComplexity;
        totalAnalyses++;
      }
    });
    
    const avgVisualScore = totalAnalyses > 0 ? visualScore / totalAnalyses : 0.7;
    
    // Avaliar evolução da análise visual ao longo da sessão
    const improvementFactor = this.calculateVisualImprovement(interactions);
    
    return Math.max(0, Math.min(1, avgVisualScore + improvementFactor));
  }

  assessComplexityHandling(data) {
    const sequences = data.patternData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    // Agrupar por nível de complexidade
    const complexityGroups = {
      simple: [],
      moderate: [],
      complex: [],
      expert: []
    };
    
    sequences.forEach(sequence => {
      const complexity = this.determineSequenceComplexity(sequence);
      if (complexityGroups[complexity]) {
        complexityGroups[complexity].push(sequence);
      }
    });
    
    let complexityScore = 0;
    let totalLevels = 0;
    
    Object.keys(complexityGroups).forEach(level => {
      const group = complexityGroups[level];
      if (group.length > 0) {
        const levelAccuracy = group.filter(s => s.isCorrect).length / group.length;
        const levelWeight = this.recognitionComplexity[level].level;
        
        complexityScore += levelAccuracy * levelWeight;
        totalLevels++;
      }
    });
    
    const avgComplexityHandling = totalLevels > 0 ? complexityScore / (totalLevels * 2.5) : 0.7; // 2.5 = média dos pesos
    
    return Math.max(0, Math.min(1, avgComplexityHandling));
  }

  assessAdaptiveRecognition(data) {
    const sequences = data.patternData.sequences || [];
    
    if (sequences.length < 3) return 0.7; // Precisa de histórico para medir adaptação
    
    // Avaliar adaptação a novos padrões
    const adaptationScores = [];
    
    for (let i = 1; i < sequences.length; i++) {
      const current = sequences[i];
      const previous = sequences[i - 1];
      
      // Verificar se houve mudança de padrão
      const patternChange = this.detectPatternChange(previous, current);
      
      if (patternChange) {
        const adaptationTime = current.responseTime || 2000;
        const adaptationAccuracy = current.isCorrect ? 1 : 0;
        
        // Calcular qualidade da adaptação
        const adaptationQuality = this.calculateAdaptationQuality(adaptationTime, adaptationAccuracy, patternChange.difficulty);
        adaptationScores.push(adaptationQuality);
      }
    }
    
    const avgAdaptation = adaptationScores.length > 0 
      ? adaptationScores.reduce((sum, score) => sum + score, 0) / adaptationScores.length 
      : 0.7;
    
    return Math.max(0, Math.min(1, avgAdaptation));
  }

  assessPatternMemory(data) {
    const sequences = data.patternData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    let memoryScore = 0;
    let totalSequences = 0;
    
    sequences.forEach(sequence => {
      const memoryLoad = sequence.targetSequence ? sequence.targetSequence.length : 3;
      const retention = sequence.isCorrect ? 1 : this.calculatePartialRetention(sequence);
      
      // Pontuação baseada na carga de memória
      const memoryFactor = Math.min(1, memoryLoad / 5); // Normalizado para sequências até 5 elementos
      const retentionScore = retention * memoryFactor;
      
      memoryScore += retentionScore;
      totalSequences++;
    });
    
    const avgMemoryScore = totalSequences > 0 ? memoryScore / totalSequences : 0.7;
    
    // Bonus para manutenção de memória em sequências longas
    const longSequences = sequences.filter(s => s.targetSequence && s.targetSequence.length >= 4);
    const longSequenceBonus = longSequences.length > 0 ? 0.1 : 0;
    
    return Math.max(0, Math.min(1, avgMemoryScore + longSequenceBonus));
  }

  assessRecognitionSpeed(data) {
    const interactions = data.patternData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    const responseTimes = interactions
      .filter(i => i.responseTime && i.responseTime > 0)
      .map(i => i.responseTime);
    
    if (responseTimes.length === 0) return 0.7;
    
    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    
    // Normalizar velocidade: 1000ms = 1.0, 3000ms = 0.33
    const speedScore = Math.max(0, Math.min(1, (3000 - avgResponseTime) / 2000));
    
    // Avaliar consistência da velocidade
    const timeVariability = this.calculateTimeVariability(responseTimes);
    const consistencyBonus = Math.max(0, (1 - timeVariability) * 0.2);
    
    return Math.max(0, Math.min(1, speedScore + consistencyBonus));
  }

  assessErrorRecovery(data) {
    const sequences = data.patternData.sequences || [];
    
    if (sequences.length === 0) return 0.7;
    
    // Identificar sequências após erros
    const errorRecoveries = [];
    
    for (let i = 1; i < sequences.length; i++) {
      const previous = sequences[i - 1];
      const current = sequences[i];
      
      if (!previous.isCorrect && current.isCorrect) {
        const recoveryQuality = this.calculateRecoveryQuality(previous, current);
        errorRecoveries.push(recoveryQuality);
      }
    }
    
    if (errorRecoveries.length === 0) {
      // Se não houve erros para recuperar, considerar positivo
      const errorRate = sequences.filter(s => !s.isCorrect).length / sequences.length;
      return errorRate < 0.2 ? 0.9 : 0.7;
    }
    
    const avgRecovery = errorRecoveries.reduce((sum, recovery) => sum + recovery, 0) / errorRecoveries.length;
    
    return Math.max(0, Math.min(1, avgRecovery));
  }

  // Métodos auxiliares

  calculateSequenceAccuracy(target, player) {
    if (!target || !player) return 0;
    
    const minLength = Math.min(target.length, player.length);
    let matches = 0;
    
    for (let i = 0; i < minLength; i++) {
      if (target[i] === player[i]) {
        matches++;
      }
    }
    
    return matches / target.length;
  }

  getDifficultyMultiplier(difficulty) {
    const multipliers = { easy: 1.0, medium: 1.2, hard: 1.4 };
    return multipliers[difficulty] || 1.0;
  }

  calculateConsistency(sequences) {
    if (sequences.length < 3) return 0;
    
    const accuracies = sequences.map(s => s.isCorrect ? 1 : 0);
    const variance = this.calculateVariance(accuracies);
    
    // Menos variância = mais consistência
    return Math.max(0, (1 - variance) * 0.15);
  }

  identifyPatternTypes(interactions) {
    const types = {};
    
    interactions.forEach(interaction => {
      // Simplificado - identificaria tipos de padrão baseado na forma e posição
      const patternType = this.inferPatternType(interaction);
      
      if (!types[patternType]) {
        types[patternType] = { correct: 0, total: 0, complexities: [] };
      }
      
      types[patternType].total++;
      if (interaction.isCorrect) types[patternType].correct++;
      types[patternType].complexities.push(this.getShapeComplexity(interaction.shapeId));
    });
    
    // Calcular complexidade média para cada tipo
    Object.keys(types).forEach(type => {
      const complexities = types[type].complexities;
      types[type].avgComplexity = complexities.reduce((sum, c) => sum + c, 0) / complexities.length;
    });
    
    return types;
  }

  calculateVisualEfficiency(scanTime, accuracy) {
    // Balancear velocidade e precisão
    const speedScore = Math.max(0, Math.min(1, (3000 - scanTime) / 2000));
    const combinedScore = (speedScore * 0.6) + (accuracy * 0.4);
    
    return combinedScore;
  }

  getShapeComplexity(shapeId) {
    const complexities = {
      circle: 1.0,
      square: 1.1,
      triangle: 1.2,
      star: 1.3,
      diamond: 1.4,
      heart: 1.5
    };
    
    return complexities[shapeId] || 1.0;
  }

  calculateVisualImprovement(interactions) {
    if (interactions.length < 5) return 0;
    
    const firstHalf = interactions.slice(0, Math.floor(interactions.length / 2));
    const secondHalf = interactions.slice(Math.floor(interactions.length / 2));
    
    const firstAccuracy = firstHalf.filter(i => i.isCorrect).length / firstHalf.length;
    const secondAccuracy = secondHalf.filter(i => i.isCorrect).length / secondHalf.length;
    
    const improvement = secondAccuracy - firstAccuracy;
    
    return Math.max(0, Math.min(0.2, improvement)); // Max 0.2 bonus
  }

  determineSequenceComplexity(sequence) {
    const length = sequence.targetSequence ? sequence.targetSequence.length : 3;
    const difficulty = sequence.difficulty || 'easy';
    
    if (length <= 3 && difficulty === 'easy') return 'simple';
    if (length <= 4 && difficulty !== 'hard') return 'moderate';
    if (length <= 5) return 'complex';
    return 'expert';
  }

  detectPatternChange(previous, current) {
    // Simplificado - detectaria mudanças de padrão
    const prevLength = previous.targetSequence ? previous.targetSequence.length : 3;
    const currLength = current.targetSequence ? current.targetSequence.length : 3;
    
    if (prevLength !== currLength) {
      return { type: 'length', difficulty: Math.abs(currLength - prevLength) };
    }
    
    if (previous.difficulty !== current.difficulty) {
      return { type: 'difficulty', difficulty: 1 };
    }
    
    return null;
  }

  calculateAdaptationQuality(time, accuracy, changeDifficulty) {
    const timeScore = Math.max(0, Math.min(1, (4000 - time) / 3000)); // Mais tolerante para adaptação
    const difficultyBonus = changeDifficulty * 0.1;
    
    return (accuracy * 0.7) + (timeScore * 0.3) + difficultyBonus;
  }

  calculatePartialRetention(sequence) {
    if (!sequence.targetSequence || !sequence.playerSequence) return 0;
    
    const matches = this.calculateSequenceAccuracy(sequence.targetSequence, sequence.playerSequence);
    return matches;
  }

  calculateTimeVariability(times) {
    if (times.length < 2) return 0;
    
    const mean = times.reduce((sum, time) => sum + time, 0) / times.length;
    const variance = times.reduce((sum, time) => sum + Math.pow(time - mean, 2), 0) / times.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Normalizar variabilidade
    return Math.min(1, standardDeviation / mean);
  }

  calculateRecoveryQuality(errorSequence, recoverySequence) {
    const errorComplexity = this.determineSequenceComplexity(errorSequence);
    const recoveryTime = recoverySequence.responseTime || 2000;
    const recoveryAccuracy = recoverySequence.isCorrect ? 1 : 0;
    
    // Bonus para recuperação rápida após erro
    const timeBonus = Math.max(0, (3000 - recoveryTime) / 3000) * 0.3;
    
    return (recoveryAccuracy * 0.7) + timeBonus;
  }

  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  inferPatternType(interaction) {
    // Simplificado - inferiria tipo baseado em características
    if (interaction.sequencePosition !== undefined) return 'sequential';
    if (interaction.shapeId) return 'geometric';
    return 'temporal';
  }

  generatePatternInsights(data) {
    const insights = [];
    const results = data.patternData;
    
    if (!results) return insights;
    
    // Analisar cada aspecto e gerar insights
    if (results.sequenceRecognition < 0.6) {
      insights.push('Dificuldades no reconhecimento de sequências');
    }
    
    if (results.patternIdentification < 0.6) {
      insights.push('Limitações na identificação de padrões específicos');
    }
    
    if (results.visualAnalysis < 0.5) {
      insights.push('Necessita melhorar análise visual');
    }
    
    if (results.complexityHandling < 0.5) {
      insights.push('Dificuldades com padrões complexos');
    }
    
    if (results.adaptiveRecognition < 0.6) {
      insights.push('Baixa adaptabilidade a novos padrões');
    }
    
    if (results.patternMemory < 0.5) {
      insights.push('Limitações na memória de padrões');
    }
    
    if (results.recognitionSpeed < 0.5) {
      insights.push('Velocidade de reconhecimento reduzida');
    }
    
    if (results.errorRecovery < 0.5) {
      insights.push('Dificuldades na recuperação após erros');
    }
    
    return insights;
  }
}
