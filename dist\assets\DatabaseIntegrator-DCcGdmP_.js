class e{constructor(){this.component="DatabaseIntegrator"}info(e,t={}){}warn(e,t={}){}error(e,t={}){}debug(e,t={}){}}class t{static validateGameMetricsInput(e,t,s){const r=[],a=[];return e&&"string"==typeof e||r.push("userId must be a non-empty string"),t&&"string"==typeof t||r.push("gameId must be a non-empty string"),s&&"object"==typeof s?(s.timestamp&&Number.isInteger(s.timestamp)||a.push("metrics.timestamp should be a valid integer"),s.interactions&&!Array.isArray(s.interactions)&&r.push("metrics.interactions must be an array")):r.push("metrics must be an object"),{valid:0===r.length,errors:r,warnings:a,sanitized:{userId:e?String(e).trim():e,gameId:t?String(t).trim():t,metrics:s?{...s,timestamp:s.timestamp||Date.now()}:s}}}static validateInteractionsInput(e,t){const s=[],r=[];return(!Number.isInteger(e)||e<=0)&&s.push("sessionId must be a positive integer"),Array.isArray(t)&&0!==t.length?t.forEach((e,t)=>{e.type&&"string"==typeof e.type||s.push(`interaction[${t}].type must be a string`),Number.isInteger(e.timestamp)||r.push(`interaction[${t}].timestamp should be a valid integer`)}):s.push("interactions must be a non-empty array"),{valid:0===s.length,errors:s,warnings:r,sanitized:{sessionId:e,interactions:t.map(e=>({...e,type:e.type?String(e.type).trim():e.type,timestamp:e.timestamp||Date.now()}))}}}static validateUserDataInput(e,t){const s=[],r=["progress","profile","predictions"];return e&&"string"==typeof e||s.push("userId must be a non-empty string"),t&&r.includes(t)||s.push(`dataType must be one of: ${r.join(", ")}`),{valid:0===s.length,errors:s,sanitized:{userId:e?String(e).trim():e,dataType:t}}}}class s{constructor(t={}){this.config={resilience:{enabled:!0,monitoringEnabled:!0,retryAttempts:3,retryDelay:1e3,timeoutDuration:5e3},cache:{memoryMaxSize:1e3,memoryTTL:3e5,redisEnabled:!!t.redisUrl,redisUrl:t.redisUrl||null},metrics:{enabled:!0,detailedLogging:!0,persistInterval:6e4},...t},this.logger=new e,this.metricsBuffer=new Map,this.intervals=new Map,this.logger.info("DatabaseIntegrator: Initialized (simplified for frontend)",{config:{resilience:this.config.resilience.enabled,cache:this.config.cache.redisEnabled?"redis":"memory",metrics:this.config.metrics.enabled}})}async saveGameMetrics(e,s,r){try{const a=t.validateGameMetricsInput(e,s,r);if(!a.valid)throw new Error(`Invalid input: ${a.errors.join(", ")}`);const{userId:i,gameId:n,metrics:o}=a.sanitized,c={sessionId:Date.now(),userId:i,gameId:n,timestamp:Date.now(),status:"success"};return this.logger.info("Game metrics saved successfully (frontend)",{userId:i,gameId:n,sessionId:c.sessionId,metricsCount:Object.keys(o).length}),c}catch(a){throw this.logger.error("Failed to save game metrics",{userId:e,gameId:s,error:a.message,stack:a.stack}),a}}async saveInteractions(e,s){try{const r=t.validateInteractionsInput(e,s);if(!r.valid)throw new Error(`Invalid interactions input: ${r.errors.join(", ")}`);const{sessionId:a,interactions:i}=r.sanitized,n={sessionId:a,interactionCount:i.length,timestamp:Date.now(),status:"success"};return this.logger.info("Interactions saved successfully (frontend)",{sessionId:a,interactionCount:i.length}),n}catch(r){throw this.logger.error("Failed to save interactions",{sessionId:e,error:r.message,stack:r.stack}),r}}async getUserData(e,s){try{const r=t.validateUserDataInput(e,s);if(!r.valid)throw new Error(`Invalid input: ${r.errors.join(", ")}`);const{userId:a,dataType:i}=r.sanitized,n={userId:a,dataType:i,data:{},timestamp:Date.now(),status:"success"};return this.logger.info("User data retrieved successfully (frontend)",{userId:a,dataType:i}),n}catch(r){throw this.logger.error("Failed to retrieve user data",{userId:e,dataType:s,error:r.message,stack:r.stack}),r}}getStatus(){try{return{database:{connected:!0,isConnected:!0,type:"frontend-simulation",status:"operational"},resilience:{status:"operational"},sessionManager:{status:"operational"},metricsEngine:{status:"operational"},systemOrchestrator:{status:"operational"},metricsBuffer:{size:this.metricsBuffer.size,status:"operational"},timestamp:(new Date).toISOString()}}catch(e){return this.logger.error("Error retrieving system status",{error:e.message,stack:e.stack}),{database:{connected:!1,isConnected:!1,type:"frontend-simulation",status:"failed"},resilience:{status:"failed"},sessionManager:{status:"failed"},metricsEngine:{status:"failed"},systemOrchestrator:{status:"failed"},metricsBuffer:{size:0,status:"failed"},timestamp:(new Date).toISOString()}}}getSystemStatus(){return this.getStatus()}async cleanup(){try{this.logger.info("Initiating DatabaseIntegrator cleanup...");for(const[e,t]of this.intervals.entries())clearInterval(t),this.logger.info(`Interval stopped: ${e}`);this.intervals.clear(),this.logger.info("✅ DatabaseIntegrator cleanup completed")}catch(e){throw this.logger.error("Error during DatabaseIntegrator cleanup",{error:e.message,stack:e.stack}),e}}}export{s as DatabaseIntegrator,t as InputValidator,s as default};
