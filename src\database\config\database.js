/**
 * 💾 DATABASE CONFIGURATION
 * Configuração principal do banco de dados PostgreSQL
 * Portal Betina V3
 */

import pgCompatibility from './pg-compatibility.js';

// Configuração base do banco de dados
const databaseConfig = {
  // Configurações de conexão
  connection: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    user: process.env.DB_USER || 'betina_user',
    password: process.env.DB_PASSWORD || 'betina_password',
    database: process.env.DB_NAME || 'betina_db',
    
    // Aplicar configurações de compatibilidade
    ...pgCompatibility,
    
    // Configurações específicas
    application_name: 'Portal_Betina_V3',
    keepAlive: true,
    keepAliveInitialDelayMillis: 10000
  },
  
  // Pool de conexões
  pool: {
    min: 2,
    max: pgCompatibility.max || 10,
    createTimeoutMillis: 8000,
    acquireTimeoutMillis: 8000,
    idleTimeoutMillis: pgCompatibility.idleTimeoutMillis || 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100,
    propagateCreateError: false
  },
  
  // Configurações de migração
  migrations: {
    directory: './src/database/migrations',
    tableName: 'knex_migrations',
    extension: 'js',
    disableTransactions: false
  },
  
  // Configurações de seeds
  seeds: {
    directory: './src/database/seeds'
  },
  
  // Configurações de logging
  logging: {
    enabled: process.env.NODE_ENV === 'development',
    level: process.env.LOG_LEVEL || 'info',
    queries: process.env.NODE_ENV === 'development'
  },
  
  // Configurações de cache
  cache: {
    enabled: true,
    ttl: 300000, // 5 minutos
    maxSize: 1000
  },
  
  // Configurações de retry
  retry: {
    enabled: true,
    maxAttempts: 3,
    delay: 1000,
    backoff: 'exponential'
  },
  
  // Configurações de health check
  healthCheck: {
    enabled: true,
    interval: 30000, // 30 segundos
    timeout: 5000,
    query: 'SELECT 1 as health_check'
  },
  
  // Configurações específicas para métricas multissensoriais
  multisensory: {
    batchSize: 100,
    flushInterval: 5000,
    compressionEnabled: true,
    indexingStrategy: 'btree_gin'
  }
};

// Configurações específicas por ambiente
const environmentConfigs = {
  development: {
    connection: {
      ...databaseConfig.connection,
      ssl: false
    },
    logging: {
      enabled: true,
      level: 'debug',
      queries: true
    }
  },
  
  production: {
    connection: {
      ...databaseConfig.connection,
      ssl: process.env.DB_SSL_ENABLED === 'true' ? {
        rejectUnauthorized: false
      } : false
    },
    logging: {
      enabled: false,
      level: 'error',
      queries: false
    },
    pool: {
      ...databaseConfig.pool,
      max: 20,
      min: 5
    }
  },
  
  test: {
    connection: {
      ...databaseConfig.connection,
      database: process.env.DB_NAME_TEST || 'betina_db_test',
      ssl: false
    },
    logging: {
      enabled: false,
      level: 'error',
      queries: false
    },
    pool: {
      ...databaseConfig.pool,
      max: 5,
      min: 1
    }
  }
};

// Aplicar configurações do ambiente atual
const currentEnv = process.env.NODE_ENV || 'development';
const envConfig = environmentConfigs[currentEnv] || environmentConfigs.development;

// Mesclar configurações
const finalConfig = {
  ...databaseConfig,
  ...envConfig,
  connection: {
    ...databaseConfig.connection,
    ...envConfig.connection
  },
  pool: {
    ...databaseConfig.pool,
    ...envConfig.pool
  },
  logging: {
    ...databaseConfig.logging,
    ...envConfig.logging
  }
};

// Validar configurações
function validateConfig(config) {
  const required = ['host', 'port', 'user', 'password', 'database'];
  const missing = required.filter(key => !config.connection[key]);
  
  if (missing.length > 0) {
    throw new Error(`Configurações obrigatórias faltando: ${missing.join(', ')}`);
  }
  
  return true;
}

// Validar antes de exportar
validateConfig(finalConfig);

console.log(`✅ Database configuration loaded for environment: ${currentEnv}`);

export default finalConfig;
