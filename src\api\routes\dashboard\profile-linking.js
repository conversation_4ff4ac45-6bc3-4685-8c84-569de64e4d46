/**
 * 🔗 Dashboard Profile Linking - Portal Betina V3
 * Endpoint para vincular perfis de crianças ao usuário logado no dashboard
 */

import express from 'express'
import { authenticate } from '../../middleware/auth/jwt.js'
import { globalRateLimit } from '../../middleware/security/rateLimiter.js'
import { validateInput, schemas, sanitizeInput } from '../../middleware/validation/inputValidator.js'
import { asyncHandler, createError } from '../../middleware/error/errorHandler.js'

const router = express.Router()

// Mock database para demonstração (em produção usar PostgreSQL)
let profileLinks = new Map()

/**
 * @route POST /api/dashboard/link-profile
 * @desc Vincular um perfil de criança ao usuário do dashboard
 * @access Private (requer autenticação do dashboard)
 */
router.post('/link-profile',
  authenticate,
  globalRateLimit,
  validateInput(schemas.dashboard.linkProfile),
  asyncHandler(async (req, res) => {
    const { profileId, dashboardUserId, profileData } = req.body
    const authenticatedUserId = req.user.id  // Corrigido: usar .id em vez de .userId

    // Debug: vamos ver o que está sendo comparado
    console.log('🔍 Debug autenticação:')
    console.log('  authenticatedUserId:', authenticatedUserId, '(tipo:', typeof authenticatedUserId, ')')
    console.log('  dashboardUserId:', dashboardUserId, '(tipo:', typeof dashboardUserId, ')')
    console.log('  req.user:', JSON.stringify(req.user, null, 2))

    // Verificar se o usuário autenticado pode vincular este perfil
    if (authenticatedUserId !== dashboardUserId) {
      throw createError.forbidden(`Não autorizado: ${authenticatedUserId} !== ${dashboardUserId}`)
    }

    // Criar vinculação
    const linkKey = `${dashboardUserId}:${profileId}`
    const linkData = {
      dashboardUserId,
      profileId,
      profileData,
      linkedAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    }

    profileLinks.set(linkKey, linkData)

    console.log(`🔗 Perfil ${profileId} vinculado ao usuário ${dashboardUserId}`)

    res.json({
      success: true,
      message: 'Perfil vinculado com sucesso',
      data: {
        profileId,
        dashboardUserId,
        linkedAt: linkData.linkedAt
      }
    })
  })
)

/**
 * @route GET /api/dashboard/linked-profiles
 * @desc Obter todos os perfis vinculados ao usuário do dashboard
 * @access Private (requer autenticação do dashboard)
 */
router.get('/linked-profiles',
  authenticate,
  globalRateLimit,
  asyncHandler(async (req, res) => {
    const dashboardUserId = req.user.id

    // Buscar todos os perfis vinculados a este usuário
    const userProfiles = []
    for (const [key, linkData] of profileLinks.entries()) {
      if (linkData.dashboardUserId === dashboardUserId) {
        userProfiles.push({
          profileId: linkData.profileId,
          profileData: linkData.profileData,
          linkedAt: linkData.linkedAt,
          lastUpdated: linkData.lastUpdated
        })
      }
    }

    res.json({
      success: true,
      message: `${userProfiles.length} perfis encontrados`,
      data: {
        dashboardUserId,
        profiles: userProfiles,
        totalProfiles: userProfiles.length
      }
    })
  })
)

/**
 * @route DELETE /api/dashboard/unlink-profile/:profileId
 * @desc Desvincular um perfil do usuário do dashboard
 * @access Private (requer autenticação do dashboard)
 */
router.delete('/unlink-profile/:profileId',
  authenticate,
  globalRateLimit,
  asyncHandler(async (req, res) => {
    const { profileId } = req.params
    const dashboardUserId = req.user.id

    const linkKey = `${dashboardUserId}:${profileId}`
    
    if (!profileLinks.has(linkKey)) {
      throw createError.notFound('Vinculação não encontrada')
    }

    profileLinks.delete(linkKey)

    console.log(`🔗❌ Perfil ${profileId} desvinculado do usuário ${dashboardUserId}`)

    res.json({
      success: true,
      message: 'Perfil desvinculado com sucesso',
      data: {
        profileId,
        dashboardUserId
      }
    })
  })
)

/**
 * @route GET /api/dashboard/profile-metrics/:profileId
 * @desc Obter métricas de um perfil específico (se vinculado ao usuário)
 * @access Private (requer autenticação do dashboard)
 */
router.get('/profile-metrics/:profileId',
  authenticate,
  globalRateLimit,
  asyncHandler(async (req, res) => {
    const { profileId } = req.params
    const dashboardUserId = req.user.id

    const linkKey = `${dashboardUserId}:${profileId}`
    
    if (!profileLinks.has(linkKey)) {
      throw createError.forbidden('Perfil não vinculado a este usuário')
    }

    // Buscar métricas do perfil
    try {
      const metricsResponse = await fetch(`http://localhost:3000/api/metrics/game-sessions?userId=${profileId}`)
      const metricsData = await metricsResponse.json()

      res.json({
        success: true,
        message: 'Métricas do perfil obtidas com sucesso',
        data: {
          profileId,
          dashboardUserId,
          metrics: metricsData,
          linkInfo: profileLinks.get(linkKey)
        }
      })
    } catch (error) {
      console.error('Erro ao buscar métricas:', error)
      throw createError.serverError('Erro ao buscar métricas do perfil')
    }
  })
)

export default router
