# ARQUITETURA DOS PROCESSADORES E COLETORES - PORTAL BETINA V3

## ⚠️ IMPORTANTE: PARE DE ALUCINAR COM A ARQUITETURA!

Este documento explica claramente como funciona a arquitetura de processadores e coletores do Portal Betina V3 para evitar confusões constantes.

---

## 📁 ESTRUTURA DE ARQUIVOS

```
src/
├── api/
│   ├── core/
│   │   └── GameSpecificProcessors.js     # ← ORQUESTRADOR (NO CORE!)
│   └── services/
│       └── processors/
│           ├── BaseProcessor.js           # ← CLASSE BASE PARA HERANÇA
│           └── games/                     # ← PROCESSADORES ESPECÍFICOS
│               ├── ImageAssociationProcessors.js
│               ├── MemoryGameProcessors.js
│               ├── MusicalSequenceProcessors.js
│               ├── ColorMatchProcessors.js
│               └── ...
└── games/
    ├── ColorMatch/
    │   └── collectors/
    │       ├── index.js                   # ← HUB DE COLETORES
    │       ├── ColorDiscriminationCollector.js
    │       ├── VisualMatchingCollector.js
    │       └── PerceptionCollector.js
    ├── ImageAssociation/
    │   └── collectors/
    │       ├── index.js                   # ← HUB DE COLETORES
    │       ├── AssociativeMemoryCollector.js
    │       ├── VisualProcessingCollector.js
    │       └── ...
    └── ...
```

---

## 🏗️ COMO A ARQUITETURA FUNCIONA

### 1. **BaseProcessor.js** - A CLASSE MÃE
- **Função**: Classe base abstrata com métodos comuns
- **Herança**: Todos os processadores específicos herdam desta classe
- **Contém**: Métodos básicos, validações, estruturas comuns
- **NÃO IMPLEMENTA**: Lógica específica de jogos

### 2. **GameSpecificProcessors.js** - O ORQUESTRADOR (NO CORE)
- **Localização**: `src/api/core/GameSpecificProcessors.js`
- **Função**: APENAS coordena e direciona para os processadores corretos
- **NÃO IMPLEMENTA**: Lógica específica de processamento de jogos
- **Responsabilidades**:
  - Mapear nomes de jogos para processadores
  - Instanciar processadores específicos
  - Coordenar hubs de coletores
  - Gerar análises terapêuticas gerais
- **NÃO DEVE**: Implementar métodos como `analyzeColorDiscrimination()`, `analyzePatternRecognition()`, etc.

### 3. **Processadores Específicos** (services/processors/games/*.js)
- **Localização**: `src/api/services/processors/games/`
- **Função**: Implementam a lógica específica de cada jogo
- **Herdam de**: BaseProcessor.js
- **Exemplos**:
  - `ColorMatchProcessors.js` → processa dados do jogo ColorMatch
  - `ImageAssociationProcessors.js` → processa dados do jogo ImageAssociation
- **Implementam**: Métodos específicos como `processGameData()`, análises especializadas

### 4. **Hubs de Coletores** (games/*/collectors/index.js)
- **Função**: Agregam todos os coletores de um jogo específico
- **Exportam**: Uma classe Hub que contém referências para todos os coletores
- **Exemplo**: `ColorMatchCollectorsHub` contém todos os coletores do ColorMatch

### 5. **Coletores Individuais** (games/*/collectors/*.js)
- **Função**: Coletam métricas específicas durante o jogo
- **Exemplos**:
  - `ColorDiscriminationCollector.js` → coleta dados de discriminação de cores
  - `VisualMatchingCollector.js` → coleta dados de correspondência visual

---

## 🔄 FLUXO DE PROCESSAMENTO

```mermaid
graph TD
    A[GameSpecificProcessors - CORE] --> B[Identifica o jogo]
    B --> C[Busca processador específico]
    C --> D[services/processors/games/ColorMatchProcessors.js]
    D --> E[Busca hub de coletores]
    E --> F[ColorMatchCollectorsHub]
    F --> G[Acessa coletores individuais]
    G --> H[ColorDiscriminationCollector]
    G --> I[VisualMatchingCollector]
    G --> J[PerceptionCollector]
    H --> K[Retorna análise específica]
    I --> K
    J --> K
    K --> L[GameSpecificProcessors - CORE agrega]
    L --> M[Análise terapêutica final]
```

---

## ❌ O QUE NÃO FAZER

### **NUNCA IMPLEMENTE NO GameSpecificProcessors.js:**
```javascript
// ❌ ERRADO - NÃO FAÇA ISSO!
analyzeColorDiscrimination(metrics) {
  // Lógica específica aqui
}

analyzePatternRecognition(metrics) {
  // Lógica específica aqui
}

analyzeVisualProcessing(metrics) {
  // Lógica específica aqui
}
```

### **NUNCA ADICIONE DIRETAMENTE:**
- Métodos específicos de análise de jogos
- Lógica de processamento de métricas específicas
- Implementações de coletores

---

## ✅ O QUE FAZER

### **GameSpecificProcessors.js DEVE APENAS:**
```javascript
// ✅ CORRETO - Orquestração
async processGameData(gameName, gameData) {
  const processorKey = this.resolveGameProcessor(gameName);
  const processor = this.processors[processorKey];
  const collectorsHub = this.gameCollectors[processorKey]?.hub;
  
  // Delega para o processador específico
  const processorInstance = new processor(this.logger);
  const specificAnalysis = await processorInstance.processGameData(gameData, collectorsHub);
  
  // Apenas gera análise terapêutica geral
  const therapeuticAnalysis = await this.generateTherapeuticAnalysis(
    processorKey, gameData, specificAnalysis, config
  );
  
  return { specificAnalysis, therapeuticAnalysis };
}
```

### **Processadores Específicos DEVEM:**
```javascript
// ✅ CORRETO - Em ColorMatchProcessors.js
class ColorMatchProcessors extends BaseProcessor {
  async processGameData(gameData, collectorsHub) {
    const colorDiscrimination = await this.analyzeColorDiscrimination(gameData, collectorsHub);
    const visualMatching = await this.analyzeVisualMatching(gameData, collectorsHub);
    
    return {
      colorDiscrimination,
      visualMatching,
      // ... outras análises específicas
    };
  }
  
  analyzeColorDiscrimination(gameData, collectorsHub) {
    // Lógica específica para análise de discriminação de cores
  }
}
```

---

## 🎯 RESPONSABILIDADES CLARAS

| Componente | Localização | Responsabilidade | O que NÃO faz |
|-----------|-------------|------------------|----------------|
| **BaseProcessor** | services/processors/ | Métodos comuns, validações | Lógica específica de jogos |
| **GameSpecificProcessors** | **core/** | Orquestração, roteamento | Análise específica de jogos |
| **Processadores Específicos** | services/processors/games/ | Análise de jogo específico | Orquestração geral |
| **Hubs de Coletores** | games/*/collectors/ | Agregação de coletores | Processamento de dados |
| **Coletores** | games/*/collectors/ | Coleta de métricas | Análise ou processamento |

---

## 🚨 LEMBRETE FINAL

**ANTES DE FAZER QUALQUER MUDANÇA:**

1. ❓ **Pergunta**: "Onde essa lógica REALMENTE pertence?"
2. 🔍 **Verifica**: "Isso é orquestração ou implementação específica?"
3. 📍 **Localiza**: "Qual componente tem essa responsabilidade?"
4. ✅ **Implementa**: No lugar correto da arquitetura

**PARE DE IMPLEMENTAR TUDO NO GameSpecificProcessors.js!**

Ele é um ORQUESTRADOR no **CORE**, não um IMPLEMENTADOR!

**Localização correta**: `src/api/core/GameSpecificProcessors.js`
