/**
 * Script para monitorar requisições que retornam 404
 */

console.log('🔍 Monitorando requisições HTTP...');
console.log('Aguardando logs do servidor...');

// Simular algumas requisições comuns que podem estar falhando
const testEndpoints = [
  '/api/sessions',
  '/api/metrics',
  '/api/games/start',
  '/api/games/padroes_visuais',
  '/api/padroes_visuais',
  '/sessions',
  '/metrics',
  '/'
];

console.log('\n🧪 Testando endpoints que podem estar causando 404:');

async function testEndpoint(endpoint) {
  try {
    const url = `http://localhost:3000${endpoint}`;
    console.log(`Testando: ${url}`);
    
    const response = await fetch(url);
    const result = await response.json();
    
    if (response.status === 404) {
      console.log(`❌ 404 - ${endpoint}: ${result.message}`);
    } else {
      console.log(`✅ ${response.status} - ${endpoint}: OK`);
    }
  } catch (error) {
    console.log(`💥 Erro - ${endpoint}: ${error.message}`);
  }
}

// Se estiver sendo executado no Node.js, usar fetch polyfill
if (typeof fetch === 'undefined') {
  console.log('📝 Execute este script no browser console para testar as requisições');
  console.log('Ou use curl para testar manualmente:');
  
  testEndpoints.forEach(endpoint => {
    console.log(`curl http://localhost:3000${endpoint}`);
  });
} else {
  // Se estiver no browser, executar os testes
  testEndpoints.forEach(testEndpoint);
}
