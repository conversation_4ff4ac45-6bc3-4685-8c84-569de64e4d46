export const MusicalSequenceConfig = {
  // Notas musicais com propriedades de som e cor
  sounds: [
    { id: 'do', note: 'Dó', color: '#FF6B6B', frequency: 261.63 },
    { id: 're', note: 'Ré', color: '#4ECDC4', frequency: 293.66 },
    { id: 'mi', note: 'Mi', color: '#45B7D1', frequency: 329.63 },
    { id: 'fa', note: 'Fá', color: '#96CEB4', frequency: 349.23 },
    { id: 'sol', note: 'Sol', color: '#FFEAA7', frequency: 392.00 },
    { id: 'la', note: 'Lá', color: '#DDA0DD', frequency: 440.00 },
    { id: 'si', note: 'Si', color: '#98D8C8', frequency: 493.88 }
  ],

  // Níveis de dificuldade
  difficulties: [
    { id: 'easy', name: '<PERSON><PERSON>ci<PERSON>', sequenceLength: 3 },
    { id: 'medium', name: '<PERSON><PERSON><PERSON>', sequenceLength: 5 },
    { id: 'hard', name: 'Difícil', sequenceLength: 7 }
  ],

  // Mensagens de encorajamento
  encouragingMessages: [
    'Muito bem! Você tem um ótimo ouvido musical! 🎵',
    'Excelente! Continue tocando! 🎶',
    'Fantástico! Você é um maestro! 🎼',
    'Incrível! Sua memória musical é impressionante! 🎹',
    'Parabéns! A música está no seu coração! ❤️'
  ],

  // Configurações do jogo
  gameSettings: {
    maxSequenceLength: 8,
    notePlayDuration: 300,
    sequenceDelay: 800,
    feedbackDuration: 3000,
    errorFeedbackDuration: 2000,
    levelProgressionRate: 3, // a cada 3 níveis, aumenta sequência
    basePoints: 10
  },

  // Fases do jogo
  gamePhases: {
    WAITING: 'waiting',
    SHOWING: 'showing',
    PLAYING: 'playing',
    FINISHED: 'finished'
  },

  // Informações do jogo
  gameInfo: {
    title: 'Sequência Musical',
    description: 'Desenvolva sua memória auditiva',
    icon: '🎵',
    category: 'music',
    ageRange: '4-12',
    skills: ['memória auditiva', 'sequenciação', 'percepção musical', 'concentração']
  }
}
