/**
 * 🎵 MUSICAL SEQUENCE V3 - JOGO DE SEQUÊNCIA MUSICAL COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useRef, useCallback, useContext } from 'react';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';
import styles from './MusicalSequence.module.css';
import { MusicalSequenceConfig } from './MusicalSequenceConfig';
import { MusicalSequenceMetrics } from './MusicalSequenceMetrics';
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { MusicalSequenceCollectorsHub } from './collectors/index.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🎯 SISTEMA DE ATIVIDADES REDESENHADO V3 - MUSICAL SEQUENCE
// Cada atividade testa diferentes funções cognitivas com layouts únicos
const ACTIVITY_TYPES = {
  SEQUENCE_REPRODUCTION: {
    id: 'sequence_reproduction',
    name: 'Reprodução de Sequência',
    icon: '🔄',
    description: 'Teste de memória auditiva sequencial',
    cognitiveFunction: 'auditory_sequential_memory',
    component: 'SequenceReproductionActivity'
  },
  RHYTHM_TIMING: {
    id: 'rhythm_timing',
    name: 'Sincronização Rítmica',
    icon: '⏱️',
    description: 'Teste de coordenação temporal e atenção sustentada',
    cognitiveFunction: 'temporal_coordination_attention',
    component: 'RhythmTimingActivity'
  },
  PITCH_DISCRIMINATION: {
    id: 'pitch_discrimination',
    name: 'Discriminação de Altura',
    icon: '🎵',
    description: 'Teste de processamento auditivo e discriminação fina',
    cognitiveFunction: 'auditory_processing_discrimination',
    component: 'PitchDiscriminationActivity'
  },
  PATTERN_PREDICTION: {
    id: 'pattern_prediction',
    name: 'Predição de Padrões',
    icon: '🔮',
    description: 'Teste de raciocínio lógico e reconhecimento de padrões',
    cognitiveFunction: 'logical_reasoning_pattern_recognition',
    component: 'PatternPredictionActivity'
  },
  CREATIVE_EXPRESSION: {
    id: 'creative_expression',
    name: 'Expressão Criativa',
    icon: '🎨',
    description: 'Teste de flexibilidade cognitiva e criatividade',
    cognitiveFunction: 'cognitive_flexibility_creativity',
    component: 'CreativeExpressionActivity'
  }
};

const MusicalSequenceGame = ({ onBack, onComplete }) => {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const { settings } = useAccessibilityContext();

  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎵 Inicializar coletores avançados de sequência musical
  const [collectorsHub] = useState(() => new MusicalSequenceCollectorsHub());

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'EASY',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades redesenhado (5 atividades distintas)
    currentActivity: ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id,
    activityCycle: [
      ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id,
      ACTIVITY_TYPES.RHYTHM_TIMING.id,
      ACTIVITY_TYPES.PITCH_DISCRIMINATION.id,
      ACTIVITY_TYPES.PATTERN_PREDICTION.id,
      ACTIVITY_TYPES.CREATIVE_EXPRESSION.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      sequenceReproduction: {
        sequence: [],
        userSequence: [],
        isPlaying: false
      },
      rhythmPatterns: {
        pattern: [],
        userPattern: [],
        tempo: 120
      },
      melodyCompletion: {
        melody: [],
        missingNotes: [],
        userCompletion: []
      },
      instrumentRecognition: {
        currentInstrument: null,
        options: [],
        selectedInstrument: null
      },
      musicalMemory: {
        sequences: [],
        currentSequence: 0,
        userRecall: []
      },
      creativeComposition: {
        userComposition: [],
        availableNotes: [],
        isRecording: false
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null,
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  // Integração com o sistema unificado Portal Betina V3
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('musical_sequence');

  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration('musical_sequence', collectorsHub);

  // 🎯 Hook orquestrador terapêutico
  const {
    processGameMetrics: recordTherapeuticActivity,
    getRecommendations: getTherapeuticRecommendations,
    setUserContext: setTherapeuticContext
  } = useTherapeuticOrchestrator({ userId: user?.id });

  // Estados principais (definidos cedo para evitar erro de inicialização)
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [difficulty, setDifficulty] = useState('easy');
  const [sequence, setSequence] = useState([]);
  const [playerSequence, setPlayerSequence] = useState([]);
  const [currentLevel, setCurrentLevel] = useState(1);
  const [currentRound, setCurrentRound] = useState(1);

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      metricsRef.current.connectToBackend({
        recordInteraction,
        updateMetrics
      });
    }
  }, [recordInteraction, updateMetrics]);

  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);

  // Toggle TTS
  const toggleTTS = useCallback(() => {
    const newTtsState = !ttsActive;
    setTtsActive(newTtsState);

    // Cancelar qualquer fala em andamento se desabilitando
    if (!newTtsState && 'speechSynthesis' in window) {
      window.speechSynthesis.cancel();
    } else if (newTtsState) {
      speak('TTS ativado');
    }
  }, [ttsActive, speak]);

  // 🎯 FUNÇÕES DE CONTROLE DO JOGO PADRONIZADAS

  // Inicializar métricas
  useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = MusicalSequenceMetrics;
    }
  }, []);

  // Função para iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    try {
      // Esconder tela inicial primeiro
      setShowStartScreen(false);
      
      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: selectedDifficulty,
        roundStartTime: Date.now()
      }));

      // Inicializar sessão unificada
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: 'musical_sequence',
          difficulty: selectedDifficulty,
          userId: user?.id || 'anonymous'
        });
      }

      // Inicializar sessões dos hooks
      await initMultisensory();

      // Configurar contexto terapêutico se usuário válido
      if (user?.id && user.id !== 'anonymous' && user.id !== '') {
        setTherapeuticContext(user.id);
      }

      // Gerar primeira atividade
      generateNewRound();

      speak('Jogo iniciado! Vamos começar com sequências musicais.');
    } catch (error) {
      console.error('Erro ao iniciar jogo:', error);
    }
  }, [startUnifiedSession, initMultisensory, setTherapeuticContext, user, speak, setShowStartScreen]);

  // Função para trocar atividade - LIVRE PARA TROCAR A QUALQUER MOMENTO
  const changeActivity = useCallback((activityId) => {
    console.log('🎯 Changing musical activity to:', activityId);

    setGameState(prev => {
      console.log('🔄 Current musical state before change:', {
        currentActivity: prev.currentActivity,
        targetActivity: activityId
      });

      // Resetar estado para nova atividade
      const newState = {
        ...prev,
        currentActivity: activityId,
        activityRoundCount: 0,
        activityIndex: prev.activityCycle.indexOf(activityId),
        showFeedback: false,
        roundStartTime: Date.now()
      };

      console.log('✅ New musical state after change:', newState);
      return newState;
    });

    // Resetar sequências
    setSequence([]);
    setPlayerSequence([]);
    setActiveInstrument(null);
    setIsPlaying(false);

    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    speak(`Mudando para: ${activity?.name}`);

    // Gerar nova sequência para a nova atividade após um pequeno delay
    setTimeout(() => {
      generateNewRound(activityId);
    }, 500);
  }, [speak]);

  // Função para gerar nova rodada
  const generateNewRound = useCallback((activityId = null) => {
    const currentActivity = activityId || gameState.currentActivity;
    console.log('🎵 Generating new round for activity:', currentActivity);

    setGameState(prev => ({
      ...prev,
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ''
    }));

    // Gerar dados específicos da atividade redesenhada
    switch (currentActivity) {
      case ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id:
        generateSequenceReproduction();
        break;
      case ACTIVITY_TYPES.RHYTHM_TIMING.id:
        generateRhythmTiming();
        break;
      case ACTIVITY_TYPES.PITCH_DISCRIMINATION.id:
        generatePitchDiscrimination();
        break;
      case ACTIVITY_TYPES.PATTERN_PREDICTION.id:
        generatePatternPrediction();
        break;
      case ACTIVITY_TYPES.CREATIVE_EXPRESSION.id:
        generateCreativeExpression();
        break;
      default:
        generateSequenceReproduction();
    }
  }, [gameState.currentActivity]);

  // =====================================================
  // 🎯 FUNÇÕES DE GERAÇÃO REDESENHADAS - ATIVIDADES DISTINTAS
  // =====================================================

  // 🔄 REPRODUÇÃO DE SEQUÊNCIA - Memória auditiva sequencial
  const generateSequenceReproduction = useCallback(() => {
    console.log('🔄 Generating sequence reproduction activity');

    const difficultyMap = { 'easy': 3, 'medium': 4, 'hard': 5 };
    const sequenceLength = difficultyMap[difficulty] || 3;
    const newSequence = [];

    // Gerar sequência de instrumentos únicos (sem repetição)
    const availableInstruments = [...instruments];
    for (let i = 0; i < sequenceLength; i++) {
      const randomIndex = Math.floor(Math.random() * availableInstruments.length);
      const selectedInstrument = availableInstruments.splice(randomIndex, 1)[0];
      newSequence.push(selectedInstrument.id);
    }

    setSequence(newSequence);
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        sequenceReproduction: {
          sequence: newSequence,
          userSequence: [],
          currentStep: 0,
          showingSequence: true,
          allowInput: false
        }
      }
    }));

    speak(`Atividade: Reprodução de Sequência. Memorize a ordem de ${sequenceLength} instrumentos diferentes.`);
  }, [difficulty, speak]);

  // ⏱️ SINCRONIZAÇÃO RÍTMICA - Coordenação temporal e atenção sustentada
  const generateRhythmTiming = useCallback(() => {
    console.log('⏱️ Generating rhythm timing activity');

    // Gerar padrão rítmico com intervalos específicos (em ms)
    const rhythmPatterns = {
      'easy': [500, 500, 1000, 500],      // Simples: curto-curto-longo-curto
      'medium': [300, 600, 300, 900, 300], // Médio: variações mais complexas
      'hard': [200, 400, 200, 800, 200, 400] // Difícil: padrão mais longo e complexo
    };

    const pattern = rhythmPatterns[difficulty] || rhythmPatterns['easy'];
    const targetBPM = 120; // Batidas por minuto

    setSequence([]); // Não usa sequência de instrumentos
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        rhythmTiming: {
          pattern: pattern,
          userTaps: [],
          targetBPM: targetBPM,
          isRecording: false,
          showMetronome: true,
          currentBeat: 0,
          accuracy: 0
        }
      }
    }));

    speak(`Atividade: Sincronização Rítmica. Toque no ritmo do metrônomo com precisão temporal.`);
  }, [difficulty, speak]);

  // 🎵 DISCRIMINAÇÃO DE ALTURA - Processamento auditivo fino
  const generatePitchDiscrimination = useCallback(() => {
    console.log('🎵 Generating pitch discrimination activity');

    // Gerar tons com diferenças sutis de altura (frequências)
    const pitchLevels = {
      'easy': [220, 240, 260, 280],       // Diferenças grandes (20Hz)
      'medium': [220, 230, 240, 250],     // Diferenças médias (10Hz)
      'hard': [220, 225, 230, 235]        // Diferenças pequenas (5Hz)
    };

    const availablePitches = pitchLevels[difficulty] || pitchLevels['easy'];
    const referencePitch = availablePitches[0];
    const testPitches = availablePitches.slice(1);

    // Escolher um tom de teste aleatório
    const targetPitch = testPitches[Math.floor(Math.random() * testPitches.length)];

    setSequence([]); // Não usa sequência de instrumentos
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        pitchDiscrimination: {
          referencePitch: referencePitch,
          targetPitch: targetPitch,
          testPitches: testPitches,
          currentPhase: 'reference', // 'reference', 'test', 'answer'
          userAnswer: null,
          showVisualAid: false
        }
      }
    }));

    speak(`Atividade: Discriminação de Altura. Compare tons musicais e identifique diferenças sutis.`);
  }, [difficulty, speak]);

  // 🔮 PREDIÇÃO DE PADRÕES - Raciocínio lógico e reconhecimento de padrões
  const generatePatternPrediction = useCallback(() => {
    console.log('🔮 Generating pattern prediction activity');

    // Gerar padrões lógicos musicais
    const patternTypes = {
      'ascending': [0, 1, 2, 3], // Sequência crescente
      'descending': [3, 2, 1, 0], // Sequência decrescente
      'alternating': [0, 2, 1, 3], // Padrão alternado
      'repeating': [0, 1, 0, 1], // Padrão repetitivo
      'fibonacci': [0, 1, 1, 2] // Sequência fibonacci
    };

    const patternNames = Object.keys(patternTypes);
    const selectedPattern = patternNames[Math.floor(Math.random() * patternNames.length)];
    const basePattern = patternTypes[selectedPattern];

    // Mapear números para instrumentos
    const patternInstruments = basePattern.map(index => instruments[index % instruments.length].id);

    // Mostrar padrão incompleto (remover último elemento)
    const incompletePattern = patternInstruments.slice(0, -1);
    const correctAnswer = patternInstruments[patternInstruments.length - 1];

    setSequence(incompletePattern);
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        patternPrediction: {
          incompletePattern: incompletePattern,
          correctAnswer: correctAnswer,
          patternType: selectedPattern,
          options: instruments.slice(0, 4).map(inst => inst.id),
          userAnswer: null,
          showPattern: true
        }
      }
    }));

    speak(`Atividade: Predição de Padrões. Analise o padrão musical e preveja o próximo elemento.`);
  }, [speak]);

  // 🎨 EXPRESSÃO CRIATIVA - Flexibilidade cognitiva e criatividade
  const generateCreativeExpression = useCallback(() => {
    console.log('🎨 Generating creative expression activity');

    // Desafios criativos com restrições específicas
    const creativePrompts = {
      'easy': {
        constraint: 'use_3_instruments',
        description: 'Crie uma melodia usando exatamente 3 instrumentos diferentes',
        minLength: 4,
        maxLength: 6
      },
      'medium': {
        constraint: 'emotional_theme',
        description: 'Crie uma melodia que expresse alegria usando 4-5 instrumentos',
        minLength: 5,
        maxLength: 8
      },
      'hard': {
        constraint: 'rhythmic_pattern',
        description: 'Crie uma composição com padrão rítmico específico: forte-fraco-forte-fraco',
        minLength: 6,
        maxLength: 10
      }
    };

    const prompt = creativePrompts[difficulty] || creativePrompts['easy'];

    setSequence([]); // Começar vazio para criação livre
    setPlayerSequence([]);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        creativeExpression: {
          prompt: prompt,
          userComposition: [],
          isRecording: false,
          canPlayback: false,
          creativityScore: 0,
          uniquenessScore: 0
        }
      }
    }));

    speak(`Atividade: Expressão Criativa. ${prompt.description}`);
  }, [difficulty, speak]);

  // Função para alternar TTS (compatibilidade)
  const toggleTTSOld = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('musicalSequence_ttsActive', JSON.stringify(newState));
      
      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      
      return newState;
    });
  }, []);
  
  // Estados principais (já definidos acima)
  const [score, setScore] = useState(0);
  const [streak, setStreak] = useState(0);
  const [totalAttempts, setTotalAttempts] = useState(0);
  const [correctAttempts, setCorrectAttempts] = useState(0);
  const [activeInstrument, setActiveInstrument] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [feedback, setFeedback] = useState({ show: false, type: '', message: '' });
  const [gameStartTime, setGameStartTime] = useState(null);
  const [maxRoundsPerLevel] = useState(3);
  const audioRef = useRef({});
  const audioContextRef = useRef(null);

  // Sistema de coleta de dados próprio do MusicalSequence
  const collectorsHubRef = useRef(null);
  const [metricsSession, setMetricsSession] = useState({
    sessionId: null,
    isActive: false,
    insights: {}
  });

  // Função para enviar métricas usando o sistema próprio
  const sendMetrics = useCallback(async (metricsData) => {
    try {
      // Usar MusicalSequenceMetrics que já gerencia o hub de coletores
      MusicalSequenceMetrics.recordAdvancedInteraction({
        ...metricsData,
        sessionId: metricsSession.sessionId,
        timestamp: Date.now()
      });
      return true;
    } catch (error) {
      console.error('Erro ao enviar métricas:', error);
      return false;
    }
  }, [metricsSession.sessionId]);
  
  const getInsights = useCallback(async () => {
    try {
      const hub = MusicalSequenceMetrics.getCollectorsHub();
      if (hub) {
        return hub.getConsolidatedInsights?.() || {};
      }
      return {};
    } catch (error) {
      console.error('Erro ao obter insights:', error);
      return {};
    }
  }, []);

  // Propriedade de conveniência para manter compatibilidade
  const orchestratorReady = metricsSession.isActive;

  const instruments = [
    { id: 'piano', name: 'Piano', emoji: '🎹', color: '#FF6B6B', sound: 'piano' },
    { id: 'guitar', name: 'Violão', emoji: '🎸', color: '#4ECDC4', sound: 'guitar' },
    { id: 'drum', name: 'Bateria', emoji: '🥁', color: '#45B7D1', sound: 'drum' },
    { id: 'flute', name: 'Flauta', emoji: '🎵', color: '#96CEB4', sound: 'flute' },
    { id: 'violin', name: 'Violino', emoji: '🎻', color: '#A8E6CF', sound: 'violin' },
    { id: 'sax', name: 'Saxofone', emoji: '🎷', color: '#FFD93D', sound: 'sax' }
  ];

  const explainGame = useCallback(() => {
    speak('Jogo de Sequência Musical. Escute a sequência de instrumentos e reproduza-a na ordem correta. Desenvolva sua memória auditiva e percepção musical!', {
      rate: 0.8
    });
  }, [speak]);

  const getAccuracy = useCallback(() => {
    if (totalAttempts === 0) return 100;
    return Math.round((correctAttempts / totalAttempts) * 100);
  }, [totalAttempts, correctAttempts]);

  // Função para inicializar AudioContext
  const initAudioContext = useCallback(() => {
    if (!audioContextRef.current || audioContextRef.current.state === 'closed') {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    }
  }, []);

  // Função para tocar um som específico
  const playSound = useCallback(async (note, duration = 500) => {
    if (!audioContextRef.current) {
      console.warn('AudioContext não iniciado. Iniciando automaticamente...');
      await initAudioContext();
    }

    try {
      const audioContext = audioContextRef.current;
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      const frequencies = {
        'C': 261.63, 'D': 293.66, 'E': 329.63, 'F': 349.23,
        'G': 392.00, 'A': 440.00, 'B': 493.88
      };
      
      oscillator.frequency.setValueAtTime(frequencies[note] || 440, audioContext.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);
      
      return new Promise(resolve => {
        oscillator.onended = resolve;
      });
    } catch (error) {
      console.error('Erro ao tocar som:', error);
    }
  }, [initAudioContext]);

  // Função para tocar sequência com array
  const playSequenceWithArray = useCallback(async (sequenceArray) => {
    if (isPlaying) return;
    
    setIsPlaying(true);
    console.log('🎵 Reproduzindo sequência:', sequenceArray);
    
    for (let i = 0; i < sequenceArray.length; i++) {
      const note = sequenceArray[i];
      setActiveInstrument(note);
      
      await playSound(note, 600);
      await new Promise(resolve => setTimeout(resolve, 100));
      
      setActiveInstrument(null);
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    setIsPlaying(false);
    
    // TTS automático após reproduzir a sequência
    if (ttsEnabled) {
      setTimeout(() => {
        speak(`Sequência reproduzida! Agora é sua vez de repetir ${sequenceArray.length} notas.`, {
          rate: 0.9,
          onEnd: () => console.log('Instrução pós-sequência anunciada')
        });
      }, 500);
    }
  }, [isPlaying, setActiveInstrument, playSound, setIsPlaying, setGameState, ttsEnabled, speak]);

  // ✅ DECLARAR generateNewSequence ANTES DOS useEffects QUE A UTILIZAM
  const generateNewSequence = useCallback(async () => {
    console.log('generateNewSequence called with difficulty:', difficulty);

    // Usar dificuldade simples se não encontrar configuração
    const difficultyMap = {
      'easy': 3,
      'medium': 4,
      'hard': 5
    };

    const sequenceLength = difficultyMap[difficulty] || 3;
    console.log('sequenceLength:', sequenceLength);

    const newSequence = [];
    for (let i = 0; i < sequenceLength; i++) {
      const randomInstrument = instruments[Math.floor(Math.random() * instruments.length)];
      newSequence.push(randomInstrument.id);
    }

    console.log('Generated sequence:', newSequence);
    setSequence(newSequence);
    setPlayerSequence([]);
    setFeedback({ show: false, type: '', message: '' });

    // Coleta avançada de dados da nova sequência
    try {
      const sequenceGenerationData = {
        timestamp: Date.now(),
        sequenceLength: newSequence.length,
        difficulty,
        level: currentLevel,
        round: currentRound,
        sequence: newSequence,
        sessionDuration: Date.now() - gameStartTime,
        gameState: 'sequence_generated'
      };

      await collectorsHub.processInteraction(sequenceGenerationData);

      // Registrar com backend
      if (recordInteraction) {
        recordInteraction({
          type: 'sequence_generation',
          data: sequenceGenerationData
        });
      }
    } catch (error) {
      console.warn('⚠️ Erro ao coletar dados da sequência:', error);
    }
  }, [difficulty, currentLevel, currentRound, gameStartTime, collectorsHub, recordInteraction]);

  useEffect(() => {
    // Inicializar AudioContext apenas quando necessário
    
    instruments.forEach(instrument => {
      audioRef.current[instrument.id] = {
        play: () => {
          try {
            // Inicializar AudioContext se necessário
            initAudioContext();
            
            // Verificar se o contexto de áudio ainda está ativo
            if (audioContextRef.current.state === 'closed') {
              console.warn('AudioContext fechado, não é possível reproduzir som');
              return;
            }
            
            // Resumir contexto se estiver suspenso
            if (audioContextRef.current.state === 'suspended') {
              audioContextRef.current.resume();
            }
            
            const oscillator = audioContextRef.current.createOscillator();
            const gainNode = audioContextRef.current.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContextRef.current.destination);
            
            const frequencies = {
              piano: 523.25, // C5
              guitar: 329.63, // E4
              drum: 146.83,  // D3
              flute: 440.00, // A4
              violin: 659.25, // E5
              sax: 293.66    // D4
            };
            
            oscillator.frequency.setValueAtTime(frequencies[instrument.id] || 440, audioContextRef.current.currentTime);
            oscillator.type = instrument.id === 'drum' ? 'square' : 'sine';
            
            gainNode.gain.setValueAtTime(0.3, audioContextRef.current.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContextRef.current.currentTime + 0.5);
            
            oscillator.start(audioContextRef.current.currentTime);
            oscillator.stop(audioContextRef.current.currentTime + 0.5);
          } catch (error) {
            console.error('Error playing sound:', error);
          }
        }
      };
    });

    return () => {
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        audioContextRef.current.close();
      }
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [initAudioContext]);  // Auto-start sequence when game begins
  // Auto-start sequence when game begins - SIMPLIFICADO E CONTROLADO
  useEffect(() => {
    if (!showStartScreen && gameState.status === 'playing' && difficulty && sequence.length === 0) {
      console.log('✅ Auto-starting sequence after game start...');

      // Gerar sequência simples diretamente
      const difficultyMap = { 'easy': 3, 'medium': 4, 'hard': 5 };
      const sequenceLength = difficultyMap[difficulty] || 3;
      const newSequence = [];

      for (let i = 0; i < sequenceLength; i++) {
        const randomInstrument = instruments[Math.floor(Math.random() * instruments.length)];
        newSequence.push(randomInstrument.id);
      }

      setSequence(newSequence);
      setPlayerSequence([]);
    }
  }, [showStartScreen, difficulty]); // Dependências mínimas

  // Auto-play sequence when it's generated - CONTROLADO
  useEffect(() => {
    if (sequence.length > 0 && !isPlaying && !showStartScreen) {
      console.log('✅ Auto-playing sequence after generation...');
      const timer = setTimeout(() => {
        playSequence();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [sequence]); // Apenas sequence como dependência

  // Função para tocar som de instrumento
  const playSoundInstrument = useCallback(async (instrumentId) => {
    try {
      if (audioRef.current[instrumentId]) {
        audioRef.current[instrumentId].play();
      }
    } catch (error) {
      console.error('Error playing sound:', error);
    }
  }, []);

  // Função para tocar a sequência gerada
  const playSequence = useCallback(async () => {
    console.log('playSequence called with sequence:', sequence);
    setIsPlaying(true);
    
    // Anunciar início da sequência
    speak(`Agora ouça atentamente a sequência de ${sequence.length} instrumentos.`, {
      rate: 1.0
    });
    
    // Aguardar o TTS terminar antes de tocar a sequência
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    for (let i = 0; i < sequence.length; i++) {
      console.log(`Playing instrument ${i}:`, sequence[i]);
      setActiveInstrument(sequence[i]);
      await playSoundInstrument(sequence[i]);
      await new Promise(resolve => setTimeout(resolve, 600));
      setActiveInstrument(null);
      await new Promise(resolve => setTimeout(resolve, 400));
    }
    
    console.log('Sequence finished, ready for user input');
    setIsPlaying(false);
    
    // Anunciar fim da sequência
    speak('Agora é sua vez! Toque os instrumentos na ordem correta.', {
      rate: 1.0
    });
  }, [sequence, speak, playSound, setIsPlaying, setGameState, setActiveInstrument]);

  const handleInstrumentClick = useCallback(async (instrumentId) => {
    console.log('handleInstrumentClick called:', { instrumentId, gameState: gameState.status, isPlaying, playerSequenceLength: playerSequence.length });

    // Permitir cliques apenas quando não está tocando a sequência
    if (isPlaying) {
      console.log('Click ignorado - aguarde a sequência terminar');
      return;
    }

    // Permitir cliques se o jogo está ativo e há uma sequência para comparar
    if (gameState.status !== 'playing' || sequence.length === 0) {
      console.log('Click ignorado - jogo não iniciado ou sem sequência');
      return;
    }
    
    const nextIndex = playerSequence.length;
    const expectedInstrument = sequence[nextIndex];
    const startTime = Date.now();
    
    console.log('Expected:', expectedInstrument, 'Clicked:', instrumentId);
    
    setActiveInstrument(instrumentId);
    await playSoundInstrument(instrumentId);

    setTimeout(() => setActiveInstrument(null), 500);
    
    const newPlayerSequence = [...playerSequence, instrumentId];
    setPlayerSequence(newPlayerSequence);
    setTotalAttempts(prev => prev + 1);
    
    const isCorrect = instrumentId === expectedInstrument;
    const responseTime = Date.now() - startTime;
    
    // Coleta avançada de dados da interação
    try {
      const interactionData = {
        type: 'instrument_click',
        action: 'sequence_attempt',
        instrumentClicked: instrumentId,
        expectedInstrument: expectedInstrument,
        isCorrect: isCorrect,
        sequencePosition: nextIndex,
        totalSequenceLength: sequence.length,
        currentSequence: sequence,
        playerSequence: newPlayerSequence,
        responseTime: responseTime,
        difficulty: difficulty,
        level: currentLevel,
        round: currentRound,
        streak: streak,
        gameState: gameState.status,
        timestamp: Date.now(),
        // Dados específicos para análise de memória auditiva
        memoryLoad: sequence.length,
        sequenceProgress: (nextIndex + 1) / sequence.length,
        // Dados para análise de padrões musicais
        instrumentFrequency: newPlayerSequence.filter(inst => inst === instrumentId).length,
        lastInstruments: newPlayerSequence.slice(-3),
        // Dados para análise de execução
        attemptNumber: totalAttempts + 1,
        sessionTime: Date.now() - gameStartTime
      };
      
      // Processar com coletores avançados
      MusicalSequenceMetrics.recordAdvancedInteraction(interactionData);
      
      // Enviar para orquestrador central se disponível
      if (orchestratorReady && sendMetrics) {
        sendMetrics({
          gameType: 'musical_sequence',
          sessionId: `musical_${gameStartTime}`,
          metrics: interactionData,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Erro na coleta avançada:', error);
    }
    
    if (!isCorrect) {
      console.log('Wrong instrument!');
      setStreak(0);
      setFeedback({
        show: true,
        type: 'error',
        message: 'Ops! Instrumento incorreto. Vamos tentar novamente!'
      });
      
      // Feedback sonoro TTS para erro
      speak('Ops! Instrumento incorreto. Vamos tentar novamente!', {
        rate: 0.9,
        pitch: 0.8
      });
      
      setTimeout(() => {
        setFeedback({ show: false, type: '', message: '' });
        setPlayerSequence([]);
        playSequence();
      }, 2000);
      return;
    }
    
    // Correct instrument
    console.log('Correct instrument!');
    setCorrectAttempts(prev => prev + 1);
      // Check if sequence is complete
    if (newPlayerSequence.length === sequence.length) {
    console.log('Sequence completed!');
    const roundScore = sequence.length * 10;
    setScore(prev => prev + roundScore);
    const newStreak = streak + 1;
    setStreak(newStreak);
    
    // Coleta avançada de dados da sequência completa
    try {
      const sequenceCompletionData = {
        type: 'sequence_completion',
        action: 'sequence_completed',
        targetSequence: sequence,
        playerSequence: newPlayerSequence,
        isCorrect: true,
        completionTime: Date.now() - gameStartTime,
        difficulty: difficulty,
        level: currentLevel,
        round: currentRound,
        score: score + roundScore,
        newStreak: newStreak,
        totalAttempts: totalAttempts + 1,
        correctAttempts: correctAttempts + 1,
        // Dados específicos para análise de aprendizado
        sequenceLength: sequence.length,
        accuracy: ((correctAttempts + 1) / (totalAttempts + 1)) * 100,
        improvementIndicator: newStreak > streak,
        timestamp: Date.now()
      };
      
      MusicalSequenceMetrics.recordAdvancedInteraction(sequenceCompletionData);
      
      if (orchestratorReady && sendMetrics) {
        sendMetrics({
          gameType: 'musical_sequence',
          sessionId: `musical_${gameStartTime}`,
          metrics: sequenceCompletionData,
          timestamp: Date.now()
        });
      }
    } catch (error) {
      console.error('Erro na coleta de dados de sequência completa:', error);
    }
    
    setFeedback({
      show: true,
      type: 'success',
      message: '🎉 Excelente! Sequência correta!'
    });
    
    // Feedback sonoro TTS para sucesso
    speak('Excelente! Sequência correta! Parabéns!', {
      rate: 1.0,
      pitch: 1.2
    });
    
    setTimeout(() => {
      setFeedback({ show: false, type: '', message: '' });

      if (newStreak % 3 === 0) {
        setCurrentLevel(prev => prev + 1);
      }      setPlayerSequence([]);
      generateNewSequence();
    }, 2000);
    }
  }, [gameState, isPlaying, playerSequence, sequence, totalAttempts, setActiveInstrument, playSound, setPlayerSequence, setTotalAttempts, difficulty, currentLevel, currentRound, streak, gameStartTime, correctAttempts, score, setStreak, setFeedback, speak, setCorrectAttempts, setScore, setCurrentLevel, generateNewSequence, orchestratorReady, sendMetrics]);

  const handleTTSClick = useCallback(() => {
    speak("Sequência Musical. Escute a sequência de instrumentos musicais e repita tocando na mesma ordem. Use os botões da sequência para reproduzir a ordem que você ouviu.");
  }, [speak]);

  // =====================================================
  // 🎵 INTERFACE ESPECÍFICA PARA CADA ATIVIDADE MUSICAL
  // =====================================================

  // =====================================================
  // 🎯 INTERFACES REDESENHADAS - LAYOUTS ÚNICOS PARA CADA ATIVIDADE
  // =====================================================

  const renderActivityInterface = () => {
    const currentActivity = gameState.currentActivity;

    switch (currentActivity) {
      case ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id:
        return renderSequenceReproduction();
      case ACTIVITY_TYPES.RHYTHM_TIMING.id:
        return renderRhythmTiming();
      case ACTIVITY_TYPES.PITCH_DISCRIMINATION.id:
        return renderPitchDiscrimination();
      case ACTIVITY_TYPES.PATTERN_PREDICTION.id:
        return renderPatternPrediction();
      case ACTIVITY_TYPES.CREATIVE_EXPRESSION.id:
        return renderCreativeExpression();
      default:
        return renderSequenceReproduction();
    }
  };

  // 🔄 REPRODUÇÃO DE SEQUÊNCIA - Layout padrão LetterRecognition
  const renderSequenceReproduction = () => {
    const activityData = gameState.activityData?.sequenceReproduction || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔄 Teste de Memória Auditiva Sequencial</h2>
          <p className={styles.questionSubtitle}>Memorize a ordem exata dos instrumentos e reproduza a sequência</p>
        </div>

        {/* Área de objetos - padrão LetterRecognition */}
        <div className={styles.objectsDisplay}>
          <div className={styles.sequenceSlots}>
            {sequence.map((instrumentId, index) => {
              const instrument = instruments.find(inst => inst.id === instrumentId);
              const isCurrentlyPlaying = isPlaying && activeInstrument === instrumentId;
              const isCompleted = playerSequence.length > index;
              const isCorrect = isCompleted && playerSequence[index] === instrumentId;

              return (
                <div
                  key={index}
                  className={`${styles.countingObject} ${
                    isCurrentlyPlaying ? styles.playing : ''
                  } ${isCompleted ? (isCorrect ? styles.correct : styles.incorrect) : ''}`}
                  style={{
                    backgroundColor: isCorrect ? 'rgba(76, 175, 80, 0.3)' :
                                   isCompleted ? 'rgba(244, 67, 54, 0.3)' :
                                   'var(--card-background)',
                    border: '2px solid rgba(255,255,255,0.3)',
                    borderRadius: '12px',
                    padding: '1rem',
                    margin: '0.5rem',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    minWidth: '80px'
                  }}
                >
                  <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>{index + 1}</div>
                  <div style={{ fontSize: '2rem', margin: '0.5rem 0' }}>
                    {!isPlaying || isCurrentlyPlaying ? instrument?.emoji : '?'}
                  </div>
                  <div style={{ fontSize: '0.8rem' }}>{instrument?.name}</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Opções de resposta - padrão LetterRecognition */}
        <div className={styles.answerOptions}>
          {instruments.map((instrument) => (
            <button
              key={instrument.id}
              className={`${styles.answerButton} ${
                activeInstrument === instrument.id ? styles.playing : ''
              }`}
              onClick={() => handleInstrumentClick(instrument.id)}
              disabled={isPlaying || playerSequence.length >= sequence.length}
              aria-label={`Escolher ${instrument.name}`}
            >
              <div style={{
                fontSize: '2rem',
                marginBottom: '0.5rem'
              }}>
                {instrument.emoji}
              </div>
              <div style={{ fontSize: '0.9rem' }}>
                {instrument.name}
              </div>
            </button>
          ))}
        </div>

        {/* Progresso */}
        {playerSequence.length > 0 && (
          <div style={{
            textAlign: 'center',
            marginTop: '1rem',
            color: 'rgba(255,255,255,0.8)'
          }}>
            Progresso: {playerSequence.length} / {sequence.length}
          </div>
        )}
      </div>
    );
  };

  // ⏱️ SINCRONIZAÇÃO RÍTMICA - Layout padrão LetterRecognition
  const renderRhythmTiming = () => {
    const activityData = gameState.activityData?.rhythmTiming || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>⏱️ Teste de Coordenação Temporal</h2>
          <p className={styles.questionSubtitle}>Sincronize seus toques com o metrônomo. Precisão temporal é fundamental!</p>
        </div>

        {/* Área de objetos - padrão LetterRecognition */}
        <div className={styles.objectsDisplay}>
          <div className={styles.metronomeDisplay} style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '2rem'
          }}>
            <div style={{
              fontSize: '4rem',
              animation: isPlaying ? 'pulse 1s infinite' : 'none'
            }}>
              🎼
            </div>
            <div style={{ fontSize: '1.2rem', color: 'rgba(255,255,255,0.8)' }}>
              BPM: {activityData.targetBPM || 120}
            </div>
            <div style={{ fontSize: '1rem', color: 'rgba(255,255,255,0.6)' }}>
              Batida: {activityData.currentBeat || 0}
            </div>
          </div>
        </div>

        {/* Opções de resposta - padrão LetterRecognition */}
        <div className={styles.answerOptions}>
          <button
            className={`${styles.answerButton} ${activeInstrument ? styles.playing : ''}`}
            onClick={() => {
              const now = Date.now();
              setActiveInstrument('tap');
              setTimeout(() => setActiveInstrument(null), 200);

              // Auto-iniciar gravação se não estiver gravando
              if (!activityData.isRecording) {
                setGameState(prev => ({
                  ...prev,
                  activityData: {
                    ...prev.activityData,
                    rhythmTiming: {
                      ...prev.activityData.rhythmTiming,
                      isRecording: true,
                      userTaps: [now]
                    }
                  }
                }));
                speak('Gravação iniciada! Continue tocando no ritmo.');
              } else {
                // Registrar tempo do toque para análise de precisão
                setGameState(prev => ({
                  ...prev,
                  activityData: {
                    ...prev.activityData,
                    rhythmTiming: {
                      ...prev.activityData.rhythmTiming,
                      userTaps: [...(prev.activityData.rhythmTiming?.userTaps || []), now]
                    }
                  }
                }));
              }
            }}
            disabled={isPlaying}
            aria-label="Tocar no ritmo"
            style={{
              minHeight: '120px',
              fontSize: '3rem',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '0.5rem',
              backgroundColor: activityData.isRecording ? 'rgba(244, 67, 54, 0.3)' : 'var(--card-background)',
              borderColor: activityData.isRecording ? 'rgba(244, 67, 54, 0.5)' : 'rgba(255,255,255,0.3)'
            }}
          >
            <div>{activityData.isRecording ? '🔴' : '🥁'}</div>
            <div style={{ fontSize: '1rem' }}>
              {activityData.isRecording ? 'GRAVANDO...' : 'TOQUE AQUI'}
            </div>
          </button>

          <button
            className={styles.answerButton}
            onClick={() => {
              setGameState(prev => ({
                ...prev,
                activityData: {
                  ...prev.activityData,
                  rhythmTiming: {
                    ...prev.activityData.rhythmTiming,
                    isRecording: !prev.activityData.rhythmTiming?.isRecording,
                    userTaps: []
                  }
                }
              }));
            }}
            aria-label={activityData.isRecording ? 'Parar gravação' : 'Iniciar gravação'}
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
              {activityData.isRecording ? '⏹️' : '▶️'}
            </div>
            <div style={{ fontSize: '0.9rem' }}>
              {activityData.isRecording ? 'Parar' : 'Iniciar'}
            </div>
          </button>
        </div>

        {/* Feedback de precisão */}
        {activityData.accuracy !== undefined && (
          <div style={{
            textAlign: 'center',
            marginTop: '1rem',
            color: 'rgba(255,255,255,0.8)'
          }}>
            Precisão: {activityData.accuracy || 0}%
          </div>
        )}
      </div>
    );
  };

  // 🎵 DISCRIMINAÇÃO DE ALTURA - Layout padrão LetterRecognition
  const renderPitchDiscrimination = () => {
    const activityData = gameState.activityData?.pitchDiscrimination || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🎵 Teste de Processamento Auditivo</h2>
          <p className={styles.questionSubtitle}>Compare tons musicais e identifique diferenças sutis de altura</p>
        </div>

        {/* Área de objetos - padrão LetterRecognition */}
        <div className={styles.objectsDisplay}>
          <div style={{
            display: 'flex',
            gap: '2rem',
            justifyContent: 'center',
            alignItems: 'center',
            flexWrap: 'wrap'
          }}>
            {/* Tom de Referência */}
            <div style={{
              textAlign: 'center',
              padding: '2rem',
              border: `3px solid ${activityData.currentPhase === 'reference' && isPlaying ? 'rgba(76, 175, 80, 0.8)' : 'rgba(255,255,255,0.3)'}`,
              borderRadius: '12px',
              backgroundColor: activityData.currentPhase === 'reference' && isPlaying ? 'rgba(76, 175, 80, 0.2)' : 'var(--card-background)',
              transform: activityData.currentPhase === 'reference' && isPlaying ? 'scale(1.05)' : 'scale(1)',
              transition: 'all 0.3s ease',
              minWidth: '200px'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🎯</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem', fontWeight: 'bold' }}>Tom Referência</div>

              {/* Visualizador de frequência */}
              <div style={{
                height: '60px',
                backgroundColor: 'rgba(0,0,0,0.3)',
                borderRadius: '8px',
                margin: '1rem 0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                overflow: 'hidden'
              }}>
                <div style={{
                  width: `${Math.min((activityData.referencePitch || 220) / 500 * 100, 100)}%`,
                  height: '100%',
                  backgroundColor: activityData.currentPhase === 'reference' && isPlaying ? 'rgba(76, 175, 80, 0.8)' : 'rgba(33, 150, 243, 0.6)',
                  borderRadius: '8px',
                  transition: 'all 0.3s ease',
                  animation: activityData.currentPhase === 'reference' && isPlaying ? 'pulse 0.5s infinite' : 'none'
                }}></div>
                <div style={{
                  position: 'absolute',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '0.9rem'
                }}>
                  {activityData.referencePitch || 220}Hz
                </div>
              </div>

              <button
                className={styles.answerButton}
                onClick={() => {
                  setIsPlaying(true);
                  setGameState(prev => ({
                    ...prev,
                    activityData: {
                      ...prev.activityData,
                      pitchDiscrimination: {
                        ...prev.activityData.pitchDiscrimination,
                        currentPhase: 'reference'
                      }
                    }
                  }));
                  setTimeout(() => setIsPlaying(false), 1500);
                }}
                disabled={isPlaying}
                style={{
                  fontSize: '0.9rem',
                  padding: '0.8rem 1.5rem',
                  backgroundColor: activityData.currentPhase === 'reference' && isPlaying ? 'rgba(76, 175, 80, 0.3)' : 'var(--card-background)'
                }}
              >
                {isPlaying && activityData.currentPhase === 'reference' ? '🔊 Tocando...' : '▶️ Tocar Referência'}
              </button>
            </div>

            <div style={{
              fontSize: '3rem',
              color: 'rgba(255,255,255,0.5)',
              fontWeight: 'bold'
            }}>VS</div>

            {/* Tom de Teste */}
            <div style={{
              textAlign: 'center',
              padding: '2rem',
              border: `3px solid ${activityData.currentPhase === 'test' && isPlaying ? 'rgba(255, 152, 0, 0.8)' : 'rgba(255,255,255,0.3)'}`,
              borderRadius: '12px',
              backgroundColor: activityData.currentPhase === 'test' && isPlaying ? 'rgba(255, 152, 0, 0.2)' : 'var(--card-background)',
              transform: activityData.currentPhase === 'test' && isPlaying ? 'scale(1.05)' : 'scale(1)',
              transition: 'all 0.3s ease',
              minWidth: '200px'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🎼</div>
              <div style={{ fontSize: '1.2rem', marginBottom: '1rem', fontWeight: 'bold' }}>Tom Teste</div>

              {/* Visualizador de frequência */}
              <div style={{
                height: '60px',
                backgroundColor: 'rgba(0,0,0,0.3)',
                borderRadius: '8px',
                margin: '1rem 0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                overflow: 'hidden'
              }}>
                <div style={{
                  width: `${Math.min((activityData.targetPitch || 240) / 500 * 100, 100)}%`,
                  height: '100%',
                  backgroundColor: activityData.currentPhase === 'test' && isPlaying ? 'rgba(255, 152, 0, 0.8)' : 'rgba(156, 39, 176, 0.6)',
                  borderRadius: '8px',
                  transition: 'all 0.3s ease',
                  animation: activityData.currentPhase === 'test' && isPlaying ? 'pulse 0.5s infinite' : 'none'
                }}></div>
                <div style={{
                  position: 'absolute',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '0.9rem'
                }}>
                  {activityData.targetPitch || 240}Hz
                </div>
              </div>

              <button
                className={styles.answerButton}
                onClick={() => {
                  setIsPlaying(true);
                  setGameState(prev => ({
                    ...prev,
                    activityData: {
                      ...prev.activityData,
                      pitchDiscrimination: {
                        ...prev.activityData.pitchDiscrimination,
                        currentPhase: 'test'
                      }
                    }
                  }));
                  setTimeout(() => setIsPlaying(false), 1500);
                }}
                disabled={isPlaying}
                style={{
                  fontSize: '0.9rem',
                  padding: '0.8rem 1.5rem',
                  backgroundColor: activityData.currentPhase === 'test' && isPlaying ? 'rgba(255, 152, 0, 0.3)' : 'var(--card-background)'
                }}
              >
                {isPlaying && activityData.currentPhase === 'test' ? '🔊 Tocando...' : '▶️ Tocar Teste'}
              </button>
            </div>
          </div>

          {/* Indicador de diferença visual */}
          <div style={{
            textAlign: 'center',
            marginTop: '2rem',
            padding: '1rem',
            backgroundColor: 'rgba(0,0,0,0.3)',
            borderRadius: '8px'
          }}>
            <div style={{ fontSize: '1rem', marginBottom: '0.5rem', color: 'rgba(255,255,255,0.8)' }}>
              📊 Diferença de Frequência: {Math.abs((activityData.targetPitch || 240) - (activityData.referencePitch || 220))}Hz
            </div>
            <div style={{ fontSize: '0.9rem', color: 'rgba(255,255,255,0.6)' }}>
              {(activityData.targetPitch || 240) > (activityData.referencePitch || 220) ?
                '⬆️ Tom teste é mais agudo' :
                (activityData.targetPitch || 240) < (activityData.referencePitch || 220) ?
                '⬇️ Tom teste é mais grave' :
                '➡️ Tons são iguais'
              }
            </div>
          </div>
        </div>

        {/* Opções de resposta - padrão LetterRecognition */}
        <div className={styles.answerOptions}>
          <button
            className={`${styles.answerButton} ${
              activityData.userAnswer === 'higher' ? styles.selected : ''
            }`}
            onClick={() => {
              setGameState(prev => ({
                ...prev,
                activityData: {
                  ...prev.activityData,
                  pitchDiscrimination: {
                    ...prev.activityData.pitchDiscrimination,
                    userAnswer: 'higher'
                  }
                }
              }));
            }}
            disabled={isPlaying}
            aria-label="Tom mais agudo"
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>⬆️</div>
            <div style={{ fontSize: '0.9rem' }}>Mais Agudo</div>
          </button>

          <button
            className={`${styles.answerButton} ${
              activityData.userAnswer === 'same' ? styles.selected : ''
            }`}
            onClick={() => {
              setGameState(prev => ({
                ...prev,
                activityData: {
                  ...prev.activityData,
                  pitchDiscrimination: {
                    ...prev.activityData.pitchDiscrimination,
                    userAnswer: 'same'
                  }
                }
              }));
            }}
            disabled={isPlaying}
            aria-label="Mesma altura"
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>➡️</div>
            <div style={{ fontSize: '0.9rem' }}>Igual</div>
          </button>

          <button
            className={`${styles.answerButton} ${
              activityData.userAnswer === 'lower' ? styles.selected : ''
            }`}
            onClick={() => {
              setGameState(prev => ({
                ...prev,
                activityData: {
                  ...prev.activityData,
                  pitchDiscrimination: {
                    ...prev.activityData.pitchDiscrimination,
                    userAnswer: 'lower'
                  }
                }
              }));
            }}
            disabled={isPlaying}
            aria-label="Tom mais grave"
          >
            <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>⬇️</div>
            <div style={{ fontSize: '0.9rem' }}>Mais Grave</div>
          </button>
        </div>
      </div>
    );
  };

  // 🔮 PREDIÇÃO DE PADRÕES - Layout padrão LetterRecognition
  const renderPatternPrediction = () => {
    const activityData = gameState.activityData?.patternPrediction || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔮 Teste de Raciocínio Lógico Musical</h2>
          <p className={styles.questionSubtitle}>Analise o padrão musical e preveja qual elemento vem a seguir</p>
        </div>

        {/* Área de objetos - padrão LetterRecognition */}
        <div className={styles.objectsDisplay}>
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '2rem'
          }}>
            {/* Título do padrão */}
            <div style={{
              textAlign: 'center',
              padding: '1rem',
              backgroundColor: 'rgba(33, 150, 243, 0.2)',
              borderRadius: '12px',
              border: '2px solid rgba(33, 150, 243, 0.5)'
            }}>
              <div style={{ fontSize: '1.2rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                🧩 Padrão Musical Incompleto
              </div>
              <div style={{ fontSize: '1rem', color: 'rgba(255,255,255,0.8)' }}>
                Tipo: <strong>{activityData.patternType || 'Analise a sequência'}</strong>
              </div>
            </div>

            {/* Sequência do padrão */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              justifyContent: 'center',
              flexWrap: 'wrap',
              padding: '2rem',
              backgroundColor: 'rgba(0,0,0,0.3)',
              borderRadius: '16px',
              border: '2px solid rgba(255,255,255,0.2)'
            }}>
              {(activityData.incompletePattern || []).map((instrumentId, index) => {
                const instrument = instruments.find(inst => inst.id === instrumentId);
                return (
                  <div key={index} style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    padding: '1.5rem',
                    border: '3px solid rgba(76, 175, 80, 0.6)',
                    borderRadius: '16px',
                    backgroundColor: 'rgba(76, 175, 80, 0.2)',
                    minWidth: '100px',
                    transform: 'scale(1)',
                    transition: 'all 0.3s ease'
                  }}>
                    <div style={{
                      fontSize: '1rem',
                      fontWeight: 'bold',
                      backgroundColor: 'rgba(76, 175, 80, 0.8)',
                      color: 'white',
                      borderRadius: '50%',
                      width: '30px',
                      height: '30px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: '0.5rem'
                    }}>
                      {index + 1}
                    </div>
                    <div style={{ fontSize: '3rem', margin: '0.5rem 0' }}>{instrument?.emoji}</div>
                    <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>{instrument?.name}</div>
                  </div>
                );
              })}

              {/* Seta indicativa */}
              <div style={{
                fontSize: '3rem',
                color: 'rgba(255, 193, 7, 0.8)',
                fontWeight: 'bold',
                animation: 'pulse 1.5s infinite'
              }}>
                ➤
              </div>

              {/* Elemento faltante - MUITO VISÍVEL */}
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                padding: '1.5rem',
                border: '4px dashed rgba(255, 193, 7, 0.8)',
                borderRadius: '16px',
                backgroundColor: 'rgba(255, 193, 7, 0.3)',
                minWidth: '100px',
                animation: 'pulse 2s infinite',
                boxShadow: '0 0 20px rgba(255, 193, 7, 0.5)'
              }}>
                <div style={{
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  backgroundColor: 'rgba(255, 193, 7, 0.8)',
                  color: 'white',
                  borderRadius: '50%',
                  width: '30px',
                  height: '30px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: '0.5rem'
                }}>
                  {(activityData.incompletePattern?.length || 0) + 1}
                </div>
                <div style={{ fontSize: '4rem', margin: '0.5rem 0', animation: 'bounce 1s infinite' }}>❓</div>
                <div style={{ fontSize: '1rem', fontWeight: 'bold', color: 'rgba(255, 193, 7, 1)' }}>
                  FALTANTE
                </div>
              </div>
            </div>

            {/* Dica do padrão */}
            <div style={{
              textAlign: 'center',
              padding: '1rem',
              backgroundColor: 'rgba(156, 39, 176, 0.2)',
              borderRadius: '12px',
              border: '2px solid rgba(156, 39, 176, 0.5)',
              maxWidth: '600px'
            }}>
              <div style={{ fontSize: '1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                💡 Dica de Análise
              </div>
              <div style={{ fontSize: '0.9rem', color: 'rgba(255,255,255,0.8)' }}>
                Observe a sequência de instrumentos e identifique o padrão lógico.
                Qual instrumento deveria vir a seguir?
              </div>
            </div>
          </div>
        </div>

        {/* Opções de resposta - padrão LetterRecognition */}
        <div className={styles.answerOptions}>
          <div style={{
            textAlign: 'center',
            marginBottom: '1rem',
            fontSize: '1.2rem',
            fontWeight: 'bold',
            color: 'rgba(255,255,255,0.9)'
          }}>
            🎯 Qual instrumento completa o padrão?
          </div>

          {(activityData.options || []).map((instrumentId) => {
            const instrument = instruments.find(inst => inst.id === instrumentId);
            const isSelected = activityData.userAnswer === instrumentId;
            const isCorrect = instrumentId === activityData.correctAnswer;

            return (
              <button
                key={instrumentId}
                className={`${styles.answerButton} ${isSelected ? styles.selected : ''}`}
                onClick={() => {
                  setGameState(prev => ({
                    ...prev,
                    activityData: {
                      ...prev.activityData,
                      patternPrediction: {
                        ...prev.activityData.patternPrediction,
                        userAnswer: instrumentId
                      }
                    }
                  }));

                  // Feedback imediato
                  if (isCorrect) {
                    speak(`Excelente! ${instrument?.name} completa o padrão corretamente.`);
                  } else {
                    speak(`${instrument?.name} selecionado. Analise se completa o padrão.`);
                  }
                }}
                disabled={isPlaying}
                aria-label={`Escolher ${instrument?.name}`}
                style={{
                  border: isSelected ? '3px solid rgba(255, 193, 7, 0.8)' : '2px solid rgba(255,255,255,0.3)',
                  backgroundColor: isSelected ? 'rgba(255, 193, 7, 0.2)' : 'var(--card-background)',
                  transform: isSelected ? 'scale(1.05)' : 'scale(1)',
                  boxShadow: isSelected ? '0 0 15px rgba(255, 193, 7, 0.5)' : 'none'
                }}
              >
                <div style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>
                  {instrument?.emoji}
                </div>
                <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>
                  {instrument?.name}
                </div>
                {isSelected && (
                  <div style={{
                    fontSize: '0.8rem',
                    color: 'rgba(255, 193, 7, 1)',
                    marginTop: '0.5rem',
                    fontWeight: 'bold'
                  }}>
                    ✓ SELECIONADO
                  </div>
                )}
              </button>
            );
          })}
        </div>

        {/* Botão de verificação */}
        {activityData.userAnswer && (
          <div style={{
            textAlign: 'center',
            marginTop: '1rem'
          }}>
            <button
              className={styles.answerButton}
              onClick={() => {
                const isCorrect = activityData.userAnswer === activityData.correctAnswer;
                const selectedInstrument = instruments.find(inst => inst.id === activityData.userAnswer);

                if (isCorrect) {
                  speak(`Perfeito! ${selectedInstrument?.name} completa o padrão ${activityData.patternType} corretamente!`);
                } else {
                  const correctInstrument = instruments.find(inst => inst.id === activityData.correctAnswer);
                  speak(`Não é bem assim. O padrão ${activityData.patternType} seria completado por ${correctInstrument?.name}.`);
                }
              }}
              disabled={isPlaying}
              style={{
                backgroundColor: 'rgba(33, 150, 243, 0.3)',
                border: '2px solid rgba(33, 150, 243, 0.5)',
                fontSize: '1rem',
                padding: '1rem 2rem'
              }}
            >
              🔍 Verificar Resposta
            </button>
          </div>
        )}
      </div>
    );
  };

  // 🧠 MEMÓRIA MUSICAL - Layout com sequência longa
  const renderMusicalMemory = () => (
    <div className={styles.activityInterface}>
      <div className={styles.instructionPanel}>
        <h3>🧠 Memória Musical</h3>
        <p>Memorize esta sequência longa de instrumentos e reproduza perfeitamente</p>
      </div>

      <div className={styles.memoryInterface}>
        <div className={styles.memoryChallenge}>
          <h4>🎵 Sequência para memorizar ({sequence.length} instrumentos):</h4>
          <div className={styles.memorySequence}>
            {sequence.map((instrumentId, index) => {
              const instrument = instruments.find(inst => inst.id === instrumentId);
              return (
                <div
                  key={index}
                  className={`${styles.memoryItem} ${
                    activeInstrument === instrumentId ? styles.playing : ''
                  } ${
                    playerSequence.length > index ? styles.completed : ''
                  }`}
                >
                  <div className={styles.itemNumber}>{index + 1}</div>
                  <div className={styles.itemIcon}>{instrument?.emoji || instrumentId}</div>
                </div>
              );
            })}
          </div>
        </div>

        <div className={styles.memoryInput}>
          <h4>🎯 Reproduza a sequência:</h4>
          <div className={styles.instrumentsGrid}>
            {instruments.map((instrument) => (
              <button
                key={instrument.id}
                className={`${styles.instrumentButton} ${
                  activeInstrument === instrument.id ? styles.playing : ''
                }`}
                onClick={() => handleInstrumentClick(instrument.id)}
                disabled={isPlaying}
              >
                <span className={styles.instrumentIcon}>{instrument.emoji}</span>
                <span className={styles.instrumentName}>{instrument.name}</span>
              </button>
            ))}
          </div>

          <div className={styles.memoryProgress}>
            <p>Progresso: {playerSequence.length} / {sequence.length}</p>
            <div className={styles.progressBar}>
              <div
                className={styles.progressFill}
                style={{ width: `${(playerSequence.length / sequence.length) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 🎨 EXPRESSÃO CRIATIVA - Layout padrão LetterRecognition
  const renderCreativeExpression = () => {
    const activityData = gameState.activityData?.creativeExpression || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🎨 Teste de Flexibilidade Cognitiva e Criatividade</h2>
          <p className={styles.questionSubtitle}>{activityData.prompt?.description || 'Expresse sua criatividade musical seguindo as diretrizes'}</p>
        </div>

        {/* Área de objetos - padrão LetterRecognition */}
        <div className={styles.objectsDisplay}>
          <div style={{
            textAlign: 'center',
            padding: '2rem'
          }}>
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🎼</div>
            <div style={{ fontSize: '1.2rem', marginBottom: '1rem', color: 'rgba(255,255,255,0.8)' }}>
              {activityData.prompt?.constraint || 'Criação Livre'}
            </div>
            <div style={{ fontSize: '1rem', color: 'rgba(255,255,255,0.6)' }}>
              Tamanho: {activityData.prompt?.minLength || 3}-{activityData.prompt?.maxLength || 8} notas
            </div>
            <div style={{ fontSize: '1rem', color: 'rgba(255,255,255,0.6)' }}>
              Atual: {activityData.userComposition?.length || 0} notas
            </div>
          </div>

          {/* Composição atual */}
          {(activityData.userComposition?.length || 0) > 0 && (
            <div style={{
              display: 'flex',
              gap: '0.5rem',
              justifyContent: 'center',
              flexWrap: 'wrap',
              marginTop: '1rem'
            }}>
              {activityData.userComposition.map((instrumentId, index) => {
                const instrument = instruments.find(inst => inst.id === instrumentId);
                return (
                  <div key={index} style={{
                    padding: '0.5rem',
                    border: '2px solid rgba(76, 175, 80, 0.5)',
                    borderRadius: '8px',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    textAlign: 'center',
                    minWidth: '60px'
                  }}>
                    <div style={{ fontSize: '0.7rem', opacity: 0.8 }}>{index + 1}</div>
                    <div style={{ fontSize: '1.5rem' }}>{instrument?.emoji}</div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Opções de resposta - padrão LetterRecognition */}
        <div className={styles.answerOptions}>
          {instruments.map((instrument) => (
            <button
              key={instrument.id}
              className={`${styles.answerButton} ${
                activeInstrument === instrument.id ? styles.playing : ''
              }`}
              onClick={() => {
                // Adicionar à composição
                const currentComposition = activityData.userComposition || [];
                if (currentComposition.length < (activityData.prompt?.maxLength || 8)) {
                  setActiveInstrument(instrument.id);
                  setTimeout(() => setActiveInstrument(null), 300);

                  setGameState(prev => ({
                    ...prev,
                    activityData: {
                      ...prev.activityData,
                      creativeExpression: {
                        ...prev.activityData.creativeExpression,
                        userComposition: [...currentComposition, instrument.id]
                      }
                    }
                  }));

                  playSoundInstrument(instrument.id);
                }
              }}
              disabled={isPlaying || (activityData.userComposition?.length || 0) >= (activityData.prompt?.maxLength || 8)}
              aria-label={`Adicionar ${instrument.name}`}
            >
              <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                {instrument.emoji}
              </div>
              <div style={{ fontSize: '0.9rem' }}>
                {instrument.name}
              </div>
            </button>
          ))}
        </div>

        {/* Controles adicionais */}
        <div style={{
          display: 'flex',
          gap: '1rem',
          justifyContent: 'center',
          marginTop: '1rem'
        }}>
          <button
            className={styles.answerButton}
            onClick={() => {
              if (activityData.userComposition?.length > 0) {
                playSequenceWithArray(activityData.userComposition);
              }
            }}
            disabled={!activityData.userComposition?.length || isPlaying}
            style={{ fontSize: '0.9rem', padding: '0.5rem 1rem' }}
          >
            ▶️ Tocar
          </button>

          <button
            className={styles.answerButton}
            onClick={() => {
              setGameState(prev => ({
                ...prev,
                activityData: {
                  ...prev.activityData,
                  creativeExpression: {
                    ...prev.activityData.creativeExpression,
                    userComposition: []
                  }
                }
              }));
            }}
            disabled={!activityData.userComposition?.length}
            style={{ fontSize: '0.9rem', padding: '0.5rem 1rem' }}
          >
            🗑️ Limpar
          </button>
        </div>
      </div>
    );
  };

  const handleBackToMenu = useCallback(() => {
    // Gerar relatório final e recomendações
    try {
      const gameEndData = {
        type: 'game_session',
        action: 'session_end',
        sessionDuration: Date.now() - gameStartTime,
        totalScore: score,
        finalLevel: currentLevel,
        finalRound: currentRound,
        totalAttempts: totalAttempts,
        correctAttempts: correctAttempts,
        finalAccuracy: totalAttempts > 0 ? (correctAttempts / totalAttempts) * 100 : 0,
        maxStreak: streak,
        difficulty: difficulty,
        timestamp: Date.now()
      };
      
      MusicalSequenceMetrics.recordAdvancedInteraction(gameEndData);
      
      // Gerar relatório final dos coletores
      const collectorsHub = MusicalSequenceMetrics.getCollectorsHub();
      if (collectorsHub) {
        const finalReport = collectorsHub.generateDetailedReport();
        const recommendations = collectorsHub.generateRecommendations();
        
        console.log('🎵 Relatório Final da Sessão:', finalReport);
        console.log('🎯 Recomendações Geradas:', recommendations);
        
        // Enviar relatório final para o orquestrador
        if (orchestratorReady && sendMetrics) {
          sendMetrics({
            gameType: 'musical_sequence',
            sessionId: `musical_${gameStartTime}`,
            metrics: {
              ...gameEndData,
              finalReport: finalReport,
              recommendations: recommendations
            },
            timestamp: Date.now()
          });
        }
        
        // Finalizar sessão dos coletores
        collectorsHub.endSession();
      }
    } catch (error) {
      console.error('Erro ao finalizar sessão:', error);
    }
    
    setShowStartScreen(true);
    onBack();
  }, [gameStartTime, score, currentLevel, currentRound, totalAttempts, correctAttempts, streak, difficulty, orchestratorReady, sendMetrics, onBack]);

  return (
    <div 
      className={`${styles.musicalSequenceGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      {showStartScreen ? (
        <GameStartScreen
          gameTitle="Sequência Musical"
          gameDescription="Desenvolva sua memória auditiva e percepção musical"
          gameIcon="🎵"
          difficulties={[
            { id: 'easy', name: 'Fácil', description: 'Sequências de 3 instrumentos', icon: '🟢' },
            { id: 'medium', name: 'Médio', description: 'Sequências de 4 instrumentos', icon: '🟡' },
            { id: 'hard', name: 'Avançado', description: 'Sequências de 5 instrumentos', icon: '🔴' }
          ]}
          onStart={startGame}
          onBack={onBack}
        />
      ) : (
        <div className={styles.gameContent}>
          {/* Header do jogo - padrão LetterRecognition */}
          <div className={styles.gameHeader}>
            <h1 className={styles.gameTitle}>
              🎵 Sequência Musical V3
              <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
                {ACTIVITY_TYPES[gameState.currentActivity?.toUpperCase()]?.name || 'Reprodução de Sequência'}
              </div>
            </h1>
            <button
              className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
              onClick={toggleTTS}
              title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
              aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            >
              {ttsActive ? '🔊' : '🔇'}
            </button>
          </div>

          {/* Header com estatísticas - padrão LetterRecognition */}
          <div className={styles.gameStats}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{score}</div>
              <div className={styles.statLabel}>Pontos</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{currentLevel}</div>
              <div className={styles.statLabel}>Nível</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{streak}</div>
              <div className={styles.statLabel}>Sequência</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statValue}>{getAccuracy()}%</div>
              <div className={styles.statLabel}>Precisão</div>
            </div>
          </div>

          {/* Menu de atividades - padrão LetterRecognition */}
          <div className={styles.activityMenu}>
            {Object.values(ACTIVITY_TYPES).map((activity) => (
              <button
                key={activity.id}
                className={`${styles.activityButton} ${
                  gameState.currentActivity === activity.id ? styles.active : ''
                }`}
                onClick={() => changeActivity(activity.id)}
              >
                <span>{activity.icon}</span>
                <span>{activity.name}</span>
              </button>
            ))}
          </div>

          {/* Renderização da atividade atual - EXATO padrão LetterRecognition */}
          {gameState.currentActivity === ACTIVITY_TYPES.SEQUENCE_REPRODUCTION.id && renderSequenceReproduction()}
          {gameState.currentActivity === ACTIVITY_TYPES.RHYTHM_TIMING.id && renderRhythmTiming()}
          {gameState.currentActivity === ACTIVITY_TYPES.PITCH_DISCRIMINATION.id && renderPitchDiscrimination()}
          {gameState.currentActivity === ACTIVITY_TYPES.PATTERN_PREDICTION.id && renderPatternPrediction()}
          {gameState.currentActivity === ACTIVITY_TYPES.CREATIVE_EXPRESSION.id && renderCreativeExpression()}

          {/* Controles do jogo - padrão LetterRecognition */}
          <div className={styles.gameControls}>
            <button className={styles.controlButton} onClick={explainGame}>
              🔊 Explicar
            </button>
            <button className={styles.controlButton} onClick={playSequence}>
              🔄 Repetir Sequência
            </button>
            <button className={styles.controlButton} onClick={handleBackToMenu}>
              ⬅️ Voltar
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default MusicalSequenceGame;
