#!/usr/bin/env node

/**
 * Teste rápido de validação para verificar se as correções funcionaram
 */

import { SystemOrchestrator } from './src/api/services/core/SystemOrchestrator.js';
import { getAnalysisOrchestrator } from './src/api/services/analysis/AnalysisOrchestrator.js';
import { logger } from './src/api/services/core/logging/StructuredLogger.js';

async function quickValidation() {
  console.log('🚀 TESTE RÁPIDO DE VALIDAÇÃO');
  console.log('=' .repeat(50));
  
  try {
    // Inicializar SystemOrchestrator
    const orchestrator = new SystemOrchestrator();
    
    // Inicializar AnalysisOrchestrator
    const analysisOrchestrator = getAnalysisOrchestrator();
    
    // Injetar dependências
    analysisOrchestrator.injectSystemOrchestrator(orchestrator);
    
    // Dados de teste
    const testGameSession = {
      id: 'test_session_001',
      childId: 'test_child_001',
      gameName: 'ColorMatch',
      timestamp: new Date().toISOString(),
      duration: 60000,
      accuracy: 0.8,
      responseTime: 1200,
      interactions: [
        { type: 'click', timestamp: Date.now() - 5000, correct: true },
        { type: 'click', timestamp: Date.now() - 3000, correct: false },
        { type: 'click', timestamp: Date.now() - 1000, correct: true }
      ],
      metrics: {
        accuracy: 0.8,
        responseTime: 1200,
        consistency: 0.7,
        engagement: 0.9
      }
    };
    
    console.log('\n🎮 Testando processamento de métricas...');
    const processResult = await orchestrator.processGameMetrics(testGameSession);
    console.log('✅ Processamento concluído:', processResult ? 'SUCESSO' : 'FALHA');
    
    console.log('\n🧠 Testando análise completa...');
    const analysisResult = await analysisOrchestrator.orchestrateCompleteAnalysis(testGameSession);
    console.log('✅ Análise concluída:', analysisResult ? 'SUCESSO' : 'FALHA');
    console.log('📊 Score geral:', analysisResult.consolidated?.overallScore || 'N/A');
    console.log('🔬 Analisadores usados:', analysisResult.metadata?.analyzersUsed || 0);
    
    console.log('\n🏥 Testando health checks...');
    const healthStatus = await orchestrator.getHealthStatus();
    console.log('✅ Health Status:', healthStatus.overall);
    
    console.log('\n🎊 TESTE CONCLUÍDO COM SUCESSO!');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

quickValidation();
