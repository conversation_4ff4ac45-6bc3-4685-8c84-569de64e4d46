/* MCPIntegration.module.css - Estilos para integração MCP */

.mcpContainer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  color: white;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Header */
.mcpHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.mcpTitle {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.mcpIcon {
  font-size: 24px;
}

.statusBadge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.statusIcon {
  font-size: 12px;
}

.statusText {
  font-size: 12px;
  font-weight: 600;
}

/* Seções */
.configSection,
.capabilitiesSection,
.resultsSection,
.instructionsSection {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sectionTitle {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Formulário de Configuração */
.configForm {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.inputLabel {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
}

.configInput {
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

.configInput:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.configInput::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.checkboxGroup {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.checkbox {
  width: 18px;
  height: 18px;
  accent-color: white;
}

.configActions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.saveButton,
.testButton,
.testMessageButton {
  padding: 10px 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.saveButton:hover:not(:disabled),
.testButton:hover:not(:disabled),
.testMessageButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.saveButton:disabled,
.testButton:disabled,
.testMessageButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Capacidades */
.capabilitiesList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.capabilityItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.capabilityIcon {
  width: 20px;
  height: 20px;
  background: rgba(76, 175, 80, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.capabilityText {
  font-size: 14px;
  line-height: 1.4;
}

/* Resultados */
.successResults,
.errorResults {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.resultItem {
  padding: 8px 0;
  font-size: 14px;
  line-height: 1.4;
}

.errorResults {
  color: #ffcdd2;
}

.testMessageResult {
  margin-top: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.testMessageResult h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.testMessageResult p {
  margin: 0 0 12px 0;
  font-size: 14px;
  line-height: 1.4;
}

.responseMetadata {
  display: flex;
  gap: 16px;
  font-size: 12px;
  opacity: 0.8;
}

/* Instruções */
.instructionsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.instructionStep {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.stepNumber {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
}

.stepContent {
  flex: 1;
}

.stepContent strong {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
}

.stepContent p {
  margin: 0;
  font-size: 13px;
  line-height: 1.4;
  opacity: 0.9;
}

/* Footer */
.statusFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 20px;
}

.integrationInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.integrationIcon {
  font-size: 24px;
}

.integrationInfo strong {
  display: block;
  font-size: 14px;
  margin-bottom: 2px;
}

.integrationInfo p {
  margin: 0;
  font-size: 12px;
  opacity: 0.8;
}

.loadingIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  .mcpContainer {
    padding: 16px;
  }
  
  .mcpHeader {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .configActions {
    flex-direction: column;
  }
  
  .statusFooter {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .responseMetadata {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .mcpTitle {
    font-size: 18px;
  }
  
  .configInput {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .saveButton,
  .testButton,
  .testMessageButton {
    padding: 8px 16px;
    font-size: 13px;
  }
}

/* Estados para acessibilidade */
.configInput:focus,
.saveButton:focus,
.testButton:focus,
.testMessageButton:focus {
  outline: 3px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
}

.checkboxLabel:focus-within {
  outline: 2px solid rgba(255, 255, 255, 0.3);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Animações */
.mcpContainer {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.successResults {
  animation: fadeIn 0.5s ease-out;
}

.testMessageResult {
  animation: slideInDown 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode adicional (o componente já é escuro por padrão) */
@media (prefers-color-scheme: light) {
  .mcpContainer {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #1a1a1a;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .configSection,
  .capabilitiesSection,
  .resultsSection,
  .instructionsSection {
    background: rgba(255, 255, 255, 0.7);
    color: #1a1a1a;
  }
  
  .configInput {
    background: rgba(255, 255, 255, 0.8);
    color: #1a1a1a;
    border-color: rgba(0, 0, 0, 0.2);
  }
  
  .configInput::placeholder {
    color: rgba(0, 0, 0, 0.5);
  }
  
  .saveButton,
  .testButton,
  .testMessageButton {
    background: rgba(102, 126, 234, 0.1);
    color: #1a1a1a;
    border-color: rgba(102, 126, 234, 0.3);
  }
}

/* Environment Variables Info */
.envInfo {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 1px solid #cbd5e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.envStatus {
  margin-bottom: 16px;
}

.envList {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.envItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.envItem.envSet {
  background-color: rgba(72, 187, 120, 0.1);
  border-left: 3px solid #48bb78;
}

.envItem.envNotSet {
  background-color: rgba(245, 101, 101, 0.1);
  border-left: 3px solid #f56565;
}

.envInstructions {
  background-color: rgba(102, 126, 234, 0.05);
  border-left: 4px solid #667eea;
  padding: 12px;
  border-radius: 4px;
}

.envInstructions p {
  margin: 0 0 8px 0;
  font-weight: 600;
  color: #2d3748;
}

.envInstructions ol {
  margin: 0;
  padding-left: 20px;
}

.envInstructions li {
  margin-bottom: 4px;
  font-size: 14px;
  color: #4a5568;
}

.envInstructions code {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.envNote {
  color: #667eea;
  font-style: italic;
  margin-top: 4px;
  display: block;
}
