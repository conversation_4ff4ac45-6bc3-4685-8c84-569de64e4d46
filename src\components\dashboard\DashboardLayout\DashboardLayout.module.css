/* 🎨 LAYOUT UNIFICADO DOS DASHBOARDS PREMIUM */

.dashboardLayout {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Loading State */
.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

/* Error State */
.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.errorIcon {
  font-size: 4rem;
  margin-bottom: 16px;
}

.errorTitle {
  color: #ef4444;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.errorMessage {
  color: #6b7280;
  font-size: 1rem;
  margin: 0 0 24px 0;
  max-width: 400px;
  line-height: 1.5;
}

.retryButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retryButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.retryButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Header */
.dashboardHeader {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.titleSection {
  display: flex;
  align-items: center;
  gap: 16px;
}

.titleIcon {
  font-size: 2.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.titleText {
  flex: 1;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 4px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin: 0;
  font-weight: 400;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #374151;
}

.actionButton:hover:not(:disabled) {
  background: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.refreshButton {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.refreshButton:hover:not(:disabled) {
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.buttonIcon {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.buttonIcon.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.buttonText {
  font-weight: 500;
}

/* Content */
.dashboardContent {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 400px;
}

/* Footer */
.dashboardFooter {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 16px 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.footerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  font-size: 0.85rem;
  color: #6b7280;
}

.footerInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footerIcon {
  font-size: 1rem;
}

.footerText {
  font-weight: 500;
}

.footerStatus {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statusDot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.statusText {
  font-weight: 500;
  color: #10b981;
}

.footerTime {
  font-weight: 400;
  font-style: italic;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardLayout {
    padding: 12px;
  }

  .dashboardHeader {
    padding: 20px;
    margin-bottom: 20px;
  }

  .headerContent {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .titleSection {
    width: 100%;
  }

  .headerActions {
    width: 100%;
    justify-content: flex-end;
  }

  .title {
    font-size: 1.75rem;
  }

  .titleIcon {
    font-size: 2rem;
  }

  .dashboardContent {
    padding: 20px;
    margin-bottom: 20px;
  }

  .footerContent {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .dashboardLayout {
    padding: 8px;
  }

  .dashboardHeader {
    padding: 16px;
  }

  .title {
    font-size: 1.5rem;
  }

  .titleIcon {
    font-size: 1.8rem;
  }

  .dashboardContent {
    padding: 16px;
  }

  .actionButton {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .buttonText {
    display: none; /* Mostrar apenas ícones em telas pequenas */
  }
}
