/**
 * Teste dos redirecionamentos de endpoints
 * Portal Betina V3 - Verificação de correção de 404s
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3000';

// Lista de endpoints que eram 404 e agora devem ser redirecionados
const endpointsToTest = [
  {
    url: '/api/sessions',
    expectRedirect: '/api/metrics/sessions',
    description: 'Redirecionamento de sessões antigas'
  },
  {
    url: '/sessions',
    expectRedirect: '/api/metrics/sessions',
    description: 'Redirecionamento de sessões sem /api'
  },
  {
    url: '/api/games/start',
    expectRedirect: '/api/public/games',
    description: 'Redirecionamento de start game'
  },
  {
    url: '/api/games/padroes_visuais',
    expectRedirect: '/api/public/games',
    description: 'Redirecionamento de jogo específico'
  },
  {
    url: '/api/padroes_visuais',
    expectRedirect: '/api/public/games',
    description: 'Redirecionamento de padrões visuais'
  },
  {
    url: '/metrics',
    expectRedirect: '/api/public/metrics',
    description: 'Redirecionamento de métricas sem /api'
  }
];

// Lista de endpoints que devem funcionar normalmente
const workingEndpoints = [
  {
    url: '/api/public/health',
    description: 'Health check público'
  },
  {
    url: '/api/public/games',
    description: 'Jogos públicos'
  },
  {
    url: '/api/public/metrics',
    description: 'Métricas públicas'
  }
];

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

async function testRedirect(endpoint) {
  try {
    console.log(`${colors.cyan}🔄 Testando: ${endpoint.url}${colors.reset}`);
    
    const response = await fetch(`${API_BASE}${endpoint.url}`, {
      redirect: 'manual' // Para capturar redirecionamentos
    });
    
    if (response.status >= 300 && response.status < 400) {
      const location = response.headers.get('location');
      console.log(`${colors.green}✅ Redirecionamento: ${response.status} → ${location}${colors.reset}`);
      
      if (endpoint.expectRedirect && location && location.includes(endpoint.expectRedirect.replace('/api', ''))) {
        console.log(`${colors.green}   ✓ Redirecionamento correto!${colors.reset}`);
        return true;
      } else {
        console.log(`${colors.yellow}   ⚠️ Redirecionamento inesperado. Esperado: ${endpoint.expectRedirect}${colors.reset}`);
        return false;
      }
    } else if (response.status === 200) {
      const data = await response.json();
      console.log(`${colors.green}✅ 200 OK: ${data.message || 'Resposta válida'}${colors.reset}`);
      
      if (data.redirect_to) {
        console.log(`${colors.blue}   ℹ️ Sugestão de redirecionamento: ${data.redirect_to}${colors.reset}`);
      }
      
      return true;
    } else {
      const data = await response.json();
      console.log(`${colors.red}❌ ${response.status}: ${data.message}${colors.reset}`);
      
      if (data.suggestions && data.suggestions.length > 0) {
        console.log(`${colors.blue}   💡 Sugestões: ${data.suggestions.join(', ')}${colors.reset}`);
      }
      
      return false;
    }
  } catch (error) {
    console.log(`${colors.red}💥 Erro de conexão: ${error.message}${colors.reset}`);
    return false;
  }
}

async function testWorkingEndpoint(endpoint) {
  try {
    console.log(`${colors.cyan}✓ Testando: ${endpoint.url}${colors.reset}`);
    
    const response = await fetch(`${API_BASE}${endpoint.url}`);
    const data = await response.json();
    
    if (response.status === 200) {
      console.log(`${colors.green}✅ 200 OK: ${data.message || 'Funcionando'}${colors.reset}`);
      return true;
    } else {
      console.log(`${colors.red}❌ ${response.status}: ${data.message}${colors.reset}`);
      return false;
    }
  } catch (error) {
    console.log(`${colors.red}💥 Erro: ${error.message}${colors.reset}`);
    return false;
  }
}

async function checkServerHealth() {
  try {
    console.log(`${colors.blue}🏥 Verificando se o servidor está online...${colors.reset}`);
    const response = await fetch(`${API_BASE}/api/public/health`);
    const data = await response.json();
    
    if (response.status === 200 && data.success) {
      console.log(`${colors.green}✅ Servidor online e funcionando${colors.reset}`);
      console.log(`   Versão: ${data.version || 'N/A'}`);
      console.log(`   Ambiente: ${data.environment || 'N/A'}`);
      return true;
    } else {
      console.log(`${colors.red}❌ Servidor com problemas${colors.reset}`);
      return false;
    }
  } catch (error) {
    console.log(`${colors.red}❌ Servidor não está respondendo: ${error.message}${colors.reset}`);
    console.log(`${colors.yellow}💡 Certifique-se de que o servidor está rodando na porta 3000${colors.reset}`);
    return false;
  }
}

async function runTests() {
  console.log(`${colors.magenta}🧪 TESTE DE REDIRECIONAMENTOS - Portal Betina V3${colors.reset}`);
  console.log('='.repeat(60));
  
  // Verificar se servidor está online
  const serverOnline = await checkServerHealth();
  if (!serverOnline) {
    console.log(`${colors.red}❌ Não é possível continuar sem o servidor online${colors.reset}`);
    process.exit(1);
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`${colors.magenta}📊 TESTANDO ENDPOINTS FUNCIONAIS${colors.reset}`);
  console.log('='.repeat(60));
  
  let workingCount = 0;
  for (const endpoint of workingEndpoints) {
    const success = await testWorkingEndpoint(endpoint);
    if (success) workingCount++;
    console.log(); // Linha em branco
  }
  
  console.log('='.repeat(60));
  console.log(`${colors.magenta}🔄 TESTANDO REDIRECIONAMENTOS${colors.reset}`);
  console.log('='.repeat(60));
  
  let redirectCount = 0;
  for (const endpoint of endpointsToTest) {
    const success = await testRedirect(endpoint);
    if (success) redirectCount++;
    console.log(); // Linha em branco
  }
  
  console.log('='.repeat(60));
  console.log(`${colors.magenta}📈 RESUMO DOS RESULTADOS${colors.reset}`);
  console.log('='.repeat(60));
  console.log(`${colors.green}✅ Endpoints funcionais: ${workingCount}/${workingEndpoints.length}${colors.reset}`);
  console.log(`${colors.blue}🔄 Redirecionamentos: ${redirectCount}/${endpointsToTest.length}${colors.reset}`);
  
  const totalSuccess = workingCount + redirectCount;
  const totalTests = workingEndpoints.length + endpointsToTest.length;
  const successRate = Math.round((totalSuccess / totalTests) * 100);
  
  console.log(`${colors.cyan}📊 Taxa de sucesso: ${successRate}%${colors.reset}`);
  
  if (successRate >= 90) {
    console.log(`${colors.green}🎉 Excelente! Sistema funcionando corretamente${colors.reset}`);
  } else if (successRate >= 70) {
    console.log(`${colors.yellow}⚠️ Atenção: Alguns problemas detectados${colors.reset}`);
  } else {
    console.log(`${colors.red}❌ Crítico: Muitos problemas encontrados${colors.reset}`);
  }
  
  console.log('='.repeat(60));
}

// Executar testes
runTests().catch(error => {
  console.error(`${colors.red}❌ Erro fatal durante os testes: ${error.message}${colors.reset}`);
  process.exit(1);
});
