#!/usr/bin/env node

/**
 * 🔧 VALIDADOR DE HOOKS REACT - Portal Betina V3
 * 
 * Script para identificar automaticamente warnings de React Hooks:
 * - Missing dependencies em useEffect
 * - useCallback sem dependencies
 * - Variáveis não estáveis em dependencies
 * - Funções não memoizadas sendo usadas em useEffect
 * 
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

console.log('🔧 VALIDADOR DE HOOKS REACT - Portal Betina V3');
console.log('================================================\n');

// Padrões para detectar problemas
const HOOK_PATTERNS = {
  useEffect: /useEffect\s*\(\s*\(\s*\)\s*=>\s*{[\s\S]*?}\s*,\s*\[(.*?)\]\s*\)/g,
  useCallback: /useCallback\s*\(\s*[\s\S]*?\s*,\s*\[(.*?)\]\s*\)/g,
  useState: /const\s+\[(\w+),\s*set\w+\]\s*=\s*useState/g,
  refs: /const\s+(\w+Ref)\s*=\s*useRef/g,
  functions: /const\s+(\w+)\s*=\s*(?:async\s+)?\([^)]*\)\s*=>/g
};

// Arquivos de jogos para verificar
const GAME_FILES = [
  'src/games/MusicalSequence/MusicalSequenceGame.jsx',
  'src/games/ColorMatch/ColorMatchGame.jsx',
  'src/games/MemoryGame/MemoryGame.jsx',
  'src/games/LetterRecognition/LetterRecognitionGame.jsx',
  'src/games/ContagemNumeros/ContagemNumerosGame.jsx',
  'src/games/PadroesVisuais/PadroesVisuaisGame.jsx',
  'src/games/QuebraCabeca/QuebraCabecaGame.jsx',
  'src/games/ImageAssociation/ImageAssociationGame.jsx',
  'src/games/CreativePainting/CreativePaintingGame.jsx'
];

/**
 * Analisar arquivo em busca de problemas de hooks
 */
function analyzeFile(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  Arquivo não encontrado: ${filePath}`);
    return null;
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  const fileName = path.basename(filePath);
  
  console.log(`🔍 Analisando: ${fileName}`);
  
  const issues = {
    missingDependencies: [],
    unusedDependencies: [],
    unstableReferences: [],
    recommendations: []
  };
  
  // Extrair variáveis do componente
  const stateVars = [...content.matchAll(HOOK_PATTERNS.useState)].map(match => match[1]);
  const refs = [...content.matchAll(HOOK_PATTERNS.refs)].map(match => match[1]);
  const functions = [...content.matchAll(HOOK_PATTERNS.functions)].map(match => match[1]);
  
  // Analisar useEffect
  const useEffects = [...content.matchAll(HOOK_PATTERNS.useEffect)];
  useEffects.forEach((match, index) => {
    const effectBody = match[0];
    const dependencies = match[1] ? match[1].split(',').map(d => d.trim()) : [];
    
    // Procurar variáveis usadas no effect
    const usedVars = [];
    
    // Procurar por state variables
    stateVars.forEach(stateVar => {
      if (effectBody.includes(stateVar) && !dependencies.includes(stateVar)) {
        usedVars.push(stateVar);
      }
    });
    
    // Procurar por funções
    functions.forEach(func => {
      if (effectBody.includes(func) && !dependencies.includes(func)) {
        usedVars.push(func);
      }
    });
    
    if (usedVars.length > 0) {
      issues.missingDependencies.push({
        effect: index + 1,
        missing: usedVars,
        line: content.substring(0, match.index).split('\n').length
      });
    }
  });
  
  // Analisar useCallback
  const useCallbacks = [...content.matchAll(HOOK_PATTERNS.useCallback)];
  useCallbacks.forEach((match, index) => {
    const callbackBody = match[0];
    const dependencies = match[1] ? match[1].split(',').map(d => d.trim()) : [];
    
    if (dependencies.length === 0) {
      // Verificar se deveria ter dependencies
      const usedVars = [];
      
      stateVars.forEach(stateVar => {
        if (callbackBody.includes(stateVar)) {
          usedVars.push(stateVar);
        }
      });
      
      if (usedVars.length > 0) {
        issues.missingDependencies.push({
          callback: index + 1,
          missing: usedVars,
          line: content.substring(0, match.index).split('\n').length
        });
      }
    }
  });
  
  // Gerar recomendações
  if (issues.missingDependencies.length > 0) {
    issues.recommendations.push('Adicionar dependencies faltantes nos useEffect/useCallback');
  }
  
  // Verificar se há funções que deveriam ser useCallback
  functions.forEach(func => {
    if (content.includes(`useEffect`) && content.includes(func)) {
      const isCallback = content.includes(`${func} = useCallback`);
      if (!isCallback) {
        issues.recommendations.push(`Considerar tornar '${func}' um useCallback para estabilizar referência`);
      }
    }
  });
  
  return {
    file: fileName,
    issues,
    hasIssues: issues.missingDependencies.length > 0 || issues.recommendations.length > 0
  };
}

/**
 * Executar análise
 */
function runAnalysis() {
  const results = [];
  let totalIssues = 0;
  
  GAME_FILES.forEach(file => {
    const result = analyzeFile(file);
    if (result) {
      results.push(result);
      if (result.hasIssues) {
        totalIssues++;
      }
    }
  });
  
  console.log('\n📊 RESULTADOS DA ANÁLISE:');
  console.log('=========================\n');
  
  results.forEach(result => {
    if (result.hasIssues) {
      console.log(`❌ ${result.file}:`);
      
      if (result.issues.missingDependencies.length > 0) {
        console.log('   📝 Dependencies faltantes:');
        result.issues.missingDependencies.forEach(issue => {
          if (issue.effect) {
            console.log(`      useEffect #${issue.effect} (linha ~${issue.line}): ${issue.missing.join(', ')}`);
          }
          if (issue.callback) {
            console.log(`      useCallback #${issue.callback} (linha ~${issue.line}): ${issue.missing.join(', ')}`);
          }
        });
      }
      
      if (result.issues.recommendations.length > 0) {
        console.log('   💡 Recomendações:');
        result.issues.recommendations.forEach(rec => {
          console.log(`      - ${rec}`);
        });
      }
      console.log();
    } else {
      console.log(`✅ ${result.file}: Sem problemas detectados`);
    }
  });
  
  console.log(`\n🎯 RESUMO: ${totalIssues}/${results.length} arquivos com problemas de hooks`);
  
  if (totalIssues > 0) {
    console.log('\n🔧 PRÓXIMOS PASSOS:');
    console.log('1. Corrigir dependencies faltantes nos useEffect');
    console.log('2. Tornar funções useCallback quando usadas em useEffect');
    console.log('3. Verificar warnings no console do navegador');
    console.log('4. Executar novamente este script após correções');
  } else {
    console.log('\n🎉 Todos os arquivos estão sem problemas de hooks detectados!');
  }
}

// Executar análise
runAnalysis();
