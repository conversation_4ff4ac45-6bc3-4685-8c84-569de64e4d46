/**
 * Teste focado nos componentes com status WARNING
 */

import { SystemOrchestrator } from './src/api/services/core/SystemOrchestrator.js';
import { IntelligentCache } from './src/api/services/core/cache/IntelligentCache.js';
import { StructuredLogger } from './src/api/services/core/logging/StructuredLogger.js';

const logger = new StructuredLogger();

async function testWarningComponents() {
  console.log('\n🔍 TESTE DE COMPONENTES EM WARNING');
  console.log('=====================================\n');
  
  try {
    // 1. Testar IntelligentCache isoladamente
    console.log('1️⃣ Teste do IntelligentCache...');
    const cache = new IntelligentCache({ maxSize: 100, ttl: 60000 });
    
    // Adicionar alguns dados para simular uso
    for (let i = 0; i < 20; i++) {
      cache.set(`test_key_${i}`, { data: `value_${i}`, timestamp: Date.now() });
    }
    
    // Simular alguns hits e misses
    for (let i = 0; i < 10; i++) {
      cache.get(`test_key_${i}`); // hits
    }
    for (let i = 30; i < 40; i++) {
      cache.get(`test_key_${i}`); // misses
    }
    
    const cacheMetrics = cache.getMetrics();
    const cacheHealth = cache.getHealthStatus();
    
    console.log('📊 Cache Metrics:', JSON.stringify(cacheMetrics, null, 2));
    console.log('🏥 Cache Health:', JSON.stringify(cacheHealth, null, 2));
    
    // 2. Testar SystemOrchestrator isoladamente
    console.log('\n2️⃣ Teste do SystemOrchestrator...');
    const orchestrator = SystemOrchestrator.getInstance();
    
    // Aguardar inicialização
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const orchestratorHealth = orchestrator.getHealthStatus();
    const orchestratorMetrics = orchestrator.getMetrics();
    
    console.log('📊 Orchestrator Health:', JSON.stringify(orchestratorHealth, null, 2));
    console.log('📊 Orchestrator Metrics:', JSON.stringify(orchestratorMetrics, null, 2));
    
    // 3. Verificar estado interno dos componentes
    console.log('\n3️⃣ Estado interno dos componentes...');
    
    // Estado do cache
    console.log('🔍 Cache State:');
    console.log(`- Tamanho: ${cacheMetrics.size}`);
    console.log(`- Hit Rate: ${cacheMetrics.hitRate}`);
    console.log(`- Utilização: ${cacheMetrics.utilizationRate}`);
    console.log(`- Evictions: ${cacheMetrics.evictions}`);
    
    // Estado do orchestrator
    console.log('\n🔍 Orchestrator State:');
    console.log(`- Status: ${orchestratorHealth.status}`);
    console.log(`- Uptime: ${orchestratorHealth.uptime}ms`);
    console.log(`- Memory Usage: ${orchestratorHealth.memoryUsage}%`);
    console.log(`- Active Sessions: ${orchestratorHealth.activeSessions}`);
    console.log(`- Total Sessions: ${orchestratorHealth.totalSessions}`);
    
    // 4. Simular atividade no sistema
    console.log('\n4️⃣ Simulando atividade no sistema...');
    
    const testGameData = {
      userId: 'test-user',
      sessionId: 'test-session',
      gameType: 'ColorMatch',
      score: 85,
      accuracy: 90,
      duration: 30000,
      timestamp: Date.now(),
      childId: 'test-child'
    };
    
    try {
      await orchestrator.processGameMetrics(testGameData);
      console.log('✅ Processamento de métricas bem-sucedido');
    } catch (error) {
      console.log('❌ Erro no processamento:', error.message);
    }
    
    // 5. Verificar health novamente após atividade
    console.log('\n5️⃣ Health check após atividade...');
    
    const newCacheHealth = cache.getHealthStatus();
    const newOrchestratorHealth = orchestrator.getHealthStatus();
    
    console.log('🔄 Novo Cache Health:', JSON.stringify(newCacheHealth, null, 2));
    console.log('🔄 Novo Orchestrator Health:', JSON.stringify(newOrchestratorHealth, null, 2));
    
    // 6. Análise de recomendações
    console.log('\n6️⃣ Análise de problemas...');
    
    if (newCacheHealth.status !== 'healthy') {
      console.log('⚠️  Cache Issues:', newCacheHealth.issues);
      console.log('💡 Cache Recommendations:', newCacheHealth.recommendations);
    }
    
    if (newOrchestratorHealth.status !== 'healthy') {
      console.log('⚠️  Orchestrator Status:', newOrchestratorHealth.status);
      console.log('🔍 Components Status:', newOrchestratorHealth.componentsStatus);
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
    console.error('Stack:', error.stack);
  }
}

// Executar teste
testWarningComponents().then(() => {
  console.log('\n✅ Teste finalizado');
  process.exit(0);
}).catch(error => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
});
