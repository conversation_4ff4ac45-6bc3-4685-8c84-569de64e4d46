/**
 * @file PatternConstructionCollector.js
 * @description Coletor especializado para análise de construção de padrões visuais
 * @version 3.0.0
 */

export class PatternConstructionCollector {
  constructor() {
    this.name = 'PatternConstructionCollector';
    this.version = '3.0.0';
    this.description = 'Analisa habilidades de criação e construção de padrões visuais';
  }

  async collect(gameState) {
    try {
      const targetPattern = gameState.targetPattern || [];
      const constructedPattern = gameState.constructedPattern || [];
      const constructionSteps = gameState.constructionSteps || [];

      return {
        // Métricas de construção
        constructionAccuracy: this.calculateConstructionAccuracy(targetPattern, constructedPattern),
        spatialOrganizationScore: this.assessSpatialOrganization(constructedPattern),
        constructionEfficiency: this.calculateConstructionEfficiency(constructionSteps, targetPattern.length),
        planningQuality: this.assessPlanningQuality(constructionSteps),
        
        // Análise do processo
        constructionStrategy: this.analyzeConstructionStrategy(constructionSteps),
        stepSequenceOptimality: this.assessStepOptimality(constructionSteps, targetPattern),
        errorCorrectionAbility: this.evaluateErrorCorrection(constructionSteps),
        
        // Habilidades visuoespaciais
        spatialVisualizationIndex: this.assessSpatialVisualization(constructedPattern, targetPattern),
        constructiveApraxiaIndicators: this.evaluateConstructiveApraxia(constructionSteps),
        visualMotorIntegration: this.assessVisualMotorIntegration(constructionSteps),
        
        // Funções executivas
        executivePlanningScore: this.assessExecutivePlanning(constructionSteps),
        workingMemoryLoad: this.evaluateWorkingMemoryLoad(targetPattern, constructionSteps),
        inhibitoryControlIndex: this.assessInhibitoryControl(constructionSteps),
        
        timestamp: Date.now(),
        sessionId: gameState.sessionId || 'unknown'
      };
    } catch (error) {
      console.error('Erro no PatternConstructionCollector:', error);
      return null;
    }
  }

  calculateConstructionAccuracy(target, constructed) {
    if (!target.length || !constructed.length) return 0;
    
    let correctPlacements = 0;
    const maxLength = Math.max(target.length, constructed.length);
    
    // Verifica correspondência posicional exata
    for (let i = 0; i < Math.min(target.length, constructed.length); i++) {
      if (this.elementsMatch(target[i], constructed[i])) {
        correctPlacements++;
      }
    }
    
    // Penaliza elementos extras ou faltantes
    const lengthPenalty = Math.abs(target.length - constructed.length);
    const accuracyScore = (correctPlacements / target.length) * 100;
    const lengthPenaltyScore = (lengthPenalty / target.length) * 20;
    
    return Math.max(0, accuracyScore - lengthPenaltyScore);
  }

  elementsMatch(element1, element2) {
    if (!element1 || !element2) return false;
    return element1.shape === element2.shape && 
           element1.color === element2.color &&
           element1.size === element2.size &&
           element1.position?.x === element2.position?.x &&
           element1.position?.y === element2.position?.y;
  }

  assessSpatialOrganization(constructedPattern) {
    if (constructedPattern.length < 2) return 50;
    
    // Analisa organização espacial do padrão construído
    const spatialMetrics = {
      alignment: this.checkAlignment(constructedPattern),
      symmetry: this.checkSymmetry(constructedPattern),
      distribution: this.checkDistribution(constructedPattern),
      coherence: this.checkSpatialCoherence(constructedPattern)
    };
    
    return (
      spatialMetrics.alignment * 0.3 +
      spatialMetrics.symmetry * 0.2 +
      spatialMetrics.distribution * 0.3 +
      spatialMetrics.coherence * 0.2
    );
  }

  checkAlignment(pattern) {
    // Verifica alinhamento dos elementos
    const positions = pattern.map(element => element.position).filter(pos => pos);
    if (positions.length < 2) return 50;
    
    // Verifica alinhamento horizontal
    const yValues = positions.map(pos => pos.y);
    const horizontalAlignment = this.calculateAlignment(yValues);
    
    // Verifica alinhamento vertical
    const xValues = positions.map(pos => pos.x);
    const verticalAlignment = this.calculateAlignment(xValues);
    
    return Math.max(horizontalAlignment, verticalAlignment);
  }

  calculateAlignment(values) {
    const uniqueValues = [...new Set(values)];
    const alignmentRatio = uniqueValues.length / values.length;
    
    // Menor ratio = melhor alinhamento
    return Math.max(0, 100 - (alignmentRatio * 100));
  }

  checkSymmetry(pattern) {
    // Verifica simetria no padrão construído
    const positions = pattern.map(element => element.position).filter(pos => pos);
    if (positions.length < 4) return 50;
    
    // Calcula centro de massa
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    
    // Verifica simetria horizontal e vertical
    const horizontalSymmetry = this.checkSymmetryAxis(pattern, centerX, 'vertical');
    const verticalSymmetry = this.checkSymmetryAxis(pattern, centerY, 'horizontal');
    
    return Math.max(horizontalSymmetry, verticalSymmetry);
  }

  checkSymmetryAxis(pattern, center, axis) {
    let symmetricPairs = 0;
    let totalElements = pattern.length;
    
    for (let element of pattern) {
      if (!element.position) continue;
      
      const mirrored = this.getMirroredPosition(element.position, center, axis);
      const hasSymmetricPair = pattern.some(other => 
        other.position && 
        Math.abs(other.position.x - mirrored.x) < 10 &&
        Math.abs(other.position.y - mirrored.y) < 10 &&
        this.elementsVisuallyMatch(element, other)
      );
      
      if (hasSymmetricPair) symmetricPairs++;
    }
    
    return (symmetricPairs / totalElements) * 100;
  }

  getMirroredPosition(position, center, axis) {
    if (axis === 'vertical') {
      return { x: 2 * center - position.x, y: position.y };
    } else {
      return { x: position.x, y: 2 * center - position.y };
    }
  }

  elementsVisuallyMatch(element1, element2) {
    return element1.shape === element2.shape && 
           element1.color === element2.color &&
           element1.size === element2.size;
  }

  checkDistribution(pattern) {
    // Verifica distribuição espacial dos elementos
    const positions = pattern.map(element => element.position).filter(pos => pos);
    if (positions.length < 3) return 50;
    
    // Calcula distâncias entre elementos adjacentes
    const distances = [];
    for (let i = 0; i < positions.length - 1; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) +
          Math.pow(positions[j].y - positions[i].y, 2)
        );
        distances.push(distance);
      }
    }
    
    // Verifica uniformidade da distribuição
    const meanDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;
    const variance = distances.reduce((sum, d) => sum + Math.pow(d - meanDistance, 2), 0) / distances.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Menor desvio padrão = melhor distribuição
    const uniformityScore = Math.max(0, 100 - (standardDeviation / meanDistance) * 100);
    return uniformityScore;
  }

  checkSpatialCoherence(pattern) {
    // Verifica coerência espacial geral
    const positions = pattern.map(element => element.position).filter(pos => pos);
    if (positions.length < 2) return 50;
    
    // Verifica se elementos estão dentro de uma área razoável
    const bounds = this.calculateBounds(positions);
    const area = (bounds.maxX - bounds.minX) * (bounds.maxY - bounds.minY);
    const density = positions.length / Math.max(area, 1);
    
    // Densidade ideal entre 0.001 e 0.01
    const idealDensity = 0.005;
    const densityScore = Math.max(0, 100 - Math.abs(density - idealDensity) * 10000);
    
    return densityScore;
  }

  calculateBounds(positions) {
    return {
      minX: Math.min(...positions.map(pos => pos.x)),
      maxX: Math.max(...positions.map(pos => pos.x)),
      minY: Math.min(...positions.map(pos => pos.y)),
      maxY: Math.max(...positions.map(pos => pos.y))
    };
  }

  calculateConstructionEfficiency(steps, targetLength) {
    if (!steps.length || !targetLength) return 0;
    
    const totalTime = steps[steps.length - 1]?.timestamp - steps[0]?.timestamp || 0;
    const stepsCount = steps.length;
    
    // Eficiência baseada em tempo e número de passos
    const timeEfficiency = Math.max(0, 100 - (totalTime / (targetLength * 3000)) * 100);
    const stepEfficiency = Math.max(0, 100 - ((stepsCount - targetLength) / targetLength) * 50);
    
    return (timeEfficiency + stepEfficiency) / 2;
  }

  assessPlanningQuality(steps) {
    if (steps.length < 2) return 50;
    
    // Analisa qualidade do planejamento baseado na sequência de ações
    const planningMetrics = {
      sequentialLogic: this.assessSequentialLogic(steps),
      backtracking: this.assessBacktracking(steps),
      hesitation: this.assessHesitation(steps),
      momentum: this.assessMomentum(steps)
    };
    
    return (
      planningMetrics.sequentialLogic * 0.4 +
      planningMetrics.backtracking * 0.2 +
      planningMetrics.hesitation * 0.2 +
      planningMetrics.momentum * 0.2
    );
  }

  assessSequentialLogic(steps) {
    // Avalia se os passos seguem uma lógica sequencial
    let logicalSequences = 0;
    
    for (let i = 1; i < steps.length; i++) {
      const currentStep = steps[i];
      const previousStep = steps[i - 1];
      
      if (this.isLogicalSequence(previousStep, currentStep)) {
        logicalSequences++;
      }
    }
    
    return (logicalSequences / (steps.length - 1)) * 100;
  }

  isLogicalSequence(step1, step2) {
    // Define lógica sequencial simples
    if (!step1.position || !step2.position) return false;
    
    // Verifica se os passos são espacialmente próximos (construção organizada)
    const distance = Math.sqrt(
      Math.pow(step2.position.x - step1.position.x, 2) +
      Math.pow(step2.position.y - step1.position.y, 2)
    );
    
    return distance < 100; // Distância razoável para sequência lógica
  }

  assessBacktracking(steps) {
    // Penaliza backtracking excessivo (desfazer ações)
    const undoActions = steps.filter(step => step.action === 'undo' || step.action === 'remove').length;
    const backtrackingRatio = undoActions / steps.length;
    
    return Math.max(0, 100 - (backtrackingRatio * 200));
  }

  assessHesitation(steps) {
    // Avalia hesitação baseada em pausas entre ações
    if (steps.length < 2) return 100;
    
    const timeDifferences = [];
    for (let i = 1; i < steps.length; i++) {
      const timeDiff = steps[i].timestamp - steps[i - 1].timestamp;
      timeDifferences.push(timeDiff);
    }
    
    const meanTime = timeDifferences.reduce((sum, time) => sum + time, 0) / timeDifferences.length;
    const longPauses = timeDifferences.filter(time => time > meanTime * 3).length;
    const hesitationRatio = longPauses / timeDifferences.length;
    
    return Math.max(0, 100 - (hesitationRatio * 150));
  }

  assessMomentum(steps) {
    // Avalia manutenção de momentum durante construção
    if (steps.length < 3) return 100;
    
    const timeDifferences = [];
    for (let i = 1; i < steps.length; i++) {
      const timeDiff = steps[i].timestamp - steps[i - 1].timestamp;
      timeDifferences.push(timeDiff);
    }
    
    // Verifica consistência do ritmo
    const meanTime = timeDifferences.reduce((sum, time) => sum + time, 0) / timeDifferences.length;
    const variance = timeDifferences.reduce((sum, time) => sum + Math.pow(time - meanTime, 2), 0) / timeDifferences.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Menor desvio = melhor momentum
    const momentumScore = Math.max(0, 100 - (standardDeviation / meanTime) * 100);
    return momentumScore;
  }

  analyzeConstructionStrategy(steps) {
    if (!steps.length) return 'no_strategy';
    
    const constructionPattern = this.identifyConstructionPattern(steps);
    
    if (constructionPattern.linear > 0.8) return 'linear';
    if (constructionPattern.centerOut > 0.8) return 'center_out';
    if (constructionPattern.outsideIn > 0.8) return 'outside_in';
    if (constructionPattern.clustered > 0.8) return 'clustered';
    
    return 'mixed';
  }

  identifyConstructionPattern(steps) {
    const positions = steps.map(step => step.position).filter(pos => pos);
    if (positions.length < 3) return { linear: 0, centerOut: 0, outsideIn: 0, clustered: 0 };
    
    return {
      linear: this.calculateLinearPattern(positions),
      centerOut: this.calculateCenterOutPattern(positions),
      outsideIn: this.calculateOutsideInPattern(positions),
      clustered: this.calculateClusteredPattern(positions)
    };
  }

  calculateLinearPattern(positions) {
    // Verifica se construção seguiu padrão linear
    let linearConnections = 0;
    
    for (let i = 1; i < positions.length; i++) {
      const distance = Math.sqrt(
        Math.pow(positions[i].x - positions[i - 1].x, 2) +
        Math.pow(positions[i].y - positions[i - 1].y, 2)
      );
      
      if (distance < 150) { // Distância considerada "próxima"
        linearConnections++;
      }
    }
    
    return linearConnections / (positions.length - 1);
  }

  calculateCenterOutPattern(positions) {
    // Verifica se construção partiu do centro para fora
    if (positions.length < 3) return 0;
    
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    
    const distances = positions.map(pos => Math.sqrt(
      Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
    ));
    
    // Verifica se distâncias aumentam com o tempo
    let increasingDistances = 0;
    for (let i = 1; i < distances.length; i++) {
      if (distances[i] >= distances[i - 1] * 0.8) {
        increasingDistances++;
      }
    }
    
    return increasingDistances / (distances.length - 1);
  }

  calculateOutsideInPattern(positions) {
    // Verifica se construção partiu de fora para dentro
    if (positions.length < 3) return 0;
    
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    
    const distances = positions.map(pos => Math.sqrt(
      Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
    ));
    
    // Verifica se distâncias diminuem com o tempo
    let decreasingDistances = 0;
    for (let i = 1; i < distances.length; i++) {
      if (distances[i] <= distances[i - 1] * 1.2) {
        decreasingDistances++;
      }
    }
    
    return decreasingDistances / (distances.length - 1);
  }

  calculateClusteredPattern(positions) {
    // Verifica se construção seguiu padrão de clusters
    const clusters = this.identifyClusters(positions);
    const clusterSwitches = this.countClusterSwitches(positions, clusters);
    
    // Mais switches entre clusters = mais clustered
    return Math.min(1, clusterSwitches / (positions.length * 0.3));
  }

  identifyClusters(positions) {
    // Algoritmo simples de clustering por proximidade
    const clusters = [];
    const visited = new Set();
    
    for (let i = 0; i < positions.length; i++) {
      if (visited.has(i)) continue;
      
      const cluster = [i];
      visited.add(i);
      
      for (let j = i + 1; j < positions.length; j++) {
        if (visited.has(j)) continue;
        
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) +
          Math.pow(positions[j].y - positions[i].y, 2)
        );
        
        if (distance < 100) {
          cluster.push(j);
          visited.add(j);
        }
      }
      
      clusters.push(cluster);
    }
    
    return clusters;
  }

  countClusterSwitches(positions, clusters) {
    // Conta quantas vezes houve mudança de cluster na sequência
    let switches = 0;
    let currentCluster = -1;
    
    for (let i = 0; i < positions.length; i++) {
      const positionCluster = this.findPositionCluster(i, clusters);
      
      if (positionCluster !== currentCluster) {
        switches++;
        currentCluster = positionCluster;
      }
    }
    
    return switches;
  }

  findPositionCluster(positionIndex, clusters) {
    for (let i = 0; i < clusters.length; i++) {
      if (clusters[i].includes(positionIndex)) {
        return i;
      }
    }
    return -1;
  }

  assessStepOptimality(steps, targetPattern) {
    // Avalia se a sequência de passos foi ótima
    const necessarySteps = targetPattern.length;
    const actualSteps = steps.filter(step => step.action === 'place' || step.action === 'add').length;
    
    const stepEfficiency = necessarySteps / Math.max(actualSteps, 1);
    return Math.min(100, stepEfficiency * 100);
  }

  evaluateErrorCorrection(steps) {
    // Avalia capacidade de correção de erros
    const errorActions = steps.filter(step => 
      step.action === 'undo' || 
      step.action === 'remove' || 
      step.action === 'correct'
    );
    
    const successfulCorrections = errorActions.filter(action => 
      this.wasSuccessfulCorrection(action, steps)
    ).length;
    
    if (errorActions.length === 0) return 100; // Sem erros
    
    return (successfulCorrections / errorActions.length) * 100;
  }

  wasSuccessfulCorrection(errorAction, allSteps) {
    // Determina se uma correção foi bem-sucedida
    const errorIndex = allSteps.indexOf(errorAction);
    const subsequentSteps = allSteps.slice(errorIndex + 1, errorIndex + 3);
    
    // Verifica se houve ação construtiva após a correção
    return subsequentSteps.some(step => 
      step.action === 'place' || step.action === 'add'
    );
  }

  assessSpatialVisualization(constructed, target) {
    // Avalia habilidades de visualização espacial
    if (!target.length || !constructed.length) return 50;
    
    const spatialMatchScore = this.calculateSpatialMatch(constructed, target);
    const orientationScore = this.assessOrientation(constructed, target);
    const proportionScore = this.assessProportions(constructed, target);
    
    return (spatialMatchScore + orientationScore + proportionScore) / 3;
  }

  calculateSpatialMatch(constructed, target) {
    // Calcula correspondência espacial global
    let matches = 0;
    
    for (let targetElement of target) {
      const bestMatch = this.findBestSpatialMatch(targetElement, constructed);
      if (bestMatch.score > 0.8) {
        matches++;
      }
    }
    
    return (matches / target.length) * 100;
  }

  findBestSpatialMatch(targetElement, constructedElements) {
    let bestScore = 0;
    let bestElement = null;
    
    for (let constructedElement of constructedElements) {
      const score = this.calculateElementSimilarity(targetElement, constructedElement);
      if (score > bestScore) {
        bestScore = score;
        bestElement = constructedElement;
      }
    }
    
    return { score: bestScore, element: bestElement };
  }

  calculateElementSimilarity(element1, element2) {
    let similarity = 0;
    
    // Similaridade visual
    if (element1.shape === element2.shape) similarity += 0.4;
    if (element1.color === element2.color) similarity += 0.3;
    if (element1.size === element2.size) similarity += 0.3;
    
    return similarity;
  }

  assessOrientation(constructed, target) {
    // Avalia manutenção de orientação espacial
    const targetBounds = this.calculateBounds(target.map(e => e.position).filter(p => p));
    const constructedBounds = this.calculateBounds(constructed.map(e => e.position).filter(p => p));
    
    if (!targetBounds || !constructedBounds) return 50;
    
    const targetAspectRatio = (targetBounds.maxX - targetBounds.minX) / (targetBounds.maxY - targetBounds.minY);
    const constructedAspectRatio = (constructedBounds.maxX - constructedBounds.minX) / (constructedBounds.maxY - constructedBounds.minY);
    
    const aspectRatioSimilarity = 1 - Math.abs(targetAspectRatio - constructedAspectRatio) / Math.max(targetAspectRatio, constructedAspectRatio);
    
    return aspectRatioSimilarity * 100;
  }

  assessProportions(constructed, target) {
    // Avalia manutenção de proporções
    const targetSizes = target.map(e => e.size).filter(s => s);
    const constructedSizes = constructed.map(e => e.size).filter(s => s);
    
    if (!targetSizes.length || !constructedSizes.length) return 50;
    
    const targetSizeDistribution = this.calculateSizeDistribution(targetSizes);
    const constructedSizeDistribution = this.calculateSizeDistribution(constructedSizes);
    
    return this.compareSizeDistributions(targetSizeDistribution, constructedSizeDistribution);
  }

  calculateSizeDistribution(sizes) {
    const distribution = {};
    sizes.forEach(size => {
      distribution[size] = (distribution[size] || 0) + 1;
    });
    
    // Normaliza para percentuais
    const total = sizes.length;
    Object.keys(distribution).forEach(size => {
      distribution[size] = distribution[size] / total;
    });
    
    return distribution;
  }

  compareSizeDistributions(dist1, dist2) {
    const allSizes = new Set([...Object.keys(dist1), ...Object.keys(dist2)]);
    let similarity = 0;
    
    for (let size of allSizes) {
      const freq1 = dist1[size] || 0;
      const freq2 = dist2[size] || 0;
      similarity += 1 - Math.abs(freq1 - freq2);
    }
    
    return (similarity / allSizes.size) * 100;
  }

  evaluateConstructiveApraxia(steps) {
    // Avalia indicadores de apraxia construtiva
    const apraxiaIndicators = {
      fragmentation: this.assessFragmentation(steps),
      perseveration: this.assessPerseveration(steps),
      spatialDisorganization: this.assessSpatialDisorganization(steps),
      closingDifficulty: this.assessClosingDifficulty(steps)
    };
    
    // Menor score = mais indicadores de apraxia
    const totalIndicators = Object.values(apraxiaIndicators).reduce((sum, val) => sum + val, 0);
    return Math.max(0, 100 - totalIndicators);
  }

  assessFragmentation(steps) {
    // Verifica fragmentação na construção
    if (steps.length < 5) return 0;
    
    const positionJumps = [];
    for (let i = 1; i < steps.length; i++) {
      if (steps[i].position && steps[i - 1].position) {
        const distance = Math.sqrt(
          Math.pow(steps[i].position.x - steps[i - 1].position.x, 2) +
          Math.pow(steps[i].position.y - steps[i - 1].position.y, 2)
        );
        positionJumps.push(distance);
      }
    }
    
    const meanJump = positionJumps.reduce((sum, jump) => sum + jump, 0) / positionJumps.length;
    const largeJumps = positionJumps.filter(jump => jump > meanJump * 2).length;
    
    return (largeJumps / positionJumps.length) * 25;
  }

  assessPerseveration(steps) {
    // Verifica perseveração (repetição excessiva)
    const actionCounts = {};
    steps.forEach(step => {
      const key = `${step.action}_${step.position?.x}_${step.position?.y}`;
      actionCounts[key] = (actionCounts[key] || 0) + 1;
    });
    
    const repetitions = Object.values(actionCounts).filter(count => count > 2).length;
    return (repetitions / Object.keys(actionCounts).length) * 25;
  }

  assessSpatialDisorganization(steps) {
    // Verifica desorganização espacial
    const positions = steps.map(step => step.position).filter(pos => pos);
    if (positions.length < 3) return 0;
    
    const organizationScore = this.checkSpatialCoherence(steps.map(step => ({ position: step.position })));
    return Math.max(0, (100 - organizationScore) / 4);
  }

  assessClosingDifficulty(steps) {
    // Verifica dificuldade em finalizar construção
    const lastQuarterSteps = steps.slice(-Math.ceil(steps.length / 4));
    const undoActionsInEnd = lastQuarterSteps.filter(step => 
      step.action === 'undo' || step.action === 'remove'
    ).length;
    
    return (undoActionsInEnd / lastQuarterSteps.length) * 25;
  }

  assessVisualMotorIntegration(steps) {
    // Avalia integração visuo-motora
    if (steps.length < 2) return 50;
    
    const motorMetrics = {
      precision: this.assessMotorPrecision(steps),
      consistency: this.assessMotorConsistency(steps),
      coordination: this.assessCoordination(steps)
    };
    
    return (motorMetrics.precision + motorMetrics.consistency + motorMetrics.coordination) / 3;
  }

  assessMotorPrecision(steps) {
    // Avalia precisão motora baseada na variabilidade de posicionamento
    const placements = steps.filter(step => step.action === 'place' && step.position);
    if (placements.length < 2) return 100;
    
    const positionVariations = [];
    for (let i = 1; i < placements.length; i++) {
      const expectedDistance = 50; // Distância esperada entre elementos
      const actualDistance = Math.sqrt(
        Math.pow(placements[i].position.x - placements[i - 1].position.x, 2) +
        Math.pow(placements[i].position.y - placements[i - 1].position.y, 2)
      );
      
      positionVariations.push(Math.abs(actualDistance - expectedDistance));
    }
    
    const meanVariation = positionVariations.reduce((sum, var_) => sum + var_, 0) / positionVariations.length;
    return Math.max(0, 100 - meanVariation);
  }

  assessMotorConsistency(steps) {
    // Avalia consistência motora
    const timeBetweenActions = [];
    for (let i = 1; i < steps.length; i++) {
      timeBetweenActions.push(steps[i].timestamp - steps[i - 1].timestamp);
    }
    
    if (timeBetweenActions.length < 2) return 100;
    
    const meanTime = timeBetweenActions.reduce((sum, time) => sum + time, 0) / timeBetweenActions.length;
    const variance = timeBetweenActions.reduce((sum, time) => sum + Math.pow(time - meanTime, 2), 0) / timeBetweenActions.length;
    const standardDeviation = Math.sqrt(variance);
    
    const consistencyScore = Math.max(0, 100 - (standardDeviation / meanTime) * 100);
    return consistencyScore;
  }

  assessCoordination(steps) {
    // Avalia coordenação baseada na fluidez de movimento
    const positions = steps.map(step => step.position).filter(pos => pos);
    if (positions.length < 3) return 100;
    
    let smoothness = 0;
    for (let i = 2; i < positions.length; i++) {
      const vector1 = {
        x: positions[i - 1].x - positions[i - 2].x,
        y: positions[i - 1].y - positions[i - 2].y
      };
      const vector2 = {
        x: positions[i].x - positions[i - 1].x,
        y: positions[i].y - positions[i - 1].y
      };
      
      const angle = this.calculateAngleBetweenVectors(vector1, vector2);
      smoothness += Math.max(0, 180 - Math.abs(angle));
    }
    
    return smoothness / (positions.length - 2);
  }

  calculateAngleBetweenVectors(v1, v2) {
    const dotProduct = v1.x * v2.x + v1.y * v2.y;
    const magnitude1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const magnitude2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);
    
    if (magnitude1 === 0 || magnitude2 === 0) return 0;
    
    const cosTheta = dotProduct / (magnitude1 * magnitude2);
    return Math.acos(Math.max(-1, Math.min(1, cosTheta))) * (180 / Math.PI);
  }

  assessExecutivePlanning(steps) {
    // Avalia funções executivas de planejamento
    const planningMetrics = {
      sequencing: this.assessSequencing(steps),
      goalDirection: this.assessGoalDirection(steps),
      flexibility: this.assessPlanningFlexibility(steps)
    };
    
    return (planningMetrics.sequencing + planningMetrics.goalDirection + planningMetrics.flexibility) / 3;
  }

  assessSequencing(steps) {
    // Avalia capacidade de sequenciamento
    const logicalSequences = this.assessSequentialLogic(steps);
    return logicalSequences;
  }

  assessGoalDirection(steps) {
    // Avalia direcionamento para objetivo
    const constructiveActions = steps.filter(step => 
      step.action === 'place' || step.action === 'add'
    ).length;
    const totalActions = steps.length;
    
    return (constructiveActions / Math.max(totalActions, 1)) * 100;
  }

  assessPlanningFlexibility(steps) {
    // Avalia flexibilidade no planejamento
    const strategyChanges = this.countStrategyChanges(steps);
    const adaptationScore = Math.min(100, strategyChanges * 20);
    
    return adaptationScore;
  }

  countStrategyChanges(steps) {
    // Conta mudanças de estratégia durante construção
    if (steps.length < 6) return 0;
    
    const segments = this.divideIntoSegments(steps, 3);
    const strategies = segments.map(segment => this.identifySegmentStrategy(segment));
    
    let changes = 0;
    for (let i = 1; i < strategies.length; i++) {
      if (strategies[i] !== strategies[i - 1]) {
        changes++;
      }
    }
    
    return changes;
  }

  divideIntoSegments(steps, segmentCount) {
    const segmentSize = Math.floor(steps.length / segmentCount);
    const segments = [];
    
    for (let i = 0; i < segmentCount; i++) {
      const start = i * segmentSize;
      const end = i === segmentCount - 1 ? steps.length : (i + 1) * segmentSize;
      segments.push(steps.slice(start, end));
    }
    
    return segments;
  }

  identifySegmentStrategy(segment) {
    // Identifica estratégia do segmento
    const positions = segment.map(step => step.position).filter(pos => pos);
    if (positions.length < 2) return 'undefined';
    
    const pattern = this.calculateLinearPattern(positions);
    if (pattern > 0.7) return 'linear';
    
    const clustering = this.calculateClusteredPattern(positions);
    if (clustering > 0.7) return 'clustered';
    
    return 'mixed';
  }

  evaluateWorkingMemoryLoad(targetPattern, steps) {
    // Avalia carga da memória de trabalho
    const patternComplexity = targetPattern.length;
    const simultaneousElements = this.calculateSimultaneousElements(steps);
    const memoryDemand = this.calculateMemoryDemand(targetPattern);
    
    const loadScore = (patternComplexity + simultaneousElements + memoryDemand) / 3;
    return Math.min(100, loadScore * 10);
  }

  calculateSimultaneousElements(steps) {
    // Calcula elementos manipulados simultaneamente
    const timeWindows = this.createTimeWindows(steps, 5000); // 5 segundos
    const maxSimultaneous = Math.max(...timeWindows.map(window => window.length));
    
    return Math.min(10, maxSimultaneous);
  }

  createTimeWindows(steps, windowSize) {
    const windows = [];
    
    for (let i = 0; i < steps.length; i++) {
      const windowEnd = steps[i].timestamp;
      const windowStart = windowEnd - windowSize;
      
      const window = steps.filter(step => 
        step.timestamp >= windowStart && step.timestamp <= windowEnd
      );
      
      windows.push(window);
    }
    
    return windows;
  }

  calculateMemoryDemand(targetPattern) {
    // Calcula demanda de memória baseada na complexidade do padrão
    const uniqueElements = new Set(targetPattern.map(element => 
      `${element.shape}_${element.color}_${element.size}`
    )).size;
    
    return Math.min(10, uniqueElements);
  }

  assessInhibitoryControl(steps) {
    // Avalia controle inibitório
    const impulsiveActions = this.countImpulsiveActions(steps);
    const correctionEfficiency = this.evaluateErrorCorrection(steps);
    
    const inhibitionScore = (100 - impulsiveActions) * 0.6 + correctionEfficiency * 0.4;
    return Math.max(0, inhibitionScore);
  }

  countImpulsiveActions(steps) {
    // Conta ações impulsivas (muito rápidas)
    if (steps.length < 2) return 0;
    
    const fastActions = [];
    for (let i = 1; i < steps.length; i++) {
      const timeDiff = steps[i].timestamp - steps[i - 1].timestamp;
      if (timeDiff < 500) { // Menos de 500ms = impulsivo
        fastActions.push(timeDiff);
      }
    }
    
    return (fastActions.length / (steps.length - 1)) * 100;
  }
}
