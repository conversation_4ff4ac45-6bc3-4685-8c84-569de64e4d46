-- 🔧 CORREÇÃO DE SCHEMAS DAS TABELAS
-- <PERSON><PERSON><PERSON> colunas faltantes e corrige estruturas
-- Portal Betina V3

-- 1. Corrigir tabela game_sessions
ALTER TABLE game_sessions 
ADD COLUMN IF NOT EXISTS session_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN IF NOT EXISTS ended_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS duration INTEGER;

-- 2. Corrigir tabela game_metrics
ALTER TABLE game_metrics 
ADD COLUMN IF NOT EXISTS accuracy DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS response_time INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS engagement_score DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS analysis_data JSONB DEFAULT '{}';

-- 3. Corrigir tabela therapeutic_analysis
ALTER TABLE therapeutic_analysis 
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS recommendations JSONB DEFAULT '{}';

-- 4. Corrigir tabela user_progress
ALTER TABLE user_progress 
ADD COLUMN IF NOT EXISTS last_session TIMESTAMP,
ADD COLUMN IF NOT EXISTS total_sessions INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS average_accuracy DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS milestone_data JSONB DEFAULT '{}';

-- 5. Corrigir tabela multisensory_data
ALTER TABLE multisensory_data 
ADD COLUMN IF NOT EXISTS calibration_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS processing_metadata JSONB DEFAULT '{}';

-- 6. Corrigir tabela sensor_calibration
ALTER TABLE sensor_calibration 
ADD COLUMN IF NOT EXISTS accuracy_score DECIMAL(5,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS device_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 7. Adicionar índices para performance
CREATE INDEX IF NOT EXISTS idx_game_sessions_user_id ON game_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_game_id ON game_sessions(game_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_started_at ON game_sessions(started_at);

CREATE INDEX IF NOT EXISTS idx_game_metrics_user_id ON game_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_game_metrics_game_id ON game_metrics(game_id);
CREATE INDEX IF NOT EXISTS idx_game_metrics_created_at ON game_metrics(created_at);
CREATE INDEX IF NOT EXISTS idx_game_metrics_accuracy ON game_metrics(accuracy);

CREATE INDEX IF NOT EXISTS idx_multisensory_session ON multisensory_data(session_id);
CREATE INDEX IF NOT EXISTS idx_multisensory_sensor_type ON multisensory_data(sensor_type);
CREATE INDEX IF NOT EXISTS idx_multisensory_timestamp ON multisensory_data(timestamp);

CREATE INDEX IF NOT EXISTS idx_therapeutic_analysis_user_id ON therapeutic_analysis(user_id);
CREATE INDEX IF NOT EXISTS idx_therapeutic_analysis_type ON therapeutic_analysis(analysis_type);

CREATE INDEX IF NOT EXISTS idx_user_progress_user_game ON user_progress(user_id, game_id);
CREATE INDEX IF NOT EXISTS idx_user_progress_updated_at ON user_progress(updated_at);

-- 8. Corrigir tabelas específicas dos jogos
DO $$
DECLARE
    game_table TEXT;
    game_tables TEXT[] := ARRAY[
        'colormatch_metrics',
        'contagemnumeros_metrics', 
        'imageassociation_metrics',
        'memorygame_metrics',
        'musicalsequence_metrics',
        'padroesvisuais_metrics',
        'quebracabeca_metrics',
        'creativepainting_metrics',
        'letterrecognition_metrics'
    ];
BEGIN
    FOREACH game_table IN ARRAY game_tables
    LOOP
        -- Adicionar colunas padrão para todas as tabelas de jogos
        EXECUTE format('
            ALTER TABLE %I 
            ADD COLUMN IF NOT EXISTS session_id VARCHAR(255) NOT NULL DEFAULT ''default_session'',
            ADD COLUMN IF NOT EXISTS user_id VARCHAR(255) NOT NULL DEFAULT ''default_user'',
            ADD COLUMN IF NOT EXISTS game_specific_data JSONB DEFAULT ''{}''::jsonb,
            ADD COLUMN IF NOT EXISTS collectors_data JSONB DEFAULT ''{}''::jsonb,
            ADD COLUMN IF NOT EXISTS performance_metrics JSONB DEFAULT ''{}''::jsonb,
            ADD COLUMN IF NOT EXISTS therapeutic_indicators JSONB DEFAULT ''{}''::jsonb,
            ADD COLUMN IF NOT EXISTS multisensory_data JSONB DEFAULT ''{}''::jsonb,
            ADD COLUMN IF NOT EXISTS accuracy DECIMAL(5,2) DEFAULT 0,
            ADD COLUMN IF NOT EXISTS response_time INTEGER DEFAULT 0,
            ADD COLUMN IF NOT EXISTS engagement_score DECIMAL(5,2) DEFAULT 0,
            ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ', game_table);
        
        -- Adicionar índices
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_user_id ON %I(user_id)', game_table, game_table);
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_session_id ON %I(session_id)', game_table, game_table);
        EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%I_created_at ON %I(created_at)', game_table, game_table);
        
        RAISE NOTICE 'Tabela % atualizada com sucesso', game_table;
    END LOOP;
END $$;

-- 9. Criar view para dashboard com dados consolidados
CREATE OR REPLACE VIEW dashboard_metrics AS
SELECT 
    gs.game_id,
    gs.user_id,
    gs.session_id,
    gs.started_at,
    gs.duration,
    gm.accuracy,
    gm.response_time,
    gm.engagement_score,
    COUNT(md.id) as multisensory_readings,
    ta.analysis_type,
    ta.confidence_score,
    up.total_sessions,
    up.average_accuracy as user_avg_accuracy
FROM game_sessions gs
LEFT JOIN game_metrics gm ON gs.session_id = gm.session_id
LEFT JOIN multisensory_data md ON gs.session_id = md.session_id
LEFT JOIN therapeutic_analysis ta ON gs.session_id = ta.session_id
LEFT JOIN user_progress up ON gs.user_id = up.user_id AND gs.game_id = up.game_id;

-- 10. Criar função para inserir dados de teste
CREATE OR REPLACE FUNCTION insert_test_data()
RETURNS void AS $$
DECLARE
    test_session_id VARCHAR(255) := 'test_session_' || extract(epoch from now())::bigint;
    test_user_id VARCHAR(255) := 'test_user_dashboard';
BEGIN
    -- Inserir sessão de teste
    INSERT INTO game_sessions (session_id, user_id, game_id, session_data, duration)
    VALUES (test_session_id, test_user_id, 'ColorMatch', '{"test": true}'::jsonb, 120000);
    
    -- Inserir métricas de teste
    INSERT INTO game_metrics (session_id, user_id, game_id, metrics_data, accuracy, response_time, engagement_score)
    VALUES (test_session_id, test_user_id, 'ColorMatch', '{"test_metrics": true}'::jsonb, 85.5, 1500, 92.3);
    
    -- Inserir dados multissensoriais de teste
    INSERT INTO multisensory_data (session_id, user_id, game_id, sensor_type, sensor_data)
    VALUES (test_session_id, test_user_id, 'ColorMatch', 'touch', '{"x": 100, "y": 200, "pressure": 0.8}'::jsonb);
    
    -- Inserir análise terapêutica de teste
    INSERT INTO therapeutic_analysis (session_id, user_id, analysis_type, analysis_data, confidence_score)
    VALUES (test_session_id, test_user_id, 'cognitive', '{"attention": 0.85, "memory": 0.78}'::jsonb, 0.82);
    
    -- Inserir progresso do usuário
    INSERT INTO user_progress (user_id, game_id, progress_data, total_sessions, average_accuracy)
    VALUES (test_user_id, 'ColorMatch', '{"level": 3, "achievements": ["first_game"]}'::jsonb, 5, 83.2)
    ON CONFLICT (user_id, game_id) DO UPDATE SET
        total_sessions = user_progress.total_sessions + 1,
        average_accuracy = (user_progress.average_accuracy + 85.5) / 2,
        updated_at = CURRENT_TIMESTAMP;
    
    RAISE NOTICE 'Dados de teste inseridos com sucesso para sessão: %', test_session_id;
END;
$$ LANGUAGE plpgsql;

-- 11. Executar inserção de dados de teste
SELECT insert_test_data();

-- 12. Verificar dados inseridos
SELECT 'game_sessions' as tabela, count(*) as registros FROM game_sessions
UNION ALL
SELECT 'game_metrics' as tabela, count(*) as registros FROM game_metrics
UNION ALL
SELECT 'multisensory_data' as tabela, count(*) as registros FROM multisensory_data
UNION ALL
SELECT 'therapeutic_analysis' as tabela, count(*) as registros FROM therapeutic_analysis
UNION ALL
SELECT 'user_progress' as tabela, count(*) as registros FROM user_progress;

-- 13. Testar view do dashboard
SELECT 
    game_id,
    user_id,
    accuracy,
    multisensory_readings,
    confidence_score,
    total_sessions
FROM dashboard_metrics 
WHERE user_id = 'test_user_dashboard'
LIMIT 5;
