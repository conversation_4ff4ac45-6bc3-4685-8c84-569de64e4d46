import{j as a,P as e}from"./index-BIwBZl_j.js";import{r as o}from"./react-router-BtSsPy6x.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";const s={dashboardContainer:"_dashboardContainer_1x5np_2",dashboardHeader:"_dashboardHeader_1x5np_13",headerContent:"_headerContent_1x5np_22",title:"_title_1x5np_26",titleIcon:"_titleIcon_1x5np_39",subtitle:"_subtitle_1x5np_44",premiumBadge:"_premiumBadge_1x5np_51",premiumIcon:"_premiumIcon_1x5np_64",alert:"_alert_1x5np_69",success:"_success_1x5np_80",error:"_error_1x5np_86",info:"_info_1x5np_92",alertIcon:"_alertIcon_1x5np_98",alertMessage:"_alertMessage_1x5np_103",alertClose:"_alertClose_1x5np_108",dashboardGrid:"_dashboardGrid_1x5np_129",card:"_card_1x5np_137",cardHeader:"_cardHeader_1x5np_151",cardTitle:"_cardTitle_1x5np_160",cardIcon:"_cardIcon_1x5np_170",refreshButton:"_refreshButton_1x5np_174",cardContent:"_cardContent_1x5np_196",statusGrid:"_statusGrid_1x5np_201",statusItem:"_statusItem_1x5np_207",statusLabel:"_statusLabel_1x5np_217",statusValue:"_statusValue_1x5np_223",enabled:"_enabled_1x5np_229",disabled:"_disabled_1x5np_233",loadingStatus:"_loadingStatus_1x5np_237",loadingSpinner:"_loadingSpinner_1x5np_246",spin:"_spin_1x5np_1",exportOptions:"_exportOptions_1x5np_261",optionItem:"_optionItem_1x5np_266",optionLabel:"_optionLabel_1x5np_279",optionCheckbox:"_optionCheckbox_1x5np_287",optionText:"_optionText_1x5np_295",progressContainer:"_progressContainer_1x5np_315",progressLabel:"_progressLabel_1x5np_323",progressBar:"_progressBar_1x5np_330",progressFill:"_progressFill_1x5np_338",progressPulse:"_progressPulse_1x5np_1",actionButtons:"_actionButtons_1x5np_352",actionButton:"_actionButton_1x5np_352",primaryButton:"_primaryButton_1x5np_375",successButton:"_successButton_1x5np_392",statsContainer:"_statsContainer_1x5np_404",statsGrid:"_statsGrid_1x5np_419",statItem:"_statItem_1x5np_425",statLabel:"_statLabel_1x5np_435",statValue:"_statValue_1x5np_440",previewContainer:"_previewContainer_1x5np_447",previewInfo:"_previewInfo_1x5np_461",previewContent:"_previewContent_1x5np_479",premiumFeatures:"_premiumFeatures_1x5np_495",featureItem:"_featureItem_1x5np_500",featureIcon:"_featureIcon_1x5np_516",featureContent:"_featureContent_1x5np_522"};function r({userId:e="user_demo",userDetails:r=null,isPremiumUser:t=!0,onError:n=()=>{},onLoading:i=()=>{}}){const[c,d]=o.useState(null),[p,l]=o.useState(!1),[u,m]=o.useState(!1),[b,h]=o.useState(null),[x,N]=o.useState(null),[D,j]=o.useState(null),[E,v]=o.useState(null),[k,B]=o.useState(0),[f,_]=o.useState(null),[g,C]=o.useState({userProfiles:!0,gameProgress:!0,accessibilitySettings:!0,preferences:!0,gameMetrics:!0,sessionData:!0}),P=a=>{const o={isValid:!0,errors:[],warnings:[]};e&&"string"==typeof e&&""!==e.trim()||(o.errors.push("ID do usuário é obrigatório"),o.isValid=!1),g&&"object"==typeof g||(o.errors.push("Opções de exportação inválidas"),o.isValid=!1);return 0===Object.values(g).filter(Boolean).length&&(o.errors.push("Selecione pelo menos uma categoria para backup"),o.isValid=!1),a&&"object"==typeof a&&(a.version||o.warnings.push("Versão do backup não especificada"),a.exportDate||o.warnings.push("Data de exportação não especificada"),a.data&&0!==Object.keys(a.data).length||o.warnings.push("Nenhum dado foi coletado para backup")),o},V=(a,e)=>{h({type:a,message:e}),setTimeout(()=>h(null),5e3)};return a.jsxDEV("div",{className:s.dashboardContainer,children:[a.jsxDEV("div",{className:s.dashboardHeader,children:[a.jsxDEV("div",{className:s.headerContent,children:[a.jsxDEV("h1",{className:s.title,children:[a.jsxDEV("span",{className:s.titleIcon,children:"💾"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:411,columnNumber:13},this),"Backup e Exportação Premium"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:410,columnNumber:11},this),a.jsxDEV("p",{className:s.subtitle,children:"Gerencie seus dados com recursos avançados exclusivos para usuários premium"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:414,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:409,columnNumber:9},this),t&&a.jsxDEV("div",{className:s.premiumBadge,children:[a.jsxDEV("span",{className:s.premiumIcon,children:"💎"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:421,columnNumber:13},this),"Premium"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:420,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:408,columnNumber:7},this),b&&a.jsxDEV("div",{className:`${s.alert} ${s[b.type]}`,children:[a.jsxDEV("span",{className:s.alertIcon,children:"success"===b.type?"✅":"error"===b.type?"❌":"ℹ️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:430,columnNumber:11},this),a.jsxDEV("span",{className:s.alertMessage,children:b.message},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:433,columnNumber:11},this),a.jsxDEV("button",{className:s.alertClose,onClick:()=>h(null),children:"×"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:434,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:429,columnNumber:9},this),a.jsxDEV("div",{className:s.dashboardGrid,children:[a.jsxDEV("div",{className:s.card,children:[a.jsxDEV("div",{className:s.cardHeader,children:[a.jsxDEV("h2",{className:s.cardTitle,children:[a.jsxDEV("span",{className:s.cardIcon,children:"📊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:448,columnNumber:15},this),"Status de Backup"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:447,columnNumber:13},this),a.jsxDEV("button",{onClick:async()=>{if(e)try{i(!0);const a=await fetch(`/api/backup/status/${e}`);if(!a.ok)throw new Error("Erro ao buscar status");{const e=await a.json();_(e.data),V("success","Status de backup atualizado!")}}catch(a){V("error","Erro ao buscar status de backup"),_({lastBackup:new Date(Date.now()-864e5).toISOString(),totalBackups:5,totalDataSize:2048576,autoBackupEnabled:!1})}finally{i(!1)}},className:s.refreshButton,title:"Atualizar status",children:"🔄"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:451,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:446,columnNumber:11},this),a.jsxDEV("div",{className:s.cardContent,children:f?a.jsxDEV("div",{className:s.statusGrid,children:[a.jsxDEV("div",{className:s.statusItem,children:[a.jsxDEV("span",{className:s.statusLabel,children:"Último backup:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:463,columnNumber:19},this),a.jsxDEV("span",{className:s.statusValue,children:new Date(f.lastBackup).toLocaleString("pt-BR")},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:464,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:462,columnNumber:17},this),a.jsxDEV("div",{className:s.statusItem,children:[a.jsxDEV("span",{className:s.statusLabel,children:"Total de backups:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:469,columnNumber:19},this),a.jsxDEV("span",{className:s.statusValue,children:f.totalBackups},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:470,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:468,columnNumber:17},this),a.jsxDEV("div",{className:s.statusItem,children:[a.jsxDEV("span",{className:s.statusLabel,children:"Tamanho total:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:473,columnNumber:19},this),a.jsxDEV("span",{className:s.statusValue,children:[(f.totalDataSize/1024/1024).toFixed(2)," MB"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:474,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:472,columnNumber:17},this),a.jsxDEV("div",{className:s.statusItem,children:[a.jsxDEV("span",{className:s.statusLabel,children:"Backup automático:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:479,columnNumber:19},this),a.jsxDEV("span",{className:`${s.statusValue} ${f.autoBackupEnabled?s.enabled:s.disabled}`,children:f.autoBackupEnabled?"Ativado":"Desativado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:480,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:478,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:461,columnNumber:15},this):a.jsxDEV("div",{className:s.loadingStatus,children:[a.jsxDEV("div",{className:s.loadingSpinner},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:487,columnNumber:17},this),a.jsxDEV("p",{children:"Carregando informações de backup..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:488,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:486,columnNumber:15},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:459,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:445,columnNumber:9},this),a.jsxDEV("div",{className:s.card,children:[a.jsxDEV("div",{className:s.cardHeader,children:a.jsxDEV("h2",{className:s.cardTitle,children:[a.jsxDEV("span",{className:s.cardIcon,children:"⚙️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:498,columnNumber:15},this),"Opções de Exportação"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:497,columnNumber:13},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:496,columnNumber:11},this),a.jsxDEV("div",{className:s.cardContent,children:a.jsxDEV("div",{className:s.exportOptions,children:Object.entries({userProfiles:{icon:"👤",title:"Perfis de usuário",desc:"Informações dos perfis criados e configurações pessoais"},gameProgress:{icon:"🎮",title:"Progresso nos jogos",desc:"Histórico, pontuações e progressão em todos os jogos"},gameMetrics:{icon:"📊",title:"Métricas de jogos",desc:"Dados detalhados de performance e análises"},sessionData:{icon:"🕐",title:"Dados de sessão",desc:"Informações de sessões terapêuticas e atividades"},accessibilitySettings:{icon:"♿",title:"Configurações de acessibilidade",desc:"Preferências de alto contraste, fonte, navegação, etc."},preferences:{icon:"⚙️",title:"Preferências gerais",desc:"Configurações personalizadas do sistema e interface"}}).map(([e,o])=>a.jsxDEV("div",{className:s.optionItem,children:a.jsxDEV("label",{className:s.optionLabel,children:[a.jsxDEV("input",{type:"checkbox",checked:g[e],onChange:()=>(a=>{C(e=>({...e,[a]:!e[a]}))})(e),className:s.optionCheckbox},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:514,columnNumber:21},this),a.jsxDEV("span",{className:s.optionText,children:[a.jsxDEV("strong",{children:[o.icon," ",o.title]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:521,columnNumber:23},this),a.jsxDEV("small",{children:o.desc},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:522,columnNumber:23},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:520,columnNumber:21},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:513,columnNumber:19},this)},e,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:512,columnNumber:17},this))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:503,columnNumber:13},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:502,columnNumber:11},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:495,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:443,columnNumber:7},this),a.jsxDEV("div",{className:s.card,children:[a.jsxDEV("div",{className:s.cardHeader,children:a.jsxDEV("h2",{className:s.cardTitle,children:[a.jsxDEV("span",{className:s.cardIcon,children:"🚀"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:536,columnNumber:13},this),"Ações de Backup"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:535,columnNumber:11},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:534,columnNumber:9},this),a.jsxDEV("div",{className:s.cardContent,children:[p&&a.jsxDEV("div",{className:s.progressContainer,children:[a.jsxDEV("div",{className:s.progressLabel,children:["Gerando backup... ",k,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:544,columnNumber:15},this),a.jsxDEV("div",{className:s.progressBar,children:a.jsxDEV("div",{className:s.progressFill,style:{width:`${k}%`}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:548,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:547,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:543,columnNumber:13},this),a.jsxDEV("div",{className:s.actionButtons,children:[a.jsxDEV("button",{onClick:async()=>{const a=P(null);if(a.isValid)if(e){l(!0),B(0),i(!0);try{let a={version:"3.1.0",exportDate:(new Date).toISOString(),userId:e,userDetails:r,data:{},metadata:{source:"premium_dashboard",totalItems:0,categories:[]}},n=0;const i=[];if(B(10),g.userProfiles){const e=localStorage.getItem("betina_profiles");if(e)try{a.data.userProfiles=JSON.parse(e),n+=Array.isArray(a.data.userProfiles)?a.data.userProfiles.length:1,i.push("userProfiles")}catch(o){a.data.userProfiles=e}}if(B(25),g.gameProgress){const e={};for(let a=0;a<localStorage.length;a++){const s=localStorage.key(a);if(s.startsWith("betina_")&&(s.includes("_history")||s.includes("_progress")||s.includes("_scores")))try{e[s]=JSON.parse(localStorage.getItem(s)),n++}catch(o){e[s]=localStorage.getItem(s)}}a.data.gameProgress=e,Object.keys(e).length>0&&i.push("gameProgress")}if(B(50),g.gameMetrics){const e={};for(let a=0;a<localStorage.length;a++){const s=localStorage.key(a);if(s.startsWith("betina_")&&s.includes("_metrics"))try{e[s]=JSON.parse(localStorage.getItem(s)),n++}catch(o){e[s]=localStorage.getItem(s)}}a.data.gameMetrics=e,Object.keys(e).length>0&&i.push("gameMetrics")}if(B(75),g.accessibilitySettings){const e=localStorage.getItem("betina_accessibility_settings");if(e)try{a.data.accessibilitySettings=JSON.parse(e),n++,i.push("accessibilitySettings")}catch(o){a.data.accessibilitySettings=e}}if(g.preferences){const e=localStorage.getItem("betina_user_preferences");if(e)try{a.data.preferences=JSON.parse(e),n++,i.push("preferences")}catch(o){a.data.preferences=e}}if(g.sessionData){const e={};for(let a=0;a<localStorage.length;a++){const s=localStorage.key(a);if(s.startsWith("betina_")&&s.includes("_session"))try{e[s]=JSON.parse(localStorage.getItem(s)),n++}catch(o){e[s]=localStorage.getItem(s)}}a.data.sessionData=e,Object.keys(e).length>0&&i.push("sessionData")}if(B(90),t)try{const o=await fetch("/api/backup/user-data",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:e,options:g})});if(o.ok){const e=await o.json();e.success&&(Object.keys(e.data).forEach(o=>{e.data[o]&&Object.keys(e.data[o]).length>0&&(a.data[o]=e.data[o],i.includes(o)||i.push(o))}),n+=e.totalItems||0,a.metadata.serverData={connected:!0,timestamp:(new Date).toISOString(),dataSource:"hybrid_real",serverCategories:e.categories||[],realDataIncluded:!0},V("success","✅ Dados reais do servidor incluídos no backup!"))}else V("warning","⚠️ Usando dados locais - servidor indisponível"),a.metadata.serverError={status:o.status,message:"Erro ao conectar com servidor",timestamp:(new Date).toISOString(),fallback:"Usando apenas dados locais"}}catch(s){a.metadata.connectionError={error:s.message,timestamp:(new Date).toISOString(),fallback:"Usando apenas dados locais",recommendation:"Verifique sua conexão com a internet"}}else a.metadata.premiumLimitation={message:"Backup limitado a dados locais",upgrade:"Faça upgrade para Premium para backup completo na nuvem",timestamp:(new Date).toISOString()};a.metadata.totalItems=n,a.metadata.categories=i;const c=P(a);if(!c.isValid)throw new Error(`Validação falhou: ${c.errors.join(", ")}`);c.warnings.length,B(100);const p={totalSize:JSON.stringify(a).length,totalItems:n,categories:i.length,compressionRatio:.85,estimatedDownloadTime:Math.ceil(JSON.stringify(a).length/1024/100)};d(a),v(p),V("success",`Backup gerado com sucesso! ${n} itens em ${i.length} categorias.`)}catch(s){n(`Erro ao gerar backup: ${s.message}`),V("error",`Erro ao gerar backup: ${s.message}`)}finally{l(!1),B(0),i(!1)}}else V("error","Erro: Não foi possível identificar o usuário.");else V("error",`Erro de validação: ${a.errors.join(", ")}`)},disabled:p||!e,className:`${s.actionButton} ${s.primaryButton}`,children:p?"🔄 Gerando...":"📦 Gerar Backup Premium"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:557,columnNumber:13},this),c&&a.jsxDEV("button",{onClick:()=>{if(!c)return;const a=JSON.stringify(c,null,2),o=new Blob([a],{type:"application/json"}),s=(new Date).toISOString().replace(/[:.]/g,"-").split("T")[0],r=`betina-premium-backup-${e}-${s}.json`,t=document.createElement("a");t.href=URL.createObjectURL(o),t.download=r,t.click(),URL.revokeObjectURL(t.href),V("success",`Backup baixado com sucesso! Arquivo: ${r}`)},className:`${s.actionButton} ${s.successButton}`,children:"💾 Baixar Backup"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:566,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:556,columnNumber:11},this),E&&a.jsxDEV("div",{className:s.statsContainer,children:[a.jsxDEV("h4",{children:"📊 Estatísticas do Backup:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:578,columnNumber:15},this),a.jsxDEV("div",{className:s.statsGrid,children:[a.jsxDEV("div",{className:s.statItem,children:[a.jsxDEV("span",{className:s.statLabel,children:"Total de itens:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:581,columnNumber:19},this),a.jsxDEV("span",{className:s.statValue,children:E.totalItems},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:582,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:580,columnNumber:17},this),a.jsxDEV("div",{className:s.statItem,children:[a.jsxDEV("span",{className:s.statLabel,children:"Categorias:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:585,columnNumber:19},this),a.jsxDEV("span",{className:s.statValue,children:E.categories},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:586,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:584,columnNumber:17},this),a.jsxDEV("div",{className:s.statItem,children:[a.jsxDEV("span",{className:s.statLabel,children:"Tamanho:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:589,columnNumber:19},this),a.jsxDEV("span",{className:s.statValue,children:[(E.totalSize/1024).toFixed(2)," KB"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:590,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:588,columnNumber:17},this),a.jsxDEV("div",{className:s.statItem,children:[a.jsxDEV("span",{className:s.statLabel,children:"Tempo estimado:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:593,columnNumber:19},this),a.jsxDEV("span",{className:s.statValue,children:[E.estimatedDownloadTime,"s"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:594,columnNumber:19},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:592,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:579,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:577,columnNumber:13},this),c&&a.jsxDEV("div",{className:s.previewContainer,children:[a.jsxDEV("h4",{children:"📋 Preview do Backup:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:602,columnNumber:15},this),a.jsxDEV("div",{className:s.previewInfo,children:[a.jsxDEV("p",{children:[a.jsxDEV("strong",{children:"Versão:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:604,columnNumber:20},this)," ",c.version]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:604,columnNumber:17},this),a.jsxDEV("p",{children:[a.jsxDEV("strong",{children:"Data:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:605,columnNumber:20},this)," ",new Date(c.exportDate).toLocaleString("pt-BR")]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:605,columnNumber:17},this),a.jsxDEV("p",{children:[a.jsxDEV("strong",{children:"Usuário:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:606,columnNumber:20},this)," ",c.userId]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:606,columnNumber:17},this),a.jsxDEV("p",{children:[a.jsxDEV("strong",{children:"Fonte:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:607,columnNumber:20},this)," ",c.metadata?.source||"premium_dashboard"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:607,columnNumber:17},this),a.jsxDEV("p",{children:[a.jsxDEV("strong",{children:"Categorias:"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:608,columnNumber:20},this)," ",c.metadata?.categories?.join(", ")||"N/A"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:608,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:603,columnNumber:15},this),a.jsxDEV("pre",{className:s.previewContent,children:a.jsxDEV("code",{children:[JSON.stringify(c,null,2).substring(0,800),"..."]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:611,columnNumber:17},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:610,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:601,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:540,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:533,columnNumber:7},this),a.jsxDEV("div",{className:s.card,children:[a.jsxDEV("div",{className:s.cardHeader,children:a.jsxDEV("h2",{className:s.cardTitle,children:[a.jsxDEV("span",{className:s.cardIcon,children:"💎"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:622,columnNumber:13},this),"Recursos Premium"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:621,columnNumber:11},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:620,columnNumber:9},this),a.jsxDEV("div",{className:s.cardContent,children:a.jsxDEV("div",{className:s.premiumFeatures,children:[a.jsxDEV("div",{className:s.featureItem,children:[a.jsxDEV("span",{className:s.featureIcon,children:"🔄"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:629,columnNumber:15},this),a.jsxDEV("div",{className:s.featureContent,children:[a.jsxDEV("h4",{children:"Backup Automático"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:631,columnNumber:17},this),a.jsxDEV("p",{children:"Backups automáticos agendados para garantir que seus dados estejam sempre seguros"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:632,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:630,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:628,columnNumber:13},this),a.jsxDEV("div",{className:s.featureItem,children:[a.jsxDEV("span",{className:s.featureIcon,children:"☁️"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:636,columnNumber:15},this),a.jsxDEV("div",{className:s.featureContent,children:[a.jsxDEV("h4",{children:"Sincronização na Nuvem"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:638,columnNumber:17},this),a.jsxDEV("p",{children:"Seus backups são armazenados com segurança na nuvem e sincronizados entre dispositivos"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:639,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:637,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:635,columnNumber:13},this),a.jsxDEV("div",{className:s.featureItem,children:[a.jsxDEV("span",{className:s.featureIcon,children:"🔐"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:643,columnNumber:15},this),a.jsxDEV("div",{className:s.featureContent,children:[a.jsxDEV("h4",{children:"Criptografia Avançada"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:645,columnNumber:17},this),a.jsxDEV("p",{children:"Todos os backups são criptografados com algoritmos de segurança de nível militar"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:646,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:644,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:642,columnNumber:13},this),a.jsxDEV("div",{className:s.featureItem,children:[a.jsxDEV("span",{className:s.featureIcon,children:"📈"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:650,columnNumber:15},this),a.jsxDEV("div",{className:s.featureContent,children:[a.jsxDEV("h4",{children:"Análise Avançada"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:652,columnNumber:17},this),a.jsxDEV("p",{children:"Relatórios detalhados sobre seus dados e padrões de uso ao longo do tempo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:653,columnNumber:17},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:651,columnNumber:15},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:649,columnNumber:13},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:627,columnNumber:11},this)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:626,columnNumber:9},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:619,columnNumber:7},this)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/BackupExportDashboard/BackupExportDashboard.jsx",lineNumber:406,columnNumber:5},this)}r.propTypes={userId:e.string,userDetails:e.object,isPremiumUser:e.bool,onError:e.func,onLoading:e.func};export{r as default};
