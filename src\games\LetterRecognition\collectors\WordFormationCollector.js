/**
 * 🔗 WORD FORMATION COLLECTOR V3
 * Coletor especializado para atividade de formação de palavras
 * Portal Betina V3
 */

export class WordFormationCollector {
  constructor() {
    this.name = 'WordFormationCollector';
    this.version = '3.0.0';
    this.isActive = true;
    this.collectedData = [];
  }

  /**
   * 🎯 Coleta dados específicos da formação de palavras
   */
  async collect(data) {
    try {
      const timestamp = new Date().toISOString();
      
      const analysisData = {
        // Dados básicos
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: 'word_formation',
        
        // Dados da tarefa
        targetWord: data.targetWord,
        formedWord: data.formedWord,
        letterSequence: data.letterSequence || [],
        isCorrect: data.formedWord === data.targetWord,
        responseTime: data.responseTime || 0,
        
        // Métricas de construção
        wordBuildingTime: data.behavioralMetrics?.wordBuildingTime || data.responseTime,
        letterSequenceErrors: data.behavioralMetrics?.letterSequenceErrors || 0,
        backtrackCount: data.behavioralMetrics?.backtrackCount || 0,
        completionTime: data.behavioralMetrics?.completionTime || data.responseTime,
        planningTime: data.behavioralMetrics?.planningTime || 0,
        
        // Análise da palavra
        wordComplexity: this.analyzeWordComplexity(data.targetWord),
        sequenceAccuracy: this.calculateSequenceAccuracy(data.targetWord, data.formedWord),
        constructionStrategy: this.analyzeConstructionStrategy(data),
        
        // Análise cognitiva
        executiveFunction: this.assessExecutiveFunction(data),
        workingMemory: this.assessWorkingMemory(data),
        sequentialPlanning: this.assessSequentialPlanning(data),
        
        // Habilidades linguísticas
        orthographicKnowledge: this.assessOrthographicKnowledge(data),
        phoneticMapping: this.assessPhoneticMapping(data),
        wordRecognition: this.assessWordRecognition(data),
        
        // Padrões de erro
        errorType: this.classifyConstructionError(data),
        errorPosition: this.analyzeErrorPosition(data.targetWord, data.formedWord),
        
        // Desenvolvimento
        constructionSkillLevel: this.assessConstructionSkillLevel(data),
        learningStrategy: this.identifyLearningStrategy(data)
      };
      
      this.collectedData.push(analysisData);
      return analysisData;
      
    } catch (error) {
      console.error('Erro no WordFormationCollector:', error);
      return null;
    }
  }

  /**
   * 📚 Analisa complexidade da palavra
   */
  analyzeWordComplexity(word) {
    if (!word) return 0.5;
    
    let complexity = 0.2; // Base
    
    // Comprimento da palavra
    complexity += Math.min(0.3, (word.length - 3) * 0.05);
    
    // Letras difíceis
    const difficultLetters = ['q', 'x', 'z', 'j', 'w', 'y'];
    const difficultCount = word.toLowerCase().split('').filter(l => difficultLetters.includes(l)).length;
    complexity += difficultCount * 0.1;
    
    // Combinações de letras complexas
    const complexCombinations = ['ch', 'sh', 'th', 'ph', 'ck', 'qu'];
    let complexCount = 0;
    complexCombinations.forEach(combo => {
      if (word.toLowerCase().includes(combo)) complexCount++;
    });
    complexity += complexCount * 0.1;
    
    // Vogais duplas
    const doubleVowels = ['aa', 'ee', 'ii', 'oo', 'uu', 'ae', 'ou', 'ai'];
    doubleVowels.forEach(vowels => {
      if (word.toLowerCase().includes(vowels)) complexity += 0.05;
    });
    
    return Math.min(1.0, complexity);
  }

  /**
   * 📐 Calcula precisão da sequência
   */
  calculateSequenceAccuracy(target, formed) {
    if (!target || !formed) return 0;
    if (target === formed) return 1.0;
    
    const targetArray = target.toLowerCase().split('');
    const formedArray = formed.toLowerCase().split('');
    
    let correctPositions = 0;
    const maxLength = Math.max(targetArray.length, formedArray.length);
    
    for (let i = 0; i < maxLength; i++) {
      if (targetArray[i] === formedArray[i]) {
        correctPositions++;
      }
    }
    
    return correctPositions / maxLength;
  }

  /**
   * 🎯 Analisa estratégia de construção
   */
  analyzeConstructionStrategy(data) {
    const strategies = [];
    
    const backtrackCount = data.behavioralMetrics?.backtrackCount || 0;
    const completionTime = data.behavioralMetrics?.completionTime || data.responseTime;
    const planningTime = data.behavioralMetrics?.planningTime || 0;
    const wordLength = data.targetWord?.length || 3;
    
    // Estratégias baseadas em planejamento
    const planningRatio = planningTime / completionTime;
    if (planningRatio > 0.3) strategies.push('planning_first');
    else if (planningRatio < 0.1) strategies.push('immediate_action');
    
    // Estratégias baseadas em backtracking
    if (backtrackCount === 0) strategies.push('linear_construction');
    else if (backtrackCount <= 2) strategies.push('minor_adjustments');
    else strategies.push('trial_and_error');
    
    // Estratégias baseadas em tempo
    const timePerLetter = completionTime / wordLength;
    if (timePerLetter < 1000) strategies.push('rapid_construction');
    else if (timePerLetter > 2000) strategies.push('methodical_construction');
    
    // Estratégias baseadas em precisão
    const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
    if (accuracy === 1.0 && backtrackCount === 0) strategies.push('expert_builder');
    else if (accuracy > 0.8) strategies.push('skilled_builder');
    else if (accuracy < 0.5) strategies.push('learning_builder');
    
    return strategies.length > 0 ? strategies : ['standard_approach'];
  }

  /**
   * 🧠 Avalia função executiva
   */
  assessExecutiveFunction(data) {
    let executiveScore = 0.5; // Base
    
    const backtrackCount = data.behavioralMetrics?.backtrackCount || 0;
    const planningTime = data.behavioralMetrics?.planningTime || 0;
    const completionTime = data.behavioralMetrics?.completionTime || data.responseTime;
    
    // Planejamento (evidenciado por tempo de planejamento)
    const planningRatio = planningTime / completionTime;
    if (planningRatio > 0.2) executiveScore += 0.2;
    else if (planningRatio < 0.05) executiveScore -= 0.1;
    
    // Inibição (evidenciada por poucos backtracks)
    if (backtrackCount === 0) executiveScore += 0.2;
    else if (backtrackCount <= 1) executiveScore += 0.1;
    else if (backtrackCount > 3) executiveScore -= 0.2;
    
    // Flexibilidade cognitiva (capacidade de corrigir erros)
    if (backtrackCount > 0 && data.formedWord === data.targetWord) {
      executiveScore += 0.1; // Soube corrigir
    }
    
    // Monitoramento (evidenciado pela precisão final)
    const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
    executiveScore += (accuracy - 0.5) * 0.4;
    
    return Math.max(0.0, Math.min(1.0, executiveScore));
  }

  /**
   * 💭 Avalia memória de trabalho
   */
  assessWorkingMemory(data) {
    let memoryScore = 0.6; // Base
    
    const wordLength = data.targetWord?.length || 3;
    const sequenceErrors = data.behavioralMetrics?.letterSequenceErrors || 0;
    const completionTime = data.behavioralMetrics?.completionTime || data.responseTime;
    
    // Capacidade baseada no comprimento da palavra
    if (wordLength <= 3) memoryScore += 0.1;
    else if (wordLength >= 6) {
      if (data.formedWord === data.targetWord) memoryScore += 0.3; // Bônus por palavra longa correta
      else memoryScore -= 0.2;
    }
    
    // Precisão da sequência indica boa memória de trabalho
    const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
    memoryScore += (accuracy - 0.5) * 0.4;
    
    // Poucos erros de sequência
    if (sequenceErrors === 0) memoryScore += 0.2;
    else memoryScore -= sequenceErrors * 0.1;
    
    // Tempo adequado (nem muito rápido nem muito lento)
    const optimalTime = wordLength * 1500; // 1.5s por letra
    const timeDeviation = Math.abs(completionTime - optimalTime) / optimalTime;
    if (timeDeviation < 0.3) memoryScore += 0.1;
    
    return Math.max(0.0, Math.min(1.0, memoryScore));
  }

  /**
   * 📋 Avalia planejamento sequencial
   */
  assessSequentialPlanning(data) {
    let planningScore = 0.5; // Base
    
    const sequence = data.letterSequence || [];
    const target = data.targetWord?.toLowerCase().split('') || [];
    const backtrackCount = data.behavioralMetrics?.backtrackCount || 0;
    
    // Analisa ordem de construção
    if (sequence.length > 0) {
      let sequentialOrder = 0;
      for (let i = 0; i < Math.min(sequence.length, target.length); i++) {
        if (sequence[i] === target[i]) sequentialOrder++;
      }
      const orderAccuracy = sequentialOrder / target.length;
      planningScore += orderAccuracy * 0.3;
    }
    
    // Poucos backtracks indicam bom planejamento
    if (backtrackCount === 0) planningScore += 0.3;
    else if (backtrackCount === 1) planningScore += 0.1;
    else planningScore -= backtrackCount * 0.1;
    
    // Tempo de planejamento adequado
    const planningTime = data.behavioralMetrics?.planningTime || 0;
    if (planningTime > 500) planningScore += 0.2;
    
    return Math.max(0.0, Math.min(1.0, planningScore));
  }

  /**
   * 📝 Avalia conhecimento ortográfico
   */
  assessOrthographicKnowledge(data) {
    let knowledge = 0.5; // Base
    
    const target = data.targetWord?.toLowerCase() || '';
    const formed = data.formedWord?.toLowerCase() || '';
    
    // Palavra completamente correta
    if (target === formed) {
      knowledge = 0.9;
      
      // Complexidade da palavra
      const complexity = this.analyzeWordComplexity(target);
      if (complexity > 0.6) knowledge += 0.1; // Bônus por palavra complexa
    } else {
      // Analisa tipos de erro
      const accuracy = this.calculateSequenceAccuracy(target, formed);
      knowledge += (accuracy - 0.5) * 0.6;
      
      // Verifica se mantém estrutura básica
      if (formed.length === target.length) knowledge += 0.1;
    }
    
    return Math.max(0.0, Math.min(1.0, knowledge));
  }

  /**
   * 🔊 Avalia mapeamento fonético
   */
  assessPhoneticMapping(data) {
    let mapping = 0.6; // Base
    
    const target = data.targetWord?.toLowerCase() || '';
    const formed = data.formedWord?.toLowerCase() || '';
    
    if (target === formed) {
      mapping += 0.3;
    } else {
      // Analisa se erros são fonéticamente plausíveis
      const phoneticErrors = this.countPhoneticErrors(target, formed);
      const totalDifferences = this.countDifferences(target, formed);
      
      if (totalDifferences > 0) {
        const phoneticAccuracy = 1 - (phoneticErrors / totalDifferences);
        mapping += phoneticAccuracy * 0.3;
      }
    }
    
    return Math.max(0.0, Math.min(1.0, mapping));
  }

  /**
   * 👁️ Avalia reconhecimento de palavra
   */
  assessWordRecognition(data) {
    let recognition = 0.5; // Base
    
    // Palavra correta indica reconhecimento
    if (data.targetWord === data.formedWord) {
      recognition = 0.9;
      
      // Velocidade indica reconhecimento automático
      const timePerLetter = data.responseTime / (data.targetWord?.length || 3);
      if (timePerLetter < 1000) recognition += 0.1;
    } else {
      // Proximidade da palavra correta
      const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
      recognition += accuracy * 0.4;
    }
    
    return recognition;
  }

  /**
   * ❌ Classifica erro de construção
   */
  classifyConstructionError(data) {
    if (data.targetWord === data.formedWord) return 'no_error';
    
    const target = data.targetWord?.toLowerCase() || '';
    const formed = data.formedWord?.toLowerCase() || '';
    
    // Erro de comprimento
    if (target.length !== formed.length) {
      if (formed.length < target.length) return 'incomplete_word';
      else return 'extended_word';
    }
    
    // Analisa posição dos erros
    const differences = [];
    for (let i = 0; i < target.length; i++) {
      if (target[i] !== formed[i]) {
        differences.push(i);
      }
    }
    
    if (differences.length === 1) return 'single_letter_error';
    if (differences.length === 2 && Math.abs(differences[0] - differences[1]) === 1) return 'adjacent_letter_error';
    if (differences.length <= target.length / 2) return 'partial_construction_error';
    
    return 'major_construction_error';
  }

  /**
   * 📍 Analisa posição do erro
   */
  analyzeErrorPosition(target, formed) {
    if (!target || !formed || target === formed) return null;
    
    const errorPositions = [];
    const maxLength = Math.max(target.length, formed.length);
    
    for (let i = 0; i < maxLength; i++) {
      const targetChar = target[i] || '';
      const formedChar = formed[i] || '';
      
      if (targetChar !== formedChar) {
        errorPositions.push({
          position: i,
          expected: targetChar,
          actual: formedChar,
          type: this.classifyPositionalError(targetChar, formedChar)
        });
      }
    }
    
    return {
      errorCount: errorPositions.length,
      positions: errorPositions,
      pattern: this.identifyErrorPattern(errorPositions)
    };
  }

  /**
   * 🔤 Classifica erro posicional
   */
  classifyPositionalError(expected, actual) {
    if (!expected) return 'extra_letter';
    if (!actual) return 'missing_letter';
    
    // Verifica se é troca de letras similares
    const similarity = this.calculateLetterSimilarity(expected, actual);
    if (similarity > 0.7) return 'similar_letter_substitution';
    
    return 'different_letter_substitution';
  }

  /**
   * 🔍 Identifica padrão de erro
   */
  identifyErrorPattern(errorPositions) {
    if (errorPositions.length === 0) return 'no_pattern';
    if (errorPositions.length === 1) return 'isolated_error';
    
    // Verifica se erros são consecutivos
    const positions = errorPositions.map(e => e.position).sort((a, b) => a - b);
    let consecutive = true;
    for (let i = 1; i < positions.length; i++) {
      if (positions[i] - positions[i-1] > 1) {
        consecutive = false;
        break;
      }
    }
    
    if (consecutive) return 'consecutive_errors';
    
    // Verifica se erros estão no início/meio/fim
    const maxPos = Math.max(...positions);
    const wordLength = errorPositions[0].position !== undefined ? maxPos + 1 : 3;
    
    if (positions.every(p => p < wordLength / 3)) return 'beginning_errors';
    if (positions.every(p => p > 2 * wordLength / 3)) return 'ending_errors';
    
    return 'distributed_errors';
  }

  /**
   * 🎓 Avalia nível de habilidade de construção
   */
  assessConstructionSkillLevel(data) {
    const orthographic = this.assessOrthographicKnowledge(data);
    const planning = this.assessSequentialPlanning(data);
    const executive = this.assessExecutiveFunction(data);
    const memory = this.assessWorkingMemory(data);
    
    const overall = (orthographic + planning + executive + memory) / 4;
    
    if (overall >= 0.8) return 'expert';
    if (overall >= 0.65) return 'proficient';
    if (overall >= 0.45) return 'developing';
    return 'beginner';
  }

  /**
   * 📚 Identifica estratégia de aprendizado
   */
  identifyLearningStrategy(data) {
    const strategies = [];
    
    const backtrackCount = data.behavioralMetrics?.backtrackCount || 0;
    const accuracy = this.calculateSequenceAccuracy(data.targetWord, data.formedWord);
    const responseTime = data.responseTime || 0;
    
    if (accuracy > 0.9 && responseTime < 3000) strategies.push('confident_learner');
    if (backtrackCount > 0 && accuracy > 0.8) strategies.push('self_correcting');
    if (backtrackCount === 0 && accuracy < 0.5) strategies.push('impulsive_responder');
    if (responseTime > 5000 && accuracy > 0.7) strategies.push('careful_processor');
    if (backtrackCount > 3) strategies.push('trial_error_learner');
    
    return strategies.length > 0 ? strategies : ['typical_learner'];
  }

  // Métodos auxiliares
  countPhoneticErrors(target, formed) {
    // Implementação simplificada - conta substituições que não fazem sentido foneticamente
    let phoneticErrors = 0;
    const minLength = Math.min(target.length, formed.length);
    
    for (let i = 0; i < minLength; i++) {
      if (target[i] !== formed[i]) {
        // Verifica se é erro fonético plausível
        if (!this.isPhoneticSubstitution(target[i], formed[i])) {
          phoneticErrors++;
        }
      }
    }
    
    return phoneticErrors;
  }

  countDifferences(target, formed) {
    let differences = 0;
    const maxLength = Math.max(target.length, formed.length);
    
    for (let i = 0; i < maxLength; i++) {
      if ((target[i] || '') !== (formed[i] || '')) {
        differences++;
      }
    }
    
    return differences;
  }

  isPhoneticSubstitution(char1, char2) {
    const phoneticGroups = [
      ['a', 'e'], ['i', 'e'], ['o', 'u'],
      ['b', 'p'], ['d', 't'], ['g', 'k'],
      ['v', 'f'], ['z', 's'], ['m', 'n']
    ];
    
    return phoneticGroups.some(group => 
      group.includes(char1) && group.includes(char2)
    );
  }

  calculateLetterSimilarity(char1, char2) {
    const similarPairs = { 'b': ['d', 'p'], 'd': ['b', 'q'], 'p': ['q', 'b'], 'q': ['p', 'd'] };
    return similarPairs[char1]?.includes(char2) ? 0.8 : 0.1;
  }

  /**
   * 📊 Gera resumo da coleta
   */
  generateSummary() {
    if (this.collectedData.length === 0) return null;
    
    const totalCollections = this.collectedData.length;
    const perfectWords = this.collectedData.filter(d => d.isCorrect).length;
    const accuracy = perfectWords / totalCollections;
    
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / totalCollections;
    const avgComplexity = this.collectedData.reduce((sum, d) => sum + d.wordComplexity, 0) / totalCollections;
    const avgBacktracks = this.collectedData.reduce((sum, d) => sum + d.backtrackCount, 0) / totalCollections;
    
    return {
      collector: this.name,
      version: this.version,
      totalCollections,
      perfectWords,
      accuracy,
      averageResponseTime: avgResponseTime,
      averageComplexity: avgComplexity,
      averageBacktracks: avgBacktracks,
      skillProgression: this.analyzeSkillProgression(),
      constructionPatterns: this.analyzeConstructionPatterns(),
      recommendations: this.generateRecommendations()
    };
  }

  analyzeSkillProgression() {
    if (this.collectedData.length < 3) return null;
    
    const recentData = this.collectedData.slice(-5);
    const earlierData = this.collectedData.slice(0, 5);
    
    const recentAccuracy = recentData.filter(d => d.isCorrect).length / recentData.length;
    const earlierAccuracy = earlierData.filter(d => d.isCorrect).length / earlierData.length;
    
    const recentSpeed = recentData.reduce((sum, d) => sum + d.responseTime, 0) / recentData.length;
    const earlierSpeed = earlierData.reduce((sum, d) => sum + d.responseTime, 0) / earlierData.length;
    
    return {
      accuracyImprovement: recentAccuracy - earlierAccuracy,
      speedImprovement: earlierSpeed - recentSpeed, // Negativo = mais lento
      currentLevel: recentAccuracy,
      trend: recentAccuracy > earlierAccuracy ? 'improving' : 'stable'
    };
  }

  analyzeConstructionPatterns() {
    const strategies = {};
    this.collectedData.forEach(d => {
      d.constructionStrategy.forEach(strategy => {
        strategies[strategy] = (strategies[strategy] || 0) + 1;
      });
    });
    
    const errorTypes = {};
    this.collectedData.forEach(d => {
      if (d.errorType !== 'no_error') {
        errorTypes[d.errorType] = (errorTypes[d.errorType] || 0) + 1;
      }
    });
    
    return {
      preferredStrategies: strategies,
      commonErrors: errorTypes,
      averageWordLength: this.collectedData.reduce((sum, d) => sum + (d.targetWord?.length || 0), 0) / this.collectedData.length
    };
  }

  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    
    const recommendations = [];
    
    if (summary.accuracy < 0.6) {
      recommendations.push({
        type: 'word_accuracy',
        priority: 'high',
        message: 'Pratique com palavras mais simples para consolidar construção básica.'
      });
    }
    
    if (summary.averageBacktracks > 2) {
      recommendations.push({
        type: 'planning',
        priority: 'medium',
        message: 'Desenvolva estratégias de planejamento antes de começar a construir.'
      });
    }
    
    if (summary.averageResponseTime > 8000) {
      recommendations.push({
        type: 'speed',
        priority: 'low',
        message: 'Pratique construção de palavras familiares para aumentar fluência.'
      });
    }
    
    return recommendations;
  }

  reset() {
    this.collectedData = [];
  }

  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: new Date().toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
