/**
 * 🧠 COLOR MEMORY COLLECTOR
 * Coletor especializado em análise de memória cromática e sequencial para ColorMatch
 * Portal Betina V3 - FASE 2.1
 */

export class ColorMemoryCollector {
  constructor() {
    this.memoryTypes = {
      shortTerm: 'Memória de Curto Prazo',
      workingMemory: 'Memória de Trabalho',
      sequentialMemory: 'Memória Sequencial',
      visualMemory: 'Memória Visual',
      chromaticMemory: 'Memória Cromática'
    };
    
    this.memoryCapacities = {
      excellent: { min: 0.9, label: 'Excelente', span: '7+' },
      good: { min: 0.75, label: 'Boa', span: '5-6' },
      average: { min: 0.60, label: 'Mé<PERSON>', span: '3-4' },
      poor: { min: 0.40, label: 'Baixa', span: '2-3' },
      critical: { min: 0.0, label: 'Crí<PERSON>', span: '0-1' }
    };
    
    this.sequenceComplexity = {
      simple: { length: 2, description: 'Sequência simples' },
      moderate: { length: 3, description: 'Sequência moderada' },
      complex: { length: 4, description: 'Sequência complexa' },
      advanced: { length: 5, description: 'Sequência avançada' },
      expert: { length: 6, description: 'Sequência especializada' }
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Análise principal da memória cromática
   */
  async analyze(data) {
    try {
      if (!this.validateMemoryData(data)) {
        return this.generateFallbackAnalysis('Dados de memória insuficientes ou inválidos');
      }

      const memoryData = this.extractMemoryData(data);

      return {
        shortTermMemory: this.analyzeShortTermMemory(memoryData),
        workingMemory: this.analyzeWorkingMemory(memoryData),
        sequentialMemory: this.analyzeSequentialMemory(memoryData),
        visualMemory: this.analyzeVisualMemory(memoryData),
        chromaticMemory: this.analyzeChromaticMemory(memoryData),
        memoryCapacity: this.calculateMemoryCapacity(memoryData),
        retentionPatterns: this.analyzeRetentionPatterns(memoryData),
        interferenceEffects: this.analyzeInterferenceEffects(memoryData),
        memoryStrategies: this.identifyMemoryStrategies(memoryData),
        forgettingCurve: this.analyzeForgettingCurve(memoryData)
      };
    } catch (error) {
      console.error('Erro na análise de memória cromática:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de memória de entrada
   */
  validateMemoryData(data) {
    if (!data) return false;
    
    // Verificar se tem dados de memória ou sequências
    const hasMemoryData = data.memoryData || data.sequences || data.colorSequences;
    const hasInteractions = data.interactions || data.responses || data.selectedItems;
    
    return hasMemoryData || hasInteractions;
  }

  /**
   * Extrai dados de memória dos dados de entrada
   */
  extractMemoryData(data) {
    // Extrair sequências de cores
    let colorSequences = [];
    if (data.sequences) {
      colorSequences = data.sequences;
    } else if (data.colorSequences) {
      colorSequences = data.colorSequences;
    } else if (data.interactions) {
      // Reconstruir sequências a partir das interações
      colorSequences = this.reconstructSequencesFromInteractions(data.interactions);
    }

    // Extrair tentativas de recall
    let recallAttempts = [];
    if (data.recallAttempts) {
      recallAttempts = data.recallAttempts;
    } else if (data.responses) {
      recallAttempts = data.responses.filter(r => r.type === 'recall');
    }

    // Extrair dados temporais
    const temporalData = {
      exposureTime: data.exposureTime || 3000, // Tempo padrão de exposição
      recallDelay: data.recallDelay || 1000,   // Delay padrão para recall
      sessionDuration: data.sessionDuration || Date.now() - (data.startTime || Date.now())
    };

    return {
      colorSequences,
      recallAttempts,
      temporalData,
      difficulty: data.difficulty || 'medium',
      interactionHistory: data.interactions || []
    };
  }

  /**
   * Reconstrói sequências a partir das interações
   */
  reconstructSequencesFromInteractions(interactions) {
    const sequences = [];
    let currentSequence = [];
    
    interactions.forEach(interaction => {
      if (interaction.type === 'sequence_start' || interaction.isSequenceStart) {
        if (currentSequence.length > 0) {
          sequences.push([...currentSequence]);
        }
        currentSequence = [];
      }
      
      if (interaction.color || interaction.targetColor) {
        currentSequence.push({
          color: interaction.color || interaction.targetColor,
          timestamp: interaction.timestamp || Date.now(),
          correct: interaction.correct !== false
        });
      }
    });
    
    if (currentSequence.length > 0) {
      sequences.push(currentSequence);
    }
    
    return sequences;
  }

  /**
   * Analisa memória de curto prazo
   */
  analyzeShortTermMemory(memoryData) {
    const { colorSequences, recallAttempts } = memoryData;
    
    if (colorSequences.length === 0) {
      return this.getDefaultMemoryScore('shortTerm');
    }

    // Analisar sequências de 2-3 cores (típico para memória de curto prazo)
    const shortSequences = colorSequences.filter(seq => seq.length >= 2 && seq.length <= 3);
    
    if (shortSequences.length === 0) {
      return this.estimateFromAvailableData(colorSequences, 'shortTerm');
    }

    const accuracy = this.calculateSequenceAccuracy(shortSequences);
    const consistencyScore = this.calculateConsistency(shortSequences.map(seq => this.getSequenceScore(seq)));
    const speedScore = this.calculateRecallSpeed(shortSequences);

    return {
      accuracy,
      consistency: consistencyScore,
      speed: speedScore,
      capacity: this.estimateCapacity(shortSequences),
      overallScore: (accuracy * 0.4 + consistencyScore * 0.3 + speedScore * 0.3),
      level: this.categorizeMemoryLevel(accuracy),
      recommendations: this.generateShortTermRecommendations(accuracy, consistencyScore, speedScore)
    };
  }

  /**
   * Analisa memória de trabalho
   */
  analyzeWorkingMemory(memoryData) {
    const { colorSequences, temporalData } = memoryData;
    
    // Memória de trabalho envolve manipulação ativa da informação
    const complexSequences = colorSequences.filter(seq => 
      seq.length >= 3 || this.hasManipulationComponent(seq)
    );

    if (complexSequences.length === 0) {
      return this.getDefaultMemoryScore('workingMemory');
    }

    const manipulationAccuracy = this.calculateManipulationAccuracy(complexSequences);
    const dualTaskPerformance = this.assessDualTaskPerformance(memoryData);
    const interferenceResistance = this.calculateInterferenceResistance(complexSequences);

    return {
      manipulationAccuracy,
      dualTaskPerformance,
      interferenceResistance,
      capacity: this.estimateWorkingMemoryCapacity(complexSequences),
      overallScore: (manipulationAccuracy * 0.4 + dualTaskPerformance * 0.3 + interferenceResistance * 0.3),
      level: this.categorizeMemoryLevel(manipulationAccuracy),
      recommendations: this.generateWorkingMemoryRecommendations(manipulationAccuracy, dualTaskPerformance)
    };
  }

  /**
   * Analisa memória sequencial
   */
  analyzeSequentialMemory(memoryData) {
    const { colorSequences } = memoryData;
    
    if (colorSequences.length === 0) {
      return this.getDefaultMemoryScore('sequentialMemory');
    }

    const orderAccuracy = this.calculateOrderAccuracy(colorSequences);
    const sequenceLength = this.calculateAverageSequenceLength(colorSequences);
    const positionEffects = this.analyzePositionEffects(colorSequences);

    return {
      orderAccuracy,
      sequenceLength,
      positionEffects,
      serialPosition: this.analyzeSerialPositionEffect(colorSequences),
      overallScore: orderAccuracy,
      level: this.categorizeMemoryLevel(orderAccuracy),
      recommendations: this.generateSequentialRecommendations(orderAccuracy, positionEffects)
    };
  }

  /**
   * Analisa memória visual
   */
  analyzeVisualMemory(memoryData) {
    const { colorSequences, temporalData } = memoryData;
    
    const visualAccuracy = this.calculateVisualAccuracy(colorSequences);
    const spatialMemory = this.analyzeSpatialMemory(memoryData);
    const colorDiscrimination = this.analyzeColorDiscrimination(colorSequences);

    return {
      visualAccuracy,
      spatialMemory,
      colorDiscrimination,
      visualSpan: this.calculateVisualSpan(colorSequences),
      overallScore: (visualAccuracy * 0.4 + spatialMemory * 0.3 + colorDiscrimination * 0.3),
      level: this.categorizeMemoryLevel(visualAccuracy),
      recommendations: this.generateVisualMemoryRecommendations(visualAccuracy, spatialMemory)
    };
  }

  /**
   * Analisa memória cromática específica
   */
  analyzeChromaticMemory(memoryData) {
    const { colorSequences } = memoryData;
    
    const colorAccuracy = this.calculateColorAccuracy(colorSequences);
    const hueMemory = this.analyzeHueMemory(colorSequences);
    const saturationMemory = this.analyzeSaturationMemory(colorSequences);
    const brightnessMemory = this.analyzeBrightnessMemory(colorSequences);

    return {
      colorAccuracy,
      hueMemory,
      saturationMemory,
      brightnessMemory,
      colorCategories: this.analyzeColorCategoryMemory(colorSequences),
      overallScore: colorAccuracy,
      level: this.categorizeMemoryLevel(colorAccuracy),
      recommendations: this.generateChromaticRecommendations(colorAccuracy, hueMemory)
    };
  }

  /**
   * Calcula capacidade de memória
   */
  calculateMemoryCapacity(memoryData) {
    const { colorSequences } = memoryData;
    
    if (colorSequences.length === 0) {
      return { span: 3, capacity: 0.5, level: 'average' };
    }

    const longestCorrectSequence = Math.max(...colorSequences
      .filter(seq => this.getSequenceScore(seq) > 0.7)
      .map(seq => seq.length)
    );

    const averageSpan = colorSequences.reduce((sum, seq) => sum + seq.length, 0) / colorSequences.length;
    const capacityScore = Math.min(longestCorrectSequence / 6, 1); // Normalizado para 6 itens máximo

    return {
      span: Math.round(averageSpan),
      longestSpan: longestCorrectSequence,
      capacity: capacityScore,
      level: this.categorizeCapacityLevel(capacityScore)
    };
  }

  /**
   * Analisa padrões de retenção
   */
  analyzeRetentionPatterns(memoryData) {
    const { colorSequences, temporalData } = memoryData;
    
    const immediateRetention = this.calculateImmediateRetention(colorSequences);
    const delayedRetention = this.calculateDelayedRetention(colorSequences, temporalData);
    const retentionDecay = this.calculateRetentionDecay(immediateRetention, delayedRetention);

    return {
      immediateRetention,
      delayedRetention,
      retentionDecay,
      retentionStability: this.calculateRetentionStability(colorSequences),
      level: this.categorizeRetentionLevel(immediateRetention, delayedRetention)
    };
  }

  /**
   * Analisa efeitos de interferência
   */
  analyzeInterferenceEffects(memoryData) {
    const { colorSequences, interactionHistory } = memoryData;
    
    const proactiveInterference = this.calculateProactiveInterference(colorSequences);
    const retroactiveInterference = this.calculateRetroactiveInterference(colorSequences);
    const colorInterference = this.calculateColorInterference(colorSequences);

    return {
      proactiveInterference,
      retroactiveInterference,
      colorInterference,
      interferenceResistance: 1 - ((proactiveInterference + retroactiveInterference) / 2),
      level: this.categorizeInterferenceLevel(proactiveInterference, retroactiveInterference)
    };
  }

  /**
   * Identifica estratégias de memória
   */
  identifyMemoryStrategies(memoryData) {
    const { colorSequences, interactionHistory } = memoryData;
    
    const strategies = {
      rehearsal: this.detectRehearsalStrategy(interactionHistory),
      chunking: this.detectChunkingStrategy(colorSequences),
      visualization: this.detectVisualizationStrategy(colorSequences),
      association: this.detectAssociationStrategy(colorSequences),
      sequential: this.detectSequentialStrategy(colorSequences)
    };

    const dominantStrategy = this.identifyDominantStrategy(strategies);
    const strategyEffectiveness = this.assessStrategyEffectiveness(strategies, colorSequences);

    return {
      strategies,
      dominantStrategy,
      effectiveness: strategyEffectiveness,
      recommendations: this.generateStrategyRecommendations(strategies, strategyEffectiveness)
    };
  }

  /**
   * Analisa curva de esquecimento
   */
  analyzeForgettingCurve(memoryData) {
    const { colorSequences, temporalData } = memoryData;
    
    const timeIntervals = this.extractTimeIntervals(colorSequences);
    const retentionByTime = this.calculateRetentionByTime(colorSequences, timeIntervals);
    const forgettingRate = this.calculateForgettingRate(retentionByTime);

    return {
      timeIntervals,
      retentionByTime,
      forgettingRate,
      halfLife: this.calculateMemoryHalfLife(forgettingRate),
      curveType: this.identifyForgettingCurveType(retentionByTime)
    };
  }

  // Métodos auxiliares...

  /**
   * Calcula precisão da sequência
   */
  calculateSequenceAccuracy(sequences) {
    if (sequences.length === 0) return 0.5;
    
    const totalScore = sequences.reduce((sum, seq) => sum + this.getSequenceScore(seq), 0);
    return totalScore / sequences.length;
  }

  /**
   * Obtém score de uma sequência específica
   */
  getSequenceScore(sequence) {
    if (!sequence || sequence.length === 0) return 0;
    
    const correctItems = sequence.filter(item => item.correct !== false).length;
    return correctItems / sequence.length;
  }

  /**
   * Calcula velocidade de recall
   */
  calculateRecallSpeed(sequences) {
    const responseTimes = [];
    
    sequences.forEach(seq => {
      seq.forEach((item, index) => {
        if (index > 0 && item.timestamp && seq[index-1].timestamp) {
          responseTimes.push(item.timestamp - seq[index-1].timestamp);
        }
      });
    });

    if (responseTimes.length === 0) return 0.5;
    
    const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    
    // Normalizar: 1000ms = 1.0, 500ms = 0.8, 2000ms = 0.2
    return Math.max(0.1, Math.min(1.0, 1.5 - (avgResponseTime / 1000)));
  }

  /**
   * Estima capacidade da memória
   */
  estimateCapacity(sequences) {
    const avgLength = sequences.reduce((sum, seq) => sum + seq.length, 0) / sequences.length;
    const maxCorrectLength = Math.max(...sequences
      .filter(seq => this.getSequenceScore(seq) >= 0.8)
      .map(seq => seq.length)
    );
    
    return Math.min(maxCorrectLength || avgLength, 7); // Máximo típico de 7±2
  }

  /**
   * Verifica se sequência tem componente de manipulação
   */
  hasManipulationComponent(sequence) {
    // Detectar se a sequência requer manipulação (ex: reversão, reordenação)
    return sequence.some(item => 
      item.manipulation || 
      item.transform || 
      item.type === 'reverse' || 
      item.type === 'sort'
    );
  }

  /**
   * Calcula precisão de manipulação
   */
  calculateManipulationAccuracy(sequences) {
    const manipulationTasks = sequences.filter(seq => this.hasManipulationComponent(seq));
    
    if (manipulationTasks.length === 0) {
      // Estimar baseado na complexidade das sequências normais
      return this.calculateSequenceAccuracy(sequences) * 0.8; // Penalizar por falta de manipulação
    }
    
    return this.calculateSequenceAccuracy(manipulationTasks);
  }

  /**
   * Avalia performance em dual-task
   */
  assessDualTaskPerformance(memoryData) {
    // Simular baseado na complexidade e interferência
    const { colorSequences, temporalData } = memoryData;
    
    const complexityScore = this.calculateAverageComplexity(colorSequences);
    const interferenceScore = this.calculateInterferenceResistance(colorSequences);
    
    return (complexityScore + interferenceScore) / 2;
  }

  /**
   * Calcula resistência à interferência
   */
  calculateInterferenceResistance(sequences) {
    if (sequences.length < 2) return 0.5;
    
    const scores = sequences.map(seq => this.getSequenceScore(seq));
    const variance = this.calculateVariance(scores);
    
    // Baixa variância = alta resistência à interferência
    return Math.max(0.1, 1 - variance);
  }

  /**
   * Estima capacidade da memória de trabalho
   */
  estimateWorkingMemoryCapacity(sequences) {
    const avgComplexity = this.calculateAverageComplexity(sequences);
    const maxCorrectComplex = Math.max(...sequences
      .filter(seq => this.getSequenceScore(seq) >= 0.7 && this.hasManipulationComponent(seq))
      .map(seq => seq.length)
    );
    
    return Math.min(maxCorrectComplex || (avgComplexity * 0.8), 5); // WM típica 4±1
  }

  /**
   * Calcula precisão de ordem
   */
  calculateOrderAccuracy(sequences) {
    let totalOrderScore = 0;
    let orderTaskCount = 0;
    
    sequences.forEach(seq => {
      if (seq.length >= 2) {
        const orderScore = this.calculateSequenceOrderScore(seq);
        totalOrderScore += orderScore;
        orderTaskCount++;
      }
    });
    
    return orderTaskCount > 0 ? totalOrderScore / orderTaskCount : 0.5;
  }

  /**
   * Calcula score de ordem de uma sequência
   */
  calculateSequenceOrderScore(sequence) {
    let correctOrder = 0;
    
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i].position === i || sequence[i].order === i) {
        correctOrder++;
      }
    }
    
    return sequence.length > 1 ? correctOrder / (sequence.length - 1) : 0;
  }

  /**
   * Calcula comprimento médio da sequência
   */
  calculateAverageSequenceLength(sequences) {
    if (sequences.length === 0) return 3;
    
    return sequences.reduce((sum, seq) => sum + seq.length, 0) / sequences.length;
  }

  /**
   * Analisa efeitos de posição
   */
  analyzePositionEffects(sequences) {
    const positionScores = { first: [], middle: [], last: [] };
    
    sequences.forEach(seq => {
      seq.forEach((item, index) => {
        const position = index === 0 ? 'first' : 
                        index === seq.length - 1 ? 'last' : 'middle';
        positionScores[position].push(item.correct !== false ? 1 : 0);
      });
    });
    
    return {
      primacy: this.calculateAverage(positionScores.first),
      middle: this.calculateAverage(positionScores.middle),
      recency: this.calculateAverage(positionScores.last)
    };
  }

  /**
   * Analisa efeito de posição serial
   */
  analyzeSerialPositionEffect(sequences) {
    const allPositions = [];
    
    sequences.forEach(seq => {
      seq.forEach((item, index) => {
        if (!allPositions[index]) allPositions[index] = [];
        allPositions[index].push(item.correct !== false ? 1 : 0);
      });
    });
    
    return allPositions.map(positionScores => 
      positionScores.length > 0 ? this.calculateAverage(positionScores) : 0
    );
  }

  /**
   * Funções auxiliares
   */
  calculateAverage(array) {
    if (!array || array.length === 0) return 0;
    return array.reduce((sum, val) => sum + val, 0) / array.length;
  }

  calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = this.calculateAverage(values);
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    return this.calculateAverage(squaredDiffs);
  }

  calculateConsistency(values) {
    const variance = this.calculateVariance(values);
    return Math.max(0, 1 - variance);
  }

  /**
   * Categoriza nível de memória
   */
  categorizeMemoryLevel(score) {
    if (score >= 0.9) return 'excellent';
    if (score >= 0.75) return 'good';
    if (score >= 0.60) return 'average';
    if (score >= 0.40) return 'poor';
    return 'critical';
  }

  /**
   * Categoriza nível de capacidade
   */
  categorizeCapacityLevel(score) {
    return this.categorizeMemoryLevel(score);
  }

  /**
   * Categoriza nível de retenção
   */
  categorizeRetentionLevel(immediate, delayed) {
    const avgRetention = (immediate + delayed) / 2;
    return this.categorizeMemoryLevel(avgRetention);
  }

  /**
   * Categoriza nível de interferência
   */
  categorizeInterferenceLevel(proactive, retroactive) {
    const avgInterference = (proactive + retroactive) / 2;
    return this.categorizeMemoryLevel(1 - avgInterference);
  }

  /**
   * Gera score padrão de memória
   */
  getDefaultMemoryScore(type) {
    return {
      accuracy: 0.5,
      consistency: 0.5,
      speed: 0.5,
      capacity: 3,
      overallScore: 0.5,
      level: 'average',
      recommendations: [`Dados insuficientes para análise de ${this.memoryTypes[type]}`]
    };
  }

  /**
   * Estima dados a partir de dados disponíveis
   */
  estimateFromAvailableData(sequences, memoryType) {
    const generalAccuracy = this.calculateSequenceAccuracy(sequences);
    return {
      accuracy: generalAccuracy * 0.8, // Penalizar por estimativa
      consistency: this.calculateConsistency(sequences.map(seq => this.getSequenceScore(seq))),
      speed: 0.5, // Neutro sem dados específicos
      capacity: this.estimateCapacity(sequences),
      overallScore: generalAccuracy * 0.8,
      level: this.categorizeMemoryLevel(generalAccuracy * 0.8),
      recommendations: [`Análise estimada baseada em dados limitados para ${this.memoryTypes[memoryType]}`]
    };
  }

  /**
   * Gera recomendações específicas
   */
  generateShortTermRecommendations(accuracy, consistency, speed) {
    const recommendations = [];
    
    if (accuracy < 0.6) {
      recommendations.push('Exercitar com sequências menores (2-3 cores)');
      recommendations.push('Técnicas de repetição imediata');
    }
    
    if (consistency < 0.6) {
      recommendations.push('Praticar regularmente para maior estabilidade');
    }
    
    if (speed < 0.6) {
      recommendations.push('Exercícios de recall rápido');
    }
    
    return recommendations.length > 0 ? recommendations : ['Manter prática atual'];
  }

  generateWorkingMemoryRecommendations(manipulation, dualTask) {
    const recommendations = [];
    
    if (manipulation < 0.6) {
      recommendations.push('Exercícios de manipulação mental de cores');
      recommendations.push('Praticar reversão de sequências');
    }
    
    if (dualTask < 0.6) {
      recommendations.push('Treinar atenção dividida');
      recommendations.push('Exercícios dual-task progressivos');
    }
    
    return recommendations.length > 0 ? recommendations : ['Desenvolver estratégias de organização'];
  }

  generateSequentialRecommendations(accuracy, positionEffects) {
    const recommendations = [];
    
    if (accuracy < 0.6) {
      recommendations.push('Praticar sequências ordenadas');
      recommendations.push('Usar técnicas de chunking');
    }
    
    if (positionEffects.middle < positionEffects.first && positionEffects.middle < positionEffects.last) {
      recommendations.push('Focar na memorização dos itens do meio');
    }
    
    return recommendations.length > 0 ? recommendations : ['Manter foco na ordem sequencial'];
  }

  generateVisualMemoryRecommendations(visual, spatial) {
    const recommendations = [];
    
    if (visual < 0.6) {
      recommendations.push('Exercitar memória visual com cores vibrantes');
    }
    
    if (spatial < 0.6) {
      recommendations.push('Praticar localização espacial de cores');
    }
    
    return recommendations.length > 0 ? recommendations : ['Desenvolver estratégias visuais'];
  }

  generateChromaticRecommendations(accuracy, hue) {
    const recommendations = [];
    
    if (accuracy < 0.6) {
      recommendations.push('Treinar discriminação cromática');
    }
    
    if (hue < 0.6) {
      recommendations.push('Exercitar memória de matizes específicas');
    }
    
    return recommendations.length > 0 ? recommendations : ['Aprofundar conhecimento cromático'];
  }

  /**
   * Métodos adicionais para análises específicas
   */
  calculateAverageComplexity(sequences) {
    return sequences.reduce((sum, seq) => {
      const complexity = seq.length + (this.hasManipulationComponent(seq) ? 2 : 0);
      return sum + complexity;
    }, 0) / sequences.length / 7; // Normalizar por complexidade máxima estimada
  }

  calculateImmediateRetention(sequences) {
    // Simular retenção imediata baseada na precisão geral
    return this.calculateSequenceAccuracy(sequences);
  }

  calculateDelayedRetention(sequences, temporalData) {
    // Simular retenção com delay baseada na retenção imediata com decay
    const immediate = this.calculateImmediateRetention(sequences);
    const delayFactor = Math.max(0.5, 1 - (temporalData.recallDelay / 10000)); // Decay baseado no delay
    return immediate * delayFactor;
  }

  calculateRetentionDecay(immediate, delayed) {
    return Math.max(0, immediate - delayed);
  }

  calculateRetentionStability(sequences) {
    const scores = sequences.map(seq => this.getSequenceScore(seq));
    return this.calculateConsistency(scores);
  }

  calculateProactiveInterference(sequences) {
    // Simular interferência baseada na similaridade entre sequências consecutivas
    if (sequences.length < 2) return 0;
    
    let interferenceScore = 0;
    for (let i = 1; i < sequences.length; i++) {
      const similarity = this.calculateSequenceSimilarity(sequences[i-1], sequences[i]);
      if (similarity > 0.7) { // Alta similaridade pode causar interferência
        interferenceScore += similarity;
      }
    }
    
    return interferenceScore / (sequences.length - 1);
  }

  calculateRetroactiveInterference(sequences) {
    // Similar à interferência proativa, mas considerando efeito reverso
    return this.calculateProactiveInterference(sequences) * 0.8;
  }

  calculateColorInterference(sequences) {
    // Calcular interferência específica de cores similares
    let colorInterference = 0;
    let comparisons = 0;
    
    sequences.forEach(seq => {
      for (let i = 0; i < seq.length - 1; i++) {
        const colorSimilarity = this.calculateColorSimilarity(seq[i].color, seq[i+1].color);
        if (colorSimilarity > 0.8) {
          colorInterference += colorSimilarity;
          comparisons++;
        }
      }
    });
    
    return comparisons > 0 ? colorInterference / comparisons : 0;
  }

  calculateSequenceSimilarity(seq1, seq2) {
    if (!seq1 || !seq2 || seq1.length === 0 || seq2.length === 0) return 0;
    
    const minLength = Math.min(seq1.length, seq2.length);
    let matches = 0;
    
    for (let i = 0; i < minLength; i++) {
      if (seq1[i].color === seq2[i].color) {
        matches++;
      }
    }
    
    return matches / minLength;
  }

  calculateColorSimilarity(color1, color2) {
    // Implementação simplificada de similaridade de cor
    if (color1 === color2) return 1;
    
    // Cores da mesma família (red, green, blue, etc.)
    const color1Family = this.getColorFamily(color1);
    const color2Family = this.getColorFamily(color2);
    
    if (color1Family === color2Family) return 0.8;
    
    return 0.2; // Cores diferentes
  }

  getColorFamily(color) {
    const colorFamilies = {
      red: ['#ff0000', '#e91e63', '#f44336'],
      green: ['#00ff00', '#4caf50', '#8bc34a'],
      blue: ['#0000ff', '#2196f3', '#03a9f4'],
      yellow: ['#ffff00', '#ffc107', '#ffeb3b']
    };
    
    for (const [family, colors] of Object.entries(colorFamilies)) {
      if (colors.includes(color.toLowerCase())) {
        return family;
      }
    }
    
    return 'other';
  }

  // Métodos para análise de estratégias de memória
  detectRehearsalStrategy(interactions) {
    // Detectar padrões de repetição/ensaio
    return 0.5; // Implementação simplificada
  }

  detectChunkingStrategy(sequences) {
    // Detectar agrupamento de informações
    return 0.5; // Implementação simplificada
  }

  detectVisualizationStrategy(sequences) {
    // Detectar uso de estratégias visuais
    return 0.5; // Implementação simplificada
  }

  detectAssociationStrategy(sequences) {
    // Detectar uso de associações
    return 0.5; // Implementação simplificada
  }

  detectSequentialStrategy(sequences) {
    // Detectar estratégias sequenciais
    return 0.5; // Implementação simplificada
  }

  identifyDominantStrategy(strategies) {
    const strategyScores = Object.entries(strategies);
    strategyScores.sort((a, b) => b[1] - a[1]);
    return strategyScores[0][0];
  }

  assessStrategyEffectiveness(strategies, sequences) {
    const overallAccuracy = this.calculateSequenceAccuracy(sequences);
    const strategyCount = Object.values(strategies).filter(score => score > 0.6).length;
    
    return overallAccuracy * (1 + strategyCount * 0.1); // Bonus por múltiplas estratégias
  }

  generateStrategyRecommendations(strategies, effectiveness) {
    const recommendations = [];
    
    if (effectiveness < 0.6) {
      recommendations.push('Desenvolver estratégias de memorização mais eficazes');
    }
    
    if (strategies.chunking < 0.6) {
      recommendations.push('Praticar agrupamento de cores por categorias');
    }
    
    if (strategies.visualization < 0.6) {
      recommendations.push('Usar técnicas de visualização mental');
    }
    
    return recommendations.length > 0 ? recommendations : ['Continuar desenvolvendo estratégias atuais'];
  }

  // Métodos para análise da curva de esquecimento
  extractTimeIntervals(sequences) {
    const intervals = [];
    sequences.forEach(seq => {
      for (let i = 1; i < seq.length; i++) {
        if (seq[i].timestamp && seq[i-1].timestamp) {
          intervals.push(seq[i].timestamp - seq[i-1].timestamp);
        }
      }
    });
    return intervals;
  }

  calculateRetentionByTime(sequences, timeIntervals) {
    // Simplificação: calcular retenção em diferentes intervalos de tempo
    const timeRanges = [1000, 3000, 5000, 10000]; // ms
    const retentionByRange = {};
    
    timeRanges.forEach(range => {
      const sequencesInRange = sequences.filter(seq => {
        const avgTime = this.calculateAverageTime(seq);
        return avgTime <= range;
      });
      
      retentionByRange[range] = sequencesInRange.length > 0 ? 
        this.calculateSequenceAccuracy(sequencesInRange) : 0.5;
    });
    
    return retentionByRange;
  }

  calculateAverageTime(sequence) {
    if (sequence.length < 2) return 0;
    
    const times = [];
    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i].timestamp && sequence[i-1].timestamp) {
        times.push(sequence[i].timestamp - sequence[i-1].timestamp);
      }
    }
    
    return times.length > 0 ? this.calculateAverage(times) : 0;
  }

  calculateForgettingRate(retentionByTime) {
    const times = Object.keys(retentionByTime).map(Number).sort((a, b) => a - b);
    const retentions = times.map(time => retentionByTime[time]);
    
    if (times.length < 2) return 0;
    
    // Calcular taxa de declínio
    const initialRetention = retentions[0];
    const finalRetention = retentions[retentions.length - 1];
    const timeSpan = times[times.length - 1] - times[0];
    
    return timeSpan > 0 ? (initialRetention - finalRetention) / timeSpan : 0;
  }

  calculateMemoryHalfLife(forgettingRate) {
    // Tempo para que a retenção caia pela metade
    return forgettingRate > 0 ? 0.5 / forgettingRate : Infinity;
  }

  identifyForgettingCurveType(retentionByTime) {
    const retentions = Object.values(retentionByTime);
    
    if (retentions.length < 3) return 'insufficient_data';
    
    const isDecreasing = retentions.every((val, i) => i === 0 || val <= retentions[i-1]);
    const isStable = Math.max(...retentions) - Math.min(...retentions) < 0.1;
    
    if (isStable) return 'stable';
    if (isDecreasing) return 'exponential_decay';
    return 'irregular';
  }

  // Métodos específicos adicionais para análises cromáticas
  calculateVisualAccuracy(sequences) {
    // Análise específica da precisão visual
    return this.calculateSequenceAccuracy(sequences);
  }

  analyzeSpatialMemory(memoryData) {
    // Analisar componente espacial da memória
    return 0.5; // Implementação simplificada
  }

  analyzeColorDiscrimination(sequences) {
    // Analisar discriminação entre cores nas sequências
    return this.calculateSequenceAccuracy(sequences);
  }

  calculateVisualSpan(sequences) {
    // Calcular span visual específico
    return this.estimateCapacity(sequences);
  }

  calculateColorAccuracy(sequences) {
    return this.calculateSequenceAccuracy(sequences);
  }

  analyzeHueMemory(sequences) {
    // Análise específica da memória de matiz
    return 0.5; // Implementação simplificada
  }

  analyzeSaturationMemory(sequences) {
    // Análise específica da memória de saturação
    return 0.5; // Implementação simplificada
  }

  analyzeBrightnessMemory(sequences) {
    // Análise específica da memória de brilho
    return 0.5; // Implementação simplificada
  }

  analyzeColorCategoryMemory(sequences) {
    // Análise da memória por categoria de cor
    return 0.5; // Implementação simplificada
  }

  /**
   * Gera análise de fallback em caso de erro
   */
  generateFallbackAnalysis(errorMessage) {
    return {
      shortTermMemory: this.getDefaultMemoryScore('shortTerm'),
      workingMemory: this.getDefaultMemoryScore('workingMemory'),
      sequentialMemory: this.getDefaultMemoryScore('sequentialMemory'),
      visualMemory: this.getDefaultMemoryScore('visualMemory'),
      chromaticMemory: this.getDefaultMemoryScore('chromaticMemory'),
      memoryCapacity: { span: 3, capacity: 0.5, level: 'average' },
      retentionPatterns: { immediateRetention: 0.5, delayedRetention: 0.4, level: 'average' },
      interferenceEffects: { interferenceResistance: 0.5, level: 'average' },
      memoryStrategies: { dominantStrategy: 'unknown', effectiveness: 0.5 },
      forgettingCurve: { forgettingRate: 0.5, curveType: 'unknown' },
      error: true,
      errorMessage,
      recommendations: ['Coletar mais dados para análise precisa da memória cromática']
    };
  }
}

export default ColorMemoryCollector;
