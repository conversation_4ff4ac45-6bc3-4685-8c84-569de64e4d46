/**
 * Script para verificar se as correções resolveram os problemas
 * - Teste do logger.js
 * - Teste do AIBrainOrchestrator.js
 * - Verificação da configuração do Vite
 */

import { createLogger, logger } from './src/utils/logger.js';
import fs from 'fs';
import path from 'path';

// Testar o logger
console.log('\n===== TESTE DO LOGGER =====');
try {
  const testLogger = createLogger('test-script');
  testLogger.info('O logger está funcionando corretamente');
  logger.info('O logger default também está funcionando');
  console.log('✅ Logger testado com sucesso');
} catch (error) {
  console.error('❌ Erro ao testar o logger:', error);
  process.exit(1);
}

// Verificar configuração do vite
console.log('\n===== VERIFICAÇÃO DA CONFIGURAÇÃO DO VITE =====');
try {
  const viteConfigPath = path.join(process.cwd(), 'vite.config.js');
  const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
  
  if (viteConfig.includes('exclude-preview') && 
      viteConfig.includes('/preview/') && 
      viteConfig.includes('preview.*\\.html')) {
    console.log('✅ Configuração do Vite atualizada para ignorar arquivos de preview');
  } else {
    console.warn('⚠️ A configuração do Vite pode não estar ignorando corretamente arquivos de preview');
  }
} catch (error) {
  console.error('❌ Erro ao verificar configuração do Vite:', error);
}

console.log('\n===== RESUMO DAS CORREÇÕES =====');
console.log('1. Corrigido problema de exportação no logger.js:');
console.log('   - Removida exportação duplicada');
console.log('   - Reorganizada exportação para compatibilidade com ES modules');
console.log('   - Exportados createLogger, Logger, getLogger e logger padrão');
console.log('\n2. Corrigido erro de atribuição a constante em AIBrainOrchestrator.js:');
console.log('   - Alterado "const parentReport" para "let parentReport"');
console.log('\n3. Corrigido problema de build com arquivos de preview:');
console.log('   - Adicionado plugin para ignorar arquivos da pasta preview');
console.log('   - Adicionada regra de exclusão para arquivos HTML de preview');
console.log('\n⚠️ IMPORTANTE: É recomendado testar o sistema completo com:');
console.log('   npm run dev');
console.log('\nPara testar apenas o build do frontend:');
console.log('   npm run build');

console.log('\n✨ Testes de verificação concluídos!');
