import{j as e,P as o,a}from"./index-BIwBZl_j.js";import{r}from"./react-router-BtSsPy6x.js";import{C as i,a as s,b as n,c as t,d as l,e as d,p as m,f as c,i as u,g as b,A as p,h}from"./chart-core-CRFNBRsI.js";import{L as v,R as N,D as f,B as j}from"./chart-react-BbBAr11T.js";import{L as x}from"./NeuropedagogicalDashboard-CAsMfhwI.js";import"./react-BQG6_13O.js";import"./react-query-CDommIwN.js";import"./helmet-CSX2cyrn.js";import"./framer-motion-DA-GaQt2.js";import"./prop-types-D_3gT01v.js";r.createContext(null);const D={home:{welcome:"Bem-vindo ao Portal Betina! Aqui você encontra atividades terapêuticas divertidas e educativas.",popularActivities:"Atividades Mais Populares",toolsUtilities:"Ferramentas e Utilitários",footerNavigation:"Use a navegação inferior para acessar diferentes seções"},games:{letterRecognition:{title:"Reconhecimento de Letras",description:"Aprenda o alfabeto de forma divertida e interativa",instruction:"Clique na letra que você ouvir ou ver na tela",difficultyEasy:"Nível fácil com letras maiúsculas",difficultyMedium:"Nível médio com letras minúsculas",difficultyHard:"Nível difícil com letras mistas",correct:"Muito bem! Resposta correta!",incorrect:"Tente novamente! Você consegue!",gameComplete:"Parabéns! Você completou o jogo!"},numberCounting:{title:"Contagem de Números",description:"Pratique contagem e reconhecimento numérico",instruction:"Conte os objetos na tela e clique no número correto",difficultyEasy:"Conte de 1 a 5",difficultyMedium:"Conte de 1 a 10",difficultyHard:"Conte de 1 a 15",correct:"Excelente! Contagem correta!",incorrect:"Conte novamente com cuidado!",gameComplete:"Fantástico! Você domina a contagem!"},musicalSequence:{title:"Sequência Musical",description:"Repita sequências sonoras e desenvolva a memória auditiva",instruction:"Ouça a sequência musical e repita clicando nos botões na ordem correta",difficultyEasy:"Sequências curtas de 2 a 3 notas",difficultyMedium:"Sequências médias de 4 a 5 notas",difficultyHard:"Sequências longas de 6 a 8 notas",listenCarefully:"Ouça com atenção a sequência musical",repeatSequence:"Agora repita a sequência",correct:"Perfeito! Sequência correta!",incorrect:"Ouça novamente e tente repetir",gameComplete:"Incrível! Sua memória auditiva está excelente!"},memoryGame:{title:"Jogo da Memória",description:"Encontre os pares e exercite sua memória",instruction:"Clique nas cartas para encontrar os pares iguais",difficultyEasy:"6 cartas - 3 pares",difficultyMedium:"12 cartas - 6 pares",difficultyHard:"20 cartas - 10 pares",cardFlipped:"Carta virada",pairFound:"Par encontrado! Muito bem!",tryAgain:"Não é um par. Tente novamente!",gameComplete:"Parabéns! Todos os pares foram encontrados!"},colorMatch:{title:"Combinação de Cores",description:"Combine cores e desenvolva percepção visual",instruction:"Clique na cor que combina com o exemplo mostrado",difficultyEasy:"Cores básicas e vibrantes",difficultyMedium:"Cores intermediárias",difficultyHard:"Tons e nuances sutis",correct:"Cor correta! Excelente percepção!",incorrect:"Observe melhor as cores e tente novamente",gameComplete:"Perfeito! Você domina as cores!"},imageAssociation:{title:"Associação de Imagens",description:"Associe imagens e desenvolva conexões cognitivas",instruction:"Encontre a imagem que se relaciona com a imagem principal",difficultyEasy:"Associações simples e diretas",difficultyMedium:"Associações por categoria",difficultyHard:"Associações conceituais",correct:"Associação correta! Muito inteligente!",incorrect:"Pense na relação entre as imagens",gameComplete:"Fantástico! Você fez todas as associações!"},creativePainting:{title:"Pintura Criativa",description:"Desenvolva criatividade através da arte digital",instruction:"Use as ferramentas de pintura para criar sua obra de arte",brushTool:"Pincel selecionado",eraserTool:"Borracha selecionada",colorSelected:"Cor selecionada",canvasClear:"Tela limpa para nova criação",artworkSaved:"Sua obra de arte foi salva!"}},tools:{dashboard:{title:"Dashboard do Sistema",description:"Monitore performance e estatísticas em tempo real",loading:"Carregando dados do dashboard"},userProfiles:{title:"Perfis de Usuário",description:"Gerencie perfis e progresso dos usuários",loading:"Carregando perfis de usuário"},backup:{title:"Backup e Exportação",description:"Faça backup e exporte dados do sistema",loading:"Preparando ferramentas de backup"},performance:{title:"Performance",description:"Analise métricas de desempenho e uso",loading:"Analisando dados de performance"},accessibility:{title:"Configurações de Acessibilidade",description:"Configure opções para melhor experiência",audioNarration:"Ativação de leitura em voz alta e feedback sonoro",highContrast:"Melhora a visibilidade com cores mais contrastantes",simplifiedNavigation:"Interface otimizada para diferentes necessidades",reducedAnimations:"Diminui movimentos que podem causar desconforto",comingSoon:"As configurações detalhadas serão implementadas em breve"}},navigation:{backToMenu:"Voltar ao menu principal",selectDifficulty:"Selecione o nível de dificuldade",startGame:"Iniciar jogo",pauseGame:"Pausar jogo",resumeGame:"Continuar jogo",restartGame:"Reiniciar jogo",nextLevel:"Próximo nível",menuButton:"Menu",settingsButton:"Configurações"},feedback:{loading:"Carregando, aguarde um momento",error:"Ops! Algo deu errado. Tente novamente",success:"Operação realizada com sucesso",wellDone:"Muito bem!",tryAgain:"Tente novamente",excellent:"Excelente!",perfect:"Perfeito!",keepGoing:"Continue assim!",almostThere:"Quase lá!"},accessibility:{buttonPress:"Botão pressionado",menuOpen:"Menu aberto",menuClose:"Menu fechado",gameStart:"Jogo iniciado",gameEnd:"Jogo finalizado",newLevel:"Novo nível desbloqueado",achievement:"Conquista desbloqueada"}},g=(e,o,a=null)=>{try{return a?D[e]?.[o]?.[a]||"":D[e]?.[o]||""}catch(r){return""}},A={ttsButton:"_ttsButton_1lc8s_15",speaking:"_speaking_1lc8s_81",pulse:"_pulse_1lc8s_1",paused:"_paused_1lc8s_91",pausePulse:"_pausePulse_1lc8s_1",disabled:"_disabled_1lc8s_101",ttsIcon:"_ttsIcon_1lc8s_139",ttsText:"_ttsText_1lc8s_151",small:"_small_1lc8s_163",medium:"_medium_1lc8s_177",large:"_large_1lc8s_191",highContrast:"_highContrast_1lc8s_305",reducedMotion:"_reducedMotion_1lc8s_327"},P=({text:o="",voice:a=null,rate:i=1,pitch:s=1,volume:n=1,size:t="medium",ariaLabel:l=null,onStart:d=null,onEnd:m=null,onError:c=null,onPause:u=null,onResume:b=null,showText:p=!1,className:h="",autoPlay:v=!1,pauseEnabled:N=!0,voiceFilter:f="pt-BR"})=>{const[j,x]=r.useState(!1),[D,g]=r.useState(!1),[P,C]=r.useState([]),[R,E]=r.useState(null),V=r.useRef(null),y=r.useRef(window.speechSynthesis),k=r.useMemo(()=>"undefined"!=typeof window&&"speechSynthesis"in window,[]),w=r.useMemo(()=>P.filter(e=>e.lang.includes(f)||e.name.toLowerCase().includes(f.toLowerCase())||e.name.toLowerCase().includes("portuguese")||e.name.toLowerCase().includes("brasil")),[P,f]),M=r.useMemo(()=>a||w[0]||null,[a,w]);r.useEffect(()=>{if(!k)return;const e=()=>{const e=y.current.getVoices();C(e)};return e(),y.current.onvoiceschanged=e,()=>{y.current.onvoiceschanged=null}},[k]),r.useEffect(()=>()=>{k&&y.current.cancel()},[k]),r.useEffect(()=>{if(v&&o.trim()&&!j&&!R){const e=setTimeout(()=>{if(!j&&k&&o.trim()){y.current.cancel(),E(null),x(!0),g(!1);const e=new SpeechSynthesisUtterance(o.trim());e.rate=Math.max(.1,Math.min(i,2)),e.pitch=Math.max(0,Math.min(s,2)),e.volume=Math.max(0,Math.min(n,1)),e.lang=f,e.voice=M,e.onstart=()=>{x(!0),d?.(e)},e.onend=()=>{x(!1),g(!1),m?.(e)},e.onpause=()=>{g(!0),u?.(e)},e.onresume=()=>{g(!1),b?.(e)},e.onerror=e=>{x(!1),g(!1);const o=`TTS Error: ${e.error}`;E(o),c?.(e)},V.current=e,y.current.speak(e)}},100);return()=>clearTimeout(e)}},[o,v,j,R,k,i,s,n,f,M,d,m,u,b,c]);const S=r.useCallback(()=>{if(!k||!o.trim()||j)return;y.current.cancel(),E(null),x(!0),g(!1);const e=new SpeechSynthesisUtterance(o.trim());e.rate=Math.max(.1,Math.min(i,2)),e.pitch=Math.max(0,Math.min(s,2)),e.volume=Math.max(0,Math.min(n,1)),e.lang=f,e.voice=M,e.onstart=()=>{x(!0),d?.(e)},e.onend=()=>{x(!1),g(!1),m?.(e)},e.onpause=()=>{g(!0),u?.(e)},e.onresume=()=>{g(!1),b?.(e)},e.onerror=e=>{x(!1),g(!1);const o=`TTS Error: ${e.error}`;E(o),c?.(e)},V.current=e,y.current.speak(e)},[o,i,s,n,k,M,f,j,d,m,c,u,b]),T=r.useCallback(()=>{k&&j&&!D&&(y.current.pause(),g(!0),u?.(V.current))},[k,j,D,u]),G=r.useCallback(()=>{k&&j&&D&&(y.current.resume(),g(!1),b?.(V.current))},[k,j,D,b]);r.useCallback(()=>{k&&(j||D)&&(y.current.cancel(),x(!1),g(!1))},[k,j,D]);const B=r.useCallback(()=>{j&&N&&!D?T():D?G():S()},[j,D,N,S,T,G]),_=r.useCallback(()=>{if(l)return l;return`${D?"Retomar":j&&N?"Pausar":"Ouvir"} texto: ${o.length>50?`${o.substring(0,50)}...`:o}`},[l,j,D,N,o]);if(!k)return null;const z=[A.ttsButton,A[t],j?A.speaking:"",D?A.paused:"",R?A.disabled:"",h].filter(Boolean).join(" ");return e.jsxDEV("button",{className:z,onClick:B,disabled:!o.trim()||R,"aria-label":_(),title:D?"Retomar leitura":j&&N?"Pausar leitura":"Ouvir texto",role:"button","aria-pressed":j||D,type:"button",children:[e.jsxDEV("span",{className:A.ttsIcon,children:D?"▶️":j?"⏸️":"🔊"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/TextToSpeech.jsx",lineNumber:255,columnNumber:7},void 0),p&&e.jsxDEV("span",{className:A.ttsText,children:D?"Retomar":j&&N?"Pausar":"Ouvir"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/common/TextToSpeech.jsx",lineNumber:259,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/common/TextToSpeech.jsx",lineNumber:245,columnNumber:5},void 0)};P.propTypes={text:o.string,voice:o.object,rate:o.number,pitch:o.number,volume:o.number,size:o.oneOf(["small","medium","large"]),ariaLabel:o.string,onStart:o.func,onEnd:o.func,onError:o.func,onPause:o.func,onResume:o.func,showText:o.bool,className:o.string,autoPlay:o.bool,pauseEnabled:o.bool,voiceFilter:o.string},P.defaultProps={text:"",voice:null,rate:1,pitch:1,volume:1,size:"medium",ariaLabel:null,onStart:null,onEnd:null,onError:null,onPause:null,onResume:null,showText:!1,className:"",autoPlay:!1,pauseEnabled:!0,voiceFilter:"pt-BR"};const C=({children:o,feature:i,title:s="Recurso Premium",description:n="Este recurso está disponível apenas para usuários premium.",showUpgrade:t=!0})=>{const{isPremium:l,hasFeatureAccess:d,canAccessDashboard:m,availablePlans:c,upgradeToPremium:u}=a(),[b,p]=r.useState(!1);if("dashboard"===i?m():d(i))return o;return e.jsxDEV("div",{className:"premium-gate-container",children:e.jsxDEV("div",{className:"premium-gate-content",children:[e.jsxDEV("div",{className:"premium-gate-header",children:[e.jsxDEV("div",{className:"premium-gate-icon",children:e.jsxDEV("span",{style:{fontSize:"3rem"},children:"💎"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:46,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:45,columnNumber:11},void 0),e.jsxDEV("h2",{className:"premium-gate-title",children:s},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:48,columnNumber:11},void 0),e.jsxDEV(P,{text:g("system","premiumFeature"),size:"small"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:49,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:44,columnNumber:9},void 0),e.jsxDEV("div",{className:"premium-gate-description",children:e.jsxDEV("p",{children:n},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:56,columnNumber:11},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:55,columnNumber:9},void 0),t&&e.jsxDEV("div",{className:"premium-gate-actions",children:e.jsxDEV("button",{className:"premium-upgrade-button",onClick:()=>p(!b),children:[e.jsxDEV("span",{children:"🚀"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:65,columnNumber:15},void 0),"Ver Planos Premium"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:61,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:60,columnNumber:11},void 0),b&&e.jsxDEV("div",{className:"premium-plans-modal",children:[e.jsxDEV("div",{className:"premium-plans-overlay",onClick:()=>p(!1)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:73,columnNumber:13},void 0),e.jsxDEV("div",{className:"premium-plans-content",children:[e.jsxDEV("div",{className:"premium-plans-header",children:[e.jsxDEV("h3",{children:"Escolha seu Plano"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:76,columnNumber:17},void 0),e.jsxDEV("button",{className:"premium-plans-close",onClick:()=>p(!1),children:"✕"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:77,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:75,columnNumber:15},void 0),e.jsxDEV("div",{className:"premium-plans-grid",children:Object.entries(c).map(([o,a])=>e.jsxDEV("div",{className:"premium-plan-card "+(a.popular?"popular":""),children:[a.popular&&e.jsxDEV("div",{className:"plan-badge",children:"Mais Popular"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:92,columnNumber:23},void 0),e.jsxDEV("div",{className:"plan-header",children:[e.jsxDEV("h4",{children:a.name},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:96,columnNumber:23},void 0),e.jsxDEV("div",{className:"plan-price",children:[a.price,e.jsxDEV("span",{className:"plan-period",children:a.period},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:99,columnNumber:25},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:97,columnNumber:23},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:95,columnNumber:21},void 0),e.jsxDEV("div",{className:"plan-features",children:[a.features.map((o,a)=>e.jsxDEV("div",{className:"plan-feature",children:["✅ ",o]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:105,columnNumber:25},void 0)),a.limitations&&a.limitations.map((o,a)=>e.jsxDEV("div",{className:"plan-limitation",children:["❌ ",o]},a,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:110,columnNumber:25},void 0))]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:103,columnNumber:21},void 0),e.jsxDEV("button",{className:"plan-button "+("premium"===o?"primary":"secondary"),onClick:()=>(e=>{u(e),p(!1);const o=new Audio;o.src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTuVgGk1",o.play().catch(()=>{})})(o),disabled:"free"===o,children:"free"===o?"Plano Atual":"Escolher Plano"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:116,columnNumber:21},void 0)]},o,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:87,columnNumber:19},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:85,columnNumber:15},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:74,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:72,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:43,columnNumber:7},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/Premium/PremiumGate.jsx",lineNumber:42,columnNumber:5},void 0)};i.register(s,n,t,l,d,m,c,u,b,p,h);const R=()=>{const{user:o,isPremium:i}=a(),[s,n]=r.useState(!0),[t,l]=r.useState(null),[d,m]=r.useState("monthly"),[c,u]=r.useState("all"),b=r.useMemo(()=>({summary:{totalSessions:2847,averageScore:87.3,improvementRate:15.7,completionRate:92.1,totalTime:"847h 23m",streakDays:45},progressData:{labels:["Jan","Fev","Mar","Abr","Mai","Jun"],datasets:[{label:"Pontuação Média",data:[65,70,75,80,85,87],borderColor:"#2563EB",backgroundColor:"rgba(37, 99, 235, 0.1)",tension:.4,fill:!0},{label:"Taxa de Conclusão",data:[80,82,85,88,90,92],borderColor:"#059669",backgroundColor:"rgba(5, 150, 105, 0.1)",tension:.4,fill:!0}]},skillsRadar:{labels:["Matemática","Linguagem","Lógica","Memória","Atenção","Criatividade"],datasets:[{label:"Atual",data:[85,90,78,82,88,95],backgroundColor:"rgba(37, 99, 235, 0.2)",borderColor:"#2563EB",borderWidth:2},{label:"Meta",data:[90,95,85,90,95,98],backgroundColor:"rgba(219, 39, 119, 0.2)",borderColor:"#DB2777",borderWidth:2}]},gameDistribution:{labels:["Jogos de Memória","Matemática","Lógica","Linguagem","Criatividade"],datasets:[{data:[25,22,18,20,15],backgroundColor:["#2563EB","#059669","#DB2777","#EA580C","#6D28D9"],borderWidth:2,borderColor:"#ffffff"}]},weeklyActivity:{labels:["Seg","Ter","Qua","Qui","Sex","Sáb","Dom"],datasets:[{label:"Minutos Jogados",data:[45,52,38,47,55,62,35],backgroundColor:"#2563EB",borderRadius:8}]},achievements:[{id:1,title:"Sequência de 30 dias",icon:"fas fa-fire",color:"#EA580C",completed:!0},{id:2,title:"Mestre da Matemática",icon:"fas fa-calculator",color:"#2563EB",completed:!0},{id:3,title:"Memória de Elefante",icon:"fas fa-brain",color:"#DB2777",completed:!0},{id:4,title:"Lógica Avançada",icon:"fas fa-puzzle-piece",color:"#059669",completed:!1},{id:5,title:"Criativo Nato",icon:"fas fa-palette",color:"#6D28D9",completed:!1}],recommendations:[{type:"focus",title:"Melhorar Lógica",description:"Concentre-se nos jogos de lógica para atingir a meta de 85%",priority:"high"},{type:"maintain",title:"Manter Criatividade",description:"Continue explorando jogos criativos, você está indo muito bem!",priority:"medium"},{type:"schedule",title:"Horário Consistente",description:"Tente manter uma rotina de estudos mais regular durante a semana",priority:"low"}]}),[]),p={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#ffffff",font:{size:12}}}},scales:{y:{beginAtZero:!0,grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#ffffff"}},x:{grid:{color:"rgba(255, 255, 255, 0.1)"},ticks:{color:"#ffffff"}}}},h=e=>{switch(e){case"high":return"#DC2626";case"medium":return"#EA580C";case"low":return"#059669";default:return"#6B7280"}},D=e=>{switch(e){case"focus":return"fas fa-bullseye";case"maintain":return"fas fa-check-circle";case"schedule":return"fas fa-calendar-alt";default:return"fas fa-lightbulb"}};return r.useEffect(()=>{i&&(async()=>{n(!0),setTimeout(()=>{l(b),n(!1)},1500)})()},[i,d,c,b]),i?s?e.jsxDEV(x,{message:"Gerando relatório avançado..."},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:269,columnNumber:12},void 0):e.jsxDEV("div",{className:"relatorio-dashboard",children:[e.jsxDEV("div",{className:"dashboard-header",children:[e.jsxDEV("h1",{className:"dashboard-title",children:[e.jsxDEV("i",{className:"fas fa-chart-line"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:276,columnNumber:11},void 0),"Relatório Avançado"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:275,columnNumber:9},void 0),e.jsxDEV("p",{className:"dashboard-subtitle",children:"Análise completa do progresso e desempenho personalizado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:279,columnNumber:9},void 0),e.jsxDEV("div",{className:"dashboard-controls",children:[e.jsxDEV("select",{value:d,onChange:e=>m(e.target.value),className:"control-select",children:[e.jsxDEV("option",{value:"weekly",children:"Última Semana"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:289,columnNumber:13},void 0),e.jsxDEV("option",{value:"monthly",children:"Último Mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:290,columnNumber:13},void 0),e.jsxDEV("option",{value:"quarterly",children:"Último Trimestre"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:291,columnNumber:13},void 0),e.jsxDEV("option",{value:"yearly",children:"Último Ano"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:292,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:284,columnNumber:11},void 0),e.jsxDEV("select",{value:c,onChange:e=>u(e.target.value),className:"control-select",children:[e.jsxDEV("option",{value:"all",children:"Todas as Métricas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:300,columnNumber:13},void 0),e.jsxDEV("option",{value:"cognitive",children:"Habilidades Cognitivas"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:301,columnNumber:13},void 0),e.jsxDEV("option",{value:"academic",children:"Desempenho Acadêmico"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:302,columnNumber:13},void 0),e.jsxDEV("option",{value:"social",children:"Habilidades Sociais"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:303,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:295,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:283,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:274,columnNumber:7},void 0),e.jsxDEV("div",{className:"summary-cards",children:[e.jsxDEV("div",{className:"summary-card",children:[e.jsxDEV("div",{className:"card-icon",children:e.jsxDEV("i",{className:"fas fa-play-circle"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:311,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:310,columnNumber:11},void 0),e.jsxDEV("div",{className:"card-content",children:[e.jsxDEV("h3",{children:"Total de Sessões"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:314,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-value",children:t?.summary.totalSessions},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:315,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-change positive",children:"+12% este mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:316,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:313,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:309,columnNumber:9},void 0),e.jsxDEV("div",{className:"summary-card",children:[e.jsxDEV("div",{className:"card-icon",children:e.jsxDEV("i",{className:"fas fa-star"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:322,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:321,columnNumber:11},void 0),e.jsxDEV("div",{className:"card-content",children:[e.jsxDEV("h3",{children:"Pontuação Média"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:325,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-value",children:[t?.summary.averageScore,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:326,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-change positive",children:"+5.3% este mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:327,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:324,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:320,columnNumber:9},void 0),e.jsxDEV("div",{className:"summary-card",children:[e.jsxDEV("div",{className:"card-icon",children:e.jsxDEV("i",{className:"fas fa-arrow-up"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:333,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:332,columnNumber:11},void 0),e.jsxDEV("div",{className:"card-content",children:[e.jsxDEV("h3",{children:"Taxa de Melhoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:336,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-value",children:[t?.summary.improvementRate,"%"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:337,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-change positive",children:"+2.1% este mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:338,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:335,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:331,columnNumber:9},void 0),e.jsxDEV("div",{className:"summary-card",children:[e.jsxDEV("div",{className:"card-icon",children:e.jsxDEV("i",{className:"fas fa-clock"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:344,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:343,columnNumber:11},void 0),e.jsxDEV("div",{className:"card-content",children:[e.jsxDEV("h3",{children:"Tempo Total"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:347,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-value",children:t?.summary.totalTime},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:348,columnNumber:13},void 0),e.jsxDEV("div",{className:"card-change positive",children:"+45h este mês"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:349,columnNumber:13},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:346,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:342,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:308,columnNumber:7},void 0),e.jsxDEV("div",{className:"charts-section",children:[e.jsxDEV("div",{className:"chart-container large",children:[e.jsxDEV("h3",{className:"chart-title",children:"Progresso ao Longo do Tempo"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:356,columnNumber:11},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(v,{data:t?.progressData,options:p},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:358,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:357,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:355,columnNumber:9},void 0),e.jsxDEV("div",{className:"chart-container",children:[e.jsxDEV("h3",{className:"chart-title",children:"Perfil de Habilidades"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:363,columnNumber:11},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(N,{data:t?.skillsRadar,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"top",labels:{color:"#ffffff",font:{size:12}}}},scales:{r:{beginAtZero:!0,max:100,grid:{color:"rgba(255, 255, 255, 0.2)"},angleLines:{color:"rgba(255, 255, 255, 0.2)"},pointLabels:{color:"#ffffff",font:{size:11}},ticks:{color:"#ffffff",backdropColor:"transparent"}}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:365,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:364,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:362,columnNumber:9},void 0),e.jsxDEV("div",{className:"chart-container",children:[e.jsxDEV("h3",{className:"chart-title",children:"Distribuição por Categoria"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:370,columnNumber:11},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(f,{data:t?.gameDistribution,options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right",labels:{color:"#ffffff",font:{size:11},usePointStyle:!0}}}}},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:372,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:371,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:369,columnNumber:9},void 0),e.jsxDEV("div",{className:"chart-container",children:[e.jsxDEV("h3",{className:"chart-title",children:"Atividade Semanal"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:377,columnNumber:11},void 0),e.jsxDEV("div",{className:"chart-wrapper",children:e.jsxDEV(j,{data:t?.weeklyActivity,options:p},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:379,columnNumber:13},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:378,columnNumber:11},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:376,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:354,columnNumber:7},void 0),e.jsxDEV("div",{className:"achievements-section",children:[e.jsxDEV("h3",{className:"section-title",children:[e.jsxDEV("i",{className:"fas fa-trophy"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:386,columnNumber:11},void 0),"Conquistas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:385,columnNumber:9},void 0),e.jsxDEV("div",{className:"achievements-grid",children:t?.achievements.map(o=>e.jsxDEV("div",{className:"achievement-card "+(o.completed?"completed":"locked"),children:[e.jsxDEV("div",{className:"achievement-icon",style:{color:o.completed?o.color:"#6B7280"},children:e.jsxDEV("i",{className:o.icon},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:399,columnNumber:17},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:395,columnNumber:15},void 0),e.jsxDEV("div",{className:"achievement-title",children:o.title},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:401,columnNumber:15},void 0),o.completed&&e.jsxDEV("div",{className:"achievement-badge",children:e.jsxDEV("i",{className:"fas fa-check"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:404,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:403,columnNumber:17},void 0)]},o.id,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:391,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:389,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:384,columnNumber:7},void 0),e.jsxDEV("div",{className:"recommendations-section",children:[e.jsxDEV("h3",{className:"section-title",children:[e.jsxDEV("i",{className:"fas fa-lightbulb"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:414,columnNumber:11},void 0),"Recomendações Personalizadas"]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:413,columnNumber:9},void 0),e.jsxDEV("div",{className:"recommendations-list",children:t?.recommendations.map((o,a)=>e.jsxDEV("div",{className:"recommendation-card",children:e.jsxDEV("div",{className:"recommendation-header",children:[e.jsxDEV("div",{className:"recommendation-icon",children:e.jsxDEV("i",{className:D(o.type)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:422,columnNumber:19},void 0)},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:421,columnNumber:17},void 0),e.jsxDEV("div",{className:"recommendation-info",children:[e.jsxDEV("h4",{className:"recommendation-title",children:o.title},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:425,columnNumber:19},void 0),e.jsxDEV("p",{className:"recommendation-description",children:o.description},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:426,columnNumber:19},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:424,columnNumber:17},void 0),e.jsxDEV("div",{className:"recommendation-priority",style:{backgroundColor:h(o.priority)},children:o.priority},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:428,columnNumber:17},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:420,columnNumber:15},void 0)},a,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:419,columnNumber:13},void 0))},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:417,columnNumber:9},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:412,columnNumber:7},void 0),e.jsxDEV("style",{children:"\n        .relatorio-dashboard {\n          padding: 2rem;\n          background: #000000;\n          min-height: 100vh;\n          color: white;\n        }\n\n        .dashboard-header {\n          text-align: center;\n          margin-bottom: 2rem;\n          padding-bottom: 1rem;\n          border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .dashboard-title {\n          font-size: 2.5rem;\n          margin-bottom: 0.5rem;\n          background: linear-gradient(135deg, #2563EB, #6D28D9);\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          background-clip: text;\n        }\n\n        .dashboard-title i {\n          margin-right: 1rem;\n          background: linear-gradient(135deg, #2563EB, #6D28D9);\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n          background-clip: text;\n        }\n\n        .dashboard-subtitle {\n          font-size: 1.1rem;\n          color: rgba(255, 255, 255, 0.7);\n          margin-bottom: 1.5rem;\n        }\n\n        .dashboard-controls {\n          display: flex;\n          justify-content: center;\n          gap: 1rem;\n          flex-wrap: wrap;\n        }\n\n        .control-select {\n          background: rgba(255, 255, 255, 0.1);\n          border: 1px solid rgba(255, 255, 255, 0.2);\n          border-radius: 0.5rem;\n          padding: 0.5rem 1rem;\n          color: white;\n          font-size: 0.9rem;\n        }\n\n        .control-select:focus {\n          outline: none;\n          border-color: #2563EB;\n          box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);\n        }\n\n        .summary-cards {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n          gap: 1.5rem;\n          margin-bottom: 2rem;\n        }\n\n        .summary-card {\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 1rem;\n          padding: 1.5rem;\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n          border: 1px solid rgba(255, 255, 255, 0.1);\n          transition: all 0.3s ease;\n        }\n\n        .summary-card:hover {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n          border-color: rgba(255, 255, 255, 0.2);\n        }\n\n        .card-icon {\n          width: 50px;\n          height: 50px;\n          background: linear-gradient(135deg, #2563EB, #6D28D9);\n          border-radius: 12px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 1.5rem;\n          color: white;\n        }\n\n        .card-content h3 {\n          margin: 0 0 0.5rem 0;\n          font-size: 0.9rem;\n          color: rgba(255, 255, 255, 0.7);\n          text-transform: uppercase;\n          letter-spacing: 0.5px;\n        }\n\n        .card-value {\n          font-size: 1.8rem;\n          font-weight: bold;\n          color: white;\n          margin-bottom: 0.25rem;\n        }\n\n        .card-change {\n          font-size: 0.8rem;\n        }\n\n        .card-change.positive {\n          color: #059669;\n        }\n\n        .charts-section {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n          gap: 2rem;\n          margin-bottom: 2rem;\n        }\n\n        .chart-container {\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 1rem;\n          padding: 1.5rem;\n          border: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .chart-container.large {\n          grid-column: 1 / -1;\n        }\n\n        .chart-title {\n          margin: 0 0 1.5rem 0;\n          font-size: 1.2rem;\n          color: white;\n          text-align: center;\n        }\n\n        .chart-wrapper {\n          height: 300px;\n          position: relative;\n        }\n\n        .achievements-section, .recommendations-section {\n          background: rgba(255, 255, 255, 0.05);\n          border-radius: 1rem;\n          padding: 1.5rem;\n          border: 1px solid rgba(255, 255, 255, 0.1);\n          margin-bottom: 2rem;\n        }\n\n        .section-title {\n          margin: 0 0 1.5rem 0;\n          font-size: 1.2rem;\n          color: white;\n          display: flex;\n          align-items: center;\n          gap: 0.5rem;\n        }\n\n        .achievements-grid {\n          display: grid;\n          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n          gap: 1rem;\n        }\n\n        .achievement-card {\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 0.5rem;\n          padding: 1rem;\n          text-align: center;\n          border: 1px solid rgba(255, 255, 255, 0.05);\n          position: relative;\n          transition: all 0.3s ease;\n        }\n\n        .achievement-card.completed {\n          border-color: rgba(37, 99, 235, 0.3);\n        }\n\n        .achievement-card.locked {\n          opacity: 0.5;\n        }\n\n        .achievement-icon {\n          font-size: 2rem;\n          margin-bottom: 0.5rem;\n        }\n\n        .achievement-title {\n          font-size: 0.9rem;\n          color: white;\n        }\n\n        .achievement-badge {\n          position: absolute;\n          top: -8px;\n          right: -8px;\n          width: 24px;\n          height: 24px;\n          background: #059669;\n          border-radius: 50%;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          font-size: 0.8rem;\n          color: white;\n        }\n\n        .recommendations-list {\n          display: flex;\n          flex-direction: column;\n          gap: 1rem;\n        }\n\n        .recommendation-card {\n          background: rgba(255, 255, 255, 0.03);\n          border-radius: 0.5rem;\n          padding: 1rem;\n          border: 1px solid rgba(255, 255, 255, 0.05);\n        }\n\n        .recommendation-header {\n          display: flex;\n          align-items: center;\n          gap: 1rem;\n        }\n\n        .recommendation-icon {\n          width: 40px;\n          height: 40px;\n          background: rgba(37, 99, 235, 0.2);\n          border-radius: 8px;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          color: #2563EB;\n        }\n\n        .recommendation-info {\n          flex: 1;\n        }\n\n        .recommendation-title {\n          margin: 0 0 0.25rem 0;\n          font-size: 1rem;\n          color: white;\n        }\n\n        .recommendation-description {\n          margin: 0;\n          font-size: 0.9rem;\n          color: rgba(255, 255, 255, 0.7);\n        }\n\n        .recommendation-priority {\n          padding: 0.25rem 0.5rem;\n          border-radius: 12px;\n          font-size: 0.7rem;\n          text-transform: uppercase;\n          font-weight: bold;\n          color: white;\n        }\n\n        @media (max-width: 768px) {\n          .relatorio-dashboard {\n            padding: 1rem;\n          }\n\n          .dashboard-title {\n            font-size: 2rem;\n          }\n\n          .summary-cards {\n            grid-template-columns: 1fr;\n          }\n\n          .charts-section {\n            grid-template-columns: 1fr;\n          }\n\n          .chart-wrapper {\n            height: 250px;\n          }\n\n          .achievements-grid {\n            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n          }\n\n          .recommendation-header {\n            flex-direction: column;\n            text-align: center;\n            gap: 0.5rem;\n          }\n        }\n      "},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:440,columnNumber:7},void 0)]},void 0,!0,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:273,columnNumber:5},void 0):e.jsxDEV(C,{feature:"Relatório Avançado"},void 0,!1,{fileName:"C:/Projetos/protalbetinav3/src/components/dashboard/RelatorioADashboard/RelatorioADashboard.jsx",lineNumber:265,columnNumber:12},void 0)};export{R as default};
