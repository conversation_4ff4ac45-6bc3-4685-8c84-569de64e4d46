/**
 * Teste do pipeline de métricas dos jogos
 * Verifica se as métricas estão sendo salvas no banco de dados
 */

console.log('🎮 Testando sistema de métricas dos jogos...');

import('./src/api/services/createIntegratedSystem.js').then(async ({ IntegratedSystemFactory }) => {
  try {
    // Criar sistema integrado
    console.log('🏗️ Criando sistema integrado...');
    const system = await IntegratedSystemFactory.create();
    console.log('✅ Sistema integrado criado');
    
    // Simular uma sessão de jogo
    const gameData = {
      gameId: 'MemoryGame',
      userId: 'test-user-001',
      sessionId: 'test-session-' + Date.now(),
      timestamp: Date.now(),
      gameType: 'memoria',
      gameData: {
        score: 85,
        level: 3,
        timeSpent: 45000,
        correctAnswers: 17,
        wrongAnswers: 3,
        accuracy: 85,
        responseTime: 2300
      },
      events: [
        { type: 'game_start', timestamp: Date.now() - 45000 },
        { type: 'card_flip', timestamp: Date.now() - 40000, data: { cardId: 1 } },
        { type: 'match_found', timestamp: Date.now() - 35000, data: { pair: [1, 5] } },
        { type: 'game_end', timestamp: Date.now(), data: { score: 85 } }
      ]
    };
    
    console.log('🎯 Processando sessão de jogo através do SystemOrchestrator...');
    
    // Processar através do método processGameMetrics que implementamos
    const result = await system.systemOrchestrator.processGameMetrics(
      gameData.userId,
      gameData.gameId,
      gameData
    );
    console.log('📊 Resultado do processamento:', {
      success: result?.success || false,
      sessionId: result?.sessionId || gameData.sessionId,
      metricsCount: result?.therapeuticMetrics ? Object.keys(result.therapeuticMetrics).length : 0,
      resultType: typeof result,
      dbSaveAttempted: result?.databaseSaved || 'unknown'
    });
    
    // Verificar se o processamento foi bem-sucedido
    if (!result) {
      console.warn('⚠️ Nenhum resultado retornado do processamento');
    } else {
      console.log('✅ Métricas processadas com sucesso');
    }
    
    // Aguardar um pouco para garantir que foi salvo
    console.log('⏳ Aguardando processamento assíncrono...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Verificar se foi salvo no banco
    console.log('🔍 Verificando dados no banco de dados...');
    
    // Conectar ao banco e verificar
    const { default: DatabaseService } = await import('./database/services/DatabaseService.js');
    const dbService = new DatabaseService();
    
    // Verificar tabelas existentes
    const tables = await dbService.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name LIKE '%game%' OR table_name LIKE '%metric%'
      ORDER BY table_name
    `);
    console.log('📋 Tabelas relacionadas a jogos/métricas:', tables.map(t => t.table_name));
    
    // Verificar sessões de jogo
    const sessions = await dbService.query('SELECT * FROM game_sessions ORDER BY created_at DESC LIMIT 5');
    console.log('📚 Últimas sessões no banco:', sessions.map(s => ({
      id: s.id,
      user_id: s.user_id,
      game_id: s.game_id,
      score: s.score,
      created_at: s.created_at
    })));
    
    // Verificar métricas cognitivas
    const cognitiveMetrics = await dbService.query('SELECT * FROM metrics_cognitive ORDER BY created_at DESC LIMIT 5');
    console.log('🧠 Últimas métricas cognitivas:', cognitiveMetrics.map(m => ({
      id: m.id,
      session_id: m.session_id,
      metric_name: m.metric_name,
      value: m.value,
      created_at: m.created_at
    })));
    
    // Verificar métricas comportamentais
    const behavioralMetrics = await dbService.query('SELECT * FROM metrics_behavioral ORDER BY created_at DESC LIMIT 5');
    console.log('🎭 Últimas métricas comportamentais:', behavioralMetrics.map(m => ({
      id: m.id,
      session_id: m.session_id,
      metric_name: m.metric_name,
      value: m.value,
      created_at: m.created_at
    })));
    
    // Verificar se a nossa sessão foi salva
    const ourSession = sessions.find(s => s.user_id === 'test-user-001');
    if (ourSession) {
      console.log('✅ Nossa sessão de teste foi encontrada no banco!');
      console.log('📊 Detalhes da sessão:', ourSession);
      
      // Verificar métricas relacionadas
      const sessionMetrics = await dbService.query(`
        SELECT table_name, COUNT(*) as count
        FROM (
          SELECT 'metrics_cognitive' as table_name FROM metrics_cognitive WHERE session_id = $1
          UNION ALL
          SELECT 'metrics_behavioral' as table_name FROM metrics_behavioral WHERE session_id = $1
          UNION ALL
          SELECT 'metrics_emotional' as table_name FROM metrics_emotional WHERE session_id = $1
          UNION ALL
          SELECT 'metrics_sensory' as table_name FROM metrics_sensory WHERE session_id = $1
        ) subquery
        GROUP BY table_name
      `, [ourSession.id]);
      
      console.log('📈 Métricas salvas para nossa sessão:', sessionMetrics);
    } else {
      console.log('❌ Nossa sessão de teste NÃO foi encontrada no banco');
    }
    
    console.log('✅ Teste de métricas concluído');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
    console.error('📍 Stack:', error.stack);
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Erro ao importar:', error.message);
  process.exit(1);
});
