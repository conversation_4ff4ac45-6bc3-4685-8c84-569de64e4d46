/**
 * 🖼️ IMAGE RECONSTRUCTION COLLECTOR V3
 * Coletor especializado para análise de reconstrução de imagens
 * Localização: src/games/MemoryGame/collectors/ImageReconstructionCollector.js
 */

export class ImageReconstructionCollector {
  constructor() {
    this.id = 'image_reconstruction_v3';
    this.name = 'Reconstrução de Imagens V3';
    this.version = '3.0.0';
    this.category = 'visual_processing';
    
    this.data = {
      reconstructions: [],
      visualAccuracy: [],
      spatialRelationships: [],
      colorMemory: [],
      shapeRecognition: [],
      patternCompletion: [],
      visualWorkingMemory: [],
      detailRetention: []
    };
  }

  // Coletar dados de tentativas de reconstrução
  collect(data) {
    const {
      originalImage,
      reconstructedImage,
      imageComplexity,
      responseTime,
      accuracy,
      difficulty,
      visualElements,
      timestamp
    } = data;

    // Análise de reconstrução visual
    const reconstructionAnalysis = this.analyzeReconstruction(originalImage, reconstructedImage);
    
    this.data.reconstructions.push({
      original: originalImage,
      reconstructed: reconstructedImage,
      complexity: imageComplexity,
      elements: visualElements,
      analysis: reconstructionAnalysis,
      responseTime,
      timestamp
    });

    // Precisão visual
    this.data.visualAccuracy.push({
      accuracy,
      spatialAccuracy: reconstructionAnalysis.spatialAccuracy,
      colorAccuracy: reconstructionAnalysis.colorAccuracy,
      shapeAccuracy: reconstructionAnalysis.shapeAccuracy,
      complexity: imageComplexity,
      timestamp
    });

    // Relacionamentos espaciais
    const spatialAnalysis = this.analyzeSpatialRelationships(originalImage, reconstructedImage);
    this.data.spatialRelationships.push(spatialAnalysis);

    // Memória de cores
    const colorAnalysis = this.analyzeColorMemory(originalImage, reconstructedImage);
    this.data.colorMemory.push(colorAnalysis);

    // Reconhecimento de formas
    const shapeAnalysis = this.analyzeShapeRecognition(originalImage, reconstructedImage);
    this.data.shapeRecognition.push(shapeAnalysis);

    // Completação de padrões
    const patternAnalysis = this.analyzePatternCompletion(originalImage, reconstructedImage);
    this.data.patternCompletion.push(patternAnalysis);

    // Memória de trabalho visual
    const visualWorkingMemory = this.calculateVisualWorkingMemory(visualElements.length, accuracy);
    this.data.visualWorkingMemory.push({
      load: visualWorkingMemory,
      performance: accuracy,
      elementCount: visualElements.length,
      timestamp
    });

    // Retenção de detalhes
    const detailAnalysis = this.analyzeDetailRetention(originalImage, reconstructedImage);
    this.data.detailRetention.push(detailAnalysis);
  }

  analyzeReconstruction(original, reconstructed) {
    if (!original || !reconstructed) {
      return { 
        overallSimilarity: 0, 
        spatialAccuracy: 0, 
        colorAccuracy: 0, 
        shapeAccuracy: 0 
      };
    }

    // Análise por elemento visual
    const elementAnalysis = this.compareVisualElements(original.elements, reconstructed.elements);
    
    return {
      overallSimilarity: elementAnalysis.overallSimilarity,
      spatialAccuracy: elementAnalysis.spatialAccuracy,
      colorAccuracy: elementAnalysis.colorAccuracy,
      shapeAccuracy: elementAnalysis.shapeAccuracy,
      sizeAccuracy: elementAnalysis.sizeAccuracy,
      orientationAccuracy: elementAnalysis.orientationAccuracy,
      elementCount: {
        original: original.elements?.length || 0,
        reconstructed: reconstructed.elements?.length || 0,
        matched: elementAnalysis.matchedElements
      },
      errors: elementAnalysis.errors
    };
  }

  compareVisualElements(originalElements, reconstructedElements) {
    if (!originalElements || !reconstructedElements) {
      return {
        overallSimilarity: 0,
        spatialAccuracy: 0,
        colorAccuracy: 0,
        shapeAccuracy: 0,
        sizeAccuracy: 0,
        orientationAccuracy: 0,
        matchedElements: 0,
        errors: []
      };
    }

    const analysis = {
      overallSimilarity: 0,
      spatialAccuracy: 0,
      colorAccuracy: 0,
      shapeAccuracy: 0,
      sizeAccuracy: 0,
      orientationAccuracy: 0,
      matchedElements: 0,
      errors: []
    };

    // Encontrar correspondências entre elementos
    const matches = this.findElementMatches(originalElements, reconstructedElements);
    
    matches.forEach(match => {
      const { original, reconstructed, similarity } = match;
      
      analysis.overallSimilarity += similarity;
      analysis.matchedElements++;

      // Análises específicas
      if (this.compareElementProperty(original, reconstructed, 'position')) {
        analysis.spatialAccuracy++;
      }
      
      if (this.compareElementProperty(original, reconstructed, 'color')) {
        analysis.colorAccuracy++;
      }
      
      if (this.compareElementProperty(original, reconstructed, 'shape')) {
        analysis.shapeAccuracy++;
      }
      
      if (this.compareElementProperty(original, reconstructed, 'size')) {
        analysis.sizeAccuracy++;
      }
      
      if (this.compareElementProperty(original, reconstructed, 'orientation')) {
        analysis.orientationAccuracy++;
      }
    });

    // Normalizar por número de elementos originais
    const elementCount = originalElements.length;
    if (elementCount > 0) {
      analysis.overallSimilarity /= elementCount;
      analysis.spatialAccuracy /= elementCount;
      analysis.colorAccuracy /= elementCount;
      analysis.shapeAccuracy /= elementCount;
      analysis.sizeAccuracy /= elementCount;
      analysis.orientationAccuracy /= elementCount;
    }

    // Identificar elementos não reconstruídos
    const unmatchedOriginal = originalElements.filter((_, index) => 
      !matches.some(match => match.originalIndex === index)
    );
    
    const unmatchedReconstructed = reconstructedElements.filter((_, index) => 
      !matches.some(match => match.reconstructedIndex === index)
    );

    analysis.errors = [
      ...unmatchedOriginal.map(element => ({ type: 'missing', element })),
      ...unmatchedReconstructed.map(element => ({ type: 'extra', element }))
    ];

    return analysis;
  }

  findElementMatches(originalElements, reconstructedElements) {
    const matches = [];
    const usedReconstructed = new Set();

    originalElements.forEach((origElement, origIndex) => {
      let bestMatch = null;
      let bestSimilarity = 0;
      let bestReconstructedIndex = -1;

      reconstructedElements.forEach((reconElement, reconIndex) => {
        if (usedReconstructed.has(reconIndex)) return;

        const similarity = this.calculateElementSimilarity(origElement, reconElement);
        if (similarity > bestSimilarity && similarity > 0.3) { // Threshold mínimo
          bestMatch = reconElement;
          bestSimilarity = similarity;
          bestReconstructedIndex = reconIndex;
        }
      });

      if (bestMatch) {
        matches.push({
          original: origElement,
          reconstructed: bestMatch,
          similarity: bestSimilarity,
          originalIndex: origIndex,
          reconstructedIndex: bestReconstructedIndex
        });
        usedReconstructed.add(bestReconstructedIndex);
      }
    });

    return matches;
  }

  calculateElementSimilarity(element1, element2) {
    if (!element1 || !element2) return 0;

    let similarity = 0;
    let factors = 0;

    // Comparar forma
    if (element1.shape && element2.shape) {
      similarity += element1.shape === element2.shape ? 1 : 0;
      factors++;
    }

    // Comparar cor
    if (element1.color && element2.color) {
      similarity += this.calculateColorSimilarity(element1.color, element2.color);
      factors++;
    }

    // Comparar posição (proximidade)
    if (element1.position && element2.position) {
      const distance = this.calculateDistance(element1.position, element2.position);
      const maxDistance = 100; // Assumindo canvas de 100x100
      similarity += Math.max(0, 1 - (distance / maxDistance));
      factors++;
    }

    // Comparar tamanho
    if (element1.size && element2.size) {
      const sizeDiff = Math.abs(element1.size - element2.size) / Math.max(element1.size, element2.size);
      similarity += Math.max(0, 1 - sizeDiff);
      factors++;
    }

    return factors > 0 ? similarity / factors : 0;
  }

  calculateColorSimilarity(color1, color2) {
    if (color1 === color2) return 1;

    // Se são cores diferentes mas próximas no espectro
    const colorProximity = {
      'red': ['orange', 'pink'],
      'blue': ['purple', 'cyan'],
      'green': ['yellow', 'cyan'],
      'yellow': ['orange', 'green'],
      'purple': ['blue', 'red'],
      'orange': ['red', 'yellow']
    };

    if (colorProximity[color1]?.includes(color2) || colorProximity[color2]?.includes(color1)) {
      return 0.5;
    }

    return 0;
  }

  calculateDistance(pos1, pos2) {
    if (!pos1 || !pos2) return Infinity;
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    return Math.sqrt(dx * dx + dy * dy);
  }

  compareElementProperty(element1, element2, property) {
    if (!element1 || !element2) return false;

    switch (property) {
      case 'position':
        return this.calculateDistance(element1.position, element2.position) < 10; // Threshold de proximidade
      case 'color':
        return this.calculateColorSimilarity(element1.color, element2.color) > 0.5;
      case 'shape':
        return element1.shape === element2.shape;
      case 'size':
        const sizeDiff = Math.abs(element1.size - element2.size) / Math.max(element1.size, element2.size);
        return sizeDiff < 0.2; // 20% de tolerância
      case 'orientation':
        if (!element1.orientation || !element2.orientation) return true; // Se não há orientação, considera correto
        const angleDiff = Math.abs(element1.orientation - element2.orientation);
        return Math.min(angleDiff, 360 - angleDiff) < 15; // 15 graus de tolerância
      default:
        return false;
    }
  }

  analyzeSpatialRelationships(original, reconstructed) {
    if (!original?.elements || !reconstructed?.elements) {
      return { accuracy: 0, preservedRelationships: 0, totalRelationships: 0 };
    }

    const originalRelationships = this.extractSpatialRelationships(original.elements);
    const reconstructedRelationships = this.extractSpatialRelationships(reconstructed.elements);
    
    const preservedRelationships = this.compareRelationships(originalRelationships, reconstructedRelationships);

    return {
      accuracy: originalRelationships.length > 0 ? preservedRelationships / originalRelationships.length : 0,
      preservedRelationships,
      totalRelationships: originalRelationships.length,
      relationshipTypes: this.categorizeRelationships(originalRelationships)
    };
  }

  extractSpatialRelationships(elements) {
    const relationships = [];

    for (let i = 0; i < elements.length; i++) {
      for (let j = i + 1; j < elements.length; j++) {
        const relationship = this.determineRelationship(elements[i], elements[j]);
        relationships.push({
          element1: i,
          element2: j,
          relationship,
          distance: this.calculateDistance(elements[i].position, elements[j].position)
        });
      }
    }

    return relationships;
  }

  determineRelationship(element1, element2) {
    const pos1 = element1.position;
    const pos2 = element2.position;

    const dx = pos2.x - pos1.x;
    const dy = pos2.y - pos1.y;

    // Determinar relação espacial predominante
    if (Math.abs(dx) > Math.abs(dy)) {
      return dx > 0 ? 'right_of' : 'left_of';
    } else {
      return dy > 0 ? 'below' : 'above';
    }
  }

  compareRelationships(original, reconstructed) {
    let preserved = 0;

    original.forEach(origRel => {
      const matchingRel = reconstructed.find(reconRel => 
        (reconRel.element1 === origRel.element1 && reconRel.element2 === origRel.element2) ||
        (reconRel.element1 === origRel.element2 && reconRel.element2 === origRel.element1)
      );

      if (matchingRel && matchingRel.relationship === origRel.relationship) {
        preserved++;
      }
    });

    return preserved;
  }

  categorizeRelationships(relationships) {
    const categories = {
      'left_of': 0,
      'right_of': 0,
      'above': 0,
      'below': 0
    };

    relationships.forEach(rel => {
      if (categories.hasOwnProperty(rel.relationship)) {
        categories[rel.relationship]++;
      }
    });

    return categories;
  }

  analyzeColorMemory(original, reconstructed) {
    if (!original?.elements || !reconstructed?.elements) {
      return { accuracy: 0, colorDistribution: {}, errors: [] };
    }

    const originalColors = this.extractColors(original.elements);
    const reconstructedColors = this.extractColors(reconstructed.elements);
    
    const colorAccuracy = this.compareColorDistributions(originalColors, reconstructedColors);
    const colorErrors = this.identifyColorErrors(original.elements, reconstructed.elements);

    return {
      accuracy: colorAccuracy,
      originalDistribution: originalColors,
      reconstructedDistribution: reconstructedColors,
      errors: colorErrors,
      colorConstancy: this.measureColorConstancy(original.elements, reconstructed.elements)
    };
  }

  extractColors(elements) {
    const colors = {};
    elements.forEach(element => {
      const color = element.color;
      colors[color] = (colors[color] || 0) + 1;
    });
    return colors;
  }

  compareColorDistributions(original, reconstructed) {
    const allColors = new Set([...Object.keys(original), ...Object.keys(reconstructed)]);
    let totalDifference = 0;
    let totalElements = Object.values(original).reduce((sum, count) => sum + count, 0);

    allColors.forEach(color => {
      const origCount = original[color] || 0;
      const reconCount = reconstructed[color] || 0;
      totalDifference += Math.abs(origCount - reconCount);
    });

    return totalElements > 0 ? Math.max(0, 1 - (totalDifference / (2 * totalElements))) : 0;
  }

  identifyColorErrors(originalElements, reconstructedElements) {
    const errors = [];
    const matches = this.findElementMatches(originalElements, reconstructedElements);

    matches.forEach(match => {
      if (match.original.color !== match.reconstructed.color) {
        errors.push({
          type: 'color_substitution',
          expected: match.original.color,
          actual: match.reconstructed.color,
          position: match.original.position
        });
      }
    });

    return errors;
  }

  measureColorConstancy(originalElements, reconstructedElements) {
    const matches = this.findElementMatches(originalElements, reconstructedElements);
    let correctColors = 0;

    matches.forEach(match => {
      if (match.original.color === match.reconstructed.color) {
        correctColors++;
      }
    });

    return matches.length > 0 ? correctColors / matches.length : 0;
  }

  analyzeShapeRecognition(original, reconstructed) {
    if (!original?.elements || !reconstructed?.elements) {
      return { accuracy: 0, shapeDistribution: {}, confusions: [] };
    }

    const originalShapes = this.extractShapes(original.elements);
    const reconstructedShapes = this.extractShapes(reconstructed.elements);
    
    const shapeAccuracy = this.compareShapeDistributions(originalShapes, reconstructedShapes);
    const shapeConfusions = this.identifyShapeConfusions(original.elements, reconstructed.elements);

    return {
      accuracy: shapeAccuracy,
      originalDistribution: originalShapes,
      reconstructedDistribution: reconstructedShapes,
      confusions: shapeConfusions,
      recognitionPattern: this.analyzeShapeRecognitionPattern(shapeConfusions)
    };
  }

  extractShapes(elements) {
    const shapes = {};
    elements.forEach(element => {
      const shape = element.shape;
      shapes[shape] = (shapes[shape] || 0) + 1;
    });
    return shapes;
  }

  compareShapeDistributions(original, reconstructed) {
    const allShapes = new Set([...Object.keys(original), ...Object.keys(reconstructed)]);
    let totalDifference = 0;
    let totalElements = Object.values(original).reduce((sum, count) => sum + count, 0);

    allShapes.forEach(shape => {
      const origCount = original[shape] || 0;
      const reconCount = reconstructed[shape] || 0;
      totalDifference += Math.abs(origCount - reconCount);
    });

    return totalElements > 0 ? Math.max(0, 1 - (totalDifference / (2 * totalElements))) : 0;
  }

  identifyShapeConfusions(originalElements, reconstructedElements) {
    const confusions = [];
    const matches = this.findElementMatches(originalElements, reconstructedElements);

    matches.forEach(match => {
      if (match.original.shape !== match.reconstructed.shape) {
        confusions.push({
          original: match.original.shape,
          confused_with: match.reconstructed.shape,
          similarity: this.calculateShapeSimilarity(match.original.shape, match.reconstructed.shape)
        });
      }
    });

    return confusions;
  }

  calculateShapeSimilarity(shape1, shape2) {
    // Definir similaridades entre formas
    const similarities = {
      'circle': { 'oval': 0.8, 'square': 0.2, 'triangle': 0.1 },
      'square': { 'rectangle': 0.8, 'triangle': 0.3, 'circle': 0.2 },
      'triangle': { 'square': 0.3, 'rectangle': 0.2, 'circle': 0.1 },
      'rectangle': { 'square': 0.8, 'oval': 0.4, 'triangle': 0.2 },
      'oval': { 'circle': 0.8, 'rectangle': 0.4, 'triangle': 0.1 }
    };

    return similarities[shape1]?.[shape2] || 0;
  }

  analyzeShapeRecognitionPattern(confusions) {
    const confusionMatrix = {};
    
    confusions.forEach(confusion => {
      const key = `${confusion.original}_to_${confusion.confused_with}`;
      confusionMatrix[key] = (confusionMatrix[key] || 0) + 1;
    });

    // Identificar o tipo de confusão mais comum
    const mostCommonConfusion = Object.entries(confusionMatrix)
      .sort(([,a], [,b]) => b - a)[0];

    return {
      confusionMatrix,
      mostCommonConfusion: mostCommonConfusion ? mostCommonConfusion[0] : null,
      totalConfusions: confusions.length
    };
  }

  analyzePatternCompletion(original, reconstructed) {
    // Análise de completação de padrões visuais
    const patternAccuracy = this.compareVisualPatterns(original, reconstructed);
    const symmetryPreservation = this.analyzeSymmetryPreservation(original, reconstructed);
    const coherenceScore = this.measureVisualCoherence(reconstructed);

    return {
      patternAccuracy,
      symmetryPreservation,
      coherenceScore,
      completionStrategy: this.identifyCompletionStrategy(original, reconstructed)
    };
  }

  compareVisualPatterns(original, reconstructed) {
    // Comparação simples baseada na distribuição de elementos
    if (!original?.elements || !reconstructed?.elements) return 0;

    const originalPattern = this.extractPattern(original.elements);
    const reconstructedPattern = this.extractPattern(reconstructed.elements);

    return this.calculatePatternSimilarity(originalPattern, reconstructedPattern);
  }

  extractPattern(elements) {
    // Extrair padrão baseado em posições relativas e propriedades
    return elements.map(element => ({
      relativePosition: this.normalizePosition(element.position),
      shape: element.shape,
      color: element.color,
      size: element.size
    }));
  }

  normalizePosition(position) {
    // Normalizar posição para análise de padrões
    return {
      x: Math.round(position.x / 10), // Discretizar para análise de padrões
      y: Math.round(position.y / 10)
    };
  }

  calculatePatternSimilarity(pattern1, pattern2) {
    if (pattern1.length !== pattern2.length) {
      return Math.min(pattern1.length, pattern2.length) / Math.max(pattern1.length, pattern2.length);
    }

    let similarity = 0;
    pattern1.forEach((element1, index) => {
      const element2 = pattern2[index];
      if (element1 && element2) {
        similarity += this.calculateElementSimilarity(element1, element2);
      }
    });

    return pattern1.length > 0 ? similarity / pattern1.length : 0;
  }

  analyzeSymmetryPreservation(original, reconstructed) {
    const originalSymmetry = this.detectSymmetry(original);
    const reconstructedSymmetry = this.detectSymmetry(reconstructed);

    return {
      originalHasSymmetry: originalSymmetry.hasSymmetry,
      reconstructedHasSymmetry: reconstructedSymmetry.hasSymmetry,
      preservationScore: this.compareSymmetries(originalSymmetry, reconstructedSymmetry)
    };
  }

  detectSymmetry(image) {
    // Detecção simples de simetria
    if (!image?.elements) return { hasSymmetry: false, type: 'none' };

    const verticalSymmetry = this.checkVerticalSymmetry(image.elements);
    const horizontalSymmetry = this.checkHorizontalSymmetry(image.elements);

    return {
      hasSymmetry: verticalSymmetry || horizontalSymmetry,
      vertical: verticalSymmetry,
      horizontal: horizontalSymmetry,
      type: verticalSymmetry && horizontalSymmetry ? 'both' : 
            verticalSymmetry ? 'vertical' : 
            horizontalSymmetry ? 'horizontal' : 'none'
    };
  }

  checkVerticalSymmetry(elements) {
    // Verificação simples de simetria vertical
    const centerX = 50; // Assumindo canvas de 100x100, centro em x=50
    
    return elements.every(element => {
      const mirrorX = 2 * centerX - element.position.x;
      return elements.some(other => 
        Math.abs(other.position.x - mirrorX) < 5 && 
        Math.abs(other.position.y - element.position.y) < 5 &&
        other.shape === element.shape &&
        other.color === element.color
      );
    });
  }

  checkHorizontalSymmetry(elements) {
    // Verificação simples de simetria horizontal
    const centerY = 50; // Assumindo canvas de 100x100, centro em y=50
    
    return elements.every(element => {
      const mirrorY = 2 * centerY - element.position.y;
      return elements.some(other => 
        Math.abs(other.position.y - mirrorY) < 5 && 
        Math.abs(other.position.x - element.position.x) < 5 &&
        other.shape === element.shape &&
        other.color === element.color
      );
    });
  }

  compareSymmetries(symmetry1, symmetry2) {
    if (!symmetry1.hasSymmetry && !symmetry2.hasSymmetry) return 1;
    if (symmetry1.hasSymmetry !== symmetry2.hasSymmetry) return 0;
    
    return symmetry1.type === symmetry2.type ? 1 : 0.5;
  }

  measureVisualCoherence(image) {
    // Medir coerência visual da reconstrução
    if (!image?.elements) return 0;

    const colorCoherence = this.measureColorCoherence(image.elements);
    const spatialCoherence = this.measureSpatialCoherence(image.elements);
    const sizeCoherence = this.measureSizeCoherence(image.elements);

    return (colorCoherence + spatialCoherence + sizeCoherence) / 3;
  }

  measureColorCoherence(elements) {
    // Coerência baseada na distribuição de cores
    const colors = new Set(elements.map(e => e.color));
    const colorCount = colors.size;
    const elementCount = elements.length;

    // Mais coerente se usa poucas cores de forma consistente
    return Math.max(0, 1 - ((colorCount - 1) / Math.max(1, elementCount - 1)));
  }

  measureSpatialCoherence(elements) {
    // Coerência baseada na distribuição espacial
    if (elements.length < 2) return 1;

    const distances = [];
    for (let i = 0; i < elements.length - 1; i++) {
      for (let j = i + 1; j < elements.length; j++) {
        distances.push(this.calculateDistance(elements[i].position, elements[j].position));
      }
    }

    // Coerência baseada na uniformidade das distâncias
    const avgDistance = distances.reduce((sum, d) => sum + d, 0) / distances.length;
    const variance = distances.reduce((sum, d) => sum + Math.pow(d - avgDistance, 2), 0) / distances.length;
    
    return Math.max(0, 1 - (variance / (avgDistance * avgDistance + 1)));
  }

  measureSizeCoherence(elements) {
    // Coerência baseada na variação de tamanhos
    const sizes = elements.map(e => e.size || 1);
    const avgSize = sizes.reduce((sum, s) => sum + s, 0) / sizes.length;
    const variance = sizes.reduce((sum, s) => sum + Math.pow(s - avgSize, 2), 0) / sizes.length;
    
    return Math.max(0, 1 - (variance / (avgSize * avgSize + 1)));
  }

  identifyCompletionStrategy(original, reconstructed) {
    // Identificar estratégia de completação
    if (!original?.elements || !reconstructed?.elements) return 'unknown';

    const originalCount = original.elements.length;
    const reconstructedCount = reconstructed.elements.length;

    if (reconstructedCount === originalCount) {
      return 'complete_reconstruction';
    } else if (reconstructedCount < originalCount) {
      return 'partial_reconstruction';
    } else {
      return 'over_reconstruction';
    }
  }

  calculateVisualWorkingMemory(elementCount, accuracy) {
    // Carga baseada no número de elementos visuais e performance
    const baseLoad = Math.min(100, elementCount * 20);
    const performanceModifier = (1 - accuracy) * 25;
    
    return Math.min(100, baseLoad + performanceModifier);
  }

  analyzeDetailRetention(original, reconstructed) {
    if (!original?.elements || !reconstructed?.elements) {
      return { 
        overallRetention: 0, 
        criticalDetails: 0, 
        peripheralDetails: 0 
      };
    }

    const matches = this.findElementMatches(original.elements, reconstructed.elements);
    
    let criticalDetailsRetained = 0;
    let peripheralDetailsRetained = 0;
    let totalCriticalDetails = 0;
    let totalPeripheralDetails = 0;

    matches.forEach(match => {
      const isCritical = this.isCriticalDetail(match.original, original.elements);
      
      if (isCritical) {
        totalCriticalDetails++;
        if (match.similarity > 0.8) criticalDetailsRetained++;
      } else {
        totalPeripheralDetails++;
        if (match.similarity > 0.8) peripheralDetailsRetained++;
      }
    });

    return {
      overallRetention: matches.length > 0 ? matches.reduce((sum, m) => sum + m.similarity, 0) / matches.length : 0,
      criticalDetails: totalCriticalDetails > 0 ? criticalDetailsRetained / totalCriticalDetails : 0,
      peripheralDetails: totalPeripheralDetails > 0 ? peripheralDetailsRetained / totalPeripheralDetails : 0,
      detailHierarchy: this.analyzeDetailHierarchy(original.elements, reconstructed.elements)
    };
  }

  isCriticalDetail(element, allElements) {
    // Determinar se um elemento é crítico baseado em:
    // 1. Posição central
    // 2. Tamanho maior
    // 3. Cor única/rara
    
    const centerDistance = this.calculateDistance(element.position, { x: 50, y: 50 });
    const isCentral = centerDistance < 25;
    
    const isLarge = element.size > 0.8; // Assumindo tamanho normalizado
    
    const colorCount = allElements.filter(e => e.color === element.color).length;
    const isUniqueColor = colorCount === 1;

    return isCentral || isLarge || isUniqueColor;
  }

  analyzeDetailHierarchy(originalElements, reconstructedElements) {
    const hierarchy = {
      primary: { retained: 0, total: 0 },
      secondary: { retained: 0, total: 0 },
      tertiary: { retained: 0, total: 0 }
    };

    const matches = this.findElementMatches(originalElements, reconstructedElements);

    originalElements.forEach((element, index) => {
      const importance = this.calculateElementImportance(element, originalElements);
      const match = matches.find(m => m.originalIndex === index);
      
      let category;
      if (importance > 0.7) category = 'primary';
      else if (importance > 0.4) category = 'secondary';
      else category = 'tertiary';

      hierarchy[category].total++;
      if (match && match.similarity > 0.7) {
        hierarchy[category].retained++;
      }
    });

    // Calcular taxas de retenção
    Object.keys(hierarchy).forEach(category => {
      const data = hierarchy[category];
      data.retentionRate = data.total > 0 ? data.retained / data.total : 0;
    });

    return hierarchy;
  }

  calculateElementImportance(element, allElements) {
    let importance = 0;

    // Importância por posição (central é mais importante)
    const centerDistance = this.calculateDistance(element.position, { x: 50, y: 50 });
    const positionImportance = Math.max(0, 1 - (centerDistance / 50));
    importance += positionImportance * 0.4;

    // Importância por tamanho
    const sizeImportance = element.size || 0.5;
    importance += sizeImportance * 0.3;

    // Importância por raridade de cor
    const colorCount = allElements.filter(e => e.color === element.color).length;
    const colorRarity = 1 / colorCount;
    importance += colorRarity * 0.3;

    return Math.min(1, importance);
  }

  // Gerar relatório de análise de reconstrução
  generateReport() {
    const totalAttempts = this.data.reconstructions.length;
    if (totalAttempts === 0) return null;

    const avgAccuracy = this.data.visualAccuracy.reduce((sum, item) => sum + item.accuracy, 0) / totalAttempts;
    const avgSpatialAccuracy = this.data.visualAccuracy.reduce((sum, item) => sum + item.spatialAccuracy, 0) / totalAttempts;
    const avgColorAccuracy = this.data.visualAccuracy.reduce((sum, item) => sum + item.colorAccuracy, 0) / totalAttempts;

    // Análise de reconstrução
    const reconstructionAnalysis = this.calculateReconstructionMetrics();
    
    // Análise de detalhes
    const detailAnalysis = this.calculateDetailRetentionMetrics();

    return {
      collectorId: this.id,
      totalAttempts,
      avgAccuracy: Math.round(avgAccuracy * 100) / 100,
      avgSpatialAccuracy: Math.round(avgSpatialAccuracy * 100) / 100,
      avgColorAccuracy: Math.round(avgColorAccuracy * 100) / 100,
      reconstructionMetrics: reconstructionAnalysis,
      detailRetention: detailAnalysis,
      recommendations: this.generateReconstructionRecommendations(avgAccuracy, reconstructionAnalysis),
      cognitiveInsights: {
        visualWorkingMemoryCapacity: this.calculateVisualCapacity(),
        spatialProcessingAccuracy: avgSpatialAccuracy * 100,
        colorMemoryStrength: avgColorAccuracy * 100,
        patternRecognitionAbility: this.calculatePatternRecognition(),
        detailOrientedProcessing: this.calculateDetailOrientation()
      }
    };
  }

  calculateReconstructionMetrics() {
    if (this.data.spatialRelationships.length === 0) return null;

    const avgSpatialRelationships = this.data.spatialRelationships.reduce((sum, item) => sum + item.accuracy, 0) / this.data.spatialRelationships.length;
    const avgColorMemory = this.data.colorMemory.reduce((sum, item) => sum + item.accuracy, 0) / this.data.colorMemory.length;
    const avgShapeRecognition = this.data.shapeRecognition.reduce((sum, item) => sum + item.accuracy, 0) / this.data.shapeRecognition.length;

    return {
      spatialRelationshipAccuracy: avgSpatialRelationships,
      colorMemoryAccuracy: avgColorMemory,
      shapeRecognitionAccuracy: avgShapeRecognition,
      overallReconstructionQuality: (avgSpatialRelationships + avgColorMemory + avgShapeRecognition) / 3
    };
  }

  calculateDetailRetentionMetrics() {
    if (this.data.detailRetention.length === 0) return null;

    const recent = this.data.detailRetention.slice(-5);
    const avgOverallRetention = recent.reduce((sum, item) => sum + item.overallRetention, 0) / recent.length;
    const avgCriticalDetails = recent.reduce((sum, item) => sum + item.criticalDetails, 0) / recent.length;
    const avgPeripheralDetails = recent.reduce((sum, item) => sum + item.peripheralDetails, 0) / recent.length;

    return {
      overallRetention: avgOverallRetention,
      criticalDetailRetention: avgCriticalDetails,
      peripheralDetailRetention: avgPeripheralDetails,
      detailProcessingStyle: this.identifyDetailProcessingStyle(avgCriticalDetails, avgPeripheralDetails)
    };
  }

  identifyDetailProcessingStyle(critical, peripheral) {
    if (critical > 0.8 && peripheral > 0.8) return 'comprehensive';
    if (critical > 0.8 && peripheral < 0.6) return 'selective_critical';
    if (critical < 0.6 && peripheral > 0.8) return 'detail_oriented';
    if (critical < 0.6 && peripheral < 0.6) return 'global_processing';
    return 'balanced';
  }

  calculateVisualCapacity() {
    if (this.data.visualWorkingMemory.length === 0) return 50;

    const recent = this.data.visualWorkingMemory.slice(-5);
    const maxElements = Math.max(...recent.map(item => item.elementCount));
    const avgPerformance = recent.reduce((sum, item) => sum + item.performance, 0) / recent.length;

    return Math.min(100, (maxElements * 15) + (avgPerformance * 40));
  }

  calculatePatternRecognition() {
    if (this.data.patternCompletion.length === 0) return 50;

    const recent = this.data.patternCompletion.slice(-5);
    const avgPatternAccuracy = recent.reduce((sum, item) => sum + item.patternAccuracy, 0) / recent.length;

    return avgPatternAccuracy * 100;
  }

  calculateDetailOrientation() {
    if (this.data.detailRetention.length === 0) return 50;

    const recent = this.data.detailRetention.slice(-5);
    const avgDetailFocus = recent.reduce((sum, item) => sum + ((item.criticalDetails + item.peripheralDetails) / 2), 0) / recent.length;

    return avgDetailFocus * 100;
  }

  generateReconstructionRecommendations(accuracy, reconstructionMetrics) {
    const recommendations = [];

    if (accuracy < 0.7) {
      recommendations.push('Praticar com imagens mais simples');
    }

    if (reconstructionMetrics && reconstructionMetrics.spatialRelationshipAccuracy < 0.6) {
      recommendations.push('Exercícios de relações espaciais');
    }

    if (reconstructionMetrics && reconstructionMetrics.colorMemoryAccuracy < 0.6) {
      recommendations.push('Treinar memória de cores');
    }

    return recommendations;
  }

  reset() {
    this.data = {
      reconstructions: [],
      visualAccuracy: [],
      spatialRelationships: [],
      colorMemory: [],
      shapeRecognition: [],
      patternCompletion: [],
      visualWorkingMemory: [],
      detailRetention: []
    };
  }
}

export default ImageReconstructionCollector;
