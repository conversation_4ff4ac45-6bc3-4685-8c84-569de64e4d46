/**
 * 🎵 SOUND MATCHING COLLECTOR V3
 * Coletor especializado para atividade de combinação de sons
 * Portal Betina V3
 */

export class SoundMatchingCollector {
  constructor() {
    this.name = 'SoundMatchingCollector';
    this.version = '3.0.0';
    this.isActive = true;
    this.collectedData = [];
  }

  /**
   * 🎯 Coleta dados específicos da combinação de sons
   */
  async collect(data) {
    try {
      const timestamp = new Date().toISOString();
      
      const analysisData = {
        // Dados básicos
        sessionId: data.sessionId,
        userId: data.userId,
        timestamp,
        activityType: 'sound_matching',
        
        // Dados da interação
        targetLetter: data.targetLetter,
        selectedLetter: data.selectedLetter,
        audioPlayed: data.audioPlayed || false,
        isCorrect: data.selectedLetter === data.targetLetter,
        responseTime: data.responseTime || 0,
        
        // Métricas de áudio
        audioReplayCount: data.behavioralMetrics?.audioReplayCount || 0,
        auditoryProcessingTime: data.behavioralMetrics?.auditoryProcessingTime || 0,
        responseConfidence: data.behavioralMetrics?.responseConfidence || 0.5,
        selectionTime: data.behavioralMetrics?.selectionTime || data.responseTime,
        
        // Análise auditiva
        phoneticComplexity: this.analyzePhoneticComplexity(data.targetLetter),
        auditoryDiscrimination: this.assessAuditoryDiscrimination(data),
        crossModalIntegration: this.assessCrossModalIntegration(data),
        auditoryMemory: this.assessAuditoryMemory(data),
        
        // Análise cognitiva
        processingStrategy: this.identifyAuditoryProcessingStrategy(data),
        attentionToSound: this.assessAuditoryAttention(data),
        phoneticAwareness: this.assessPhoneticAwareness(data),
        
        // Padrões de erro auditivo
        errorType: this.classifyAuditoryError(data.targetLetter, data.selectedLetter),
        phoneticConfusion: this.analyzePhoneticConfusion(data.targetLetter, data.selectedLetter),
        
        // Desenvolvimento auditivo
        auditorySkillLevel: this.assessAuditorySkillLevel(data),
        listeningStrategy: this.identifyListeningStrategy(data)
      };
      
      this.collectedData.push(analysisData);
      return analysisData;
      
    } catch (error) {
      console.error('Erro no SoundMatchingCollector:', error);
      return null;
    }
  }

  /**
   * 🔊 Analisa complexidade fonética
   */
  analyzePhoneticComplexity(letter) {
    if (!letter) return 0.5;
    
    const complexityMap = {
      // Vogais - mais simples
      'a': 0.2, 'e': 0.2, 'i': 0.2, 'o': 0.2, 'u': 0.2,
      
      // Consoantes simples
      'm': 0.3, 'p': 0.3, 'b': 0.3, 't': 0.3, 'd': 0.3,
      'n': 0.3, 'l': 0.3, 's': 0.3, 'f': 0.3,
      
      // Consoantes médias
      'k': 0.5, 'g': 0.5, 'v': 0.5, 'z': 0.5, 'j': 0.5,
      'c': 0.5, 'h': 0.5, 'r': 0.5,
      
      // Consoantes complexas
      'x': 0.7, 'q': 0.7, 'w': 0.7, 'y': 0.7
    };
    
    return complexityMap[letter.toLowerCase()] || 0.5;
  }

  /**
   * 👂 Avalia discriminação auditiva
   */
  assessAuditoryDiscrimination(data) {
    let discrimination = 0.6; // Base
    
    // Número de replays indica dificuldade de discriminação
    const replays = data.behavioralMetrics?.audioReplayCount || 0;
    if (replays === 0) discrimination += 0.3; // Excelente discriminação
    else if (replays === 1) discrimination += 0.1; // Boa discriminação
    else if (replays >= 3) discrimination -= 0.3; // Dificuldade de discriminação
    
    // Tempo de processamento auditivo
    const processingTime = data.behavioralMetrics?.auditoryProcessingTime || data.responseTime;
    if (processingTime < 2000) discrimination += 0.2;
    else if (processingTime > 5000) discrimination -= 0.2;
    
    // Precisão da resposta
    if (data.selectedLetter === data.targetLetter) {
      discrimination += 0.2;
    } else {
      // Analisa se o erro é por confusão fonética
      const phoneticSimilarity = this.calculatePhoneticSimilarity(data.targetLetter, data.selectedLetter);
      if (phoneticSimilarity > 0.7) discrimination -= 0.1; // Erro compreensível
      else discrimination -= 0.3; // Erro mais significativo
    }
    
    return Math.max(0.0, Math.min(1.0, discrimination));
  }

  /**
   * 🔗 Avalia integração cross-modal (áudio-visual)
   */
  assessCrossModalIntegration(data) {
    let integration = 0.5; // Base
    
    const responseTime = data.responseTime || 0;
    const replays = data.behavioralMetrics?.audioReplayCount || 0;
    
    // Boa integração = resposta rápida após ouvir o som
    if (responseTime < 2500 && data.audioPlayed) {
      integration += 0.3;
    }
    
    // Múltiplos replays podem indicar dificuldade de integração
    if (replays > 2) {
      integration -= 0.2;
    }
    
    // Precisão indica integração bem-sucedida
    if (data.selectedLetter === data.targetLetter) {
      integration += 0.3;
    }
    
    // Confiança na resposta
    const confidence = data.behavioralMetrics?.responseConfidence || 0.5;
    integration = (integration + confidence) / 2;
    
    return Math.max(0.0, Math.min(1.0, integration));
  }

  /**
   * 🧠 Avalia memória auditiva
   */
  assessAuditoryMemory(data) {
    let memory = 0.6; // Base
    
    const replays = data.behavioralMetrics?.audioReplayCount || 0;
    const responseTime = data.responseTime || 0;
    
    // Poucos replays indicam boa memória auditiva
    if (replays === 0) memory += 0.3;
    else if (replays === 1) memory += 0.1;
    else memory -= replays * 0.1;
    
    // Tempo entre último áudio e resposta (simulado)
    // Em implementação real, seria o tempo desde o último play
    const delayTime = Math.max(0, responseTime - 1000);
    if (delayTime < 2000) memory += 0.2;
    else if (delayTime > 5000) memory -= 0.2;
    
    return Math.max(0.0, Math.min(1.0, memory));
  }

  /**
   * 🎯 Identifica estratégia de processamento auditivo
   */
  identifyAuditoryProcessingStrategy(data) {
    const strategies = [];
    
    const replays = data.behavioralMetrics?.audioReplayCount || 0;
    const responseTime = data.responseTime || 0;
    const confidence = data.behavioralMetrics?.responseConfidence || 0.5;
    
    // Estratégias baseadas em replays
    if (replays === 0) strategies.push('immediate_recognition');
    if (replays === 1) strategies.push('verification');
    if (replays >= 2) strategies.push('repetitive_processing');
    if (replays >= 4) strategies.push('struggling');
    
    // Estratégias baseadas em tempo
    if (responseTime < 1500) strategies.push('quick_processing');
    if (responseTime > 4000) strategies.push('deliberate_processing');
    
    // Estratégias baseadas em confiança
    if (confidence > 0.8) strategies.push('confident');
    if (confidence < 0.4) strategies.push('uncertain');
    
    return strategies.length > 0 ? strategies : ['standard'];
  }

  /**
   * 👂 Avalia atenção auditiva
   */
  assessAuditoryAttention(data) {
    let attention = 0.6; // Base
    
    const replays = data.behavioralMetrics?.audioReplayCount || 0;
    const responseTime = data.responseTime || 0;
    
    // Poucos replays indicam boa atenção
    if (replays === 0) attention += 0.2;
    else if (replays > 3) attention -= 0.3;
    
    // Tempo de resposta adequado
    if (responseTime >= 1000 && responseTime <= 3000) attention += 0.2;
    
    // Precisão indica atenção sustentada
    if (data.selectedLetter === data.targetLetter) attention += 0.2;
    
    return Math.max(0.0, Math.min(1.0, attention));
  }

  /**
   * 🗣️ Avalia consciência fonética
   */
  assessPhoneticAwareness(data) {
    let awareness = 0.5; // Base
    
    // Precisão em letra alvo
    if (data.selectedLetter === data.targetLetter) {
      awareness += 0.3;
      
      // Resposta rápida e correta indica boa consciência fonética
      if (data.responseTime < 2000) awareness += 0.2;
    } else {
      // Analisa tipo de erro
      const phoneticSimilarity = this.calculatePhoneticSimilarity(data.targetLetter, data.selectedLetter);
      if (phoneticSimilarity > 0.7) {
        awareness += 0.1; // Erro compreensível, ainda indica alguma consciência
      } else {
        awareness -= 0.2; // Erro que indica baixa consciência fonética
      }
    }
    
    // Complexidade da letra alvo
    const complexity = this.analyzePhoneticComplexity(data.targetLetter);
    if (data.selectedLetter === data.targetLetter && complexity > 0.6) {
      awareness += 0.2; // Bonus por acertar letra complexa
    }
    
    return Math.max(0.0, Math.min(1.0, awareness));
  }

  /**
   * ❌ Classifica erro auditivo
   */
  classifyAuditoryError(target, selected) {
    if (!target || !selected || target === selected) return 'no_error';
    
    const phoneticSimilarity = this.calculatePhoneticSimilarity(target, selected);
    
    if (phoneticSimilarity > 0.7) {
      return 'phonetic_confusion';
    }
    
    // Verifica se é confusão de categoria fonética
    if (this.isSamePhoneticCategory(target, selected)) {
      return 'category_confusion';
    }
    
    // Verifica se é erro de voicing (sonoridade)
    if (this.isVoicingError(target, selected)) {
      return 'voicing_error';
    }
    
    // Verifica se é erro de lugar de articulação
    if (this.isPlaceError(target, selected)) {
      return 'place_error';
    }
    
    return 'random_auditory_error';
  }

  /**
   * 🔊 Analisa confusão fonética
   */
  analyzePhoneticConfusion(target, selected) {
    if (!target || !selected || target === selected) return null;
    
    const confusionTypes = [];
    
    // Confusão de voicing
    if (this.isVoicingError(target, selected)) {
      confusionTypes.push('voicing');
    }
    
    // Confusão de lugar
    if (this.isPlaceError(target, selected)) {
      confusionTypes.push('place');
    }
    
    // Confusão de modo
    if (this.isModeError(target, selected)) {
      confusionTypes.push('mode');
    }
    
    return {
      hasConfusion: confusionTypes.length > 0,
      types: confusionTypes,
      severity: this.calculateConfusionSeverity(target, selected)
    };
  }

  /**
   * 📊 Calcula similaridade fonética
   */
  calculatePhoneticSimilarity(target, selected) {
    if (!target || !selected) return 0;
    
    const phoneticFeatures = {
      // Vogais
      'a': { type: 'vowel', height: 'low', backness: 'central' },
      'e': { type: 'vowel', height: 'mid', backness: 'front' },
      'i': { type: 'vowel', height: 'high', backness: 'front' },
      'o': { type: 'vowel', height: 'mid', backness: 'back' },
      'u': { type: 'vowel', height: 'high', backness: 'back' },
      
      // Consoantes
      'p': { type: 'consonant', place: 'bilabial', manner: 'plosive', voice: false },
      'b': { type: 'consonant', place: 'bilabial', manner: 'plosive', voice: true },
      't': { type: 'consonant', place: 'alveolar', manner: 'plosive', voice: false },
      'd': { type: 'consonant', place: 'alveolar', manner: 'plosive', voice: true },
      'k': { type: 'consonant', place: 'velar', manner: 'plosive', voice: false },
      'g': { type: 'consonant', place: 'velar', manner: 'plosive', voice: true },
      'f': { type: 'consonant', place: 'labiodental', manner: 'fricative', voice: false },
      'v': { type: 'consonant', place: 'labiodental', manner: 'fricative', voice: true },
      's': { type: 'consonant', place: 'alveolar', manner: 'fricative', voice: false },
      'z': { type: 'consonant', place: 'alveolar', manner: 'fricative', voice: true },
      'm': { type: 'consonant', place: 'bilabial', manner: 'nasal', voice: true },
      'n': { type: 'consonant', place: 'alveolar', manner: 'nasal', voice: true },
      'l': { type: 'consonant', place: 'alveolar', manner: 'lateral', voice: true },
      'r': { type: 'consonant', place: 'alveolar', manner: 'rhotic', voice: true }
    };
    
    const targetFeatures = phoneticFeatures[target.toLowerCase()];
    const selectedFeatures = phoneticFeatures[selected.toLowerCase()];
    
    if (!targetFeatures || !selectedFeatures) return 0.1;
    
    let similarity = 0;
    
    // Mesmo tipo (vogal/consoante)
    if (targetFeatures.type === selectedFeatures.type) similarity += 0.3;
    
    if (targetFeatures.type === 'consonant' && selectedFeatures.type === 'consonant') {
      // Mesmo lugar de articulação
      if (targetFeatures.place === selectedFeatures.place) similarity += 0.3;
      
      // Mesmo modo de articulação
      if (targetFeatures.manner === selectedFeatures.manner) similarity += 0.3;
      
      // Mesma sonoridade
      if (targetFeatures.voice === selectedFeatures.voice) similarity += 0.1;
    }
    
    if (targetFeatures.type === 'vowel' && selectedFeatures.type === 'vowel') {
      // Mesma altura
      if (targetFeatures.height === selectedFeatures.height) similarity += 0.3;
      
      // Mesma anterioridade
      if (targetFeatures.backness === selectedFeatures.backness) similarity += 0.4;
    }
    
    return Math.min(1.0, similarity);
  }

  /**
   * Métodos auxiliares para classificação de erros
   */
  isSamePhoneticCategory(target, selected) {
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    const plosives = ['p', 'b', 't', 'd', 'k', 'g'];
    const fricatives = ['f', 'v', 's', 'z'];
    const nasals = ['m', 'n'];
    
    const categories = [vowels, plosives, fricatives, nasals];
    
    return categories.some(category => 
      category.includes(target.toLowerCase()) && category.includes(selected.toLowerCase())
    );
  }

  isVoicingError(target, selected) {
    const voicingPairs = [
      ['p', 'b'], ['t', 'd'], ['k', 'g'], ['f', 'v'], ['s', 'z']
    ];
    
    return voicingPairs.some(pair => 
      (pair[0] === target.toLowerCase() && pair[1] === selected.toLowerCase()) ||
      (pair[1] === target.toLowerCase() && pair[0] === selected.toLowerCase())
    );
  }

  isPlaceError(target, selected) {
    const placeGroups = [
      ['p', 'b', 'm'], // Bilabial
      ['t', 'd', 'n', 'l', 's', 'z'], // Alveolar
      ['k', 'g'], // Velar
      ['f', 'v'] // Labiodental
    ];
    
    return placeGroups.some(group => 
      group.includes(target.toLowerCase()) && group.includes(selected.toLowerCase())
    );
  }

  isModeError(target, selected) {
    const modeGroups = [
      ['p', 'b', 't', 'd', 'k', 'g'], // Plosivas
      ['f', 'v', 's', 'z'], // Fricativas
      ['m', 'n'], // Nasais
      ['l', 'r'] // Líquidas
    ];
    
    return modeGroups.some(group => 
      group.includes(target.toLowerCase()) && group.includes(selected.toLowerCase())
    );
  }

  calculateConfusionSeverity(target, selected) {
    const similarity = this.calculatePhoneticSimilarity(target, selected);
    return 1 - similarity; // Maior similaridade = menor severidade
  }

  /**
   * 🎓 Avalia nível de habilidade auditiva
   */
  assessAuditorySkillLevel(data) {
    const discrimination = this.assessAuditoryDiscrimination(data);
    const awareness = this.assessPhoneticAwareness(data);
    const attention = this.assessAuditoryAttention(data);
    const integration = this.assessCrossModalIntegration(data);
    
    const overall = (discrimination + awareness + attention + integration) / 4;
    
    if (overall >= 0.8) return 'advanced';
    if (overall >= 0.6) return 'proficient';
    if (overall >= 0.4) return 'developing';
    return 'emerging';
  }

  /**
   * 👂 Identifica estratégia de escuta
   */
  identifyListeningStrategy(data) {
    const strategies = [];
    
    const replays = data.behavioralMetrics?.audioReplayCount || 0;
    const responseTime = data.responseTime || 0;
    const isCorrect = data.selectedLetter === data.targetLetter;
    
    if (replays === 0 && isCorrect) strategies.push('confident_listener');
    if (replays === 1 && isCorrect) strategies.push('careful_listener');
    if (replays >= 2) strategies.push('analytical_listener');
    if (replays >= 4) strategies.push('struggling_listener');
    
    if (responseTime < 2000 && isCorrect) strategies.push('quick_processor');
    if (responseTime > 4000) strategies.push('deliberate_processor');
    
    return strategies.length > 0 ? strategies : ['typical_listener'];
  }

  /**
   * 📊 Gera resumo da coleta
   */
  generateSummary() {
    if (this.collectedData.length === 0) return null;
    
    const totalCollections = this.collectedData.length;
    const correctAnswers = this.collectedData.filter(d => d.isCorrect).length;
    const accuracy = correctAnswers / totalCollections;
    
    const avgResponseTime = this.collectedData.reduce((sum, d) => sum + d.responseTime, 0) / totalCollections;
    const avgReplays = this.collectedData.reduce((sum, d) => sum + d.audioReplayCount, 0) / totalCollections;
    const avgDiscrimination = this.collectedData.reduce((sum, d) => sum + d.auditoryDiscrimination, 0) / totalCollections;
    
    // Análise de padrões de erro auditivo
    const errorTypes = {};
    this.collectedData.forEach(d => {
      if (d.errorType !== 'no_error') {
        errorTypes[d.errorType] = (errorTypes[d.errorType] || 0) + 1;
      }
    });
    
    return {
      collector: this.name,
      version: this.version,
      totalCollections,
      accuracy,
      averageResponseTime: avgResponseTime,
      averageReplays: avgReplays,
      averageDiscrimination: avgDiscrimination,
      auditoryErrorPatterns: errorTypes,
      phoneticInsights: this.analyzePhoneticPatterns(),
      recommendations: this.generateRecommendations()
    };
  }

  /**
   * 🔊 Analisa padrões fonéticos
   */
  analyzePhoneticPatterns() {
    const phoneticData = this.collectedData.map(d => ({
      target: d.targetLetter,
      selected: d.selectedLetter,
      correct: d.isCorrect,
      complexity: d.phoneticComplexity
    }));
    
    // Agrupa por complexidade
    const complexityGroups = {
      simple: phoneticData.filter(d => d.complexity <= 0.3),
      medium: phoneticData.filter(d => d.complexity > 0.3 && d.complexity <= 0.6),
      complex: phoneticData.filter(d => d.complexity > 0.6)
    };
    
    return {
      performanceByComplexity: {
        simple: complexityGroups.simple.length > 0 ? 
          complexityGroups.simple.filter(d => d.correct).length / complexityGroups.simple.length : 0,
        medium: complexityGroups.medium.length > 0 ? 
          complexityGroups.medium.filter(d => d.correct).length / complexityGroups.medium.length : 0,
        complex: complexityGroups.complex.length > 0 ? 
          complexityGroups.complex.filter(d => d.correct).length / complexityGroups.complex.length : 0
      },
      totalByComplexity: {
        simple: complexityGroups.simple.length,
        medium: complexityGroups.medium.length,
        complex: complexityGroups.complex.length
      }
    };
  }

  /**
   * 💡 Gera recomendações específicas
   */
  generateRecommendations() {
    const summary = this.generateSummary();
    if (!summary) return [];
    
    const recommendations = [];
    
    // Recomendações baseadas em replays
    if (summary.averageReplays > 2) {
      recommendations.push({
        type: 'auditory_memory',
        priority: 'high',
        message: 'Pratique exercícios de memória auditiva para reduzir necessidade de repetições.'
      });
    }
    
    // Recomendações baseadas em discriminação
    if (summary.averageDiscrimination < 0.6) {
      recommendations.push({
        type: 'auditory_discrimination',
        priority: 'high',
        message: 'Foque em exercícios de discriminação auditiva com pares mínimos.'
      });
    }
    
    // Recomendações baseadas em padrões de erro
    const errorTypes = summary.auditoryErrorPatterns;
    if (errorTypes.phonetic_confusion > 2) {
      recommendations.push({
        type: 'phonetic_training',
        priority: 'medium',
        message: 'Pratique distinção entre sons fonéticamente similares.'
      });
    }
    
    if (errorTypes.voicing_error > 1) {
      recommendations.push({
        type: 'voicing_training',
        priority: 'medium',
        message: 'Trabalhe diferenciação entre sons surdos e sonoros.'
      });
    }
    
    return recommendations;
  }

  /**
   * 🔄 Reset do coletor
   */
  reset() {
    this.collectedData = [];
  }

  /**
   * 📥 Exporta dados coletados
   */
  exportData() {
    return {
      collector: this.name,
      version: this.version,
      collectedAt: new Date().toISOString(),
      data: this.collectedData,
      summary: this.generateSummary()
    };
  }
}
