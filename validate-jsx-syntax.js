/**
 * Script para validar sintaxe JSX
 */

import { readFileSync } from 'fs'

try {
  console.log('🔍 Validando sintaxe do PerformanceDashboard...')
  
  const filePath = './src/components/dashboard/PerformanceDashboard/PerformanceDashboard.jsx'
  const content = readFileSync(filePath, 'utf8')
  
  // Verificações básicas de sintaxe
  const issues = []
  
  // Verificar parênteses balanceados
  const openParens = (content.match(/\(/g) || []).length
  const closeParens = (content.match(/\)/g) || []).length
  if (openParens !== closeParens) {
    issues.push(`❌ Parênteses desbalanceados: ${openParens} abertos, ${closeParens} fechados`)
  }
  
  // Verificar chaves balanceadas
  const openBraces = (content.match(/\{/g) || []).length
  const closeBraces = (content.match(/\}/g) || []).length
  if (openBraces !== closeBraces) {
    issues.push(`❌ Chaves desbalanceadas: ${openBraces} abertas, ${closeBraces} fechadas`)
  }
  
  // Verificar colchetes balanceados
  const openBrackets = (content.match(/\[/g) || []).length
  const closeBrackets = (content.match(/\]/g) || []).length
  if (openBrackets !== closeBrackets) {
    issues.push(`❌ Colchetes desbalanceados: ${openBrackets} abertos, ${closeBrackets} fechados`)
  }
  
  if (issues.length === 0) {
    console.log('✅ Sintaxe básica parece estar correta!')
    console.log(`📊 Estatísticas:`)
    console.log(`   - Parênteses: ${openParens} pares`)
    console.log(`   - Chaves: ${openBraces} pares`)
    console.log(`   - Colchetes: ${openBrackets} pares`)
  } else {
    console.log('❌ Problemas encontrados:')
    issues.forEach(issue => console.log(`   ${issue}`))
  }
  
} catch (error) {
  console.error('❌ Erro ao validar arquivo:', error.message)
}
