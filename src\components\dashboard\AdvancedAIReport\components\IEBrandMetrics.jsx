/**
 * @file IEBrandMetrics.jsx
 * @description Componente de Métricas Filtradas pela IE Brand para Dashboard A
 * @version 3.0.0
 * @premium true
 */

import React, { useState, useEffect } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  Filler,
} from 'chart.js'
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2'
import styles from './IEBrandMetrics.module.css'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  Filler
)

const IEBrandMetrics = ({ dashboardData, className }) => {
  const [selectedMetric, setSelectedMetric] = useState('neuroplasticity')
  const [timeRange, setTimeRange] = useState('30d')
  const [ieBrandInsights, setIeBrandInsights] = useState(null)

  // Configurações MCP via environment variables
  const mcpConfig = {
    endpoint: process.env.REACT_APP_MCP_ENDPOINT,
    enabled: process.env.REACT_APP_MCP_ENABLED === 'true'
  }

  // Métricas específicas da IE Brand
  const ieBrandMetrics = {
    neuroplasticity: {
      title: 'Neuroplasticidade',
      description: 'Capacidade de adaptação neural',
      icon: '🧠',
      color: '#4CAF50'
    },
    cognitive_flexibility: {
      title: 'Flexibilidade Cognitiva',
      description: 'Adaptação a mudanças de contexto',
      icon: '🔄',
      color: '#2196F3'
    },
    attention_regulation: {
      title: 'Regulação da Atenção',
      description: 'Controle do foco atencional',
      icon: '🎯',
      color: '#FF9800'
    },
    executive_function: {
      title: 'Função Executiva',
      description: 'Planejamento e tomada de decisão',
      icon: '⚡',
      color: '#9C27B0'
    },
    social_cognition: {
      title: 'Cognição Social',
      description: 'Compreensão social e empatia',
      icon: '👥',
      color: '#E91E63'
    },
    sensory_integration: {
      title: 'Integração Sensorial',
      description: 'Processamento multissensorial',
      icon: '🌈',
      color: '#607D8B'
    }
  }

  useEffect(() => {
    generateIEBrandInsights()
  }, [selectedMetric, timeRange, dashboardData])

  const generateIEBrandInsights = () => {
    // Simular análise avançada da IE Brand baseada nos dados
    const insights = {
      neuroplasticity: {
        score: Math.round(65 + Math.random() * 30),
        trend: 'positive',
        factors: [
          'Variabilidade de atividades: Excelente',
          'Adaptação à dificuldade: Boa',
          'Persistência em desafios: Muito boa'
        ],
        recommendations: [
          'Continuar variando tipos de atividades',
          'Introduzir desafios progressivos',
          'Manter rotina de prática regular'
        ]
      },
      cognitive_flexibility: {
        score: Math.round(60 + Math.random() * 35),
        trend: 'stable',
        factors: [
          'Mudança entre tarefas: Boa',
          'Adaptação a regras novas: Moderada',
          'Resolução criativa: Em desenvolvimento'
        ],
        recommendations: [
          'Praticar jogos com mudanças de regras',
          'Estimular pensamento divergente',
          'Exercícios de alternância de tarefas'
        ]
      },
      attention_regulation: {
        score: Math.round(70 + Math.random() * 25),
        trend: 'positive',
        factors: [
          'Tempo de foco sustentado: Bom',
          'Resistência a distrações: Muito boa',
          'Atenção seletiva: Excelente'
        ],
        recommendations: [
          'Aumentar gradualmente duração das atividades',
          'Introduzir ambientes com mais distrações',
          'Treinar atenção dividida'
        ]
      },
      executive_function: {
        score: Math.round(55 + Math.random() * 40),
        trend: 'improving',
        factors: [
          'Planejamento estratégico: Em desenvolvimento',
          'Controle inibitório: Bom',
          'Memória de trabalho: Moderada'
        ],
        recommendations: [
          'Jogos de estratégia progressiva',
          'Exercícios de sequenciamento',
          'Atividades de planejamento de passos'
        ]
      },
      social_cognition: {
        score: Math.round(50 + Math.random() * 35),
        trend: 'stable',
        factors: [
          'Reconhecimento emocional: Em desenvolvimento',
          'Teoria da mente: Moderada',
          'Empatia comportamental: Boa'
        ],
        recommendations: [
          'Jogos colaborativos estruturados',
          'Atividades de reconhecimento facial',
          'Histórias sociais interativas'
        ]
      },
      sensory_integration: {
        score: Math.round(75 + Math.random() * 20),
        trend: 'positive',
        factors: [
          'Processamento visual: Excelente',
          'Integração auditiva: Boa',
          'Coordenação multissensorial: Muito boa'
        ],
        recommendations: [
          'Atividades multissensoriais complexas',
          'Integração de modalidades menos dominantes',
          'Desafios de sincronização sensorial'
        ]
      }
    }

    setIeBrandInsights(insights[selectedMetric])
  }

  const generateChartData = () => {
    const metric = ieBrandMetrics[selectedMetric]
    const insights = ieBrandInsights
    
    if (!insights) return null

    return {
      evolutionChart: {
        labels: ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4', 'Atual'],
        datasets: [{
          label: metric.title,
          data: [
            insights.score - 15,
            insights.score - 10,
            insights.score - 5,
            insights.score,
            insights.score + 3
          ],
          borderColor: metric.color,
          backgroundColor: `${metric.color}20`,
          fill: true,
          tension: 0.4
        }]
      },
      comparisonChart: {
        labels: Object.keys(ieBrandMetrics).map(key => 
          ieBrandMetrics[key].title.split(' ')[0]
        ),
        datasets: [{
          data: Object.keys(ieBrandMetrics).map(() => 
            60 + Math.random() * 35
          ),
          backgroundColor: Object.values(ieBrandMetrics).map(m => `${m.color}80`),
          borderColor: Object.values(ieBrandMetrics).map(m => m.color),
          borderWidth: 2
        }]
      },
      detailChart: {
        labels: ['Velocidade', 'Precisão', 'Consistência', 'Adaptabilidade', 'Eficiência'],
        datasets: [{
          label: metric.title,
          data: [
            insights.score + Math.random() * 10 - 5,
            insights.score + Math.random() * 10 - 5,
            insights.score + Math.random() * 10 - 5,
            insights.score + Math.random() * 10 - 5,
            insights.score + Math.random() * 10 - 5
          ],
          backgroundColor: `${metric.color}30`,
          borderColor: metric.color,
          pointBackgroundColor: metric.color,
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: metric.color,
        }]
      }
    }
  }

  const chartData = generateChartData()
  const currentMetric = ieBrandMetrics[selectedMetric]

  return (
    <div className={`${styles.metricsContainer} ${className || ''}`}>
      {/* Header */}
      <div className={styles.metricsHeader}>
        <div className={styles.headerInfo}>
          <h2 className={styles.metricsTitle}>
            <span className={styles.brandIcon}>🧠</span>
            IE Brand Analytics
          </h2>
          <p className={styles.metricsSubtitle}>
            Métricas avançadas de neurocognição e desenvolvimento
          </p>
        </div>
        
        <div className={styles.headerControls}>
          <select 
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className={styles.timeSelector}
          >
            <option value="7d">7 dias</option>
            <option value="30d">30 dias</option>
            <option value="90d">90 dias</option>
          </select>
        </div>
      </div>

      {/* Seletor de Métricas */}
      <div className={styles.metricsSelector}>
        {Object.entries(ieBrandMetrics).map(([key, metric]) => (
          <button
            key={key}
            onClick={() => setSelectedMetric(key)}
            className={`${styles.metricButton} ${
              selectedMetric === key ? styles.active : ''
            }`}
            style={{
              '--metric-color': metric.color
            }}
          >
            <span className={styles.metricIcon}>{metric.icon}</span>
            <span className={styles.metricLabel}>{metric.title}</span>
          </button>
        ))}
      </div>

      {/* Métricas Principais */}
      {ieBrandInsights && (
        <div className={styles.mainMetrics}>
          <div className={styles.scoreCard}>
            <div className={styles.scoreHeader}>
              <span className={styles.scoreIcon} style={{ color: currentMetric.color }}>
                {currentMetric.icon}
              </span>
              <div>
                <h3 className={styles.scoreTitle}>{currentMetric.title}</h3>
                <p className={styles.scoreDescription}>{currentMetric.description}</p>
              </div>
            </div>
            <div className={styles.scoreValue}>
              <span className={styles.scoreNumber}>{ieBrandInsights.score}</span>
              <span className={styles.scoreUnit}>/100</span>
              <div className={`${styles.scoreTrend} ${styles[ieBrandInsights.trend]}`}>
                {ieBrandInsights.trend === 'positive' && '📈 Melhorando'}
                {ieBrandInsights.trend === 'stable' && '➡️ Estável'}
                {ieBrandInsights.trend === 'improving' && '🔄 Em progresso'}
              </div>
            </div>
          </div>

          <div className={styles.factorsCard}>
            <h4 className={styles.factorsTitle}>Fatores Analisados</h4>
            <div className={styles.factorsList}>
              {ieBrandInsights.factors.map((factor, index) => (
                <div key={index} className={styles.factorItem}>
                  <span className={styles.factorIcon}>✓</span>
                  <span className={styles.factorText}>{factor}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Gráficos */}
      {chartData && (
        <div className={styles.chartsGrid}>
          <div className={styles.chartCard}>
            <h4 className={styles.chartTitle}>📈 Evolução Temporal</h4>
            <div className={styles.chartContainer}>
              <Line 
                data={chartData.evolutionChart}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    y: {
                      beginAtZero: true,
                      max: 100,
                      ticks: { color: '#666' },
                      grid: { color: '#e0e0e0' }
                    },
                    x: {
                      ticks: { color: '#666' },
                      grid: { color: '#e0e0e0' }
                    }
                  },
                  plugins: {
                    legend: { position: 'bottom' }
                  }
                }}
              />
            </div>
          </div>

          <div className={styles.chartCard}>
            <h4 className={styles.chartTitle}>🔍 Análise Detalhada</h4>
            <div className={styles.chartContainer}>
              <Radar 
                data={chartData.detailChart}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    r: {
                      beginAtZero: true,
                      max: 100,
                      ticks: { color: '#666' },
                      grid: { color: '#e0e0e0' }
                    }
                  },
                  plugins: {
                    legend: { position: 'bottom' }
                  }
                }}
              />
            </div>
          </div>

          <div className={styles.chartCard}>
            <h4 className={styles.chartTitle}>📊 Comparativo Geral</h4>
            <div className={styles.chartContainer}>
              <Doughnut 
                data={chartData.comparisonChart}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: { position: 'bottom' }
                  }
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Recomendações IE Brand */}
      {ieBrandInsights && (
        <div className={styles.recommendationsSection}>
          <h4 className={styles.recommendationsTitle}>
            💡 Recomendações IE Brand
          </h4>
          <div className={styles.recommendationsList}>
            {ieBrandInsights.recommendations.map((recommendation, index) => (
              <div key={index} className={styles.recommendationCard}>
                <div className={styles.recommendationIcon}>🎯</div>
                <div className={styles.recommendationContent}>
                  <p>{recommendation}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Footer IE Brand */}
      <div className={styles.brandFooter}>
        <div className={styles.brandInfo}>
          <span className={styles.brandLogo}>🧠</span>
          <div>
            <strong>IE Brand Analytics</strong>
            <p>Tecnologia neurocognitiva avançada para desenvolvimento personalizado</p>
          </div>
        </div>
        <div className={styles.mcpStatus}>
          <span className={styles.mcpIndicator}>
            {mcpConfig.enabled && mcpConfig.endpoint ? '🔗' : '🔗'}
          </span>
          <span>
            {mcpConfig.enabled && mcpConfig.endpoint ? 'MCP Configurado' : 'MCP via ENV'}
          </span>
        </div>
      </div>
    </div>
  )
}

export default IEBrandMetrics
