/**
 * 🔧 TESTE DE INTEGRAÇÃO COMPLETA DA CADEIA DE ANÁLISE
 * Coletores → Processadores → Analisadores → Orquestrador
 * Portal Betina V3
 */

import { GameSpecificProcessors } from './src/api/services/processors/GameSpecificProcessors.js';
import { AnalysisOrchestrator } from './src/api/services/analysis/AnalysisOrchestrator.js';
import { CognitiveAnalyzer } from './src/api/services/analysis/CognitiveAnalyzer.js';
import { BehavioralAnalyzer } from './src/api/services/analysis/BehavioralAnalyzer.js';
import { TherapeuticAnalyzer } from './src/api/services/analysis/TherapeuticAnalyzer.js';
import { ProgressAnalyzer } from './src/api/services/analysis/ProgressAnalyzer.js';
import { SystemOrchestrator } from './src/api/services/orchestrator/SystemOrchestrator.js';

// Dados de teste robustos para diferentes jogos
const testGameData = {
  CreativePainting: {
    sessionId: 'test-session-complete-creative',
    userId: 'test-user-complete-123',
    childId: 'test-child-complete-456',
    gameType: 'CreativePainting',
    startTime: Date.now() - 600000, // 10 minutos atrás
    endTime: Date.now(),
    completed: true,
    score: 85,
    level: 2,
    
    // Dados específicos do Creative Painting
    brushStrokes: [
      { x: 100, y: 150, pressure: 0.8, size: 5, color: '#FF0000', timestamp: Date.now() - 500000, smoothness: 0.7, irregularity: 0.3 },
      { x: 120, y: 170, pressure: 0.6, size: 8, color: '#00FF00', timestamp: Date.now() - 480000, smoothness: 0.8, irregularity: 0.2 },
      { x: 140, y: 190, pressure: 0.9, size: 3, color: '#0000FF', timestamp: Date.now() - 460000, smoothness: 0.9, irregularity: 0.1 },
      { x: 160, y: 210, pressure: 0.7, size: 6, color: '#FFFF00', timestamp: Date.now() - 440000, smoothness: 0.6, irregularity: 0.4 },
      { x: 180, y: 230, pressure: 0.8, size: 4, color: '#FF00FF', timestamp: Date.now() - 420000, smoothness: 0.8, irregularity: 0.2 }
    ],
    colorMixings: [
      { targetColor: '#FF8800', resultColor: '#FF7700', isCorrect: true, responseTime: 2500 },
      { targetColor: '#00FFFF', resultColor: '#00AAFF', isCorrect: false, responseTime: 4000 },
      { targetColor: '#8800FF', resultColor: '#8800FF', isCorrect: true, responseTime: 1800 }
    ],
    toolSelections: [
      { toolId: 'brush', correctTool: 'brush', isInappropriate: false, responseTime: 1500 },
      { toolId: 'pencil', correctTool: 'brush', isInappropriate: true, responseTime: 3000 },
      { toolId: 'brush', correctTool: 'brush', isInappropriate: false, responseTime: 1200 }
    ],
    actions: [
      { type: 'brush_stroke', success: true, timestamp: Date.now() - 500000 },
      { type: 'color_mix', success: false, timestamp: Date.now() - 480000 },
      { type: 'tool_change', success: true, timestamp: Date.now() - 460000 },
      { type: 'brush_stroke', success: true, timestamp: Date.now() - 440000 }
    ],
    metrics: {
      creativityScore: 85,
      coordinationScore: 78,
      engagementTime: 580,
      colorsUsed: 12,
      toolChanges: 6,
      accuracy: 0.75
    },
    sessionData: {
      duration: 600,
      totalActions: 15,
      pauseTime: 45,
      focusLoss: 2
    }
  },

  LetterRecognition: {
    sessionId: 'test-session-complete-letter',
    userId: 'test-user-complete-123',
    childId: 'test-child-complete-456',
    gameType: 'LetterRecognition',
    startTime: Date.now() - 420000, // 7 minutos atrás
    endTime: Date.now(),
    completed: true,
    score: 92,
    level: 3,
    
    // Dados específicos do Letter Recognition
    attempts: [
      {
        targetLetter: 'A',
        selectedLetter: 'A',
        isCorrect: true,
        position: 'center',
        responseTime: 1200,
        distractors: ['B', 'H', 'R']
      },
      {
        targetLetter: 'B',
        selectedLetter: 'D',
        isCorrect: false,
        position: 'right',
        responseTime: 2500,
        distractors: ['P', 'R', 'D']
      },
      {
        targetLetter: 'C',
        selectedLetter: 'C',
        isCorrect: true,
        position: 'left',
        responseTime: 1800,
        distractors: ['O', 'Q', 'G']
      },
      {
        targetLetter: 'D',
        selectedLetter: 'D',
        isCorrect: true,
        position: 'center',
        responseTime: 1400,
        distractors: ['B', 'P', 'R']
      }
    ],
    letterForm: 'B',
    recognitionSpeed: 1725, // Média dos tempos de resposta
    context: 'isolated',
    visualErrors: [
      { type: 'mirror_confusion', orientation: 'horizontal', mirrorConfusion: true }
    ],
    auditoryErrors: [
      { type: 'similar_sound', phoneticSimilarity: 0.7 }
    ],
    readingSpeed: 15,
    visualAttentionScores: { focus: 0.8, sustain: 0.7, selective: 0.85 },
    linguisticMetrics: { phonologicalAwareness: 0.75, orthographicProcessing: 0.82 },
    sessionData: {
      duration: 420,
      totalAttempts: 12,
      correctAttempts: 9,
      accuracy: 0.75
    }
  },

  MemoryGame: {
    sessionId: 'test-session-complete-memory',
    userId: 'test-user-complete-123',
    childId: 'test-child-complete-456',
    gameType: 'MemoryGame',
    startTime: Date.now() - 480000, // 8 minutos atrás
    endTime: Date.now(),
    completed: true,
    score: 88,
    level: 4,

    // Dados específicos do Memory Game
    attemptHistory: [
      { timestamp: Date.now() - 450000, isMatch: true, cards: [{ id: 1, image: 'cat' }, { id: 8, image: 'cat' }], responseTime: 3200 },
      { timestamp: Date.now() - 430000, isMatch: false, cards: [{ id: 2, image: 'dog' }, { id: 5, image: 'bird' }], responseTime: 2800 },
      { timestamp: Date.now() - 410000, isMatch: true, cards: [{ id: 3, image: 'fish' }, { id: 9, image: 'fish' }], responseTime: 2100 },
      { timestamp: Date.now() - 390000, isMatch: false, cards: [{ id: 4, image: 'tree' }, { id: 6, image: 'flower' }], responseTime: 4500 },
      { timestamp: Date.now() - 370000, isMatch: true, cards: [{ id: 7, image: 'house' }, { id: 10, image: 'house' }], responseTime: 1800 }
    ],
    memoryMetrics: { 
      workingMemoryLoad: 8, 
      spatialMemoryScore: 82, 
      temporalMemory: 76,
      recognitionAccuracy: 0.78,
      recallSpeed: 2880 // média dos tempos de resposta
    },
    gameConfig: { 
      boardSize: '4x4', 
      cardTypes: 8, 
      timeLimit: 300,
      difficulty: 'medium'
    },
    sessionData: {
      duration: 480,
      totalPairs: 8,
      foundPairs: 6,
      attempts: 16,
      accuracy: 0.75
    }
  }
};

async function testCompleteAnalysisChain() {
  console.log('\n🔧 === TESTE DE INTEGRAÇÃO COMPLETA DA CADEIA DE ANÁLISE ===');
  console.log(`📅 Iniciado em: ${new Date().toISOString()}`);
  console.log('🔗 Coletores → Processadores → Analisadores → Orquestrador\n');

  try {
    // === ETAPA 1: INICIALIZAÇÃO DOS COMPONENTES ===
    console.log('🚀 ETAPA 1: Inicializando componentes da cadeia...\n');
    
    // Inicializar processadores (que contêm os coletores)
    console.log('1️⃣ Inicializando GameSpecificProcessors...');
    const processors = new GameSpecificProcessors();
    console.log('✅ Processadores inicializados com sucesso');
    
    // Inicializar analisadores
    console.log('2️⃣ Inicializando analisadores especializados...');
    const cognitiveAnalyzer = new CognitiveAnalyzer();
    const behavioralAnalyzer = new BehavioralAnalyzer();
    const therapeuticAnalyzer = new TherapeuticAnalyzer();
    const progressAnalyzer = new ProgressAnalyzer();
    console.log('✅ Analisadores especializados inicializados');
    
    // Inicializar orquestrador de análise
    console.log('3️⃣ Inicializando AnalysisOrchestrator...');
    const analysisOrchestrator = new AnalysisOrchestrator();
    console.log('✅ AnalysisOrchestrator inicializado');
    
    // Inicializar orquestrador de sistema
    console.log('4️⃣ Inicializando SystemOrchestrator...');
    const systemOrchestrator = new SystemOrchestrator();
    console.log('✅ SystemOrchestrator inicializado\n');

    // === ETAPA 2: PROCESSAMENTO INDIVIDUAL POR JOGO ===
    console.log('🎮 ETAPA 2: Processando dados dos jogos através da cadeia completa...\n');
    
    const allAnalysisResults = [];
    
    for (const [gameType, gameData] of Object.entries(testGameData)) {
      console.log(`\n🎯 PROCESSANDO JOGO: ${gameType}`);
      console.log('='.repeat(60));
      
      try {
        // FASE 1: Coletores + Processadores
        console.log('📊 Fase 1: Executando coletores e processadores...');
        const processingResult = await processors.processGameData(gameType, gameData);
        
        if (processingResult && processingResult.success) {
          console.log('✅ Processamento bem-sucedido');
          console.log(`   📈 Coletores utilizados: ${processingResult.metadata?.collectorsUsed?.length || 0}`);
          console.log(`   🔍 Análise específica: ${Object.keys(processingResult.specificAnalysis).length} métricas`);
          console.log(`   🏥 Análise terapêutica: ${Object.keys(processingResult.therapeuticAnalysis || {}).length} insights`);
        } else {
          console.log('❌ Falha no processamento');
          continue;
        }

        // FASE 2: Analisadores especializados
        console.log('🧠 Fase 2: Executando analisadores especializados...');
        
        const analysisResults = {
          gameType,
          processing: processingResult,
          cognitive: null,
          behavioral: null,
          therapeutic: null,
          progress: null
        };

        // Análise cognitiva
        try {
          console.log('   🧠 Executando análise cognitiva...');
          const cognitiveResult = await cognitiveAnalyzer.analyzeCognitivePatterns({
            gameData,
            processedMetrics: processingResult.specificAnalysis.metrics,
            collectorsData: processingResult.specificAnalysis
          });
          analysisResults.cognitive = cognitiveResult;
          console.log('   ✅ Análise cognitiva concluída');
        } catch (error) {
          console.log(`   ⚠️ Análise cognitiva com erro: ${error.message}`);
        }

        // Análise comportamental
        try {
          console.log('   👤 Executando análise comportamental...');
          const behavioralResult = await behavioralAnalyzer.analyzeBehavioralPatterns({
            gameData,
            processedMetrics: processingResult.specificAnalysis.metrics,
            sessionData: gameData.sessionData
          });
          analysisResults.behavioral = behavioralResult;
          console.log('   ✅ Análise comportamental concluída');
        } catch (error) {
          console.log(`   ⚠️ Análise comportamental com erro: ${error.message}`);
        }

        // Análise terapêutica
        try {
          console.log('   🏥 Executando análise terapêutica...');
          const therapeuticResult = await therapeuticAnalyzer.analyzeTherapeuticProgress({
            gameData,
            cognitiveAnalysis: analysisResults.cognitive,
            behavioralAnalysis: analysisResults.behavioral
          });
          analysisResults.therapeutic = therapeuticResult;
          console.log('   ✅ Análise terapêutica concluída');
        } catch (error) {
          console.log(`   ⚠️ Análise terapêutica com erro: ${error.message}`);
        }

        // Análise de progresso
        try {
          console.log('   📈 Executando análise de progresso...');
          const progressResult = await progressAnalyzer.analyzeProgress({
            currentSession: gameData,
            historicalData: [gameData], // Simulando histórico
            gameType
          });
          analysisResults.progress = progressResult;
          console.log('   ✅ Análise de progresso concluída');
        } catch (error) {
          console.log(`   ⚠️ Análise de progresso com erro: ${error.message}`);
        }

        // FASE 3: Orquestração de análise
        console.log('🎭 Fase 3: Orquestrando análises...');
        try {
          const orchestratedAnalysis = await analysisOrchestrator.orchestrateAnalysis({
            gameType,
            gameData,
            processingResult,
            cognitiveAnalysis: analysisResults.cognitive,
            behavioralAnalysis: analysisResults.behavioral,
            therapeuticAnalysis: analysisResults.therapeutic,
            progressAnalysis: analysisResults.progress
          });
          
          analysisResults.orchestrated = orchestratedAnalysis;
          console.log('✅ Orquestração de análise concluída');
          console.log(`   🎯 Insights integrados: ${orchestratedAnalysis?.insights?.length || 0}`);
          console.log(`   💡 Recomendações: ${orchestratedAnalysis?.recommendations?.length || 0}`);
        } catch (error) {
          console.log(`   ⚠️ Orquestração com erro: ${error.message}`);
        }

        allAnalysisResults.push(analysisResults);
        console.log(`✅ Processamento completo de ${gameType} finalizado!`);
        
      } catch (error) {
        console.error(`❌ Erro geral no processamento de ${gameType}:`, error.message);
      }
    }

    // === ETAPA 3: ORQUESTRAÇÃO DE SISTEMA ===
    console.log(`\n\n🌟 ETAPA 3: Orquestração de sistema global...\n`);
    
    try {
      console.log('🔗 Integrando todos os resultados no SystemOrchestrator...');
      
      const globalAnalysis = await systemOrchestrator.orchestrateGlobalAnalysis({
        userId: 'test-user-complete-123',
        childId: 'test-child-complete-456',
        sessionResults: allAnalysisResults,
        analysisDate: new Date().toISOString()
      });

      console.log('✅ Orquestração global concluída!');
      console.log(`📊 Jogos analisados: ${allAnalysisResults.length}`);
      console.log(`🧠 Perfil cognitivo global: ${globalAnalysis?.cognitiveProfile ? 'Gerado' : 'Não disponível'}`);
      console.log(`🎯 Recomendações globais: ${globalAnalysis?.globalRecommendations?.length || 0}`);
      console.log(`📈 Métricas agregadas: ${Object.keys(globalAnalysis?.aggregatedMetrics || {}).length}`);

      // === ETAPA 4: RESUMO FINAL ===
      console.log(`\n\n📋 === RESUMO FINAL DA INTEGRAÇÃO COMPLETA ===\n`);
      
      let successfulProcessing = 0;
      let successfulCognitive = 0;
      let successfulBehavioral = 0;
      let successfulTherapeutic = 0;
      let successfulProgress = 0;
      let successfulOrchestration = 0;

      allAnalysisResults.forEach(result => {
        if (result.processing?.success) successfulProcessing++;
        if (result.cognitive) successfulCognitive++;
        if (result.behavioral) successfulBehavioral++;
        if (result.therapeutic) successfulTherapeutic++;
        if (result.progress) successfulProgress++;
        if (result.orchestrated) successfulOrchestration++;
      });

      console.log(`🎮 Jogos testados: ${Object.keys(testGameData).length}`);
      console.log(`📊 Processamento (Coletores + Processadores): ${successfulProcessing}/${allAnalysisResults.length} sucessos`);
      console.log(`🧠 Análise Cognitiva: ${successfulCognitive}/${allAnalysisResults.length} sucessos`);
      console.log(`👤 Análise Comportamental: ${successfulBehavioral}/${allAnalysisResults.length} sucessos`);
      console.log(`🏥 Análise Terapêutica: ${successfulTherapeutic}/${allAnalysisResults.length} sucessos`);
      console.log(`📈 Análise de Progresso: ${successfulProgress}/${allAnalysisResults.length} sucessos`);
      console.log(`🎭 Orquestração de Análise: ${successfulOrchestration}/${allAnalysisResults.length} sucessos`);
      console.log(`🌟 Orquestração Global: ${globalAnalysis ? 'Sucesso' : 'Falha'}`);
      
      const overallSuccess = (successfulProcessing + successfulCognitive + successfulBehavioral + 
                            successfulTherapeutic + successfulProgress + successfulOrchestration) / 
                            (allAnalysisResults.length * 6);
                            
      console.log(`\n🎯 Taxa de sucesso geral: ${Math.round(overallSuccess * 100)}%`);
      
      if (overallSuccess >= 0.8) {
        console.log('🎉 INTEGRAÇÃO COMPLETA FUNCIONANDO EXCELENTEMENTE!');
      } else if (overallSuccess >= 0.6) {
        console.log('✅ INTEGRAÇÃO COMPLETA FUNCIONANDO BEM!');
      } else {
        console.log('⚠️ INTEGRAÇÃO PARCIAL - NECESSÁRIO AJUSTES');
      }

    } catch (error) {
      console.error('❌ Erro na orquestração global:', error.message);
    }

  } catch (error) {
    console.error('💥 Erro crítico na integração completa:', error);
    console.error('Stack:', error.stack);
  }
}

// Executar o teste
testCompleteAnalysisChain()
  .then(() => {
    console.log('\n🏁 Teste de integração completa finalizado!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Falha crítica no teste:', error);
    process.exit(1);
  });
