/**
 * 🎯 ATTENTIONAL SELECTIVITY COLLECTOR
 * Coletor especializado em análise de atenção seletiva e filtro perceptual para ColorMatch
 * Portal Betina V3
 */

export class AttentionalSelectivityCollector {
  constructor() {
    this.attentionTypes = {
      focused: 'atenção focada',
      selective: 'atenção seletiva',
      divided: 'atenção dividida',
      sustained: 'atenção sustentada',
      alternating: 'atenção alternada'
    };
    
    this.selectivityLevels = {
      excellent: { min: 0.9, label: 'excelente' },
      good: { min: 0.75, label: 'boa' },
      average: { min: 0.60, label: 'média' },
      poor: { min: 0.40, label: 'baixa' },
      critical: { min: 0.0, label: 'crítica' }
    };
    
    this.distractorTypes = {
      visual: 'distratores visuais',
      color: 'distratores de cor',
      spatial: 'distratores espaciais',
      temporal: 'distratores temporais'
    };
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} data - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de atenção seletiva
   */
  collect(data) {
    return this.analyze(data);
  }
  
  async analyze(data) {
    if (!data || !data.attentionData) {
      console.warn('AttentionalSelectivityCollector: Dados inválidos recebidos', data);
      return {
        selectiveAttention: 0.7,
        focusedAttention: 0.7,
        inhibitoryControl: 0.7,
        distractorResistance: 0.7,
        attentionalFlexibility: 0.7,
        vigilance: 0.7,
        executiveAttention: 0.7,
        attentionalCapacity: 0.7
      };
    }

    return {
      selectiveAttention: this.assessSelectiveAttention(data),
      focusedAttention: this.assessFocusedAttention(data),
      inhibitoryControl: this.assessInhibitoryControl(data),
      distractorResistance: this.assessDistractorResistance(data),
      attentionalFlexibility: this.assessAttentionalFlexibility(data),
      vigilance: this.assessVigilance(data),
      executiveAttention: this.assessExecutiveAttention(data),
      attentionalCapacity: this.calculateAttentionalCapacity(data),
      attentionProfile: this.generateAttentionProfile(data),
      filteringEfficiency: this.calculateFilteringEfficiency(data)
    };
  }

  assessSelectiveAttention(data) {
    const interactions = data.attentionData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Avaliar capacidade de selecionar informações relevantes
    const targetedInteractions = interactions.filter(i => i.isTarget);
    const nonTargetedInteractions = interactions.filter(i => !i.isTarget);
    
    // Precisão na seleção de alvos
    const targetAccuracy = targetedInteractions.length > 0 ? 
      targetedInteractions.filter(i => i.correct).length / targetedInteractions.length : 0.5;
    
    // Capacidade de ignorar não-alvos
    const nonTargetRejection = nonTargetedInteractions.length > 0 ? 
      nonTargetedInteractions.filter(i => !i.selected).length / nonTargetedInteractions.length : 0.8;
    
    // Combinar métricas
    const selectivityScore = (targetAccuracy * 0.6) + (nonTargetRejection * 0.4);
    
    // Ajustar por velocidade de seleção
    const selectionSpeed = this.calculateSelectionSpeed(targetedInteractions);
    const speedBonus = selectionSpeed > 0.8 ? 0.1 : selectionSpeed > 0.6 ? 0.05 : 0;
    
    return Math.max(0, Math.min(1, selectivityScore + speedBonus));
  }

  assessFocusedAttention(data) {
    const interactions = data.attentionData.interactions || [];
    const sessionDuration = data.sessionDuration || 0;
    
    if (interactions.length === 0) return 0.7;
    
    // Medir consistência da atenção focada
    const focusWindows = this.divideIntoFocusWindows(interactions, 30000); // janelas de 30s
    
    let focusScore = 0;
    
    focusWindows.forEach(window => {
      const windowAccuracy = window.filter(i => i.correct).length / window.length;
      const windowConsistency = this.calculateWindowConsistency(window);
      
      const windowFocus = (windowAccuracy * 0.7) + (windowConsistency * 0.3);
      focusScore += windowFocus;
    });
    
    const avgFocusScore = focusWindows.length > 0 ? focusScore / focusWindows.length : 0.7;
    
    // Ajustar pela duração da sessão (sessões mais longas testam melhor o foco)
    const durationFactor = Math.min(1, sessionDuration / 300000); // 5 minutos como referência
    const focusStability = this.calculateFocusStability(focusWindows);
    
    return Math.max(0, Math.min(1, avgFocusScore * (0.8 + 0.2 * durationFactor) * focusStability));
  }

  assessInhibitoryControl(data) {
    const interactions = data.attentionData.interactions || [];
    const inhibitionTasks = interactions.filter(i => i.requiresInhibition);
    
    if (inhibitionTasks.length === 0) {
      // Estimar controle inibitório baseado em padrões gerais
      return this.estimateInhibitoryControl(interactions);
    }
    
    // Calcular precisão em tarefas que requerem inibição
    const inhibitionAccuracy = inhibitionTasks.filter(i => i.correct).length / inhibitionTasks.length;
    
    // Avaliar velocidade de inibição (tempo para suprimir resposta incorreta)
    const inhibitionSpeed = this.calculateInhibitionSpeed(inhibitionTasks);
    
    // Avaliar resistência à interferência
    const interferenceResistance = this.calculateInterferenceResistance(interactions);
    
    const inhibitoryScore = (
      inhibitionAccuracy * 0.4 +
      inhibitionSpeed * 0.3 +
      interferenceResistance * 0.3
    );
    
    return Math.max(0, Math.min(1, inhibitoryScore));
  }

  assessDistractorResistance(data) {
    const interactions = data.attentionData.interactions || [];
    const distractorData = data.attentionData.distractors || [];
    
    if (distractorData.length === 0) {
      // Estimar resistência baseado na consistência geral
      return this.estimateDistractorResistance(interactions);
    }
    
    let resistanceScore = 0;
    let resistanceTests = 0;
    
    // Avaliar resistência por tipo de distrator
    Object.keys(this.distractorTypes).forEach(type => {
      const typeDistractors = distractorData.filter(d => d.type === type);
      
      if (typeDistractors.length > 0) {
        const typeResistance = this.calculateTypeSpecificResistance(typeDistractors, interactions);
        resistanceScore += typeResistance;
        resistanceTests++;
      }
    });
    
    const avgResistance = resistanceTests > 0 ? resistanceScore / resistanceTests : 0.7;
    
    // Bonus por resistência consistente
    const consistencyBonus = this.calculateResistanceConsistency(distractorData, interactions) * 0.1;
    
    return Math.max(0, Math.min(1, avgResistance + consistencyBonus));
  }

  assessAttentionalFlexibility(data) {
    const interactions = data.attentionData.interactions || [];
    const contextChanges = data.attentionData.contextChanges || [];
    
    if (contextChanges.length === 0) {
      // Estimar flexibilidade baseado em mudanças de padrão
      return this.estimateFlexibilityFromPatterns(interactions);
    }
    
    let flexibilityScore = 0;
    
    contextChanges.forEach(change => {
      const preChangePerformance = this.getPerformanceBeforeChange(interactions, change);
      const postChangePerformance = this.getPerformanceAfterChange(interactions, change);
      
      // Flexibilidade = capacidade de adaptar rapidamente após mudança
      const adaptationSpeed = this.calculateAdaptationSpeed(preChangePerformance, postChangePerformance);
      const adaptationEffectiveness = this.calculateAdaptationEffectiveness(postChangePerformance);
      
      const changeFlexibility = (adaptationSpeed * 0.6) + (adaptationEffectiveness * 0.4);
      flexibilityScore += changeFlexibility;
    });
    
    return Math.max(0, Math.min(1, flexibilityScore / contextChanges.length));
  }

  assessVigilance(data) {
    const interactions = data.attentionData.interactions || [];
    const sessionDuration = data.sessionDuration || 0;
    
    if (interactions.length === 0 || sessionDuration < 60000) return 0.7; // Mínimo 1 minuto
    
    // Dividir sessão em segmentos temporais
    const timeSegments = this.divideIntoTimeSegments(interactions, sessionDuration, 5);
    
    if (timeSegments.length < 3) return 0.7;
    
    // Calcular performance em cada segmento
    const segmentPerformances = timeSegments.map(segment => {
      const accuracy = segment.filter(i => i.correct).length / segment.length;
      const avgResponseTime = this.calculateAverageResponseTime(segment);
      
      return {
        accuracy: accuracy,
        responseTime: avgResponseTime,
        alertness: accuracy * (1 - Math.min(avgResponseTime / 4000, 1)) // Penalizar tempos muito altos
      };
    });
    
    // Analisar decaimento da vigilância ao longo do tempo
    const vigilanceDecrement = this.calculateVigilanceDecrement(segmentPerformances);
    const averageVigilance = segmentPerformances.reduce((sum, perf) => sum + perf.alertness, 0) / segmentPerformances.length;
    
    // Vigilância = performance média - decaimento
    const vigilanceScore = averageVigilance * (1 - vigilanceDecrement);
    
    return Math.max(0, Math.min(1, vigilanceScore));
  }

  assessExecutiveAttention(data) {
    const interactions = data.attentionData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Atenção executiva = controle top-down + resolução de conflitos
    const conflictResolution = this.assessConflictResolution(interactions);
    const topDownControl = this.assessTopDownControl(interactions);
    const cognitiveFlexibility = this.assessCognitiveFlexibility(interactions);
    
    const executiveScore = (
      conflictResolution * 0.4 +
      topDownControl * 0.3 +
      cognitiveFlexibility * 0.3
    );
    
    return Math.max(0, Math.min(1, executiveScore));
  }

  calculateAttentionalCapacity(data) {
    const interactions = data.attentionData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Capacidade = quantidade de informação que pode ser processada simultaneamente
    const simultaneousProcessing = this.calculateSimultaneousProcessing(interactions);
    const multitaskingAbility = this.calculateMultitaskingAbility(interactions);
    const workingMemoryCapacity = this.estimateWorkingMemoryCapacity(interactions);
    
    const capacityScore = (
      simultaneousProcessing * 0.4 +
      multitaskingAbility * 0.3 +
      workingMemoryCapacity * 0.3
    );
    
    return Math.max(0, Math.min(1, capacityScore));
  }

  generateAttentionProfile(data) {
    const results = {
      selectiveAttention: this.assessSelectiveAttention(data),
      focusedAttention: this.assessFocusedAttention(data),
      inhibitoryControl: this.assessInhibitoryControl(data),
      distractorResistance: this.assessDistractorResistance(data),
      attentionalFlexibility: this.assessAttentionalFlexibility(data)
    };
    
    // Identificar pontos fortes e fracos
    const strengths = Object.entries(results)
      .filter(([key, value]) => value > 0.75)
      .map(([key]) => key);
    
    const weaknesses = Object.entries(results)
      .filter(([key, value]) => value < 0.5)
      .map(([key]) => key);
    
    // Determinar perfil dominante
    const dominantAttention = Object.entries(results)
      .sort((a, b) => b[1] - a[1])[0];
    
    return {
      dominantType: dominantAttention[0],
      dominantScore: dominantAttention[1],
      strengths: strengths,
      weaknesses: weaknesses,
      overallProfile: this.classifyAttentionProfile(results)
    };
  }

  calculateFilteringEfficiency(data) {
    const interactions = data.attentionData.interactions || [];
    
    if (interactions.length === 0) return 0.7;
    
    // Eficiência de filtragem = capacidade de processar apenas informações relevantes
    const relevantProcessing = interactions.filter(i => i.isRelevant).length;
    const irrelevantProcessing = interactions.filter(i => !i.isRelevant && i.processed).length;
    
    const totalRelevant = interactions.filter(i => i.isRelevant).length;
    const totalIrrelevant = interactions.filter(i => !i.isRelevant).length;
    
    const relevantEfficiency = totalRelevant > 0 ? relevantProcessing / totalRelevant : 1;
    const filteringEfficiency = totalIrrelevant > 0 ? 1 - (irrelevantProcessing / totalIrrelevant) : 1;
    
    return (relevantEfficiency * 0.6) + (filteringEfficiency * 0.4);
  }

  // Métodos auxiliares
  calculateSelectionSpeed(interactions) {
    if (interactions.length === 0) return 0.5;
    
    const selectionTimes = interactions
      .filter(i => i.responseTime && i.correct)
      .map(i => i.responseTime);
    
    if (selectionTimes.length === 0) return 0.5;
    
    const avgTime = selectionTimes.reduce((sum, time) => sum + time, 0) / selectionTimes.length;
    
    // Normalizar velocidade (1000ms = velocidade ideal)
    const speedScore = Math.max(0, 1 - (avgTime - 1000) / 3000);
    return Math.min(1, speedScore);
  }

  divideIntoFocusWindows(interactions, windowSize) {
    const windows = [];
    let currentWindow = [];
    let windowStart = null;
    
    interactions.forEach(interaction => {
      const timestamp = new Date(interaction.timestamp).getTime();
      
      if (windowStart === null) {
        windowStart = timestamp;
        currentWindow = [interaction];
      } else if (timestamp - windowStart <= windowSize) {
        currentWindow.push(interaction);
      } else {
        if (currentWindow.length > 0) {
          windows.push(currentWindow);
        }
        windowStart = timestamp;
        currentWindow = [interaction];
      }
    });
    
    if (currentWindow.length > 0) {
      windows.push(currentWindow);
    }
    
    return windows;
  }

  calculateWindowConsistency(window) {
    if (window.length < 2) return 1;
    
    const accuracies = window.map(i => i.correct ? 1 : 0);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    
    // Consistência maior = menor variância
    return Math.max(0, 1 - variance);
  }

  calculateFocusStability(focusWindows) {
    if (focusWindows.length < 2) return 1;
    
    const windowScores = focusWindows.map(window => {
      return window.filter(i => i.correct).length / window.length;
    });
    
    const mean = windowScores.reduce((sum, score) => sum + score, 0) / windowScores.length;
    const variance = windowScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / windowScores.length;
    
    // Estabilidade = 1 - coeficiente de variação
    const coefficientOfVariation = mean > 0 ? Math.sqrt(variance) / mean : 0;
    return Math.max(0, 1 - coefficientOfVariation);
  }

  estimateInhibitoryControl(interactions) {
    // Estimar controle inibitório baseado em padrões de erro
    const totalInteractions = interactions.length;
    const impulsiveErrors = interactions.filter(i => 
      !i.correct && i.responseTime && i.responseTime < 800
    ).length;
    
    // Menor número de erros impulsivos = melhor controle inibitório
    const inhibitionScore = totalInteractions > 0 ? 1 - (impulsiveErrors / totalInteractions) : 0.7;
    
    return Math.max(0.3, Math.min(1, inhibitionScore));
  }

  calculateInhibitionSpeed(inhibitionTasks) {
    const inhibitionTimes = inhibitionTasks
      .filter(i => i.correct && i.inhibitionTime)
      .map(i => i.inhibitionTime);
    
    if (inhibitionTimes.length === 0) return 0.5;
    
    const avgInhibitionTime = inhibitionTimes.reduce((sum, time) => sum + time, 0) / inhibitionTimes.length;
    
    // Velocidade ideal de inibição ~ 1500ms
    const speedScore = Math.max(0, 1 - Math.abs(avgInhibitionTime - 1500) / 2000);
    return Math.min(1, speedScore);
  }

  calculateInterferenceResistance(interactions) {
    const interferenceTrials = interactions.filter(i => i.hasInterference);
    
    if (interferenceTrials.length === 0) return 0.7;
    
    const resistanceScore = interferenceTrials.filter(i => i.correct).length / interferenceTrials.length;
    return resistanceScore;
  }

  estimateDistractorResistance(interactions) {
    // Estimar resistência baseado na consistência da performance
    if (interactions.length < 5) return 0.7;
    
    const consistencyScore = this.calculateOverallConsistency(interactions);
    
    // Consistência alta sugere boa resistência a distratores
    return Math.max(0.4, Math.min(1, consistencyScore));
  }

  calculateTypeSpecificResistance(typeDistractors, interactions) {
    // Calcular resistência específica por tipo de distrator
    let resistanceSum = 0;
    
    typeDistractors.forEach(distractor => {
      const distractorTrials = interactions.filter(i => 
        i.timestamp >= distractor.startTime && i.timestamp <= distractor.endTime
      );
      
      if (distractorTrials.length > 0) {
        const accuracy = distractorTrials.filter(i => i.correct).length / distractorTrials.length;
        resistanceSum += accuracy;
      }
    });
    
    return typeDistractors.length > 0 ? resistanceSum / typeDistractors.length : 0.7;
  }

  calculateResistanceConsistency(distractorData, interactions) {
    // Medir consistência da resistência a distratores
    const resistanceScores = distractorData.map(distractor => {
      const distractorTrials = interactions.filter(i => 
        i.timestamp >= distractor.startTime && i.timestamp <= distractor.endTime
      );
      
      return distractorTrials.length > 0 ? 
        distractorTrials.filter(i => i.correct).length / distractorTrials.length : 0;
    });
    
    if (resistanceScores.length < 2) return 1;
    
    const mean = resistanceScores.reduce((sum, score) => sum + score, 0) / resistanceScores.length;
    const variance = resistanceScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / resistanceScores.length;
    
    return Math.max(0, 1 - variance);
  }

  estimateFlexibilityFromPatterns(interactions) {
    // Estimar flexibilidade atencional baseado em mudanças de padrão
    if (interactions.length < 10) return 0.7;
    
    const patterns = this.identifyAttentionPatterns(interactions);
    const patternChanges = this.countPatternChanges(patterns);
    
    // Flexibilidade moderada = mudanças apropriadas sem instabilidade excessiva
    const changeRate = patternChanges / patterns.length;
    const flexibilityScore = changeRate > 0.1 && changeRate < 0.4 ? 0.8 : 0.6;
    
    return flexibilityScore;
  }

  getPerformanceBeforeChange(interactions, change) {
    const beforeChange = interactions.filter(i => 
      new Date(i.timestamp).getTime() < change.timestamp &&
      new Date(i.timestamp).getTime() > change.timestamp - 30000 // 30s antes
    );
    
    return beforeChange.length > 0 ? 
      beforeChange.filter(i => i.correct).length / beforeChange.length : 0.5;
  }

  getPerformanceAfterChange(interactions, change) {
    const afterChange = interactions.filter(i => 
      new Date(i.timestamp).getTime() > change.timestamp &&
      new Date(i.timestamp).getTime() < change.timestamp + 30000 // 30s depois
    );
    
    return afterChange.length > 0 ? 
      afterChange.filter(i => i.correct).length / afterChange.length : 0.5;
  }

  calculateAdaptationSpeed(prePerformance, postPerformance) {
    // Velocidade de adaptação baseada em quão rapidamente o desempenho se recupera
    const recoveryRate = postPerformance - prePerformance;
    return Math.max(0, 0.5 + recoveryRate);
  }

  calculateAdaptationEffectiveness(postPerformance) {
    // Efetividade da adaptação = quão bem se adapta ao novo contexto
    return postPerformance;
  }

  divideIntoTimeSegments(interactions, sessionDuration, numSegments) {
    const segmentDuration = sessionDuration / numSegments;
    const segments = [];
    
    for (let i = 0; i < numSegments; i++) {
      const segmentStart = i * segmentDuration;
      const segmentEnd = (i + 1) * segmentDuration;
      
      const segmentInteractions = interactions.filter(interaction => {
        const relativeTime = new Date(interaction.timestamp).getTime() - new Date(interactions[0].timestamp).getTime();
        return relativeTime >= segmentStart && relativeTime < segmentEnd;
      });
      
      if (segmentInteractions.length > 0) {
        segments.push(segmentInteractions);
      }
    }
    
    return segments;
  }

  calculateVigilanceDecrement(segmentPerformances) {
    if (segmentPerformances.length < 2) return 0;
    
    const firstSegment = segmentPerformances[0].alertness;
    const lastSegment = segmentPerformances[segmentPerformances.length - 1].alertness;
    
    const decrement = Math.max(0, firstSegment - lastSegment);
    return Math.min(0.5, decrement); // Máximo de 50% de decaimento
  }

  calculateAverageResponseTime(interactions) {
    const times = interactions
      .filter(i => i.responseTime && i.responseTime > 0)
      .map(i => i.responseTime);
    
    return times.length > 0 ? times.reduce((sum, time) => sum + time, 0) / times.length : 2000;
  }

  assessConflictResolution(interactions) {
    const conflictTrials = interactions.filter(i => i.hasConflict);
    
    if (conflictTrials.length === 0) return 0.7;
    
    const conflictAccuracy = conflictTrials.filter(i => i.correct).length / conflictTrials.length;
    const conflictSpeed = this.calculateAverageResponseTime(conflictTrials.filter(i => i.correct));
    
    // Resolução eficaz = precisão + velocidade adequada
    const speedScore = Math.max(0, 1 - (conflictSpeed - 2000) / 3000);
    
    return (conflictAccuracy * 0.7) + (speedScore * 0.3);
  }

  assessTopDownControl(interactions) {
    // Controle top-down = capacidade de direcionar atenção voluntariamente
    const controlledTrials = interactions.filter(i => i.requiresTopDownControl);
    
    if (controlledTrials.length === 0) return 0.7;
    
    const controlAccuracy = controlledTrials.filter(i => i.correct).length / controlledTrials.length;
    return controlAccuracy;
  }

  assessCognitiveFlexibility(interactions) {
    const flexibilityTrials = interactions.filter(i => i.requiresFlexibility);
    
    if (flexibilityTrials.length === 0) return 0.7;
    
    const flexibilityAccuracy = flexibilityTrials.filter(i => i.correct).length / flexibilityTrials.length;
    return flexibilityAccuracy;
  }

  calculateSimultaneousProcessing(interactions) {
    // Estimar capacidade de processamento simultâneo
    const simultaneousGroups = this.groupInteractionsByTime(interactions, 1000);
    const maxSimultaneous = Math.max(...simultaneousGroups.map(g => g.length), 1);
    
    // Normalizar (máximo típico = 7)
    return Math.min(maxSimultaneous / 7, 1);
  }

  calculateMultitaskingAbility(interactions) {
    const multitaskingTrials = interactions.filter(i => i.isMultitasking);
    
    if (multitaskingTrials.length === 0) return 0.7;
    
    const multitaskingAccuracy = multitaskingTrials.filter(i => i.correct).length / multitaskingTrials.length;
    return multitaskingAccuracy;
  }

  estimateWorkingMemoryCapacity(interactions) {
    // Estimar capacidade de memória de trabalho baseado em padrões de resposta
    const memoryLoadTrials = interactions.filter(i => i.memoryLoad && i.memoryLoad > 1);
    
    if (memoryLoadTrials.length === 0) return 0.7;
    
    const memoryAccuracy = memoryLoadTrials.filter(i => i.correct).length / memoryLoadTrials.length;
    return memoryAccuracy;
  }

  classifyAttentionProfile(results) {
    const avgScore = Object.values(results).reduce((sum, val) => sum + val, 0) / Object.keys(results).length;
    
    if (avgScore >= 0.85) return 'superior';
    if (avgScore >= 0.70) return 'above_average';
    if (avgScore >= 0.55) return 'average';
    if (avgScore >= 0.40) return 'below_average';
    return 'impaired';
  }

  calculateOverallConsistency(interactions) {
    if (interactions.length < 3) return 1;
    
    const accuracies = interactions.map(i => i.correct ? 1 : 0);
    const mean = accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length;
    const variance = accuracies.reduce((sum, acc) => sum + Math.pow(acc - mean, 2), 0) / accuracies.length;
    
    return Math.max(0, 1 - variance);
  }

  identifyAttentionPatterns(interactions) {
    // Identificar padrões de atenção ao longo do tempo
    const windowSize = 5;
    const patterns = [];
    
    for (let i = 0; i <= interactions.length - windowSize; i++) {
      const window = interactions.slice(i, i + windowSize);
      const accuracy = window.filter(w => w.correct).length / window.length;
      const avgTime = this.calculateAverageResponseTime(window);
      
      patterns.push({
        accuracy: accuracy,
        responseTime: avgTime,
        pattern: accuracy > 0.8 ? 'focused' : accuracy > 0.5 ? 'moderate' : 'distracted'
      });
    }
    
    return patterns;
  }

  countPatternChanges(patterns) {
    let changes = 0;
    
    for (let i = 1; i < patterns.length; i++) {
      if (patterns[i].pattern !== patterns[i-1].pattern) {
        changes++;
      }
    }
    
    return changes;
  }

  groupInteractionsByTime(interactions, timeWindow) {
    const groups = [];
    let currentGroup = [];
    let lastTimestamp = null;
    
    interactions.forEach(interaction => {
      const timestamp = new Date(interaction.timestamp).getTime();
      
      if (lastTimestamp === null || timestamp - lastTimestamp <= timeWindow) {
        currentGroup.push(interaction);
      } else {
        if (currentGroup.length > 0) {
          groups.push(currentGroup);
        }
        currentGroup = [interaction];
      }
      
      lastTimestamp = timestamp;
    });
    
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }
    
    return groups;
  }

  generateAttentionalInsights(results) {
    const insights = [];
    
    if (results.selectiveAttention < 0.6) {
      insights.push('Dificuldades na atenção seletiva - problemas para filtrar informações relevantes');
    }
    
    if (results.focusedAttention < 0.5) {
      insights.push('Limitações na manutenção do foco atencional');
    }
    
    if (results.inhibitoryControl < 0.6) {
      insights.push('Controle inibitório reduzido - dificuldade para suprimir respostas inadequadas');
    }
    
    if (results.distractorResistance < 0.5) {
      insights.push('Baixa resistência a distratores - alta susceptibilidade à interferência');
    }
    
    if (results.attentionalFlexibility < 0.6) {
      insights.push('Flexibilidade atencional limitada - dificuldade para adaptar o foco conforme necessário');
    }
    
    if (results.vigilance < 0.5) {
      insights.push('Problemas de vigilância - declínio do estado de alerta ao longo do tempo');
    }
    
    if (results.executiveAttention < 0.6) {
      insights.push('Controle executivo da atenção comprometido');
    }
    
    if (results.attentionalCapacity < 0.5) {
      insights.push('Capacidade atencional reduzida - limitações no processamento simultâneo');
    }
    
    return insights;
  }
}
