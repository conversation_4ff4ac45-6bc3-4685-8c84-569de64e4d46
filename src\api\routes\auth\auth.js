/**
 * Portal Betina V3 - Auth Routes
 * Endpoints para autenticação administrativa
 * @version 3.0.0
 */

import express from 'express';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { createLogger } from '../../../utils/logger.js';
import { asyncHandler, AppError } from '../../middleware/error/errorHandler.js';

const router = express.Router();
const logger = createLogger('auth-routes');

// Chaves administrativas (em produção, usar variáveis de ambiente)
const ADMIN_KEYS = {
  'betina2025_admin_key': {
    name: 'Portal Betina V3 Admin',
    permissions: ['admin', 'read', 'write', 'delete'],
    level: 'super_admin'
  }
};

const JWT_SECRET = process.env.JWT_SECRET || 'betina_v3_super_secret_key_2025';
const ADMIN_PASSWORDS = {
  'betina2025': {
    hash: '$2b$10$example_hash_replace_with_real', // Substituir por hash real
    user: {
      id: 'admin_001',
      username: 'admin',
      name: 'Administrador Portal Betina',
      email: '<EMAIL>',
      role: 'super_admin',
      permissions: ['admin', 'read', 'write', 'delete']
    }
  }
};

/**
 * POST /api/auth/admin-login
 * Login administrativo com token do banco de dados
 */
router.post('/admin-login', asyncHandler(async (req, res) => {
  const { password, adminKey } = req.body;

  if (!password || !adminKey) {
    throw new AppError('Password e adminKey são obrigatórios', 400);
  }

  try {
    // Verificar chave administrativa
    const keyInfo = ADMIN_KEYS[adminKey];
    if (!keyInfo) {
      throw new AppError('Chave administrativa inválida', 401);
    }

    // Verificar senha (em produção, buscar do banco de dados)
    const adminData = ADMIN_PASSWORDS[password];
    if (!adminData) {
      // Tentar buscar do banco de dados
      const dbUser = await findAdminInDatabase(password);
      if (!dbUser) {
        throw new AppError('Credenciais inválidas', 401);
      }
    }

    const user = adminData?.user || {
      id: 'admin_db',
      username: 'admin',
      name: 'Administrador Portal Betina',
      email: '<EMAIL>',
      role: 'super_admin',
      permissions: keyInfo.permissions
    };

    // Gerar token JWT
    const token = jwt.sign(
      {
        userId: user.id,
        username: user.username,
        role: user.role,
        permissions: user.permissions,
        adminKey: adminKey,
        level: keyInfo.level
      },
      JWT_SECRET,
      { 
        expiresIn: '24h',
        issuer: 'portal-betina-v3',
        audience: 'admin-dashboard'
      }
    );

    // Log da autenticação
    logger.info('Login administrativo realizado:', {
      userId: user.id,
      username: user.username,
      role: user.role,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    res.json({
      success: true,
      token,
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
        permissions: user.permissions
      },
      expiresIn: '24h',
      tokenType: 'Bearer'
    });

  } catch (error) {
    logger.error('Erro no login administrativo:', error);
    throw error;
  }
}));

/**
 * POST /api/auth/admin-token
 * Buscar token administrativo apenas com chave
 */
router.post('/admin-token', asyncHandler(async (req, res) => {
  const { adminKey } = req.body;

  if (!adminKey) {
    throw new AppError('AdminKey é obrigatório', 400);
  }

  // Verificar chave administrativa
  const keyInfo = ADMIN_KEYS[adminKey];
  if (!keyInfo) {
    throw new AppError('Chave administrativa inválida', 401);
  }

  // Gerar token temporário para acesso limitado
  const token = jwt.sign(
    {
      userId: 'temp_admin',
      role: 'admin_read_only',
      permissions: ['read'],
      adminKey: adminKey,
      level: 'temp_access',
      temporary: true
    },
    JWT_SECRET,
    { 
      expiresIn: '1h',
      issuer: 'portal-betina-v3',
      audience: 'admin-dashboard'
    }
  );

  res.json({
    success: true,
    token,
    temporary: true,
    expiresIn: '1h',
    permissions: ['read'],
    message: 'Token temporário gerado - acesso limitado'
  });
}));

/**
 * POST /api/auth/verify-token
 * Verificar se o token é válido
 */
router.post('/verify-token', asyncHandler(async (req, res) => {
  const { token } = req.body;

  if (!token) {
    throw new AppError('Token é obrigatório', 400);
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    res.json({
      success: true,
      valid: true,
      decoded: {
        userId: decoded.userId,
        username: decoded.username,
        role: decoded.role,
        permissions: decoded.permissions,
        level: decoded.level,
        temporary: decoded.temporary || false,
        expiresAt: new Date(decoded.exp * 1000)
      }
    });

  } catch (error) {
    res.json({
      success: false,
      valid: false,
      error: error.message
    });
  }
}));

/**
 * GET /api/auth/admin-profile
 * Buscar perfil do administrador logado
 */
router.get('/admin-profile', asyncHandler(async (req, res) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    throw new AppError('Token de autorização necessário', 401);
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Buscar dados adicionais do banco se necessário
    const profile = await getAdminProfile(decoded.userId);

    res.json({
      success: true,
      profile: {
        id: decoded.userId,
        username: decoded.username,
        role: decoded.role,
        permissions: decoded.permissions,
        level: decoded.level,
        temporary: decoded.temporary || false,
        ...profile
      }
    });

  } catch (error) {
    throw new AppError('Token inválido', 401);
  }
}));

// Funções auxiliares (implementar conforme a infraestrutura)
async function findAdminInDatabase(password) {
  try {
    // TODO: Implementar busca real no banco de dados
    // const admin = await Admin.findOne({ where: { password: hash } });
    // return admin;
    return null;
  } catch (error) {
    logger.error('Erro ao buscar admin no banco:', error);
    return null;
  }
}

async function getAdminProfile(userId) {
  try {
    // TODO: Implementar busca de perfil no banco
    // const profile = await AdminProfile.findOne({ where: { userId } });
    // return profile;
    return {
      lastLogin: new Date(),
      loginCount: 1,
      preferences: {}
    };
  } catch (error) {
    logger.error('Erro ao buscar perfil admin:', error);
    return {};
  }
}

export default router;
