# 🏗️ ARQUITETURA DE ANÁLISE - PORTAL BETINA V3

## 📋 ESTRUTURA ATUAL DOS SERVIÇOS

### **1. COLETORES DE DADOS (Data Collectors)**
```
📁 src/games/
├── ColorMatch/ColorMatchGame.jsx
├── MemoryGame/MemoryGame.jsx
├── MusicalSequence/MusicalSequenceGame.jsx
└── ... (outros jogos)

📁 src/hooks/
├── useMultisensoryIntegration.js
├── useTherapeuticOrchestrator.js
└── useUnifiedGameLogic.js
```

### **2. PROCESSADORES (Processors)**
```
📁 src/api/services/processors/
├── GameSpecificProcessors.js    ← Processa métricas específicas por jogo
├── GameAnalysisUtils.js         ← Utilitários de análise
├── BaseProcessorMethods.js      ← Métodos base para processamento
├── ProcessorProxy.js            ← Proxy para processadores
└── IGameProcessor.js            ← Interface para processadores
```

### **3. ANALISADORES (Analyzers) - ONDE SEUS ARQUIVOS SE ENCAIXAM**
```
📁 src/api/services/analysis/
├── ✅ BehavioralAnalyzer.js      ← Análise comportamental (TEA)
├── ✅ CognitiveAnalyzer.js       ← Análise cognitiva (memória, atenção)
├── ✅ TherapeuticAnalyzer.js     ← Análise terapêutica (progresso)
├── ✅ ProgressAnalyzer.js        ← Análise de progresso temporal
├── SessionAnalyzer.js           ← Análise de sessões
├── MetricsValidator.js          ← Validação de métricas
└── index.js                     ← Exportações centralizadas
```

### **4. ORQUESTRADORES (Orchestrators)**
```
📁 src/api/services/core/
├── SystemOrchestrator.js        ← Orquestrador principal do sistema
└── health/HealthCheckService.js  ← Monitoramento de saúde

📁 src/api/services/ai/
└── AIBrainOrchestrator.js       ← Orquestrador de IA
```

### **5. ADAPTADORES (Adaptive Services)**
```
📁 src/api/services/adaptive/
├── ✅ adaptiveEngine.js          ← Motor adaptativo principal
├── difficultyAdjuster.js        ← Ajuste de dificuldade
├── personalizedLearning.js      ← Aprendizado personalizado
└── index.js                     ← Exportações
```

## 🔄 FLUXO DE INTEGRAÇÃO DOS ANALISADORES

### **FASE 1: COLETA**
```javascript
// Jogos coletam métricas
const metrics = {
  accuracy: 0.85,
  responseTime: 2500,
  engagement: 0.9,
  childId: "child123",
  gameName: "MemoryGame"
};
```

### **FASE 2: PROCESSAMENTO**
```javascript
// GameSpecificProcessors processa métricas
const processedMetrics = GameSpecificProcessors.processGame(
  "MemoryGame", 
  metrics
);
```

### **FASE 3: ANÁLISE ESPECIALIZADA**
```javascript
// Analisadores especializados analisam os dados
const behavioralAnalysis = await BehavioralAnalyzer.analyze(processedMetrics);
const cognitiveAnalysis = await CognitiveAnalyzer.analyze(processedMetrics);
const therapeuticAnalysis = await TherapeuticAnalyzer.analyze(processedMetrics);
const progressAnalysis = await ProgressAnalyzer.analyze(processedMetrics);
```

### **FASE 4: ORQUESTRAÇÃO**
```javascript
// SystemOrchestrator coordena as análises
const systemAnalysis = await SystemOrchestrator.processGameMetrics(
  childId, 
  gameName, 
  metrics
);
```

### **FASE 5: IA E RELATÓRIOS**
```javascript
// AIBrainOrchestrator gera relatórios inteligentes
const aiReport = await AIBrainOrchestrator.processGameMetrics(
  gameName, 
  metrics, 
  multisensoryData
);
```

## 📊 RESPONSABILIDADES DOS ANALISADORES

### **BehavioralAnalyzer.js**
- **Função:** Detecta padrões comportamentais relacionados ao TEA
- **Entrada:** Métricas de interação, tempo de resposta, padrões de clique
- **Saída:** Sinais comportamentais, preferências sensoriais, alertas
- **Integração:** SystemOrchestrator → BehavioralAnalyzer → AIBrainOrchestrator

### **CognitiveAnalyzer.js**
- **Função:** Analisa habilidades cognitivas (memória, atenção, raciocínio)
- **Entrada:** Métricas de performance, acurácia, sequências
- **Saída:** Perfil cognitivo, forças/fraquezas, recomendações
- **Integração:** SystemOrchestrator → CognitiveAnalyzer → AIBrainOrchestrator

### **TherapeuticAnalyzer.js**
- **Função:** Avalia eficácia terapêutica e progresso
- **Entrada:** Histórico de sessões, métricas terapêuticas
- **Saída:** Indicadores de progresso, recomendações terapêuticas
- **Integração:** SystemOrchestrator → TherapeuticAnalyzer → AIBrainOrchestrator

### **ProgressAnalyzer.js**
- **Função:** Analisa progresso temporal e tendências
- **Entrada:** Dados históricos, métricas temporais
- **Saída:** Tendências, predições, marcos de desenvolvimento
- **Integração:** SystemOrchestrator → ProgressAnalyzer → AIBrainOrchestrator

## 🎯 INTEGRAÇÃO COM CACHE INTELIGENTE

### **Cache por Analisador**
```javascript
// Cada analisador usa cache inteligente
const cache = getCache({
  maxSize: 500,
  defaultTTL: 1800000, // 30 minutos
  strategy: 'LRU'
});

// Cache com tags para invalidação
cache.set(cacheKey, result, {
  tags: [`child_${childId}`, `analyzer_behavioral`, `date_${today}`],
  ttl: 1800000
});
```

## 🔧 PONTOS DE INTEGRAÇÃO NECESSÁRIOS

### **1. SystemOrchestrator.js**
- Deve importar e usar todos os analisadores
- Coordenar a execução em pipeline
- Agregar resultados de múltiplos analisadores

### **2. AIBrainOrchestrator.js**
- Deve receber análises consolidadas
- Gerar relatórios baseados em múltiplas análises
- Usar cache inteligente para otimização

### **3. AdaptiveEngine.js**
- Deve usar resultados dos analisadores
- Adaptar experiência baseada em análises
- Personalizar jogos conforme perfil detectado

## 🚀 PRÓXIMOS PASSOS

1. **Implementar cache inteligente** nos analisadores existentes
2. **Integrar analisadores** no SystemOrchestrator
3. **Conectar com AIBrainOrchestrator** para relatórios avançados
4. **Integrar com AdaptiveEngine** para personalização
5. **Adicionar métricas** ao HealthCheckService

## 📝 EXEMPLO DE INTEGRAÇÃO COMPLETA

```javascript
// No SystemOrchestrator
async processGameMetrics(childId, gameName, metrics) {
  // 1. Processa métricas
  const processed = await GameSpecificProcessors.processGame(gameName, metrics);
  
  // 2. Analisa com especialistas
  const analyses = await Promise.all([
    BehavioralAnalyzer.analyze(processed),
    CognitiveAnalyzer.analyze(processed),
    TherapeuticAnalyzer.analyze(processed),
    ProgressAnalyzer.analyze(processed)
  ]);
  
  // 3. Consolida resultados
  const consolidatedAnalysis = this.consolidateAnalyses(analyses);
  
  // 4. Envia para IA
  const aiReport = await AIBrainOrchestrator.processGameMetrics(
    gameName, 
    metrics, 
    consolidatedAnalysis
  );
  
  // 5. Adapta experiência
  const adaptations = await AdaptiveEngine.processSessionAdaptation(
    childId, 
    consolidatedAnalysis
  );
  
  return { consolidatedAnalysis, aiReport, adaptations };
}
```

Esta arquitetura garante que cada analisador tenha uma responsabilidade específica e trabalhe em conjunto para fornecer uma análise completa e inteligente do progresso da criança.
