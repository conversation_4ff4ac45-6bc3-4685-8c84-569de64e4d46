import React, { Suspense, lazy } from 'react'

// Lazy loading dos jogos para reduzir bundle size
const ContagemNumerosGame = lazy(() => import('../../games/ContagemNumeros/ContagemNumerosGame.jsx'))
const LetterRecognitionGame = lazy(() => import('../../games/LetterRecognition/LetterRecognitionGame.jsx'))
const MusicalSequenceGame = lazy(() => import('../../games/MusicalSequence/MusicalSequenceGame.jsx'))
const MemoryGame = lazy(() => import('../../games/MemoryGame/MemoryGame.jsx'))
const ColorMatchGame = lazy(() => import('../../games/ColorMatch/ColorMatchGame.jsx'))
const ImageAssociationGame = lazy(() => import('../../games/ImageAssociation/ImageAssociationGame.jsx'))
const PadroesVisuaisGame = lazy(() => import('../../games/PadroesVisuais/PadroesVisuaisGame.jsx'))
const QuebraCabecaGame = lazy(() => import('../../games/QuebraCabeca/QuebraCabecaGame.jsx'))
const CreativePaintingGame = lazy(() => import('../../games/CreativePainting/CreativePaintingGame.jsx'))

// Componente de carregamento otimizado
const GameLoadingFallback = () => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
    fontSize: '18px',
    fontFamily: 'Arial, sans-serif'
  }}>
    <div style={{ marginBottom: '20px', fontSize: '48px' }}>🎮</div>
    <div>Carregando jogo...</div>
  </div>
)

const GamePage = ({ gameId, onBack }) => {
  // Mapear IDs para componentes lazy-loaded
  const gameComponents = {
    'numbers': ContagemNumerosGame,
    'number-counting': ContagemNumerosGame,
    'contagem-numeros': ContagemNumerosGame,
    'letters': LetterRecognitionGame,
    'letter-recognition': LetterRecognitionGame,
    'music': MusicalSequenceGame,
    'musical-sequence': MusicalSequenceGame,
    'memory': MemoryGame,
    'memory-game': MemoryGame,
    'colors': ColorMatchGame,
    'color-match': ColorMatchGame,
    'images': ImageAssociationGame,
    'image-association': ImageAssociationGame,
    'patterns': PadroesVisuaisGame,
    'padroes-visuais': PadroesVisuaisGame,
    'quebra-cabeca': QuebraCabecaGame,
    'emotional-puzzle': QuebraCabecaGame, // Quebra-cabeça emocional
    'creative-painting': CreativePaintingGame,
    'pintura-criativa': CreativePaintingGame
  }

  // Se existe componente real, renderizar o jogo com Suspense
  const GameComponent = gameComponents[gameId]

  if (GameComponent) {
    return (
      <Suspense fallback={<GameLoadingFallback />}>
        <GameComponent onBack={onBack} />
      </Suspense>
    )
  }

  // Fallback para jogos não implementados
  const gameInfo = {
    'letter-recognition': {
      title: 'Reconhecimento de Letras',
      description: 'Jogo educativo para aprender o alfabeto',
      content: '🔤 Este é o jogo de reconhecimento de letras! Em breve estará totalmente funcional.'
    },
    'letters': {
      title: 'Reconhecimento de Letras',
      description: 'Jogo educativo para aprender o alfabeto',
      content: '🔤 Este é o jogo de reconhecimento de letras! Em breve estará totalmente funcional.'
    },
    'number-counting': {
      title: 'Contagem de Números',
      description: 'Jogo para aprender números e contagem',
      content: '🔢 Este é o jogo de contagem de números! Em breve estará totalmente funcional.'
    },
    'numbers': {
      title: 'Contagem de Números',
      description: 'Jogo para aprender números e contagem',
      content: '🔢 Este é o jogo de contagem de números! Em breve estará totalmente funcional.'
    },
    'musical-sequence': {
      title: 'Sequência Musical',
      description: 'Jogo de memória auditiva',
      content: '🎵 Este é o jogo de sequência musical! Em breve estará totalmente funcional.'
    },
    'music': {
      title: 'Sequência Musical',
      description: 'Jogo de memória auditiva',
      content: '🎵 Este é o jogo de sequência musical! Em breve estará totalmente funcional.'
    },
    'memory-game': {
      title: 'Jogo da Memória',
      description: 'Jogo clássico de memória',
      content: '🧠 Este é o jogo da memória! Em breve estará totalmente funcional.'
    },
    'memory': {
      title: 'Jogo da Memória',
      description: 'Jogo clássico de memória',
      content: '🧠 Este é o jogo da memória! Em breve estará totalmente funcional.'
    },
    'color-match': {
      title: 'Combinação de Cores',
      description: 'Jogo de combinação de cores',
      content: '🌈 Este é o jogo de combinação de cores! Em breve estará totalmente funcional.'
    },
    'colors': {
      title: 'Combinação de Cores',
      description: 'Jogo de combinação de cores',
      content: '🌈 Este é o jogo de combinação de cores! Em breve estará totalmente funcional.'
    },
    'image-association': {
      title: 'Associação de Imagens',
      description: 'Jogo de associação cognitiva',
      content: '🧩 Este é o jogo de associação de imagens! Em breve estará totalmente funcional.'
    },
    'images': {
      title: 'Associação de Imagens',
      description: 'Jogo de associação cognitiva',
      content: '🧩 Este é o jogo de associação de imagens! Em breve estará totalmente funcional.'
    }
  }

  const game = gameInfo[gameId] || {
    title: 'Jogo não encontrado',
    description: 'Este jogo ainda não foi implementado',
    content: '❌ Jogo não encontrado'
  }

  return (
    <div style={{
      padding: '2rem',
      textAlign: 'center',
      background: 'rgba(255, 255, 255, 0.95)',
      borderRadius: '1rem',
      margin: '2rem',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
    }}>
      <h1 style={{ color: '#0066cc', marginBottom: '1rem' }}>
        {game.title}
      </h1>
      <p style={{ color: '#666', marginBottom: '2rem', fontSize: '1.1rem' }}>
        {game.description}
      </p>
      <div style={{ 
        padding: '3rem', 
        background: '#f8f9fa', 
        borderRadius: '0.5rem',
        marginBottom: '2rem',
        fontSize: '1.2rem'
      }}>
        {game.content}
      </div>
    </div>
  )
}

export default GamePage
