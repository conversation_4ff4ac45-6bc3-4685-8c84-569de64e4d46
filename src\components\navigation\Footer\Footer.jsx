/**
 * @file Footer.jsx
 * @description Footer simples do Portal Betina
 * @version 3.0.0
 */

import React from 'react'
import styles from './Footer.module.css'

const activities = [
  { id: 'home', name: '<PERSON><PERSON><PERSON>', icon: '🏠' },
  { id: 'letter-recognition', name: '<PERSON><PERSON>', icon: '🔤' },
  { id: 'number-counting', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🔢' },
  { id: 'memory-game', name: 'Me<PERSON><PERSON><PERSON>', icon: '🧠' },
  { id: 'musical-sequence', name: 'Música', icon: '🎵' },
  { id: 'color-match', name: 'Cores', icon: '🌈' },
  { id: 'image-association', name: 'Imagens', icon: '🖼️' },
  { id: 'padroes-visuais', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🔷' },
  { id: 'quebra-cabeca', name: 'Puzzle', icon: '🧩' },
  { id: 'creative-painting', name: 'Arte', icon: '🎨' }
]

/**
 * Componente de rodapé com navegação para atividades
 */
function Footer({ currentActivity, onActivityChange }) {
  const handleActivityClick = (activityId, activityName) => {
    if (onActivityChange) {
      onActivityChange(activityId)
    }
  }

  return (
    <footer className={styles.footer}>
      <div className={styles.footerContent}>
        <div className={styles.navigationGrid}>
          {activities.map((activity) => (            <button
              key={activity.id}
              className={`${styles.navButton} ${currentActivity === activity.id ? styles.active : ''}`}
              onClick={() => handleActivityClick(activity.id, activity.name)}
              role="button"
              aria-label={`Navegar para ${activity.name}`}
            >
              <div className={styles.navIcon}>{activity.icon}</div>
              <div className={styles.navLabel}>{activity.name}</div>
            </button>
          ))}
        </div>
      </div>
    </footer>
  )
}

export default Footer
