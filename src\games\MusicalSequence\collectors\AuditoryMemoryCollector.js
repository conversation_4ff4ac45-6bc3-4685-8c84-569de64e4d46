/**
 * AuditoryMemoryCollector - Coleta dados sobre memória auditiva
 * Analisa padrões de retenção e recuperação de sequências auditivas
 */
export class AuditoryMemoryCollector {
  constructor() {
    this.memoryData = [];
    this.sequenceRetentionData = [];
    this.temporalPatterns = [];
    this.auditorySpanData = [];
    this.debugMode = true;
  }

  /**
   * Coleta dados de retenção de memória auditiva
   */
  collectMemoryRetention(interactionData) {
    try {
      // Validação de dados
      if (!interactionData || typeof interactionData !== 'object') {
        console.warn('AuditoryMemoryCollector: Dados de interação inválidos');
        return null;
      }

      const {
        sequence = [],
        playerResponse = [],
        sequenceLength = 0,
        responseTime = 0,
        isCorrect = false,
        partialCorrect = 0,
        attemptNumber = 0,
        difficulty = '',
        timestamp = Date.now()
      } = interactionData;

      const retentionData = {
        timestamp,
        sequenceLength,
        auditorySpan: this.calculateAuditorySpan(sequence, playerResponse),
        memoryDecay: this.calculateMemoryDecay(sequence, playerResponse),
        retentionRate: this.calculateRetentionRate(partialCorrect, sequenceLength),
        serialPositionEffect: this.analyzeSerialPositionEffect(sequence, playerResponse),
        responseLatency: responseTime,
        isCorrect,
        difficulty,
        attemptNumber,
        forgettingCurve: this.analyzeForgettingCurve(sequence, playerResponse)
      };

      this.memoryData.push(retentionData);
      
      if (this.debugMode) {
        console.log('🧠 AuditoryMemoryCollector - Dados de retenção coletados:', retentionData);
      }

      return retentionData;
    } catch (error) {
      console.error('Erro no AuditoryMemoryCollector.collectMemoryRetention:', error);
      return null;
    }
  }

  /**
   * Analisa padrões de sequência temporal
   */
  analyzeTemporalSequence(sequenceData) {
    try {
      if (!sequenceData || !Array.isArray(sequenceData.sequence)) {
        console.warn('AuditoryMemoryCollector: Dados de sequência inválidos');
        return null;
      }

      const {
        sequence = [],
        playerResponse = [],
        timeBetweenNotes = [],
        difficulty = '',
        timestamp = Date.now()
      } = sequenceData;

      const temporalAnalysis = {
        timestamp,
        sequencePattern: this.identifySequencePattern(sequence),
        temporalGrouping: this.analyzeTemporalGrouping(timeBetweenNotes),
        rhythmicAccuracy: this.assessRhythmicAccuracy(sequence, playerResponse),
        chunking: this.analyzeChunking(sequence, playerResponse),
        temporalOrder: this.analyzeTemporalOrder(sequence, playerResponse),
        difficulty
      };

      this.temporalPatterns.push(temporalAnalysis);

      if (this.debugMode) {
        console.log('⏰ AuditoryMemoryCollector - Análise temporal:', temporalAnalysis);
      }

      return temporalAnalysis;
    } catch (error) {
      console.error('Erro no AuditoryMemoryCollector.analyzeTemporalSequence:', error);
      return null;
    }
  }

  /**
   * Coleta dados sobre span auditivo (amplitude de memória)
   */
  collectAuditorySpan(spanData) {
    try {
      if (!spanData || typeof spanData !== 'object') {
        console.warn('AuditoryMemoryCollector: Dados de span inválidos');
        return null;
      }

      const {
        maxCorrectSequence = 0,
        consistentSpan = 0,
        improvementRate = 0,
        difficulty = '',
        sessionDuration = 0,
        timestamp = Date.now()
      } = spanData;

      const auditorySpan = {
        timestamp,
        maxSpan: maxCorrectSequence,
        consistentSpan,
        spanGrowth: this.calculateSpanGrowth(maxCorrectSequence),
        reliabilityIndex: this.calculateReliabilityIndex(consistentSpan, maxCorrectSequence),
        improvementRate,
        difficulty,
        sessionDuration,
        spanEfficiency: this.calculateSpanEfficiency(maxCorrectSequence, sessionDuration)
      };

      this.auditorySpanData.push(auditorySpan);

      if (this.debugMode) {
        console.log('📏 AuditoryMemoryCollector - Dados de span auditivo:', auditorySpan);
      }

      return auditorySpan;
    } catch (error) {
      console.error('Erro no AuditoryMemoryCollector.collectAuditorySpan:', error);
      return null;
    }
  }

  // Métodos auxiliares para análise
  calculateAuditorySpan(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) return 0;
    
    let correctCount = 0;
    for (let i = 0; i < Math.min(sequence.length, response.length); i++) {
      if (sequence[i] === response[i]) {
        correctCount++;
      } else {
        break;
      }
    }
    return correctCount;
  }

  calculateMemoryDecay(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) return 1;
    
    const totalItems = sequence.length;
    const correctItems = this.calculateAuditorySpan(sequence, response);
    return correctItems / totalItems;
  }

  calculateRetentionRate(partialCorrect, sequenceLength) {
    if (sequenceLength === 0) return 0;
    return (partialCorrect || 0) / sequenceLength;
  }

  analyzeSerialPositionEffect(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) {
      return { primacy: 0, recency: 0, middle: 0 };
    }

    const length = sequence.length;
    if (length < 3) return { primacy: 0, recency: 0, middle: 0 };

    const primacyCorrect = sequence[0] === response[0] ? 1 : 0;
    const recencyCorrect = sequence[length - 1] === response[length - 1] ? 1 : 0;
    
    let middleCorrect = 0;
    for (let i = 1; i < length - 1; i++) {
      if (sequence[i] === response[i]) middleCorrect++;
    }
    
    return {
      primacy: primacyCorrect,
      recency: recencyCorrect,
      middle: middleCorrect / Math.max(1, length - 2)
    };
  }

  analyzeForgettingCurve(sequence, response) {
    const accuracy = this.calculateAuditorySpan(sequence, response) / sequence.length;
    return {
      accuracy,
      retentionStrength: Math.pow(accuracy, 0.5), // Modelo simplificado da curva do esquecimento
      consolidationLevel: accuracy > 0.8 ? 'strong' : accuracy > 0.5 ? 'moderate' : 'weak'
    };
  }

  identifySequencePattern(sequence) {
    if (!Array.isArray(sequence) || sequence.length < 2) return 'none';
    
    // Analisa se há padrões repetitivos
    const patterns = {
      ascending: true,
      descending: true,
      repetitive: false,
      alternating: true
    };

    for (let i = 1; i < sequence.length; i++) {
      if (sequence[i] <= sequence[i-1]) patterns.ascending = false;
      if (sequence[i] >= sequence[i-1]) patterns.descending = false;
      if (i > 1 && sequence[i] === sequence[i-2]) patterns.alternating = false;
    }

    // Verifica repetições
    const uniqueElements = new Set(sequence).size;
    patterns.repetitive = uniqueElements < sequence.length * 0.7;

    if (patterns.ascending) return 'ascending';
    if (patterns.descending) return 'descending';
    if (patterns.alternating && sequence.length > 2) return 'alternating';
    if (patterns.repetitive) return 'repetitive';
    return 'random';
  }

  analyzeTemporalGrouping(timeBetweenNotes) {
    if (!Array.isArray(timeBetweenNotes) || timeBetweenNotes.length === 0) {
      return { groups: 0, avgGroupSize: 0 };
    }

    const avgTime = timeBetweenNotes.reduce((a, b) => a + b, 0) / timeBetweenNotes.length;
    const threshold = avgTime * 1.5;
    
    let groups = 1;
    for (const time of timeBetweenNotes) {
      if (time > threshold) groups++;
    }

    return {
      groups,
      avgGroupSize: (timeBetweenNotes.length + 1) / groups,
      temporalVariability: this.calculateVariability(timeBetweenNotes)
    };
  }

  assessRhythmicAccuracy(sequence, response) {
    // Simplificação: considera precisão temporal baseada na sequência
    if (!Array.isArray(sequence) || !Array.isArray(response)) return 0;
    
    const correctCount = this.calculateAuditorySpan(sequence, response);
    return correctCount / sequence.length;
  }

  analyzeChunking(sequence, response) {
    // Analisa se o usuário agrupa itens em chunks
    if (!Array.isArray(sequence) || sequence.length < 4) return { chunkSize: 1, efficiency: 0 };
    
    const possibleChunkSizes = [2, 3, 4];
    let bestChunkSize = 1;
    let bestAccuracy = 0;

    for (const chunkSize of possibleChunkSizes) {
      if (sequence.length % chunkSize === 0) {
        let chunkAccuracy = 0;
        const numChunks = sequence.length / chunkSize;
        
        for (let i = 0; i < numChunks; i++) {
          const chunkStart = i * chunkSize;
          let chunkCorrect = true;
          
          for (let j = 0; j < chunkSize; j++) {
            if (sequence[chunkStart + j] !== response[chunkStart + j]) {
              chunkCorrect = false;
              break;
            }
          }
          
          if (chunkCorrect) chunkAccuracy++;
        }
        
        const accuracy = chunkAccuracy / numChunks;
        if (accuracy > bestAccuracy) {
          bestAccuracy = accuracy;
          bestChunkSize = chunkSize;
        }
      }
    }

    return {
      chunkSize: bestChunkSize,
      efficiency: bestAccuracy,
      chunkingStrategy: bestChunkSize > 1 ? 'chunking' : 'sequential'
    };
  }

  analyzeTemporalOrder(sequence, response) {
    if (!Array.isArray(sequence) || !Array.isArray(response)) return { orderAccuracy: 0 };
    
    let orderErrors = 0;
    for (let i = 0; i < Math.min(sequence.length, response.length); i++) {
      if (sequence[i] !== response[i]) orderErrors++;
    }

    return {
      orderAccuracy: 1 - (orderErrors / sequence.length),
      orderErrors,
      temporalConfusion: orderErrors > sequence.length * 0.3
    };
  }

  calculateSpanGrowth(currentSpan) {
    if (this.auditorySpanData.length === 0) return 0;
    
    const previousSpan = this.auditorySpanData[this.auditorySpanData.length - 1]?.maxSpan || 0;
    return currentSpan - previousSpan;
  }

  calculateReliabilityIndex(consistentSpan, maxSpan) {
    if (maxSpan === 0) return 0;
    return consistentSpan / maxSpan;
  }

  calculateSpanEfficiency(span, duration) {
    if (duration === 0) return 0;
    return span / (duration / 1000); // span por segundo
  }

  calculateVariability(values) {
    if (!Array.isArray(values) || values.length === 0) return 0;
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  // Métodos de relatório
  getMemoryReport() {
    return {
      totalSessions: this.memoryData.length,
      averageRetentionRate: this.memoryData.reduce((sum, data) => sum + data.retentionRate, 0) / this.memoryData.length || 0,
      memorySpanTrend: this.auditorySpanData.map(data => data.maxSpan),
      consolidationLevels: this.memoryData.map(data => data.forgettingCurve.consolidationLevel),
      temporalPatterns: this.temporalPatterns.map(data => data.sequencePattern)
    };
  }

  clearData() {
    this.memoryData = [];
    this.sequenceRetentionData = [];
    this.temporalPatterns = [];
    this.auditorySpanData = [];
    
    if (this.debugMode) {
      console.log('🧠 AuditoryMemoryCollector - Dados limpos');
    }
  }
}
