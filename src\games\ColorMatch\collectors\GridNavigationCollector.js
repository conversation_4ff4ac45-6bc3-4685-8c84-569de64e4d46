/**
 * 🎯 GRID NAVIGATION COLLECTOR
 * Coletor especializado em análise de padrões de navegação no grid do ColorMatch
 * Portal Betina V3 - FASE 2.1
 */

export class GridNavigationCollector {
  constructor() {
    this.navigationPatterns = {
      linear: 'Linear (sequencial)',
      systematic: 'Sistem<PERSON><PERSON>o (metódico)',
      random: 'Aleatório',
      clustered: 'Agrupado (por região)',
      diagonal: 'Diagonal',
      spiral: 'Espiral',
      bouncing: 'Saltitante'
    };
    
    this.gridSizes = {
      6: { rows: 2, cols: 3, type: 'easy' },
      9: { rows: 3, cols: 3, type: 'medium' },
      12: { rows: 3, cols: 4, type: 'hard' }
    };
    
    this.spatialMetrics = {
      topLeft: 'Superior Esquerdo',
      topRight: 'Superior Direito',
      bottomLeft: 'Inferior Esquerdo',
      bottomRight: 'Inferior Direito',
      center: 'Centro',
      edges: 'Bordas'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Análise principal dos padrões de navegação no grid
   */
  async analyze(data) {
    try {
      console.log('🎯 GridNavigationCollector: Iniciando análise de navegação...');
      
      if (!this.validateGridData(data)) {
        return this.generateFallbackAnalysis('Dados de grid inválidos');
      }

      const navigationData = this.extractNavigationData(data);
      const gridConfig = this.determineGridConfig(navigationData.gridSize);
      
      // Análises principais
      const navigationPattern = this.analyzeNavigationPattern(navigationData.clickSequence, gridConfig);
      const spatialBias = this.analyzeSpatialBias(navigationData.clickSequence, gridConfig);
      const scanningStrategy = this.analyzeScanningStrategy(navigationData);
      const efficiencyMetrics = this.calculateEfficiencyMetrics(navigationData);
      const cognitiveLoad = this.assessCognitiveLoad(navigationData, gridConfig);

      const results = {
        // Métricas de navegação
        navigationPattern: navigationPattern.primaryPattern,
        patternConsistency: navigationPattern.consistency,
        scanningStrategy: scanningStrategy.strategy,
        
        // Métricas espaciais
        spatialBias: spatialBias.dominantRegion,
        spatialDistribution: spatialBias.distribution,
        coverageEfficiency: spatialBias.coverage,
        
        // Métricas de eficiência
        selectionEfficiency: efficiencyMetrics.efficiency,
        averageDistance: efficiencyMetrics.averageDistance,
        backtrackingRate: efficiencyMetrics.backtracking,
        
        // Métricas cognitivas
        cognitiveLoadIndex: cognitiveLoad.loadIndex,
        adaptabilityScore: cognitiveLoad.adaptability,
        strategicPlanning: cognitiveLoad.planning,
        
        // Métricas temporais
        responseTimeVariability: this.calculateResponseTimeVariability(navigationData),
        pausePatterns: this.analyzePausePatterns(navigationData),
        
        // Métricas de desenvolvimento
        visualMotorCoordination: this.assessVisualMotorCoordination(navigationData),
        spatialMemory: this.assessSpatialMemory(navigationData),
        executiveFunction: this.assessExecutiveFunction(navigationData),
        
        // Contexto da análise
        gridSize: navigationData.gridSize,
        totalClicks: navigationData.clickSequence.length,
        sessionDuration: navigationData.sessionDuration,
        difficulty: navigationData.difficulty,
        
        // Recomendações específicas
        recommendations: this.generateNavigationRecommendations({
          navigationPattern, spatialBias, efficiencyMetrics, cognitiveLoad
        }),
        
        // Metadados
        analysisTimestamp: new Date().toISOString(),
        collectorVersion: '2.1.0',
        dataQuality: this.assessDataQuality(navigationData)
      };

      console.log('✅ GridNavigationCollector: Análise concluída:', {
        pattern: results.navigationPattern,
        efficiency: results.selectionEfficiency,
        spatialBias: results.spatialBias,
        cognitiveLoad: results.cognitiveLoadIndex
      });

      return results;

    } catch (error) {
      console.error('❌ GridNavigationCollector: Erro na análise:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de entrada do grid
   */
  validateGridData(data) {
    if (!data) return false;
    
    // Verificar se tem dados de grid ou sequência de cliques
    const hasGridData = data.gameGrid || data.gridData || data.selectedItems;
    const hasClickSequence = data.clickSequence || data.interactions || data.selectedItems;
    
    return hasGridData && hasClickSequence;
  }

  /**
   * Extrai dados de navegação dos dados de entrada
   */
  extractNavigationData(data) {
    // Extrair sequência de cliques
    let clickSequence = [];
    
    if (data.selectedItems && Array.isArray(data.selectedItems)) {
      clickSequence = data.selectedItems.map((item, index) => ({
        position: item.itemIndex || index,
        timestamp: item.timestamp || Date.now(),
        responseTime: item.responseTime || 0,
        coordinates: this.calculateCoordinates(item.itemIndex || index, data.gameGrid?.length || 9),
        correct: item.correct || false
      }));
    } else if (data.clickSequence) {
      clickSequence = data.clickSequence;
    }

    // Determinar tamanho do grid
    const gridSize = data.gameGrid?.length || data.gridSize || 9;
    
    return {
      clickSequence,
      gridSize,
      difficulty: data.difficulty || 'medium',
      sessionDuration: data.sessionDuration || 0,
      gameGrid: data.gameGrid || [],
      totalInteractions: clickSequence.length
    };
  }

  /**
   * Calcula coordenadas x,y baseado no índice do item
   */
  calculateCoordinates(index, gridSize) {
    const config = this.gridSizes[gridSize] || this.gridSizes[9];
    const row = Math.floor(index / config.cols);
    const col = index % config.cols;
    return { x: col, y: row, row, col };
  }

  /**
   * Determina configuração do grid baseado no tamanho
   */
  determineGridConfig(gridSize) {
    return this.gridSizes[gridSize] || this.gridSizes[9];
  }

  /**
   * Analisa padrão de navegação no grid
   */
  analyzeNavigationPattern(clickSequence, gridConfig) {
    if (clickSequence.length < 2) {
      return { primaryPattern: 'insufficient_data', consistency: 0 };
    }

    const patterns = {
      linear: 0,
      systematic: 0,
      random: 0,
      clustered: 0,
      diagonal: 0,
      spiral: 0
    };

    // Analisar sequência de movimentos
    for (let i = 1; i < clickSequence.length; i++) {
      const prev = clickSequence[i - 1].coordinates;
      const curr = clickSequence[i].coordinates;
      
      const movement = this.classifyMovement(prev, curr, gridConfig);
      patterns[movement.pattern] += movement.confidence;
    }

    // Determinar padrão dominante
    const totalMovements = clickSequence.length - 1;
    const normalizedPatterns = {};
    
    Object.keys(patterns).forEach(pattern => {
      normalizedPatterns[pattern] = patterns[pattern] / totalMovements;
    });

    const primaryPattern = Object.keys(normalizedPatterns).reduce((a, b) => 
      normalizedPatterns[a] > normalizedPatterns[b] ? a : b
    );

    const consistency = normalizedPatterns[primaryPattern];

    return {
      primaryPattern,
      consistency,
      patterns: normalizedPatterns,
      totalMovements
    };
  }

  /**
   * Classifica tipo de movimento entre duas posições
   */
  classifyMovement(prev, curr, gridConfig) {
    const deltaX = curr.x - prev.x;
    const deltaY = curr.y - prev.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Movimento linear (horizontal ou vertical)
    if ((deltaX === 0 && Math.abs(deltaY) === 1) || (deltaY === 0 && Math.abs(deltaX) === 1)) {
      return { pattern: 'linear', confidence: 0.9 };
    }

    // Movimento diagonal
    if (Math.abs(deltaX) === Math.abs(deltaY) && deltaX !== 0) {
      return { pattern: 'diagonal', confidence: 0.8 };
    }

    // Movimento sistemático (próximo)
    if (distance <= 1.5) {
      return { pattern: 'systematic', confidence: 0.7 };
    }

    // Movimento agrupado (mesma região)
    if (distance <= 2) {
      return { pattern: 'clustered', confidence: 0.6 };
    }

    // Movimento distante (aleatório ou saltitante)
    return { pattern: 'random', confidence: 0.5 };
  }

  /**
   * Analisa viés espacial na navegação
   */
  analyzeSpatialBias(clickSequence, gridConfig) {
    const regionCounts = {
      topLeft: 0,
      topRight: 0,
      bottomLeft: 0,
      bottomRight: 0,
      center: 0,
      edges: 0
    };

    clickSequence.forEach(click => {
      const region = this.determineRegion(click.coordinates, gridConfig);
      regionCounts[region]++;
    });

    const totalClicks = clickSequence.length;
    const distribution = {};
    
    Object.keys(regionCounts).forEach(region => {
      distribution[region] = regionCounts[region] / totalClicks;
    });

    const dominantRegion = Object.keys(distribution).reduce((a, b) => 
      distribution[a] > distribution[b] ? a : b
    );

    // Calcular cobertura do grid
    const uniquePositions = new Set(clickSequence.map(c => `${c.coordinates.x},${c.coordinates.y}`));
    const coverage = uniquePositions.size / (gridConfig.rows * gridConfig.cols);

    return {
      dominantRegion,
      distribution,
      coverage,
      uniformity: this.calculateUniformity(distribution)
    };
  }

  /**
   * Determina região espacial de uma coordenada
   */
  determineRegion(coords, gridConfig) {
    const { rows, cols } = gridConfig;
    const { x, y } = coords;

    // Centro
    const centerX = Math.floor(cols / 2);
    const centerY = Math.floor(rows / 2);
    
    if (x === centerX && y === centerY && rows % 2 === 1 && cols % 2 === 1) {
      return 'center';
    }

    // Bordas
    if (x === 0 || x === cols - 1 || y === 0 || y === rows - 1) {
      if (x < cols / 2 && y < rows / 2) return 'topLeft';
      if (x >= cols / 2 && y < rows / 2) return 'topRight';
      if (x < cols / 2 && y >= rows / 2) return 'bottomLeft';
      return 'bottomRight';
    }

    // Interior
    if (x < cols / 2 && y < rows / 2) return 'topLeft';
    if (x >= cols / 2 && y < rows / 2) return 'topRight';
    if (x < cols / 2 && y >= rows / 2) return 'bottomLeft';
    return 'bottomRight';
  }

  /**
   * Analisa estratégia de escaneamento
   */
  analyzeScanningStrategy(navigationData) {
    const { clickSequence } = navigationData;
    
    if (clickSequence.length < 3) {
      return { strategy: 'insufficient_data', confidence: 0 };
    }

    // Analisar direções de movimento
    const directions = [];
    for (let i = 1; i < clickSequence.length; i++) {
      const prev = clickSequence[i - 1].coordinates;
      const curr = clickSequence[i].coordinates;
      directions.push(this.getDirection(prev, curr));
    }

    // Classificar estratégia baseada nas direções
    const strategy = this.classifyStrategy(directions);
    
    return {
      strategy: strategy.type,
      confidence: strategy.confidence,
      directions,
      consistency: this.calculateDirectionConsistency(directions)
    };
  }

  /**
   * Obtém direção entre duas coordenadas
   */
  getDirection(prev, curr) {
    const deltaX = curr.x - prev.x;
    const deltaY = curr.y - prev.y;

    if (deltaX > 0 && deltaY === 0) return 'right';
    if (deltaX < 0 && deltaY === 0) return 'left';
    if (deltaX === 0 && deltaY > 0) return 'down';
    if (deltaX === 0 && deltaY < 0) return 'up';
    if (deltaX > 0 && deltaY > 0) return 'down-right';
    if (deltaX < 0 && deltaY > 0) return 'down-left';
    if (deltaX > 0 && deltaY < 0) return 'up-right';
    if (deltaX < 0 && deltaY < 0) return 'up-left';
    
    return 'same';
  }

  /**
   * Classifica estratégia baseada nas direções
   */
  classifyStrategy(directions) {
    const directionCounts = {};
    directions.forEach(dir => {
      directionCounts[dir] = (directionCounts[dir] || 0) + 1;
    });

    const horizontal = (directionCounts.left || 0) + (directionCounts.right || 0);
    const vertical = (directionCounts.up || 0) + (directionCounts.down || 0);
    const diagonal = (directionCounts['up-left'] || 0) + (directionCounts['up-right'] || 0) + 
                    (directionCounts['down-left'] || 0) + (directionCounts['down-right'] || 0);

    const total = directions.length;
    
    if (horizontal > total * 0.6) return { type: 'horizontal', confidence: 0.8 };
    if (vertical > total * 0.6) return { type: 'vertical', confidence: 0.8 };
    if (diagonal > total * 0.4) return { type: 'diagonal', confidence: 0.7 };
    
    return { type: 'mixed', confidence: 0.5 };
  }

  /**
   * Calcula métricas de eficiência
   */
  calculateEfficiencyMetrics(navigationData) {
    const { clickSequence } = navigationData;
    
    if (clickSequence.length < 2) {
      return { efficiency: 0, averageDistance: 0, backtracking: 0 };
    }

    let totalDistance = 0;
    let backtrackingCount = 0;
    const visitedPositions = new Set();

    for (let i = 1; i < clickSequence.length; i++) {
      const prev = clickSequence[i - 1].coordinates;
      const curr = clickSequence[i].coordinates;
      
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2)
      );
      totalDistance += distance;

      // Detectar backtracking
      const posKey = `${curr.x},${curr.y}`;
      if (visitedPositions.has(posKey)) {
        backtrackingCount++;
      }
      visitedPositions.add(posKey);
    }

    const averageDistance = totalDistance / (clickSequence.length - 1);
    const backtrackingRate = backtrackingCount / clickSequence.length;
    
    // Eficiência baseada na distância média (menor é melhor)
    const efficiency = Math.max(0, 1 - (averageDistance / 3)); // Normalizado para grid

    return {
      efficiency,
      averageDistance,
      backtracking: backtrackingRate,
      totalDistance,
      uniquePositions: visitedPositions.size
    };
  }

  /**
   * Avalia carga cognitiva
   */
  assessCognitiveLoad(navigationData, gridConfig) {
    const { clickSequence, difficulty, gridSize } = navigationData;
    
    // Base load baseado na dificuldade e tamanho do grid
    const baseLoad = {
      easy: 0.3,
      medium: 0.6,
      hard: 0.9
    }[difficulty] || 0.6;

    // Ajuste baseado no tamanho do grid
    const sizeMultiplier = gridSize / 9; // Normalizado para grid médio
    
    // Complexidade baseada na variabilidade dos movimentos
    const movementVariability = this.calculateMovementVariability(clickSequence);
    
    // Planning score baseado na consistência
    const planning = this.assessPlanningCapacity(clickSequence);
    
    const loadIndex = Math.min(1, baseLoad * sizeMultiplier * (1 + movementVariability));
    
    return {
      loadIndex,
      adaptability: 1 - movementVariability,
      planning,
      complexity: sizeMultiplier
    };
  }

  /**
   * Calcula variabilidade de movimento
   */
  calculateMovementVariability(clickSequence) {
    if (clickSequence.length < 3) return 0;

    const distances = [];
    for (let i = 1; i < clickSequence.length; i++) {
      const prev = clickSequence[i - 1].coordinates;
      const curr = clickSequence[i].coordinates;
      const distance = Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + Math.pow(curr.y - prev.y, 2)
      );
      distances.push(distance);
    }

    const mean = distances.reduce((sum, d) => sum + d, 0) / distances.length;
    const variance = distances.reduce((sum, d) => sum + Math.pow(d - mean, 2), 0) / distances.length;
    
    return Math.sqrt(variance) / mean; // Coeficiente de variação
  }

  /**
   * Avalia capacidade de planejamento
   */
  assessPlanningCapacity(clickSequence) {
    if (clickSequence.length < 4) return 0.5;

    // Medir reversões de direção (indicador de falta de planejamento)
    let reversals = 0;
    for (let i = 2; i < clickSequence.length; i++) {
      const dir1 = this.getDirection(
        clickSequence[i - 2].coordinates,
        clickSequence[i - 1].coordinates
      );
      const dir2 = this.getDirection(
        clickSequence[i - 1].coordinates,
        clickSequence[i].coordinates
      );
      
      if (this.isReversal(dir1, dir2)) {
        reversals++;
      }
    }

    const reversalRate = reversals / (clickSequence.length - 2);
    return Math.max(0, 1 - reversalRate * 2); // Penalizar reversões
  }

  /**
   * Verifica se houve reversão de direção
   */
  isReversal(dir1, dir2) {
    const reversals = {
      'left': 'right',
      'right': 'left',
      'up': 'down',
      'down': 'up'
    };
    return reversals[dir1] === dir2;
  }

  /**
   * Calcula variabilidade de tempo de resposta
   */
  calculateResponseTimeVariability(navigationData) {
    const responseTimes = navigationData.clickSequence
      .map(click => click.responseTime)
      .filter(rt => rt > 0);

    if (responseTimes.length < 2) return { variability: 0, consistency: 0.5 };

    const mean = responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length;
    const variance = responseTimes.reduce((sum, rt) => sum + Math.pow(rt - mean, 2), 0) / responseTimes.length;
    const standardDeviation = Math.sqrt(variance);
    
    const variability = mean > 0 ? standardDeviation / mean : 0;
    const consistency = Math.max(0, 1 - variability);

    return { variability, consistency, mean, standardDeviation };
  }

  /**
   * Analisa padrões de pausa
   */
  analyzePausePatterns(navigationData) {
    const { clickSequence } = navigationData;
    
    if (clickSequence.length < 2) return { averagePause: 0, pauseVariability: 0 };

    const pauses = [];
    for (let i = 1; i < clickSequence.length; i++) {
      const pause = clickSequence[i].timestamp - clickSequence[i - 1].timestamp;
      if (pause > 0) pauses.push(pause);
    }

    if (pauses.length === 0) return { averagePause: 0, pauseVariability: 0 };

    const averagePause = pauses.reduce((sum, p) => sum + p, 0) / pauses.length;
    const pauseVariability = this.calculateVariability(pauses);

    return {
      averagePause,
      pauseVariability,
      longestPause: Math.max(...pauses),
      shortestPause: Math.min(...pauses)
    };
  }

  /**
   * Avalia coordenação visual-motora
   */
  assessVisualMotorCoordination(navigationData) {
    const efficiency = this.calculateEfficiencyMetrics(navigationData);
    const timeVariability = this.calculateResponseTimeVariability(navigationData);
    
    // Coordenação baseada na eficiência e consistência temporal
    const coordination = (efficiency.efficiency + timeVariability.consistency) / 2;
    
    return Math.max(0, Math.min(1, coordination));
  }

  /**
   * Avalia memória espacial
   */
  assessSpatialMemory(navigationData) {
    const { clickSequence } = navigationData;
    
    // Medir retorno a posições anteriores de forma intencional
    const intentionalReturns = this.countIntentionalReturns(clickSequence);
    const memoryScore = Math.min(1, intentionalReturns / Math.max(1, clickSequence.length * 0.1));
    
    return memoryScore;
  }

  /**
   * Avalia função executiva
   */
  assessExecutiveFunction(navigationData) {
    const planning = this.assessPlanningCapacity(navigationData.clickSequence);
    const efficiency = this.calculateEfficiencyMetrics(navigationData);
    const adaptability = 1 - this.calculateMovementVariability(navigationData.clickSequence);
    
    return (planning + efficiency.efficiency + adaptability) / 3;
  }

  /**
   * Conta retornos intencionais (não backtracking)
   */
  countIntentionalReturns(clickSequence) {
    // Implementação simplificada
    const uniquePositions = new Set();
    let intentionalReturns = 0;

    clickSequence.forEach((click, index) => {
      const posKey = `${click.coordinates.x},${click.coordinates.y}`;
      if (uniquePositions.has(posKey) && index > 2) {
        // Considerar como intencional se não é imediatamente após visita
        intentionalReturns++;
      }
      uniquePositions.add(posKey);
    });

    return intentionalReturns;
  }

  /**
   * Calcula uniformidade de distribuição
   */
  calculateUniformity(distribution) {
    const values = Object.values(distribution);
    const expectedValue = 1 / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - expectedValue, 2), 0) / values.length;
    
    return 1 - Math.sqrt(variance);
  }

  /**
   * Calcula consistência de direção
   */
  calculateDirectionConsistency(directions) {
    if (directions.length === 0) return 0;

    const directionCounts = {};
    directions.forEach(dir => {
      directionCounts[dir] = (directionCounts[dir] || 0) + 1;
    });

    const maxCount = Math.max(...Object.values(directionCounts));
    return maxCount / directions.length;
  }

  /**
   * Calcula variabilidade geral
   */
  calculateVariability(values) {
    if (values.length < 2) return 0;

    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return mean > 0 ? Math.sqrt(variance) / mean : 0;
  }

  /**
   * Avalia qualidade dos dados
   */
  assessDataQuality(navigationData) {
    let qualityScore = 1.0;
    const issues = [];

    // Verificar quantidade de dados
    if (navigationData.clickSequence.length < 3) {
      qualityScore -= 0.3;
      issues.push('Poucos cliques para análise robusta');
    }

    // Verificar consistência temporal
    const hasValidTimestamps = navigationData.clickSequence.every(click => 
      click.timestamp && click.timestamp > 0
    );
    if (!hasValidTimestamps) {
      qualityScore -= 0.2;
      issues.push('Timestamps inválidos ou ausentes');
    }

    // Verificar coordenadas válidas
    const hasValidCoordinates = navigationData.clickSequence.every(click =>
      click.coordinates && 
      typeof click.coordinates.x === 'number' && 
      typeof click.coordinates.y === 'number'
    );
    if (!hasValidCoordinates) {
      qualityScore -= 0.3;
      issues.push('Coordenadas inválidas');
    }

    return {
      score: Math.max(0, qualityScore),
      issues,
      level: qualityScore > 0.8 ? 'high' : qualityScore > 0.5 ? 'medium' : 'low'
    };
  }

  /**
   * Gera recomendações específicas de navegação
   */
  generateNavigationRecommendations({ navigationPattern, spatialBias, efficiencyMetrics, cognitiveLoad }) {
    const recommendations = [];

    // Recomendações baseadas no padrão de navegação
    if (navigationPattern.consistency < 0.5) {
      recommendations.push({
        category: 'navigation_strategy',
        priority: 'medium',
        recommendation: 'Desenvolver estratégia de escaneamento mais consistente',
        rationale: 'Padrão de navegação muito variável pode indicar dificuldade de planejamento'
      });
    }

    if (navigationPattern.primaryPattern === 'random') {
      recommendations.push({
        category: 'systematic_approach',
        priority: 'high',
        recommendation: 'Ensinar estratégias de busca sistemática (linha por linha, coluna por coluna)',
        rationale: 'Navegação aleatória reduz eficiência e aumenta carga cognitiva'
      });
    }

    // Recomendações baseadas em viés espacial
    if (spatialBias.coverage < 0.7) {
      recommendations.push({
        category: 'spatial_coverage',
        priority: 'medium',
        recommendation: 'Praticar exercícios de escaneamento completo do campo visual',
        rationale: `Cobertura espacial de apenas ${Math.round(spatialBias.coverage * 100)}% indica escaneamento incompleto`
      });
    }

    // Recomendações baseadas em eficiência
    if (efficiencyMetrics.efficiency < 0.6) {
      recommendations.push({
        category: 'efficiency',
        priority: 'high',
        recommendation: 'Trabalhar planejamento de movimentos e redução de distâncias desnecessárias',
        rationale: 'Baixa eficiência de navegação pode sobrecarregar recursos cognitivos'
      });
    }

    if (efficiencyMetrics.backtracking > 0.3) {
      recommendations.push({
        category: 'memory',
        priority: 'medium',
        recommendation: 'Fortalecer memória de trabalho visual para reduzir revisitas',
        rationale: 'Alto índice de backtracking sugere dificuldades de memória espacial'
      });
    }

    // Recomendações baseadas em carga cognitiva
    if (cognitiveLoad.loadIndex > 0.8) {
      recommendations.push({
        category: 'cognitive_load',
        priority: 'high',
        recommendation: 'Reduzir complexidade inicial e aumentar gradualmente',
        rationale: 'Alta carga cognitiva pode comprometer performance e aprendizado'
      });
    }

    if (cognitiveLoad.planning < 0.4) {
      recommendations.push({
        category: 'executive_function',
        priority: 'high',
        recommendation: 'Desenvolver habilidades de planejamento através de jogos estratégicos',
        rationale: 'Baixa capacidade de planejamento afeta eficiência na resolução de tarefas'
      });
    }

    return recommendations.length > 0 ? recommendations : [{
      category: 'maintenance',
      priority: 'low',
      recommendation: 'Manter padrão atual de navegação eficiente',
      rationale: 'Performance de navegação dentro dos parâmetros esperados'
    }];
  }

  /**
   * Gera análise de fallback em caso de erro
   */
  generateFallbackAnalysis(errorMessage) {
    return {
      navigationPattern: 'unknown',
      patternConsistency: 0.5,
      scanningStrategy: 'unknown',
      spatialBias: 'center',
      spatialDistribution: {},
      coverageEfficiency: 0.5,
      selectionEfficiency: 0.5,
      averageDistance: 0,
      backtrackingRate: 0,
      cognitiveLoadIndex: 0.5,
      adaptabilityScore: 0.5,
      strategicPlanning: 0.5,
      responseTimeVariability: { variability: 0, consistency: 0.5 },
      pausePatterns: { averagePause: 0, pauseVariability: 0 },
      visualMotorCoordination: 0.5,
      spatialMemory: 0.5,
      executiveFunction: 0.5,
      gridSize: 9,
      totalClicks: 0,
      sessionDuration: 0,
      difficulty: 'unknown',
      recommendations: [{
        category: 'data_collection',
        priority: 'high',
        recommendation: 'Melhorar coleta de dados de navegação',
        rationale: `Erro na análise: ${errorMessage}`
      }],
      analysisTimestamp: new Date().toISOString(),
      collectorVersion: '2.1.0',
      dataQuality: { score: 0, issues: [errorMessage], level: 'error' },
      status: 'fallback',
      error: errorMessage
    };
  }
}
