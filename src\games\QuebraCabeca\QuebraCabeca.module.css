/**
 * @file QuebraCabeca.module.css
 * @description Estilos modulares para o Jogo Quebra-Cabeça - Adaptado ao padrão unificado
 * @version 3.1.0
 */

/* Variáveis CSS para consistência e reutilização */
:root {
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-blur: blur(10px);
  --success-bg: rgba(76, 175, 80, 0.3);
  --success-border: rgba(76, 175, 80, 0.5);
  --error-bg: rgba(244, 67, 54, 0.3);
  --error-border: rgba(244, 67, 54, 0.5);
  --primary-font: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Container principal do QuebraCabeca */
.quebraCabecaGame {
  min-height: 100vh;
  background: var(--gradient-bg);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  color: white;
  font-family: var(--primary-font);
}

/* Conteúdo do jogo */
.gameContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* Header do jogo */
.gameHeader {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem 3rem 1rem 1rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  position: relative;
  min-height: 70px;
}

.gameTitle {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
  color: white;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activitySubtitle {
  font-size: 0.7rem;
  opacity: 0.8;
  margin-top: 0.25rem;
  background: var(--card-background);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: var(--card-border);
}

/* Botões TTS */
.headerTtsButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: white;
  z-index: 10;
}

.headerTtsButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.05);
}

.headerTtsButton:active {
  transform: scale(0.95);
}

.ttsActive {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
}

.ttsInactive {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
}

/* Área do quebra-cabeça - Seguindo padrão Memory Game */
.puzzleContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 2rem 0;
  gap: 1.5rem;
}

/* Exibição da emoção alvo */
.targetEmotionDisplay {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1rem 2rem;
  margin-bottom: 1rem;
}

.targetEmotionIcon {
  font-size: 3rem;
  margin-bottom: 0.5rem;
}

.targetEmotionName {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Container das peças disponíveis */
.piecesContainer {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.5rem;
  margin-top: 1rem;
  width: 100%;
  max-width: 600px;
}

.piecesTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Grid do quebra-cabeça - Seguindo padrão Memory Game */
.puzzleGrid {
  display: grid;
  gap: 1rem;
  justify-content: center;
  margin: 1rem auto;
  max-width: 400px;
  padding: 1.5rem;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Slots do quebra-cabeça - Seguindo padrão Memory Game */
.puzzleSlot {
  width: 80px;
  height: 80px;
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.puzzleSlot.empty {
  border: 2px dashed rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.05);
}

.puzzleSlot.filled {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
  border-color: var(--success-border);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

.puzzleSlot:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.placedPiece {
  font-size: 2rem;
  animation: placeAnimation 0.3s ease-out;
}

.emptySlot {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.5);
  font-weight: bold;
}

@keyframes placeAnimation {
  0% { transform: scale(0.8); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

.puzzlePiece::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.puzzlePiece:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
}

.puzzlePiece:hover::before {
  transform: translateX(100%);
}

.puzzlePiece.filled {
  background: linear-gradient(135deg, rgba(255, 206, 84, 0.4), rgba(255, 206, 84, 0.2));
  border-color: #FECA57;
  box-shadow: 0 6px 20px rgba(254, 202, 87, 0.3);
}

.puzzlePiece.correct {
  background: linear-gradient(135deg, var(--success-bg), rgba(76, 175, 80, 0.2));
  border-color: var(--success-border);
  animation: correctPulse 0.8s ease-in-out;
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.puzzlePiece.empty {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
  border: 2px dashed rgba(255, 255, 255, 0.4);
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.1);
}

@keyframes correctPulse {
  0% { transform: scale(1); }
  25% { transform: scale(1.15); }
  50% { transform: scale(1.05); }
  75% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Área das peças - Design elegante mobile-first */
.piecesArea {
  width: 280px;
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 24px;
  padding: 1.75rem;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.piecesArea:hover {
  box-shadow: 0 8px 35px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.piecesTitle {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 1.75rem;
  text-align: center;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Grid das peças disponíveis - Seguindo padrão Memory Game */
.piecesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  gap: 1rem;
  justify-content: center;
  max-width: 500px;
  margin: 0 auto;
}

.availablePiece {
  width: 70px;
  height: 70px;
  background: var(--card-background);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  color: white;
}

.availablePiece:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.availablePiece:active {
  transform: scale(0.95);
}

.availablePiece.used {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}

.availablePiece.used:hover {
  transform: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.availablePiece::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.availablePiece:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.35), rgba(255, 255, 255, 0.15));
  transform: scale(1.1) rotate(3deg);
  cursor: grab;
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
}

.availablePiece:hover::before {
  left: 100%;
}

.availablePiece:active {
  cursor: grabbing;
  transform: scale(1.05) rotate(1deg);
}

.availablePiece.used {
  opacity: 0.4;
  cursor: not-allowed;
  transform: scale(0.9);
  filter: grayscale(100%);
  background: rgba(128, 128, 128, 0.2);
}

/* Imagem de referência */
.referenceImage {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.referenceTitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.referencePreview {
  width: 150px;
  height: 150px;
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 8px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
}

/* Estatísticas do jogo - Cards elegantes mobile-first */
.gameStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 0 0.5rem;
}

.statCard {
  background: var(--card-background);
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1.25rem 1rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.statCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
  background: rgba(255, 255, 255, 0.15);
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #E91E63);
  opacity: 0.8;
  border-radius: 16px 16px 0 0;
}

.statCard::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.statCard:hover::after {
  animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
  50% { opacity: 1; }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
}

.statValue {
  font-size: 1.75rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #fff, #e3f2fd);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.statLabel {
  font-size: 0.85rem;
  opacity: 0.9;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Controles do jogo - Botões elegantes mobile-first */
.gameControls {
  display: flex;
  gap: 1.25rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
  padding: 0 1rem;
}

.controlButton {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  backdrop-filter: var(--card-blur);
  border: var(--card-border);
  border-radius: 16px;
  padding: 1rem 2rem;
  color: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.95rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  text-align: center;
}

.controlButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.controlButton:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.15));
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
}

.controlButton:hover::before {
  left: 100%;
}

.controlButton:active {
  transform: translateY(-1px) scale(1.01);
}

.nextButton {
  background: linear-gradient(135deg, var(--success-bg), rgba(76, 175, 80, 0.2));
  border-color: var(--success-border);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.2);
}

.nextButton:hover {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.5), rgba(76, 175, 80, 0.3));
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
}

.hintButton {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.4), rgba(255, 193, 7, 0.2));
  border-color: rgba(255, 193, 7, 0.6);
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.hintButton:hover {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.5), rgba(255, 193, 7, 0.3));
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
}

.resetButton {
  background: linear-gradient(135deg, var(--error-bg), rgba(244, 67, 54, 0.2));
  border-color: var(--error-border);
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.2);
}

.resetButton:hover {
  background: rgba(244, 67, 54, 0.4);
}

.changeDifficultyBtn {
  background: rgba(156, 39, 176, 0.3);
  border-color: rgba(156, 39, 176, 0.5);
}

.changeDifficultyBtn:hover {
  background: rgba(156, 39, 176, 0.4);
}

/* Mensagens de feedback */
.feedbackMessage {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 1.5rem 2.5rem;
  border-radius: 16px;
  font-size: 1.3rem;
  font-weight: 700;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: messageSlide 3s ease-in-out;
}

.feedbackMessage.success {
  background: var(--success-bg);
  color: white;
}

.feedbackMessage.hint {
  background: rgba(255, 193, 7, 0.95);
  color: white;
}

.feedbackMessage.error {
  background: var(--error-bg);
  color: white;
}

@keyframes messageSlide {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  15% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  85% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

/* Tela de finalização */
.completionScreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: var(--card-blur);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.completionCard {
  background: var(--gradient-bg);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: white;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: var(--card-border);
}

.completionTitle {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
}

.completionMessage {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.completionActions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsividade móvel-first - Design elegante */
@media (max-width: 768px) {
  .quebraCabecaGame {
    padding: 0.75rem;
  }
  
  .puzzleArea {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .piecesArea {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    padding: 1.5rem;
  }
  
  .gameTitle {
    font-size: 1.6rem;
  }
  
  .puzzleGrid {
    max-width: 280px;
    gap: 0.5rem;
    padding: 1.25rem;
  }
  
  .puzzlePiece {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }
  
  .availablePiece {
    width: 65px;
    height: 65px;
    font-size: 1.4rem;
  }
  
  .piecesGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
  
  .gameControls {
    gap: 1rem;
    padding: 0 0.5rem;
  }
  
  .controlButton {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
    min-width: 100px;
  }
  
  .gameStats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.875rem;
  }
  
  .statCard {
    padding: 1rem 0.75rem;
  }
  
  .statValue {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .gameHeader {
    padding: 1rem 2.5rem 1rem 1rem;
  }
  
  .gameTitle {
    font-size: 1.4rem;
  }
  
  .activitySubtitle {
    font-size: 0.65rem;
    padding: 0.2rem 0.6rem;
  }
  
  .puzzleGrid {
    max-width: 240px;
    gap: 0.4rem;
    padding: 1rem;
  }
  
  .puzzlePiece {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    border-radius: 12px;
  }
  
  .availablePiece {
    width: 55px;
    height: 55px;
    font-size: 1.2rem;
    border-radius: 12px;
  }
  
  .piecesGrid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.75rem;
  }
  
  .piecesArea {
    padding: 1.25rem;
    border-radius: 20px;
  }
  
  .puzzleBoard {
    padding: 1.75rem;
    border-radius: 20px;
  }
  
  .boardTitle {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
  }
  
  .gameControls {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
  
  .controlButton {
    width: 100%;
    max-width: 280px;
    padding: 1rem;
    font-size: 0.875rem;
  }
  
  .referencePreview {
    width: 120px;
    height: 120px;
    font-size: 2rem;
  }
  
  .completionCard {
    padding: 2rem;
    margin: 1rem;
    border-radius: 20px;
  }
  
  .completionTitle {
    font-size: 2rem;
  }
  
  .gameStats {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }
  
  .statCard {
    padding: 0.875rem 0.5rem;
  }
  
  .statValue {
    font-size: 1.3rem;
  }
  
  .statLabel {
    font-size: 0.75rem;
  }
}

/* Suporte a alto contraste */
[data-theme="high-contrast"] {
  --card-background: #000;
  --card-border: 1px solid #fff;
  --success-bg: #28a745;
  --success-border: #1e7e34;
  --error-bg: #dc3545;
  --error-border: #bd2130;
}

/* Suporte a movimento reduzido */
.reduced-motion {
  .puzzlePiece, .controlButton, .feedbackMessage, .completionCard, .availablePiece {
    animation: none !important;
    transition: none !important;
  }
}

/* =====================================================
   🎯 ESTILOS PARA ATIVIDADES ESPECÍFICAS - QUEBRA-CABEÇA
   ===================================================== */

.activityContainer {
  width: 100%;
  padding: 1rem;
  min-height: 400px;
}

.activityHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.activityTitle {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 0.5rem;
}

.activitySubtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

/* Rotação de Peças */
.rotationInstructions {
  text-align: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: rgba(33, 150, 243, 0.2);
  border-radius: 12px;
  border: 2px solid rgba(33, 150, 243, 0.5);
}

.instructionText {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
}

.rotationActivity {
  display: flex;
  justify-content: center;
  align-items: center;
}

.rotationGrid {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

.rotationSlot {
  position: relative;
  transition: transform 0.3s ease;
  cursor: pointer;
}

.rotationSlot:hover {
  transform: scale(1.05);
}

/* Quebra-Cabeça de Padrões */
.patternArea {
  margin-bottom: 2rem;
}

.patternTitle {
  text-align: center;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.patternSequence {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.patternPiece {
  position: relative;
  padding: 1rem;
  border: 2px solid rgba(255, 152, 0, 0.6);
  border-radius: 12px;
  background: rgba(255, 152, 0, 0.2);
  text-align: center;
  min-width: 80px;
}

.patternPiece.missing {
  border: 3px dashed rgba(255, 193, 7, 0.8);
  background: rgba(255, 193, 7, 0.3);
  animation: pulse 2s infinite;
}

.patternEmoji {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.missingPiece {
  font-size: 2rem;
  color: rgba(255, 193, 7, 0.8);
}

.patternNumber {
  font-size: 0.8rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.8);
}

.optionsArea {
  text-align: center;
}

.optionsTitle {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
}

.optionsGrid {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.optionButton {
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: var(--card-background);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.optionButton:hover {
  transform: scale(1.05);
  border-color: rgba(33, 150, 243, 0.6);
}

.optionButton.selected {
  border: 3px solid #4CAF50;
  background: rgba(76, 175, 80, 0.3);
  transform: scale(1.05);
}

.optionButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.optionEmoji {
  font-size: 2rem;
}

/* Correspondência Emocional */
.targetEmotionArea {
  text-align: center;
  margin-bottom: 2rem;
}

.targetEmotionTitle {
  font-size: 1.2rem;
  color: white;
  margin-bottom: 1rem;
}

.targetEmotion {
  padding: 2rem;
  border: 3px solid rgba(244, 67, 54, 0.6);
  border-radius: 20px;
  background: rgba(244, 67, 54, 0.2);
  display: inline-block;
}

.targetEmotionEmoji {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.targetEmotionContext {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
}

.matchingArea {
  margin-bottom: 2rem;
  text-align: center;
}

.matchingTitle {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
}

.selectedMatches {
  min-height: 80px;
  padding: 1rem;
  border: 2px dashed rgba(76, 175, 80, 0.5);
  border-radius: 12px;
  background: rgba(76, 175, 80, 0.1);
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.selectedMatch {
  font-size: 2rem;
  padding: 0.5rem;
  border: 2px solid rgba(76, 175, 80, 0.6);
  border-radius: 8px;
  background: rgba(76, 175, 80, 0.2);
}

.emptyMatches {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

.matchingOptions {
  text-align: center;
}

.matchingOptionsTitle {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
}

.matchingOptionsGrid {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.matchingOption {
  font-size: 2rem;
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: var(--card-background);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.matchingOption:hover {
  transform: scale(1.05);
}

.matchingOption.selected {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Montagem Criativa */
.creativeCanvas {
  margin-bottom: 2rem;
  text-align: center;
}

.canvasTitle {
  font-size: 1.2rem;
  color: white;
  margin-bottom: 1rem;
}

.canvasGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  max-width: 300px;
  margin: 0 auto 1rem;
  padding: 1rem;
  border: 2px dashed rgba(156, 39, 176, 0.5);
  border-radius: 12px;
  background: rgba(156, 39, 176, 0.1);
}

.canvasSlot {
  width: 80px;
  height: 80px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.canvasSlot.filled {
  background: rgba(156, 39, 176, 0.3);
  border-color: rgba(156, 39, 176, 0.6);
}

.canvasSlot.empty {
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.3);
}

.canvasPiece {
  font-size: 2rem;
}

.emptySlot {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.5);
}

.creativeConstraints {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.creativePieces {
  text-align: center;
}

.creativePiecesTitle {
  font-size: 1.1rem;
  color: white;
  margin-bottom: 1rem;
}

.creativePiecesGrid {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.creativePiece {
  font-size: 2rem;
  padding: 1rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: var(--card-background);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.creativePiece:hover {
  transform: scale(1.05);
}

.creativePiece.used {
  opacity: 0.5;
  cursor: not-allowed;
}

.progressArea {
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(33, 150, 243, 0.2);
  border-radius: 12px;
  border: 2px solid rgba(33, 150, 243, 0.5);
}

.progressTitle {
  font-size: 1.1rem;
  color: white;
  font-weight: bold;
}

/* Menu de atividades - PADRÃO LETTERRECOGNITION */
.activityMenu {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 2rem;
  padding: 0 1rem;
  flex-wrap: wrap;
}

.activityButton {
  background: var(--card-background);
  border: var(--card-border);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  min-width: 80px;
  backdrop-filter: var(--card-blur);
}

.activityButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.activityButton.active {
  background: var(--success-bg);
  border-color: var(--success-border);
  color: white;
}

.activityIcon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.activityName {
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  line-height: 1.2;
}

/* =====================================================
   🎯 ESTILOS SEGUINDO PADRÃO MEMORY GAME - OBRIGATÓRIOS
   ===================================================== */

/* Área da pergunta - SEGUINDO PADRÃO MEMORY GAME */
.questionArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1rem;
}

.questionHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.questionTitle {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.instructions {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1rem;
  line-height: 1.4;
}

/* Removido - estilos duplicados que conflitam com o padrão Memory Game */

/* Removido - usando estilos do padrão Memory Game definidos anteriormente */

.noPieces {
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  text-align: center;
  padding: 2rem;
  grid-column: 1 / -1;
}

.emptySlot {
  color: rgba(255, 255, 255, 0.5);
  font-size: 2rem;
  font-weight: bold;
}

/* Elementos específicos do quebra-cabeça */
.targetEmotion {
  font-size: 4rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.patternSequence {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

.patternPiece {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.patternPiece.missing {
  border: 3px dashed var(--warning-border);
  background: var(--warning-bg);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.creativeGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  max-width: 200px;
  margin: 0 auto;
}

.creativeSlot {
  width: 60px;
  height: 60px;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.creativeSlot:hover {
  border-color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
}

/* Responsividade Mobile First */
@media (max-width: 768px) {
  .questionArea {
    padding: 1.5rem;
  }

  .puzzleGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    max-width: 250px;
  }

  .puzzleSlot {
    width: 100px;
    height: 100px;
    font-size: 1.5rem;
  }

  .optionsContainer {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
  }

  .answerOption {
    padding: 0.75rem;
    font-size: 1.5rem;
    min-height: 70px;
  }

  .patternPiece {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .creativeGrid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.25rem;
    max-width: 150px;
  }

  .creativeSlot {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
  }
}