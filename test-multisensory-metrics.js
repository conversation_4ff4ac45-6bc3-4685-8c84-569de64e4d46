/**
 * Script para testar a integração multissensorial em todos os jogos
 * Verifica se MultisensoryMetricsCollector está configurado corretamente
 * e se o hook useMultisensoryIntegration está sendo utilizado
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// Configuração
const GAMES_DIR = path.join(__dirname, 'src', 'games');
const HOOKS_DIR = path.join(__dirname, 'src', 'hooks');
const SERVICES_DIR = path.join(__dirname, 'src', 'api', 'services');
const AIBRAIN_DIR = path.join(__dirname, 'src', 'ai');

// Lista de jogos que devem utilizar integração multissensorial
const EXPECTED_GAMES = [
  'ColorMatch',
  'MemoryGame',
  'ImageAssociation',
  'MusicalSequence',
  'WordSearch',
  'AttentionTraining'
];

// Função principal
async function main() {
  console.log(chalk.blue('🔍 VERIFICAÇÃO DE INTEGRAÇÃO MULTISSENSORIAL'));
  console.log(chalk.blue('============================================='));

  // 1. Verificar implementação do MultisensoryMetricsCollector
  console.log(chalk.yellow('\n1. Verificando implementação do MultisensoryMetricsCollector...'));
  checkMultisensoryImplementation();

  // 2. Verificar uso do hook useMultisensoryIntegration nos jogos
  console.log(chalk.yellow('\n2. Verificando uso do hook useMultisensoryIntegration nos jogos...'));
  checkMultisensoryHookUsage();

  // 3. Verificar integração com AIBrain
  console.log(chalk.yellow('\n3. Verificando integração com AI Brain...'));
  checkAIBrainIntegration();

  // 4. Verificar armazenamento de métricas
  console.log(chalk.yellow('\n4. Verificando armazenamento de métricas multissensoriais...'));
  checkMetricsStorage();

  console.log(chalk.blue('\n✅ VERIFICAÇÃO COMPLETA'));
}

// Verifica implementação do MultisensoryMetricsCollector
function checkMultisensoryImplementation() {
  const metricsPath = path.join(SERVICES_DIR, 'multisensoryAnalysis', 'multisensoryMetrics.js');
  
  if (!fs.existsSync(metricsPath)) {
    console.log(chalk.red('❌ MultisensoryMetricsCollector não encontrado no caminho esperado!'));
    return;
  }

  const content = fs.readFileSync(metricsPath, 'utf8');
  
  // Verificar métodos essenciais
  const hasStartCollection = content.includes('startMetricsCollection');
  const hasStopCollection = content.includes('stopMetricsCollection');
  const hasProcessData = content.includes('processMetricsData');
  const hasSensorHandlers = content.includes('sensorHandlers');
  
  console.log(hasStartCollection 
    ? chalk.green('✅ Método startMetricsCollection encontrado')
    : chalk.red('❌ Método startMetricsCollection NÃO encontrado'));
    
  console.log(hasStopCollection 
    ? chalk.green('✅ Método stopMetricsCollection encontrado')
    : chalk.red('❌ Método stopMetricsCollection NÃO encontrado'));
    
  console.log(hasProcessData 
    ? chalk.green('✅ Método processMetricsData encontrado')
    : chalk.red('❌ Método processMetricsData NÃO encontrado'));
    
  console.log(hasSensorHandlers 
    ? chalk.green('✅ Gerenciamento de sensores encontrado')
    : chalk.red('❌ Gerenciamento de sensores NÃO encontrado'));
}

// Verifica uso do hook useMultisensoryIntegration nos jogos
function checkMultisensoryHookUsage() {
  // Verificar se o hook existe
  const hookPath = path.join(HOOKS_DIR, 'useMultisensoryIntegration.js');
  
  if (!fs.existsSync(hookPath)) {
    console.log(chalk.red('❌ Hook useMultisensoryIntegration não encontrado!'));
    return;
  }
  
  console.log(chalk.green('✅ Hook useMultisensoryIntegration existe'));
  
  // Verificar em cada jogo
  let gamesWithIntegration = 0;
  
  EXPECTED_GAMES.forEach(game => {
    const gameDir = path.join(GAMES_DIR, game);
    if (!fs.existsSync(gameDir)) {
      console.log(chalk.yellow(`⚠️ Diretório do jogo ${game} não encontrado`));
      return;
    }
    
    // Procurar arquivos JSX no diretório do jogo
    const gameFiles = fs.readdirSync(gameDir)
      .filter(file => file.endsWith('.jsx') && file.includes('Game'));
      
    if (gameFiles.length === 0) {
      console.log(chalk.yellow(`⚠️ Nenhum arquivo de jogo encontrado para ${game}`));
      return;
    }
    
    // Verificar cada arquivo do jogo
    let gameHasIntegration = false;
    
    gameFiles.forEach(file => {
      const filePath = path.join(gameDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      const hasImport = content.includes("import { useMultisensoryIntegration }") || 
                        content.includes("import useMultisensoryIntegration");
      const hasUsage = content.includes("useMultisensoryIntegration(");
      
      if (hasImport && hasUsage) {
        gameHasIntegration = true;
      }
    });
    
    console.log(gameHasIntegration 
      ? chalk.green(`✅ ${game} usa integração multissensorial`)
      : chalk.red(`❌ ${game} NÃO usa integração multissensorial`));
      
    if (gameHasIntegration) {
      gamesWithIntegration++;
    }
  });
  
  const integrationPercentage = (gamesWithIntegration / EXPECTED_GAMES.length) * 100;
  
  console.log(chalk.blue(`\n📊 ${integrationPercentage.toFixed(1)}% dos jogos utilizam integração multissensorial`));
}

// Verifica integração com AI Brain
function checkAIBrainIntegration() {
  // Verificar se existe um integrador no AI Brain
  const possiblePaths = [
    path.join(AIBRAIN_DIR, 'multisensory', 'MultisensoryAIBrainIntegrator.js'),
    path.join(AIBRAIN_DIR, 'integrators', 'MultisensoryIntegrator.js'),
    path.join(AIBRAIN_DIR, 'processors', 'MultisensoryProcessor.js'),
    path.join(SERVICES_DIR, 'aibrain', 'MultisensoryAIBrainService.js')
  ];
  
  let aibrainIntegratorFound = false;
  let aibrainIntegratorPath = null;
  
  for (const checkPath of possiblePaths) {
    if (fs.existsSync(checkPath)) {
      aibrainIntegratorFound = true;
      aibrainIntegratorPath = checkPath;
      break;
    }
  }
  
  if (!aibrainIntegratorFound) {
    console.log(chalk.red('❌ Integrador multissensorial com AI Brain não encontrado!'));
    return;
  }
  
  console.log(chalk.green(`✅ Integrador multissensorial com AI Brain encontrado em: ${path.relative(__dirname, aibrainIntegratorPath)}`));
  
  // Verificar o conteúdo do integrador
  const content = fs.readFileSync(aibrainIntegratorPath, 'utf8');
  
  const hasProcessMetrics = content.includes('processMultisensoryMetrics') || 
                            content.includes('processMetrics') ||
                            content.includes('analyzeMultisensoryData');
  
  const hasSendToAIBrain = content.includes('sendToAIBrain') || 
                           content.includes('processWithAI') ||
                           content.includes('analyzeWithAI');
  
  console.log(hasProcessMetrics 
    ? chalk.green('✅ Processamento de métricas multissensoriais encontrado')
    : chalk.red('❌ Processamento de métricas multissensoriais NÃO encontrado'));
    
  console.log(hasSendToAIBrain 
    ? chalk.green('✅ Envio de dados para AI Brain encontrado')
    : chalk.red('❌ Envio de dados para AI Brain NÃO encontrado'));
}

// Verifica armazenamento de métricas
function checkMetricsStorage() {
  // Verificar métodos de armazenamento em hooks relevantes
  const relevantHooks = [
    'useGameMetrics.js',
    'useMultisensoryIntegration.js',
    'useSystemOrchestrator.js'
  ];
  
  relevantHooks.forEach(hook => {
    const hookPath = path.join(HOOKS_DIR, hook);
    
    if (!fs.existsSync(hookPath)) {
      console.log(chalk.yellow(`⚠️ Hook ${hook} não encontrado`));
      return;
    }
    
    const content = fs.readFileSync(hookPath, 'utf8');
    
    const hasLocalStorageSave = content.includes('localStorage.setItem') || 
                               content.includes('setInLocalStorage');
    
    const hasServerSave = content.includes('saveMetricsData') || 
                          content.includes('sendMetrics') ||
                          content.includes('api.post') ||
                          content.includes('axios.post');
    
    console.log(`${hook}:`);
    console.log(hasLocalStorageSave 
      ? chalk.green('  ✅ Armazenamento em localStorage')
      : chalk.yellow('  ⚠️ Sem armazenamento em localStorage'));
      
    console.log(hasServerSave 
      ? chalk.green('  ✅ Envio para servidor')
      : chalk.yellow('  ⚠️ Sem envio para servidor'));
  });
}

// Executa o script
main().catch(error => {
  console.error('Erro na execução do script:', error);
  process.exit(1);
});
