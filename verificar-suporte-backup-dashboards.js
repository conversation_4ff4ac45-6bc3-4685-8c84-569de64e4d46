/**
 * Script para verificar se todos os dashboards premium estão prontos para usar dados de backup
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// Configurações
const dashboardDir = path.join(__dirname, 'src', 'components', 'dashboard');
const backupAdapterPath = path.join(__dirname, 'src', 'utils', 'backupDataAdapter.js');
const backupSupportPath = path.join(__dirname, 'src', 'utils', 'backupExportSupport.js');

// Lista de dashboards premium que devem suportar backup
const premiumDashboards = [
  'PerformanceDashboard',
  'NeuropedagogicalDashboard',
  'AdvancedAIReport',
];

// Função para verificar se um dashboard tem suporte para backup
function checkDashboardBackupSupport(dashboardName) {
  console.log(chalk.blue(`\nVerificando ${dashboardName}...`));
  
  const dashboardPath = path.join(dashboardDir, dashboardName);
  
  // Verificar se o diretório existe
  if (!fs.existsSync(dashboardPath)) {
    console.log(chalk.red(`✗ Diretório do dashboard não encontrado: ${dashboardPath}`));
    return false;
  }
  
  // Encontrar o arquivo principal do dashboard
  let mainFile = null;
  const files = fs.readdirSync(dashboardPath);
  for (const file of files) {
    if (file.endsWith('.jsx') && file.includes(dashboardName)) {
      mainFile = path.join(dashboardPath, file);
      break;
    }
  }
  
  if (!mainFile) {
    console.log(chalk.red(`✗ Arquivo principal do dashboard não encontrado em ${dashboardPath}`));
    return false;
  }
  
  console.log(chalk.green(`✓ Arquivo do dashboard encontrado: ${path.basename(mainFile)}`));
  
  // Ler o conteúdo do arquivo
  const content = fs.readFileSync(mainFile, 'utf-8');
  
  // Verificar importação do backupDataAdapter
  const hasBackupAdapter = content.includes('backupDataAdapter') || 
                          content.includes('extractGameDataFromBackup');
  
  console.log(hasBackupAdapter 
    ? chalk.green('✓ Dashboard importa backupDataAdapter') 
    : chalk.red('✗ Dashboard não importa backupDataAdapter'));
  
  // Verificar importação do backupExportSupport
  const hasBackupSupport = content.includes('backupExportSupport') || 
                          content.includes('useBackupImport') ||
                          content.includes('hasImportedBackup') ||
                          content.includes('getImportedBackup');
  
  console.log(hasBackupSupport 
    ? chalk.green('✓ Dashboard importa backupExportSupport') 
    : chalk.red('✗ Dashboard não importa backupExportSupport'));
  
  // Verificar uso do BackupWarningBanner
  const hasWarningBanner = content.includes('BackupWarningBanner');
  
  console.log(hasWarningBanner 
    ? chalk.green('✓ Dashboard usa BackupWarningBanner') 
    : chalk.red('✗ Dashboard não usa BackupWarningBanner'));
  
  // Verificar uso do BackupManager
  const hasBackupManager = content.includes('BackupManager');
  
  console.log(hasBackupManager 
    ? chalk.green('✓ Dashboard usa BackupManager') 
    : chalk.red('✗ Dashboard não usa BackupManager'));
  
  // Verificar se o dashboard verifica dados de backup no localStorage
  const checksLocalStorage = content.includes('localStorage.getItem(') || 
                            content.includes('getImportedBackup');
  
  console.log(checksLocalStorage 
    ? chalk.green('✓ Dashboard verifica dados no localStorage') 
    : chalk.red('✗ Dashboard não verifica dados no localStorage'));
  
  // Verificação completa
  const isCompliant = hasBackupAdapter && 
                     hasBackupSupport && 
                     hasWarningBanner && 
                     hasBackupManager && 
                     checksLocalStorage;
  
  console.log(isCompliant 
    ? chalk.green(`\n✅ ${dashboardName} está pronto para usar dados de backup`) 
    : chalk.red(`\n❌ ${dashboardName} não está totalmente pronto para usar dados de backup`));
  
  return isCompliant;
}

// Função principal
function main() {
  console.log(chalk.yellow('======================================================'));
  console.log(chalk.yellow('     VERIFICAÇÃO DE SUPORTE A BACKUP NOS DASHBOARDS    '));
  console.log(chalk.yellow('======================================================'));
  
  // Verificar se os arquivos de suporte existem
  const hasBackupAdapter = fs.existsSync(backupAdapterPath);
  const hasBackupSupport = fs.existsSync(backupSupportPath);
  
  console.log('\n--- Verificando arquivos de suporte ---');
  console.log(hasBackupAdapter 
    ? chalk.green('✓ backupDataAdapter.js encontrado') 
    : chalk.red('✗ backupDataAdapter.js não encontrado'));
  console.log(hasBackupSupport 
    ? chalk.green('✓ backupExportSupport.js encontrado') 
    : chalk.red('✗ backupExportSupport.js não encontrado'));
  
  if (!hasBackupAdapter || !hasBackupSupport) {
    console.log(chalk.red('\n❌ Arquivos de suporte necessários estão faltando. Corrija isso primeiro.'));
    return;
  }
  
  // Verificar cada dashboard
  console.log('\n--- Verificando dashboards premium ---');
  
  let compliantCount = 0;
  for (const dashboard of premiumDashboards) {
    if (checkDashboardBackupSupport(dashboard)) {
      compliantCount++;
    }
  }
  
  // Resumo final
  console.log('\n--- Resumo Final ---');
  if (compliantCount === premiumDashboards.length) {
    console.log(chalk.green(`\n✅ Todos os ${compliantCount} dashboards premium estão prontos para usar dados de backup`));
  } else {
    console.log(chalk.yellow(`\n⚠️ ${compliantCount} de ${premiumDashboards.length} dashboards estão prontos para usar dados de backup`));
    console.log(chalk.yellow('Execute este script novamente após implementar o suporte em todos os dashboards'));
  }
}

// Executar verificação
main();
