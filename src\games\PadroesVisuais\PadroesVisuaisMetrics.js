export const PadroesVisuaisMetrics = {
  // Registrar início do jogo
  startGame: (difficulty) => {
    const metrics = {
      gameId: 'padroes-visuais',
      sessionId: `patterns_${Date.now()}`,
      startTime: new Date().toISOString(),
      difficulty,
      userId: 'anonymous',
      device: navigator.userAgent
    }

    console.log('Padrões Visuais Game Started:', metrics)
    return metrics
  },

  // Registrar tentativa de sequência
  recordSequenceAttempt: (attempt) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      level: attempt.level,
      sequenceLength: attempt.sequenceLength,
      targetSequence: attempt.targetSequence,
      playerSequence: attempt.playerSequence,
      isCorrect: attempt.isCorrect,
      responseTime: attempt.responseTime,
      difficulty: attempt.difficulty,
      errors: attempt.errors || 0,
      partialCorrect: attempt.partialCorrect, // quantas formas estavam corretas antes do erro
      shapesMatched: attempt.shapesMatched
    }

    console.log('Padrões Visuais Attempt:', metrics)
    return metrics
  },

  // Registrar clique em forma individual
  recordShapeClick: (shapeData) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'shape_clicked',
      shapeId: shapeData.shapeId,
      shapeName: shapeData.shapeName,
      sequencePosition: shapeData.sequencePosition,
      isCorrectPosition: shapeData.isCorrectPosition,
      isCorrectShape: shapeData.isCorrectShape,
      level: shapeData.level
    }

    console.log('Padrões Visuais Shape Click:', metrics)
    return metrics
  },

  // Registrar repetição de sequência
  recordSequenceRepeat: (repeatData) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'sequence_repeated',
      level: repeatData.level,
      sequenceLength: repeatData.sequenceLength,
      attempt: repeatData.attempt
    }

    console.log('Padrões Visuais Sequence Repeated:', metrics)
    return metrics
  },

  // Registrar progressão de nível
  recordLevelProgression: (progressData) => {
    const metrics = {
      timestamp: new Date().toISOString(),
      action: 'level_completed',
      completedLevel: progressData.completedLevel,
      nextLevel: progressData.nextLevel,
      difficulty: progressData.difficulty,
      attemptsInLevel: progressData.attemptsInLevel,
      timeInLevel: progressData.timeInLevel
    }

    console.log('Padrões Visuais Level Progression:', metrics)
    return metrics
  },

  // Registrar final do jogo
  endGame: (gameData) => {
    const metrics = {
      sessionId: gameData.sessionId,
      endTime: new Date().toISOString(),
      totalTime: gameData.totalTime,
      totalLevels: gameData.totalLevels,
      correctSequences: gameData.correctSequences,
      totalScore: gameData.totalScore,
      finalLevel: gameData.finalLevel,
      accuracy: gameData.accuracy,
      averageSequenceLength: gameData.averageSequenceLength,
      difficulty: gameData.difficulty,
      sequenceRepeats: gameData.sequenceRepeats || 0,
      completed: gameData.completed
    }

    console.log('Padrões Visuais Game Ended:', metrics)
    return metrics
  },

  // Calcular estatísticas do jogo
  calculateStats: (attempts) => {
    if (!attempts || attempts.length === 0) {
      return {
        accuracy: 0,
        averageSequenceLength: 0,
        totalAttempts: 0,
        correctSequences: 0
      }
    }

    const correctAttempts = attempts.filter(attempt => attempt.isCorrect)
    const totalSequenceLength = attempts.reduce((sum, attempt) => sum + attempt.sequenceLength, 0)

    return {
      accuracy: (correctAttempts.length / attempts.length) * 100,
      averageSequenceLength: totalSequenceLength / attempts.length,
      totalAttempts: attempts.length,
      correctSequences: correctAttempts.length,
      longestSequence: Math.max(...attempts.map(a => a.sequenceLength)),
      memorySpan: calculateMemorySpan(attempts),
      patternRecognition: calculatePatternRecognition(attempts),
      visualMemoryGrowth: calculateVisualMemoryGrowth(attempts)
    }
  },

  // Analisar padrões de memória visual
  analyzeVisualPatterns: (attempts, shapeClicks) => {
    const errorsByPosition = attempts
      .filter(attempt => !attempt.isCorrect)
      .reduce((acc, error) => {
        const errorPosition = error.partialCorrect || 0
        acc[errorPosition] = (acc[errorPosition] || 0) + 1
        return acc
      }, {})

    const shapePreferences = shapeClicks.reduce((acc, click) => {
      acc[click.shapeId] = (acc[click.shapeId] || 0) + 1
      return acc
    }, {})

    const shapeAccuracy = {}
    const shapesUsed = [...new Set(shapeClicks.map(c => c.shapeId))]

    shapesUsed.forEach(shapeId => {
      const shapeClicks = shapeClicks.filter(c => c.shapeId === shapeId)
      const shapeCorrect = shapeClicks.filter(c => c.isCorrectShape).length
      shapeAccuracy[shapeId] = (shapeCorrect / shapeClicks.length) * 100
    })

    return {
      errorsByPosition,
      shapePreferences,
      shapeAccuracy,
      difficultShapes: getDifficultShapes(shapeAccuracy),
      suggestions: generateVisualSuggestions(attempts, errorsByPosition, shapeAccuracy)
    }
  }
}

// Funções auxiliares para análise visual
function calculateMemorySpan (attempts) {
  const correctSequences = attempts.filter(a => a.isCorrect)
  if (correctSequences.length === 0) return 0

  // Maior sequência consecutiva correta
  let maxSpan = 0
  let currentSpan = 0

  attempts.forEach(attempt => {
    if (attempt.isCorrect) {
      currentSpan = Math.max(currentSpan, attempt.sequenceLength)
    } else {
      maxSpan = Math.max(maxSpan, currentSpan)
      currentSpan = 0
    }
  })

  return Math.max(maxSpan, currentSpan)
}

function calculatePatternRecognition (attempts) {
  if (attempts.length < 5) return 0

  // Avaliar melhoria na velocidade de reconhecimento
  const firstAttempts = attempts.slice(0, Math.floor(attempts.length / 3))
  const lastAttempts = attempts.slice(-Math.floor(attempts.length / 3))

  const firstAvgTime = firstAttempts.reduce((sum, a) => sum + a.responseTime, 0) / firstAttempts.length
  const lastAvgTime = lastAttempts.reduce((sum, a) => sum + a.responseTime, 0) / lastAttempts.length

  return ((firstAvgTime - lastAvgTime) / firstAvgTime) * 100
}

function calculateVisualMemoryGrowth (attempts) {
  if (attempts.length < 6) return 0

  const firstHalf = attempts.slice(0, Math.floor(attempts.length / 2))
  const secondHalf = attempts.slice(Math.floor(attempts.length / 2))

  const firstAccuracy = firstHalf.filter(a => a.isCorrect).length / firstHalf.length
  const secondAccuracy = secondHalf.filter(a => a.isCorrect).length / secondHalf.length

  return ((secondAccuracy - firstAccuracy) / firstAccuracy) * 100
}

function getDifficultShapes (shapeAccuracy) {
  return Object.entries(shapeAccuracy)
    .filter(([, accuracy]) => accuracy < 60)
    .sort(([, a], [, b]) => a - b)
    .slice(0, 3)
    .map(([shape, accuracy]) => ({ shape, accuracy }))
}

function generateVisualSuggestions (attempts, errorsByPosition, shapeAccuracy) {
  const suggestions = []

  if (attempts.length === 0) {
    suggestions.push('Comece jogando para desenvolver sua memória visual!')
    return suggestions
  }

  const accuracy = attempts.filter(a => a.isCorrect).length / attempts.length

  // Sugestões baseadas na precisão geral
  if (accuracy < 0.4) {
    suggestions.push('Foque bem na primeira visualização da sequência')
    suggestions.push('Use a função "Repetir Sequência" sempre que precisar')
  } else if (accuracy < 0.7) {
    suggestions.push('Tente criar uma história visual com as formas para lembrar melhor')
    suggestions.push('Observe bem as cores e formas de cada elemento')
  } else if (accuracy > 0.9) {
    suggestions.push('Excelente memória visual! Tente sequências mais longas')
    suggestions.push('Você está pronto para níveis mais desafiadores')
  }

  // Sugestões baseadas em posições de erro
  const mostErrorPosition = Object.entries(errorsByPosition)
    .sort(([, a], [, b]) => b - a)[0]

  if (mostErrorPosition) {
    const position = parseInt(mostErrorPosition[0])
    if (position === 0) {
      suggestions.push('Preste mais atenção à primeira forma da sequência')
    } else if (position === 1) {
      suggestions.push('A segunda forma é crucial - foque nela após ver a primeira')
    } else if (position >= 3) {
      suggestions.push('Sua memória de curto prazo está boa, continue praticando sequências longas')
    }
  }

  // Sugestões baseadas em formas problemáticas
  const difficultShapes = getDifficultShapes(shapeAccuracy)
  if (difficultShapes.length > 0) {
    suggestions.push(`Pratique mais com as formas: ${difficultShapes.map(s => s.shape).join(', ')}`)
  }

  return suggestions.length > 0 ? suggestions : ['Continue praticando para fortalecer sua memória visual!']
}
