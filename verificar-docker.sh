#!/bin/bash

# 🐳 SCRIPT DE VERIFICAÇÃO DOCKER - Portal Betina V3
# Verifica se todos os serviços estão funcionando corretamente

echo "🔍 Verificando ambiente Docker do Portal Betina V3..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para verificar se um comando existe
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Função para verificar status de um serviço
check_service() {
    local service_name=$1
    local expected_status="Up"
    
    echo -n "🔧 Verificando $service_name... "
    
    if docker-compose ps | grep -q "$service_name.*$expected_status"; then
        echo -e "${GREEN}✅ ATIVO${NC}"
        return 0
    else
        echo -e "${RED}❌ INATIVO${NC}"
        return 1
    fi
}

# Função para verificar health check
check_health() {
    local service_name=$1
    local url=$2
    
    echo -n "🩺 Health check $service_name... "
    
    if curl -s -f "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ SAUDÁVEL${NC}"
        return 0
    else
        echo -e "${RED}❌ FALHA${NC}"
        return 1
    fi
}

# Verificar se Docker está instalado
echo "🐳 Verificando instalação do Docker..."
if ! command_exists docker; then
    echo -e "${RED}❌ Docker não está instalado!${NC}"
    exit 1
fi

if ! command_exists docker-compose; then
    echo -e "${RED}❌ Docker Compose não está instalado!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker e Docker Compose instalados${NC}"

# Verificar se o arquivo docker-compose.yml existe
echo "📄 Verificando docker-compose.yml..."
if [ ! -f "docker-compose.yml" ]; then
    echo -e "${RED}❌ Arquivo docker-compose.yml não encontrado!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ docker-compose.yml encontrado${NC}"

# Verificar se o arquivo .env existe
echo "⚙️ Verificando arquivo .env..."
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️ Arquivo .env não encontrado. Criando a partir do .env.example...${NC}"
    if [ -f ".env.example" ]; then
        cp .env.example .env
        echo -e "${GREEN}✅ Arquivo .env criado${NC}"
    else
        echo -e "${RED}❌ Arquivo .env.example não encontrado!${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ Arquivo .env encontrado${NC}"
fi

# Verificar status dos serviços
echo ""
echo "🔍 Verificando status dos serviços Docker..."

# Verificar se o docker-compose está rodando
if ! docker-compose ps > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️ Serviços não estão rodando. Iniciando...${NC}"
    docker-compose up -d
    sleep 10
fi

# Lista de serviços para verificar
services=("portal-betina-v3-db" "portal-betina-v3-api" "portal-betina-v3-frontend")

all_services_ok=true

for service in "${services[@]}"; do
    if ! check_service "$service"; then
        all_services_ok=false
    fi
done

# Verificar health checks
echo ""
echo "🩺 Verificando health checks..."

# Health check da API
if ! check_health "API" "http://localhost:3000/api/public/health"; then
    all_services_ok=false
fi

# Health check do Frontend
if ! check_health "Frontend" "http://localhost:5173"; then
    all_services_ok=false
fi

# Verificar conectividade do banco
echo -n "🗄️ Verificando banco de dados... "
if docker-compose exec -T portal-betina-db pg_isready -U betina_user -d betina_db > /dev/null 2>&1; then
    echo -e "${GREEN}✅ CONECTADO${NC}"
else
    echo -e "${RED}❌ FALHA NA CONEXÃO${NC}"
    all_services_ok=false
fi

# Verificar logs recentes para erros
echo ""
echo "📋 Verificando logs recentes..."

# Verificar logs da API
echo -n "📊 Logs da API... "
api_errors=$(docker-compose logs --tail=50 api 2>/dev/null | grep -i "error\|failed\|exception" | wc -l)
if [ "$api_errors" -eq 0 ]; then
    echo -e "${GREEN}✅ SEM ERROS${NC}"
else
    echo -e "${YELLOW}⚠️ $api_errors ERROS ENCONTRADOS${NC}"
fi

# Verificar logs do banco
echo -n "🗄️ Logs do banco... "
db_errors=$(docker-compose logs --tail=50 portal-betina-db 2>/dev/null | grep -i "error\|failed\|fatal" | wc -l)
if [ "$db_errors" -eq 0 ]; then
    echo -e "${GREEN}✅ SEM ERROS${NC}"
else
    echo -e "${YELLOW}⚠️ $db_errors ERROS ENCONTRADOS${NC}"
fi

# Verificar uso de recursos
echo ""
echo "💾 Verificando uso de recursos..."

# Verificar uso de CPU e memória dos containers
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep portal-betina

# Verificar espaço em disco dos volumes
echo ""
echo "💿 Verificando volumes Docker..."
docker volume ls | grep portal

# Verificar rede
echo ""
echo "🌐 Verificando rede Docker..."
docker network ls | grep betina

# Resumo final
echo ""
echo "=========================="
if [ "$all_services_ok" = true ]; then
    echo -e "${GREEN}🎉 TODOS OS SERVIÇOS ESTÃO FUNCIONANDO CORRETAMENTE!${NC}"
    echo ""
    echo -e "${BLUE}📍 URLs de Acesso:${NC}"
    echo "   🎨 Frontend: http://localhost:5173"
    echo "   🚀 API: http://localhost:3000"
    echo "   📊 Health Check: http://localhost:3000/api/public/health"
    echo "   📈 Monitoramento: http://localhost:9090"
    echo ""
    echo -e "${BLUE}🔧 Comandos Úteis:${NC}"
    echo "   Ver logs: docker-compose logs -f"
    echo "   Reiniciar: docker-compose restart"
    echo "   Parar: docker-compose down"
else
    echo -e "${RED}❌ ALGUNS SERVIÇOS APRESENTAM PROBLEMAS!${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Comandos para Diagnóstico:${NC}"
    echo "   Ver logs detalhados: docker-compose logs"
    echo "   Reiniciar serviços: docker-compose restart"
    echo "   Rebuild: docker-compose up -d --build"
    echo ""
    echo -e "${YELLOW}🆘 Se problemas persistirem:${NC}"
    echo "   1. Verificar arquivo .env"
    echo "   2. Verificar portas em uso"
    echo "   3. Verificar espaço em disco"
    echo "   4. Limpar cache Docker: docker system prune"
fi

echo "=========================="
