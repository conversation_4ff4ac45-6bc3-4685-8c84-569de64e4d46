#!/usr/bin/env node

/**
 * 🔧 CORRETOR DE DEPENDÊNCIAS CRÍTICAS DE HOOKS
 * Script para corrigir apenas os problemas críticos de dependencies faltantes
 */

import fs from 'fs';

const fixCriticalDependencies = () => {
  console.log('🔧 CORRIGINDO DEPENDÊNCIAS CRÍTICAS DE HOOKS');
  console.log('=============================================');
  
  // Correção no MemoryGame - gameState faltante
  try {
    const memoryGamePath = './src/games/MemoryGame/MemoryGame.jsx';
    let memoryContent = fs.readFileSync(memoryGamePath, 'utf8');
    
    // Procurar pelo useCallback que precisa da dependência gameState
    const restartGamePattern = /const restartGame = useCallback\(([^}]+)}, \[([^\]]*)\]\)/s;
    const match = memoryContent.match(restartGamePattern);
    
    if (match) {
      const deps = match[2].trim();
      if (deps && !deps.includes('gameState')) {
        const newDeps = deps ? `${deps}, gameState` : 'gameState';
        const replacement = `const restartGame = useCallback(${match[1]}}, [${newDeps}])`;
        memoryContent = memoryContent.replace(restartGamePattern, replacement);
        fs.writeFileSync(memoryGamePath, memoryContent);
        console.log('✅ MemoryGame.jsx - Adicionada dependência gameState');
      }
    }
  } catch (error) {
    console.error('❌ Erro ao corrigir MemoryGame:', error.message);
  }
  
  console.log('\n📝 NOTA SOBRE DEPENDÊNCIAS ttsActive:');
  console.log('As dependências ttsActive nos toggleTTS são opcionais pois:');
  console.log('- Usam setState em forma funcional (prev => !prev)');
  console.log('- Não referenciam ttsActive diretamente na função');
  console.log('- Adicionar ttsActive causaria re-renders desnecessários');
  console.log('\n✅ CORREÇÕES CRÍTICAS CONCLUÍDAS');
};

fixCriticalDependencies();
