var t,e=Object.defineProperty,r=(t,r,s)=>((t,r,s)=>r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[r]=s)(t,"symbol"!=typeof r?r+"":r,s);import{r as s,R as i}from"./react-router-BtSsPy6x.js";var n=(t=>(t.BASE="base",t.BODY="body",t.HEAD="head",t.HTML="html",t.LINK="link",t.META="meta",t.NOSCRIPT="noscript",t.SCRIPT="script",t.STYLE="style",t.TITLE="title",t.FRAGMENT="Symbol(react.fragment)",t))(n||{}),a={rel:["amphtml","canonical","alternate"]},o={type:["application/ld+json"]},c={charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]};Object.values(n);var l={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"};Object.entries(l).reduce((t,[e,r])=>(t[r]=e,t),{});var p="data-rh",u=(t,e)=>Array.isArray(t)?t.reduce((t,r)=>(((t,e)=>{const r=Object.keys(t);for(let s=0;s<r.length;s+=1)if(e[r[s]]&&e[r[s]].includes(t[r[s]]))return!0;return!1})(r,e)?t.priority.push(r):t.default.push(r),t),{priority:[],default:[]}):{default:t,priority:[]},m=["noscript","script","style"],d=(t,e=!0)=>!1===e?String(t):String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),g=t=>Object.keys(t).reduce((e,r)=>{const s=void 0!==t[r]?`${r}="${t[r]}"`:`${r}`;return e?`${e} ${s}`:s},""),h=(t,e,r,s)=>{const i=g(r),n=(a=e,Array.isArray(a)?a.join(""):a);var a;return i?`<${t} ${p}="true" ${i}>${d(n,s)}</${t}>`:`<${t} ${p}="true">${d(n,s)}</${t}>`},y=(t,e={})=>Object.keys(t).reduce((e,r)=>(e[l[r]||r]=t[r],e),e),b=(t,e)=>e.map((e,r)=>{const s={key:r,[p]:!0};return Object.keys(e).forEach(t=>{const r=l[t]||t;if("innerHTML"===r||"cssText"===r){const t=e.innerHTML||e.cssText;s.dangerouslySetInnerHTML={__html:t}}else s[r]=e[t]}),i.createElement(t,s)}),T=(t,e,r=!0)=>{switch(t){case"title":return{toComponent:()=>((t,e,r)=>{const s=y(r,{key:e,[p]:!0});return[i.createElement("title",s,e)]})(0,e.title,e.titleAttributes),toString:()=>h(t,e.title,e.titleAttributes,r)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>y(e),toString:()=>g(e)};default:return{toComponent:()=>b(t,e),toString:()=>((t,e,r=!0)=>e.reduce((e,s)=>{const i=s,n=Object.keys(i).filter(t=>!("innerHTML"===t||"cssText"===t)).reduce((t,e)=>{const s=void 0===i[e]?e:`${e}="${d(i[e],r)}"`;return t?`${t} ${s}`:s},""),a=i.innerHTML||i.cssText||"",o=-1===m.indexOf(t);return`${e}<${t} ${p}="true" ${n}${o?"/>":`>${a}</${t}>`}`},""))(t,e,r)}}},$=t=>{const{baseTag:e,bodyAttributes:r,encode:s=!0,htmlAttributes:i,noscriptTags:n,styleTags:l,title:p="",titleAttributes:m,prioritizeSeoTags:d}=t;let{linkTags:g,metaTags:h,scriptTags:y}=t,$={toComponent:()=>{},toString:()=>""};return d&&({priorityMethods:$,linkTags:g,metaTags:h,scriptTags:y}=(({metaTags:t,linkTags:e,scriptTags:r,encode:s})=>{const i=u(t,c),n=u(e,a),l=u(r,o);return{priorityMethods:{toComponent:()=>[...b("meta",i.priority),...b("link",n.priority),...b("script",l.priority)],toString:()=>`${T("meta",i.priority,s)} ${T("link",n.priority,s)} ${T("script",l.priority,s)}`},metaTags:i.default,linkTags:n.default,scriptTags:l.default}})(t)),{priority:$,base:T("base",e,s),bodyAttributes:T("bodyAttributes",r,s),htmlAttributes:T("htmlAttributes",i,s),link:T("link",g,s),meta:T("meta",h,s),noscript:T("noscript",n,s),script:T("script",y,s),style:T("style",l,s),title:T("title",{title:p,titleAttributes:m},s)}},A=[],f=!("undefined"==typeof window||!window.document||!window.document.createElement),O=class{constructor(t,e){r(this,"instances",[]),r(this,"canUseDOM",f),r(this,"context"),r(this,"value",{setHelmet:t=>{this.context.helmet=t},helmetInstances:{get:()=>this.canUseDOM?A:this.instances,add:t=>{(this.canUseDOM?A:this.instances).push(t)},remove:t=>{const e=(this.canUseDOM?A:this.instances).indexOf(t);(this.canUseDOM?A:this.instances).splice(e,1)}}}),this.context=t,this.canUseDOM=e||!1,e||(t.helmet=$({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},M=i.createContext({}),k=(t=class extends s.Component{constructor(e){super(e),r(this,"helmetData"),this.helmetData=new O(this.props.context||{},t.canUseDOM)}render(){return i.createElement(M.Provider,{value:this.helmetData.value},this.props.children)}},r(t,"canUseDOM",f),t);export{k as H};
