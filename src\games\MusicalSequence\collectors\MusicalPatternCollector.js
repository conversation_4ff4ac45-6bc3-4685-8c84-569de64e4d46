/**
 * MusicalPatternCollector - Coleta dados sobre reconhecimento de padrões musicais
 * Analisa capacidade de identificar e reproduzir padrões melódicos, rítmicos e harmônicos
 */
export class MusicalPatternCollector {
  constructor() {
    this.patternData = [];
    this.melodicPatterns = [];
    this.rhythmicPatterns = [];
    this.instrumentPreferences = {};
    this.harmonicAnalysis = [];
    this.debugMode = true;
  }

  /**
   * Coleta dados sobre reconhecimento de padrões melódicos
   */
  collectMelodicPattern(patternData) {
    try {
      if (!patternData || typeof patternData !== 'object') {
        console.warn('MusicalPatternCollector: Dados de padrão inválidos');
        return null;
      }

      const {
        sequence = [],
        playerResponse = [],
        instruments = [],
        difficulty = '',
        isCorrect = false,
        responseTime = 0,
        sequenceType = 'random',
        timestamp = Date.now()
      } = patternData;

      const melodicAnalysis = {
        timestamp,
        patternType: this.identifyMelodicPattern(sequence, instruments),
        intervalAnalysis: this.analyzeMelodicIntervals(sequence, instruments),
        contourRecognition: this.analyzeMelodicContour(sequence, instruments),
        patternComplexity: this.calculatePatternComplexity(sequence),
        reproductionAccuracy: this.assessReproductionAccuracy(sequence, playerResponse),
        instrumentTransitions: this.analyzeInstrumentTransitions(sequence, instruments),
        difficulty,
        isCorrect,
        responseTime,
        sequenceType,
        timbralRecognition: this.analyzeTimbralRecognition(instruments, playerResponse)
      };

      this.melodicPatterns.push(melodicAnalysis);

      if (this.debugMode) {
        console.log('🎵 MusicalPatternCollector - Padrão melódico coletado:', melodicAnalysis);
      }

      return melodicAnalysis;
    } catch (error) {
      console.error('Erro no MusicalPatternCollector.collectMelodicPattern:', error);
      return null;
    }
  }

  /**
   * Analisa padrões rítmicos e temporais
   */
  analyzeRhythmicPattern(rhythmData) {
    try {
      if (!rhythmData || typeof rhythmData !== 'object') {
        console.warn('MusicalPatternCollector: Dados rítmicos inválidos');
        return null;
      }

      const {
        sequence = [],
        playerResponse = [],
        timings = [],
        expectedTimings = [],
        tempo = 120,
        difficulty = '',
        timestamp = Date.now()
      } = rhythmData;

      const rhythmicAnalysis = {
        timestamp,
        rhythmicAccuracy: this.calculateRhythmicAccuracy(timings, expectedTimings),
        tempoConsistency: this.analyzeTempoConsistency(timings),
        rhythmicPattern: this.identifyRhythmicPattern(timings),
        syncopationRecognition: this.analyzeSyncopation(timings, tempo),
        metricalGrouping: this.analyzeMetricalGrouping(timings),
        rhythmicMemory: this.assessRhythmicMemory(sequence, playerResponse),
        timingPrecision: this.calculateTimingPrecision(timings, expectedTimings),
        difficulty,
        tempo
      };

      this.rhythmicPatterns.push(rhythmicAnalysis);

      if (this.debugMode) {
        console.log('🥁 MusicalPatternCollector - Padrão rítmico analisado:', rhythmicAnalysis);
      }

      return rhythmicAnalysis;
    } catch (error) {
      console.error('Erro no MusicalPatternCollector.analyzeRhythmicPattern:', error);
      return null;
    }
  }

  /**
   * Coleta preferências e reconhecimento de instrumentos
   */
  collectInstrumentPreferences(instrumentData) {
    try {
      if (!instrumentData || typeof instrumentData !== 'object') {
        console.warn('MusicalPatternCollector: Dados de instrumento inválidos');
        return null;
      }

      const {
        instrument = '',
        correctRecognition = false,
        responseTime = 0,
        confidence = 0,
        frequency = 0,
        context = 'sequence',
        timestamp = Date.now()
      } = instrumentData;

      // Atualiza preferências
      if (!this.instrumentPreferences[instrument]) {
        this.instrumentPreferences[instrument] = {
          totalExposures: 0,
          correctRecognitions: 0,
          avgResponseTime: 0,
          avgConfidence: 0,
          frequencies: [],
          contexts: {}
        };
      }

      const pref = this.instrumentPreferences[instrument];
      pref.totalExposures++;
      if (correctRecognition) pref.correctRecognitions++;
      
      // Atualiza médias
      pref.avgResponseTime = (pref.avgResponseTime * (pref.totalExposures - 1) + responseTime) / pref.totalExposures;
      pref.avgConfidence = (pref.avgConfidence * (pref.totalExposures - 1) + confidence) / pref.totalExposures;
      
      if (frequency > 0) pref.frequencies.push(frequency);
      if (!pref.contexts[context]) pref.contexts[context] = 0;
      pref.contexts[context]++;

      const instrumentAnalysis = {
        timestamp,
        instrument,
        recognitionAccuracy: pref.correctRecognitions / pref.totalExposures,
        avgResponseTime: pref.avgResponseTime,
        frequencyRange: this.calculateFrequencyRange(pref.frequencies),
        timbralDiscrimination: this.calculateTimbralDiscrimination(instrument),
        preferenceScore: this.calculatePreferenceScore(instrument),
        context
      };

      if (this.debugMode) {
        console.log('🎺 MusicalPatternCollector - Preferência de instrumento:', instrumentAnalysis);
      }

      return instrumentAnalysis;
    } catch (error) {
      console.error('Erro no MusicalPatternCollector.collectInstrumentPreferences:', error);
      return null;
    }
  }

  /**
   * Analisa reconhecimento de padrões harmônicos
   */
  analyzeHarmonicPattern(harmonicData) {
    try {
      if (!harmonicData || typeof harmonicData !== 'object') {
        console.warn('MusicalPatternCollector: Dados harmônicos inválidos');
        return null;
      }

      const {
        sequence = [],
        simultaneousNotes = [],
        chordProgression = [],
        playerResponse = [],
        difficulty = '',
        timestamp = Date.now()
      } = harmonicData;

      const harmonicAnalysis = {
        timestamp,
        chordRecognition: this.analyzeChordRecognition(simultaneousNotes, playerResponse),
        harmonicProgression: this.analyzeHarmonicProgression(chordProgression),
        dissonanceRecognition: this.analyzeDissonanceRecognition(simultaneousNotes),
        voiceLeading: this.analyzeVoiceLeading(sequence),
        harmonicMemory: this.assessHarmonicMemory(chordProgression, playerResponse),
        tonalityRecognition: this.analyzeTonalityRecognition(sequence),
        difficulty
      };

      this.harmonicAnalysis.push(harmonicAnalysis);

      if (this.debugMode) {
        console.log('🎹 MusicalPatternCollector - Padrão harmônico:', harmonicAnalysis);
      }

      return harmonicAnalysis;
    } catch (error) {
      console.error('Erro no MusicalPatternCollector.analyzeHarmonicPattern:', error);
      return null;
    }
  }

  // Métodos auxiliares para análise melódica
  identifyMelodicPattern(sequence, instruments) {
    if (!Array.isArray(sequence) || sequence.length < 2) return 'none';

    const instrumentMap = this.createInstrumentMap(instruments);
    const pitches = sequence.map(note => this.getInstrumentPitch(note, instrumentMap));
    
    // Analisa direção melódica
    let ascending = 0, descending = 0, repeated = 0;
    
    for (let i = 1; i < pitches.length; i++) {
      if (pitches[i] > pitches[i-1]) ascending++;
      else if (pitches[i] < pitches[i-1]) descending++;
      else repeated++;
    }

    const total = pitches.length - 1;
    if (ascending / total > 0.7) return 'ascending';
    if (descending / total > 0.7) return 'descending';
    if (repeated / total > 0.5) return 'repeated';
    
    // Verifica padrões mais complexos
    if (this.isArpeggioPattern(pitches)) return 'arpeggio';
    if (this.isScalePattern(pitches)) return 'scale';
    if (this.isSequencePattern(pitches)) return 'sequence';
    
    return 'mixed';
  }

  analyzeMelodicIntervals(sequence, instruments) {
    if (!Array.isArray(sequence) || sequence.length < 2) {
      return { avgInterval: 0, intervalVariety: 0, consonanceRatio: 0 };
    }

    const instrumentMap = this.createInstrumentMap(instruments);
    const pitches = sequence.map(note => this.getInstrumentPitch(note, instrumentMap));
    const intervals = [];

    for (let i = 1; i < pitches.length; i++) {
      intervals.push(Math.abs(pitches[i] - pitches[i-1]));
    }

    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const intervalVariety = new Set(intervals).size / intervals.length;
    const consonantIntervals = intervals.filter(interval => [1, 2, 3, 4, 5, 7, 8, 12].includes(interval % 12));
    const consonanceRatio = consonantIntervals.length / intervals.length;

    return { avgInterval, intervalVariety, consonanceRatio, intervals };
  }

  analyzeMelodicContour(sequence, instruments) {
    if (!Array.isArray(sequence) || sequence.length < 3) {
      return { contourType: 'none', directionChanges: 0, contourComplexity: 0 };
    }

    const instrumentMap = this.createInstrumentMap(instruments);
    const pitches = sequence.map(note => this.getInstrumentPitch(note, instrumentMap));
    
    let directionChanges = 0;
    let lastDirection = null;

    for (let i = 1; i < pitches.length; i++) {
      const currentDirection = pitches[i] > pitches[i-1] ? 'up' : pitches[i] < pitches[i-1] ? 'down' : 'same';
      if (lastDirection && currentDirection !== lastDirection && currentDirection !== 'same') {
        directionChanges++;
      }
      if (currentDirection !== 'same') lastDirection = currentDirection;
    }

    const contourComplexity = directionChanges / (pitches.length - 1);
    let contourType = 'linear';
    
    if (contourComplexity > 0.5) contourType = 'complex';
    else if (contourComplexity > 0.2) contourType = 'moderate';

    return { contourType, directionChanges, contourComplexity };
  }

  calculatePatternComplexity(sequence) {
    if (!Array.isArray(sequence)) return 0;
    
    const uniqueElements = new Set(sequence).size;
    const repetitionRate = 1 - (uniqueElements / sequence.length);
    const lengthFactor = Math.min(sequence.length / 8, 1); // Normalizado para máximo 8
    
    return (uniqueElements / 6) * 0.4 + (1 - repetitionRate) * 0.3 + lengthFactor * 0.3;
  }

  assessReproductionAccuracy(sequence, playerResponse) {
    if (!Array.isArray(sequence) || !Array.isArray(playerResponse)) return 0;
    
    const minLength = Math.min(sequence.length, playerResponse.length);
    let correctCount = 0;
    
    for (let i = 0; i < minLength; i++) {
      if (sequence[i] === playerResponse[i]) correctCount++;
    }
    
    const accuracy = correctCount / sequence.length;
    const completeness = minLength / sequence.length;
    
    return { accuracy, completeness, overallScore: accuracy * completeness };
  }

  analyzeInstrumentTransitions(sequence, instruments) {
    if (!Array.isArray(sequence) || sequence.length < 2) {
      return { transitionComplexity: 0, timbralContrast: 0, transitionPatterns: [] };
    }

    const transitions = [];
    for (let i = 1; i < sequence.length; i++) {
      transitions.push({
        from: sequence[i-1],
        to: sequence[i],
        contrast: this.calculateTimbralContrast(sequence[i-1], sequence[i])
      });
    }

    const avgContrast = transitions.reduce((sum, t) => sum + t.contrast, 0) / transitions.length;
    const transitionTypes = new Set(transitions.map(t => `${t.from}-${t.to}`)).size;
    const transitionComplexity = transitionTypes / transitions.length;

    return {
      transitionComplexity,
      timbralContrast: avgContrast,
      transitionPatterns: this.identifyTransitionPatterns(transitions)
    };
  }

  analyzeTimbralRecognition(instruments, playerResponse) {
    if (!Array.isArray(instruments) || !Array.isArray(playerResponse)) {
      return { accuracy: 0, confusionMatrix: {} };
    }

    const confusionMatrix = {};
    let correctCount = 0;

    for (let i = 0; i < Math.min(instruments.length, playerResponse.length); i++) {
      const expected = instruments[i];
      const actual = playerResponse[i];
      
      if (!confusionMatrix[expected]) confusionMatrix[expected] = {};
      if (!confusionMatrix[expected][actual]) confusionMatrix[expected][actual] = 0;
      confusionMatrix[expected][actual]++;
      
      if (expected === actual) correctCount++;
    }

    return {
      accuracy: correctCount / instruments.length,
      confusionMatrix,
      timbralDiscrimination: this.calculateOverallTimbralDiscrimination(confusionMatrix)
    };
  }

  // Métodos auxiliares para análise rítmica
  calculateRhythmicAccuracy(timings, expectedTimings) {
    if (!Array.isArray(timings) || !Array.isArray(expectedTimings)) return 0;
    
    const tolerance = 100; // ms de tolerância
    let accurateCount = 0;
    
    for (let i = 0; i < Math.min(timings.length, expectedTimings.length); i++) {
      if (Math.abs(timings[i] - expectedTimings[i]) <= tolerance) {
        accurateCount++;
      }
    }
    
    return accurateCount / expectedTimings.length;
  }

  analyzeTempoConsistency(timings) {
    if (!Array.isArray(timings) || timings.length < 3) return { consistency: 0, avgTempo: 0 };
    
    const intervals = [];
    for (let i = 1; i < timings.length; i++) {
      intervals.push(timings[i] - timings[i-1]);
    }
    
    const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    const variance = intervals.reduce((acc, interval) => acc + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
    const stdDev = Math.sqrt(variance);
    
    const consistency = Math.max(0, 1 - (stdDev / avgInterval));
    const avgTempo = 60000 / avgInterval; // BPM
    
    return { consistency, avgTempo, tempoVariability: stdDev };
  }

  identifyRhythmicPattern(timings) {
    if (!Array.isArray(timings) || timings.length < 3) return 'none';
    
    const intervals = [];
    for (let i = 1; i < timings.length; i++) {
      intervals.push(timings[i] - timings[i-1]);
    }
    
    // Verifica padrões comuns
    const uniqueIntervals = [...new Set(intervals)];
    
    if (uniqueIntervals.length === 1) return 'steady';
    if (uniqueIntervals.length === 2) {
      const ratio = Math.max(...uniqueIntervals) / Math.min(...uniqueIntervals);
      if (Math.abs(ratio - 2) < 0.1) return 'dotted';
      if (Math.abs(ratio - 1.5) < 0.1) return 'triplet';
    }
    
    return 'complex';
  }

  // Métodos utilitários
  createInstrumentMap(instruments) {
    const map = {};
    if (Array.isArray(instruments)) {
      instruments.forEach((instrument, index) => {
        map[instrument] = index; // Simplified pitch mapping
      });
    }
    return map;
  }

  getInstrumentPitch(note, instrumentMap) {
    // Mapeia instrumentos para alturas relativas (simplificado)
    const pitchMap = {
      'piano': 60, 'guitar': 64, 'drum': 36, 
      'flute': 72, 'violin': 76, 'sax': 58
    };
    return pitchMap[note] || 60;
  }

  isArpeggioPattern(pitches) {
    // Simplificação: verifica se há saltos de terceira/quinta
    if (pitches.length < 3) return false;
    
    const intervals = [];
    for (let i = 1; i < pitches.length; i++) {
      intervals.push(Math.abs(pitches[i] - pitches[i-1]));
    }
    
    return intervals.some(interval => [3, 4, 5, 7].includes(interval % 12));
  }

  isScalePattern(pitches) {
    // Verifica movimento por graus conjuntos
    for (let i = 1; i < pitches.length; i++) {
      const interval = Math.abs(pitches[i] - pitches[i-1]);
      if (interval > 2) return false;
    }
    return true;
  }

  isSequencePattern(pitches) {
    // Verifica padrões repetitivos transpostos
    if (pitches.length < 4) return false;
    
    const pattern = [pitches[1] - pitches[0], pitches[2] - pitches[1]];
    for (let i = 3; i < pitches.length - 1; i++) {
      const currentPattern = [pitches[i] - pitches[i-1], pitches[i+1] - pitches[i]];
      if (currentPattern[0] === pattern[0] && currentPattern[1] === pattern[1]) {
        return true;
      }
    }
    return false;
  }

  calculateTimbralContrast(instrument1, instrument2) {
    // Simplificação: contraste baseado em diferenças timbricas conhecidas
    const timbralCategories = {
      'piano': 'keyboard', 'guitar': 'string', 'drum': 'percussion',
      'flute': 'woodwind', 'violin': 'string', 'sax': 'woodwind'
    };
    
    const cat1 = timbralCategories[instrument1] || 'unknown';
    const cat2 = timbralCategories[instrument2] || 'unknown';
    
    return cat1 === cat2 ? 0.2 : 0.8;
  }

  calculateFrequencyRange(frequencies) {
    if (!Array.isArray(frequencies) || frequencies.length === 0) {
      return { min: 0, max: 0, range: 0 };
    }
    
    const min = Math.min(...frequencies);
    const max = Math.max(...frequencies);
    return { min, max, range: max - min };
  }

  calculateTimbralDiscrimination(instrument) {
    const pref = this.instrumentPreferences[instrument];
    if (!pref) return 0;
    
    return pref.correctRecognitions / pref.totalExposures;
  }

  calculatePreferenceScore(instrument) {
    const pref = this.instrumentPreferences[instrument];
    if (!pref) return 0;
    
    // Score baseado em precisão, tempo de resposta e confiança
    const accuracyScore = pref.correctRecognitions / pref.totalExposures;
    const speedScore = Math.max(0, 1 - (pref.avgResponseTime / 5000)); // Normalizado para 5s
    const confidenceScore = pref.avgConfidence;
    
    return (accuracyScore * 0.5 + speedScore * 0.3 + confidenceScore * 0.2);
  }

  identifyTransitionPatterns(transitions) {
    if (!Array.isArray(transitions)) return [];
    
    const patterns = [];
    const transitionCounts = {};
    
    transitions.forEach(t => {
      const key = `${t.from}-${t.to}`;
      transitionCounts[key] = (transitionCounts[key] || 0) + 1;
    });
    
    Object.entries(transitionCounts).forEach(([pattern, count]) => {
      if (count > 1) {
        patterns.push({ pattern, frequency: count, type: 'recurring' });
      }
    });
    
    return patterns;
  }

  // Métodos de análise harmônica (simplificados para o contexto de sequência musical)
  analyzeChordRecognition(simultaneousNotes, playerResponse) {
    return { accuracy: 0, chordTypes: [], recognition: 'basic' };
  }

  analyzeHarmonicProgression(chordProgression) {
    return { progressionType: 'simple', functionality: 'tonal' };
  }

  analyzeDissonanceRecognition(simultaneousNotes) {
    return { dissonanceLevel: 0, resolution: 'none' };
  }

  analyzeVoiceLeading(sequence) {
    return { smoothness: 0, parallelMotion: 0 };
  }

  assessHarmonicMemory(progression, response) {
    return { accuracy: 0, completeness: 0 };
  }

  analyzeTonalityRecognition(sequence) {
    return { key: 'C', mode: 'major', certainty: 0 };
  }

  calculateOverallTimbralDiscrimination(confusionMatrix) {
    // Calcula discriminação timbral geral baseada na matriz de confusão
    let totalCorrect = 0;
    let totalAttempts = 0;
    
    Object.keys(confusionMatrix).forEach(expected => {
      Object.keys(confusionMatrix[expected]).forEach(actual => {
        const count = confusionMatrix[expected][actual];
        totalAttempts += count;
        if (expected === actual) totalCorrect += count;
      });
    });
    
    return totalAttempts > 0 ? totalCorrect / totalAttempts : 0;
  }

  // Métodos de relatório
  getPatternReport() {
    return {
      totalPatterns: this.melodicPatterns.length,
      avgPatternComplexity: this.melodicPatterns.reduce((sum, p) => sum + p.patternComplexity, 0) / this.melodicPatterns.length || 0,
      instrumentPreferences: Object.keys(this.instrumentPreferences).map(instrument => ({
        instrument,
        accuracy: this.instrumentPreferences[instrument].correctRecognitions / this.instrumentPreferences[instrument].totalExposures,
        exposures: this.instrumentPreferences[instrument].totalExposures
      })),
      rhythmicAccuracy: this.rhythmicPatterns.reduce((sum, r) => sum + r.rhythmicAccuracy, 0) / this.rhythmicPatterns.length || 0,
      melodicContours: this.melodicPatterns.map(p => p.contourRecognition.contourType)
    };
  }

  clearData() {
    this.patternData = [];
    this.melodicPatterns = [];
    this.rhythmicPatterns = [];
    this.instrumentPreferences = {};
    this.harmonicAnalysis = [];
    
    if (this.debugMode) {
      console.log('🎵 MusicalPatternCollector - Dados limpos');
    }
  }
}
