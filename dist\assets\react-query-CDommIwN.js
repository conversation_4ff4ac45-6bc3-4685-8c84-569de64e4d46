var t,e,r,s,n,i,a,o,u,c,l,h,f,p,d,y,v,m,g,b,w,O,k,S,P,j,E,F,M,R,C,q,_,x,D,T,A,W,Q,K,U,$=t=>{throw TypeError(t)},I=(t,e,r)=>e.has(t)||$("Cannot "+r),L=(t,e,r)=>(I(t,e,"read from private field"),r?r.call(t):e.get(t)),N=(t,e,r)=>e.has(t)?$("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,r),H=(t,e,r,s)=>(I(t,e,"write to private field"),s?s.call(t,r):e.set(t,r),r),G=(t,e,r)=>(I(t,e,"access private method"),r),z=(t,e,r,s)=>({set _(s){H(t,e,s,r)},get _(){return L(t,e,s)}});import{r as B}from"./react-router-BtSsPy6x.js";import{r as Y}from"./react-BQG6_13O.js";var J=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},X="undefined"==typeof window||"Deno"in globalThis;function V(){}function Z(t,e){return"function"==typeof t?t(e):t}function tt(t,e){const{type:r="all",exact:s,fetchStatus:n,predicate:i,queryKey:a,stale:o}=t;if(a)if(s){if(e.queryHash!==rt(a,e.options))return!1}else if(!nt(e.queryKey,a))return!1;if("all"!==r){const t=e.isActive();if("active"===r&&!t)return!1;if("inactive"===r&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&((!n||n===e.state.fetchStatus)&&!(i&&!i(e)))}function et(t,e){const{exact:r,status:s,predicate:n,mutationKey:i}=t;if(i){if(!e.options.mutationKey)return!1;if(r){if(st(e.options.mutationKey)!==st(i))return!1}else if(!nt(e.options.mutationKey,i))return!1}return(!s||e.state.status===s)&&!(n&&!n(e))}function rt(t,e){return(e?.queryKeyHashFn||st)(t)}function st(t){return JSON.stringify(t,(t,e)=>ot(e)?Object.keys(e).sort().reduce((t,r)=>(t[r]=e[r],t),{}):e)}function nt(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&Object.keys(e).every(r=>nt(t[r],e[r])))}function it(t,e){if(t===e)return t;const r=at(t)&&at(e);if(r||ot(t)&&ot(e)){const s=r?t:Object.keys(t),n=s.length,i=r?e:Object.keys(e),a=i.length,o=r?[]:{},u=new Set(s);let c=0;for(let l=0;l<a;l++){const s=r?l:i[l];(!r&&u.has(s)||r)&&void 0===t[s]&&void 0===e[s]?(o[s]=void 0,c++):(o[s]=it(t[s],e[s]),o[s]===t[s]&&void 0!==t[s]&&c++)}return n===a&&c===n?t:o}return e}function at(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function ot(t){if(!ut(t))return!1;const e=t.constructor;if(void 0===e)return!0;const r=e.prototype;return!!ut(r)&&(!!r.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function ut(t){return"[object Object]"===Object.prototype.toString.call(t)}function ct(t,e,r=0){const s=[...t,e];return r&&s.length>r?s.slice(1):s}function lt(t,e,r=0){const s=[e,...t];return r&&s.length>r?s.slice(0,-1):s}var ht=Symbol();function ft(t,e){return t.queryFn,!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==ht?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var pt=new(s=class extends J{constructor(){super(),N(this,t),N(this,e),N(this,r),H(this,r,t=>{if(!X&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}})}onSubscribe(){L(this,e)||this.setEventListener(L(this,r))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=L(this,e))||t.call(this),H(this,e,void 0))}setEventListener(t){var s;H(this,r,t),null==(s=L(this,e))||s.call(this),H(this,e,t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()}))}setFocused(e){L(this,t)!==e&&(H(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof L(this,t)?L(this,t):"hidden"!==globalThis.document?.visibilityState}},t=new WeakMap,e=new WeakMap,r=new WeakMap,s),dt=new(o=class extends J{constructor(){super(),N(this,n,!0),N(this,i),N(this,a),H(this,a,t=>{if(!X&&window.addEventListener){const e=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",r)}}})}onSubscribe(){L(this,i)||this.setEventListener(L(this,a))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=L(this,i))||t.call(this),H(this,i,void 0))}setEventListener(t){var e;H(this,a,t),null==(e=L(this,i))||e.call(this),H(this,i,t(this.setOnline.bind(this)))}setOnline(t){L(this,n)!==t&&(H(this,n,t),this.listeners.forEach(e=>{e(t)}))}isOnline(){return L(this,n)}},n=new WeakMap,i=new WeakMap,a=new WeakMap,o);function yt(t){return Math.min(1e3*2**t,3e4)}function vt(t){return"online"!==(t??"online")||dt.isOnline()}var mt=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function gt(t){return t instanceof mt}function bt(t){let e,r=!1,s=0,n=!1;const i=function(){let t,e;const r=new Promise((r,s)=>{t=r,e=s});function s(t){Object.assign(r,t),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=e=>{s({status:"fulfilled",value:e}),t(e)},r.reject=t=>{s({status:"rejected",reason:t}),e(t)},r}(),a=()=>pt.isFocused()&&("always"===t.networkMode||dt.isOnline())&&t.canRun(),o=()=>vt(t.networkMode)&&t.canRun(),u=r=>{n||(n=!0,t.onSuccess?.(r),e?.(),i.resolve(r))},c=r=>{n||(n=!0,t.onError?.(r),e?.(),i.reject(r))},l=()=>new Promise(r=>{e=t=>{(n||a())&&r(t)},t.onPause?.()}).then(()=>{e=void 0,n||t.onContinue?.()}),h=()=>{if(n)return;let e;const i=0===s?t.initialPromise:void 0;try{e=i??t.fn()}catch(o){e=Promise.reject(o)}Promise.resolve(e).then(u).catch(e=>{if(n)return;const i=t.retry??(X?0:3),o=t.retryDelay??yt,u="function"==typeof o?o(s,e):o,f=!0===i||"number"==typeof i&&s<i||"function"==typeof i&&i(s,e);var p;!r&&f?(s++,t.onFail?.(s,e),(p=u,new Promise(t=>{setTimeout(t,p)})).then(()=>a()?void 0:l()).then(()=>{r?c(e):h()})):c(e)})};return{promise:i,cancel:e=>{n||(c(new mt(e)),t.abort?.())},continue:()=>(e?.(),i),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:o,start:()=>(o()?h():l().then(h),i)}}var wt=t=>setTimeout(t,0);var Ot=function(){let t=[],e=0,r=t=>{t()},s=t=>{t()},n=wt;const i=s=>{e?t.push(s):n(()=>{r(s)})};return{batch:i=>{let a;e++;try{a=i()}finally{e--,e||(()=>{const e=t;t=[],e.length&&n(()=>{s(()=>{e.forEach(t=>{r(t)})})})})()}return a},batchCalls:t=>(...e)=>{i(()=>{t(...e)})},schedule:i,setNotifyFunction:t=>{r=t},setBatchNotifyFunction:t=>{s=t},setScheduler:t=>{n=t}}}(),kt=(c=class{constructor(){N(this,u)}destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&H(this,u,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(X?1/0:3e5))}clearGcTimeout(){L(this,u)&&(clearTimeout(L(this,u)),H(this,u,void 0))}},u=new WeakMap,c),St=(b=class extends kt{constructor(t){super(),N(this,m),N(this,l),N(this,h),N(this,f),N(this,p),N(this,d),N(this,y),N(this,v),H(this,v,!1),H(this,y,t.defaultOptions),this.setOptions(t.options),this.observers=[],H(this,p,t.client),H(this,f,L(this,p).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,H(this,l,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,r=void 0!==e,s=r?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:r?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??L(this,l),this.scheduleGc()}get meta(){return this.options.meta}get promise(){return L(this,d)?.promise}setOptions(t){this.options={...L(this,y),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||L(this,f).remove(this)}setData(t,e){const r=function(t,e,r){if("function"==typeof r.structuralSharing)return r.structuralSharing(t,e);if(!1!==r.structuralSharing){try{return it(t,e)}catch(s){throw s}return it(t,e)}return e}(this.state.data,t,this.options);return G(this,m,g).call(this,{data:r,type:"success",dataUpdatedAt:e?.updatedAt,manual:e?.manual}),r}setState(t,e){G(this,m,g).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){const e=L(this,d)?.promise;return L(this,d)?.cancel(t),e?e.then(V).catch(V):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(L(this,l))}isActive(){return this.observers.some(t=>{return!1!==(e=t.options.enabled,r=this,"function"==typeof e?e(r):e);var e,r})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===ht||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0&&this.observers.some(t=>"static"===Z(t.options.staleTime,this))}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):void 0===this.state.data||this.state.isInvalidated}isStaleByTime(t=0){return void 0===this.state.data||"static"!==t&&(!!this.state.isInvalidated||!function(t,e){return Math.max(t+(e||0)-Date.now(),0)}(this.state.dataUpdatedAt,t))}onFocus(){const t=this.observers.find(t=>t.shouldFetchOnWindowFocus());t?.refetch({cancelRefetch:!1}),L(this,d)?.continue()}onOnline(){const t=this.observers.find(t=>t.shouldFetchOnReconnect());t?.refetch({cancelRefetch:!1}),L(this,d)?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),L(this,f).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(e=>e!==t),this.observers.length||(L(this,d)&&(L(this,v)?L(this,d).cancel({revert:!0}):L(this,d).cancelRetry()),this.scheduleGc()),L(this,f).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||G(this,m,g).call(this,{type:"invalidate"})}fetch(t,e){if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&e?.cancelRefetch)this.cancel({silent:!0});else if(L(this,d))return L(this,d).continueRetry(),L(this,d).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find(t=>t.options.queryFn);t&&this.setOptions(t.options)}Array.isArray(this.options.queryKey);const r=new AbortController,s=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(H(this,v,!0),r.signal)})},n=()=>{const t=ft(this.options,e),r=(()=>{const t={client:L(this,p),queryKey:this.queryKey,meta:this.meta};return s(t),t})();return H(this,v,!1),this.options.persister?this.options.persister(t,r,this):t(r)},i=(()=>{const t={fetchOptions:e,options:this.options,queryKey:this.queryKey,client:L(this,p),state:this.state,fetchFn:n};return s(t),t})();this.options.behavior?.onFetch(i,this),H(this,h,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===i.fetchOptions?.meta||G(this,m,g).call(this,{type:"fetch",meta:i.fetchOptions?.meta});const a=t=>{gt(t)&&t.silent||G(this,m,g).call(this,{type:"error",error:t}),gt(t)||(L(this,f).config.onError?.(t,this),L(this,f).config.onSettled?.(this.state.data,t,this)),this.scheduleGc()};return H(this,d,bt({initialPromise:e?.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:t=>{if(void 0!==t){try{this.setData(t)}catch(e){return void a(e)}L(this,f).config.onSuccess?.(t,this),L(this,f).config.onSettled?.(t,this.state.error,this),this.scheduleGc()}else a(new Error(`${this.queryHash} data is undefined`))},onError:a,onFail:(t,e)=>{G(this,m,g).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{G(this,m,g).call(this,{type:"pause"})},onContinue:()=>{G(this,m,g).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),L(this,d).start()}},l=new WeakMap,h=new WeakMap,f=new WeakMap,p=new WeakMap,d=new WeakMap,y=new WeakMap,v=new WeakMap,m=new WeakSet,g=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...(r=e.data,s=this.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:vt(s.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return gt(n)&&n.revert&&L(this,h)?{...L(this,h),fetchStatus:"idle"}:{...e,error:n,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}var r,s})(this.state),Ot.batch(()=>{this.observers.forEach(t=>{t.onQueryUpdate()}),L(this,f).notify({query:this,type:"updated",action:t})})},b);var Pt=(O=class extends J{constructor(t={}){super(),N(this,w),this.config=t,H(this,w,new Map)}build(t,e,r){const s=e.queryKey,n=e.queryHash??rt(s,e);let i=this.get(n);return i||(i=new St({client:t,queryKey:s,queryHash:n,options:t.defaultQueryOptions(e),state:r,defaultOptions:t.getQueryDefaults(s)}),this.add(i)),i}add(t){L(this,w).has(t.queryHash)||(L(this,w).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=L(this,w).get(t.queryHash);e&&(t.destroy(),e===t&&L(this,w).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Ot.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return L(this,w).get(t)}getAll(){return[...L(this,w).values()]}find(t){const e={exact:!0,...t};return this.getAll().find(t=>tt(e,t))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter(e=>tt(t,e)):e}notify(t){Ot.batch(()=>{this.listeners.forEach(e=>{e(t)})})}onFocus(){Ot.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Ot.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},w=new WeakMap,O),jt=(F=class extends kt{constructor(t){super(),N(this,j),N(this,k),N(this,S),N(this,P),this.mutationId=t.mutationId,H(this,S,t.mutationCache),H(this,k,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){L(this,k).includes(t)||(L(this,k).push(t),this.clearGcTimeout(),L(this,S).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){H(this,k,L(this,k).filter(e=>e!==t)),this.scheduleGc(),L(this,S).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){L(this,k).length||("pending"===this.state.status?this.scheduleGc():L(this,S).remove(this))}continue(){return L(this,P)?.continue()??this.execute(this.state.variables)}async execute(t){const e=()=>{G(this,j,E).call(this,{type:"continue"})};H(this,P,bt({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{G(this,j,E).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{G(this,j,E).call(this,{type:"pause"})},onContinue:e,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>L(this,S).canRun(this)}));const r="pending"===this.state.status,s=!L(this,P).canStart();try{if(r)e();else{G(this,j,E).call(this,{type:"pending",variables:t,isPaused:s}),await(L(this,S).config.onMutate?.(t,this));const e=await(this.options.onMutate?.(t));e!==this.state.context&&G(this,j,E).call(this,{type:"pending",context:e,variables:t,isPaused:s})}const n=await L(this,P).start();return await(L(this,S).config.onSuccess?.(n,t,this.state.context,this)),await(this.options.onSuccess?.(n,t,this.state.context)),await(L(this,S).config.onSettled?.(n,null,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(n,null,t,this.state.context)),G(this,j,E).call(this,{type:"success",data:n}),n}catch(n){try{throw await(L(this,S).config.onError?.(n,t,this.state.context,this)),await(this.options.onError?.(n,t,this.state.context)),await(L(this,S).config.onSettled?.(void 0,n,this.state.variables,this.state.context,this)),await(this.options.onSettled?.(void 0,n,t,this.state.context)),n}finally{G(this,j,E).call(this,{type:"error",error:n})}}finally{L(this,S).runNext(this)}}},k=new WeakMap,S=new WeakMap,P=new WeakMap,j=new WeakSet,E=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),Ot.batch(()=>{L(this,k).forEach(e=>{e.onMutationUpdate(t)}),L(this,S).notify({mutation:this,type:"updated",action:t})})},F);var Et=(q=class extends J{constructor(t={}){super(),N(this,M),N(this,R),N(this,C),this.config=t,H(this,M,new Set),H(this,R,new Map),H(this,C,0)}build(t,e,r){const s=new jt({mutationCache:this,mutationId:++z(this,C)._,options:t.defaultMutationOptions(e),state:r});return this.add(s),s}add(t){L(this,M).add(t);const e=Ft(t);if("string"==typeof e){const r=L(this,R).get(e);r?r.push(t):L(this,R).set(e,[t])}this.notify({type:"added",mutation:t})}remove(t){if(L(this,M).delete(t)){const e=Ft(t);if("string"==typeof e){const r=L(this,R).get(e);if(r)if(r.length>1){const e=r.indexOf(t);-1!==e&&r.splice(e,1)}else r[0]===t&&L(this,R).delete(e)}}this.notify({type:"removed",mutation:t})}canRun(t){const e=Ft(t);if("string"==typeof e){const r=L(this,R).get(e),s=r?.find(t=>"pending"===t.state.status);return!s||s===t}return!0}runNext(t){const e=Ft(t);if("string"==typeof e){const r=L(this,R).get(e)?.find(e=>e!==t&&e.state.isPaused);return r?.continue()??Promise.resolve()}return Promise.resolve()}clear(){Ot.batch(()=>{L(this,M).forEach(t=>{this.notify({type:"removed",mutation:t})}),L(this,M).clear(),L(this,R).clear()})}getAll(){return Array.from(L(this,M))}find(t){const e={exact:!0,...t};return this.getAll().find(t=>et(e,t))}findAll(t={}){return this.getAll().filter(e=>et(t,e))}notify(t){Ot.batch(()=>{this.listeners.forEach(e=>{e(t)})})}resumePausedMutations(){const t=this.getAll().filter(t=>t.state.isPaused);return Ot.batch(()=>Promise.all(t.map(t=>t.continue().catch(V))))}},M=new WeakMap,R=new WeakMap,C=new WeakMap,q);function Ft(t){return t.options.scope?.id}function Mt(t){return{onFetch:(e,r)=>{const s=e.options,n=e.fetchOptions?.meta?.fetchMore?.direction,i=e.state.data?.pages||[],a=e.state.data?.pageParams||[];let o={pages:[],pageParams:[]},u=0;const c=async()=>{let r=!1;const c=ft(e.options,e.fetchOptions),l=async(t,s,n)=>{if(r)return Promise.reject();if(null==s&&t.pages.length)return Promise.resolve(t);const i=(()=>{const t={client:e.client,queryKey:e.queryKey,pageParam:s,direction:n?"backward":"forward",meta:e.options.meta};var i;return i=t,Object.defineProperty(i,"signal",{enumerable:!0,get:()=>(e.signal.aborted?r=!0:e.signal.addEventListener("abort",()=>{r=!0}),e.signal)}),t})(),a=await c(i),{maxPages:o}=e.options,u=n?lt:ct;return{pages:u(t.pages,a,o),pageParams:u(t.pageParams,s,o)}};if(n&&i.length){const t="backward"===n,e={pages:i,pageParams:a},r=(t?Ct:Rt)(s,e);o=await l(e,r,t)}else{const e=t??i.length;do{const t=0===u?a[0]??s.initialPageParam:Rt(s,o);if(u>0&&null==t)break;o=await l(o,t),u++}while(u<e)}return o};e.options.persister?e.fetchFn=()=>e.options.persister?.(c,{client:e.client,queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},r):e.fetchFn=c}}}function Rt(t,{pages:e,pageParams:r}){const s=e.length-1;return e.length>0?t.getNextPageParam(e[s],e,r[s],r):void 0}function Ct(t,{pages:e,pageParams:r}){return e.length>0?t.getPreviousPageParam?.(e[0],e,r[0],r):void 0}var qt,_t,xt=(U=class{constructor(t={}){N(this,_),N(this,x),N(this,D),N(this,T),N(this,A),N(this,W),N(this,Q),N(this,K),H(this,_,t.queryCache||new Pt),H(this,x,t.mutationCache||new Et),H(this,D,t.defaultOptions||{}),H(this,T,new Map),H(this,A,new Map),H(this,W,0)}mount(){z(this,W)._++,1===L(this,W)&&(H(this,Q,pt.subscribe(async t=>{t&&(await this.resumePausedMutations(),L(this,_).onFocus())})),H(this,K,dt.subscribe(async t=>{t&&(await this.resumePausedMutations(),L(this,_).onOnline())})))}unmount(){var t,e;z(this,W)._--,0===L(this,W)&&(null==(t=L(this,Q))||t.call(this),H(this,Q,void 0),null==(e=L(this,K))||e.call(this),H(this,K,void 0))}isFetching(t){return L(this,_).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return L(this,x).findAll({...t,status:"pending"}).length}getQueryData(t){const e=this.defaultQueryOptions({queryKey:t});return L(this,_).get(e.queryHash)?.state.data}ensureQueryData(t){const e=this.defaultQueryOptions(t),r=L(this,_).build(this,e),s=r.state.data;return void 0===s?this.fetchQuery(t):(t.revalidateIfStale&&r.isStaleByTime(Z(e.staleTime,r))&&this.prefetchQuery(e),Promise.resolve(s))}getQueriesData(t){return L(this,_).findAll(t).map(({queryKey:t,state:e})=>[t,e.data])}setQueryData(t,e,r){const s=this.defaultQueryOptions({queryKey:t}),n=L(this,_).get(s.queryHash),i=n?.state.data,a=function(t,e){return"function"==typeof t?t(e):t}(e,i);if(void 0!==a)return L(this,_).build(this,s).setData(a,{...r,manual:!0})}setQueriesData(t,e,r){return Ot.batch(()=>L(this,_).findAll(t).map(({queryKey:t})=>[t,this.setQueryData(t,e,r)]))}getQueryState(t){const e=this.defaultQueryOptions({queryKey:t});return L(this,_).get(e.queryHash)?.state}removeQueries(t){const e=L(this,_);Ot.batch(()=>{e.findAll(t).forEach(t=>{e.remove(t)})})}resetQueries(t,e){const r=L(this,_);return Ot.batch(()=>(r.findAll(t).forEach(t=>{t.reset()}),this.refetchQueries({type:"active",...t},e)))}cancelQueries(t,e={}){const r={revert:!0,...e},s=Ot.batch(()=>L(this,_).findAll(t).map(t=>t.cancel(r)));return Promise.all(s).then(V).catch(V)}invalidateQueries(t,e={}){return Ot.batch(()=>(L(this,_).findAll(t).forEach(t=>{t.invalidate()}),"none"===t?.refetchType?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},e)))}refetchQueries(t,e={}){const r={...e,cancelRefetch:e.cancelRefetch??!0},s=Ot.batch(()=>L(this,_).findAll(t).filter(t=>!t.isDisabled()&&!t.isStatic()).map(t=>{let e=t.fetch(void 0,r);return r.throwOnError||(e=e.catch(V)),"paused"===t.state.fetchStatus?Promise.resolve():e}));return Promise.all(s).then(V)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const r=L(this,_).build(this,e);return r.isStaleByTime(Z(e.staleTime,r))?r.fetch(e):Promise.resolve(r.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(V).catch(V)}fetchInfiniteQuery(t){return t.behavior=Mt(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(V).catch(V)}ensureInfiniteQueryData(t){return t.behavior=Mt(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return dt.isOnline()?L(this,x).resumePausedMutations():Promise.resolve()}getQueryCache(){return L(this,_)}getMutationCache(){return L(this,x)}getDefaultOptions(){return L(this,D)}setDefaultOptions(t){H(this,D,t)}setQueryDefaults(t,e){L(this,T).set(st(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...L(this,T).values()],r={};return e.forEach(e=>{nt(t,e.queryKey)&&Object.assign(r,e.defaultOptions)}),r}setMutationDefaults(t,e){L(this,A).set(st(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...L(this,A).values()],r={};return e.forEach(e=>{nt(t,e.mutationKey)&&Object.assign(r,e.defaultOptions)}),r}defaultQueryOptions(t){if(t._defaulted)return t;const e={...L(this,D).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=rt(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),e.queryFn===ht&&(e.enabled=!1),e}defaultMutationOptions(t){return t?._defaulted?t:{...L(this,D).mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){L(this,_).clear(),L(this,x).clear()}},_=new WeakMap,x=new WeakMap,D=new WeakMap,T=new WeakMap,A=new WeakMap,W=new WeakMap,Q=new WeakMap,K=new WeakMap,U),Dt={exports:{}},Tt={};function At(){return qt||(qt=1,function(){var t=Y(),e=Symbol.for("react.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),n=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),o=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),l=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.for("react.offscreen"),d=Symbol.iterator;var y=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function v(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),s=1;s<e;s++)r[s-1]=arguments[s];!function(t,e,r){var s=y.ReactDebugCurrentFrame.getStackAddendum();""!==s&&(e+="%s",r=r.concat([s]));var n=r.map(function(t){return String(t)});n.unshift("Warning: "+e),Function.prototype.apply.call(console[t],console,n)}("error",t,r)}var m;function g(t){return t.displayName||"Context"}function b(t){if(null==t)return null;if("number"==typeof t.tag&&v("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),"function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t;switch(t){case s:return"Fragment";case r:return"Portal";case i:return"Profiler";case n:return"StrictMode";case c:return"Suspense";case l:return"SuspenseList"}if("object"==typeof t)switch(t.$$typeof){case o:return g(t)+".Consumer";case a:return g(t._context)+".Provider";case u:return function(t,e,r){var s=t.displayName;if(s)return s;var n=e.displayName||e.name||"";return""!==n?r+"("+n+")":r}(t,t.render,"ForwardRef");case h:var e=t.displayName||null;return null!==e?e:b(t.type)||"Memo";case f:var p=t,d=p._payload,y=p._init;try{return b(y(d))}catch(m){return null}}return null}m=Symbol.for("react.module.reference");var w,O,k,S,P,j,E,F=Object.assign,M=0;function R(){}R.__reactDisabledLog=!0;var C,q=y.ReactCurrentDispatcher;function _(t,e,r){if(void 0===C)try{throw Error()}catch(n){var s=n.stack.trim().match(/\n( *(at )?)/);C=s&&s[1]||""}return"\n"+C+t}var x,D=!1,T="function"==typeof WeakMap?WeakMap:Map;function A(t,e){if(!t||D)return"";var r,s=x.get(t);if(void 0!==s)return s;D=!0;var n,i=Error.prepareStackTrace;Error.prepareStackTrace=void 0,n=q.current,q.current=null,function(){if(0===M){w=console.log,O=console.info,k=console.warn,S=console.error,P=console.group,j=console.groupCollapsed,E=console.groupEnd;var t={configurable:!0,enumerable:!0,value:R,writable:!0};Object.defineProperties(console,{info:t,log:t,warn:t,error:t,group:t,groupCollapsed:t,groupEnd:t})}M++}();try{if(e){var a=function(){throw Error()};if(Object.defineProperty(a.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(a,[])}catch(d){r=d}Reflect.construct(t,[],a)}else{try{a.call()}catch(d){r=d}t.call(a.prototype)}}else{try{throw Error()}catch(d){r=d}t()}}catch(y){if(y&&r&&"string"==typeof y.stack){for(var o=y.stack.split("\n"),u=r.stack.split("\n"),c=o.length-1,l=u.length-1;c>=1&&l>=0&&o[c]!==u[l];)l--;for(;c>=1&&l>=0;c--,l--)if(o[c]!==u[l]){if(1!==c||1!==l)do{if(c--,--l<0||o[c]!==u[l]){var h="\n"+o[c].replace(" at new "," at ");return t.displayName&&h.includes("<anonymous>")&&(h=h.replace("<anonymous>",t.displayName)),"function"==typeof t&&x.set(t,h),h}}while(c>=1&&l>=0);break}}}finally{D=!1,q.current=n,function(){if(0===--M){var t={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:F({},t,{value:w}),info:F({},t,{value:O}),warn:F({},t,{value:k}),error:F({},t,{value:S}),group:F({},t,{value:P}),groupCollapsed:F({},t,{value:j}),groupEnd:F({},t,{value:E})})}M<0&&v("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}(),Error.prepareStackTrace=i}var f=t?t.displayName||t.name:"",p=f?_(f):"";return"function"==typeof t&&x.set(t,p),p}function W(t,e,r){if(null==t)return"";if("function"==typeof t)return A(t,!(!(s=t.prototype)||!s.isReactComponent));var s;if("string"==typeof t)return _(t);switch(t){case c:return _("Suspense");case l:return _("SuspenseList")}if("object"==typeof t)switch(t.$$typeof){case u:return A(t.render,!1);case h:return W(t.type,e,r);case f:var n=t,i=n._payload,a=n._init;try{return W(a(i),e,r)}catch(o){}}return""}x=new T;var Q=Object.prototype.hasOwnProperty,K={},U=y.ReactDebugCurrentFrame;function $(t){if(t){var e=t._owner,r=W(t.type,t._source,e?e.type:null);U.setExtraStackFrame(r)}else U.setExtraStackFrame(null)}var I=Array.isArray;function L(t){return I(t)}function N(t){return""+t}function H(t){if(function(t){try{return N(t),!1}catch(e){return!0}}(t))return v("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",function(t){return"function"==typeof Symbol&&Symbol.toStringTag&&t[Symbol.toStringTag]||t.constructor.name||"Object"}(t)),N(t)}var G,z,B=y.ReactCurrentOwner,J={key:!0,ref:!0,__self:!0,__source:!0};function X(t,r,s,n,i){var a,o={},u=null,c=null;for(a in void 0!==s&&(H(s),u=""+s),function(t){if(Q.call(t,"key")){var e=Object.getOwnPropertyDescriptor(t,"key").get;if(e&&e.isReactWarning)return!1}return void 0!==t.key}(r)&&(H(r.key),u=""+r.key),function(t){if(Q.call(t,"ref")){var e=Object.getOwnPropertyDescriptor(t,"ref").get;if(e&&e.isReactWarning)return!1}return void 0!==t.ref}(r)&&(c=r.ref,function(t){"string"==typeof t.ref&&B.current}(r)),r)Q.call(r,a)&&!J.hasOwnProperty(a)&&(o[a]=r[a]);if(t&&t.defaultProps){var l=t.defaultProps;for(a in l)void 0===o[a]&&(o[a]=l[a])}if(u||c){var h="function"==typeof t?t.displayName||t.name||"Unknown":t;u&&function(t,e){var r=function(){G||(G=!0,v("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",e))};r.isReactWarning=!0,Object.defineProperty(t,"key",{get:r,configurable:!0})}(o,h),c&&function(t,e){var r=function(){z||(z=!0,v("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",e))};r.isReactWarning=!0,Object.defineProperty(t,"ref",{get:r,configurable:!0})}(o,h)}return function(t,r,s,n,i,a,o){var u={$$typeof:e,type:t,key:r,ref:s,props:o,_owner:a,_store:{}};return Object.defineProperty(u._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(u,"_self",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(u,"_source",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.freeze&&(Object.freeze(u.props),Object.freeze(u)),u}(t,u,c,i,n,B.current,o)}var V,Z=y.ReactCurrentOwner,tt=y.ReactDebugCurrentFrame;function et(t){if(t){var e=t._owner,r=W(t.type,t._source,e?e.type:null);tt.setExtraStackFrame(r)}else tt.setExtraStackFrame(null)}function rt(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}function st(){if(Z.current){var t=b(Z.current.type);if(t)return"\n\nCheck the render method of `"+t+"`."}return""}V=!1;var nt={};function it(t,e){if(t._store&&!t._store.validated&&null==t.key){t._store.validated=!0;var r=function(t){var e=st();if(!e){var r="string"==typeof t?t:t.displayName||t.name;r&&(e="\n\nCheck the top-level render call using <"+r+">.")}return e}(e);if(!nt[r]){nt[r]=!0;var s="";t&&t._owner&&t._owner!==Z.current&&(s=" It was passed a child from "+b(t._owner.type)+"."),et(t),v('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,s),et(null)}}}function at(t,e){if("object"==typeof t)if(L(t))for(var r=0;r<t.length;r++){var s=t[r];rt(s)&&it(s,e)}else if(rt(t))t._store&&(t._store.validated=!0);else if(t){var n=function(t){if(null===t||"object"!=typeof t)return null;var e=d&&t[d]||t["@@iterator"];return"function"==typeof e?e:null}(t);if("function"==typeof n&&n!==t.entries)for(var i,a=n.call(t);!(i=a.next()).done;)rt(i.value)&&it(i.value,e)}}function ot(t){var e,r=t.type;if(null!=r&&"string"!=typeof r){if("function"==typeof r)e=r.propTypes;else{if("object"!=typeof r||r.$$typeof!==u&&r.$$typeof!==h)return;e=r.propTypes}if(e){var s=b(r);!function(t,e,r,s,n){var i=Function.call.bind(Q);for(var a in t)if(i(t,a)){var o=void 0;try{if("function"!=typeof t[a]){var u=Error((s||"React class")+": "+r+" type `"+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof t[a]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw u.name="Invariant Violation",u}o=t[a](e,a,s,r,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(c){o=c}!o||o instanceof Error||($(n),v("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",s||"React class",r,a,typeof o),$(null)),o instanceof Error&&!(o.message in K)&&(K[o.message]=!0,$(n),v("Failed %s type: %s",r,o.message),$(null))}}(e,t.props,"prop",s,t)}else if(void 0!==r.PropTypes&&!V){V=!0,v("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",b(r)||"Unknown")}"function"!=typeof r.getDefaultProps||r.getDefaultProps.isReactClassApproved||v("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}var ut={};function ct(t,r,d,y,g,w){var O=function(t){return"string"==typeof t||"function"==typeof t||t===s||t===i||t===n||t===c||t===l||t===p||"object"==typeof t&&null!==t&&(t.$$typeof===f||t.$$typeof===h||t.$$typeof===a||t.$$typeof===o||t.$$typeof===u||t.$$typeof===m||void 0!==t.getModuleId)}(t);if(!O){var k="";(void 0===t||"object"==typeof t&&null!==t&&0===Object.keys(t).length)&&(k+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var S;k+=st(),null===t?S="null":L(t)?S="array":void 0!==t&&t.$$typeof===e?(S="<"+(b(t.type)||"Unknown")+" />",k=" Did you accidentally export a JSX literal instead of a component?"):S=typeof t,v("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",S,k)}var P=X(t,r,d,g,w);if(null==P)return P;if(O){var j=r.children;if(void 0!==j)if(y)if(L(j)){for(var E=0;E<j.length;E++)at(j[E],t);Object.freeze&&Object.freeze(j)}else v("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else at(j,t)}if(Q.call(r,"key")){var F=b(t),M=Object.keys(r).filter(function(t){return"key"!==t}),R=M.length>0?"{key: someKey, "+M.join(": ..., ")+": ...}":"{key: someKey}";if(!ut[F+R])v('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />',R,F,M.length>0?"{"+M.join(": ..., ")+": ...}":"{}",F),ut[F+R]=!0}return t===s?function(t){for(var e=Object.keys(t.props),r=0;r<e.length;r++){var s=e[r];if("children"!==s&&"key"!==s){et(t),v("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",s),et(null);break}}null!==t.ref&&(et(t),v("Invalid attribute `ref` supplied to `React.Fragment`."),et(null))}(P):ot(P),P}var lt=function(t,e,r){return ct(t,e,r,!1)},ht=function(t,e,r){return ct(t,e,r,!0)};Tt.Fragment=s,Tt.jsx=lt,Tt.jsxs=ht}()),Tt}var Wt=(_t||(_t=1,Dt.exports=At()),Dt.exports),Qt=B.createContext(void 0),Kt=({client:t,children:e})=>(B.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),Wt.jsx(Qt.Provider,{value:t,children:e}));export{xt as Q,Kt as a,Wt as j};
