/* 🎯 NAVEGAÇÃO UNIFICADA DOS DASHBOARDS PREMIUM */

.navigation {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.navigation.compact {
  padding: 16px;
  margin-bottom: 16px;
}

.navigationHeader {
  text-align: center;
  margin-bottom: 24px;
  color: white;
}

.title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.titleIcon {
  font-size: 1.8rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  font-weight: 400;
}

.dashboardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.compact .dashboardGrid {
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.dashboardCard {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  color: white;
  text-align: center;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.compact .dashboardCard {
  padding: 16px;
  min-height: 80px;
}

.dashboardCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.dashboardCard.active {
  background: var(--dashboard-gradient);
  border-color: var(--dashboard-color);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

.dashboardCard.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.cardIcon {
  font-size: 2.5rem;
  margin-bottom: 12px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.compact .cardIcon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.cardLabel {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.compact .cardLabel {
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.cardDescription {
  font-size: 0.8rem;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.activeIndicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.activeIcon {
  font-size: 0.8rem;
  font-weight: bold;
  color: #10b981;
}

.statusIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
}

.statusDot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.statusText {
  font-weight: 500;
}

/* Responsividade */
@media (max-width: 768px) {
  .navigation {
    padding: 16px;
    margin-bottom: 16px;
  }

  .dashboardGrid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
  }

  .dashboardCard {
    padding: 16px;
    min-height: 100px;
  }

  .cardIcon {
    font-size: 2rem;
  }

  .cardLabel {
    font-size: 0.9rem;
  }

  .cardDescription {
    font-size: 0.75rem;
  }

  .title {
    font-size: 1.3rem;
  }

  .subtitle {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .dashboardGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboardCard {
    min-height: 80px;
    padding: 12px;
  }

  .cardIcon {
    font-size: 1.8rem;
    margin-bottom: 6px;
  }

  .cardLabel {
    font-size: 0.8rem;
    margin-bottom: 4px;
  }

  .cardDescription {
    display: none; /* Ocultar descrição em telas muito pequenas */
  }
}
