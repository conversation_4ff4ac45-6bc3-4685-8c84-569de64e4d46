<!DOCTYPE htm        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            color: white;
        }ml lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Color Match V3 - Preview Interativo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 1rem;
            color: white;
        }
        
        .game-container {
            max-width: 900px;
            margin: 0 auto;
            width: 100%;
        }
        
        .header {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 1rem;
            padding: 1rem 3rem 1rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            min-height: 60px;
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0;
            color: white;
            text-align: center;
            flex: 1;
        }
        
        .header-tts-button {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
            color: white;
            z-index: 10;
        }
        
        .header-tts-button:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.05);
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            min-width: 80px;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 0.25rem;
        }
        
        .activity-selector {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .activity-selector h3 {
            color: white;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .activity-buttons {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .activity-btn {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .activity-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-1px);
        }
        
        .activity-btn.active {
            background: rgba(76, 175, 80, 0.4);
            border-color: rgba(76, 175, 80, 0.6);
        }
        
        .game-area {
            padding: 0;
        }
        
        .activity {
            display: none;
        }
        
        .activity.active {
            display: block;
        }
        
        .instruction {
            text-align: center;
            margin-bottom: 2rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1.5rem;
        }
        
        .instruction h3 {
            color: white;
            margin-bottom: 0.5rem;
            font-size: 1.5rem;
        }
        
        .instruction p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.1rem;
        }
        
        .color-target {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin: 1rem 0;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            border: 2px dashed rgba(255, 255, 255, 0.4);
        }
        
        .color-sample {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .color-name {
            font-size: 1.8rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .items-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
        }
        
        .color-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
        }
        
        .color-item:hover {
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .color-item.correct {
            background: rgba(76, 175, 80, 0.4);
            border-color: rgba(76, 175, 80, 0.7);
            animation: correctFlash 0.5s ease-in-out;
        }
        
        .color-item.incorrect {
            background: rgba(244, 67, 54, 0.4);
            border-color: rgba(244, 67, 54, 0.7);
            animation: incorrectShake 0.5s ease-in-out;
        }
        
        .color-item.selected {
            background: rgba(33, 150, 243, 0.4);
            border-color: rgba(33, 150, 243, 0.7);
        }
        
        @keyframes correctFlash {
            0%, 100% { background: rgba(76, 175, 80, 0.4); }
            50% { background: rgba(76, 175, 80, 0.7); }
        }
        
        @keyframes incorrectShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .item-emoji {
            font-size: 2.5rem;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .item-name {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }
        
        .progress-bar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            height: 8px;
            margin: 1rem 0;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 20px;
        }
        
        .timer {
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 1rem 0;
            padding: 0.5rem;
            background: rgba(255, 193, 7, 0.2);
            border-radius: 8px;
        }
        
        .speed-challenge-timer {
            background: rgba(255, 87, 34, 0.3);
            color: white;
            animation: timerPulse 1s ease-in-out infinite;
        }
        
        @keyframes timerPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .sequence-display {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .sequence-color {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 2px solid white;
            position: relative;
            animation: sequencePulse 1s ease-in-out infinite;
        }
        
        @keyframes sequencePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .sequence-number {
            position: absolute;
            top: -8px;
            right: -8px;
            background: white;
            color: #333;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
        }
        
        .gradient-options {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            flex-wrap: wrap;
        }
        
        .gradient-option {
            width: 100px;
            height: 100px;
            border-radius: 12px;
            border: 3px solid white;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .gradient-option:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .accessibility-controls {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        
        .accessibility-btn {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }
        
        .accessibility-btn:hover {
            background: rgba(255, 255, 255, 0.25);
        }
        
        @media (max-width: 600px) {
            .items-grid {
                grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
                gap: 0.5rem;
            }
            
            .item-emoji {
                font-size: 2rem;
            }
            
            .color-sample {
                width: 60px;
                height: 60px;
            }
            
            .activity-buttons {
                gap: 0.25rem;
            }
            
            .activity-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Header -->
        <div class="header">
            <h1>🎨 Color Match V3 - Preview Interativo</h1>
            <button class="header-tts-button" onclick="toggleTTS()" title="Ativar/Desativar TTS">
                🔊
            </button>
        </div>
        
        <!-- Stats -->
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">5</div>
                <div class="stat-label">Rodada</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">180</div>
                <div class="stat-label">Pontos</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">88%</div>
                <div class="stat-label">Precisão</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">15s</div>
                <div class="stat-label">Tempo</div>
            </div>
        </div>
        
        <!-- Activity Selector -->
        <div class="activity-selector">
            <h3>🎮 <strong>PREVIEW:</strong> Clique para testar cada atividade:</h3>
            <div class="activity-buttons">
                <button class="activity-btn active" onclick="showActivity('basic-matching')">🎨 Básico</button>
                <button class="activity-btn" onclick="showActivity('speed-challenge')">⚡ Velocidade</button>
                <button class="activity-btn" onclick="showActivity('color-memory')">🧠 Memória</button>
                <button class="activity-btn" onclick="showActivity('shade-discrimination')">🎭 Tons</button>
                <button class="activity-btn" onclick="showActivity('sequence-colors')">🔗 Sequência</button>
                <button class="activity-btn" onclick="showActivity('gradient-matching')">🌈 Gradiente</button>
            </div>
        </div>
        
        <!-- Game Area -->
        <div class="game-area">
            
            <!-- 1. Basic Color Matching -->
            <div id="basic-matching" class="activity active">
                <div class="instruction">
                    <h3>Encontre todos os itens VERMELHOS!</h3>
                    <p>Clique em todos os objetos que têm a cor vermelha</p>
                </div>
                
                <div class="color-target">
                    <div class="color-sample" style="background: #e91e63;"></div>
                    <div class="color-name">VERMELHO</div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 60%;"></div>
                </div>
                <div style="text-align: center; margin-bottom: 1rem;">
                    Progresso: 3/5 itens encontrados
                </div>
                
                <div class="items-grid">
                    <div class="color-item correct" onclick="selectItem(this, true)">
                        <span class="item-emoji">🍎</span>
                        <span class="item-name">Maçã</span>
                    </div>
                    <div class="color-item" onclick="selectItem(this, false)">
                        <span class="item-emoji">🍌</span>
                        <span class="item-name">Banana</span>
                    </div>
                    <div class="color-item correct" onclick="selectItem(this, true)">
                        <span class="item-emoji">🍓</span>
                        <span class="item-name">Morango</span>
                    </div>
                    <div class="color-item" onclick="selectItem(this, false)">
                        <span class="item-emoji">🍃</span>
                        <span class="item-name">Folha</span>
                    </div>
                    <div class="color-item correct" onclick="selectItem(this, true)">
                        <span class="item-emoji">❤️</span>
                        <span class="item-name">Coração</span>
                    </div>
                    <div class="color-item" onclick="selectItem(this, false)">
                        <span class="item-emoji">🟡</span>
                        <span class="item-name">Círculo</span>
                    </div>
                </div>
            </div>
            
            <!-- 2. Speed Challenge -->
            <div id="speed-challenge" class="activity">
                <div class="instruction">
                    <h3>Desafio de Velocidade: AZUL</h3>
                    <p>Encontre rapidamente todos os itens azuis!</p>
                </div>
                
                <div class="timer speed-challenge-timer">
                    ⏱️ Tempo restante: 12 segundos
                </div>
                
                <div class="color-target">
                    <div class="color-sample" style="background: #2196F3;"></div>
                    <div class="color-name">AZUL</div>
                </div>
                
                <div class="items-grid">
                    <div class="color-item" onclick="selectItem(this, true)">
                        <span class="item-emoji">🔵</span>
                        <span class="item-name">Círculo</span>
                    </div>
                    <div class="color-item" onclick="selectItem(this, false)">
                        <span class="item-emoji">🔴</span>
                        <span class="item-name">Círculo</span>
                    </div>
                    <div class="color-item" onclick="selectItem(this, true)">
                        <span class="item-emoji">🌊</span>
                        <span class="item-name">Onda</span>
                    </div>
                    <div class="color-item" onclick="selectItem(this, false)">
                        <span class="item-emoji">🟢</span>
                        <span class="item-name">Verde</span>
                    </div>
                </div>
            </div>
            
            <!-- 3. Color Memory -->
            <div id="color-memory" class="activity">
                <div class="instruction">
                    <h3>Memória de Cores</h3>
                    <p>Memorize a sequência e reproduza na ordem correta</p>
                </div>
                
                <div class="sequence-display">
                    <div class="sequence-color" style="background: #e91e63;">
                        <div class="sequence-number">1</div>
                    </div>
                    <div class="sequence-color" style="background: #4CAF50;">
                        <div class="sequence-number">2</div>
                    </div>
                    <div class="sequence-color" style="background: #FFC107;">
                        <div class="sequence-number">3</div>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 1rem 0;">
                    <p>Agora clique nas cores na mesma ordem:</p>
                </div>
                
                <div class="items-grid">
                    <div class="color-item" onclick="selectSequence(this, '#4CAF50')">
                        <span class="item-emoji">🟢</span>
                        <span class="item-name">Verde</span>
                    </div>
                    <div class="color-item" onclick="selectSequence(this, '#e91e63')">
                        <span class="item-emoji">🔴</span>
                        <span class="item-name">Vermelho</span>
                    </div>
                    <div class="color-item" onclick="selectSequence(this, '#FFC107')">
                        <span class="item-emoji">🟡</span>
                        <span class="item-name">Amarelo</span>
                    </div>
                    <div class="color-item" onclick="selectSequence(this, '#2196F3')">
                        <span class="item-emoji">🔵</span>
                        <span class="item-name">Azul</span>
                    </div>
                </div>
            </div>
            
            <!-- 4. Shade Discrimination -->
            <div id="shade-discrimination" class="activity">
                <div class="instruction">
                    <h3>Discriminação de Tons</h3>
                    <p>Encontre o tom EXATO de verde mostrado</p>
                </div>
                
                <div class="color-target">
                    <div class="color-sample" style="background: #4CAF50;"></div>
                    <div class="color-name">ESTE TOM DE VERDE</div>
                </div>
                
                <div class="items-grid">
                    <div class="color-item" onclick="selectShade(this, false)">
                        <span class="item-emoji">🟢</span>
                        <span class="item-name">Verde Claro</span>
                    </div>
                    <div class="color-item" onclick="selectShade(this, true)">
                        <span class="item-emoji">🟢</span>
                        <span class="item-name">Verde Médio</span>
                    </div>
                    <div class="color-item" onclick="selectShade(this, false)">
                        <span class="item-emoji">🟢</span>
                        <span class="item-name">Verde Escuro</span>
                    </div>
                    <div class="color-item" onclick="selectShade(this, false)">
                        <span class="item-emoji">🟢</span>
                        <span class="item-name">Verde Lima</span>
                    </div>
                </div>
            </div>
            
            <!-- 5. Sequence Colors -->
            <div id="sequence-colors" class="activity">
                <div class="instruction">
                    <h3>Sequência de Cores</h3>
                    <p>Complete o padrão: Vermelho → ? → Azul</p>
                </div>
                
                <div class="sequence-display">
                    <div class="sequence-color" style="background: #e91e63;"></div>
                    <div class="sequence-color" style="background: #ddd; border: 2px dashed #999;">
                        <span style="color: #666; font-size: 2rem;">?</span>
                    </div>
                    <div class="sequence-color" style="background: #2196F3;"></div>
                </div>
                
                <div class="items-grid">
                    <div class="color-item" onclick="selectPattern(this, false)">
                        <span class="item-emoji">🟡</span>
                        <span class="item-name">Amarelo</span>
                    </div>
                    <div class="color-item" onclick="selectPattern(this, true)">
                        <span class="item-emoji">🟣</span>
                        <span class="item-name">Roxo</span>
                    </div>
                    <div class="color-item" onclick="selectPattern(this, false)">
                        <span class="item-emoji">🟢</span>
                        <span class="item-name">Verde</span>
                    </div>
                </div>
            </div>
            
            <!-- 6. Gradient Matching -->
            <div id="gradient-matching" class="activity">
                <div class="instruction">
                    <h3>Correspondência de Gradiente</h3>
                    <p>Encontre o gradiente que corresponde ao padrão</p>
                </div>
                
                <div class="color-target">
                    <div style="width: 200px; height: 80px; background: linear-gradient(45deg, #ff6b6b, #4ecdc4); border-radius: 12px; border: 3px solid white; margin: 0 auto;"></div>
                    <div class="color-name">PADRÃO ALVO</div>
                </div>
                
                <div class="gradient-options">
                    <div class="gradient-option" onclick="selectGradient(this, false)" 
                         style="background: linear-gradient(45deg, #ff6b6b, #ffa500);"></div>
                    <div class="gradient-option" onclick="selectGradient(this, true)" 
                         style="background: linear-gradient(45deg, #ff6b6b, #4ecdc4);"></div>
                    <div class="gradient-option" onclick="selectGradient(this, false)" 
                         style="background: linear-gradient(45deg, #4ecdc4, #45b7d1);"></div>
                </div>
            </div>
            
        </div>
        
        <!-- Accessibility Controls -->
        <div class="accessibility-controls">
            <button class="accessibility-btn" onclick="explainGame()">📖 Explicar Jogo</button>
            <button class="accessibility-btn" onclick="repeatInstruction()">🔁 Repetir Instrução</button>
            <button class="accessibility-btn" onclick="announceProgress()">📊 Progresso</button>
            <button class="accessibility-btn" onclick="giveHint()">💡 Dica</button>
        </div>
    </div>

    <script>
        // Activity Management seguindo padrão do Number Counting V3
        const activities = {
            'basic-matching': 'Correspondência Básica de Cores',
            'speed-challenge': 'Desafio de Velocidade',
            'color-memory': 'Memória de Cores',
            'shade-discrimination': 'Discriminação de Tons',
            'sequence-colors': 'Sequência de Cores',
            'gradient-matching': 'Correspondência de Gradiente'
        };
        
        let currentActivity = 'basic-matching';
        let ttsActive = true;
        let sequenceStep = 0;
        let foundItems = 3;
        let totalItems = 5;
        
        function toggleTTS() {
            ttsActive = !ttsActive;
            const btn = document.querySelector('.header-tts-button');
            btn.textContent = ttsActive ? '🔊' : '🔇';
            btn.style.background = ttsActive ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)';
            btn.style.borderColor = ttsActive ? 'rgba(76, 175, 80, 0.5)' : 'rgba(244, 67, 54, 0.5)';
            
            if (ttsActive) {
                speak('TTS ativado. Sistema de áudio funcionando.');
            }
        }
        
        function showActivity(activityId) {
            // Hide all activities
            document.querySelectorAll('.activity').forEach(el => {
                el.classList.remove('active');
            });
            
            // Show selected activity
            document.getElementById(activityId).classList.add('active');
            
            // Update button states
            document.querySelectorAll('.activity-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            if (ttsActive) {
                speak(`Atividade alterada para: ${activities[activityId]}`);
            }
            
            currentActivity = activityId;
            resetActivity(activityId);
        }
        
        function resetActivity(activityId) {
            // Reset specific activity states
            if (activityId === 'color-memory') {
                sequenceStep = 0;
            }
            
            // Reset item states
            const items = document.querySelectorAll(`#${activityId} .color-item, #${activityId} .gradient-option`);
            items.forEach(item => {
                item.classList.remove('correct', 'incorrect', 'selected');
                item.style.pointerEvents = 'auto';
                item.style.border = '';
                item.style.boxShadow = '';
            });
            
            console.log(`🎮 Atividade ${activities[activityId]} resetada`);
        }
        
        // Basic Matching
        function selectItem(element, isCorrect) {
            if (isCorrect) {
                element.classList.add('correct');
                foundItems++;
                playFeedback('Correto! Ótima escolha de cor!', 'success');
            } else {
                element.classList.add('incorrect');
                playFeedback('Ops! Esta não é a cor certa. Tente novamente!', 'error');
            }
            
            element.style.pointerEvents = 'none';
            updateProgress();
        }
        
        // Speed Challenge
        function selectItem(element, isCorrect) {
            if (currentActivity === 'speed-challenge') {
                if (isCorrect) {
                    element.classList.add('correct');
                    playFeedback('Rápido e correto!', 'success');
                } else {
                    element.classList.add('incorrect');
                    playFeedback('Cor incorreta! Continue procurando!', 'error');
                }
                element.style.pointerEvents = 'none';
            }
        }
        
        // Color Memory Sequence
        const correctSequence = ['#e91e63', '#4CAF50', '#FFC107'];
        function selectSequence(element, color) {
            const expectedColor = correctSequence[sequenceStep];
            
            if (color === expectedColor) {
                element.classList.add('correct');
                sequenceStep++;
                
                if (sequenceStep >= correctSequence.length) {
                    playFeedback('Parabéns! Sequência completa!', 'success');
                    sequenceStep = 0;
                } else {
                    playFeedback(`Correto! Próxima cor na sequência.`, 'success');
                }
            } else {
                element.classList.add('incorrect');
                playFeedback('Ordem incorreta! Comece novamente.', 'error');
                setTimeout(() => {
                    document.querySelectorAll('#color-memory .color-item').forEach(item => {
                        item.classList.remove('correct', 'incorrect');
                    });
                    sequenceStep = 0;
                }, 1500);
            }
        }
        
        // Shade Discrimination
        function selectShade(element, isCorrect) {
            if (isCorrect) {
                element.classList.add('correct');
                playFeedback('Perfeito! Você identificou o tom exato!', 'success');
            } else {
                element.classList.add('incorrect');
                playFeedback('Tom incorreto. Observe bem as nuances!', 'error');
            }
            element.style.pointerEvents = 'none';
        }
        
        // Sequence Pattern
        function selectPattern(element, isCorrect) {
            if (isCorrect) {
                element.classList.add('correct');
                playFeedback('Excelente! Padrão completado corretamente!', 'success');
            } else {
                element.classList.add('incorrect');
                playFeedback('Não é a cor certa para o padrão!', 'error');
            }
            element.style.pointerEvents = 'none';
        }
        
        // Gradient Matching
        function selectGradient(element, isCorrect) {
            if (isCorrect) {
                element.style.border = '3px solid #4CAF50';
                element.style.boxShadow = '0 0 20px rgba(76, 175, 80, 0.5)';
                playFeedback('Perfeito! Gradiente correspondente encontrado!', 'success');
            } else {
                element.style.border = '3px solid #f44336';
                element.style.boxShadow = '0 0 20px rgba(244, 67, 54, 0.5)';
                playFeedback('Gradiente incorreto. Compare com cuidado!', 'error');
            }
            element.style.pointerEvents = 'none';
        }
        
        function updateProgress() {
            const progressFill = document.querySelector('.progress-fill');
            const progressText = document.querySelector('.progress-bar + div');
            
            if (progressFill && progressText) {
                const percentage = (foundItems / totalItems) * 100;
                progressFill.style.width = percentage + '%';
                progressText.textContent = `Progresso: ${foundItems}/${totalItems} itens encontrados`;
            }
        }
        
        // Accessibility Functions
        function explainGame() {
            const explanation = `Color Match é um jogo de correspondência de cores. 
                Seu objetivo é identificar e selecionar itens que tenham a cor indicada. 
                O jogo tem várias atividades: correspondência básica, desafio de velocidade, 
                memória de cores, discriminação de tons, sequências e gradientes.`;
            
            speak(explanation);
        }
        
        function repeatInstruction() {
            let instruction = '';
            
            switch(currentActivity) {
                case 'basic-matching':
                    instruction = 'Encontre todos os itens vermelhos clicando neles.';
                    break;
                case 'speed-challenge':
                    instruction = 'Encontre rapidamente todos os itens azuis antes do tempo acabar.';
                    break;
                case 'color-memory':
                    instruction = 'Memorize a sequência de cores e reproduza na ordem correta.';
                    break;
                case 'shade-discrimination':
                    instruction = 'Encontre o tom exato de verde mostrado no exemplo.';
                    break;
                case 'sequence-colors':
                    instruction = 'Complete o padrão de cores identificando a cor que falta.';
                    break;
                case 'gradient-matching':
                    instruction = 'Encontre o gradiente que corresponde exatamente ao padrão mostrado.';
                    break;
            }
            
            speak(instruction);
        }
        
        function announceProgress() {
            const progress = `Progresso atual: ${foundItems} de ${totalItems} itens encontrados. 
                Atividade: ${activities[currentActivity]}. 
                Pontuação: 180 pontos com 88% de precisão.`;
            speak(progress);
        }
        
        function giveHint() {
            let hint = '';
            
            switch(currentActivity) {
                case 'basic-matching':
                    hint = 'Procure por objetos que são naturalmente vermelhos, como maçã, morango e coração.';
                    break;
                case 'speed-challenge':
                    hint = 'Objetos azuis incluem: círculo azul, onda e outras coisas relacionadas à água.';
                    break;
                case 'color-memory':
                    hint = `A sequência correta é: primeiro vermelho, depois verde, por último amarelo.`;
                    break;
                case 'shade-discrimination':
                    hint = 'Compare cuidadosamente os tons. O correto é um verde médio, nem muito claro nem muito escuro.';
                    break;
                case 'sequence-colors':
                    hint = 'Entre vermelho e azul no espectro de cores, temos o roxo ou violeta.';
                    break;
                case 'gradient-matching':
                    hint = 'Observe a direção do gradiente e as cores exatas que se misturam.';
                    break;
            }
            
            speak(hint);
        }
        
        function playFeedback(message, type = 'neutral') {
            if (ttsActive) {
                speak(message, type);
            }
            
            // Visual feedback
            console.log(`${type.toUpperCase()}: ${message}`);
        }
        
        function speak(text, type = 'neutral') {
            if (ttsActive && 'speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'pt-BR';
                utterance.rate = type === 'success' ? 1.1 : type === 'error' ? 0.9 : 1.0;
                utterance.pitch = type === 'success' ? 1.2 : type === 'error' ? 0.8 : 1.0;
                speechSynthesis.speak(utterance);
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎨 Color Match V3 Preview carregado completamente!');
            updateProgress();
            
            // Speak welcome message
            if (ttsActive) {
                setTimeout(() => {
                    speak('Color Match V3 carregado. Use os botões para navegar entre as atividades e teste cada uma delas.');
                }, 1000);
            }
        });
    </script>
</body>
</html>
