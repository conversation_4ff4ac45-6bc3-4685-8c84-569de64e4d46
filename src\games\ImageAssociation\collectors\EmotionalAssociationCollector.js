// ============================================================================
// EMOTIONAL ASSOCIATION COLLECTOR - ATIVIDADE 3
// Coleta e análise de dados para reconhecimento emocional e inteligência afetiva
// ============================================================================

import { BaseCollector } from '../../../utils/BaseCollector.js';

export class EmotionalAssociationCollector extends BaseCollector {
  constructor() {
    super('EmotionalAssociation');
    
    this.cognitiveMetrics = {
      // Métricas específicas de processamento emocional
      emotionalRecognition: [],
      affectiveProcessing: [],
      empathicResponses: [],
      emotionalMemory: [],
      socialCognition: [],
      
      // Padrões emocionais
      emotionalValence: [],
      arousalLevels: [],
      emotionalComplexity: [],
      contextualEmotion: [],
      
      // Inteligência emocional
      emotionalAccuracy: [],
      emotionalFlexibility: [],
      emotionalRegulation: [],
      emotionalIntelligence: []
    };

    this.emotionalCategories = {
      happiness: {
        valence: 'positive',
        arousal: 'medium',
        complexity: 'basic',
        triggers: ['achievement', 'surprise', 'love', 'play'],
        expressions: ['🎁', '🎂', '🌈', '🏆', '👶', '🎪'],
        physiology: ['smile', 'laughter', 'energy'],
        duration: 'short_to_medium'
      },
      sadness: {
        valence: 'negative',
        arousal: 'low',
        complexity: 'basic',
        triggers: ['loss', 'separation', 'disappointment', 'failure'],
        expressions: ['🌧️', '💔', '🥀', '👋', '📰', '🏚️'],
        physiology: ['tears', 'low_energy', 'withdrawal'],
        duration: 'medium_to_long'
      },
      excitement: {
        valence: 'positive',
        arousal: 'high',
        complexity: 'medium',
        triggers: ['anticipation', 'adventure', 'novelty', 'stimulation'],
        expressions: ['🎢', '🎮', '🎭', '🏃', '🎵', '🎊'],
        physiology: ['increased_heart_rate', 'energy', 'alertness'],
        duration: 'short'
      },
      calmness: {
        valence: 'neutral_positive',
        arousal: 'low',
        complexity: 'medium',
        triggers: ['relaxation', 'meditation', 'nature', 'security'],
        expressions: ['🧘', '🌅', '🛀', '📚', '🍵', '🌊'],
        physiology: ['relaxation', 'slow_breathing', 'peace'],
        duration: 'medium_to_long'
      },
      anger: {
        valence: 'negative',
        arousal: 'high',
        complexity: 'medium',
        triggers: ['frustration', 'injustice', 'threat', 'obstruction'],
        expressions: ['🚫', '⏰', '🚗', '📢', '🔥', '⚡'],
        physiology: ['tension', 'increased_heart_rate', 'heat'],
        duration: 'short_to_medium'
      },
      fear: {
        valence: 'negative',
        arousal: 'high',
        complexity: 'basic',
        triggers: ['danger', 'unknown', 'threat', 'vulnerability'],
        expressions: ['🌙', '👻', '🕷️', '⛈️', '🏥', '📉'],
        physiology: ['fight_flight', 'sweating', 'trembling'],
        duration: 'short_to_medium'
      }
    };

    this.sessionData = {
      startTime: null,
      endTime: null,
      totalEmotionalAssociations: 0,
      correctEmotionalAssociations: 0,
      incorrectEmotionalAssociations: 0,
      averageEmotionalProcessingTime: 0,
      emotionsByAccuracy: {},
      emotionalConfusions: {},
      emotionalInsights: [],
      empathyScore: 0,
      difficultyLevel: 'beginner'
    };
  }

  // ========================================================================
  // COLETA DE DADOS DE ASSOCIAÇÃO EMOCIONAL
  // ========================================================================

  collectEmotionalAssociation(associationData) {
    const {
      selectedSituation,
      targetEmotion,
      actualEmotion,
      responseTime,
      timestamp,
      emotionalContext = {},
      confidenceLevel = null,
      emotionalJustification = null
    } = associationData;

    const isCorrect = targetEmotion === actualEmotion;
    
    const association = {
      id: this.generateInteractionId(),
      timestamp,
      responseTime,
      selectedSituation: {
        id: selectedSituation.id,
        image: selectedSituation.image,
        actualEmotion: actualEmotion,
        emotionalTriggers: this.identifyEmotionalTriggers(selectedSituation),
        contextualFactors: this.extractContextualFactors(selectedSituation)
      },
      targetEmotion,
      isCorrect,
      emotionalResponse: {
        valence: this.emotionalCategories[actualEmotion]?.valence,
        arousal: this.emotionalCategories[actualEmotion]?.arousal,
        complexity: this.emotionalCategories[actualEmotion]?.complexity
      },
      context: {
        emotionalContext,
        confidenceLevel,
        emotionalJustification,
        difficultyLevel: this.sessionData.difficultyLevel
      }
    };

    this.interactions.push(association);

    // Análises cognitivas especializadas
    this.analyzeEmotionalRecognition(association);
    this.analyzeAffectiveProcessing(association);
    this.analyzeEmpathicResponse(association);
    this.analyzeSocialCognition(association);
    
    // Atualizar métricas de sessão
    this.updateSessionMetrics(association);

    return association;
  }

  // ========================================================================
  // ANÁLISES COGNITIVAS ESPECIALIZADAS
  // ========================================================================

  analyzeEmotionalRecognition(association) {
    const { responseTime, isCorrect, targetEmotion, selectedSituation } = association;
    
    const recognitionMetric = {
      timestamp: association.timestamp,
      emotion: targetEmotion,
      situation: selectedSituation.image,
      recognized: isCorrect,
      responseTime,
      emotionalSalience: this.calculateEmotionalSalience(selectedSituation, targetEmotion),
      recognitionAccuracy: isCorrect ? 1 : 0,
      processingDifficulty: this.calculateProcessingDifficulty(targetEmotion, selectedSituation)
    };

    this.cognitiveMetrics.emotionalRecognition.push(recognitionMetric);

    // Análise de valência emocional
    const valenceMetric = {
      timestamp: association.timestamp,
      emotion: targetEmotion,
      valence: association.emotionalResponse.valence,
      arousal: association.emotionalResponse.arousal,
      valenceAccuracy: this.assessValenceAccuracy(association),
      arousalAccuracy: this.assessArousalAccuracy(association)
    };

    this.cognitiveMetrics.emotionalValence.push(valenceMetric);
  }

  analyzeAffectiveProcessing(association) {
    const { responseTime, targetEmotion, selectedSituation } = association;
    
    const affectiveMetric = {
      timestamp: association.timestamp,
      emotionalComplexity: this.emotionalCategories[targetEmotion]?.complexity,
      processingTime: responseTime,
      affectiveLoad: this.calculateAffectiveLoad(association),
      emotionalIntensity: this.estimateEmotionalIntensity(selectedSituation, targetEmotion),
      affectiveResonance: this.measureAffectiveResonance(association)
    };

    this.cognitiveMetrics.affectiveProcessing.push(affectiveMetric);
  }

  analyzeEmpathicResponse(association) {
    const { isCorrect, targetEmotion, responseTime, context } = association;
    
    const empathicMetric = {
      timestamp: association.timestamp,
      targetEmotion,
      empathicAccuracy: isCorrect ? 1 : 0,
      empathicLatency: responseTime,
      confidenceLevel: context.confidenceLevel,
      emotionalPerspectiveTaking: this.assessPerspectiveTaking(association),
      emotionalUnderstanding: this.assessEmotionalUnderstanding(association)
    };

    this.cognitiveMetrics.empathicResponses.push(empathicMetric);
  }

  analyzeSocialCognition(association) {
    const { targetEmotion, selectedSituation, isCorrect } = association;
    
    const socialMetric = {
      timestamp: association.timestamp,
      socialContext: this.extractSocialContext(selectedSituation),
      emotionalContagion: this.assessEmotionalContagion(association),
      socialEmpathy: this.measureSocialEmpathy(association),
      theoryOfMind: this.assessTheoryOfMind(association),
      socialEmotionalLearning: isCorrect ? 1 : 0
    };

    this.cognitiveMetrics.socialCognition.push(socialMetric);
  }

  // ========================================================================
  // CÁLCULOS ESPECIALIZADOS EM INTELIGÊNCIA EMOCIONAL
  // ========================================================================

  calculateEmotionalSalience(situation, emotion) {
    // Quão saliente é a emoção na situação apresentada
    const situationTriggers = this.identifyEmotionalTriggers(situation);
    const emotionTriggers = this.emotionalCategories[emotion]?.triggers || [];
    
    const overlap = situationTriggers.filter(trigger => emotionTriggers.includes(trigger)).length;
    return overlap / Math.max(situationTriggers.length, emotionTriggers.length, 1);
  }

  calculateProcessingDifficulty(emotion, situation) {
    const emotionComplexity = {
      'happiness': 0.3,
      'sadness': 0.4,
      'excitement': 0.6,
      'calmness': 0.7,
      'anger': 0.5,
      'fear': 0.4
    };

    const situationAmbiguity = this.calculateSituationAmbiguity(situation);
    const emotionComplexityScore = emotionComplexity[emotion] || 0.5;
    
    return (emotionComplexityScore + situationAmbiguity) / 2;
  }

  calculateSituationAmbiguity(situation) {
    // Situações ambíguas podem evocar múltiplas emoções
    const ambiguousContexts = {
      '🎁': 0.2, // Presente - geralmente alegria
      '🌧️': 0.4, // Chuva - pode ser tristeza ou calma
      '🎢': 0.3, // Montanha-russa - empolgação ou medo
      '🧘': 0.1, // Meditação - claramente calma
      '🚫': 0.6, // Proibição - raiva ou frustração
      '🌙': 0.5, // Noite - medo ou calma
      '👻': 0.2, // Fantasma - claramente medo
      '🎂': 0.2, // Bolo - claramente alegria
      '💔': 0.3, // Coração partido - tristeza
      '🔥': 0.4  // Fogo - raiva ou medo
    };

    return ambiguousContexts[situation.image] || 0.5;
  }

  assessValenceAccuracy(association) {
    const { targetEmotion, isCorrect } = association;
    
    if (isCorrect) return 1.0;
    
    // Se errou, verificar se pelo menos acertou a valência
    const targetValence = this.emotionalCategories[targetEmotion]?.valence;
    const selectedValence = this.emotionalCategories[association.selectedSituation.actualEmotion]?.valence;
    
    return targetValence === selectedValence ? 0.5 : 0;
  }

  assessArousalAccuracy(association) {
    const { targetEmotion, isCorrect } = association;
    
    if (isCorrect) return 1.0;
    
    // Se errou, verificar se pelo menos acertou o nível de arousal
    const targetArousal = this.emotionalCategories[targetEmotion]?.arousal;
    const selectedArousal = this.emotionalCategories[association.selectedSituation.actualEmotion]?.arousal;
    
    return targetArousal === selectedArousal ? 0.5 : 0;
  }

  calculateAffectiveLoad(association) {
    const { targetEmotion, responseTime } = association;
    const emotionComplexity = this.emotionalCategories[targetEmotion]?.complexity;
    
    let loadScore = 0;
    
    // Complexidade emocional
    if (emotionComplexity === 'basic') loadScore += 0.2;
    else if (emotionComplexity === 'medium') loadScore += 0.5;
    else if (emotionComplexity === 'high') loadScore += 0.8;
    
    // Tempo de processamento
    if (responseTime > 8000) loadScore += 0.3;
    else if (responseTime > 5000) loadScore += 0.2;
    
    // Arousal da emoção
    const arousal = this.emotionalCategories[targetEmotion]?.arousal;
    if (arousal === 'high') loadScore += 0.2;
    
    return Math.min(1.0, loadScore);
  }

  estimateEmotionalIntensity(situation, emotion) {
    // Intensidade da emoção evocada pela situação
    const intensityMap = {
      '🎁': { happiness: 0.8, excitement: 0.6 },
      '🌧️': { sadness: 0.6, calmness: 0.4 },
      '🎢': { excitement: 0.9, fear: 0.7 },
      '🧘': { calmness: 0.9 },
      '🚫': { anger: 0.8 },
      '🌙': { fear: 0.5, calmness: 0.7 },
      '👻': { fear: 0.9 },
      '🎂': { happiness: 0.8 },
      '💔': { sadness: 0.9 },
      '🔥': { anger: 0.7, fear: 0.6 }
    };

    return intensityMap[situation.image]?.[emotion] || 0.5;
  }

  measureAffectiveResonance(association) {
    // Quão bem a pessoa "ressoa" com a emoção
    const { isCorrect, responseTime, targetEmotion } = association;
    
    if (!isCorrect) return 0;
    
    // Ressonância baseada na velocidade de reconhecimento
    const optimalTime = 4000; // 4 segundos
    const timeFactor = Math.exp(-Math.abs(responseTime - optimalTime) / 2000);
    
    return timeFactor;
  }

  assessPerspectiveTaking(association) {
    // Capacidade de se colocar no lugar do outro
    const { isCorrect, context, targetEmotion } = association;
    
    let perspectiveScore = isCorrect ? 0.5 : 0;
    
    // Se forneceu justificativa, aumenta a pontuação
    if (context.emotionalJustification) {
      perspectiveScore += 0.3;
    }
    
    // Se demonstrou confiança adequada
    if (context.confidenceLevel) {
      if ((context.confidenceLevel > 0.7 && isCorrect) || 
          (context.confidenceLevel < 0.5 && !isCorrect)) {
        perspectiveScore += 0.2;
      }
    }
    
    return Math.min(1.0, perspectiveScore);
  }

  assessEmotionalUnderstanding(association) {
    const { isCorrect, targetEmotion, selectedSituation } = association;
    
    // Compreensão vai além do reconhecimento correto
    let understandingScore = isCorrect ? 0.6 : 0;
    
    // Compreender os gatilhos emocionais
    const triggers = this.identifyEmotionalTriggers(selectedSituation);
    const expectedTriggers = this.emotionalCategories[targetEmotion]?.triggers || [];
    
    const triggerOverlap = triggers.filter(t => expectedTriggers.includes(t)).length;
    const triggerScore = triggerOverlap / Math.max(expectedTriggers.length, 1);
    
    understandingScore += triggerScore * 0.4;
    
    return Math.min(1.0, understandingScore);
  }

  extractSocialContext(situation) {
    // Identificar se a situação tem componente social
    const socialSituations = {
      '🎁': 'gift_giving',
      '🎂': 'celebration',
      '👋': 'separation',
      '🤝': 'cooperation',
      '💔': 'relationship',
      '🎪': 'shared_experience'
    };

    return socialSituations[situation.image] || 'individual';
  }

  assessEmotionalContagion(association) {
    // Tendência de "pegar" emoções dos outros
    const { targetEmotion, isCorrect, responseTime } = association;
    
    // Emoções altamente contagiosas
    const contagiousEmotions = ['happiness', 'excitement', 'fear'];
    
    if (!contagiousEmotions.includes(targetEmotion)) return 0.5;
    
    // Reconhecimento rápido de emoções contagiosas indica alta suscetibilidade
    if (isCorrect && responseTime < 3000) return 0.8;
    if (isCorrect && responseTime < 5000) return 0.6;
    
    return 0.3;
  }

  measureSocialEmpathy(association) {
    const socialContext = this.extractSocialContext(association.selectedSituation);
    
    if (socialContext === 'individual') return 0.5;
    
    // Empatia social é maior em contextos interpessoais
    return association.isCorrect ? 0.8 : 0.2;
  }

  assessTheoryOfMind(association) {
    // Capacidade de compreender estados mentais dos outros
    const { isCorrect, targetEmotion, selectedSituation } = association;
    
    // Situações que requerem teoria da mente
    const theoryOfMindSituations = ['🎁', '👋', '💔', '🎂'];
    
    if (!theoryOfMindSituations.includes(selectedSituation.image)) return 0.5;
    
    return isCorrect ? 0.8 : 0.2;
  }

  identifyEmotionalTriggers(situation) {
    const triggerMap = {
      '🎁': ['surprise', 'love', 'anticipation'],
      '🌧️': ['melancholy', 'reflection', 'sadness'],
      '🎢': ['thrill', 'excitement', 'adrenaline'],
      '🧘': ['peace', 'mindfulness', 'relaxation'],
      '🚫': ['frustration', 'limitation', 'anger'],
      '🌙': ['mystery', 'fear', 'calm'],
      '👻': ['fear', 'surprise', 'supernatural'],
      '🎂': ['celebration', 'joy', 'achievement'],
      '💔': ['loss', 'heartbreak', 'sadness'],
      '🔥': ['danger', 'passion', 'destruction']
    };

    return triggerMap[situation.image] || [];
  }

  extractContextualFactors(situation) {
    const contextMap = {
      '🎁': ['social', 'positive', 'anticipatory'],
      '🌧️': ['environmental', 'natural', 'atmospheric'],
      '🎢': ['recreational', 'physical', 'intense'],
      '🧘': ['personal', 'spiritual', 'controlled'],
      '🚫': ['restrictive', 'authoritative', 'limiting'],
      '🌙': ['temporal', 'environmental', 'mysterious'],
      '👻': ['supernatural', 'threatening', 'unknown'],
      '🎂': ['celebratory', 'social', 'milestone'],
      '💔': ['relational', 'personal', 'loss'],
      '🔥': ['destructive', 'powerful', 'transformative']
    };

    return contextMap[situation.image] || [];
  }

  // ========================================================================
  // ATUALIZAÇÃO DE MÉTRICAS DE SESSÃO
  // ========================================================================

  updateSessionMetrics(association) {
    const { isCorrect, responseTime, targetEmotion } = association;

    this.sessionData.totalEmotionalAssociations++;
    
    if (isCorrect) {
      this.sessionData.correctEmotionalAssociations++;
    } else {
      this.sessionData.incorrectEmotionalAssociations++;
      
      // Rastrear confusões emocionais
      const actualEmotion = association.selectedSituation.actualEmotion;
      const confusionKey = `${targetEmotion}->${actualEmotion}`;
      
      if (!this.sessionData.emotionalConfusions[confusionKey]) {
        this.sessionData.emotionalConfusions[confusionKey] = 0;
      }
      this.sessionData.emotionalConfusions[confusionKey]++;
    }

    // Atualizar precisão por emoção
    if (!this.sessionData.emotionsByAccuracy[targetEmotion]) {
      this.sessionData.emotionsByAccuracy[targetEmotion] = { total: 0, correct: 0 };
    }
    
    this.sessionData.emotionsByAccuracy[targetEmotion].total++;
    if (isCorrect) {
      this.sessionData.emotionsByAccuracy[targetEmotion].correct++;
    }

    // Atualizar tempo médio
    const totalTime = this.interactions.reduce((sum, i) => sum + i.responseTime, 0);
    this.sessionData.averageEmotionalProcessingTime = totalTime / this.interactions.length;

    // Calcular score de empatia
    this.updateEmpathyScore();
  }

  updateEmpathyScore() {
    const empathicMetrics = this.cognitiveMetrics.empathicResponses;
    
    if (empathicMetrics.length === 0) {
      this.sessionData.empathyScore = 0;
      return;
    }

    const totalEmpathicAccuracy = empathicMetrics.reduce((sum, metric) => 
      sum + metric.empathicAccuracy, 0
    );
    
    const totalPerspectiveTaking = empathicMetrics.reduce((sum, metric) => 
      sum + metric.emotionalPerspectiveTaking, 0
    );

    const totalEmotionalUnderstanding = empathicMetrics.reduce((sum, metric) => 
      sum + metric.emotionalUnderstanding, 0
    );

    this.sessionData.empathyScore = (
      (totalEmpathicAccuracy / empathicMetrics.length) * 0.4 +
      (totalPerspectiveTaking / empathicMetrics.length) * 0.3 +
      (totalEmotionalUnderstanding / empathicMetrics.length) * 0.3
    );
  }

  // ========================================================================
  // RELATÓRIOS E ANÁLISES FINAIS
  // ========================================================================

  generateCognitiveReport() {
    return {
      emotionalIntelligence: this.analyzeEmotionalIntelligence(),
      empathicCapacity: this.analyzeEmpathicCapacity(),
      affectiveProcessing: this.analyzeAffectiveProcessing(),
      socialCognition: this.analyzeSocialCognition(),
      emotionalRegulation: this.analyzeEmotionalRegulation(),
      adaptiveRecommendations: this.generateAdaptiveRecommendations()
    };
  }

  analyzeEmotionalIntelligence() {
    const recognitionMetrics = this.cognitiveMetrics.emotionalRecognition;
    const empathicMetrics = this.cognitiveMetrics.empathicResponses;

    return {
      overallAccuracy: this.sessionData.correctEmotionalAssociations / this.sessionData.totalEmotionalAssociations,
      recognitionSpeed: this.calculateAverageMetric(recognitionMetrics, 'responseTime'),
      empathyScore: this.sessionData.empathyScore,
      emotionalRangeRecognized: Object.keys(this.sessionData.emotionsByAccuracy).length,
      emotionalComplexityHandling: this.assessComplexityHandling()
    };
  }

  analyzeEmpathicCapacity() {
    const empathicMetrics = this.cognitiveMetrics.empathicResponses;

    return {
      averageEmpathicAccuracy: this.calculateAverageMetric(empathicMetrics, 'empathicAccuracy'),
      perspectiveTaking: this.calculateAverageMetric(empathicMetrics, 'emotionalPerspectiveTaking'),
      emotionalUnderstanding: this.calculateAverageMetric(empathicMetrics, 'emotionalUnderstanding'),
      empathicLatency: this.calculateAverageMetric(empathicMetrics, 'empathicLatency')
    };
  }

  assessComplexityHandling() {
    const complexityPerformance = {};
    
    this.interactions.forEach(interaction => {
      const emotion = interaction.targetEmotion;
      const complexity = this.emotionalCategories[emotion]?.complexity;
      
      if (!complexityPerformance[complexity]) {
        complexityPerformance[complexity] = { total: 0, correct: 0 };
      }
      
      complexityPerformance[complexity].total++;
      if (interaction.isCorrect) {
        complexityPerformance[complexity].correct++;
      }
    });

    Object.keys(complexityPerformance).forEach(complexity => {
      const data = complexityPerformance[complexity];
      complexityPerformance[complexity].accuracy = data.correct / data.total;
    });

    return complexityPerformance;
  }

  generateAdaptiveRecommendations() {
    const recommendations = [];
    
    // Análise por emoção
    const emotionPerformance = {};
    Object.entries(this.sessionData.emotionsByAccuracy).forEach(([emotion, data]) => {
      emotionPerformance[emotion] = data.correct / data.total;
    });

    const weakEmotions = Object.entries(emotionPerformance)
      .filter(([emotion, accuracy]) => accuracy < 0.6)
      .map(([emotion]) => emotion);

    if (weakEmotions.length > 0) {
      recommendations.push({
        type: 'emotional_training',
        recommendation: `Reforçar reconhecimento: ${weakEmotions.join(', ')}`,
        confidence: 0.8,
        details: {
          emotions: weakEmotions,
          suggestedActivities: ['emotion_labeling', 'context_analysis', 'empathy_exercises']
        }
      });
    }

    // Análise de empatia
    if (this.sessionData.empathyScore < 0.5) {
      recommendations.push({
        type: 'empathy_development',
        recommendation: 'Exercícios de desenvolvimento empático',
        confidence: 0.7,
        details: {
          currentScore: this.sessionData.empathyScore,
          suggestedActivities: ['perspective_taking', 'emotional_storytelling', 'social_scenarios']
        }
      });
    }

    // Análise de confusões emocionais
    const majorConfusions = Object.entries(this.sessionData.emotionalConfusions)
      .filter(([confusion, count]) => count >= 2)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 2);

    majorConfusions.forEach(([confusion, count]) => {
      recommendations.push({
        type: 'emotional_discrimination',
        recommendation: `Diferenciar emoções: ${confusion}`,
        confidence: 0.6,
        details: {
          confusionPattern: confusion,
          frequency: count
        }
      });
    });

    return recommendations;
  }

  getActivityScore() {
    if (this.sessionData.totalEmotionalAssociations === 0) return 0;
    
    const accuracy = this.sessionData.correctEmotionalAssociations / this.sessionData.totalEmotionalAssociations;
    const speedFactor = Math.max(0, 1 - (this.sessionData.averageEmotionalProcessingTime - 5000) / 10000);
    const empathyFactor = this.sessionData.empathyScore;
    const complexityFactor = this.calculateComplexityFactor();
    
    return Math.round(accuracy * speedFactor * empathyFactor * complexityFactor * 1000);
  }

  calculateComplexityFactor() {
    const complexityHandling = this.assessComplexityHandling();
    
    let totalScore = 0;
    let totalWeight = 0;
    
    Object.entries(complexityHandling).forEach(([complexity, data]) => {
      const weight = complexity === 'basic' ? 1 : complexity === 'medium' ? 1.5 : 2;
      totalScore += data.accuracy * weight;
      totalWeight += weight;
    });

    return totalWeight > 0 ? totalScore / totalWeight : 0.5;
  }
}

export default EmotionalAssociationCollector;
