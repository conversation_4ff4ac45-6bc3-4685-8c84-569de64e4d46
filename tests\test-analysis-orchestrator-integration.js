/**
 * @file test-analysis-orchestrator-integration.js
 * @description Teste de integração do AnalysisOrchestrator com cache inteligente
 * @version 1.0.0
 */

import { getAnalysisOrchestrator } from './src/api/services/analysis/AnalysisOrchestrator.js';
import { getBehavioralAnalyzer } from './src/api/services/analysis/BehavioralAnalyzer.js';

console.log('🎭 TESTE DE INTEGRAÇÃO - ANALYSIS ORCHESTRATOR');
console.log('=' .repeat(60));

async function testAnalysisOrchestrator() {
  try {
    // 1. Inicializar AnalysisOrchestrator
    console.log('\n🚀 1. Inicializando AnalysisOrchestrator...');
    const orchestrator = getAnalysisOrchestrator({
      enableParallelAnalysis: true,
      analysisTimeout: 10000
    });

    // Aguardar inicialização dos analisadores
    await new Promise(resolve => setTimeout(resolve, 1000));

    // 2. Preparar dados de sessão de teste
    console.log('\n📊 2. Preparando dados de sessão de teste...');
    const gameSession = {
      id: 'test_session_001',
      childId: 'child_001',
      gameName: 'MemoryGame',
      duration: 120000, // 2 minutos
      accuracy: 0.85,
      interactions: [
        { type: 'click', timestamp: Date.now() - 10000, correct: true },
        { type: 'click', timestamp: Date.now() - 8000, correct: false },
        { type: 'click', timestamp: Date.now() - 5000, correct: true }
      ],
      sessionStartTime: Date.now() - 120000,
      sessionEndTime: Date.now(),
      metadata: {
        difficulty: 'medium',
        adaptations: ['visual', 'auditory']
      }
    };

    console.log('Sessão preparada:', {
      id: gameSession.id,
      childId: gameSession.childId,
      gameName: gameSession.gameName,
      interactions: gameSession.interactions.length
    });

    // 3. Executar análise completa
    console.log('\n🔍 3. Executando análise completa...');
    const startTime = Date.now();
    
    const analysisResult = await orchestrator.orchestrateCompleteAnalysis(gameSession, {
      forceRefresh: false // Permitir cache
    });

    const analysisTime = Date.now() - startTime;

    console.log('\n✅ Análise completa concluída!');
    console.log(`⏱️ Tempo de análise: ${analysisTime}ms`);
    console.log('📊 Resultados:', {
      orchestrationId: analysisResult.orchestrationId,
      overallScore: analysisResult.consolidated?.overallScore,
      analyzersUsed: analysisResult.metadata?.analyzersUsed,
      cacheUsed: analysisResult.metadata?.cacheUsed
    });

    // 4. Executar novamente para testar cache
    console.log('\n💾 4. Testando cache - executando análise novamente...');
    const cacheStartTime = Date.now();
    
    const cachedResult = await orchestrator.orchestrateCompleteAnalysis(gameSession, {
      forceRefresh: false
    });

    const cacheTime = Date.now() - cacheStartTime;

    console.log('\n🚀 Cache testado!');
    console.log(`⚡ Tempo com cache: ${cacheTime}ms (${Math.round(((analysisTime - cacheTime) / analysisTime) * 100)}% mais rápido)`);
    console.log('📈 Cache funcionando:', {
      cacheUsed: cacheTime < analysisTime / 2,
      orchestrationId: cachedResult.orchestrationId
    });

    // 5. Obter métricas consolidadas
    console.log('\n📈 5. Métricas consolidadas...');
    const metrics = orchestrator.getOrchestratorMetrics();
    
    console.log('Métricas do Orquestrador:', {
      totalOrchestrations: metrics.totalOrchestrations,
      successfulAnalyses: metrics.successfulAnalyses,
      averageTime: `${Math.round(metrics.averageOrchestrationTime)}ms`,
      cacheMetrics: {
        hits: metrics.cache.hits,
        misses: metrics.cache.misses,
        hitRate: `${Math.round(metrics.cache.hitRate * 100)}%`
      }
    });

    // 6. Testar BehavioralAnalyzer individualmente
    console.log('\n🧠 6. Testando BehavioralAnalyzer individual...');
    const behavioralAnalyzer = getBehavioralAnalyzer();
    
    const behavioralResult = await behavioralAnalyzer.analyzeGameBehavior(gameSession);
    
    console.log('Resultado Comportamental:', {
      success: behavioralResult.success,
      confidence: behavioralResult.confidence,
      patterns: behavioralResult.patterns?.length || 0,
      recommendations: behavioralResult.recommendations?.length || 0
    });

    // 7. Métricas do cache do behavioral analyzer
    console.log('\n💾 7. Métricas do cache behavioral...');
    const behavioralMetrics = behavioralAnalyzer.getCacheMetrics();
    
    console.log('Cache Behavioral:', {
      hits: behavioralMetrics.hits,
      misses: behavioralMetrics.misses,
      hitRate: `${Math.round(behavioralMetrics.hitRate * 100)}%`,
      size: behavioralMetrics.size
    });

    return {
      success: true,
      analysisTime,
      cacheTime,
      speedImprovement: `${Math.round(((analysisTime - cacheTime) / analysisTime) * 100)}%`,
      metrics
    };

  } catch (error) {
    console.error('❌ Erro no teste:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Executar teste
testAnalysisOrchestrator()
  .then(result => {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 RESULTADO FINAL DO TESTE');
    console.log('='.repeat(60));
    
    if (result.success) {
      console.log('✅ SUCESSO - Integração funcionando perfeitamente!');
      console.log(`⚡ Melhoria de performance com cache: ${result.speedImprovement}`);
      console.log('🎭 AnalysisOrchestrator integrado com sucesso');
      console.log('💾 Cache inteligente otimizando análises');
      console.log('🧠 Analisadores especializados funcionando');
    } else {
      console.log('❌ FALHA - Problemas na integração');
      console.log('Erro:', result.error);
    }
  })
  .catch(error => {
    console.error('💥 ERRO CRÍTICO:', error);
  });
