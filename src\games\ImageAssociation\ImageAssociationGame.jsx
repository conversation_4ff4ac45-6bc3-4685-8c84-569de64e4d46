/**
 * 🖼️ IMAGE ASSOCIATION V3 - JOGO DE ASSOCIAÇÃO COM MÚLTIPLAS ATIVIDADES
 * Portal Betina V3 - Jogo educativo com 6 atividades diversificadas
 */

import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';
import { ImageAssociationConfig } from './ImageAssociationConfig';
import { ImageAssociationMetrics } from './ImageAssociationMetrics';
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';
import { v4 as uuidv4 } from 'uuid';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// Hook unificado para integração com backend
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
// Hook específico para análise terapêutica
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🖼️ Importar coletores avançados de associação
import { ImageAssociationCollectorsHub } from './collectors/index.js';
// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';

// Importa estilos modulares
import styles from './ImageAssociation.module.css';

// 🎯 SISTEMA DE ATIVIDADES REDESENHADO V3 - IMAGE ASSOCIATION
// Cada atividade testa diferentes funções cognitivas com layouts únicos
const ACTIVITY_TYPES = {
  BASIC_ASSOCIATION: {
    id: 'basic_association',
    name: 'Associação Básica',
    icon: '🔗',
    description: 'Teste de associação conceitual simples',
    cognitiveFunction: 'conceptual_association',
    component: 'BasicAssociationActivity'
  },
  CATEGORY_SORTING: {
    id: 'category_sorting',
    name: 'Classificação por Categoria',
    icon: '📂',
    description: 'Teste de categorização e organização mental',
    cognitiveFunction: 'categorical_thinking_organization',
    component: 'CategorySortingActivity'
  },
  VISUAL_SEQUENCE: {
    id: 'visual_sequence',
    name: 'Sequência Visual',
    icon: '📝',
    description: 'Teste de raciocínio sequencial e lógica visual',
    cognitiveFunction: 'sequential_reasoning_visual_logic',
    component: 'VisualSequenceActivity'
  },
  EMOTION_MATCHING: {
    id: 'emotion_matching',
    name: 'Correspondência Emocional',
    icon: '😊',
    description: 'Teste de reconhecimento e correspondência emocional',
    cognitiveFunction: 'emotional_recognition_matching',
    component: 'EmotionMatchingActivity'
  },
  CONTEXT_REASONING: {
    id: 'context_reasoning',
    name: 'Raciocínio Contextual',
    icon: '🏠',
    description: 'Teste de compreensão contextual e inferência',
    cognitiveFunction: 'contextual_reasoning_inference',
    component: 'ContextReasoningActivity'
  }
};

const ImageAssociationGame = ({ onBack }) => {
  // 🏠 Contexto do sistema
  const { user } = useContext(SystemContext);

  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();

  // Estados TTS
  const [ttsActive, setTtsActive] = useState(true);
  const [ttsEnabled, setTtsEnabled] = useState(true);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [currentSpeechButton, setCurrentSpeechButton] = useState(null);

  // Estados do jogo (compatibilidade com versão antiga)
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [gameStarted, setGameStarted] = useState(false);
  const [currentAssociation, setCurrentAssociation] = useState(null);
  const [selectedOption, setSelectedOption] = useState(null);
  const [feedback, setFeedback] = useState(null);
  const [score, setScore] = useState(0);
  const [level, setLevel] = useState(1);
  const [stars, setStars] = useState(0);
  const [successes, setSuccesses] = useState(0);
  const [attempts, setAttempts] = useState(0);
  const [currentPhase, setCurrentPhase] = useState(1);
  const [gameStartTime, setGameStartTime] = useState(null);
  const [difficulty, setDifficulty] = useState('EASY');

  // Referência para métricas
  const metricsRef = useRef(null);

  // 🎯 ESTADO EXPANDIDO COM SISTEMA DE ATIVIDADES V3
  const [gameState, setGameState] = useState({
    status: 'start', // 'start', 'playing', 'paused', 'finished'
    score: 0,
    round: 1,
    totalRounds: 10,
    difficulty: 'EASY',
    accuracy: 100,
    roundStartTime: null,

    // 🎯 Sistema de atividades redesenhado (5 atividades distintas)
    currentActivity: ACTIVITY_TYPES.BASIC_ASSOCIATION.id,
    activityCycle: [
      ACTIVITY_TYPES.BASIC_ASSOCIATION.id,
      ACTIVITY_TYPES.CATEGORY_SORTING.id,
      ACTIVITY_TYPES.VISUAL_SEQUENCE.id,
      ACTIVITY_TYPES.EMOTION_MATCHING.id,
      ACTIVITY_TYPES.CONTEXT_REASONING.id
    ],
    activityIndex: 0,
    roundsPerActivity: 4,
    activityRoundCount: 0,
    activitiesCompleted: [],

    // 🎯 Dados específicos de atividades
    activityData: {
      basicAssociation: {
        currentPair: null,
        options: [],
        selectedOption: null
      },
      categoryMatching: {
        categories: [],
        currentCategory: null,
        placedItems: []
      },
      sequenceAssociation: {
        sequence: [],
        userProgress: [],
        missingIndex: null
      },
      emotionRecognition: {
        currentEmotion: null,
        faceOptions: [],
        selectedFace: null
      },
      contextAssociation: {
        context: null,
        objects: [],
        correctObjects: []
      },
      memoryAssociation: {
        pairs: [],
        flippedCards: [],
        matchedPairs: []
      }
    },

    // 🎯 Feedback e animações
    showFeedback: false,
    feedbackType: null, // 'success', 'error', 'hint'
    feedbackMessage: '',
    showCelebration: false,

    // 🎯 Métricas comportamentais
    responseTime: 0,
    hesitationCount: 0,
    helpUsed: false,
    consecutiveCorrect: 0,
    totalAttempts: 0,
    correctAttempts: 0
  });

  // 🖼️ Inicializar coletores avançados de associação primeiro
  // 🖼️ Buscar instância dos coletores do sistema (evita múltiplas instanciações)
  const [collectorsHub] = useState(() => {
    // Tentar obter instância do sistema global primeiro
    if (window.globalSystemInstance?.gameSpecificProcessors?.gameCollectors?.ImageAssociation?.hub) {
      console.log('🖼️ Reutilizando instância existente do ImageAssociation CollectorsHub');
      return window.globalSystemInstance.gameSpecificProcessors.gameCollectors.ImageAssociation.hub;
    }
    // Fallback: criar nova instância apenas se necessário
    console.log('🖼️ Criando nova instância do ImageAssociation CollectorsHub');
    return new ImageAssociationCollectorsHub();
  });

  // Integração com o sistema unificado Portal Betina V3 - DEVE VIR ANTES DO useMultisensoryIntegration
  const {
    startUnifiedSession,
    recordInteraction,
    endUnifiedSession,
    updateMetrics,
    portalReady,
    sessionId,
    isSessionActive,
    gameState: unifiedGameState,
    sessionMetrics
  } = useUnifiedGameLogic('image_association');

  //  Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'image_association',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    learningStyle: user?.profile?.learningStyle || 'visual'
  });

  // 🎯 Hook orquestrador terapêutico
  const {
    processGameMetrics: recordTherapeuticActivity,
    getRecommendations: getTherapeuticRecommendations,
    setUserContext: setTherapeuticContext
  } = useTherapeuticOrchestrator({ userId: user?.id });

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (metricsRef.current && recordInteraction && updateMetrics) {
      // ImageAssociationMetrics é um objeto, não uma classe com connectToBackend
      // Vamos apenas verificar se está disponível
      console.log('🖼️ ImageAssociation: Métricas inicializadas e backend conectado');
    }
  }, [recordInteraction, updateMetrics]);

  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }

    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);
  // Função para falar texto usando TTS (compatibilidade)
  const speakOld = useCallback((text, options = {}) => {
    // Verificar se TTS está ativo no jogo
    if (!ttsActive) {
      console.log('🔇 TTS desativado - não reproduzindo áudio');
      return;
    }
    
    if (!ttsEnabled || !('speechSynthesis' in window)) {
      console.warn('Text-to-Speech não está habilitado ou não é suportado neste navegador');
      return;
    }
    
    // Cancelar qualquer fala em andamento
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    
    // Configurações padrão com opções personalizáveis
    utterance.lang = options.lang || 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    // Marcar que está falando
    setIsSpeaking(true);
    if (options.buttonId) {
      setCurrentSpeechButton(options.buttonId);
    }
    
    // Callback quando iniciar
    utterance.onstart = () => {
      setIsSpeaking(true);
    };
    
    // Callback para quando terminar de falar
    utterance.onend = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
      if (options.onEnd) {
        options.onEnd();
      }
    };
    
    // Callback para erros
    utterance.onerror = () => {
      setIsSpeaking(false);
      setCurrentSpeechButton(null);
    };
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive, ttsEnabled]);
  
  // Função para explicar o jogo
  const explainGame = useCallback(() => {
    const explanation = `
      Bem-vindo ao jogo Associação de Imagens! 
      Este é um jogo onde você precisa conectar imagens que fazem sentido juntas.
      
      Como jogar:
      Observe atentamente a imagem da esquerda.
      Depois, escolha qual das opções à direita combina melhor com ela.
      Use sua lógica e conhecimento para fazer as associações corretas.
      
      Quanto mais associações corretas você fizer, mais pontos ganhará!
      
      Boa sorte e divirta-se desenvolvendo sua capacidade de associação!
    `;
    
    speak(explanation, {
      rate: 0.8,
      buttonId: 'explain',
      onEnd: () => console.log('Explicação do jogo concluída')
    });
  }, [speak]);

  // =====================================================
  // 🎯 FUNÇÕES DE GERAÇÃO REDESENHADAS - ATIVIDADES DISTINTAS
  // =====================================================

  // 🔗 ASSOCIAÇÃO BÁSICA - Associação conceitual simples
  const generateBasicAssociation = useCallback(() => {
    console.log('🔗 Generating basic association activity');

    const associations = [
      {
        main: { emoji: '🐶', label: 'Cachorro' },
        options: [
          { emoji: '🦴', label: 'Osso' },
          { emoji: '🐱', label: 'Gato' },
          { emoji: '🚗', label: 'Carro' }
        ],
        correct: { emoji: '🦴', label: 'Osso' },
        explanation: 'Cachorro come osso'
      },
      {
        main: { emoji: '🌧️', label: 'Chuva' },
        options: [
          { emoji: '☀️', label: 'Sol' },
          { emoji: '☂️', label: 'Guarda-chuva' },
          { emoji: '🏠', label: 'Casa' }
        ],
        correct: { emoji: '☂️', label: 'Guarda-chuva' },
        explanation: 'Chuva precisa de guarda-chuva'
      },
      {
        main: { emoji: '🔑', label: 'Chave' },
        options: [
          { emoji: '🚪', label: 'Porta' },
          { emoji: '🍎', label: 'Maçã' },
          { emoji: '📱', label: 'Celular' }
        ],
        correct: { emoji: '🚪', label: 'Porta' },
        explanation: 'Chave abre porta'
      }
    ];

    const randomAssociation = associations[Math.floor(Math.random() * associations.length)];
    setCurrentAssociation(randomAssociation);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        basicAssociation: {
          currentPair: randomAssociation,
          selectedOption: null,
          showExplanation: false
        }
      }
    }));

    speak(`Atividade: Associação Básica. O que se relaciona com ${randomAssociation.main.label}?`);
  }, [speak]);

  // 📂 CLASSIFICAÇÃO POR CATEGORIA - Categorização e organização mental
  const generateCategorySorting = useCallback(() => {
    console.log('📂 Generating category sorting activity');

    const categories = {
      'easy': [
        {
          name: 'Animais',
          emoji: '🐾',
          items: [
            { emoji: '🐶', label: 'Cachorro', category: 'animals' },
            { emoji: '🐱', label: 'Gato', category: 'animals' },
            { emoji: '🐸', label: 'Sapo', category: 'animals' }
          ],
          distractors: [
            { emoji: '🚗', label: 'Carro', category: 'vehicles' },
            { emoji: '🍎', label: 'Maçã', category: 'food' }
          ]
        },
        {
          name: 'Frutas',
          emoji: '🍎',
          items: [
            { emoji: '🍎', label: 'Maçã', category: 'fruits' },
            { emoji: '🍌', label: 'Banana', category: 'fruits' },
            { emoji: '🍊', label: 'Laranja', category: 'fruits' }
          ],
          distractors: [
            { emoji: '🚗', label: 'Carro', category: 'vehicles' },
            { emoji: '🐶', label: 'Cachorro', category: 'animals' }
          ]
        }
      ]
    };

    const selectedCategory = categories['easy'][Math.floor(Math.random() * categories['easy'].length)];
    const allItems = [...selectedCategory.items, ...selectedCategory.distractors];
    const shuffledItems = allItems.sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        categorySorting: {
          targetCategory: selectedCategory,
          allItems: shuffledItems,
          sortedItems: [],
          correctItems: selectedCategory.items
        }
      }
    }));

    speak(`Atividade: Classificação por Categoria. Encontre todos os itens da categoria ${selectedCategory.name}.`);
  }, [speak]);

  // 📝 SEQUÊNCIA VISUAL - Raciocínio sequencial e lógica visual
  const generateVisualSequence = useCallback(() => {
    console.log('📝 Generating visual sequence activity');

    const sequencePatterns = {
      'size_progression': {
        name: 'Progressão de Tamanho',
        sequence: ['🔴', '🟠', '🟡'],
        options: ['🟢', '🔵', '🟣'],
        correct: '🟢',
        explanation: 'Sequência de cores do arco-íris'
      },
      'shape_alternation': {
        name: 'Alternância de Formas',
        sequence: ['⭐', '🔺', '⭐'],
        options: ['🔺', '⭐', '🔴'],
        correct: '🔺',
        explanation: 'Padrão alternado: estrela, triângulo, estrela...'
      },
      'number_sequence': {
        name: 'Sequência Numérica',
        sequence: ['1️⃣', '2️⃣', '3️⃣'],
        options: ['4️⃣', '1️⃣', '5️⃣'],
        correct: '4️⃣',
        explanation: 'Sequência numérica crescente'
      }
    };

    const patternKeys = Object.keys(sequencePatterns);
    const selectedPattern = sequencePatterns[patternKeys[Math.floor(Math.random() * patternKeys.length)]];

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        visualSequence: {
          pattern: selectedPattern,
          userAnswer: null,
          showPattern: true
        }
      }
    }));

    speak(`Atividade: Sequência Visual. Analise o padrão e escolha o próximo elemento da sequência.`);
  }, [speak]);

  // 😊 CORRESPONDÊNCIA EMOCIONAL - Reconhecimento e correspondência emocional
  const generateEmotionMatching = useCallback(() => {
    console.log('😊 Generating emotion matching activity');

    const emotionPairs = [
      {
        situation: { emoji: '🎂', label: 'Festa de Aniversário' },
        emotions: [
          { emoji: '😊', label: 'Feliz', correct: true },
          { emoji: '😢', label: 'Triste', correct: false },
          { emoji: '😡', label: 'Bravo', correct: false }
        ],
        explanation: 'Festas de aniversário deixam as pessoas felizes'
      },
      {
        situation: { emoji: '🌧️', label: 'Dia Chuvoso' },
        emotions: [
          { emoji: '😊', label: 'Feliz', correct: false },
          { emoji: '😔', label: 'Melancólico', correct: true },
          { emoji: '😡', label: 'Bravo', correct: false }
        ],
        explanation: 'Dias chuvosos podem deixar as pessoas melancólicas'
      },
      {
        situation: { emoji: '🎁', label: 'Receber Presente' },
        emotions: [
          { emoji: '😊', label: 'Feliz', correct: true },
          { emoji: '😢', label: 'Triste', correct: false },
          { emoji: '😴', label: 'Sonolento', correct: false }
        ],
        explanation: 'Receber presentes deixa as pessoas felizes'
      }
    ];

    const selectedPair = emotionPairs[Math.floor(Math.random() * emotionPairs.length)];

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        emotionMatching: {
          situation: selectedPair.situation,
          emotions: selectedPair.emotions,
          userAnswer: null,
          explanation: selectedPair.explanation
        }
      }
    }));

    speak(`Atividade: Correspondência Emocional. Como você se sentiria em: ${selectedPair.situation.label}?`);
  }, [speak]);

  // 🏠 RACIOCÍNIO CONTEXTUAL - Compreensão contextual e inferência
  const generateContextReasoning = useCallback(() => {
    console.log('🏠 Generating context reasoning activity');

    const contextScenarios = [
      {
        context: { emoji: '🏥', label: 'Hospital' },
        items: [
          { emoji: '👩‍⚕️', label: 'Médica', belongs: true },
          { emoji: '🩺', label: 'Estetoscópio', belongs: true },
          { emoji: '🍕', label: 'Pizza', belongs: false },
          { emoji: '🏖️', label: 'Praia', belongs: false }
        ],
        question: 'O que você encontra em um hospital?'
      },
      {
        context: { emoji: '🏫', label: 'Escola' },
        items: [
          { emoji: '👩‍🏫', label: 'Professora', belongs: true },
          { emoji: '📚', label: 'Livros', belongs: true },
          { emoji: '🚗', label: 'Carro', belongs: false },
          { emoji: '🐠', label: 'Peixe', belongs: false }
        ],
        question: 'O que você encontra em uma escola?'
      },
      {
        context: { emoji: '🍽️', label: 'Restaurante' },
        items: [
          { emoji: '👨‍🍳', label: 'Chef', belongs: true },
          { emoji: '🍽️', label: 'Pratos', belongs: true },
          { emoji: '🛏️', label: 'Cama', belongs: false },
          { emoji: '🌙', label: 'Lua', belongs: false }
        ],
        question: 'O que você encontra em um restaurante?'
      }
    ];

    const selectedScenario = contextScenarios[Math.floor(Math.random() * contextScenarios.length)];
    const shuffledItems = selectedScenario.items.sort(() => Math.random() - 0.5);

    setGameState(prev => ({
      ...prev,
      activityData: {
        ...prev.activityData,
        contextReasoning: {
          context: selectedScenario.context,
          items: shuffledItems,
          question: selectedScenario.question,
          selectedItems: [],
          correctItems: selectedScenario.items.filter(item => item.belongs)
        }
      }
    }));

    speak(`Atividade: Raciocínio Contextual. ${selectedScenario.question}`);
  }, [speak]);

  // 🎯 FUNÇÕES DE CONTROLE DO JOGO PADRONIZADAS

  // Inicializar métricas
  useEffect(() => {
    if (!metricsRef.current) {
      metricsRef.current = ImageAssociationMetrics;
    }
  }, []);

  // Inicializar primeira atividade quando o jogo começar
  useEffect(() => {
    if (gameState.status === 'playing' && gameState.currentActivity) {
      // Gerar conteúdo para a atividade atual
      switch (gameState.currentActivity) {
        case ACTIVITY_TYPES.BASIC_ASSOCIATION.id:
          generateBasicAssociation();
          break;
        case ACTIVITY_TYPES.CATEGORY_SORTING.id:
          generateCategorySorting();
          break;
        case ACTIVITY_TYPES.VISUAL_SEQUENCE.id:
          generateVisualSequence();
          break;
        case ACTIVITY_TYPES.EMOTION_MATCHING.id:
          generateEmotionMatching();
          break;
        case ACTIVITY_TYPES.CONTEXT_REASONING.id:
          generateContextReasoning();
          break;
        default:
          generateBasicAssociation();
      }
    }
  }, [gameState.status, gameState.currentActivity, generateBasicAssociation, generateCategorySorting, generateVisualSequence, generateEmotionMatching, generateContextReasoning]);

  // Função para gerar nova rodada
  const generateNewRound = useCallback(() => {
    const currentActivity = gameState.currentActivity;

    setGameState(prev => ({
      ...prev,
      roundStartTime: Date.now(),
      showFeedback: false,
      feedbackType: null,
      feedbackMessage: ''
    }));

    // Gerar dados específicos da atividade redesenhada
    switch (currentActivity) {
      case ACTIVITY_TYPES.BASIC_ASSOCIATION.id:
        generateBasicAssociation();
        break;
      case ACTIVITY_TYPES.CATEGORY_SORTING.id:
        generateCategorySorting();
        break;
      case ACTIVITY_TYPES.VISUAL_SEQUENCE.id:
        generateVisualSequence();
        break;
      case ACTIVITY_TYPES.EMOTION_MATCHING.id:
        generateEmotionMatching();
        break;
      case ACTIVITY_TYPES.CONTEXT_REASONING.id:
        generateContextReasoning();
        break;
      default:
        generateBasicAssociation();
    }
  }, [gameState.currentActivity, generateBasicAssociation, generateCategorySorting, generateVisualSequence, generateEmotionMatching, generateContextReasoning]);

  // Função para iniciar o jogo
  const startGame = useCallback(async (selectedDifficulty) => {
    try {
      // Esconder tela inicial primeiro
      setShowStartScreen(false);
      setGameStarted(true);

      setGameState(prev => ({
        ...prev,
        status: 'playing',
        difficulty: selectedDifficulty,
        roundStartTime: Date.now()
      }));

      // Inicializar sessão unificada
      if (startUnifiedSession) {
        await startUnifiedSession({
          gameType: 'image_association',
          difficulty: selectedDifficulty,
          userId: user?.id || 'anonymous'
        });
      }

      // Inicializar sessões dos hooks
      await initMultisensory();

      // Configurar contexto terapêutico se usuário válido
      if (user?.id && user.id !== 'anonymous' && user.id !== '') {
        setTherapeuticContext(user.id);
      }

      // Gerar primeira atividade
      generateNewRound();

      speak('Jogo iniciado! Vamos começar com associação de imagens.');
    } catch (error) {
      console.error('Erro ao iniciar jogo:', error);
    }
  }, [startUnifiedSession, initMultisensory, setTherapeuticContext, user, speak, generateNewRound]);

  // 🎯 FUNÇÕES DE GERAÇÃO DE ATIVIDADES (duplicatas removidas - já definidas acima)

  //  Hook multissensorial integrado já está declarado acima
  //  Hook orquestrador terapêutico integrado já está declarado acima

  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false)
  const [analysisResults, setAnalysisResults] = useState(null)
  const [attemptCount, setAttemptCount] = useState(0)

  // Calcular precisão
  const getAccuracy = useCallback(() => {
    return attempts > 0 ? Math.round((successes / attempts) * 100) : 0
  }, [attempts, successes])

  // 🖼️ Inicializar sessão com coletores
  const initializeSession = useCallback(async () => {
    try {
      if (collectorsHub && !collectorsHub.initialized) {
        await collectorsHub.initialize()
        console.log('🎯 ImageAssociation: Coletores inicializados')
      }
    } catch (error) {
      console.warn('⚠️ ImageAssociation: Erro ao inicializar coletores:', error)
    }
  }, [collectorsHub])

  // 🖼️ Registrar interação de associação
  const recordAssociationInteraction = useCallback(async (selectedOption, isCorrect, reactionTime) => {
    try {
      if (!collectorsHub || !currentAssociation) return

      const gameData = {
        sessionId: `imageassoc_${Date.now()}`,
        phase: currentPhase,
        category: currentAssociation.category || 'association',
        mainItem: currentAssociation.main,
        correctAnswer: currentAssociation.correct,
        userAnswer: selectedOption,
        isCorrect,
        responseTime: reactionTime,
        difficulty: difficulty.toLowerCase(),
        options: currentAssociation.options,
        explanation: currentAssociation.explanation,
        timestamp: Date.now()
      }

      await collectorsHub.collectGameEvent(gameData)
      // 🔄 Registrar interação multissensorial
      await recordMultisensoryInteraction('game_interaction', {
        interactionType: 'user_action',
        gameSpecificData: gameData,
        multisensoryProcessing: {
          visualProcessing: { visualRecognition: 0.7, visualMemory: 0.7, visualAttention: 0.7 },
          cognitiveProcessing: { associativeMemory: 0.7, processingSpeed: 0.7, adaptability: 0.7 },
          behavioralProcessing: { interactionCount: 0.7, averageResponseTime: 0.7, consistency: 0.7 }
        }
      });
      console.log('📊 ImageAssociation: Dados coletados:', gameData)
      
    } catch (error) {
      console.warn('⚠️ ImageAssociation: Erro ao registrar interação:', error)
    }
  }, [collectorsHub, currentAssociation, recordMultisensoryInteraction, difficulty])

  // Carregar fase atual
  const loadCurrentPhase = useCallback(() => {
    // Filtrar associações por dificuldade atual
    const availableAssociations = ImageAssociationConfig.associations.filter(
      a => a.difficulty === difficulty
    );
    
    const association = availableAssociations.find(a => a.phase === currentPhase);
    
    if (association) {
      setCurrentAssociation(association);
      setSelectedOption(null);
      setFeedback(null);
    } else {
      // Se não encontrar a fase, mostrar mensagem de conclusão
      setFeedback({
        type: 'success',
        message: `🎉 Parabéns! Você completou todas as fases da dificuldade ${difficulty}! 🏆`
      });
    }
  }, [difficulty, currentPhase]);

  // Próxima fase
  const nextPhase = useCallback(() => {
    // Obter associações da dificuldade atual
    const availableAssociations = ImageAssociationConfig.associations.filter(
      a => a.difficulty === difficulty
    );
    
    // Encontrar próxima fase
    const currentIndex = availableAssociations.findIndex(a => a.phase === currentPhase);
    
    if (currentIndex >= 0 && currentIndex < availableAssociations.length - 1) {
      // Há mais fases nesta dificuldade
      const nextAssociation = availableAssociations[currentIndex + 1];
      setCurrentPhase(nextAssociation.phase);
    } else {
      // Completou todas as fases da dificuldade atual
      setFeedback({
        type: 'success',
        message: `🎉 Parabéns! Você completou todas as fases da dificuldade ${difficulty}! 🏆`
      });
      
      // Anunciar conclusão via TTS
      speak(`Parabéns! Você completou todas as fases da dificuldade ${difficulty}! Excelente trabalho!`, {
        rate: 1.0,
        onEnd: () => console.log('Feedback de conclusão anunciado')
      });
      
      // Voltar ao menu após alguns segundos
      setTimeout(() => {
        setShowStartScreen(true);
        setGameStarted(false);
      }, 3000);
      return;
    }
    
    setTimeout(() => {
      loadCurrentPhase();
    }, 100);
  }, [difficulty, currentPhase, speak, loadCurrentPhase]);

  // 🔄 Função para trocar atividade manualmente
  const switchToActivity = useCallback((activityId) => {
    console.log('🔄 Switching to activity:', activityId);

    setGameState(prev => ({
      ...prev,
      currentActivity: activityId,
      activityData: {
        ...prev.activityData,
        [activityId]: {} // Reset da atividade
      }
    }));

    // Gerar novo conteúdo para a atividade
    switch (activityId) {
      case ACTIVITY_TYPES.BASIC_ASSOCIATION.id:
        generateBasicAssociation();
        break;
      case ACTIVITY_TYPES.CATEGORY_SORTING.id:
        generateCategorySorting();
        break;
      case ACTIVITY_TYPES.VISUAL_SEQUENCE.id:
        generateVisualSequence();
        break;
      case ACTIVITY_TYPES.EMOTION_MATCHING.id:
        generateEmotionMatching();
        break;
      case ACTIVITY_TYPES.CONTEXT_REASONING.id:
        generateContextReasoning();
        break;
      default:
        generateBasicAssociation();
    }

    // Anunciar nova atividade
    const activity = Object.values(ACTIVITY_TYPES).find(a => a.id === activityId);
    if (activity && speak) {
      speak(`Atividade alterada para: ${activity.name}. ${activity.description}`, { rate: 0.8 });
    }
  }, [generateBasicAssociation, generateCategorySorting, generateVisualSequence, generateEmotionMatching, generateContextReasoning, speak]);

  // Lidar com sucesso
  const handleSuccess = useCallback(async () => {
    setSuccesses(prev => prev + 1);
    
    const points = difficulty === 'EASY' ? 10 : difficulty === 'MEDIUM' ? 15 : 20;
    setScore(prev => prev + points);
    
    // Atualizar level e estrelas baseado no score
    const newLevel = Math.floor(score / 100) + 1;
    setLevel(newLevel);
    setStars(Math.min(3, Math.floor(getAccuracy() / 30)));

    // Mensagem de sucesso
    const successMessages = [
      "Muito bem! Excelente associação! 🎉",
      "Perfeito! Você entendeu a relação! ⭐",
      "Fantástico! Continue assim! 🚀",
      "Brilhante! Sua lógica está ótima! 💡"
    ];
    
    const message = successMessages[Math.floor(Math.random() * successMessages.length)];
    
    setFeedback({
      type: 'success',
      message: `${message} +${points} pontos!`
    });

    // Anunciar sucesso via TTS
    speak(`${message} Você ganhou ${points} pontos!`, {
      rate: 1.1,
      onEnd: () => console.log('Feedback de sucesso anunciado')
    });

    // Aguardar um pouco e carregar próxima fase automaticamente
    setTimeout(() => {
      nextPhase();
    }, 2000);
  }, [difficulty, score, getAccuracy, speak, nextPhase])

  // Lidar com erro
  const handleError = useCallback(() => {
    setFeedback({
      type: 'error',
      message: 'Pense na relação entre os elementos! Tente novamente! 🤔',
    })

    // Anunciar erro via TTS
    speak('Ops! Pense na relação entre os elementos. Tente novamente!', {
      rate: 0.9,
      onEnd: () => console.log('Feedback de erro anunciado')
    });

    // Limpar seleção após feedback
    setTimeout(() => {
      setSelectedOption(null);
      setFeedback(null);
    }, 2000);
  }, [speak]);

  const handleOptionSelect = useCallback(async (option) => {
    if (selectedOption) return

    const startTime = Date.now()
    setSelectedOption(option)
    setAttempts(prev => prev + 1)

    const isCorrect = option.emoji === currentAssociation.correct.emoji
    const reactionTime = Date.now() - startTime

    // 🖼️ Registrar com coletores avançados
    await recordAssociationInteraction(option, isCorrect, reactionTime)

    if (isCorrect) {
      setTimeout(() => {
        handleSuccess()
      }, 1000)
    } else {
      handleError()
    }
  }, [selectedOption, currentAssociation, recordAssociationInteraction, handleSuccess, handleError]);

  // Função para reiniciar o jogo
  const restartGame = useCallback(() => {
    setShowStartScreen(true);
    setGameStarted(false);
    setCurrentAssociation(null);
    setSelectedOption(null);
    setFeedback(null);
    setScore(0);
    setLevel(1);
    setStars(0);
    setSuccesses(0);
    setAttempts(0);
    setCurrentPhase(1);
    setAttemptCount(0);
    setCognitiveAnalysisVisible(false);
    setAnalysisResults(null);
  }, []);

  // Efeito para carregar fase quando currentPhase muda
  useEffect(() => {
    if (gameStarted && currentPhase) {
      loadCurrentPhase();
    }
  }, [currentPhase, gameStarted, difficulty, loadCurrentPhase]);

  // Mover finalizeMultisensorySession para useCallback
  const finalizeMultisensorySession = useCallback(async () => {
    try {
      const multisensoryReport = await finalizeMultisensory({
        finalScore: score,
        finalAccuracy: attempts > 0 ? successes / attempts : 0,
        totalInteractions: attempts,
        sessionDuration: Date.now() - gameStartTime,
        difficulty: difficulty
      });
      console.log('🔄 ImageAssociation: Relatório multissensorial final:', multisensoryReport);
    } catch (error) {
      console.warn('⚠️ ImageAssociation: Erro ao finalizar sessão multissensorial:', error);
    }
  }, [finalizeMultisensory, score, successes, attempts, gameStartTime, difficulty]);

  // 🔄 Detectar quando o jogo é completado e finalizar sessão multissensorial
  useEffect(() => {
    if (gameStarted && level > 10) { // Assumindo que 10 é o nível máximo
      finalizeMultisensorySession();
    }
  }, [gameStarted, level, finalizeMultisensorySession]);

  // Cleanup do TTS quando componente é desmontado
  useEffect(() => {
    return () => {
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);  

  // Função para toggle do TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      console.log(`🔊 TTS ${newState ? 'ativado' : 'desativado'}`);
      
      // Se desativando, parar qualquer fala em andamento
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
      
      // Salvar preferência no localStorage
      localStorage.setItem('imageAssociationTTS', newState.toString());
      
      return newState;
    });
  }, []);

  // Carregar preferência do TTS do localStorage
  useEffect(() => {
    const savedTTSState = localStorage.getItem('imageAssociationTTS');
    if (savedTTSState !== null) {
      setTtsActive(savedTTSState === 'true');
    }
  }, []);

  // Cleanup do TTS quando componente for desmontado
  useEffect(() => {
    return () => {
      // Parar qualquer TTS em andamento ao sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      console.log('🔇 TTS parado ao sair do jogo Associação de Imagens');
    };
  }, []);

  // Cleanup global para evitar TTS vazando entre jogos
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
        setIsSpeaking(false);
        setCurrentSpeechButton(null);
      }
    };

    const handlePageHide = () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('pagehide', handlePageHide);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('pagehide', handlePageHide);
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // =====================================================
  // 🎯 FUNÇÕES DE RENDERIZAÇÃO REDESENHADAS - LAYOUTS ÚNICOS
  // =====================================================

  // 🔗 ASSOCIAÇÃO BÁSICA - Layout com conexão visual
  const renderBasicAssociation = () => {
    const activityData = gameState.activityData?.basicAssociation || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🔗 Teste de Associação Conceitual</h2>
          <p className={styles.questionSubtitle}>
            {currentAssociation ? `O que se relaciona com ${currentAssociation.main.label}?` : 'Conecte imagens relacionadas'}
          </p>
        </div>

        {currentAssociation && (
          <>
            {/* Área de objetos - Layout de conexão visual */}
            <div className={styles.objectsDisplay}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '2rem',
                padding: '2rem'
              }}>
                {/* Item principal destacado */}
                <div style={{
                  textAlign: 'center',
                  padding: '2rem',
                  border: '3px solid rgba(33, 150, 243, 0.6)',
                  borderRadius: '20px',
                  backgroundColor: 'rgba(33, 150, 243, 0.2)',
                  minWidth: '200px',
                  boxShadow: '0 0 20px rgba(33, 150, 243, 0.3)'
                }}>
                  <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>
                    {currentAssociation.main.emoji}
                  </div>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>
                    {currentAssociation.main.label}
                  </div>
                </div>

                {/* Seta de conexão animada */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <div style={{
                    fontSize: '3rem',
                    animation: 'bounce 2s infinite',
                    color: 'rgba(255, 193, 7, 0.8)'
                  }}>
                    ⬇️
                  </div>
                  <div style={{
                    fontSize: '1rem',
                    color: 'rgba(255,255,255,0.8)',
                    fontWeight: 'bold'
                  }}>
                    SE RELACIONA COM
                  </div>
                </div>
              </div>
            </div>

            {/* Opções de resposta - Layout em grid destacado */}
            <div className={styles.answerOptions}>
              {currentAssociation.options.map((option, index) => {
                const isSelected = selectedOption === option;
                const isCorrect = option.emoji === currentAssociation.correct.emoji;

                return (
                  <button
                    key={index}
                    className={`${styles.answerButton} ${
                      isSelected ? (isCorrect ? styles.correct : styles.incorrect) : ''
                    }`}
                    onClick={() => handleOptionSelect(option)}
                    disabled={selectedOption !== null}
                    aria-label={`Escolher ${option.label}`}
                    style={{
                      border: isSelected ?
                        (isCorrect ? '3px solid #4CAF50' : '3px solid #f44336') :
                        '2px solid rgba(255,255,255,0.3)',
                      backgroundColor: isSelected ?
                        (isCorrect ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)') :
                        'var(--card-background)',
                      transform: isSelected ? 'scale(1.05)' : 'scale(1)',
                      boxShadow: isSelected ?
                        (isCorrect ? '0 0 15px rgba(76, 175, 80, 0.5)' : '0 0 15px rgba(244, 67, 54, 0.5)') :
                        'none'
                    }}
                  >
                    <div style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>
                      {option.emoji}
                    </div>
                    <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>
                      {option.label}
                    </div>

                    {isSelected && (
                      <div style={{
                        position: 'absolute',
                        top: '10px',
                        right: '10px',
                        fontSize: '1.5rem',
                        fontWeight: 'bold'
                      }}>
                        {isCorrect ? '✅' : '❌'}
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </>
        )}
      </div>
    );
  };

  // 📂 CLASSIFICAÇÃO POR CATEGORIA - Layout com área de classificação
  const renderCategorySorting = () => {
    const activityData = gameState.activityData?.categorySorting || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>📂 Teste de Categorização Mental</h2>
          <p className={styles.questionSubtitle}>
            {activityData.targetCategory ?
              `Encontre todos os itens da categoria: ${activityData.targetCategory.name}` :
              'Agrupe imagens por categoria'
            }
          </p>
        </div>

        {activityData.targetCategory && (
          <>
            {/* Área de objetos - Layout de categoria alvo */}
            <div className={styles.objectsDisplay}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '2rem'
              }}>
                {/* Categoria alvo */}
                <div style={{
                  textAlign: 'center',
                  padding: '2rem',
                  border: '3px solid rgba(156, 39, 176, 0.6)',
                  borderRadius: '20px',
                  backgroundColor: 'rgba(156, 39, 176, 0.2)',
                  minWidth: '300px'
                }}>
                  <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>
                    {activityData.targetCategory.emoji}
                  </div>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>
                    Categoria: {activityData.targetCategory.name}
                  </div>
                </div>

                {/* Área de classificação */}
                <div style={{
                  border: '2px dashed rgba(156, 39, 176, 0.5)',
                  borderRadius: '16px',
                  padding: '2rem',
                  minHeight: '120px',
                  minWidth: '400px',
                  backgroundColor: 'rgba(156, 39, 176, 0.1)',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '1.2rem', marginBottom: '1rem', color: 'rgba(255,255,255,0.8)' }}>
                    📥 Área de Classificação
                  </div>

                  {activityData.sortedItems?.length > 0 ? (
                    <div style={{
                      display: 'flex',
                      gap: '1rem',
                      justifyContent: 'center',
                      flexWrap: 'wrap'
                    }}>
                      {activityData.sortedItems.map((item, index) => (
                        <div key={index} style={{
                          padding: '1rem',
                          border: '2px solid rgba(76, 175, 80, 0.6)',
                          borderRadius: '12px',
                          backgroundColor: 'rgba(76, 175, 80, 0.2)',
                          textAlign: 'center'
                        }}>
                          <div style={{ fontSize: '2rem' }}>{item.emoji}</div>
                          <div style={{ fontSize: '0.8rem' }}>{item.label}</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ color: 'rgba(255,255,255,0.6)' }}>
                      Clique nos itens abaixo para classificá-los
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Opções de resposta - Layout de itens para classificar */}
            <div className={styles.answerOptions}>
              {activityData.allItems?.map((item, index) => {
                const isSelected = activityData.sortedItems?.some(sorted => sorted.emoji === item.emoji);
                const isCorrect = activityData.correctItems?.some(correct => correct.emoji === item.emoji);

                return (
                  <button
                    key={index}
                    className={`${styles.answerButton} ${isSelected ? styles.selected : ''}`}
                    onClick={() => {
                      if (!isSelected) {
                        setGameState(prev => ({
                          ...prev,
                          activityData: {
                            ...prev.activityData,
                            categorySorting: {
                              ...prev.activityData.categorySorting,
                              sortedItems: [...(prev.activityData.categorySorting?.sortedItems || []), item]
                            }
                          }
                        }));

                        if (isCorrect) {
                          speak(`Correto! ${item.label} pertence à categoria ${activityData.targetCategory.name}.`);
                        } else {
                          speak(`${item.label} não pertence à categoria ${activityData.targetCategory.name}.`);
                        }
                      }
                    }}
                    disabled={isSelected}
                    aria-label={`Classificar ${item.label}`}
                    style={{
                      opacity: isSelected ? 0.5 : 1,
                      border: isSelected ?
                        (isCorrect ? '3px solid #4CAF50' : '3px solid #f44336') :
                        '2px solid rgba(255,255,255,0.3)'
                    }}
                  >
                    <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                      {item.emoji}
                    </div>
                    <div style={{ fontSize: '0.9rem' }}>
                      {item.label}
                    </div>

                    {isSelected && (
                      <div style={{
                        position: 'absolute',
                        top: '5px',
                        right: '5px',
                        fontSize: '1.2rem'
                      }}>
                        {isCorrect ? '✅' : '❌'}
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </>
        )}
      </div>
    );
  };

  // 📝 SEQUÊNCIA VISUAL - Layout com padrão sequencial
  const renderVisualSequence = () => {
    const activityData = gameState.activityData?.visualSequence || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>📝 Teste de Raciocínio Sequencial</h2>
          <p className={styles.questionSubtitle}>Analise o padrão e escolha o próximo elemento da sequência</p>
        </div>

        {activityData.pattern && (
          <>
            {/* Área de objetos - Layout de sequência visual */}
            <div className={styles.objectsDisplay}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '2rem',
                padding: '2rem'
              }}>
                {/* Título do padrão */}
                <div style={{
                  textAlign: 'center',
                  padding: '1rem',
                  backgroundColor: 'rgba(255, 152, 0, 0.2)',
                  borderRadius: '12px',
                  border: '2px solid rgba(255, 152, 0, 0.5)'
                }}>
                  <div style={{ fontSize: '1.2rem', fontWeight: 'bold' }}>
                    🧩 Padrão: {activityData.pattern.name}
                  </div>
                </div>

                {/* Sequência visual */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  padding: '2rem',
                  backgroundColor: 'rgba(0,0,0,0.3)',
                  borderRadius: '16px',
                  border: '2px solid rgba(255,255,255,0.2)'
                }}>
                  {activityData.pattern.sequence.map((item, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      padding: '1.5rem',
                      border: '3px solid rgba(33, 150, 243, 0.6)',
                      borderRadius: '16px',
                      backgroundColor: 'rgba(33, 150, 243, 0.2)',
                      minWidth: '80px'
                    }}>
                      <div style={{
                        fontSize: '0.8rem',
                        fontWeight: 'bold',
                        backgroundColor: 'rgba(33, 150, 243, 0.8)',
                        color: 'white',
                        borderRadius: '50%',
                        width: '25px',
                        height: '25px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        marginBottom: '0.5rem'
                      }}>
                        {index + 1}
                      </div>
                      <div style={{ fontSize: '3rem' }}>{item}</div>
                    </div>
                  ))}

                  {/* Seta indicativa */}
                  <div style={{
                    fontSize: '3rem',
                    color: 'rgba(255, 193, 7, 0.8)',
                    animation: 'pulse 1.5s infinite'
                  }}>
                    ➤
                  </div>

                  {/* Elemento faltante */}
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    padding: '1.5rem',
                    border: '4px dashed rgba(255, 193, 7, 0.8)',
                    borderRadius: '16px',
                    backgroundColor: 'rgba(255, 193, 7, 0.3)',
                    minWidth: '80px',
                    animation: 'pulse 2s infinite'
                  }}>
                    <div style={{
                      fontSize: '0.8rem',
                      fontWeight: 'bold',
                      backgroundColor: 'rgba(255, 193, 7, 0.8)',
                      color: 'white',
                      borderRadius: '50%',
                      width: '25px',
                      height: '25px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: '0.5rem'
                    }}>
                      {activityData.pattern.sequence.length + 1}
                    </div>
                    <div style={{ fontSize: '3rem' }}>❓</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Opções de resposta - Layout de escolhas sequenciais */}
            <div className={styles.answerOptions}>
              {activityData.pattern.options?.map((option, index) => {
                const isSelected = activityData.userAnswer === option;
                const isCorrect = option === activityData.pattern.correct;

                return (
                  <button
                    key={index}
                    className={`${styles.answerButton} ${isSelected ? styles.selected : ''}`}
                    onClick={() => {
                      setGameState(prev => ({
                        ...prev,
                        activityData: {
                          ...prev.activityData,
                          visualSequence: {
                            ...prev.activityData.visualSequence,
                            userAnswer: option
                          }
                        }
                      }));

                      if (isCorrect) {
                        speak(`Excelente! ${option} completa o padrão ${activityData.pattern.name} corretamente.`);
                      } else {
                        speak(`${option} selecionado. Analise se completa o padrão.`);
                      }
                    }}
                    disabled={activityData.userAnswer !== null}
                    aria-label={`Escolher ${option}`}
                    style={{
                      border: isSelected ?
                        (isCorrect ? '3px solid #4CAF50' : '3px solid #f44336') :
                        '2px solid rgba(255,255,255,0.3)',
                      backgroundColor: isSelected ?
                        (isCorrect ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)') :
                        'var(--card-background)',
                      transform: isSelected ? 'scale(1.05)' : 'scale(1)'
                    }}
                  >
                    <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>
                      {option}
                    </div>

                    {isSelected && (
                      <div style={{
                        position: 'absolute',
                        top: '10px',
                        right: '10px',
                        fontSize: '1.5rem'
                      }}>
                        {isCorrect ? '✅' : '❌'}
                      </div>
                    )}
                  </button>
                );
              })}
            </div>
          </>
        )}
      </div>
    );
  };

  // 😊 CORRESPONDÊNCIA EMOCIONAL - Layout com situações e emoções
  const renderEmotionMatching = () => {
    const activityData = gameState.activityData?.emotionMatching || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>😊 Teste de Correspondência Emocional</h2>
          <p className={styles.questionSubtitle}>
            {activityData.situation ? 
              `Como você se sentiria em: ${activityData.situation.label}?` : 
              'Identifique a emoção adequada para cada situação'
            }
          </p>
        </div>

        {activityData.situation && (
          <>
            {/* Área de objetos - Layout de situação emocional */}
            <div className={styles.objectsDisplay}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '2rem',
                padding: '2rem'
              }}>
                {/* Situação apresentada */}
                <div style={{
                  textAlign: 'center',
                  padding: '2rem',
                  border: '3px solid rgba(255, 193, 7, 0.6)',
                  borderRadius: '20px',
                  backgroundColor: 'rgba(255, 193, 7, 0.2)',
                  minWidth: '300px',
                  boxShadow: '0 0 20px rgba(255, 193, 7, 0.3)'
                }}>
                  <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>
                    {activityData.situation.emoji}
                  </div>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>
                    {activityData.situation.label}
                  </div>
                </div>

                {/* Pergunta emocional */}
                <div style={{
                  textAlign: 'center',
                  padding: '1rem',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '12px',
                  border: '2px solid rgba(255, 255, 255, 0.2)'
                }}>
                  <div style={{ fontSize: '1.2rem', fontWeight: 'bold', color: 'white' }}>
                    🤔 Como você se sentiria nesta situação?
                  </div>
                </div>
              </div>
            </div>

            {/* Opções de resposta - Layout de emoções */}
            <div className={styles.answerOptions}>
              {activityData.emotions?.map((emotion, index) => {
                const isSelected = activityData.userAnswer === emotion;
                const isCorrect = emotion.correct;

                return (
                  <button
                    key={index}
                    className={`${styles.answerButton} ${isSelected ? styles.selected : ''}`}
                    onClick={() => {
                      setGameState(prev => ({
                        ...prev,
                        activityData: {
                          ...prev.activityData,
                          emotionMatching: {
                            ...prev.activityData.emotionMatching,
                            userAnswer: emotion
                          }
                        }
                      }));

                      if (isCorrect) {
                        speak(`Perfeito! ${emotion.label} é realmente como nos sentimos em ${activityData.situation.label}.`);
                      } else {
                        speak(`${emotion.label} selecionado. Pense em como você se sentiria nesta situação.`);
                      }
                    }}
                    disabled={activityData.userAnswer !== null}
                    aria-label={`Escolher emoção: ${emotion.label}`}
                    style={{
                      border: isSelected ?
                        (isCorrect ? '3px solid #4CAF50' : '3px solid #f44336') :
                        '2px solid rgba(255,255,255,0.3)',
                      backgroundColor: isSelected ?
                        (isCorrect ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)') :
                        'var(--card-background)',
                      transform: isSelected ? 'scale(1.05)' : 'scale(1)',
                      boxShadow: isSelected ?
                        (isCorrect ? '0 0 15px rgba(76, 175, 80, 0.5)' : '0 0 15px rgba(244, 67, 54, 0.5)') :
                        'none'
                    }}
                  >
                    <div style={{ fontSize: '3rem', marginBottom: '0.5rem' }}>
                      {emotion.emoji}
                    </div>
                    <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>
                      {emotion.label}
                    </div>

                    {isSelected && (
                      <div style={{
                        position: 'absolute',
                        top: '10px',
                        right: '10px',
                        fontSize: '1.5rem',
                        fontWeight: 'bold'
                      }}>
                        {isCorrect ? '✅' : '❌'}
                      </div>
                    )}
                  </button>
                );
              })}
            </div>

            {/* Explicação quando resposta selecionada */}
            {activityData.userAnswer && (
              <div style={{
                margin: '2rem auto',
                padding: '1.5rem',
                backgroundColor: activityData.userAnswer.correct ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)',
                border: activityData.userAnswer.correct ? '2px solid rgba(76, 175, 80, 0.5)' : '2px solid rgba(244, 67, 54, 0.5)',
                borderRadius: '12px',
                textAlign: 'center',
                maxWidth: '600px'
              }}>
                <div style={{
                  fontSize: '1.1rem',
                  color: 'white',
                  fontWeight: 'bold',
                  marginBottom: '0.5rem'
                }}>
                  {activityData.userAnswer.correct ? '🎉 Correto!' : '🤔 Pense novamente'}
                </div>
                <div style={{
                  fontSize: '1rem',
                  color: 'rgba(255,255,255,0.9)'
                }}>
                  {activityData.explanation}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    );
  };

  // 🏠 RACIOCÍNIO CONTEXTUAL - Layout com contextos e objetos
  const renderContextReasoning = () => {
    const activityData = gameState.activityData?.contextReasoning || {};

    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>🏠 Teste de Raciocínio Contextual</h2>
          <p className={styles.questionSubtitle}>
            {activityData.question || 'O que você encontra neste contexto?'}
          </p>
        </div>

        {activityData.context && (
          <>
            {/* Área de objetos - Layout de contexto */}
            <div className={styles.objectsDisplay}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '2rem',
                padding: '2rem'
              }}>
                {/* Contexto principal */}
                <div style={{
                  textAlign: 'center',
                  padding: '2rem',
                  border: '3px solid rgba(103, 58, 183, 0.6)',
                  borderRadius: '20px',
                  backgroundColor: 'rgba(103, 58, 183, 0.2)',
                  minWidth: '300px',
                  boxShadow: '0 0 20px rgba(103, 58, 183, 0.3)'
                }}>
                  <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>
                    {activityData.context.emoji}
                  </div>
                  <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white' }}>
                    {activityData.context.label}
                  </div>
                </div>

                {/* Área de seleção de itens corretos */}
                <div style={{
                  border: '2px dashed rgba(103, 58, 183, 0.5)',
                  borderRadius: '16px',
                  padding: '2rem',
                  minHeight: '120px',
                  minWidth: '400px',
                  backgroundColor: 'rgba(103, 58, 183, 0.1)',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '1.2rem', marginBottom: '1rem', color: 'rgba(255,255,255,0.8)' }}>
                    🎯 Itens selecionados que pertencem ao contexto
                  </div>

                  {activityData.selectedItems?.length > 0 ? (
                    <div style={{
                      display: 'flex',
                      gap: '1rem',
                      justifyContent: 'center',
                      flexWrap: 'wrap'
                    }}>
                      {activityData.selectedItems.map((item, index) => (
                        <div key={index} style={{
                          padding: '1rem',
                          border: item.belongs ? '2px solid rgba(76, 175, 80, 0.6)' : '2px solid rgba(244, 67, 54, 0.6)',
                          borderRadius: '12px',
                          backgroundColor: item.belongs ? 'rgba(76, 175, 80, 0.2)' : 'rgba(244, 67, 54, 0.2)',
                          textAlign: 'center'
                        }}>
                          <div style={{ fontSize: '2rem' }}>{item.emoji}</div>
                          <div style={{ fontSize: '0.8rem' }}>{item.label}</div>
                          <div style={{ fontSize: '1.2rem', marginTop: '0.5rem' }}>
                            {item.belongs ? '✅' : '❌'}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div style={{ color: 'rgba(255,255,255,0.6)' }}>
                      Clique nos itens abaixo que pertencem ao contexto
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Opções de resposta - Layout de itens contextuais */}
            <div className={styles.answerOptions}>
              {activityData.items?.map((item, index) => {
                const isSelected = activityData.selectedItems?.some(selected => selected.emoji === item.emoji);
                const isCorrect = item.belongs;

                return (
                  <button
                    key={index}
                    className={`${styles.answerButton} ${isSelected ? styles.selected : ''}`}
                    onClick={() => {
                      if (!isSelected) {
                        setGameState(prev => ({
                          ...prev,
                          activityData: {
                            ...prev.activityData,
                            contextReasoning: {
                              ...prev.activityData.contextReasoning,
                              selectedItems: [...(prev.activityData.contextReasoning?.selectedItems || []), item]
                            }
                          }
                        }));

                        if (isCorrect) {
                          speak(`Correto! ${item.label} realmente pertence ao contexto ${activityData.context.label}.`);
                        } else {
                          speak(`${item.label} não pertence ao contexto ${activityData.context.label}. Pense sobre onde encontramos isso.`);
                        }
                      }
                    }}
                    disabled={isSelected}
                    aria-label={`Selecionar ${item.label}`}
                    style={{
                      opacity: isSelected ? 0.7 : 1,
                      border: isSelected ?
                        (isCorrect ? '3px solid #4CAF50' : '3px solid #f44336') :
                        '2px solid rgba(255,255,255,0.3)',
                      backgroundColor: isSelected ?
                        (isCorrect ? 'rgba(76, 175, 80, 0.3)' : 'rgba(244, 67, 54, 0.3)') :
                        'var(--card-background)'
                    }}
                  >
                    <div style={{ fontSize: '2.5rem', marginBottom: '0.5rem' }}>
                      {item.emoji}
                    </div>
                    <div style={{ fontSize: '1rem', fontWeight: 'bold' }}>
                      {item.label}
                    </div>

                    {isSelected && (
                      <div style={{
                        position: 'absolute',
                        top: '10px',
                        right: '10px',
                        fontSize: '1.5rem',
                        fontWeight: 'bold'
                      }}>
                        {isCorrect ? '✅' : '❌'}
                      </div>
                    )}
                  </button>
                );
              })}
            </div>

            {/* Progresso da atividade */}
            {activityData.selectedItems?.length > 0 && (
              <div style={{
                margin: '2rem auto',
                padding: '1rem',
                backgroundColor: 'rgba(103, 58, 183, 0.2)',
                border: '2px solid rgba(103, 58, 183, 0.5)',
                borderRadius: '12px',
                textAlign: 'center',
                maxWidth: '400px'
              }}>
                <div style={{ fontSize: '1.1rem', color: 'white', fontWeight: 'bold' }}>
                  📊 Progresso: {activityData.selectedItems.length} itens selecionados
                </div>
                <div style={{ fontSize: '0.9rem', color: 'rgba(255,255,255,0.8)', marginTop: '0.5rem' }}>
                  Corretos: {activityData.selectedItems.filter(item => item.belongs).length} | 
                  Incorretos: {activityData.selectedItems.filter(item => !item.belongs).length}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    );
  };

  // 🧠 MEMÓRIA ASSOCIATIVA - Layout com pares
  const renderMemoryAssociation = () => (
    <div className={styles.questionArea}>
      <div className={styles.questionHeader}>
        <h2 className={styles.questionTitle}>🧠 Memória Associativa</h2>
        <p className={styles.questionSubtitle}>Lembre-se de pares de imagens</p>
      </div>

      <div className={styles.objectsDisplay}>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🧠</div>
          <div style={{ fontSize: '1.2rem', color: 'rgba(255,255,255,0.8)' }}>
            Atividade em desenvolvimento
          </div>
        </div>
      </div>

      <div className={styles.answerOptions}>
        <button className={styles.answerButton} disabled>
          <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🔧</div>
          <div style={{ fontSize: '0.9rem' }}>Em breve</div>
        </button>
      </div>
    </div>
  );

  // Se ainda não iniciou, mostra a tela de início
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Associação de Imagens"
        gameDescription="Desenvolva sua capacidade de associação e raciocínio lógico"
        gameIcon="🖼️"
        onStart={startGame}
        onBack={onBack}
        difficulties={[
          { id: 'easy', name: 'Fácil', description: 'Associações básicas e simples', icon: '�' },
          { id: 'medium', name: 'Médio', description: 'Relações funcionais e contextuais', icon: '�' },
          { id: 'hard', name: 'Avançado', description: 'Associações abstratas e complexas', icon: '�' }
        ]}
      />
    );
  }

  return (
    <div 
      className={`${styles.imageAssociationGame} ${settings.reducedMotion ? 'reduced-motion' : ''} ${settings.highContrast ? 'high-contrast' : ''}`}
      data-font-size={settings.fontSize}
      data-theme={settings.colorScheme}
      style={{
        fontSize: settings.fontSize === 'small' ? '0.875rem' : 
                 settings.fontSize === 'large' ? '1.25rem' : '1rem'
      }}
    >
      <div className={styles.gameContent}>
        {/* Header do jogo - EXATO padrão LetterRecognition */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🖼️ Associação de Imagens V3
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              {ACTIVITY_TYPES[gameState.currentActivity?.toUpperCase()]?.name || 'Associação Básica'}
            </div>
          </h1>
          <button
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
            onClick={toggleTTS}
            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* Header com estatísticas - EXATO padrão LetterRecognition */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.round}</div>
            <div className={styles.statLabel}>Rodada</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.accuracy}%</div>
            <div className={styles.statLabel}>Precisão</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.level}</div>
            <div className={styles.statLabel}>Nível</div>
          </div>
        </div>

        {/* Menu de atividades - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => switchToActivity(activity.id)}
            >
              <span>{activity.icon}</span>
              <span>{activity.name}</span>
            </button>
          ))}
        </div>

        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}
        {gameState.currentActivity === ACTIVITY_TYPES.BASIC_ASSOCIATION.id && renderBasicAssociation()}
        {gameState.currentActivity === ACTIVITY_TYPES.CATEGORY_SORTING.id && renderCategorySorting()}
        {gameState.currentActivity === ACTIVITY_TYPES.VISUAL_SEQUENCE.id && renderVisualSequence()}
        {gameState.currentActivity === ACTIVITY_TYPES.EMOTION_MATCHING.id && renderEmotionMatching()}
        {gameState.currentActivity === ACTIVITY_TYPES.CONTEXT_REASONING.id && renderContextReasoning()}

        {/* Controles do jogo - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameControls}>
          <button className={styles.controlButton} onClick={() => speak('Associação de imagens. Conecte imagens relacionadas e desenvolva raciocínio lógico.')}>
            🔊 Explicar
          </button>
          <button className={styles.controlButton} onClick={() => speak('Repita as instruções da atividade atual.')}>
            🔄 Repetir
          </button>
          <button className={styles.controlButton} onClick={() => speak('Teste de acessibilidade ativado.')}>
            🧪 Teste TTS
          </button>
          <button className={styles.controlButton} onClick={() => setShowStartScreen(true)}>
            🔄 Reiniciar
          </button>
          <button className={styles.controlButton} onClick={onBack}>
            ⬅️ Voltar
          </button>
        </div>
      </div>
    </div>
  );
};

export default ImageAssociationGame

