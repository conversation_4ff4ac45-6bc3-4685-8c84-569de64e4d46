/**
 * @file AdvancedAIReport.module.css
 * @description Estilos modulares para Dashboard de Relatório de IA Avançado
 * @version 3.0.0
 */

/* Container principal do dashboard */
.dashboardContainer {
  padding: 24px;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

/* Header do dashboard */
.dashboardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.dashboardTitle {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.titleIcon {
  background: linear-gradient(135deg, #9f7aea 0%, #667eea 100%);
  padding: 8px;
  border-radius: 8px;
  color: white;
  font-size: 20px;
}

/* Controles do dashboard */
.dashboardControls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.timeframeSelector {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  font-size: 14px;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timeframeSelector:hover,
.timeframeSelector:focus {
  border-color: #9f7aea;
  box-shadow: 0 0 0 3px rgba(159, 122, 234, 0.1);
  outline: none;
}

.refreshButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, #9f7aea 0%, #667eea 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(159, 122, 234, 0.3);
}

/* Seção de perfil cognitivo */
.cognitiveProfileSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.profileHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.profileTitle {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.profileIcon {
  background: linear-gradient(135deg, #9f7aea, #667eea);
  padding: 6px;
  border-radius: 6px;
  color: white;
  font-size: 16px;
}

.profileGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.profileCard {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.profileCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #9f7aea, #667eea);
}

.profileCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.profileCardTitle {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.profileBadge {
  background: linear-gradient(135deg, #9f7aea, #667eea);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.profileValue {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 8px 0;
}

.profileDescription {
  font-size: 14px;
  color: #4a5568;
  line-height: 1.5;
}

/* Seção de análise de IA */
.aiAnalysisSection {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.analysisTitle {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.analysisGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.analysisCard {
  background: #f7fafc;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #9f7aea;
  position: relative;
}

.analysisCard.prediction {
  border-left-color: #667eea;
}

.analysisCard.recommendation {
  border-left-color: #48bb78;
}

.analysisCard.insight {
  border-left-color: #ed8936;
}

.analysisCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.analysisCardTitle {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin: 0;
}

.analysisConfidence {
  background: #9f7aea;
  color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
}

.analysisConfidence.prediction {
  background: #667eea;
}

.analysisConfidence.recommendation {
  background: #48bb78;
}

.analysisConfidence.insight {
  background: #ed8936;
}

.analysisContent {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 12px;
}

.analysisMetrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
  margin-top: 16px;
}

.analysisMetric {
  text-align: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(10px);
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.analysisMetricValue {
  font-size: 18px;
  font-weight: 700;
  color: white;
  margin: 0;
}

.analysisMetricLabel {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 4px 0 0 0;
}

/* Grid de gráficos de IA */
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.chartCard {
  background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(10px);
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.chartTitle {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chartSubtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 16px 0;
}

.chartContainer {
  position: relative;
  height: 300px;
}

.chartContainer.radar {
  height: 350px;
}

.chartContainer.line {
  height: 280px;
}

.chartContainer.doughnut {
  height: 250px;
}

/* Seção de recomendações */
.recommendationsSection {
  background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(10px);
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.recommendationsTitle {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommendationsList {
  display: grid;
  gap: 16px;
}

.recommendationItem {
  background: #f7fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #48bb78;
  display: flex;
  gap: 16px;
}

.recommendationItem.urgent {
  border-left-color: #f56565;
}

.recommendationItem.important {
  border-left-color: #ed8936;
}

.recommendationItem.suggestion {
  border-left-color: #667eea;
}

.recommendationIcon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  flex-shrink: 0;
  background: #48bb78;
}

.recommendationIcon.urgent {
  background: #f56565;
}

.recommendationIcon.important {
  background: #ed8936;
}

.recommendationIcon.suggestion {
  background: #667eea;
}

.recommendationContent {
  flex: 1;
}

.recommendationTitle {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin: 0 0 8px 0;
}

.recommendationText {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
  margin-bottom: 8px;
}

.recommendationMeta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.recommendationPriority {
  background: #48bb78;
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 500;
}

.recommendationPriority.urgent {
  background: #f56565;
}

.recommendationPriority.important {
  background: #ed8936;
}

.recommendationPriority.suggestion {
  background: #667eea;
}

/* Estados de loading e erro */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 16px;
}

.loadingText {
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}

.errorContainer {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
}

/* Responsividade */
@media (max-width: 768px) {
  .dashboardContainer {
    padding: 16px;
    margin-bottom: 16px;
  }

  .dashboardHeader {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .dashboardControls {
    justify-content: center;
  }

  .dashboardTitle {
    font-size: 24px;
    text-align: center;
  }

  .profileGrid,
  .analysisGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chartsGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .chartContainer {
    height: 250px;
  }

  .recommendationItem {
    flex-direction: column;
    gap: 12px;
  }

  .recommendationIcon {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .dashboardContainer {
    padding: 12px;
  }

  .dashboardTitle {
    font-size: 20px;
  }

  .profileValue {
    font-size: 22px;
  }

  .chartContainer {
    height: 200px;
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.dashboardContainer {
  animation: fadeIn 0.5s ease-out;
}

.profileCard,
.analysisCard {
  animation: slideIn 0.6s ease-out;
}

.chartCard,
.recommendationsSection {
  animation: fadeIn 0.6s ease-out;
}

.recommendationItem {
  animation: slideIn 0.7s ease-out;
}

.loadingContainer {
  animation: pulse 2s infinite;
}

/* Acessibilidade */
.refreshButton:focus-visible,
.timeframeSelector:focus-visible {
  outline: 2px solid #9f7aea;
  outline-offset: 2px;
}

/* Tema escuro (futuro) */
@media (prefers-color-scheme: dark) {
  .dashboardContainer {
    background-color: white;
    color: #e2e8f0;
  }

  .cognitiveProfileSection,
  .aiAnalysisSection,
  .recommendationsSection,
  .chartCard {
    background-color: white;
    border-color: rgba(255, 255, 255, 0.7);
  }

  .profileCard,
  .analysisCard {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-color: rgba(255, 255, 255, 0.7);
  }

  .dashboardTitle,
  .profileTitle,
  .analysisTitle,
  .recommendationsTitle,
  .chartTitle,
  .profileValue {
    color: #e2e8f0;
  }

  .recommendationItem {
    background-color: rgba(255, 255, 255, 0.7);
  }

  .analysisMetric {
    background-color: rgba(255, 255, 255, 0.7);
    border-color: rgba(255, 255, 255, 0.6);
  }
}
