/**
 * 🧠 MEMORY GAME - Portal Betina V3
 * Layout baseado no padrão do ColorMatch
 * Implementação completa com design moderno seguindo exatamente o padrão
 */

import React, { useState, useEffect, useCallback, useRef, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { v4 as uuidv4 } from 'uuid';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';

// Importa configurações e métricas específicas do jogo
import { MEMORY_CARDS, GAME_CONFIG, ENCOURAGEMENT_MESSAGES, THEME_CONFIG } from './MemoryGameConfig.js';
import { MemoryGameMetrics } from './MemoryGameMetrics.js';
import { useGameAudio } from '../../games/shared/GameUtils.js';

// Importa o componente padrão de tela de dificuldade
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';
import { SystemContext } from '../../components/context/SystemContext.jsx';

// Hook unificado para integração com backend
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
// Hook específico para análise terapêutica
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';

// 🧠 Importar coletores avançados de memória
import { MemoryGameCollectorsHub } from './collectors/index.js';

// 🔄 Importar hook multissensorial
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';

// Importar temas para a atividade de sequência numérica
import sequenceThemes from './sequenceThemes';

// Importa estilos modulares
import styles from './MemoryGame.module.css';

// 🎯 DEFINIÇÃO DAS ATIVIDADES - PADRÃO LETTERRECOGNITION
const ACTIVITY_TYPES = {
  pair_matching: {
    id: 'pair_matching',
    name: 'Pares de Cartas',
    icon: '🧩',
    description: 'Encontre todos os pares de cartas iguais'
  },
  sequence_memory: {
    id: 'sequence_memory',
    name: 'Sequência de Cores',
    icon: '🧠',
    description: 'Memorize e repita a sequência de cores'
  },
  spatial_location: {
    id: 'spatial_location',
    name: 'Posições Espaciais',
    icon: '📍',
    description: 'Lembre-se das posições espaciais'
  },
  image_reconstruction: {
    id: 'image_reconstruction',
    name: 'Reconstrução de Imagem',
    icon: '🖼️',
    description: 'Reconstrua a imagem original'
  },
  number_sequence: {
    id: 'number_sequence',
    name: 'Sequência Numérica',
    icon: '🔢',
    description: 'Complete a sequência numérica'
  }
};

function MemoryGame({ onBack }) {
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const sessionIdRef = useRef(uuidv4());
  
  // ♿ Contexto de acessibilidade
  const { settings } = useAccessibilityContext();
  
  // 🔊 Sistema de áudio para feedback sonoro
  const { playSound } = useGameAudio();
  
  // =====================================================
  // 🔊 SISTEMA DE TEXT-TO-SPEECH (TTS) PADRONIZADO
  // =====================================================
  
  // Estado do TTS com persistência
  const [ttsActive, setTtsActive] = useState(() => {
    const saved = localStorage.getItem('memoryGame_ttsActive');
    return saved !== null ? JSON.parse(saved) : true;
  });
  
  // Função para alternar TTS
  const toggleTTS = useCallback(() => {
    setTtsActive(prev => {
      const newState = !prev;
      localStorage.setItem('memoryGame_ttsActive', JSON.stringify(newState));
      
      // Cancelar qualquer fala em andamento se desabilitando
      if (!newState && 'speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
      
      return newState;
    });
  }, []);
  
  // Função TTS padronizada
  const speak = useCallback((text, options = {}) => {
    // Verificar se TTS está ativo
    if (!ttsActive || !('speechSynthesis' in window)) {
      return;
    }
    
    // Cancelar qualquer fala anterior
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'pt-BR';
    utterance.rate = options.rate || 0.9;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;
    
    window.speechSynthesis.speak(utterance);
  }, [ttsActive]);
  
  // Hook unificado para lógica de jogos - integração com backend
  const {
    startUnifiedSession,
    endUnifiedSession,
    recordInteraction,
    updateMetrics,
    sessionId,
    isSessionActive
  } = useUnifiedGameLogic('memory');
  
  // Sistema terapêutico específico para análise de memória
  const shouldUseTherapeutic = user?.id && user.id !== 'anonymous' && user.id !== '';
  const therapeuticOrchestrator = useTherapeuticOrchestrator({ 
    userId: shouldUseTherapeutic ? user.id : null 
  });
  
  // 🧠 Buscar instância dos coletores do sistema (evita múltiplas instanciações)
  const [collectorsHub] = useState(() => {
    // Tentar obter instância do sistema global primeiro
    if (window.globalSystemInstance?.gameSpecificProcessors?.gameCollectors?.MemoryGame?.hub) {
      console.log('🧠 Reutilizando instância existente do MemoryGame CollectorsHub');
      return window.globalSystemInstance.gameSpecificProcessors.gameCollectors.MemoryGame.hub;
    }
    // Fallback: criar nova instância apenas se necessário
    console.log('🧠 Criando nova instância do MemoryGame CollectorsHub');
    return new MemoryGameCollectorsHub();
  });
  
  // 🔄 Hook multissensorial integrado
  const {
    initializeSession: initMultisensory,
    recordInteraction: recordMultisensoryInteraction,
    finalizeSession: finalizeMultisensory,
    updateData: updateMultisensoryData,
    multisensoryData,
    isInitialized: multisensoryInitialized
  } = useMultisensoryIntegration(sessionId, {
    gameType: 'memory-game',
    sensorTypes: {
      visual: true,
      haptic: true,
      tts: ttsEnabled,
      gestural: true,
      biometric: true
    },
    adaptiveMode: true,
    autoUpdate: true,
    enablePatternAnalysis: true,
    logLevel: 'info',
    learningStyle: user?.profile?.learningStyle || 'visual'
  });
  
  // Estado do jogo V3 - Sistema de 6 atividades diversificadas
  const [gameState, setGameState] = useState({
    status: 'waiting', // waiting, playing, completed
    difficulty: GAME_CONFIG.initialDifficulty,
    activityTimerStarted: false, // 🔥 Controla quando o timer deve iniciar
    
    // 🎯 SISTEMA DE ATIVIDADES V3
    currentActivity: 'pair_matching',
    activitiesCompleted: 0,
    activitiesRotation: ['pair_matching', 'sequence_memory', 'spatial_location', 'image_reconstruction', 'number_sequence'],
    rotationIndex: 0,
    activityAttempts: 0,
    showActivityMenu: false,
    
    // Estados específicos por atividade
    activityData: {
      pair_matching: { cards: [], flippedCards: [], matchedCards: [] },
      sequence_memory: { sequence: [], userSequence: [], showingSequence: false },
      spatial_location: { grid: [], targetPositions: [], userSelections: [] },
      image_reconstruction: { pieces: [], targetImage: [], userArrangement: [] },
      number_sequence: { numbers: [], userNumbers: [], showingNumbers: false }
    },
    
    // Compatibilidade com código existente
    theme: null,
    cards: [],
    flippedCards: [],
    matchedCards: [],
    
    // Métricas gerais
    score: 0,
    attempts: 0,
    startTime: null,
    level: 1,
    gameStarted: false,
    showingCards: false,
    canPlay: false,
    feedback: null,
    lives: GAME_CONFIG.maxLives,
    moves: 0,
    timeElapsed: 0,
    elapsedTime: 0,
    bonusPoints: 0,
    streak: 0,
    perfectMatches: 0,
    hintsUsed: 0,
    timeBonus: 0,
    difficultyBonus: 0,
    metrics: new MemoryGameMetrics(),
    // Campos para funcionalidades avançadas
    sequenceMemorized: false,
    showingSequence: false,
    specialTaskActive: false,
    themeProgress: {
      groupsFormed: 0,
      requiredGroups: 0,
      currentTask: null
    }
  });
  
  // Mover finalizeMultisensorySession para useCallback
  const finalizeMultisensorySession = useCallback(async () => {
    try {
      const sessionDuration = gameState.startTime ? (Date.now() - gameState.startTime) : 0;
      const finalAccuracy = gameState.attempts > 0 ? (gameState.score / (gameState.attempts * 100)) : 0;
      
      const multisensoryReport = await finalizeMultisensory({
        finalScore: gameState.score,
        finalAccuracy: finalAccuracy,
        totalInteractions: gameState.attempts,
        sessionDuration: sessionDuration,
        difficulty: gameState.difficulty,
        matchedPairs: gameState.matchedCards.length / 2,
        totalPairs: gameState.cards.length / 2
      });
      
      console.log('🔄 MemoryGame: Relatório multissensorial final:', multisensoryReport);
    } catch (error) {
      console.warn('⚠️ MemoryGame: Erro ao finalizar sessão multissensorial:', error);
    }
  }, [gameState, finalizeMultisensory]);

  // 🧠 Função para registrar interações de memória para análise cognitiva
  const recordMemoryInteraction = useCallback((firstCard, secondCard, isCorrect, responseTime, attemptNumber) => {
    try {
      const interactionData = {
        type: 'memory_pair_attempt',
        data: {
          firstCard: {
            id: firstCard.id,
            pairId: firstCard.pairId,
            name: firstCard.name
          },
          secondCard: {
            id: secondCard.id,
            pairId: secondCard.pairId,
            name: secondCard.name
          },
          isCorrect,
          responseTime,
          attemptNumber,
          difficulty: gameState.difficulty,
          timestamp: Date.now()
        }
      };

      // Registrar no sistema unificado se disponível
      if (recordInteraction) {
        recordInteraction(interactionData);
      }

      // Registrar nas métricas do jogo
      if (metricsRef.current) {
        metricsRef.current.recordInteraction(interactionData);
      }

      // Registrar no sistema multissensorial
      if (recordMultisensoryInteraction) {
        recordMultisensoryInteraction('memory_interaction', interactionData);
      }
    } catch (error) {
      console.warn('⚠️ Erro ao registrar interação de memória:', error);
    }
  }, [gameState.difficulty, recordInteraction, recordMultisensoryInteraction]);

  // 🔄 Detectar quando o jogo é completado e finalizar sessão multissensorial
  useEffect(() => {
    if (gameState.status === 'completed' && multisensoryInitialized) {
      finalizeMultisensorySession();
    }
  }, [gameState, finalizeMultisensorySession, multisensoryInitialized]);

  // Conectar métricas ao backend após inicialização
  useEffect(() => {
    if (gameState.metrics && startUnifiedSession && recordInteraction && updateMetrics) {
      gameState.metrics.connectToBackend({
        startSession: startUnifiedSession,
        recordInteraction,
        updateMetrics
      });
    }
  }, [gameState, startUnifiedSession, recordInteraction, updateMetrics]);

  const [showStartScreen, setShowStartScreen] = useState(true);
  const [showEncouragement, setShowEncouragement] = useState(false);
  const [attemptCount, setAttemptCount] = useState(0);
  const [cognitiveAnalysisVisible, setCognitiveAnalysisVisible] = useState(false);
  const [analysisResults, setAnalysisResults] = useState(null);

  // Refs para métricas e timer
  const gameRef = useRef(null);
  const metricsRef = useRef(gameState.metrics);
  const timerRef = useRef(null);

  // Função para embaralhar arrays
  const shuffleArray = useCallback((array) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  }, []);

  // Funções de timer
  const startTimer = useCallback(() => {
    timerRef.current = setInterval(() => {
      setGameState(prev => ({
        ...prev,
        elapsedTime: Date.now() - prev.startTime
      }));
    }, 1000);
  }, []);

  const stopTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Função para reiniciar o jogo
  const handleRestart = useCallback(() => {
    // Parar timer atual
    stopTimer();
    
    // Resetar para o estado inicial
    setGameState(prev => ({
      ...prev,
      status: 'waiting',
      difficulty: GAME_CONFIG.initialDifficulty,
      activityTimerStarted: false,
      currentActivity: 'pair_matching',
      activitiesCompleted: 0,
      rotationIndex: 0,
      activityAttempts: 0,
      showActivityMenu: false,
      activityData: {
        pair_matching: { cards: [], flippedCards: [], matchedCards: [] },
        sequence_memory: { sequence: [], userSequence: [], showingSequence: false },
        spatial_location: { grid: [], targetPositions: [], userSelections: [] },
        image_reconstruction: { pieces: [], targetImage: [], userArrangement: [] },
        number_sequence: { numbers: [], userNumbers: [], showingNumbers: false }
      },
      cards: [],
      flippedCards: [],
      matchedCards: [],
      score: 0,
      attempts: 0,
      startTime: null,
      level: 1,
      gameStarted: false,
      showingCards: false,
      canPlay: false,
      feedback: null,
      lives: GAME_CONFIG.maxLives,
      moves: 0,
      timeElapsed: 0,
      elapsedTime: 0,
      bonusPoints: 0,
      streak: 0,
      perfectMatches: 0,
      hintsUsed: 0,
      timeBonus: 0,
      difficultyBonus: 0,
      sequenceMemorized: false,
      showingSequence: false,
      specialTaskActive: false,
      themeProgress: {
        groupsFormed: 0,
        requiredGroups: 0,
        currentTask: null
      }
    }));
    
    // Mostrar tela inicial novamente
    setShowStartScreen(true);
    
    // Feedback sonoro
    speak("Jogo reiniciado! Escolha uma nova dificuldade para começar.", { rate: 0.9 });
  }, [stopTimer, speak]);

  // Função para explicar o jogo
  const explainGame = useCallback(() => {
    const explanation = `
      Bem-vindo ao Jogo da Memória! 
      
      Como jogar:
      - Clique nas cartas para virá-las
      - Encontre os pares idênticos
      - Você tem ${gameState.lives} vidas
      - Tente fazer o menor número de tentativas possível
      - Complete o jogo no menor tempo para ganhar mais pontos
      
      Dificuldades disponíveis:
      - Fácil: ${GAME_CONFIG.difficulties.easy.pairs} pares
      - Médio: ${GAME_CONFIG.difficulties.medium.pairs} pares  
      - Difícil: ${GAME_CONFIG.difficulties.hard.pairs} pares
      
      Boa sorte e divirta-se!
    `;
    
    speak(explanation, { rate: 0.8 });
  }, [gameState.lives, speak]);

  // Função para resetar o jogo (alias para handleRestart)
  const resetGame = useCallback(() => {
    handleRestart();
  }, [handleRestart]);

  // Inicializar jogo
  useEffect(() => {
    if (gameState.status === 'playing' && gameState.activityTimerStarted) {
      startTimer();
    } else {
      stopTimer();
    }
    
    return () => {
      stopTimer();
      // Cancelar qualquer TTS ativo quando sair do jogo
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [gameState.status, gameState.activityTimerStarted, startTimer, stopTimer]);
  
  // Inicializar cartas automaticamente para teste
  useEffect(() => {
    if (!showStartScreen && gameState.cards.length === 0 && gameState.currentActivity === 'pair_matching') {
      // Inicializar cartas básicas para permitir teste imediato
      const cardSet = MEMORY_CARDS[gameState.difficulty] || MEMORY_CARDS['easy'] || [];
      if (cardSet.length > 0) {
        const shuffledCards = shuffleArray([...cardSet]).map((card, index) => ({
          ...card,
          uniqueId: `${card.id}-${index}`,
          isFlipped: false,
          isMatched: false
        }));
        
        setGameState(prev => ({
          ...prev,
          cards: shuffledCards,
          flippedCards: [],
          matchedCards: [],
          status: 'playing',
          activityData: {
            ...prev.activityData,
            pair_matching: {
              cards: shuffledCards,
              flippedCards: [],
              matchedCards: []
            }
          }
        }));
      }
    }
  }, [showStartScreen, gameState.cards.length, gameState.currentActivity, gameState.difficulty, shuffleArray]);
  
  // Função para determinar número de colunas do grid baseado na dificuldade
  const getGridCols = useCallback(() => {
    switch (gameState.difficulty) {
      case 'easy':
        return 2; // 2x4 grid = 2 colunas
      case 'medium':
        return 3; // 3x4 grid = 3 colunas
      case 'hard':
        return 4; // 4x4 grid = 4 colunas
      default:
        return 2;
    }
  }, [gameState.difficulty]);
  
  // Função para formatar tempo em mm:ss
  const formatTime = useCallback((timeInSeconds) => {
    if (!timeInSeconds || timeInSeconds < 0) return '00:00';
    
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);
  
  const initializeGame = useCallback(async (difficulty = gameState.difficulty) => {
    const cardSet = MEMORY_CARDS[difficulty];
    const themeConfig = THEME_CONFIG[difficulty];
    
    if (!cardSet || !themeConfig) {
      console.error('Conjunto de cartas ou tema não encontrado para a dificuldade:', difficulty);
      return;
    }
    
    const shuffledCards = shuffleArray([...cardSet]).map((card, index) => ({
      ...card,
      uniqueId: `${card.id}-${index}`,
      isFlipped: false,
      isMatched: false
    }));

    // Configurar tarefas especiais baseadas no tema
    const specialTask = getSpecialTaskForTheme(themeConfig, difficulty);

    // 🎯 Inicializar sistema de atividades V3
    const initialActivity = 'pair_matching';
    const activityData = {};
    
    // Inicializar dados para todas as atividades
    ['pair_matching', 'sequence_memory', 'spatial_location', 'image_reconstruction', 'number_sequence'].forEach(activity => {
      activityData[activity] = initializeActivityData(activity, difficulty);
    });

    // Configurar dados específicos para pair_matching
    activityData.pair_matching = {
      cards: shuffledCards,
      flippedCards: [],
      matchedCards: []
    };

    setGameState(prev => ({
      ...prev,
      status: themeConfig.hasSequenceTask ? 'showing_sequence' : 'playing',
      difficulty,
      theme: themeConfig,
      activityTimerStarted: false, // 🔥 Reset timer ao inicializar jogo
      
      // 🎯 Sistema de atividades V3
      currentActivity: initialActivity,
      activitiesCompleted: 0,
      rotationIndex: 0,
      activityAttempts: 0,
      activityData,
      
      // Compatibilidade com código existente
      cards: shuffledCards,
      flippedCards: [],
      matchedCards: [],
      attempts: 0,
      startTime: Date.now(),
      elapsedTime: 0,
      sequenceMemorized: false,
      showingSequence: themeConfig.hasSequenceTask || false,
      specialTaskActive: false,
      themeProgress: {
        groupsFormed: 0,
        requiredGroups: themeConfig.requiredGroups || 0,
        currentTask: specialTask
      }
    }));

    setShowStartScreen(false);

    // Iniciar métricas
    metricsRef.current.startGame({
      gameType: 'MemoryGame',
      difficulty,
      theme: themeConfig.name,
      cardsCount: shuffledCards.length,
      timestamp: Date.now()
    });
    
    // 🔄 Inicializar integração multissensorial
    try {
      await initMultisensory(sessionIdRef.current, {
        difficulty,
        theme: themeConfig.name,
        gameMode: 'memory_matching',
        cardsCount: shuffledCards.length,
        userId: user?.id || 'anonymous'
      });
      
      console.log('🔄 MemoryGame: Integração multissensorial inicializada');
    } catch (error) {
      console.warn('⚠️ MemoryGame: Erro ao inicializar integração multissensorial:', error);
    }
    
    // TTS temático ao iniciar
    setTimeout(() => {
      const welcomeMessage = `${themeConfig.narrative} Encontre os pares iguais para completar a aventura. Boa sorte!`;
      speak(welcomeMessage, { rate: 0.8 });
    }, 1000);

    // Se tem tarefa de sequência, mostrar cartas por 3 segundos
    if (themeConfig.hasSequenceTask) {
      setTimeout(() => {
        showSequencePreview(shuffledCards);
      }, 2000);
    }
  }, [gameState.difficulty, speak]);

  // Função para pular tela inicial (para testes)
  const skipStartScreen = useCallback(() => {
    setShowStartScreen(false);
    initializeGame('easy');
  }, [initializeGame]);

  // 🔥 Função para iniciar timer na primeira interação
  const startActivityTimer = useCallback(() => {
    if (!gameState.activityTimerStarted && gameState.status === 'playing') {
      setGameState(prev => ({
        ...prev,
        activityTimerStarted: true
      }));
    }
  }, [gameState.activityTimerStarted, gameState.status]);

  const handleCardClick = useCallback((cardUniqueId) => {
    // 🔥 Iniciar timer na primeira interação
    startActivityTimer();

    // Verificações de segurança
    if (gameState.status !== 'playing') return;

    const card = gameState.cards.find(c => c.uniqueId === cardUniqueId);
    console.log('🃏 Card clicked:', card?.name, 'isFlipped:', card?.isFlipped, 'isMatched:', card?.isMatched);

    if (!card || card.isFlipped || card.isMatched) return;

    // Prevenir cliques quando já há 2 cartas viradas
    if (gameState.flippedCards.length >= 2) return;

    const newFlippedCards = [...gameState.flippedCards, cardUniqueId];
    
    // Atualizar estado imediatamente
    setGameState(prev => ({
      ...prev,
      cards: prev.cards.map(c => 
        c.uniqueId === cardUniqueId ? { ...c, isFlipped: true } : c
      ),
      flippedCards: newFlippedCards,
      attempts: newFlippedCards.length === 2 ? prev.attempts + 1 : prev.attempts
    }));

    // Verificar par apenas quando 2 cartas estão viradas
    if (newFlippedCards.length === 2) {
      // Dar tempo para o usuário ver as cartas antes de verificar
      setTimeout(() => {
        setGameState(currentState => {
          const [firstId, secondId] = newFlippedCards;
          const firstCard = currentState.cards.find(c => c.uniqueId === firstId);
          const secondCard = currentState.cards.find(c => c.uniqueId === secondId);

          // Verificar se as cartas ainda existem (proteção adicional)
          if (!firstCard || !secondCard) {
            return { ...currentState, flippedCards: [] };
          }

          // Verificar se é um par (comparar por pairId para maior precisão)
          const isMatch = firstCard.pairId === secondCard.pairId;

          if (isMatch) {
            // Match encontrado
            const newMatchedCards = [...currentState.matchedCards, firstId, secondId];
            const updatedCards = currentState.cards.map(c => 
              c.uniqueId === firstId || c.uniqueId === secondId 
                ? { ...c, isMatched: true } 
                : c
            );
            
            // Feedback sonoro TTS temático para acerto
            const encouragementMessage = getThematicEncouragement(currentState.difficulty);
            speak(encouragementMessage, {
              rate: 1.0,
              pitch: 1.2
            });

            // Verificar tarefa especial completada
            const specialTaskCompleted = checkSpecialTaskCompletion(newMatchedCards, currentState.cards, currentState.theme);
            if (specialTaskCompleted) {
              const specialMessage = getThematicEncouragement(currentState.difficulty, true);
              setTimeout(() => speak(specialMessage, { rate: 0.9, pitch: 1.3 }), 1500);
            }
            
            // Verificar se o jogo foi completado
            if (newMatchedCards.length >= currentState.cards.length) {
              setTimeout(() => {
                setGameState(prev => ({ ...prev, status: 'completed' }));
                // Feedback sonoro TTS temático para conclusão do jogo
                const completionMessage = currentState.theme ? 
                  `Parabéns! Você completou ${currentState.theme.name}! ${currentState.theme.narrative}` :
                  'Parabéns! Você completou o jogo da memória com sucesso!';
                speak(completionMessage, {
                  rate: 0.9,
                  pitch: 1.3
                });
              }, 500);
            }
            
            // 🧠 Registrar tentativa de memória para análise cognitiva
            recordMemoryInteraction(firstCard, secondCard, true, Date.now() - currentState.startTime, currentState.attempts + 1);

            return {
              ...currentState,
              cards: updatedCards,
              matchedCards: newMatchedCards,
              flippedCards: [],
              score: currentState.score + 100
            };
          } else {
            // Não é um par - CORRIGIR: Desvirar as cartas
            // Feedback sonoro TTS para erro
            speak('Ops! As cartas não são iguais. Tente novamente!', {
              rate: 0.9,
              pitch: 0.8
            });
            
            // 🧠 Registrar tentativa incorreta para análise cognitiva
            recordMemoryInteraction(firstCard, secondCard, false, Date.now() - currentState.startTime, currentState.attempts + 1);
            
            // CORREÇÃO: Desvirar as cartas que não formaram par
            const updatedCards = currentState.cards.map(c => 
              c.uniqueId === firstId || c.uniqueId === secondId 
                ? { ...c, isFlipped: false }  // Voltar ao estado original
                : c
            );
            
            return {
              ...currentState,
              cards: updatedCards,
              flippedCards: []
            };
          }
        });
      }, 1000); // 1 segundo para o usuário ver as cartas
    }
  }, [gameState.status, gameState.cards, gameState.flippedCards, gameState.matchedCards, speak]);

  // 🎨 Funções auxiliares para tarefas temáticas especiais
  const getSpecialTaskForTheme = useCallback((themeConfig, difficulty) => {
    switch (difficulty) {
      case 'easy':
        return 'sequence_memorization';
      case 'medium':
        return 'speed_grouping';
      case 'hard':
        return 'constellation_formation';
      default:
        return null;
    }
  }, []);

  const showSequencePreview = useCallback((cards) => {
    // Mostrar todas as cartas por 3 segundos (tarefa de sequência)
    setGameState(prev => ({
      ...prev,
      cards: prev.cards.map(card => ({ ...card, isFlipped: true })),
      showingSequence: true
    }));
    
    speak("Memorize a posição das frutas! Em 3 segundos elas vão se esconder.", { rate: 0.9 });
    
    setTimeout(() => {
      setGameState(prev => ({
        ...prev,
        cards: prev.cards.map(card => ({ ...card, isFlipped: false })),
        showingSequence: false,
        sequenceMemorized: true,
        status: 'playing'
      }));
      speak("Agora encontre os pares de frutas!", { rate: 1.0 });
    }, 3000);
  }, [speak]);

  const checkSpecialTaskCompletion = useCallback((matchedCards, cards, theme) => {
    if (!theme) return false;

    switch (theme.name) {
      case 'Meios de Transporte':
        return checkSpeedGrouping(matchedCards, cards, theme);
      case 'Espaço e Estrelas':
        return checkConstellationFormation(matchedCards, cards, theme);
      case 'Jardim Encantado':
        return checkGardenFormation(matchedCards, cards, theme);
      default:
        return false;
    }
  }, []);

  const checkSpeedGrouping = useCallback((matchedCards, cards, theme) => {
    const matchedPairs = matchedCards.length / 2;
    const totalPairs = cards.length / 2;
    return matchedPairs >= totalPairs; // Completar todos os pares
  }, []);

  const checkConstellationFormation = useCallback((matchedCards, cards, theme) => {
    // Verificar se pelo menos uma constelação foi formada
    const starMatches = matchedCards.filter(cardId => {
      const card = cards.find(c => c.uniqueId === cardId);
      return card && (card.pairId === 'star' || card.pairId === 'bright_star');
    });
    return starMatches.length >= 2; // Pelo menos um par de estrelas
  }, []);

  const checkGardenFormation = useCallback((matchedCards, cards, theme) => {
    const flowerMatches = matchedCards.filter(cardId => {
      const card = cards.find(c => c.uniqueId === cardId);
      return card && card.garden_type === 'flower';
    });
    const insectMatches = matchedCards.filter(cardId => {
      const card = cards.find(c => c.uniqueId === cardId);
      return card && card.garden_type === 'insect';
    });
    
    // Pelo menos 2 pares de flores e 1 par de insetos
    return flowerMatches.length >= 4 && insectMatches.length >= 2;
  }, []);

  const getThematicEncouragement = useCallback((difficulty, isSpecialTask = false) => {
    const messages = ENCOURAGEMENT_MESSAGES[difficulty] || ENCOURAGEMENT_MESSAGES.general;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    if (isSpecialTask) {
      switch (difficulty) {
        case 'medium':
          return 'Ótimo! Você organizou o trânsito perfeitamente! 🚦';
        case 'hard':
          return 'Incrível! Você formou uma constelação linda! ⭐';
        case 'very_hard':
          return 'Fantástico! Seu jardim está encantado! 🌸';
        default:
          return randomMessage;
      }
    }
    
    return randomMessage;
  }, []);

  // 🎯 SISTEMA DE ROTAÇÃO DE ATIVIDADES V3
  const rotateToNextActivity = useCallback(() => {
    setGameState(prev => {
      const nextIndex = (prev.rotationIndex + 1) % prev.activitiesRotation.length;
      const nextActivity = prev.activitiesRotation[nextIndex];
      
      return {
        ...prev,
        currentActivity: nextActivity,
        rotationIndex: nextIndex,
        activitiesCompleted: prev.activitiesCompleted + 1,
        activityAttempts: 0,
        status: 'playing'
      };
    });
  }, []);

  const initializeActivityData = useCallback((activity, difficulty) => {
    switch (activity) {
      case 'pair_matching':
        return {
          cards: [],
          flippedCards: [],
          matchedCards: []
        };
      case 'sequence_memory':
        return {
          sequence: [],
          userSequence: [],
          showingSequence: false
        };
      case 'spatial_location':
        return {
          grid: [],
          targetPositions: [],
          userSelections: []
        };
      case 'image_reconstruction':
        return {
          pieces: [],
          targetImage: [],
          userArrangement: []
        };
      case 'number_sequence':
        return {
          numbers: [],
          userNumbers: [],
          showingNumbers: false
        };
      default:
        return {};
    }
  }, []);

  const handleActivityCompletion = useCallback(() => {
    // Registrar métricas da atividade atual
    metricsRef.current.recordActivityComplete({
      activity: gameState.currentActivity,
      attempts: gameState.activityAttempts,
      score: gameState.score,
      timeElapsed: gameState.elapsedTime
    });

    // Feedback de conclusão
    const activityNames = {
      pair_matching: 'Combinação de Pares',
      sequence_memory: 'Memória Sequencial',
      spatial_location: 'Localização Espacial',
      sound_patterns: 'Padrões Sonoros',
      image_reconstruction: 'Reconstrução de Imagem',
      number_sequence: 'Sequência Numérica'
    };

    speak(`Atividade ${activityNames[gameState.currentActivity]} concluída com sucesso!`);

    // Verificar se completou todas as atividades
    if (gameState.activitiesCompleted + 1 >= gameState.activitiesRotation.length) {
      // Jogo completo
      setGameState(prev => ({
        ...prev,
        status: 'completed',
        activitiesCompleted: prev.activitiesCompleted + 1
      }));
    } else {
      // Rotacionar para próxima atividade após 2 segundos
      setTimeout(() => {
        rotateToNextActivity();
      }, 2000);
    }
  }, [gameState.currentActivity, gameState.activityAttempts, gameState.score, gameState.elapsedTime, gameState.activitiesCompleted, gameState.activitiesRotation.length, speak, rotateToNextActivity]);

  // Função para trocar atividade manualmente
  const switchToActivity = useCallback((activityId) => {
    setGameState(prev => {
      const newState = { ...prev };
      newState.currentActivity = activityId;
      newState.rotationIndex = prev.activitiesRotation.indexOf(activityId);
      newState.activityTimerStarted = false; // 🔥 Reset timer ao trocar atividade
      
      // Inicializar dados da nova atividade se necessário
      if (activityId === 'pair_matching' && (!newState.cards || newState.cards.length === 0)) {
        // Inicializar cartas para pair_matching
        const cardSet = MEMORY_CARDS[newState.difficulty] || [];
        const shuffledCards = shuffleArray([...cardSet]).map((card, index) => ({
          ...card,
          uniqueId: `${card.id}-${index}`,
          isFlipped: false,
          isMatched: false
        }));
        
        newState.cards = shuffledCards;
        newState.flippedCards = [];
        newState.matchedCards = [];
        
        // Atualizar dados da atividade
        newState.activityData.pair_matching = {
          cards: shuffledCards,
          flippedCards: [],
          matchedCards: []
        };
      }
      
      return newState;
    });
    
    // Feedback sonoro
    const activityNames = {
      pair_matching: 'Combinação de Pares',
      sequence_memory: 'Memória Sequencial',
      spatial_location: 'Localização Espacial',
      image_reconstruction: 'Reconstrução de Imagem',
      number_sequence: 'Sequência Numérica'
    };
    
    speak(`Mudando para ${activityNames[activityId]}`);
  }, [speak, shuffleArray]);

  const renderActivityInterface = useCallback(() => {
    const activity = gameState.currentActivity;
    const activityData = gameState.activityData[activity];

    switch (activity) {
      case 'pair_matching':
        return renderPairMatchingActivity(activityData);
      case 'sequence_memory':
        return renderSequenceMemoryActivity(activityData);
      case 'spatial_location':
        return renderSpatialLocationActivity(activityData);
      case 'image_reconstruction':
        return renderImageReconstructionActivity(activityData);
      case 'number_sequence':
        return renderNumberSequenceActivity(activityData);
      default:
        return <div>Atividade não encontrada</div>;
    }
  }, [gameState.currentActivity, gameState.activityData]);

  // Renderizadores específicos para cada atividade
  const renderPairMatchingActivity = useCallback((data) => {
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>
            🧩 Encontre todos os pares de cartas iguais!
          </h2>
        </div>

        {/* Grid das cartas */}
        <div
          className={`${styles.memoryGrid} ${
            getGridCols() === 2 ? styles.grid2x2 :
            getGridCols() === 3 ? styles.grid3x3 :
            getGridCols() === 4 ? styles.grid4x4 :
            styles.grid5x5
          }`}
        >
          {gameState.cards.map((card) => {
          console.log('🃏 Rendering card:', card.name, 'isFlipped:', card.isFlipped, 'isMatched:', card.isMatched);
          return (
            <motion.div
              key={card.uniqueId}
              className={`
                ${styles.memoryCard}
                ${card.isFlipped ? styles.flipped : ''}
                ${card.isMatched ? styles.matched : ''}
                ${gameState.flippedCards.length >= 2 && !card.isFlipped ? styles.cardDisabled : ''}
              `}
              onClick={() => handleCardClick(card.uniqueId)}
              whileHover={{ 
                scale: !card.isMatched && !card.isFlipped ? 1.02 : 1,
                transition: { duration: 0.1 }
              }}
              whileTap={{ scale: 0.98 }}
            >
              <div className={styles.cardInner}>
                <div className={styles.cardBack}>
                  <span className={styles.cardBackIcon}>?</span>
                </div>
                <div className={styles.cardFront}>
                  <span className={styles.cardIcon} role="img" aria-label={card.name}>
                    {card.symbol}
                  </span>
                  <span className={styles.cardName}>{card.name}</span>
                </div>
              </div>
            </motion.div>
          );
        })}
        </div>
      </div>
    );
  }, [gameState.cards, gameState.flippedCards, getGridCols, handleCardClick]);

  const renderSequenceMemoryActivity = useCallback((data) => {
    const { sequence, userSequence, showingSequence } = data || {
      sequence: [],
      userSequence: [],
      showingSequence: false
    };

    // Inicializar sequência se estiver vazia
    if (!sequence || sequence.length === 0) {
      const difficultyMap = {
        'easy': 3,
        'medium': 5,
        'hard': 7
      };
      const sequenceLength = difficultyMap[gameState.difficulty] || 5;
      
      const colors = ['red', 'blue', 'green', 'yellow'];
      const newSequence = Array(sequenceLength).fill(null).map(() => 
        colors[Math.floor(Math.random() * colors.length)]
      );
      
      data.sequence = newSequence;
      data.showingSequence = true;
      
      // Mostrar sequência ao usuário
      let currentIndex = 0;
      const interval = setInterval(() => {
        if (currentIndex >= newSequence.length) {
          clearInterval(interval);
          setTimeout(() => {
            setGameState(prev => {
              const activityData = {
                ...prev.activityData,
                sequence_memory: {
                  ...prev.activityData.sequence_memory,
                  showingSequence: false,
                  highlightedIndex: -1
                }
              };
              return { ...prev, activityData };
            });
            speak("Agora repita a sequência que você viu.");
          }, 500);
          return;
        }
        
        // Destacar cor atual
        setGameState(prev => {
          const activityData = {
            ...prev.activityData,
            sequence_memory: {
              ...prev.activityData.sequence_memory,
              highlightedIndex: currentIndex
            }
          };
          return { ...prev, activityData };
        });
        
        // Som correspondente à cor
        const colorSounds = {
          red: 'high',
          blue: 'medium',
          green: 'low',
          yellow: 'success'
        };
        playSound(colorSounds[newSequence[currentIndex]] || 'tap');
        
        currentIndex++;
      }, 1000);
    }

    // Função para lidar com o clique nos botões coloridos
    const handleColorClick = (color) => {
      // 🔥 Iniciar timer na primeira interação
      startActivityTimer();
      
      // Ignorar cliques durante a demonstração da sequência
      if (data.showingSequence) return;
      
      const newUserSequence = [...userSequence, color];
      
      // Verificar se o usuário clicou na cor correta para esta posição
      const isCorrect = color === sequence[userSequence.length];
      
      // Feedback sonoro
      const colorSounds = {
        red: 'high',
        blue: 'medium',
        green: 'low',
        yellow: 'success'
      };
      playSound(isCorrect ? colorSounds[color] : 'error');
      
      // Atualizar a sequência do usuário
      setGameState(prev => {
        const activityData = {
          ...prev.activityData,
          sequence_memory: {
            ...prev.activityData.sequence_memory,
            userSequence: newUserSequence
          }
        };
        return { ...prev, activityData };
      });
      
      // Se errou, mostrar sequência correta novamente após 1 segundo
      if (!isCorrect) {
        setTimeout(() => {
          speak("Ops! Vamos tentar novamente. Observe a sequência.");
          setGameState(prev => {
            const activityData = {
              ...prev.activityData,
              sequence_memory: {
                ...prev.activityData.sequence_memory,
                userSequence: [],
                showingSequence: true
              }
            };
            return { ...prev, activityData };
          });
          
          // Mostrar sequência
          let currentIndex = 0;
          const interval = setInterval(() => {
            if (currentIndex >= sequence.length) {
              clearInterval(interval);
              setTimeout(() => {
                setGameState(prev => {
                  const activityData = {
                    ...prev.activityData,
                    sequence_memory: {
                      ...prev.activityData.sequence_memory,
                      showingSequence: false,
                      highlightedIndex: -1
                    }
                  };
                  return { ...prev, activityData };
                });
              }, 500);
              return;
            }
            
            // Destacar cor atual
            setGameState(prev => {
              const activityData = {
                ...prev.activityData,
                sequence_memory: {
                  ...prev.activityData.sequence_memory,
                  highlightedIndex: currentIndex
                }
              };
              return { ...prev, activityData };
            });
            
            playSound(colorSounds[sequence[currentIndex]] || 'tap');
            currentIndex++;
          }, 1000);
        }, 1000);
        return;
      }
      
      // Se completou a sequência corretamente
      if (newUserSequence.length === sequence.length) {
        // Calcular pontuação
        const score = 100;
        
        // Atualizar pontuação e incrementar tentativas
        setGameState(prev => ({
          ...prev,
          score: prev.score + score,
          activityAttempts: prev.activityAttempts + 1
        }));
        
        // Feedback de sucesso
        speak("Parabéns! Você tem uma excelente memória sequencial!");
        
        // Limpar para próxima rodada após 2 segundos
        setTimeout(() => {
          setGameState(prev => {
            const activityData = {
              ...prev.activityData,
              sequence_memory: {
                sequence: [],
                userSequence: [],
                showingSequence: false
              }
            };
            return { ...prev, activityData };
          });
        }, 2000);
      }
    };
    
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>
            🧠 Memorize e repita a sequência!
          </h2>
          <p className={styles.instructions}>
            {data.showingSequence 
              ? "Observe a sequência de cores!" 
              : "Clique nas cores na mesma sequência que foram mostradas."}
          </p>
        </div>
        
        <div className={styles.sequenceContainer}>
          {/* Indicador da sequência atual sendo mostrada */}
          {data.showingSequence && (
            <div style={{ 
              fontSize: '1.2rem', 
              marginBottom: '1rem', 
              color: '#ffeb3b',
              fontWeight: 'bold' 
            }}>
              Sequência sendo mostrada... ({(data.highlightedIndex || 0) + 1}/{sequence.length})
            </div>
          )}
          
          {/* Botões de cores para interação */}
          <div className={styles.colorButtons}>
            {['red', 'blue', 'green', 'yellow'].map((color) => (
              <motion.div
                key={`color-${color}`}
                className={`${styles.colorButton} ${styles[color]}`}
                animate={{ 
                  scale: (data.highlightedIndex >= 0 && data.sequence && 
                          data.sequence[data.highlightedIndex] === color) ? 1.3 : 1,
                  boxShadow: (data.highlightedIndex >= 0 && data.sequence && 
                            data.sequence[data.highlightedIndex] === color) ? 
                            '0 0 30px rgba(255, 255, 255, 0.8)' : '0 4px 15px rgba(0, 0, 0, 0.2)'
                }}
                onClick={() => handleColorClick(color)}
                whileHover={{ scale: data.showingSequence ? 1 : 1.05 }}
                whileTap={{ scale: data.showingSequence ? 1 : 0.95 }}
                style={{
                  opacity: data.showingSequence ? 0.7 : 1,
                  cursor: data.showingSequence ? 'default' : 'pointer'
                }}
              />
            ))}
          </div>
          
          {/* Feedback de progresso */}
          {!data.showingSequence && sequence && sequence.length > 0 && (
            <div className={styles.sequenceFeedback}>
              <p style={{ fontSize: '1.1rem', marginBottom: '1rem' }}>
                Progressão: {userSequence.length} / {sequence.length}
              </p>
              <div className={styles.sequenceProgress}>
                {sequence.map((_, index) => (
                  <div 
                    key={`progress-${index}`}
                    className={`${styles.sequenceStep} ${index < userSequence.length ? styles.completed : ''}`}
                  />
                ))}
              </div>
            </div>
          )}
          
          {/* Mostrar a sequência alvo de forma visual (apenas quando não estiver mostrando) */}
          {!data.showingSequence && sequence && sequence.length > 0 && (
            <div style={{ marginTop: '2rem' }}>
              <p style={{ fontSize: '0.9rem', color: 'rgba(255, 255, 255, 0.7)', marginBottom: '1rem' }}>
                Sequência para repetir:
              </p>
              <div style={{ 
                display: 'flex', 
                gap: '0.5rem', 
                justifyContent: 'center',
                flexWrap: 'wrap'
              }}>
                {sequence.map((color, index) => (
                  <div
                    key={`sequence-${index}`}
                    style={{
                      width: '30px',
                      height: '30px',
                      borderRadius: '50%',
                      backgroundColor: color,
                      border: '2px solid rgba(255, 255, 255, 0.3)',
                      opacity: index < userSequence.length ? 1 : 0.4,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '0.8rem',
                      fontWeight: 'bold'
                    }}
                  >
                    {index + 1}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }, [gameState.difficulty, speak, playSound]);

  const renderSpatialLocationActivity = useCallback((data) => {
    const { grid, targetPositions, userSelections } = data || {
      grid: Array(16).fill(null).map((_, i) => ({ id: i, revealed: false })),
      targetPositions: [],
      userSelections: []
    };
    
    // Inicializar grid se estiver vazio
    if (!grid || grid.length === 0) {
      data.grid = Array(16).fill(null).map((_, i) => ({ id: i, revealed: false }));
    }
    
    // Inicializar alvos se não houver
    if (!targetPositions || targetPositions.length === 0) {
      // Gera 5 posições aleatórias para nível médio
      const difficultyMap = {
        'easy': 3,
        'medium': 5,
        'hard': 7
      };
      const positionsCount = difficultyMap[gameState.difficulty] || 5;
      
      const newTargets = [];
      while (newTargets.length < positionsCount) {
        const position = Math.floor(Math.random() * 16);
        if (!newTargets.includes(position)) {
          newTargets.push(position);
        }
      }
      data.targetPositions = newTargets;
      
      // Mostrar por 3 segundos e depois esconder
      data.showingPositions = true;
      setTimeout(() => {
        setGameState(prev => {
          const activityData = {
            ...prev.activityData,
            spatial_location: {
              ...prev.activityData.spatial_location,
              showingPositions: false
            }
          };
          return { ...prev, activityData };
        });
        
        // Instrução por áudio
        speak("Agora clique nas posições que você viu destacadas.");
      }, 3000);
    }
    
    const handleCellClick = (position) => {
      // 🔥 Iniciar timer na primeira interação
      startActivityTimer();
      
      // Ignorar se já estiver selecionada ou se estiver mostrando posições
      if (data.showingPositions || userSelections.includes(position)) {
        return;
      }
      
      // Adicionar à seleção do usuário
      const newSelections = [...userSelections, position];
      
      // Atualizar estado
      setGameState(prev => {
        const activityData = {
          ...prev.activityData,
          spatial_location: {
            ...prev.activityData.spatial_location,
            userSelections: newSelections
          }
        };
        return { ...prev, activityData };
      });
      
      // Feedback sonoro
      const isCorrectPosition = targetPositions.includes(position);
      playSound(isCorrectPosition ? 'correct' : 'incorrect');
      
      // Verificar se completou
      if (newSelections.length === targetPositions.length) {
        // Calculando pontuação: número de acertos
        const correctSelections = newSelections.filter(pos => targetPositions.includes(pos));
        const score = Math.round((correctSelections.length / targetPositions.length) * 100);
        
        // Atualizar pontuação e incrementar tentativas
        setGameState(prev => ({
          ...prev,
          score: prev.score + score,
          activityAttempts: prev.activityAttempts + 1
        }));
        
        // Feedback baseado na performance
        if (score >= 80) {
          speak("Muito bem! Você tem uma excelente memória espacial!");
        } else if (score >= 50) {
          speak("Bom trabalho! Continue praticando para melhorar.");
        } else {
          speak("Vamos tentar novamente? A prática leva à perfeição.");
        }
        
        // Limpar para próxima rodada após 2 segundos
        setTimeout(() => {
          setGameState(prev => {
            const activityData = {
              ...prev.activityData,
              spatial_location: {
                grid: Array(16).fill(null).map((_, i) => ({ id: i, revealed: false })),
                targetPositions: [],
                userSelections: []
              }
            };
            return { ...prev, activityData };
          });
        }, 2000);
      }
    };
    
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>
            📍 Lembre-se das posições espaciais!
          </h2>
          <p className={styles.instructions}>
            {data.showingPositions 
              ? "Memorize as posições destacadas!" 
              : "Clique nas posições que você viu destacadas."}
          </p>
        </div>
        
        <div className={styles.spatialContainer}>
          {/* Contador de tempo durante demonstração */}
          {data.showingPositions && (
            <div style={{ 
              fontSize: '1.2rem', 
              marginBottom: '1rem', 
              color: '#ffeb3b',
              fontWeight: 'bold',
              textAlign: 'center'
            }}>
              Observe as posições destacadas! ({targetPositions.length} posições)
            </div>
          )}
          
          {/* Instruções específicas */}
          {!data.showingPositions && targetPositions.length > 0 && (
            <div style={{ 
              fontSize: '1rem', 
              marginBottom: '1rem', 
              color: 'rgba(255, 255, 255, 0.8)',
              textAlign: 'center'
            }}>
              Clique nas {targetPositions.length} posições que foram destacadas
            </div>
          )}
          
          {/* Grid espacial 4x4 */}
          <div className={styles.spatialGrid}>
            {Array(16).fill(null).map((_, position) => (
              <motion.div
                key={`cell-${position}`}
                className={`${styles.spatialCell} 
                  ${(data.showingPositions && targetPositions.includes(position)) ? styles.highlighted : ''}
                  ${(!data.showingPositions && userSelections.includes(position)) ? styles.selected : ''}
                  ${(!data.showingPositions && userSelections.includes(position) && targetPositions.includes(position)) 
                    ? styles.correct : ''}
                  ${(!data.showingPositions && userSelections.includes(position) && !targetPositions.includes(position)) 
                    ? styles.incorrect : ''}`
                }
                onClick={() => handleCellClick(position)}
                whileHover={{ 
                  scale: data.showingPositions ? 1 : 1.05,
                  transition: { duration: 0.1 }
                }}
                whileTap={{ 
                  scale: data.showingPositions ? 1 : 0.95,
                  transition: { duration: 0.1 }
                }}
                style={{
                  cursor: data.showingPositions ? 'default' : 'pointer',
                  opacity: data.showingPositions ? 0.8 : 1
                }}
              >
                {/* Número da posição para facilitar identificação */}
                <span style={{
                  fontSize: '0.8rem',
                  color: 'rgba(255, 255, 255, 0.5)',
                  position: 'absolute',
                  top: '4px',
                  left: '6px'
                }}>
                  {position + 1}
                </span>
                
                {/* Indicador visual quando posição foi selecionada */}
                {userSelections.includes(position) && !data.showingPositions && (
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    background: targetPositions.includes(position) ? '#4caf50' : '#f44336',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 'bold',
                    fontSize: '0.8rem'
                  }}>
                    {targetPositions.includes(position) ? '✓' : '✗'}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
          
          {/* Feedback de progresso */}
          {userSelections.length > 0 && !data.showingPositions && (
            <div className={styles.feedback}>
              <p style={{ fontSize: '1.1rem', marginBottom: '0.5rem' }}>
                Seleções: {userSelections.length} / {targetPositions.length}
              </p>
              
              {/* Mostrar acertos vs erros */}
              {userSelections.length > 0 && (
                <div style={{ 
                  display: 'flex', 
                  gap: '2rem', 
                  justifyContent: 'center',
                  marginTop: '1rem'
                }}>
                  <div style={{ color: '#4caf50' }}>
                    ✓ Acertos: {userSelections.filter(pos => targetPositions.includes(pos)).length}
                  </div>
                  <div style={{ color: '#f44336' }}>
                    ✗ Erros: {userSelections.filter(pos => !targetPositions.includes(pos)).length}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* Legenda para ajudar o usuário */}
          {!data.showingPositions && userSelections.length === 0 && targetPositions.length > 0 && (
            <div style={{
              marginTop: '2rem',
              padding: '1rem',
              background: 'rgba(0, 0, 0, 0.3)',
              borderRadius: '8px',
              fontSize: '0.9rem',
              color: 'rgba(255, 255, 255, 0.8)',
              textAlign: 'center'
            }}>
              💡 Dica: As células estão numeradas de 1 a 16. Clique nas que foram destacadas em amarelo!
            </div>
          )}
        </div>
      </div>
    );
  }, [gameState.difficulty, speak, playSound]);

  const renderImageReconstructionActivity = useCallback((data) => {
    const { pieces, targetImage, userArrangement, completed } = data || {
      pieces: [],
      targetImage: [],
      userArrangement: [],
      completed: false
    };
    
    // Inicializar peças se estiver vazio
    if (!pieces || pieces.length === 0) {
      // Importar temas de sequência numérica do arquivo de configuração
      const imageOptions = sequenceThemes;
      
      // Selecionar um tema aleatório
      const selectedTheme = imageOptions[Math.floor(Math.random() * imageOptions.length)];
      
      // Determinar tamanho do grid baseado na dificuldade
      const difficultyMap = {
        'easy': 4,  // 2x2 grid
        'medium': 9, // 3x3 grid
        'hard': 16   // 4x4 grid
      };
      const gridSize = difficultyMap[gameState.difficulty] || 9;
      
      // Selecionar emojis aleatórios do tema
      const shuffledEmojis = shuffleArray([...selectedTheme.emojis]);
      const selectedEmojis = shuffledEmojis.slice(0, gridSize);
      
      // Criar peças para o quebra-cabeça
      const imagePieces = selectedEmojis.map((emoji, index) => ({
        id: `piece-${index}`,
        emoji,
        position: index
      }));
      
      // Criar cópia para imagem alvo
      const targetImage = [...imagePieces];
      
      // Embaralhar as peças
      const shuffledPieces = shuffleArray([...imagePieces]);
      
      // Atualizar o estado
      setGameState(prev => {
        const activityData = {
          ...prev.activityData,
          image_reconstruction: {
            pieces: shuffledPieces,
            targetImage: targetImage,
            userArrangement: [],
            themeName: selectedTheme.name,
            showPreview: true
          }
        };
        return { ...prev, activityData };
      });
      
      // Mostrar a imagem original por 5 segundos e depois esconder
      speak(`Observe este padrão de ${selectedTheme.name}. Você terá que reconstruí-lo em seguida.`);
      setTimeout(() => {
        setGameState(prev => {
          const activityData = {
            ...prev.activityData,
            image_reconstruction: {
              ...prev.activityData.image_reconstruction,
              showPreview: false
            }
          };
          return { ...prev, activityData };
        });
        speak("Agora arraste os emojis para reconstruir o padrão que você viu.");
      }, 5000);
    }
    
    // Função para lidar com o arrasto e soltura de peças
    const handlePieceDrag = (pieceId, targetIndex) => {
      // 🔥 Iniciar timer na primeira interação
      startActivityTimer();
      
      // Ignorar se a atividade foi completada
      if (data.completed) return;
      
      // Encontrar a peça arrastada
      const draggedPiece = pieces.find(p => p.id === pieceId);
      
      if (!draggedPiece) return;
      
      // Verificar se a posição já está ocupada
      const positionOccupied = userArrangement.some(item => item.position === targetIndex);
      if (positionOccupied) return;
      
      // Adicionar à disposição do usuário
      const newArrangement = [
        ...userArrangement.filter(item => item.piece.id !== pieceId),
        { piece: draggedPiece, position: targetIndex }
      ];
      
      // Reproduzir som de clique
      playSound('click');
      
      // Atualizar estado
      setGameState(prev => {
        const activityData = {
          ...prev.activityData,
          image_reconstruction: {
            ...prev.activityData.image_reconstruction,
            userArrangement: newArrangement
          }
        };
        return { ...prev, activityData };
      });
      
      // Verificar se todas as peças foram posicionadas
      if (newArrangement.length === pieces.length) {
        // Verificar quantas peças estão na posição correta
        let correctPieces = 0;
        newArrangement.forEach(item => {
          if (item.piece.id === targetImage[item.position].id) {
            correctPieces++;
          }
        });
        
        // Calcular pontuação - percentual de peças corretas
        const score = Math.round((correctPieces / pieces.length) * 100);
        
        // Atualizar estado marcando como completado
        setGameState(prev => {
          const activityData = {
            ...prev.activityData,
            image_reconstruction: {
              ...prev.activityData.image_reconstruction,
              completed: true,
              score
            }
          };
          return { 
            ...prev, 
            activityData,
            score: prev.score + score,
            activityAttempts: prev.activityAttempts + 1
          };
        });
        
        // Feedback sonoro
        playSound(score > 70 ? 'success' : 'complete');
        
        // Feedback por voz
        if (score > 90) {
          speak("Excelente! Você reconstruiu a imagem perfeitamente!");
        } else if (score > 70) {
          speak("Muito bom! Você reconstruiu grande parte da imagem corretamente.");
        } else if (score > 50) {
          speak("Bom trabalho! Você conseguiu reconstruir parte da imagem.");
        } else {
          speak("Continue praticando. A reconstrução de imagens melhora sua memória visual.");
        }
        
        // Reiniciar após 3 segundos
        setTimeout(() => {
          setGameState(prev => {
            const activityData = {
              ...prev.activityData,
              image_reconstruction: {
                pieces: [],
                targetImage: [],
                userArrangement: [],
                completed: false
              }
            };
            return { ...prev, activityData };
          });
        }, 3000);
      }
    };
    
    // Função para remover uma peça da grade
    const handleRemovePiece = (pieceId) => {
      // Atualizar estado removendo a peça
      setGameState(prev => {
        const activityData = {
          ...prev.activityData,
          image_reconstruction: {
            ...prev.activityData.image_reconstruction,
            userArrangement: prev.activityData.image_reconstruction.userArrangement.filter(
              item => item.piece.id !== pieceId
            )
          }
        };
        return { ...prev, activityData };
      });
      
      // Reproduzir som
      playSound('pop');
    };      return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>
            🖼️ Reconstrua a imagem original!
          </h2>
          <p className={styles.instructions}>
            {data.showPreview 
              ? "Observe este padrão com atenção!" 
              : data.completed 
                ? "Atividade completada!" 
                : "Toque nas peças para reconstruir a imagem."}
          </p>
        </div>
        <div className={styles.imageContainer}>
          {data.showPreview ? (
            <div className={styles.imagePreview}>
              <div className={styles.emojiPreviewGrid}
                style={{ 
                  gridTemplateColumns: `repeat(${Math.sqrt(targetImage.length)}, 1fr)`
                }}
              >
                {targetImage.map((piece, index) => (
                  <div key={`preview-${index}`} className={styles.previewItem}>
                    <span className={styles.previewEmoji}>{piece.emoji}</span>
                  </div>
                ))}
              </div>
              <p style={{ margin: '1rem 0', fontSize: '1rem', opacity: 0.9 }}>
                Memorize o padrão de {data.themeName}...
              </p>
            </div>
          ) : (
            <>
              {/* Grade para reconstrução - Mobile First */}
              <div 
                className={styles.reconstructionGrid}
                style={{ 
                  gridTemplateColumns: `repeat(${Math.sqrt(pieces.length)}, 1fr)`,
                  pointerEvents: data.completed ? 'none' : 'auto'
                }}
              >
                {Array(pieces.length).fill(null).map((_, index) => {
                  const arrangedPiece = userArrangement.find(item => item.position === index);
                  return (
                    <div 
                      key={`grid-${index}`} 
                      className={styles.dropZone}
                      onClick={() => arrangedPiece && handleRemovePiece(arrangedPiece.piece.id)}
                      role="button"
                      tabIndex={0}
                      aria-label={arrangedPiece ? `Posição ocupada por ${arrangedPiece.piece.emoji}. Toque para remover.` : `Posição ${index + 1} vazia. Disponível para colocar peça.`}
                    >
                      {arrangedPiece && (
                        <motion.div 
                          className={styles.puzzlePiece}
                          initial={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          transition={{ duration: 0.3, ease: "backOut" }}
                        >
                          <span className={styles.emojiPiece}>
                            {arrangedPiece.piece.emoji}
                          </span>
                        </motion.div>
                      )}
                    </div>
                  );
                })}
              </div>
              
              {/* Peças disponíveis - Mobile First */}
              <div className={styles.availablePieces}>
                {pieces.map((piece, index) => {
                  // Verificar se a peça já está na grade
                  const isPlaced = userArrangement.some(item => item.piece.id === piece.id);
                  if (isPlaced) return null;
                  
                  return (
                    <motion.div
                      key={piece.id}
                      className={styles.pieceContainer}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div 
                        className={styles.availablePiece}
                        onClick={() => {
                          // Encontrar primeira posição vazia
                          const occupiedPositions = userArrangement.map(item => item.position);
                          const availablePositions = Array(pieces.length)
                            .fill(null)
                            .map((_, i) => i)
                            .filter(pos => !occupiedPositions.includes(pos));
                            
                          if (availablePositions.length > 0) {
                            handlePieceDrag(piece.id, availablePositions[0]);
                          }
                        }}
                        role="button"
                        tabIndex={0}
                        aria-label={`Peça ${piece.emoji}. Toque para colocar na próxima posição disponível.`}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            const occupiedPositions = userArrangement.map(item => item.position);
                            const availablePositions = Array(pieces.length)
                              .fill(null)
                              .map((_, i) => i)
                              .filter(pos => !occupiedPositions.includes(pos));
                              
                            if (availablePositions.length > 0) {
                              handlePieceDrag(piece.id, availablePositions[0]);
                            }
                          }
                        }}
                      >
                        <span className={styles.emojiPiece}>
                          {piece.emoji}
                        </span>
                      </div>
                    </motion.div>
                  );
                })}
              </div>
              
              {/* Feedback de progresso */}
              {userArrangement.length > 0 && !data.completed && (
                <div className={styles.feedback}>
                  <p>Progresso: {userArrangement.length}/{pieces.length} peças posicionadas</p>
                </div>
              )}
              
              {/* Pontuação final */}
              {data.completed && data.score !== undefined && (
                <div className={styles.feedback}>
                  <p>🎯 Pontuação: {data.score}%</p>
                  {data.score > 90 && <p>🏆 Perfeito!</p>}
                  {data.score > 70 && data.score <= 90 && <p>⭐ Muito bom!</p>}
                  {data.score > 50 && data.score <= 70 && <p>👍 Bom trabalho!</p>}
                  {data.score <= 50 && <p>💪 Continue praticando!</p>}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    );
  }, [gameState.difficulty, speak, playSound, shuffleArray]);

  const renderNumberSequenceActivity = useCallback((data) => {
    const { numbers = [], userNumbers = [], showingNumbers = false, completed = false } = data || {};
    
    // Inicializar a sequência numérica se ainda não foi criada
    if (numbers.length === 0 && !completed) {
      // Determinar nível de dificuldade
      const difficultyMap = {
        'easy': { length: 5, maxNumber: 9 },
        'medium': { length: 7, maxNumber: 20 },
        'hard': { length: 9, maxNumber: 50 }
      };
      
      const config = difficultyMap[gameState.difficulty] || difficultyMap.medium;
      
      // Gerar sequência numérica baseada em um padrão
      const generateSequence = () => {
        // Escolher aleatoriamente um tipo de sequência
        const sequenceTypes = ['arithmetic', 'geometric', 'fibonacci', 'alternate'];
        const sequenceType = sequenceTypes[Math.floor(Math.random() * sequenceTypes.length)];
        
        let sequence = [];
        let pattern = '';
        
        switch (sequenceType) {
          case 'arithmetic': {
            // Progressão aritmética: a, a+d, a+2d, ...
            const start = Math.floor(Math.random() * 10) + 1; // Número inicial entre 1 e 10
            const diff = Math.floor(Math.random() * 5) + 1;   // Diferença entre 1 e 5
            
            for (let i = 0; i < config.length; i++) {
              sequence.push(start + i * diff);
            }
            pattern = `+${diff} (Progressão Aritmética)`;
            break;
          }
          
          case 'geometric': {
            // Progressão geométrica: a, a*r, a*r^2, ...
            const start = Math.floor(Math.random() * 5) + 1; // Número inicial entre 1 e 5
            const ratio = Math.floor(Math.random() * 2) + 2; // Razão entre 2 e 3
            
            let current = start;
            for (let i = 0; i < config.length; i++) {
              sequence.push(current);
              current *= ratio;
            }
            pattern = `x${ratio} (Progressão Geométrica)`;
            break;
          }
          
          case 'fibonacci': {
            // Variação de Fibonacci: a, b, a+b, a+2b, 2a+3b, ...
            const a = Math.floor(Math.random() * 5) + 1;
            const b = Math.floor(Math.random() * 5) + 1;
            
            sequence.push(a, b);
            for (let i = 2; i < config.length; i++) {
              sequence.push(sequence[i-1] + sequence[i-2]);
            }
            pattern = `Fibonacci (cada número é a soma dos dois anteriores)`;
            break;
          }
          
          default: {
            // Alternância: +a, -b, +a, -b, ...
            const addValue = Math.floor(Math.random() * 5) + 1;
            const subtractValue = Math.floor(Math.random() * 3) + 1;
            
            let current = Math.floor(Math.random() * 10) + 1;
            for (let i = 0; i < config.length; i++) {
              sequence.push(current);
              if (i % 2 === 0) {
                current += addValue;
              } else {
                current -= subtractValue;
              }
            }
            pattern = `Alternado (+${addValue}, -${subtractValue})`;
          }
        }
        
        return { sequence, pattern };
      };
      
      const { sequence, pattern } = generateSequence();
      
      // Remover último número para o usuário completar
      const visibleSequence = [...sequence];
      const missingNumber = visibleSequence.pop();
      
      // Atualizar o estado
      setGameState(prev => {
        const activityData = {
          ...prev.activityData,
          number_sequence: {
            numbers: visibleSequence,
            fullSequence: sequence,
            missingNumber,
            pattern,
            userAnswer: '',
            showingNumbers: true,
            answered: false
          }
        };
        return { ...prev, activityData };
      });
      
      // Dar instruções por voz
      speak("Observe esta sequência numérica e tente descobrir qual é o próximo número.");
    }
    
    // Função para lidar com a submissão da resposta
    const handleSubmit = () => {
      // 🔥 Iniciar timer na primeira interação
      startActivityTimer();
      
      const userAnswer = parseInt(data.userAnswer);
      const isCorrect = userAnswer === data.missingNumber;
      
      // Atualizar estado com resultado
      setGameState(prev => {
        const activityData = {
          ...prev.activityData,
          number_sequence: {
            ...prev.activityData.number_sequence,
            answered: true,
            isCorrect
          }
        };
        return { 
          ...prev, 
          activityData,
          score: prev.score + (isCorrect ? 100 : 0),
          activityAttempts: prev.activityAttempts + 1
        };
      });
      
      // Feedback sonoro e por voz
      if (isCorrect) {
        playSound('success');
        speak("Correto! Muito bem!");
      } else {
        playSound('error');
        speak(`Incorreto. O número correto era ${data.missingNumber}.`);
      }
      
      // Preparar para próxima rodada
      setTimeout(() => {
        setGameState(prev => {
          const activityData = {
            ...prev.activityData,
            number_sequence: {
              numbers: [],
              userNumbers: [],
              showingNumbers: false
            }
          };
          return { ...prev, activityData };
        });
      }, 3000);
    };
    
    // Função para atualizar a resposta do usuário
    const updateUserAnswer = (value) => {
      setGameState(prev => {
        const activityData = {
          ...prev.activityData,
          number_sequence: {
            ...prev.activityData.number_sequence,
            userAnswer: value
          }
        };
        return { ...prev, activityData };
      });
    };
    
    return (
      <div className={styles.questionArea}>
        <div className={styles.questionHeader}>
          <h2 className={styles.questionTitle}>
            🔢 Complete a sequência numérica!
          </h2>
          <p className={styles.instructions}>
            {data.answered 
              ? data.isCorrect 
                ? "✅ Correto! Muito bem!" 
                : `❌ Incorreto. O número correto era ${data.missingNumber}.`
              : "Qual é o próximo número desta sequência?"}
          </p>
        </div>
        
        <div className={styles.numberSequenceContainer}>
          {/* Dica para o usuário */}
          {!data.answered && data.numbers && data.numbers.length > 0 && (
            <div style={{ 
              fontSize: '1rem', 
              color: 'rgba(255, 255, 255, 0.8)',
              marginBottom: '1rem',
              textAlign: 'center'
            }}>
              💡 Observe o padrão e descubra o próximo número
            </div>
          )}
          
          {/* Exibir a sequência numérica */}
          <div className={styles.numberRow}>
            {data.numbers && data.numbers.map((num, index) => (
              <div key={`num-${index}`} className={styles.numberCard}>
                {num}
              </div>
            ))}
            <div className={`${styles.numberCard} ${styles.missingNumber}`}>
              {data.answered ? data.missingNumber : '?'}
            </div>
          </div>
          
          {/* Campo de entrada para a resposta do usuário */}
          {!data.answered && (
            <div className={styles.answerContainer}>
              <input
                type="number"
                value={data.userAnswer || ''}
                onChange={(e) => updateUserAnswer(e.target.value)}
                className={styles.numberInput}
                placeholder="?"
                min="-999"
                max="9999"
                inputMode="numeric"
                pattern="[0-9]*"
                autoComplete="off"
                onFocus={(e) => e.target.select()}
              />
              <button
                className={styles.submitButton}
                onClick={handleSubmit}
                disabled={!data.userAnswer || data.userAnswer.trim() === ''}
              >
                ✓ Verificar
              </button>
            </div>
          )}
          
          {/* Informação sobre o padrão (mostrado apenas após a resposta) */}
          {data.answered && (
            <div className={styles.patternInfo}>
              <p>
                <strong>Padrão descoberto:</strong><br />
                {data.pattern}
              </p>
              {data.isCorrect && (
                <p style={{ color: '#4caf50', marginTop: '0.5rem', fontSize: '1.1rem' }}>
                  🎉 Excelente raciocínio lógico!
                </p>
              )}
            </div>
          )}
          
          {/* Feedback visual durante entrada */}
          {!data.answered && data.userAnswer && (
            <div style={{
              marginTop: '1rem',
              padding: '0.5rem',
              background: 'rgba(33, 150, 243, 0.1)',
              border: '1px solid rgba(33, 150, 243, 0.3)',
              borderRadius: '8px',
              color: 'white',
              fontSize: '0.9rem'
            }}>
              Sua resposta: <strong>{data.userAnswer}</strong>
            </div>
          )}
          
          {/* Dica adicional para novos usuários */}
          {!data.answered && data.numbers && data.numbers.length > 0 && !data.userAnswer && (
            <div style={{
              marginTop: '2rem',
              padding: '1rem',
              background: 'rgba(255, 193, 7, 0.1)',
              border: '1px solid rgba(255, 193, 7, 0.3)',
              borderRadius: '12px',
              color: 'rgba(255, 255, 255, 0.9)',
              fontSize: '0.9rem',
              lineHeight: '1.4'
            }}>
              <strong>💡 Dicas:</strong><br />
              • Observe as diferenças entre os números<br />
              • Pode ser soma, multiplicação ou outro padrão<br />
              • Use o teclado numérico para responder
            </div>
          )}
        </div>
      </div>
    );
  }, [gameState.difficulty, speak, playSound]);
  // Tela inicial profissional com seleção de dificuldade
  if (showStartScreen) {
    return (
      <GameStartScreen
        gameTitle="Jogo da Memória"
        gameDescription="Desenvolva sua memória visual de forma divertida"
        gameIcon="🧠"
        difficulties={[
          { id: 'easy', name: 'Fácil', description: '8 cartas (4 pares)', icon: '🟢' },
          { id: 'medium', name: 'Médio', description: '12 cartas (6 pares)', icon: '🟡' },
          {
            id: 'hard', name: 'Difícil', description: '16 cartas (8 pares)', icon: '🔴',
            preview: '���💎⭐✨🔥⚡'
          }
        ]}
        onStart={(difficulty) => initializeGame(difficulty)}
        onBack={onBack}
      />
    );
  }

  // Jogo principal
  return (
    <div className={styles.memoryGame}>
      <div className={styles.gameContent}>
        {/* Header do jogo - padrão LetterRecognition */}
        <div className={styles.gameHeader}>
          <h1 className={styles.gameTitle}>
            🧠 Jogo da Memória V3
            <div style={{ fontSize: '0.7rem', opacity: 0.8, marginTop: '0.25rem' }}>
              Nível {gameState.difficulty}
            </div>
          </h1>
          <button
            className={`${styles.headerTtsButton} ${ttsActive ? styles.ttsActive : ''}`}
            onClick={toggleTTS}
            title={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
            aria-label={ttsActive ? 'Desativar TTS' : 'Ativar TTS'}
          >
            {ttsActive ? '🔊' : '🔇'}
          </button>
        </div>

        {/* Header com estatísticas - padrão LetterRecognition */}
        <div className={styles.gameStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.score}</div>
            <div className={styles.statLabel}>Pontos</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.moves}</div>
            <div className={styles.statLabel}>Jogadas</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{gameState.matches}</div>
            <div className={styles.statLabel}>Pares</div>
          </div>
        </div>

        {/* Menu de atividades - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.activityMenu}>
          {Object.values(ACTIVITY_TYPES).map((activity) => (
            <button
              key={activity.id}
              className={`${styles.activityButton} ${
                gameState.currentActivity === activity.id ? styles.active : ''
              }`}
              onClick={() => switchToActivity(activity.id)}
            >
              <span>{activity.icon}</span>
              <span>{activity.name}</span>
            </button>
          ))}
        </div>

        {/* Renderização da atividade atual - PADRÃO LETTERRECOGNITION EXATO */}
        {renderActivityInterface()}

        {/* Controles do jogo - PADRÃO LETTERRECOGNITION EXATO */}
        <div className={styles.gameControls}>
          <button className={styles.controlButton} onClick={explainGame}>
            🔊 Explicar
          </button>
          <button className={styles.controlButton} onClick={() => speak('Repita as instruções da atividade atual.')}>
            🔄 Repetir
          </button>
          <button className={styles.controlButton} onClick={resetGame}>
            🔄 Reiniciar
          </button>
          <button className={styles.controlButton} onClick={onBack}>
            ⬅️ Voltar
          </button>
        </div>
      </div>
    </div>
  );
}

export default MemoryGame;
