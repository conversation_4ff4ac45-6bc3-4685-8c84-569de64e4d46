/**
 * 🎮 PADRÃO DE ESTRUTURA DE JOGOS - Portal Betina V3
 * 
 * Este documento define o padrão que TODOS os jogos devem seguir
 * Baseado na estrutura do ColorMatch e MemoryGame
 */

# 📋 ESTRUTURA PADRÃO DE JOGOS

## 🗂️ Estrutura de Arquivos Obrigatória
```
/src/games/[NomeDoJogo]/
├── collectors/                    # Coletores de métricas específicos
│   ├── index.js                  # Hub principal de coletores
│   └── [varios]Collector.js      # Coletores individuais
├── [NomeDoJogo].module.css       # Estilos modulares CSS
├── [NomeDoJogo]Config.js         # Configurações do jogo
├── [NomeDoJogo]Game.jsx          # Componente principal
└── [NomeDoJogo]Metrics.js        # Sistema de métricas
```

## 🎯 SISTEMA DE ATIVIDADES OBRIGATÓRIO
Cada jogo DEVE ter pelo menos 4-6 atividades diferentes:

```javascript
const ACTIVITY_TYPES = {
  ATIVIDADE_PRINCIPAL: {
    id: 'atividade_principal',
    name: 'Nome da Atividade',
    icon: '🎯', 
    description: 'Descrição clara da atividade',
    component: 'ComponenteActivity'
  },
  // ... mais 3-5 atividades
};
```

## 📦 IMPORTS OBRIGATÓRIOS
```javascript
// React e hooks essenciais
import React, { useState, useEffect, useContext, useCallback, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';

// Contextos padrão
import { SystemContext } from '../../components/context/SystemContext.jsx';
import { useAccessibilityContext } from '../../components/context/AccessibilityContext';

// Componente padrão de dificuldade (NÃO MEXER)
import GameStartScreen from '../../components/common/GameStartScreen/GameStartScreen.jsx';

// Hooks unificados obrigatórios
import { useUnifiedGameLogic } from '../../hooks/useUnifiedGameLogic.js';
import { useTherapeuticOrchestrator } from '../../hooks/useTherapeuticOrchestrator.js';
import { useMultisensoryIntegration } from '../../hooks/useMultisensoryIntegration.js';

// Configurações específicas do jogo
import { [NomeDoJogo]Config } from './[NomeDoJogo]Config.js';
import { [NomeDoJogo]Metrics } from './[NomeDoJogo]Metrics.js';
import { [NomeDoJogo]CollectorsHub } from './collectors/index.js';

// Estilos modulares
import styles from './[NomeDoJogo].module.css';
```

## 🏗️ ESTRUTURA DO COMPONENTE PRINCIPAL
```javascript
function [NomeDoJogo]Game({ onBack }) {
  // 1. CONTEXTOS E REFS OBRIGATÓRIOS
  const { user, ttsEnabled = true } = useContext(SystemContext);
  const sessionIdRef = useRef(uuidv4());
  const { settings } = useAccessibilityContext();

  // 2. ESTADOS DE CONTROLE PADRÃO
  const [gameState, setGameState] = useState('start');
  const [difficulty, setDifficulty] = useState('facil');
  const [currentActivity, setCurrentActivity] = useState(null);
  const [sessionData, setSessionData] = useState(null);

  // 3. HOOKS UNIFICADOS OBRIGATÓRIOS
  const gameLogic = useUnifiedGameLogic({
    gameType: '[NomeDoJogo]',
    sessionId: sessionIdRef.current,
    config: [NomeDoJogo]Config
  });

  const therapeuticOrchestrator = useTherapeuticOrchestrator({
    gameType: '[NomeDoJogo]',
    sessionId: sessionIdRef.current
  });

  const multisensoryIntegration = useMultisensoryIntegration({
    gameType: '[NomeDoJogo]',
    sessionId: sessionIdRef.current
  });

  // 4. LÓGICA ESPECÍFICA DO JOGO
  // ... implementação específica

  // 5. RENDER PADRÃO
  return (
    <div className={styles.gameContainer}>
      {gameState === 'start' && (
        <GameStartScreen
          gameTitle="[Nome do Jogo]"
          gameDescription="[Descrição]"
          onStartGame={handleStartGame}
          onBack={onBack}
          activities={Object.values(ACTIVITY_TYPES)}
        />
      )}
      
      {gameState === 'playing' && (
        // Interface do jogo específico
      )}
      
      {gameState === 'results' && (
        // Tela de resultados padrão
      )}
    </div>
  );
}
```

## 🎨 PADRÃO DE INTERFACE

### 1. Tela de Dificuldade (NÃO MEXER)
- Usar sempre o componente `GameStartScreen`
- Passar as atividades disponíveis
- Manter o design padrão

### 2. Tela do Jogo
- Layout consistente entre todos os jogos
- Mesma posição para botões de controle
- Feedback visual padronizado
- Sistema de pontuação unificado

### 3. Tela de Resultados
- Estatísticas padronizadas
- Gráficos consistentes
- Botões de ação uniformes

## 📊 SISTEMA DE MÉTRICAS OBRIGATÓRIO
Cada jogo deve implementar:
- Coletores específicos para suas atividades
- Hub de coletores centralizado
- Métricas terapêuticas padronizadas
- Integração com sistema de análise

## 🔧 CONFIGURAÇÕES PADRONIZADAS
- Níveis de dificuldade: ['facil', 'medio', 'dificil']
- Sistema de pontuação unificado
- Configurações de acessibilidade
- Parâmetros terapêuticos

---

# 📝 JOGOS QUE PRECISAM SER PADRONIZADOS

## ✅ Jogos Já Padronizados
1. **ColorMatch** - ✅ Completo (modelo)
2. **MemoryGame** - ✅ Completo (modelo)
3. **ContagemNumeros** - ✅ Seguindo padrão
4. **ImageAssociation** - ✅ Seguindo padrão

## 🔄 Jogos que Precisam Ajustes
1. **LetterRecognition** - Verificar estrutura
2. **MusicalSequence** - Verificar estrutura  
3. **QuebraCabeca** - Verificar estrutura
4. **PadroesVisuais** - Verificar estrutura
5. **CreativePainting** - Verificar estrutura

## ⚠️ Jogos Não Implementados
1. **PatternMatching** - Criar do zero
2. **SequenceLearning** - Criar do zero

---

# 🎯 PRÓXIMOS PASSOS

1. **Auditoria**: Verificar cada jogo existente
2. **Padronização**: Ajustar jogos que não seguem o padrão
3. **Criação**: Implementar jogos faltantes
4. **Testes**: Validar funcionamento de todos os jogos
5. **Documentação**: Criar guias específicos para cada jogo
