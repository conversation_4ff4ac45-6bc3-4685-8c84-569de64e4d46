# Configurações para desenvolvimento local
DB_HOST=localhost
DB_PORT=5432
DB_USER=betina_user
DB_PASSWORD=betina_password
DB_NAME=betina_db
DB_ENVIRONMENT=online
DB_SSL_ENABLED=false

API_PORT=3000
API_HOST=localhost
JWT_SECRET=portal_betina_jwt_secret_key_super_secure_production_2025_@#$%
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

API_URL_INTERNAL=http://localhost:3000/api
API_URL_EXTERNAL=http://localhost:3000/api

SECURITY_HEADERS_ENABLED=true
CSP_ENABLED=true
HELMET_ENABLED=true

COMPRESSION_LEVEL=6
STATIC_CACHE_MAX_AGE=86400

SYNC_INTERVAL=30000
BATCH_SIZE=50

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=http://localhost:5173,http://localhost:3000,http://localhost:5174,http://localhost:8080
CORS_CREDENTIALS=true

# Frontend
FRONTEND_PORT=5173
FRONTEND_HOST=localhost

# Email
EMAIL_ENABLED=false

# Redis (se necessário)
REDIS_ENABLED=false

# Logging
LOG_LEVEL=debug
ENVIRONMENT=development
