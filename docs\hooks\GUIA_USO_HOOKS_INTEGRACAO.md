# Guia de Uso dos Hooks de Integração

Este documento fornece exemplos práticos de como utilizar os hooks de integração do Portal Betina V3 em componentes React.

## Índice

1. [useSystem](#usesystem) - Acesso ao sistema integrado completo
2. [useSystemEvents](#usesystemevents) - Sistema de eventos
3. [useGameOrchestrator](#usegameorchestrator) - Orquestração de jogos
4. [useUserProfile](#useuserprofile) - Gestão de perfis
5. [useResilientDatabase](#useresilientdatabase) - Acesso a dados com resiliência

## useSystem

Hook para acessar o sistema integrado completo.

### Exemplo de Uso

```jsx
import React from 'react';
import { useSystem } from '../hooks';

function SystemStatusComponent() {
  const { system, healthCheck, getStatistics } = useSystem();
  const [health, setHealth] = React.useState(null);
  const [stats, setStats] = React.useState(null);

  React.useEffect(() => {
    // Verificar saúde do sistema
    const checkHealth = async () => {
      const healthData = await healthCheck();
      setHealth(healthData);
    };

    // Obter estatísticas
    const getStats = () => {
      const systemStats = getStatistics();
      setStats(systemStats);
    };

    checkHealth();
    getStats();
  }, [healthCheck, getStatistics]);

  return (
    <div className="system-status">
      <h2>Status do Sistema</h2>
      
      {health && (
        <div className="health-status">
          <p>Status: {health.healthy ? '✅ Saudável' : '❌ Problemas Detectados'}</p>
          {health.components && (
            <ul>
              <li>Database: {health.components.database ? '✅' : '❌'}</li>
              <li>
                Resiliência: {
                  health.components.resilience.some(cb => cb.state === 'OPEN') 
                    ? '⚠️ Circuit Breaker Aberto' 
                    : '✅ Normal'
                }
              </li>
            </ul>
          )}
        </div>
      )}

      {stats && (
        <div className="system-stats">
          <p>Estatísticas coletadas em: {new Date(stats.timestamp).toLocaleTimeString()}</p>
        </div>
      )}
    </div>
  );
}
```

## useSystemEvents

Hook para trabalhar com eventos do sistema.

### Exemplo de Uso

```jsx
import React from 'react';
import { useSystemEvents } from '../hooks';

function GameComponent({ gameId, userId }) {
  const { dispatchGameEvent, EventTypes } = useSystemEvents();

  // Iniciar o jogo
  const handleStartGame = async () => {
    try {
      await dispatchGameEvent(gameId, 'start', {
        userId,
        difficulty: 'medium'
      });
      console.log('Jogo iniciado e evento registrado');
    } catch (error) {
      console.error('Erro ao iniciar jogo:', error);
    }
  };

  // Finalizar o jogo
  const handleFinishGame = async (score) => {
    try {
      await dispatchGameEvent(gameId, 'complete', {
        userId,
        score,
        timeSpent: 120 // segundos
      });
      console.log('Jogo finalizado e evento registrado');
    } catch (error) {
      console.error('Erro ao finalizar jogo:', error);
    }
  };

  return (
    <div className="game">
      <button onClick={handleStartGame}>Iniciar Jogo</button>
      {/* Conteúdo do jogo */}
      <button onClick={() => handleFinishGame(100)}>Finalizar Jogo</button>
    </div>
  );
}
```

## useGameOrchestrator

Hook para gerenciar a orquestração de jogos, incluindo recomendações.

### Exemplo de Uso

```jsx
import React from 'react';
import { useGameOrchestrator } from '../hooks';

function GameOrchestrationComponent({ userId, profileId }) {
  const {
    currentGameId,
    recommendations,
    isAnalyzing,
    startGame,
    completeGame,
    updateGameProgress,
    refreshRecommendations
  } = useGameOrchestrator({ userId, profileId });

  // Iniciar um jogo específico
  const handleStartGame = async (gameId) => {
    const result = await startGame(gameId, { difficulty: 'auto' });
    if (result.success) {
      console.log(`Jogo ${gameId} iniciado com sucesso`);
    }
  };

  // Finalizar o jogo atual
  const handleCompleteGame = async (metrics) => {
    if (!currentGameId) return;
    
    const result = await completeGame(currentGameId, metrics);
    if (result.success) {
      console.log(`Jogo ${currentGameId} finalizado com sucesso`);
    }
  };

  // Atualizar progresso durante o jogo
  const handleProgressUpdate = async (progressData) => {
    if (!currentGameId) return;
    
    await updateGameProgress(currentGameId, progressData);
  };

  return (
    <div className="game-orchestration">
      <h2>Jogos Recomendados</h2>
      
      {isAnalyzing && <p>Analisando seu perfil para recomendações...</p>}
      
      <div className="recommendations">
        {recommendations.map((game) => (
          <div key={game.id} className="game-card">
            <h3>{game.title}</h3>
            <p>{game.description}</p>
            <button onClick={() => handleStartGame(game.id)}>
              Iniciar
            </button>
          </div>
        ))}
      </div>
      
      {recommendations.length === 0 && !isAnalyzing && (
        <button onClick={refreshRecommendations}>
          Buscar Recomendações
        </button>
      )}
      
      {currentGameId && (
        <div className="current-game">
          <p>Jogo atual: {currentGameId}</p>
          <button onClick={() => handleCompleteGame({ score: 100 })}>
            Finalizar Jogo
          </button>
        </div>
      )}
    </div>
  );
}
```

## useUserProfile

Hook para gerenciar perfis de usuário.

### Exemplo de Uso

```jsx
import React, { useState } from 'react';
import { useUserProfile } from '../hooks';

function UserProfileComponent({ userId }) {
  const {
    profiles,
    currentProfile,
    isLoading,
    error,
    createProfile,
    updateProfile,
    selectProfile
  } = useUserProfile({ userId });
  
  const [newProfileName, setNewProfileName] = useState('');

  // Criar novo perfil
  const handleCreateProfile = async () => {
    if (!newProfileName.trim()) return;
    
    try {
      const newProfile = await createProfile({
        name: newProfileName,
        preferences: {
          theme: 'default',
          gameDifficulty: 'medium'
        }
      });
      
      console.log('Novo perfil criado:', newProfile);
      setNewProfileName('');
    } catch (error) {
      console.error('Erro ao criar perfil:', error);
    }
  };

  // Atualizar perfil
  const handleUpdateProfile = async (profileId, updates) => {
    try {
      const updated = await updateProfile(profileId, updates);
      console.log('Perfil atualizado:', updated);
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
    }
  };

  // Selecionar perfil
  const handleSelectProfile = async (profileId) => {
    try {
      const selected = await selectProfile(profileId);
      console.log('Perfil selecionado:', selected);
    } catch (error) {
      console.error('Erro ao selecionar perfil:', error);
    }
  };

  if (isLoading) return <p>Carregando perfis...</p>;
  
  if (error) return <p>Erro ao carregar perfis: {error}</p>;

  return (
    <div className="user-profiles">
      <h2>Perfis</h2>
      
      {profiles.length > 0 ? (
        <div className="profile-list">
          {profiles.map((profile) => (
            <div 
              key={profile.id} 
              className={`profile-item ${currentProfile?.id === profile.id ? 'active' : ''}`}
            >
              <h3>{profile.name}</h3>
              <p>Última atualização: {new Date(profile.lastModified || profile.createdAt).toLocaleDateString()}</p>
              
              <button onClick={() => handleSelectProfile(profile.id)}>
                Selecionar
              </button>
              
              <button onClick={() => handleUpdateProfile(profile.id, { 
                preferences: { ...profile.preferences, theme: 'dark' }
              })}>
                Tema Escuro
              </button>
            </div>
          ))}
        </div>
      ) : (
        <p>Nenhum perfil encontrado</p>
      )}
      
      <div className="create-profile">
        <h3>Criar Novo Perfil</h3>
        <input
          type="text"
          value={newProfileName}
          onChange={(e) => setNewProfileName(e.target.value)}
          placeholder="Nome do perfil"
        />
        <button onClick={handleCreateProfile}>Criar</button>
      </div>
    </div>
  );
}
```

## useResilientDatabase

Hook para acesso ao banco de dados com resiliência.

### Exemplo de Uso

```jsx
import React, { useState, useEffect } from 'react';
import { useResilientDatabase } from '../hooks';

function GameDataComponent({ userId, gameId }) {
  const { 
    saveGameMetrics, 
    getUserData, 
    status, 
    checkStatus 
  } = useResilientDatabase();
  
  const [gameData, setGameData] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  
  // Carregar dados do jogo
  useEffect(() => {
    const loadGameData = async () => {
      try {
        const data = await getUserData(userId, 'gameProgress');
        // Filtrar dados específicos do jogo atual
        const thisGameData = data?.games?.find(g => g.gameId === gameId) || null;
        setGameData(thisGameData);
      } catch (error) {
        console.error('Erro ao carregar dados do jogo:', error);
      }
    };
    
    loadGameData();
  }, [userId, gameId, getUserData]);

  // Salvar métricas do jogo
  const handleSaveMetrics = async (metrics) => {
    try {
      setIsSaving(true);
      
      const result = await saveGameMetrics(userId, gameId, metrics);
      console.log('Métricas salvas:', result);
      
      // Verificar status do sistema após a operação
      await checkStatus();
    } catch (error) {
      console.error('Erro ao salvar métricas:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="game-data">
      <h2>Dados do Jogo</h2>
      
      {/* Exibir status do sistema resiliente */}
      <div className="resilience-status">
        <p>Status do sistema: {status.isReady ? '✅ Pronto' : '⚠️ Não Disponível'}</p>
        {status.error && <p>Erro: {status.error.message}</p>}
      </div>
      
      {/* Exibir dados do jogo */}
      {gameData ? (
        <div className="game-progress">
          <p>Nível atual: {gameData.level}</p>
          <p>Pontuação: {gameData.score}</p>
          <p>Última jogada: {new Date(gameData.lastPlayed).toLocaleString()}</p>
        </div>
      ) : (
        <p>Nenhum dado encontrado para este jogo</p>
      )}
      
      {/* Botão para salvar progresso simulado */}
      <button 
        disabled={isSaving} 
        onClick={() => handleSaveMetrics({
          level: 5,
          score: 100,
          completedChallenges: 3,
          lastPlayed: new Date().toISOString()
        })}
      >
        {isSaving ? 'Salvando...' : 'Salvar Progresso'}
      </button>
    </div>
  );
}
```

## Uso Conjunto dos Hooks

```jsx
import React, { useEffect } from 'react';
import { 
  useSystem, 
  useResilientDatabase, 
  useGameOrchestrator,
  useUserProfile,
  useSystemEvents
} from '../hooks';

function IntegratedGameComponent({ userId }) {
  const { system, healthCheck } = useSystem();
  const { profiles, currentProfile, selectProfile } = useUserProfile({ userId });
  const { 
    recommendations,
    startGame,
    completeGame
  } = useGameOrchestrator({ 
    userId, 
    profileId: currentProfile?.id 
  });
  const { dispatchEvent, EventTypes } = useSystemEvents();
  
  // Verificar saúde do sistema na inicialização
  useEffect(() => {
    const checkSystem = async () => {
      const health = await healthCheck();
      console.log('Status de saúde:', health);
      
      // Notificar inicialização
      await dispatchEvent(EventTypes.SYSTEM_HEALTH_CHECK, {
        component: 'IntegratedGameComponent',
        health
      });
    };
    
    checkSystem();
  }, [healthCheck, dispatchEvent, EventTypes]);

  // Iniciar um jogo recomendado
  const handleStartRecommendedGame = async () => {
    if (recommendations.length === 0) return;
    
    // Pegar o primeiro jogo recomendado
    const recommendedGame = recommendations[0];
    
    // Iniciar o jogo
    const result = await startGame(recommendedGame.id);
    
    if (result.success) {
      console.log(`Jogo ${recommendedGame.id} iniciado com sucesso`);
    }
  };

  return (
    <div className="integrated-game">
      <h1>Portal Betina V3 - Sistema Integrado</h1>
      
      {/* Seleção de perfil */}
      {profiles.length > 0 && (
        <div className="profile-selector">
          <h2>Perfil Atual: {currentProfile?.name || 'Nenhum'}</h2>
          <select 
            value={currentProfile?.id || ''} 
            onChange={(e) => selectProfile(e.target.value)}
          >
            <option value="">Selecione um perfil</option>
            {profiles.map(profile => (
              <option key={profile.id} value={profile.id}>
                {profile.name}
              </option>
            ))}
          </select>
        </div>
      )}
      
      {/* Recomendações */}
      {recommendations.length > 0 && (
        <div className="recommendations">
          <h2>Jogos Recomendados</h2>
          <button onClick={handleStartRecommendedGame}>
            Iniciar Jogo Recomendado
          </button>
        </div>
      )}
    </div>
  );
}
```

## Melhores Práticas

1. **Sempre verifique erros**: Utilize try/catch em operações assíncronas
2. **Utilize memoização**: Para performance em componentes que usam múltiplos hooks
3. **Verifique disponibilidade**: Sempre verifique se o sistema está disponível antes de operações
4. **Propague eventos**: Utilize o sistema de eventos para comunicação entre componentes
5. **Respeite o ciclo de vida**: Limpe recursos (clearInterval, etc) no retorno de useEffect
