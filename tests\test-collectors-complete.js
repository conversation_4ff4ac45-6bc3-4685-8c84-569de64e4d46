/**
 * 🧪 TESTE COMPLETO DE INTEGRAÇÃO DOS COLETORES DOS JOGOS
 * Versão aprimorada para validar a coleta de dados e integração com processadores
 * Portal Betina V3 - Validação final
 */

import { ColorMatchCollectorsHub } from './src/games/ColorMatch/collectors/index.js';
import { NumberCountingCollectorsHub } from './src/games/ContagemNumeros/collectors/index.js';
import { ImageAssociationCollectorsHub } from './src/games/ImageAssociation/collectors/index.js';
import { MusicalSequenceCollectorsHub } from './src/games/MusicalSequence/collectors/index.js';
import { QuebraCabecaCollectorsHub } from './src/games/QuebraCabeca/collectors/index.js';
import { CreativePaintingCollectorsHub } from './src/games/CreativePainting/collectors/index.js';
import { LetterRecognitionCollectorsHub } from './src/games/LetterRecognition/collectors/index.js';
import { MemoryGameCollectorsHub } from './src/games/MemoryGame/collectors/index.js';

class CompleteCollectorsTest {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      warnings: 0,
      details: []
    };
    
    this.games = [
      {
        name: 'ColorMatch',
        hub: ColorMatchCollectorsHub,
        expectedCollectors: ['colorPerception', 'visualProcessing', 'attentionalSelectivity', 'colorCognition', 'errorPattern']
      },
      {
        name: 'ContagemNumeros',
        hub: NumberCountingCollectorsHub,
        expectedCollectors: ['numericalCognition', 'attentionFocus', 'visualProcessing', 'mathematicalReasoning', 'errorPattern']
      },
      {
        name: 'ImageAssociation',
        hub: ImageAssociationCollectorsHub,
        expectedCollectors: ['associativeMemory', 'visualProcessing', 'categorization', 'flexibility', 'errorPattern']
      },
      {
        name: 'MusicalSequence',
        hub: MusicalSequenceCollectorsHub,
        expectedCollectors: ['auditoryMemoryCollector', 'musicalPatternCollector', 'sequenceExecutionCollector', 'musicalLearningCollector', 'rhythmPatternCollector', 'errorPatternCollector']
      },
      {
        name: 'QuebraCabeca',
        hub: QuebraCabecaCollectorsHub,
        expectedCollectors: ['spatialReasoning', 'problemSolving', 'visualProcessing', 'motorSkills', 'errorPattern']
      },
      {
        name: 'CreativePainting',
        hub: CreativePaintingCollectorsHub,
        expectedCollectors: ['creativityAnalysis', 'motorSkills', 'emotionalExpression', 'artisticStyle', 'engagementMetrics', 'errorPattern']
      },
      {
        name: 'LetterRecognition',
        hub: LetterRecognitionCollectorsHub,
        expectedCollectors: ['letterRecognition', 'visualAttention', 'phonological', 'learningDifficulties', 'errorPattern']
      },
      {
        name: 'MemoryGame',
        hub: MemoryGameCollectorsHub,
        expectedCollectors: ['visualSpatial', 'attentionFocus', 'cognitiveStrategies', 'memoryDifficulties', 'errorPattern']
      }
    ];
  }

  /**
   * Executa todos os testes de integração
   */
  async runAllTests() {
    console.log('🧪 Iniciando Teste COMPLETO de Integração dos Coletores dos Jogos');
    console.log('='.repeat(80));

    console.log('📋 Validando 8 jogos funcionais com todos seus coletores\n');
    
    for (const game of this.games) {
      await this.testGameCollectors(game);
    }

    // Teste de integração do hub central
    await this.testErrorPatternCollectorIntegration();

    this.printSummary();
    return this.testResults;
  }

  /**
   * Testa os coletores de um jogo específico
   */
  async testGameCollectors(gameConfig) {
    console.log(`\n🎮 Testando ${gameConfig.name}...`);
    
    try {
      // 1. Testar instanciação do hub
      const hub = new gameConfig.hub();
      this.addResult('pass', `${gameConfig.name}: Hub instanciado com sucesso`);

      // 2. Verificar se todos os coletores esperados estão presentes
      const collectorsProperty = hub.collectors || hub._collectors;
      if (!collectorsProperty) {
        this.addResult('fail', `${gameConfig.name}: Propriedade 'collectors' não encontrada`);
        return;
      }

      // 3. Verificar coletores individuais
      const presentCollectors = Object.keys(collectorsProperty);
      console.log(`   📋 Coletores encontrados: ${presentCollectors.join(', ')}`);

      for (const expectedCollector of gameConfig.expectedCollectors) {
        if (presentCollectors.includes(expectedCollector)) {
          this.addResult('pass', `${gameConfig.name}: Coletor '${expectedCollector}' presente`);
          
          // Testar se o coletor tem método analyze
          const collector = collectorsProperty[expectedCollector];
          if (typeof collector.analyze === 'function') {
            this.addResult('pass', `${gameConfig.name}: Coletor '${expectedCollector}' tem método analyze`);
          } else {
            this.addResult('warning', `${gameConfig.name}: Coletor '${expectedCollector}' não tem método analyze`);
          }
          
          // Testar se o coletor tem método collect
          if (typeof collector.collect === 'function') {
            this.addResult('pass', `${gameConfig.name}: Coletor '${expectedCollector}' tem método collect`);
            
            // Testar o método collect com dados mock
            const mockData = this.generateMockGameData(gameConfig.name);
            try {
              const collectedData = await collector.collect(mockData);
              if (collectedData) {
                this.addResult('pass', `${gameConfig.name}: ${expectedCollector}.collect retornou dados`);
              } else {
                this.addResult('warning', `${gameConfig.name}: ${expectedCollector}.collect não retornou dados`);
              }
            } catch (error) {
              this.addResult('warning', `${gameConfig.name}: ${expectedCollector}.collect gerou erro: ${error.message}`);
            }
          } else {
            this.addResult('warning', `${gameConfig.name}: Coletor '${expectedCollector}' não tem método collect`);
          }
        } else {
          this.addResult('fail', `${gameConfig.name}: Coletor '${expectedCollector}' ausente`);
        }
      }

      // 4. Verificar ErrorPatternCollector especificamente
      await this.testErrorPatternCollector(gameConfig.name, hub);

      // 5. Testar método runCompleteAnalysis
      if (typeof hub.runCompleteAnalysis === 'function') {
        this.addResult('pass', `${gameConfig.name}: Método 'runCompleteAnalysis' presente`);
        
        // Teste básico com dados mock
        const mockData = this.generateMockGameData(gameConfig.name);
        try {
          const results = await hub.runCompleteAnalysis(mockData);
          if (results && Object.keys(results).length > 0) {
            this.addResult('pass', `${gameConfig.name}: runCompleteAnalysis retornou resultados`);
            
            // Verificar se os resultados contêm dados do ErrorPatternCollector
            if (results.errorPattern || results.errors || results.errorPatterns) {
              this.addResult('pass', `${gameConfig.name}: ErrorPatternCollector integrado nos resultados da análise`);
            } else {
              this.addResult('warning', `${gameConfig.name}: Não encontrou dados do ErrorPatternCollector nos resultados`);
            }
          } else {
            this.addResult('warning', `${gameConfig.name}: runCompleteAnalysis não retornou resultados`);
          }
        } catch (error) {
          this.addResult('warning', `${gameConfig.name}: runCompleteAnalysis gerou erro: ${error.message}`);
        }
      } else {
        this.addResult('fail', `${gameConfig.name}: Método 'runCompleteAnalysis' ausente`);
      }

    } catch (error) {
      this.addResult('fail', `${gameConfig.name}: Erro na instanciação: ${error.message}`);
    }
  }

  /**
   * Testa especificamente o ErrorPatternCollector
   */
  async testErrorPatternCollector(gameName, hub) {
    const collectorsProperty = hub.collectors || hub._collectors;
    const errorCollectorKey = Object.keys(collectorsProperty).find(key => 
      key.toLowerCase().includes('error') || key.toLowerCase().includes('errorpattern')
    );
    
    if (!errorCollectorKey) {
      this.addResult('fail', `${gameName}: ErrorPatternCollector não encontrado`);
      return;
    }
    
    const errorCollector = collectorsProperty[errorCollectorKey];

    if (errorCollector) {
      this.addResult('pass', `${gameName}: ErrorPatternCollector presente (${errorCollectorKey})`);
      
      // Verificar métodos essenciais do ErrorPatternCollector
      const essentialMethods = ['analyze', 'collect', 'getErrorPatterns'];
      for (const method of essentialMethods) {
        if (typeof errorCollector[method] === 'function') {
          this.addResult('pass', `${gameName}: ErrorPatternCollector.${method} presente`);
          
          // Testar o método com dados mock se for collect ou analyze
          if (method === 'collect' || method === 'analyze') {
            const mockData = this.generateMockGameData(gameName);
            try {
              const result = await errorCollector[method](mockData);
              if (result) {
                this.addResult('pass', `${gameName}: ErrorPatternCollector.${method} executou com sucesso`);
              } else {
                this.addResult('warning', `${gameName}: ErrorPatternCollector.${method} não retornou dados`);
              }
            } catch (error) {
              this.addResult('warning', `${gameName}: ErrorPatternCollector.${method} gerou erro: ${error.message}`);
            }
          }
        } else {
          this.addResult('warning', `${gameName}: ErrorPatternCollector.${method} ausente`);
        }
      }
      
      // Verificar propriedades esperadas
      const expectedProperties = ['name', 'description', 'version', 'isActive', 'collectedData'];
      for (const prop of expectedProperties) {
        if (errorCollector[prop] !== undefined) {
          this.addResult('pass', `${gameName}: ErrorPatternCollector.${prop} presente`);
        } else {
          this.addResult('warning', `${gameName}: ErrorPatternCollector.${prop} ausente`);
        }
      }
    } else {
      this.addResult('fail', `${gameName}: ErrorPatternCollector ausente`);
    }
  }
  
  /**
   * Teste de integração específico dos ErrorPatternCollector
   */
  async testErrorPatternCollectorIntegration() {
    console.log('\n🧩 Testando integração dos ErrorPatternCollector...');
    
    // Testar cada jogo em sequência
    const results = [];
    
    for (const game of this.games) {
      try {
        const hub = new game.hub();
        const mockData = this.generateMockGameData(game.name);
        
        // Adicionar erros simulados para teste
        mockData.errors = [
          { type: 'interaction', timestamp: Date.now() - 5000 },
          { type: 'performance', timestamp: Date.now() - 2000 }
        ];
        
        // Coletar dados de erro usando ErrorPatternCollector
        const collectorsProperty = hub.collectors || hub._collectors;
        const errorCollectorKey = Object.keys(collectorsProperty).find(key => 
          key.toLowerCase().includes('error') || key.toLowerCase().includes('errorpattern')
        );
        
        if (!errorCollectorKey) {
          this.addResult('fail', `${game.name}: ErrorPatternCollector não encontrado para integração`);
          continue;
        }
        
        const errorCollector = collectorsProperty[errorCollectorKey];
        
        if (errorCollector && typeof errorCollector.collect === 'function') {
          try {
            const errorData = await errorCollector.collect(mockData);
            
            if (errorData) {
              this.addResult('pass', `${game.name}: ErrorPatternCollector coletou dados com sucesso`);
              results.push({
                game: game.name,
                success: true,
                data: errorData
              });
            } else {
              this.addResult('warning', `${game.name}: ErrorPatternCollector não retornou dados`);
              results.push({
                game: game.name,
                success: false,
                error: 'Sem dados retornados'
              });
            }
          } catch (error) {
            this.addResult('warning', `${game.name}: Erro ao coletar dados: ${error.message}`);
            results.push({
              game: game.name,
              success: false,
              error: error.message
            });
          }
        } else {
          this.addResult('fail', `${game.name}: ErrorPatternCollector não tem método collect`);
          results.push({
            game: game.name,
            success: false,
            error: 'Método collect não encontrado'
          });
        }
      } catch (error) {
        this.addResult('fail', `${game.name}: Erro no teste de integração: ${error.message}`);
        results.push({
          game: game.name,
          success: false,
          error: error.message
        });
      }
    }
    
    // Resumo da integração
    const successful = results.filter(r => r.success).length;
    console.log(`\n📊 Integração dos ErrorPatternCollector: ${successful}/${this.games.length} jogos funcionais`);
    
    if (successful === this.games.length) {
      this.addResult('pass', 'Todos os ErrorPatternCollector estão integrados corretamente!');
    } else {
      this.addResult('warning', `${this.games.length - successful} ErrorPatternCollector precisam ser revisados`);
    }
  }

  /**
   * Gera dados mock para teste
   */
  generateMockGameData(gameName) {
    const baseData = {
      gameId: gameName.toLowerCase(),
      sessionId: 'test-session-' + Date.now(),
      playerId: 'test-player',
      timestamp: new Date().toISOString(),
      duration: 120000,
      score: 85,
      difficulty: 'medium',
      attempts: 5,
      errors: 2,
      interactions: []
    };

    // Dados específicos por jogo
    switch (gameName) {
      case 'ColorMatch':
        return {
          ...baseData,
          colorMatches: 8,
          colorErrors: 2,
          avgResponseTime: 1500,
          colorAccuracy: 0.8
        };
      
      case 'ContagemNumeros':
        return {
          ...baseData,
          numbersCount: 10,
          countingErrors: 1,
          numericalAccuracy: 0.9,
          processingSpeed: 'normal'
        };
      
      case 'ImageAssociation':
        return {
          ...baseData,
          associations: 12,
          associationErrors: 3,
          categoriesExplored: 4,
          semanticScore: 0.75
        };
      
      case 'MusicalSequence':
        return {
          ...baseData,
          sequencesCompleted: 5,
          rhythmErrors: 2,
          tempoAccuracy: 0.85,
          musicalMemory: 0.78
        };
      
      case 'QuebraCabeca':
        return {
          ...baseData,
          piecesPlaced: 15,
          incorrectPlacements: 4,
          completionRate: 0.8,
          spatialScore: 0.75
        };
      
      case 'CreativePainting':
        return {
          ...baseData,
          elementsUsed: 8,
          colorsPalette: 6,
          creativeScore: 0.85,
          engagementLevel: 0.9
        };
      
      case 'LetterRecognition':
        return {
          ...baseData,
          lettersRecognized: 20,
          letterErrors: 3,
          phonologicalScore: 0.85,
          visualAttentionScore: 0.8
        };
      
      case 'MemoryGame':
        return {
          ...baseData,
          memoryPairs: 8,
          memoryErrors: 3,
          memoryAccuracy: 0.75,
          retentionRate: 0.85
        };
      
      default:
        return baseData;
    }
  }

  /**
   * Adiciona resultado do teste
   */
  addResult(type, message) {
    this.testResults[type === 'pass' ? 'passed' : type === 'fail' ? 'failed' : 'warnings']++;
    this.testResults.details.push({
      type,
      message,
      timestamp: new Date().toISOString()
    });

    const icon = type === 'pass' ? '✅' : type === 'fail' ? '❌' : '⚠️';
    console.log(`   ${icon} ${message}`);
  }

  /**
   * Imprime resumo dos testes
   */
  printSummary() {
    console.log('\n' + '='.repeat(80));
    console.log('📊 RESUMO DOS TESTES DE INTEGRAÇÃO COMPLETA DOS COLETORES');
    console.log('='.repeat(80));
    console.log(`✅ Testes Aprovados: ${this.testResults.passed}`);
    console.log(`❌ Testes Falharam: ${this.testResults.failed}`);
    console.log(`⚠️  Avisos: ${this.testResults.warnings}`);
    
    const total = this.testResults.passed + this.testResults.failed + this.testResults.warnings;
    const successRate = total > 0 ? ((this.testResults.passed / total) * 100).toFixed(1) : 0;
    
    console.log(`📈 Taxa de Sucesso: ${successRate}%`);
    
    if (this.testResults.failed === 0) {
      console.log('\n🎉 Todos os coletores estão funcionais e integrados corretamente!');
      console.log('✅ Os ErrorPatternCollector estão presentes e funcionais em todos os 8 jogos');
      console.log('✅ A interface de collect() está implementada em todos os coletores');
    } else {
      console.log('\n🔧 Algumas correções são necessárias nos coletores:');
      const failedTests = this.testResults.details
        .filter(d => d.type === 'fail')
        .map(d => `   ❌ ${d.message}`)
        .join('\n');
      console.log(failedTests);
    }
  }
}

// Executar teste se for chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const test = new CompleteCollectorsTest();
  await test.runAllTests();
}

export { CompleteCollectorsTest };
