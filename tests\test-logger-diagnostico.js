// test-logger-diagnostico.js
import { createLogger, logger } from './src/utils/logger.js';

console.log("=== TESTE DE DIAGNÓSTICO DO LOGGER ===");

// Teste do logger default
console.log("\n1. Testando logger default:");
logger.info("Teste de mensagem INFO");
logger.debug("Teste de mensagem DEBUG");
logger.warn("Teste de mensagem WARN");
logger.error("Teste de mensagem ERROR");

// Teste do logger customizado
console.log("\n2. Testando createLogger com namespace:");
const customLogger = createLogger("teste-diagnostico");
customLogger.info("Teste de mensagem INFO com namespace");
customLogger.debug("Teste de mensagem DEBUG com namespace");
customLogger.warn("Teste de mensagem WARN com namespace");
customLogger.error("Teste de mensagem ERROR com namespace");

console.log("\n=== TESTE FINALIZADO ===");
