/**
 * Dashboard Overview Routes
 * Rotas para visão geral do dashboard
 */

import express from 'express';

const router = express.Router();

/**
 * GET / - Dashboard Overview
 */
router.get('/', (req, res) => {
  try {
    const userId = req.user?.id || 'anonymous';

    // Dados básicos de overview
    const overviewData = {
      user: {
        id: userId,
        name: req.user?.first_name || 'Usuário',
        role: req.user?.role || 'guest'
      },
      stats: {
        totalSessions: 0,
        totalGames: 11,
        averageScore: 0,
        lastActivity: new Date().toISOString()
      },
      recentActivity: [],
      quickActions: [
        { id: 1, title: 'Iniciar <PERSON><PERSON>', icon: 'play', url: '/games' },
        { id: 2, title: 'Ver Relatórios', icon: 'chart', url: '/reports' },
        { id: 3, title: 'Configurações', icon: 'settings', url: '/settings' }
      ]
    };

    res.json({
      success: true,
      message: 'Overview carregado com sucesso',
      data: overviewData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Erro ao carregar overview:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor',
      error: error.message
    });
  }
});

export default router;
