/* IEBrandMetrics.module.css - Estilos para métricas IE Brand */

.metricsContainer {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Header */
.metricsHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
}

.headerInfo {
  flex: 1;
}

.metricsTitle {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
}

.brandIcon {
  font-size: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.metricsSubtitle {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
  line-height: 1.4;
}

.headerControls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.timeSelector {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.timeSelector:hover {
  border-color: #667eea;
}

.timeSelector:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Seletor de Métricas */
.metricsSelector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 32px;
}

.metricButton {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.metricButton:hover {
  border-color: var(--metric-color);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metricButton.active {
  border-color: var(--metric-color);
  background: linear-gradient(135deg, var(--metric-color)10, var(--metric-color)05);
  color: #2c3e50;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metricIcon {
  font-size: 24px;
  flex-shrink: 0;
}

.metricLabel {
  font-weight: 600;
}

/* Métricas Principais */
.mainMetrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.scoreCard {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  padding: 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.scoreCard::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.scoreHeader {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.scoreIcon {
  font-size: 32px;
}

.scoreTitle {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

.scoreDescription {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.scoreValue {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 12px;
}

.scoreNumber {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
}

.scoreUnit {
  font-size: 24px;
  opacity: 0.8;
}

.scoreTrend {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.scoreTrend.positive {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.scoreTrend.stable {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
}

.scoreTrend.improving {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
}

.factorsCard {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e9ecef;
}

.factorsTitle {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.factorsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.factorItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.factorIcon {
  width: 20px;
  height: 20px;
  background: #4CAF50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
}

.factorText {
  font-size: 14px;
  color: #495057;
  line-height: 1.4;
}

/* Gráficos */
.chartsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.chartCard {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.chartTitle {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chartContainer {
  height: 300px;
  position: relative;
}

/* Recomendações */
.recommendationsSection {
  margin-bottom: 32px;
}

.recommendationsTitle {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.recommendationsList {
  display: grid;
  gap: 16px;
}

.recommendationCard {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  border-left: 4px solid #667eea;
  transition: all 0.2s ease;
}

.recommendationCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.recommendationIcon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.recommendationContent {
  flex: 1;
}

.recommendationContent p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
}

/* Footer IE Brand */
.brandFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 2px solid rgba(0, 0, 0, 0.1);
  margin-top: 32px;
}

.brandInfo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.brandLogo {
  font-size: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brandInfo strong {
  display: block;
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 4px;
}

.brandInfo p {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
}

.mcpStatus {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.mcpIndicator {
  font-size: 14px;
}

/* Responsividade */
@media (max-width: 1200px) {
  .chartsGrid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .metricsContainer {
    padding: 16px;
  }
  
  .metricsHeader {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .metricsSelector {
    grid-template-columns: 1fr;
  }
  
  .mainMetrics {
    grid-template-columns: 1fr;
  }
  
  .chartsGrid {
    grid-template-columns: 1fr;
  }
  
  .chartContainer {
    height: 250px;
  }
  
  .brandFooter {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .metricsTitle {
    font-size: 24px;
  }
  
  .scoreNumber {
    font-size: 36px;
  }
  
  .scoreUnit {
    font-size: 18px;
  }
  
  .chartContainer {
    height: 200px;
  }
}

/* Estados para acessibilidade */
.metricButton:focus {
  outline: 3px solid rgba(102, 126, 234, 0.3);
  outline-offset: 2px;
}

.timeSelector:focus {
  outline: 3px solid rgba(102, 126, 234, 0.3);
  outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .metricsContainer {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .metricsTitle {
    color: #e0e0e0;
  }
  
  .metricsSubtitle {
    color: #a0a0a0;
  }
  
  .metricButton {
    background: #333;
    border-color: #444;
    color: #e0e0e0;
  }
  
  .factorsCard,
  .chartCard,
  .recommendationCard {
    background: #333;
    border-color: #444;
  }
  
  .factorsTitle,
  .chartTitle,
  .recommendationsTitle {
    color: #e0e0e0;
  }
  
  .factorText,
  .recommendationContent p {
    color: #c0c0c0;
  }
}
