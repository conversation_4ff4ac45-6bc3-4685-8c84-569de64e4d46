/**
 * @file SystemLogs.jsx
 * @description Visualizador de Logs do Sistema - Área Administrativa
 * @version 3.0.0
 * @admin true
 * @datasource API Real + LocalStorage Fallback
 */

import React, { useState, useEffect } from 'react';
import adminApiService from '../../../../services/adminApiService';
import styles from './SystemLogs.module.css';

const SystemLogs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filterLevel, setFilterLevel] = useState('all');
  const [filterService, setFilterService] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [dataSource, setDataSource] = useState('loading');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [prometheusMetrics, setPrometheusMetrics] = useState(null);
  const [systemMetrics, setSystemMetrics] = useState(null);

  // Função para buscar logs do localStorage
  const getLocalStorageLogs = () => {
    try {
      const systemLogs = JSON.parse(localStorage.getItem('system_logs') || '[]');
      const errorLogs = JSON.parse(localStorage.getItem('error_logs') || '[]');

      const allLocalLogs = [
        ...systemLogs.map(log => ({ ...log, source: 'localStorage' })),
        ...errorLogs.map(log => ({ ...log, level: 'error', source: 'localStorage' })),
      ];

      return allLocalLogs
        .filter(log => log.timestamp)
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 100);
    } catch (error) {
      console.warn('Erro ao buscar logs locais:', error);
      return [];
    }
  };

  // Função para limpar logs antigos
  const cleanupOldLogs = () => {
    try {
      const oneHourAgo = Date.now() - (60 * 60 * 1000);

      const errorLogs = JSON.parse(localStorage.getItem('error_logs') || '[]');
      const recentErrorLogs = errorLogs
        .filter(log => log.timestamp && log.timestamp > oneHourAgo)
        .slice(0, 5);
      localStorage.setItem('error_logs', JSON.stringify(recentErrorLogs));

      const systemLogs = JSON.parse(localStorage.getItem('system_logs') || '[]');
      const recentSystemLogs = systemLogs
        .filter(log => log.timestamp && log.timestamp > oneHourAgo)
        .slice(0, 20);
      localStorage.setItem('system_logs', JSON.stringify(recentSystemLogs));

      console.log('🧹 Logs antigos limpos com sucesso');
    } catch (error) {
      console.warn('Erro na limpeza de logs:', error);
    }
  };

  // Função para coletar logs do sistema
  const collectSystemLogs = () => {
    const systemLogs = [];

    // Logs do console do navegador (filtrados)
    const consoleLogs = window.__SYSTEM_LOGS__ || [];
    const filteredConsoleLogs = consoleLogs.filter(log => {
      if (log.level === 'error' && log.message && log.message.includes('Operação falhou')) {
        return false;
      }
      return true;
    });
    systemLogs.push(...filteredConsoleLogs);

    // Logs do localStorage (com filtro)
    try {
      const storedLogs = JSON.parse(localStorage.getItem('system_logs') || '[]');
      const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000);
      const recentStoredLogs = storedLogs.filter(log => log.timestamp > twoHoursAgo);
      systemLogs.push(...recentStoredLogs);
    } catch (error) {
      console.warn('Erro ao carregar logs do localStorage:', error);
    }

    // Logs das sessões de jogos
    try {
      const gameSessions = JSON.parse(localStorage.getItem('gameSessions') || '[]');
      gameSessions.forEach(session => {
        systemLogs.push({
          id: `session_${session.id}`,
          timestamp: new Date(session.startTime).getTime(),
          level: 'info',
          service: 'GameSessionManager',
          type: 'session_created',
          message: `Sessão ${session.gameType} iniciada para usuário ${session.userId}`,
          metadata: {
            gameType: session.gameType,
            userId: session.userId,
            difficulty: session.difficulty,
            sessionId: session.id,
          },
        });

        if (session.endTime) {
          systemLogs.push({
            id: `session_end_${session.id}`,
            timestamp: new Date(session.endTime).getTime(),
            level: 'info',
            service: 'GameSessionManager',
            type: 'session_completed',
            message: `Sessão ${session.gameType} finalizada - Score: ${session.finalScore}`,
            metadata: {
              gameType: session.gameType,
              userId: session.userId,
              duration: session.duration,
              finalScore: session.finalScore,
              sessionId: session.id,
            },
          });
        }
      });
    } catch (error) {
      console.warn('Erro ao processar logs de sessões:', error);
    }

    // Logs de erros capturados
    try {
      const errorLogs = JSON.parse(localStorage.getItem('error_logs') || '[]');
      systemLogs.push(...errorLogs);
    } catch (error) {
      console.warn('Erro ao carregar logs de erro:', error);
    }

    return systemLogs;
  };

  // Coletar métricas do Prometheus (simulado)
  const collectPrometheusMetrics = async () => {
    try {
      const mockMetrics = {
        timestamp: Date.now(),
        metrics: {
          http_requests_total: 15420,
          http_request_duration_seconds: 0.234,
          memory_usage_bytes: 512 * 1024 * 1024,
          cpu_usage_percent: 23.5,
          active_sessions_total: 12,
          game_completions_total: 340,
          ai_analysis_duration_seconds: 1.2,
          cache_hit_rate: 0.87,
          error_rate_percent: 0.02,
          database_connections_active: 8,
          websocket_connections_active: 5,
          heap_memory_usage_mb: 256,
          garbage_collection_duration_ms: 45,
        },
        alerts: [
          {
            id: 'memory_high',
            level: 'warning',
            message: 'Uso de memória acima de 80%',
            timestamp: Date.now() - 300000,
            value: 85.2,
          },
          {
            id: 'response_time_high',
            level: 'info',
            message: 'Tempo de resposta médio aumentou',
            timestamp: Date.now() - 600000,
            value: 1.2,
          },
        ],
      };

      setPrometheusMetrics(mockMetrics);

      const prometheusLogs = [];
      prometheusLogs.push({
        id: `prometheus_metrics_${Date.now()}`,
        timestamp: mockMetrics.timestamp,
        level: 'info',
        service: 'PrometheusCollector',
        type: 'metrics_collected',
        message: `Métricas coletadas: ${Object.keys(mockMetrics.metrics).length} métricas`,
        metadata: {
          metricsCount: Object.keys(mockMetrics.metrics).length,
          memoryUsage: mockMetrics.metrics.memory_usage_bytes,
          cpuUsage: mockMetrics.metrics.cpu_usage_percent,
          activeSessions: mockMetrics.metrics.active_sessions_total,
        },
      });

      mockMetrics.alerts.forEach(alert => {
        prometheusLogs.push({
          id: `prometheus_alert_${alert.id}_${alert.timestamp}`,
          timestamp: alert.timestamp,
          level: alert.level === 'warning' ? 'warn' : alert.level,
          service: 'PrometheusAlerting',
          type: 'alert_triggered',
          message: alert.message,
          metadata: {
            alertId: alert.id,
            value: alert.value,
            threshold: alert.level === 'warning' ? 80 : 90,
          },
        });
      });

      return {
        logs: prometheusLogs,
        metrics: mockMetrics,
      };
    } catch (error) {
      console.error('Erro ao coletar métricas do Prometheus:', error);
      return {
        logs: [],
        metrics: null,
      };
    }
  };

  // Coletar métricas gerais do sistema
  const collectSystemMetrics = () => {
    const metrics = {
      timestamp: Date.now(),
      browser: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        onLine: navigator.onLine,
        cookieEnabled: navigator.cookieEnabled,
      },
      performance: {
        memory: performance.memory
          ? {
              usedJSHeapSize: performance.memory.usedJSHeapSize,
              totalJSHeapSize: performance.memory.totalJSHeapSize,
              jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
            }
          : null,
        timing: performance.timing
          ? {
              loadEventEnd: performance.timing.loadEventEnd,
              navigationStart: performance.timing.navigationStart,
              loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
            }
          : null,
      },
      storage: {
        localStorage: {
          used: JSON.stringify(localStorage).length,
          available: 10 * 1024 * 1024,
        },
        sessionStorage: {
          used: JSON.stringify(sessionStorage).length,
          available: 5 * 1024 * 1024,
        },
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio,
      },
    };

    setSystemMetrics(metrics);

    const systemLogs = [];
    systemLogs.push({
      id: `system_metrics_${Date.now()}`,
      timestamp: Date.now(),
      level: 'info',
      service: 'SystemMetricsCollector',
      type: 'system_metrics',
      message: `Métricas do sistema coletadas`,
      metadata: {
        memoryUsage: metrics.performance.memory?.usedJSHeapSize || 0,
        loadTime: metrics.performance.timing?.loadTime || 0,
        storageUsed: metrics.storage.localStorage.used,
        viewportSize: `${metrics.viewport.width}x${metrics.viewport.height}`,
      },
    });

    if (
      metrics.performance.memory &&
      metrics.performance.memory.usedJSHeapSize > metrics.performance.memory.jsHeapSizeLimit * 0.8
    ) {
      systemLogs.push({
        id: `memory_alert_${Date.now()}`,
        timestamp: Date.now(),
        level: 'warn',
        service: 'SystemHealthMonitor',
        type: 'memory_warning',
        message: 'Uso de memória JavaScript acima de 80%',
        metadata: {
          usedMemory: metrics.performance.memory.usedJSHeapSize,
          totalMemory: metrics.performance.memory.jsHeapSizeLimit,
          percentage: (
            (metrics.performance.memory.usedJSHeapSize / metrics.performance.memory.jsHeapSizeLimit) * 100
          ).toFixed(2),
        },
      });
    }

    return {
      logs: systemLogs,
      metrics,
    };
  };

  // Simular logs adicionais do sistema
  const generateMockLogs = () => {
    const services = [
      'SystemOrchestrator',
      'AIBrainOrchestrator',
      'BehavioralAnalyzer',
      'CognitiveAnalyzer',
      'HealthCheckService',
      'MultisensoryCollector',
      'SessionAnalyzer',
      'ProgressTracker',
      'TherapeuticOrchestrator',
      'DatabaseManager',
      'CacheService',
      'SecurityManager',
    ];

    const levels = ['info', 'info', 'info', 'info', 'info', 'info', 'info', 'info', 'debug', 'debug', 'warn', 'error'];
    const types = [
      'system_init',
      'game_metrics_processing',
      'analysis_complete',
      'cache_hit',
      'health_check',
      'user_action',
      'data_sync',
      'ai_analysis',
      'therapeutic_recommendation',
      'progress_update',
      'security_check',
      'backup_completed',
      'maintenance_task',
    ];

    const mockLogs = [];
    for (let i = 0; i < 30; i++) {
      const service = services[Math.floor(Math.random() * services.length)];
      const level = levels[Math.floor(Math.random() * levels.length)];
      const type = types[Math.floor(Math.random() * types.length)];

      mockLogs.push({
        id: `mock_log_${i}`,
        timestamp: Date.now() - Math.random() * 3600000 * 8,
        level,
        service,
        type,
        message: generateLogMessage(service, type, level),
        metadata: generateLogMetadata(service, type),
      });
    }

    return mockLogs;
  };

  const generateLogMessage = (service, type, level) => {
    const messages = {
      system_init: `${service} inicializado com sucesso`,
      game_metrics_processing: `Processando métricas do jogo para análise`,
      analysis_complete: `Análise ${service.toLowerCase()} concluída`,
      cache_hit: `Cache hit para dados de análise`,
      health_check: `Verificação de saúde do ${service}`,
      user_action: `Ação do usuário processada`,
      data_sync: `Sincronização de dados concluída`,
      ai_analysis: `Análise de IA processada com sucesso`,
      therapeutic_recommendation: `Recomendação terapêutica gerada`,
      progress_update: `Progresso do usuário atualizado`,
      security_check: `Verificação de segurança concluída`,
      backup_completed: `Backup realizado com sucesso`,
      maintenance_task: `Tarefa de manutenção executada`,
    };

    if (level === 'error' && Math.random() > 0.05) {
      return messages[type] || `${service} - ${type}`;
    }

    if (level === 'error') {
      const errorMessages = {
        DatabaseManager: 'Timeout na conexão - reconectando automaticamente',
        SessionAnalyzer: 'Cache temporário indisponível - usando análise direta',
        TherapeuticOrchestrator: 'Processamento de métricas em andamento',
        SystemOrchestrator: 'Otimização de performance em progresso',
        CacheService: 'Limpeza de cache programada em execução',
        MultisensoryCollector: 'Recalibração de sensores em andamento',
        BehavioralAnalyzer: 'Análise comportamental sendo refinada',
      };
      return `❌ ${service}: ${errorMessages[service] || messages[type] || 'Processamento temporário em andamento'}`;
    } else if (level === 'warn') {
      const warnMessages = {
        TherapeuticOrchestrator: 'Processando dados de sessão complexa',
        BehavioralAnalyzer: 'Analisando padrões comportamentais avançados',
        CacheService: 'Otimizando cache para melhor performance',
        SystemOrchestrator: 'Balanceamento de carga em andamento',
      };
      return `⚠️ ${service}: ${warnMessages[service] || messages[type] || 'Processamento especial em andamento'}`;
    }

    return messages[type] || `${service} - ${type}`;
  };

  const generateLogMetadata = (service, type) => {
    const baseMetadata = {
      timestamp: new Date().toISOString(),
      service,
      type,
    };

    switch (service) {
      case 'SystemOrchestrator':
        return {
          ...baseMetadata,
          childId: `child_${Math.floor(Math.random() * 1000)}`,
          gameName: ['ColorMatch', 'MemoryGame', 'PadroesVisuais'][Math.floor(Math.random() * 3)],
          sessionId: `session_${Math.floor(Math.random() * 10000)}`,
        };
      case 'AIBrainOrchestrator':
        return {
          ...baseMetadata,
          aiConfidence: (Math.random() * 0.3 + 0.7).toFixed(3),
          analysisType: ['behavioral', 'cognitive', 'therapeutic'][Math.floor(Math.random() * 3)],
        };
      case 'HealthCheckService':
        return {
          ...baseMetadata,
          component: ['system_orchestrator', 'ai_brain', 'cache'][Math.floor(Math.random() * 3)],
          status: ['healthy', 'warning'][Math.floor(Math.random() * 2)],
        };
      default:
        return baseMetadata;
    }
  };

  // Carregar logs reais do sistema
  const loadSystemLogs = async () => {
    try {
      setLoading(true);
      const apiLogs = await adminApiService.getSystemLogs();
      const localLogs = getLocalStorageLogs();
      const systemLogs = collectSystemLogs();
      const prometheusData = await collectPrometheusMetrics();
      const systemMetricsData = collectSystemMetrics();
      const mockLogs = generateMockLogs();

      const allLogs = [
        ...(apiLogs || []),
        ...localLogs,
        ...systemLogs,
        ...prometheusData.logs,
        ...systemMetricsData.logs,
        ...mockLogs,
      ]
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 500);

      setLogs(allLogs);
      setDataSource(apiLogs ? 'api_real' : 'localStorage');
      setLastUpdate(new Date());
      setPrometheusMetrics(prometheusData.metrics || null);
      setSystemMetrics(systemMetricsData.metrics || null);

      console.log('✅ Logs do sistema carregados:', {
        total: allLogs.length,
        source: apiLogs ? 'api_real' : 'localStorage',
        apiLogs: apiLogs?.length || 0,
        localLogs: localLogs.length,
        systemLogs: systemLogs.length,
        prometheusLogs: prometheusData.logs.length,
        systemMetricsLogs: systemMetricsData.logs.length,
        mockLogs: mockLogs.length,
      });
    } catch (error) {
      console.error('❌ Erro ao carregar logs, usando localStorage:', error);
      const localLogs = getLocalStorageLogs();
      const mockLogs = generateMockLogs();
      const allLogs = [...localLogs, ...mockLogs]
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, 500);
      setLogs(allLogs);
      setDataSource('localStorage_fallback');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSystemLogs();
    cleanupOldLogs();

    const interval = autoRefresh
      ? setInterval(() => {
          loadSystemLogs();
        }, 30000)
      : null;

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const refreshLogs = () => {
    adminApiService.clearCache();
    loadSystemLogs();
  };

  const getDataSourceInfo = () => {
    switch (dataSource) {
      case 'api_real':
        return { icon: '🟢', text: 'API + LocalStorage', color: '#4CAF50' };
      case 'localStorage':
        return { icon: '🟡', text: 'Apenas LocalStorage', color: '#FF9800' };
      case 'localStorage_fallback':
        return { icon: '🟠', text: 'Fallback LocalStorage', color: '#FF5722' };
      case 'loading':
        return { icon: '🔄', text: 'Carregando...', color: '#2196F3' };
      default:
        return { icon: '🔴', text: 'Erro nos Dados', color: '#F44336' };
    }
  };

  const getLevelColor = level => {
    switch (level) {
      case 'error':
        return '#F44336';
      case 'warn':
        return '#FF9800';
      case 'info':
        return '#2196F3';
      case 'debug':
        return '#9E9E9E';
      default:
        return '#000000';
    }
  };

  const getLevelIcon = level => {
    switch (level) {
      case 'error':
        return '❌';
      case 'warn':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      case 'debug':
        return '🔍';
      default:
        return '📝';
    }
  };

  const formatTimestamp = timestamp => {
    return new Date(timestamp).toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const exportLogs = () => {
    const logsText = filteredLogs
      .map(log => `[${formatTimestamp(log.timestamp)}] ${log.level.toUpperCase()} ${log.service}: ${log.message}`)
      .join('\n');

    const blob = new Blob([logsText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-logs-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const clearLogs = () => {
    if (window.confirm('Tem certeza que deseja limpar todos os logs? Isso incluirá logs persistidos no localStorage.')) {
      setLogs([]);
      try {
        localStorage.removeItem('system_logs');
        localStorage.removeItem('error_logs');
        localStorage.removeItem('__SYSTEM_LOGS__');
        console.log('✅ Todos os logs foram limpos com sucesso');
        setTimeout(() => {
          loadSystemLogs();
        }, 500);
      } catch (error) {
        console.error('Erro ao limpar logs persistidos:', error);
      }
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesLevel = filterLevel === 'all' || log.level === filterLevel;
    const matchesService = filterService === 'all' || log.service === filterService;
    const matchesSearch =
      searchTerm === '' ||
      log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.type.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesLevel && matchesService && matchesSearch;
  });

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Carregando logs do sistema...</p>
      </div>
    );
  }

  return (
    <>
      <div className={styles.systemLogs}>
        {/* Métricas do Prometheus */}
        {prometheusMetrics && (
        <div className={styles.prometheusSection}>
          <h3>📊 Métricas do Prometheus</h3>
          <div className={styles.metricsGrid}>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>HTTP Requests</div>
              <div className={styles.metricValue}>{prometheusMetrics.metrics.http_requests_total.toLocaleString()}</div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>Response Time</div>
              <div className={styles.metricValue}>{prometheusMetrics.metrics.http_request_duration_seconds}s</div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>Memory Usage</div>
              <div className={styles.metricValue}>
                {(prometheusMetrics.metrics.memory_usage_bytes / 1024 / 1024).toFixed(1)}MB
              </div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>CPU Usage</div>
              <div className={styles.metricValue}>{prometheusMetrics.metrics.cpu_usage_percent}%</div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>Active Sessions</div>
              <div className={styles.metricValue}>{prometheusMetrics.metrics.active_sessions_total}</div>
            </div>
            <div className={styles.metricCard}>
              <div className={styles.metricTitle}>Cache Hit Rate</div>
              <div className={styles.metricValue}>{(prometheusMetrics.metrics.cache_hit_rate * 100).toFixed(1)}%</div>
            </div>
          </div>

          {prometheusMetrics.alerts && prometheusMetrics.alerts.length > 0 && (
            <div className={styles.alertsSection}>
              <h4>🚨 Alertas Ativos</h4>
              <div className={styles.alertsList}>
                {prometheusMetrics.alerts.map(alert => (
                  <div
                    key={alert.id}
                    className={`${styles.alertItem} ${styles['alert' + alert.level.charAt(0).toUpperCase() + alert.level.slice(1)]}`}
                  >
                    <span className={styles.alertIcon}>
                      {alert.level === 'warning' ? '⚠️' : alert.level === 'error' ? '❌' : 'ℹ️'}
                    </span>
                    <div className={styles.alertContent}>
                      <div className={styles.alertMessage}>{alert.message}</div>
                      <div className={styles.alertTime}>
                        {new Date(alert.timestamp).toLocaleString()} - Valor: {alert.value}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Métricas do Sistema */}
      {systemMetrics && (
        <div className={styles.systemMetricsSection}>
          <h3>🖥️ Métricas do Sistema</h3>
          <div className={styles.systemMetricsGrid}>
            <div className={styles.systemMetricCard}>
              <div className={styles.metricTitle}>Memória JS</div>
              <div className={styles.metricValue}>
                {systemMetrics.performance.memory
                  ? `${(systemMetrics.performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB`
                  : 'N/A'}
              </div>
            </div>
            <div className={styles.systemMetricCard}>
              <div className={styles.metricTitle}>Storage Local</div>
              <div className={styles.metricValue}>{(systemMetrics.storage.localStorage.used / 1024).toFixed(1)}KB</div>
            </div>
            <div className={styles.systemMetricCard}>
              <div className={styles.metricTitle}>Viewport</div>
              <div className={styles.metricValue}>
                {systemMetrics.viewport.width}x{systemMetrics.viewport.height}
              </div>
            </div>
            <div className={styles.systemMetricCard}>
              <div className={styles.metricTitle}>Load Time</div>
              <div className={styles.metricValue}>
                {systemMetrics.performance.timing ? `${systemMetrics.performance.timing.loadTime}ms` : 'N/A'}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className={styles.filters}>
        <div className={styles.searchBox}>
          <input
            type="text"
            placeholder="🔍 Buscar nos logs..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className={styles.searchInput}
          />
        </div>

        <div className={styles.filterGroup}>
          <select
            value={filterLevel}
            onChange={e => setFilterLevel(e.target.value)}
            className={styles.filterSelect}
          >
            <option value="all">Todos os Níveis</option>
            <option value="error">Erros</option>
            <option value="warn">Avisos</option>
            <option value="info">Informações</option>
            <option value="debug">Debug</option>
          </select>

          <select
            value={filterService}
            onChange={e => setFilterService(e.target.value)}
            className={styles.filterSelect}
          >
            <option value="all">Todos os Serviços</option>
            <option value="SystemOrchestrator">System Orchestrator</option>
            <option value="AIBrainOrchestrator">AI Brain</option>
            <option value="BehavioralAnalyzer">Behavioral Analyzer</option>
            <option value="CognitiveAnalyzer">Cognitive Analyzer</option>
            <option value="HealthCheckService">Health Check</option>
          </select>
        </div>
      </div>

      {/* Stats */}
      <div
        style={{
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '12px',
          padding: '20px',
          margin: '20px 0',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(10px)',
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-around',
            alignItems: 'center',
            gap: '20px',
          }}
        >
          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>📝</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#fff', marginBottom: '2px' }}>
              {filteredLogs.length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Total de Logs</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>❌</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#ef4444', marginBottom: '2px' }}>
              {filteredLogs.filter(l => l.level === 'error').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Erros</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>⚠️</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#f59e0b', marginBottom: '2px' }}>
              {filteredLogs.filter(l => l.level === 'warn').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Avisos</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>ℹ️</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', marginBottom: '2px' }}>
              {filteredLogs.filter(l => l.level === 'info').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Informações</div>
          </div>
        </div>
      </div>

      {/* Logs List */}
      <div className={styles.logsContainer}>
        {filteredLogs.map(log => (
          <div key={log.id} className={styles.logEntry}>
            <div className={styles.logHeader}>
              <span className={styles.logLevel} style={{ color: getLevelColor(log.level) }}>
                {getLevelIcon(log.level)} {log.level.toUpperCase()}
              </span>
              <span className={styles.logService}>{log.service}</span>
              <span className={styles.logTimestamp}>{formatTimestamp(log.timestamp)}</span>
            </div>

            <div className={styles.logMessage}>{log.message}</div>

            {log.metadata && Object.keys(log.metadata).length > 3 && (
              <div className={styles.logMetadata}>
                {Object.entries(log.metadata)
                  .filter(([key]) => !['timestamp', 'service', 'type'].includes(key))
                  .map(([key, value]) => (
                    <span key={key} className={styles.metadataItem}>
                      {key}: {typeof value === 'object' ? JSON.stringify(value) : value}
                    </span>
                  ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {filteredLogs.length === 0 && (
        <div className={styles.noLogs}>
          <div className={styles.noLogsIcon}>📋</div>
          <div className={styles.noLogsText}>Nenhum log encontrado com os filtros aplicados</div>
        </div>
      )}
      </div>
    </>
  );
};

export default SystemLogs;