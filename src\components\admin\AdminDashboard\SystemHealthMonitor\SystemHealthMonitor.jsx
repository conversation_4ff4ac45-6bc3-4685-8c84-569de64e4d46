/**
 * @file SystemHealthMonitor.jsx
 * @description Monitor de Saúde do Sistema - Área Administrativa
 * @version 3.0.0
 * @admin true
 * @datasource API Real + Fallback
 */

import React, { useState, useEffect } from 'react'
import adminApiService from '../../../../services/adminApiService'
import styles from './SystemHealthMonitor.module.css'

const SystemHealthMonitor = () => {
  const [healthData, setHealthData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [lastUpdate, setLastUpdate] = useState(new Date())
  const [dataSource, setDataSource] = useState('loading')

  // Carregar dados reais de saúde do sistema
  const loadHealthData = async () => {
    try {
      setLoading(true)
      const data = await adminApiService.getSystemHealthData()
      
      setHealthData(data)
      setDataSource('api_real')
      setLastUpdate(new Date())
      
      console.log('✅ Dados de saúde do sistema carregados da API real:', data)
    } catch (error) {
      console.error('❌ Erro ao carregar dados de saúde:', error)
      setDataSource('fallback')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadHealthData()
    const interval = setInterval(loadHealthData, 30000) // Atualizar a cada 30s
    return () => clearInterval(interval)
  }, [])

  // Função para forçar atualização dos dados
  const refreshData = () => {
    adminApiService.clearCache()
    loadHealthData()
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'healthy': return '#4CAF50'
      case 'warning': return '#FF9800'
      case 'unhealthy': return '#F44336'
      default: return '#9E9E9E'
    }
  }

  const getDataSourceInfo = () => {
    switch (dataSource) {
      case 'api_real':
        return { icon: '🟢', text: 'Dados Reais da API', color: '#4CAF50' }
      case 'fallback':
        return { icon: '🟡', text: 'Dados de Fallback', color: '#FF9800' }
      case 'loading':
        return { icon: '🔄', text: 'Carregando...', color: '#2196F3' }
      default:
        return { icon: '🔴', text: 'Erro nos Dados', color: '#F44336' }
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Carregando dados do sistema...</p>
      </div>
    )
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'healthy': return '✅'
      case 'warning': return '⚠️'
      case 'unhealthy': return '❌'
      default: return '❓'
    }
  }

  const formatUptime = (uptime) => {
    const hours = Math.floor(uptime / 3600000)
    const minutes = Math.floor((uptime % 3600000) / 60000)
    return `${hours}h ${minutes}m`
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Carregando dados de saúde do sistema...</p>
      </div>
    )
  }

  return (
    <div className={styles.healthMonitor}>
      {/* Components Grid */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px', margin: '20px 0' }}>
        {healthData?.components && Object.entries(healthData.components).map(([name, component]) => (
          <div key={name} style={{
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
            padding: '20px',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            backdropFilter: 'blur(10px)',
            transition: 'transform 0.2s ease',
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: '15px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                <span style={{ fontSize: '28px' }}>
                  {getStatusIcon(component.status)}
                </span>
                <h3 style={{ 
                  margin: 0, 
                  fontSize: '20px', 
                  fontWeight: 'bold', 
                  color: '#fff',
                  textTransform: 'uppercase'
                }}>
                  {name.replace(/_/g, ' ')}
                </h3>
              </div>
              <span style={{ 
                color: getStatusColor(component.status),
                fontSize: '16px',
                fontWeight: 'bold',
                textTransform: 'lowercase'
              }}>
                {component.status}
              </span>
            </div>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '10px' }}>
              {component?.metrics && Object.entries(component.metrics).map(([key, value]) => (
                <div key={key} style={{
                  background: 'rgba(0, 0, 0, 0.2)',
                  borderRadius: '8px',
                  padding: '8px 12px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '2px'
                }}>
                  <span style={{ 
                    fontSize: '12px', 
                    color: '#ccc',
                    textTransform: 'lowercase'
                  }}>
                    {key.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                  </span>
                  <span style={{ 
                    fontSize: '16px', 
                    fontWeight: 'bold', 
                    color: '#fff' 
                  }}>
                    {typeof value === 'number' && key.includes('Time') 
                      ? formatUptime(Date.now() - value)
                      : typeof value === 'boolean'
                      ? value ? '✅' : '❌'
                      : value
                    }
                  </span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* System Metrics Summary */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '12px',
        padding: '20px',
        margin: '20px 0',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        backdropFilter: 'blur(10px)'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          gap: '20px'
        }}>
          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>🖥️</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#fff', marginBottom: '2px' }}>
              {Object.keys(healthData.components).length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Componentes</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>✅</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#10b981', marginBottom: '2px' }}>
              {Object.values(healthData.components).filter(c => c.status === 'healthy').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Saudáveis</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>⚠️</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#f59e0b', marginBottom: '2px' }}>
              {Object.values(healthData.components).filter(c => c.status === 'warning').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Avisos</div>
          </div>

          <div style={{ textAlign: 'center', flex: 1 }}>
            <div style={{ fontSize: '24px', marginBottom: '5px' }}>❌</div>
            <div style={{ fontSize: '28px', fontWeight: 'bold', color: '#ef4444', marginBottom: '2px' }}>
              {Object.values(healthData.components).filter(c => c.status === 'unhealthy').length}
            </div>
            <div style={{ fontSize: '12px', color: '#ccc' }}>Problemas</div>
          </div>
        </div>
      </div>

      {/* Cache Performance */}
      {healthData.components.intelligent_cache && (
        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          borderRadius: '12px',
          padding: '20px',
          margin: '20px 0',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          backdropFilter: 'blur(10px)'
        }}>
          <div style={{ marginBottom: '15px', fontSize: '16px', fontWeight: 'bold', color: '#fff' }}>
            💾 Performance do Cache
          </div>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            gap: '20px'
          }}>
            <div style={{ flex: 2 }}>
              <div style={{ fontSize: '14px', color: '#ccc', marginBottom: '8px' }}>
                Cache Inteligente
              </div>
              <div style={{
                background: 'rgba(0, 0, 0, 0.3)',
                borderRadius: '8px',
                height: '8px',
                overflow: 'hidden'
              }}>
                <div style={{
                  background: 'linear-gradient(90deg, #10b981, #06d6a0)',
                  height: '100%',
                  borderRadius: '8px',
                  width: `${parseFloat(healthData.components.intelligent_cache.metrics.hitRate) * 100}%`
                }}></div>
              </div>
            </div>
            
            <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#10b981' }}>
                  {healthData.components.intelligent_cache.metrics.hitRate}
                </div>
                <div style={{ fontSize: '10px', color: '#ccc' }}>Hit Rate</div>
              </div>
              
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#fff' }}>
                  {healthData.components.intelligent_cache.metrics.hits}
                </div>
                <div style={{ fontSize: '10px', color: '#ccc' }}>Hits</div>
              </div>
              
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#f59e0b' }}>
                  {healthData.components.intelligent_cache.metrics.misses}
                </div>
                <div style={{ fontSize: '10px', color: '#ccc' }}>Misses</div>
              </div>
              
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#6366f1' }}>
                  {healthData.components.intelligent_cache.metrics.size}/{healthData.components.intelligent_cache.metrics.maxSize}
                </div>
                <div style={{ fontSize: '10px', color: '#ccc' }}>Size</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export { SystemHealthMonitor }
export default SystemHealthMonitor
