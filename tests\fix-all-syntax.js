#!/usr/bin/env node

/**
 * 🛠️ CORRETOR AUTOMÁTICO DE SINTAXE USECALLBACK
 * Script para corrigir todos os erros de sintaxe dos useCallback de uma vez
 */

import fs from 'fs';
import path from 'path';

const GAMES_DIRECTORY = './src/games';

// Padrões problemáticos que precisam ser corrigidos
const fixPatterns = [
  // Padrão 1: }, [deps])); -> }, [deps]);
  {
    regex: /}, \[([^\]]*)\]\);?\);/g,
    replacement: '}, [$1]);'
  },
  
  // Padrão 2: dependências no lugar errado dentro de chamadas de função
  {
    regex: /(\w+)\([^)]*\), \[([^\]]*)\]\)/g,
    replacement: (match, funcName, deps, offset, string) => {
      // Verificar se está dentro de um useCallback
      const beforeMatch = string.substring(0, offset);
      const useCallbackIndex = beforeMatch.lastIndexOf('useCallback(');
      if (useCallbackIndex !== -1) {
        // Remover as dependências do lugar errado
        return match.replace(`, [${deps}])`, ')');
      }
      return match;
    }
  },
  
  // Padrão 3: }; -> }, [deps]); quando é useCallback
  {
    regex: /(const \w+ = useCallback\([^}]+\});(?!\s*,\s*\[)/g,
    replacement: '$1, []);'
  }
];

// Função para corrigir sintaxe de um arquivo
const fixFile = (filePath) => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let modified = false;
    
    console.log(`🔧 Analisando: ${path.basename(filePath)}`);
    
    // Aplicar correções específicas conhecidas
    const knownFixes = [
      // MusicalSequenceGame - handleBackToMenu
      {
        search: /difficulty: difficulty,\s+timestamp: Date\.now\(\)\s+}, \[onBack\]\);/g,
        replace: 'difficulty: difficulty,\n        timestamp: Date.now()\n      };\n      \n      MusicalSequenceMetrics.recordAdvancedInteraction(gameEndData);'
      },
      
      // Funções com sintaxe incorreta do useCallback
      {
        search: /}, \[([^\]]*)\]\)\);/g,
        replace: '}, [$1]);'
      },
      
      // Remove dependências de dentro de chamadas de função
      {
        search: /(\w+\([^)]*), \[([^\]]*)\]\)/g,
        replace: '$1)'
      },
      
      // Fecha useCallback sem dependências corretamente
      {
        search: /(const \w+ = useCallback\([^}]+\});(?!\s*,\s*\[)/g,
        replace: '$1, []);'
      }
    ];
    
    knownFixes.forEach(fix => {
      const beforeFix = content;
      content = content.replace(fix.search, fix.replace);
      if (content !== beforeFix) {
        modified = true;
        console.log(`  ✅ Aplicada correção automática`);
      }
    });
    
    // Correções específicas por arquivo
    const filename = path.basename(filePath);
    
    if (filename.includes('MusicalSequence')) {
      // Corrigir handleBackToMenu especificamente
      content = content.replace(
        /difficulty: difficulty,\s+timestamp: Date\.now\(\)\s+}, \[onBack\]\);/,
        'difficulty: difficulty,\n        timestamp: Date.now()\n      };\n      \n      MusicalSequenceMetrics.recordAdvancedInteraction(gameEndData);'
      );
      
      // Adicionar fechamento correto para handleBackToMenu
      if (content.includes('setShowStartScreen(true);') && !content.includes('}, [gameStartTime, score')) {
        content = content.replace(
          'setShowStartScreen(true);\n    onBack();\n  };',
          'setShowStartScreen(true);\n    onBack();\n  }, [gameStartTime, score, currentLevel, currentRound, totalAttempts, correctAttempts, streak, difficulty, orchestratorReady, sendMetrics, onBack]);'
        );
        modified = true;
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  💾 Arquivo corrigido`);
      return true;
    } else {
      console.log(`  ℹ️ Nenhuma correção necessária`);
      return false;
    }
    
  } catch (error) {
    console.error(`❌ Erro ao processar ${filePath}:`, error.message);
    return false;
  }
};

// Função principal
const main = () => {
  console.log('🚨 CORRETOR AUTOMÁTICO DE SINTAXE USECALLBACK');
  console.log('==============================================');
  
  const gameFiles = [
    './src/games/MusicalSequence/MusicalSequenceGame.jsx',
    './src/games/ColorMatch/ColorMatchGame.jsx', 
    './src/games/MemoryGame/MemoryGame.jsx',
    './src/games/PadroesVisuais/PadroesVisuaisGame.jsx',
    './src/games/QuebraCabeca/QuebraCabecaGame.jsx',
    './src/games/ContagemNumeros/ContagemNumerosGame.jsx',
    './src/games/LetterRecognition/LetterRecognitionGame.jsx',
    './src/games/ImageAssociation/ImageAssociationGame.jsx'
  ];
  
  let totalFixed = 0;
  
  gameFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const wasFixed = fixFile(file);
      if (wasFixed) totalFixed++;
    } else {
      console.log(`⚠️ Arquivo não encontrado: ${file}`);
    }
  });
  
  console.log('\n✨ CORREÇÃO CONCLUÍDA');
  console.log(`📊 Total de arquivos corrigidos: ${totalFixed}`);
  console.log('\n🔧 Próximos passos:');
  console.log('1. Executar build: npm run build');
  console.log('2. Se houver erros, corrigir manualmente os restantes');
};

main();
