const e=0,t=1,r=2,i=3,s=4;class o{constructor(e="MultisensoryMetrics",r=t){this.module=e,this.minLevel=r,this.errorHistory=[],this.maxErrors=50}_formatMessage(e,t,r){const i=(new Date).toISOString(),s=this._getLevelIcon(e),o=r?`: ${JSON.stringify(r)}`:"";return`${s} [${i}][${this.module}] ${t}${o}`}_getLevelIcon(o){switch(o){case e:return"🔍";case t:return"ℹ️";case r:return"⚠️";case i:return"❌";case s:return"🚨";default:return"📝"}}debug(e,t){this.minLevel}info(e,t){this.minLevel}warn(e,t){this.minLevel}error(e,t,r){this.minLevel<=i&&(this.errorHistory.push({message:e,error:t?.message||t,stack:t?.stack,data:r,timestamp:(new Date).toISOString()}),this.errorHistory.length>this.maxErrors&&(this.errorHistory=this.errorHistory.slice(-this.maxErrors)))}critical(e,t,r){this.minLevel<=s&&(this.errorHistory.push({message:e,error:t?.message||t,stack:t?.stack,data:r,timestamp:(new Date).toISOString(),isCritical:!0}),this.errorHistory.length>this.maxErrors&&(this.errorHistory=this.errorHistory.slice(-this.maxErrors)))}getErrorHistory(){return[...this.errorHistory]}}class n{constructor(){this.logger=new o("MultisensoryMetricsCollector"),this.sessionId=null,this.userId=null,this.isCollecting=!1,this.sensorData=[],this.collectionStartTime=null,this.deviceInfo=null,this.sensorHandlers={accelerometer:new c(this),gyroscope:new l(this),touch:new h(this),geolocation:new u(this),device:new d(this)},this.neurodivergencePatterns={repetitiveMovements:0,selfRegulation:0,sensorySeekingLevel:0,anxietyIndicators:0,stimmingDetection:!1},this.sensorConfig={accelerometer:{enabled:!1,frequency:10},gyroscope:{enabled:!1,frequency:10},touchMetrics:{enabled:!0,trackPressure:!0},geolocation:{enabled:!1,accuracy:"high"},deviceContext:{enabled:!0,trackBattery:!0}},this.dataBuffer={accelerometer:[],gyroscope:[],touch:[],maxBufferSize:100,flushThreshold:50},this.throttleConfig={lastProcessTime:0,minInterval:200},this.performanceMetrics={dataPoints:0,processedBatches:0,totalProcessingTime:0,averageProcessingTime:0}}async startMetricsCollection(e,t,r={}){try{if(!e||"string"!=typeof e)throw new Error("Invalid sessionId: must be a non-empty string");if(!t||"string"!=typeof t)throw new Error("Invalid userId: must be a non-empty string");return this.logger.info("Starting metrics collection",{sessionId:e,userId:t}),this.sensorData=[],this.dataBuffer={accelerometer:[],gyroscope:[],touch:[],maxBufferSize:r.maxBufferSize||100,flushThreshold:r.flushThreshold||50},this.sessionId=e,this.userId=t,this.isCollecting=!0,this.collectionStartTime=(new Date).toISOString(),this.sensorData=[],this.logger.info("Iniciando coleta de métricas",{sessionId:e,userId:t}),await this._detectDeviceCapabilities(),await this._startSensorCollection(),this.logger.info("Coleta iniciada com sucesso",{sessionId:e,sensorsEnabled:this._getEnabledSensors(),deviceCapabilities:this.deviceInfo}),{success:!0,sessionId:e,sensorsEnabled:this._getEnabledSensors(),deviceCapabilities:this.deviceInfo}}catch(i){return this.logger.error("Erro ao iniciar coleta de métricas",i),{success:!1,error:i.message}}}async stopMetricsCollection(){try{if(!this.isCollecting)return{success:!1,error:"Collection not active"};this.isCollecting=!1;const e=(new Date).toISOString();this.logger.info("Parando coleta de métricas",{sessionId:this.sessionId}),await this._stopSensorCollection();const t=this._analyzeCollectedData(),r={sessionId:this.sessionId,userId:this.userId,startTime:this.collectionStartTime,endTime:e,duration:new Date(e)-new Date(this.collectionStartTime),sensorData:this.sensorData,neurodivergencePatterns:this.neurodivergencePatterns,analysis:t,deviceInfo:this.deviceInfo};return this.logger.info("Coleta parada e relatório gerado",{sessionId:this.sessionId,totalDataPoints:this.sensorData.length,analysis:t}),{success:!0,report:r,sessionId:this.sessionId,totalDataPoints:this.sensorData.length,analysis:t}}catch(e){return this.logger.error("Erro ao parar coleta de métricas",e),{success:!1,error:e.message}}}async getCurrentMetrics(){return this.isCollecting?{timestamp:(new Date).toISOString(),mobileSensors:await this._getCurrentSensorReadings(),neurodivergencePatterns:{...this.neurodivergencePatterns},deviceContext:await this._getDeviceContext()}:null}async _detectDeviceCapabilities(){"undefined"!=typeof window&&"undefined"!=typeof navigator?(this.deviceInfo={userAgent:navigator.userAgent,platform:navigator.platform,isMobile:/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),sensors:{accelerometer:"DeviceMotionEvent"in window,gyroscope:"DeviceOrientationEvent"in window,geolocation:"geolocation"in navigator,touch:"ontouchstart"in window},capabilities:{vibration:"vibrate"in navigator,notifications:"Notification"in window,battery:"getBattery"in navigator}},this.sensorConfig.accelerometer.enabled=this.deviceInfo.sensors.accelerometer,this.sensorConfig.gyroscope.enabled=this.deviceInfo.sensors.gyroscope,this.sensorConfig.geolocation.enabled=this.deviceInfo.sensors.geolocation):(this.deviceInfo={userAgent:"Node.js Test Environment",platform:"Node.js",isMobile:!1,sensors:{accelerometer:!1,gyroscope:!1,geolocation:!1,touch:!1},capabilities:{vibration:!1,notifications:!1,battery:!1}},this.sensorConfig.accelerometer.enabled=!0,this.sensorConfig.gyroscope.enabled=!0,this.sensorConfig.geolocation.enabled=!0)}async _startSensorCollection(){"undefined"!=typeof window?(this.sensorConfig.accelerometer.enabled&&window.addEventListener("devicemotion",this._handleDeviceMotion.bind(this)),this.sensorConfig.gyroscope.enabled&&window.addEventListener("deviceorientation",this._handleDeviceOrientation.bind(this)),this.sensorConfig.touchMetrics.enabled&&(document.addEventListener("touchstart",this._handleTouchStart.bind(this)),document.addEventListener("touchmove",this._handleTouchMove.bind(this)),document.addEventListener("touchend",this._handleTouchEnd.bind(this))),this.sensorConfig.deviceContext.enabled&&setInterval(()=>this._collectDeviceContext(),5e3)):(this.logger.info("Ambiente Node.js detectado - simulando coleta de sensores"),this._simulateSensorData())}_simulateSensorData(){if(this.sensorConfig.accelerometer.enabled){const e={x:2*Math.random()-1,y:2*Math.random()-1,z:2*Math.random()-1,timestamp:Date.now()};this._handleDeviceMotion({acceleration:e})}if(this.sensorConfig.gyroscope.enabled){const e={alpha:360*Math.random(),beta:360*Math.random(),gamma:360*Math.random(),timestamp:Date.now()};this._handleDeviceOrientation(e)}}async _stopSensorCollection(){"undefined"!=typeof window&&"undefined"!=typeof document?(window.removeEventListener("devicemotion",this._handleDeviceMotion.bind(this)),window.removeEventListener("deviceorientation",this._handleDeviceOrientation.bind(this)),document.removeEventListener("touchstart",this._handleTouchStart.bind(this)),document.removeEventListener("touchmove",this._handleTouchMove.bind(this)),document.removeEventListener("touchend",this._handleTouchEnd.bind(this))):this.logger.info("Parando coleta de sensores simulada")}_handleDeviceMotion(e){if(!this.isCollecting)return;const t={type:"devicemotion",timestamp:(new Date).toISOString(),acceleration:{x:e.acceleration?.x||0,y:e.acceleration?.y||0,z:e.acceleration?.z||0},accelerationIncludingGravity:{x:e.accelerationIncludingGravity?.x||0,y:e.accelerationIncludingGravity?.y||0,z:e.accelerationIncludingGravity?.z||0},rotationRate:{alpha:e.rotationRate?.alpha||0,beta:e.rotationRate?.beta||0,gamma:e.rotationRate?.gamma||0}};this.sensorData.push(t),this._analyzeMovementPatterns(t)}_handleDeviceOrientation(e){if(!this.isCollecting)return;const t={type:"deviceorientation",timestamp:(new Date).toISOString(),alpha:e.alpha||0,beta:e.beta||0,gamma:e.gamma||0,absolute:e.absolute||!1};this.sensorData.push(t)}_handleTouchStart(e){this.isCollecting&&Array.from(e.touches).forEach(e=>{const t={type:"touchstart",timestamp:(new Date).toISOString(),identifier:e.identifier,x:e.clientX,y:e.clientY,force:e.force||0,radiusX:e.radiusX||0,radiusY:e.radiusY||0};this.sensorData.push(t)})}_handleTouchMove(e){this.isCollecting&&Array.from(e.touches).forEach(e=>{const t={type:"touchmove",timestamp:(new Date).toISOString(),identifier:e.identifier,x:e.clientX,y:e.clientY,force:e.force||0};this.sensorData.push(t)})}_handleTouchEnd(e){this.isCollecting&&Array.from(e.changedTouches).forEach(e=>{const t={type:"touchend",timestamp:(new Date).toISOString(),identifier:e.identifier,x:e.clientX,y:e.clientY};this.sensorData.push(t)})}_analyzeMovementPatterns(e){Math.sqrt(e.acceleration.x**2+e.acceleration.y**2+e.acceleration.z**2)>2&&(this.neurodivergencePatterns.repetitiveMovements+=.1,this.neurodivergencePatterns.stimmingDetection=!0),this.neurodivergencePatterns.repetitiveMovements=Math.min(this.neurodivergencePatterns.repetitiveMovements,10)}async _collectDeviceContext(){if(!this.isCollecting)return;const e={type:"devicecontext",timestamp:(new Date).toISOString(),battery:await this._getBatteryInfo(),network:navigator.connection?{effectiveType:navigator.connection.effectiveType,downlink:navigator.connection.downlink,rtt:navigator.connection.rtt}:null,time:{hour:(new Date).getHours(),dayOfWeek:(new Date).getDay()}};this.sensorData.push(e)}async _getBatteryInfo(){try{if("getBattery"in navigator){const e=await navigator.getBattery();return{level:e.level,charging:e.charging,chargingTime:e.chargingTime,dischargingTime:e.dischargingTime}}}catch(e){}return null}async _getCurrentSensorReadings(){const e=this.sensorData.slice(-10);return{accelerometer:e.filter(e=>"devicemotion"===e.type).slice(-1)[0]||null,gyroscope:e.filter(e=>"deviceorientation"===e.type).slice(-1)[0]||null,touch:e.filter(e=>e.type.startsWith("touch")).slice(-3)||[],count:e.length}}async _getDeviceContext(){return{battery:await this._getBatteryInfo(),timestamp:(new Date).toISOString(),isActive:this.isCollecting}}_getEnabledSensors(){return Object.entries(this.sensorConfig).filter(([e,t])=>t.enabled).map(([e,t])=>e)}_analyzeCollectedData(){const e=this.sensorData.length,t={};this.sensorData.forEach(e=>{t[e.type]=(t[e.type]||0)+1});const r=this.sensorData.filter(e=>e.type.startsWith("touch")),i=this._analyzeTouchPatterns(r);return{totalEvents:e,eventTypes:t,touchAnalysis:i,duration:this.collectionStartTime?new Date-new Date(this.collectionStartTime):0,neurodivergenceIndicators:{...this.neurodivergencePatterns}}}_analyzeTouchPatterns(e){if(0===e.length)return{touches:0,avgPressure:0,patterns:[]};const t=e.filter(e=>void 0!==e.force).map(e=>e.force),r=t.length>0?t.reduce((e,t)=>e+t,0)/t.length:0;return{touches:e.length,avgPressure:r,maxPressure:Math.max(...t,0),touchTypes:{start:e.filter(e=>"touchstart"===e.type).length,move:e.filter(e=>"touchmove"===e.type).length,end:e.filter(e=>"touchend"===e.type).length}}}async processMultisensoryData(e,t){performance.now();try{if(!e||"object"!=typeof e)throw new Error("Invalid normalizedMetrics: must be a valid object");if(!t||!Array.isArray(t)||0===t.length)throw new Error("Invalid multisensoryData: must be a non-empty array");const r=["gameId","sessionId","userId"];for(const t of r)if(!e[t])throw new Error(`Missing required field in normalizedMetrics: ${t}`);this.logger.info("Processando dados multissensoriais...",{sessionId:this.sessionId,metricsCount:t.length,gameId:e.gameId});const i=Date.now();i-this.throttleConfig.lastProcessTime<this.throttleConfig.minInterval&&(this.logger.debug("Throttling applied to processMultisensoryData"),await new Promise(e=>setTimeout(e,this.throttleConfig.minInterval))),this.throttleConfig.lastProcessTime=i;const s=await this._analyzeSensorData(t),o=this._analyzeNeurodivergencePatterns(t),n=this._correlateWithGameMetrics(e,t);return this.logger.info("Processamento concluído",{sessionId:this.sessionId,sensorAnalysis:s,neurodivergenceAnalysis:o,correlation:n}),{timestamp:(new Date).toISOString(),sensorData:s,neurodivergencePatterns:o,gameCorrelation:n,deviceContext:await this._getDeviceContext(),confidence:this._calculateAnalysisConfidence(s,o)}}catch(r){return this.logger.error("Erro ao processar dados multissensoriais",r),{error:!0,errorMessage:r.message,timestamp:(new Date).toISOString()}}}async _analyzeSensorData(e){const t={accelerometer:null,gyroscope:null,touch:null,context:null};return e.accelerometer&&(t.accelerometer={avgMagnitude:this._calculateAccelerometerMagnitude(e.accelerometer),variability:this._calculateVariability(e.accelerometer),patterns:this._detectMovementPatterns(e.accelerometer)}),e.gyroscope&&(t.gyroscope={orientation:this._analyzeOrientation(e.gyroscope),stability:this._calculateOrientationStability(e.gyroscope)}),e.touch&&(t.touch={pressure:this._analyzeTouchPressure(e.touch),patterns:this._analyzeTouchPatterns(e.touch),frequency:this._calculateTouchFrequency(e.touch)}),t}_analyzeNeurodivergencePatterns(e){return{stimmingIndicators:this._detectStimmingPatterns(e),sensorySeekingLevel:this._calculateSensorySeekingLevel(e),regulationPatterns:this._analyzeRegulationPatterns(e),anxietyIndicators:this._detectAnxietyIndicators(e)}}_correlateWithGameMetrics(e,t){return{performanceCorrelation:this._calculatePerformanceCorrelation(e,t),attentionCorrelation:this._calculateAttentionCorrelation(e,t),engagementCorrelation:this._calculateEngagementCorrelation(e,t),stressCorrelation:this._calculateStressCorrelation(e,t)}}_calculateAnalysisConfidence(e,t){let r=0,i=0;return e.accelerometer&&(r+=.3,i++),e.gyroscope&&(r+=.2,i++),e.touch&&(r+=.3,i++),t.stimmingIndicators&&(r+=.2,i++),i>0?r/i:.5}_calculateAccelerometerMagnitude(e){return e&&Array.isArray(e)?e.reduce((e,t)=>e+Math.sqrt(Math.pow(t.x||0,2)+Math.pow(t.y||0,2)+Math.pow(t.z||0,2)),0)/e.length:0}_calculateVariability(e){if(!e||e.length<2)return 0;const t=e.map(e=>e.magnitude||0),r=t.reduce((e,t)=>e+t,0)/t.length,i=t.reduce((e,t)=>e+Math.pow(t-r,2),0)/t.length;return Math.sqrt(i)}_detectMovementPatterns(e){return{repetitive:this._calculateRepetitiveScore(e),erratic:this._calculateErraticScore(e),smooth:this._calculateSmoothScore(e)}}_analyzeOrientation(e){return e&&0!==e.length?{stable:this._isOrientationStable(e),avgTilt:this._calculateAverageTilt(e)}:{stable:!0,avgTilt:0}}_calculateOrientationStability(e){return e&&e.length>0?.8:0}_analyzeTouchPressure(e){if(!e||0===e.length)return{avg:0,max:0,variability:0};const t=e.map(e=>e.pressure||0);return{avg:t.reduce((e,t)=>e+t,0)/t.length,max:Math.max(...t),variability:this._calculateVariability(t.map(e=>({magnitude:e})))}}_calculateTouchFrequency(e){return e?e.length:0}_detectStimmingPatterns(e){return{detected:!1,confidence:.5,patterns:[]}}_calculateSensorySeekingLevel(e){return.5}_analyzeRegulationPatterns(e){return{selfRegulation:.7,externalRegulation:.3}}_detectAnxietyIndicators(e){return{detected:!1,level:.2,indicators:[]}}_calculatePerformanceCorrelation(e,t){try{if(!e||!t||!Array.isArray(t)||0===t.length)return this.logger.warn("Invalid inputs for performance correlation calculation"),.5;const r=e.score||0,i=e.accuracy||0,s=e.averageResponseTime||500,o=Math.min(1,Math.max(0,r/1e3)),n=Math.min(1,Math.max(0,i/100)),a=Math.min(1,Math.max(0,1-s/2e3));let c=0;const l=t.filter(e=>"touch"===e.type);if(l.length>1){const e=l.slice(1).map((e,t)=>{const r=l[t];return Math.sqrt(Math.pow(e.x-r.x,2)+Math.pow(e.y-r.y,2))}),t=e.reduce((e,t)=>e+t,0)/e.length;c=Math.min(1,Math.max(0,1-t/500))}let h=0;const u=t.filter(e=>"accelerometer"===e.type);if(u.length>1){const e=u.slice(1).map((e,t)=>{const r=u[t],i=(e.timestamp-r.timestamp)/1e3;return Math.sqrt(Math.pow((e.x-r.x)/i,2)+Math.pow((e.y-r.y)/i,2)+Math.pow((e.z-r.z)/i,2))}),t=e.reduce((e,t)=>e+t,0)/e.length;h=Math.min(1,Math.max(0,1-t/50))}const d={score:.3,accuracy:.3,responseTime:.2,touchStability:.1,movementSmoothness:.1},g=o*d.score+n*d.accuracy+a*d.responseTime+c*d.touchStability+h*d.movementSmoothness;return this.logger.debug("Performance correlation calculated",{performanceCorrelation:g,components:{normalizedScore:o,normalizedAccuracy:n,normalizedResponseTime:a,touchStability:c,movementSmoothness:h}}),g}catch(r){return this.logger.error("Error calculating performance correlation",r),.5}}_calculateAttentionCorrelation(e,t){try{if(!e||!t||!Array.isArray(t))return.5;const r=e.consecutiveErrors||0,i=e.distractionCount||0,s=e.focusLapses||0,o=Math.min(1,Math.max(0,1-r/5)),n=Math.min(1,Math.max(0,1-i/10)),a=Math.min(1,Math.max(0,1-s/8));let c=.5,l=.5;const h=t.filter(e=>"accelerometer"===e.type);if(h.length>5){const e=h.map(e=>Math.sqrt(Math.pow(e.x,2)+Math.pow(e.y,2)+Math.pow(e.z,2))),t=e.reduce((e,t)=>e+t,0)/e.length,r=Math.sqrt(e.reduce((e,r)=>e+Math.pow(r-t,2),0)/e.length);c=Math.min(1,Math.max(0,1-r/3))}const u=t.filter(e=>"touch"===e.type);if(u.length>5&&void 0!==u[0].targetX){const e=u.map(e=>{const t=Math.sqrt(Math.pow(e.x-e.targetX,2)+Math.pow(e.y-e.targetY,2));return Math.max(0,1-t/200)});l=e.reduce((e,t)=>e+t,0)/e.length}const d={errorMetric:.25,distractionMetric:.2,focusMetric:.25,deviceMovementMetric:.15,touchPrecisionMetric:.15};return o*d.errorMetric+n*d.distractionMetric+a*d.focusMetric+c*d.deviceMovementMetric+l*d.touchPrecisionMetric}catch(r){return this.logger.error("Error calculating attention correlation",r),.5}}_calculateEngagementCorrelation(e,t){try{if(!e||!t||!Array.isArray(t))return.5;const r=e.playTime||0,i=e.interactionRate||0,s=e.completionRate||0,o=Math.min(1,Math.max(0,r/300)),n=Math.min(1,Math.max(0,i/60)),a=Math.min(1,Math.max(0,s/100));let c=.5,l=.5;const h=t.filter(e=>"touch"===e.type);if(h.length>5){const e=new Set;h.forEach(t=>{const r=Math.floor(t.x/(t.screenWidth/3)),i=Math.floor(t.y/(t.screenHeight/3));e.add(`${r},${i}`)}),c=Math.min(1,Math.max(0,e.size/9))}if(e.responseTimeSeries&&Array.isArray(e.responseTimeSeries)){const t=e.responseTimeSeries.reduce((e,t)=>e+t,0)/e.responseTimeSeries.length;l=Math.min(1,Math.max(0,1-(t-300)/2e3))}const u={playTimeMetric:.2,interactionMetric:.25,completionMetric:.25,inputVarietyMetric:.15,responsivenessFactor:.15};return o*u.playTimeMetric+n*u.interactionMetric+a*u.completionMetric+c*u.inputVarietyMetric+l*u.responsivenessFactor}catch(r){return this.logger.error("Error calculating engagement correlation",r),.5}}_calculateStressCorrelation(e,t){try{if(!e||!t||!Array.isArray(t))return.3;const r=e.errorRate||0,i=e.timeoutCount||0,s=e.retryCount||0,o=Math.min(1,Math.max(0,r/100)),n=Math.min(1,Math.max(0,i/5)),a=Math.min(1,Math.max(0,s/8));let c=.3,l=.3;const h=t.filter(e=>"accelerometer"===e.type);if(h.length>5){const e=h.map(e=>Math.sqrt(Math.pow(e.x,2)+Math.pow(e.y,2)+Math.pow(e.z,2))),t=e.reduce((e,t)=>e+t,0)/e.length,r=e.reduce((e,r)=>e+Math.abs(r-t),0)/e.length;c=Math.min(1,Math.max(0,r/5))}const u=t.filter(e=>"touch"===e.type&&void 0!==e.pressure);if(u.length>0){const e=u.reduce((e,t)=>e+(t.pressure||0),0)/u.length;l=Math.min(1,Math.max(0,e/.8))}const d={errorStress:.25,timeoutStress:.2,retryStress:.2,movementIntensity:.2,touchPressureStress:.15};return o*d.errorStress+n*d.timeoutStress+a*d.retryStress+c*d.movementIntensity+l*d.touchPressureStress}catch(r){return this.logger.error("Error calculating stress correlation",r),.3}}_calculateRepetitiveScore(e){try{if(!e||!Array.isArray(e)||e.length<10)return.3;const t=e.filter(e=>"accelerometer"===e.type),r=e.filter(e=>"gyroscope"===e.type);if(t.length<10&&r.length<10)return.3;let i=0;if(t.length>=10){const e=t.map(e=>e.x||0),r=t.map(e=>e.y||0),s=t.map(e=>e.z||0),o=this._calculateAutocorrelation(e),n=this._calculateAutocorrelation(r),a=(o+n+this._calculateAutocorrelation(s))/3;i=Math.min(1,Math.max(0,a))}else if(r.length>=10){const e=r.map(e=>e.x||0),t=r.map(e=>e.y||0),s=r.map(e=>e.z||0),o=this._calculateAutocorrelation(e),n=this._calculateAutocorrelation(t),a=(o+n+this._calculateAutocorrelation(s))/3;i=Math.min(1,Math.max(0,a))}return i}catch(t){return this.logger.error("Error calculating repetitive score",t),.3}}_calculateAutocorrelation(e){try{const t=e.length;if(t<4)return 0;const r=e.reduce((e,t)=>e+t,0)/t,i=e.map(e=>e-r),s=i.reduce((e,t)=>e+t*t,0)/t;if(0===s)return 0;let o=0;for(let e=1;e<t;e++)o+=i[e]*i[e-1];return o/=(t-1)*s,Math.abs(o)}catch(t){return this.logger.error("Error in autocorrelation calculation",t),0}}_calculateErraticScore(e){try{if(!e||!Array.isArray(e)||e.length<5)return.2;const t=e.filter(e=>"accelerometer"===e.type),r=e.filter(e=>"touch"===e.type);if(t.length<5&&r.length<5)return.2;let i=0;if(t.length>=5){const e=[];for(let r=1;r<t.length;r++){const i=(t[r].timestamp-t[r-1].timestamp)/1e3;if(i>0){const s=Math.abs(t[r].x-t[r-1].x)/i,o=Math.abs(t[r].y-t[r-1].y)/i,n=Math.abs(t[r].z-t[r-1].z)/i,a=Math.sqrt(s*s+o*o+n*n);e.push(a)}}if(e.length>0){const t=e.reduce((e,t)=>e+t,0)/e.length,r=Math.sqrt(e.reduce((e,r)=>e+Math.pow(r-t,2),0)/e.length);i=Math.min(1,Math.max(0,t/20*.7+r/10*.3))}}else if(r.length>=5){const e=[];for(let t=1;t<r.length;t++){const i=r[t].x-r[t-1].x,s=r[t].y-r[t-1].y,o=(r[t].timestamp-r[t-1].timestamp)/1e3;if(o>0){const t=Math.sqrt(i*i+s*s)/o;e.push(t)}}if(e.length>0){const t=e.reduce((e,t)=>e+t,0)/e.length,r=Math.sqrt(e.reduce((e,r)=>e+Math.pow(r-t,2),0)/e.length);i=Math.min(1,Math.max(0,t/500*.5+r/300*.5))}}return i}catch(t){return this.logger.error("Error calculating erratic score",t),.2}}_calculateSmoothScore(e){try{return 1-this._calculateErraticScore(e)}catch(t){return this.logger.error("Error calculating smooth score",t),.8}}_isOrientationStable(e){try{if(!e||!Array.isArray(e)||e.length<5)return!0;const t=e.filter(e=>"deviceorientation"===e.type||"gyroscope"===e.type);if(t.length<5)return!0;let r=0,i=0,s=0,o=0;for(let e=1;e<t.length;e++){const n=t[e-1],a=t[e];void 0!==n.alpha&&void 0!==a.alpha&&(r+=Math.abs(a.alpha-n.alpha),i+=Math.abs(a.beta-n.beta),s+=Math.abs(a.gamma-n.gamma),o++)}if(0===o)return!0;return r/o<5&&i/o<5&&s/o<5}catch(t){return this.logger.error("Error checking orientation stability",t),!0}}_calculateAverageTilt(e){try{if(!e||!Array.isArray(e)||0===e.length)return 0;const t=e.filter(e=>"deviceorientation"===e.type||"gyroscope"===e.type);if(0===t.length)return 0;let r=0,i=0;for(const e of t)void 0!==e.beta&&(r+=e.beta,i++);return i>0?r/i:0}catch(t){return this.logger.error("Error calculating average tilt",t),0}}async onValidationStarted(e){try{if(!e||!e.sessionId)return;this.recordValidationEvent({type:"validation_started",sessionId:e.sessionId,timestamp:e.timestamp||(new Date).toISOString(),metricsCount:e.metrics?.length||0})}catch(t){}}async onValidationCompleted(e){try{if(!e||!e.sessionId)return;this.recordValidationEvent({type:"validation_completed",sessionId:e.sessionId,timestamp:e.timestamp||(new Date).toISOString(),results:e.results||{valid:!0},metricsCount:e.metrics?.length||0}),this.updateValidationStatistics(e)}catch(t){}}recordValidationEvent(e){this.validationEvents||(this.validationEvents=[]),this.validationEvents.push({...e,recordedAt:(new Date).toISOString()}),this.validationEvents.length>100&&(this.validationEvents=this.validationEvents.slice(-100))}updateValidationStatistics(e){this.validationStats||(this.validationStats={total:0,valid:0,invalid:0,lastUpdated:null}),this.validationStats.total++,e.results?.valid?this.validationStats.valid++:this.validationStats.invalid++,this.validationStats.lastUpdated=(new Date).toISOString()}}class a{constructor(e){this.collector=e,this.isAvailable=!1,this.readings=[]}initialize(){return!0}start(){return!0}stop(){return!0}onReading(){}onError(e){this.collector.logger.error(`${this.constructor.name} error`,e)}getLatestData(){return this.readings.length>0?this.readings[this.readings.length-1]:null}getData(e=10){return this.readings.slice(-e)}}class c extends a{async initialize(){try{return"Accelerometer"in window?(this.sensor=new Accelerometer({frequency:this.collector.sensorConfig.accelerometer.frequency}),this.isAvailable=!0,this.sensor.addEventListener("reading",()=>this.onReading()),this.sensor.addEventListener("error",e=>this.onError(e)),this.collector.logger.info("Accelerometer initialized successfully"),!0):(this.isAvailable=!1,this.collector.logger.warn("Accelerometer API not available in this browser"),!1)}catch(e){return this.isAvailable=!1,this.collector.logger.error("Failed to initialize accelerometer",e),!1}}start(){if(this.isAvailable&&this.sensor)try{return this.sensor.start(),this.collector.logger.info("Accelerometer started"),!0}catch(e){return this.collector.logger.error("Failed to start accelerometer",e),!1}return!1}stop(){if(this.isAvailable&&this.sensor)try{return this.sensor.stop(),this.collector.logger.info("Accelerometer stopped"),!0}catch(e){return this.collector.logger.error("Failed to stop accelerometer",e),!1}return!1}onReading(){if(this.sensor){const e={x:this.sensor.x,y:this.sensor.y,z:this.sensor.z,timestamp:Date.now()};this.readings.push(e),this.collector.dataBuffer.accelerometer.push(e),this.collector.dataBuffer.accelerometer.length>=this.collector.dataBuffer.flushThreshold&&this.collector.processBufferedData("accelerometer")}}}class l extends a{async initialize(){try{return"Gyroscope"in window?(this.sensor=new Gyroscope({frequency:this.collector.sensorConfig.gyroscope.frequency}),this.isAvailable=!0,this.sensor.addEventListener("reading",()=>this.onReading()),this.sensor.addEventListener("error",e=>this.onError(e)),this.collector.logger.info("Gyroscope initialized successfully"),!0):(this.isAvailable=!1,this.collector.logger.warn("Gyroscope API not available in this browser"),!1)}catch(e){return this.isAvailable=!1,this.collector.logger.error("Failed to initialize gyroscope",e),!1}}start(){if(this.isAvailable&&this.sensor)try{return this.sensor.start(),this.collector.logger.info("Gyroscope started"),!0}catch(e){return this.collector.logger.error("Failed to start gyroscope",e),!1}return!1}stop(){if(this.isAvailable&&this.sensor)try{return this.sensor.stop(),this.collector.logger.info("Gyroscope stopped"),!0}catch(e){return this.collector.logger.error("Failed to stop gyroscope",e),!1}return!1}onReading(){if(this.sensor){const e={alpha:this.sensor.alpha,beta:this.sensor.beta,gamma:this.sensor.gamma,timestamp:Date.now()};this.readings.push(e),this.collector.dataBuffer.gyroscope.push(e),this.collector.dataBuffer.gyroscope.length>=this.collector.dataBuffer.flushThreshold&&this.collector.processBufferedData("gyroscope")}}}class h extends a{initialize(){return!0}start(){if(!this.isTracking)try{return window.addEventListener("touchstart",this.onTouchStart.bind(this)),window.addEventListener("touchmove",this.onTouchMove.bind(this)),window.addEventListener("touchend",this.onTouchEnd.bind(this)),this.isTracking=!0,this.collector.logger.info("Touch metrics tracking started"),!0}catch(e){return this.collector.logger.error("Failed to start touch metrics",e),!1}return!0}stop(){if(this.isTracking)try{return window.removeEventListener("touchstart",this.onTouchStart.bind(this)),window.removeEventListener("touchmove",this.onTouchMove.bind(this)),window.removeEventListener("touchend",this.onTouchEnd.bind(this)),this.isTracking=!1,this.collector.logger.info("Touch metrics tracking stopped"),!0}catch(e){return this.collector.logger.error("Failed to stop touch metrics",e),!1}return!0}}class u extends a{}class d extends a{}export{o as MultisensoryLogger,n as MultisensoryMetricsCollector,n as default};
