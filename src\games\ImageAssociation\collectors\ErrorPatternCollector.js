/**
 * 🖼️ IMAGE ASSOCIATION ERROR PATTERN COLLECTOR
 * Algoritmo especializado para coleta e análise de padrões de erro no jogo de associação de imagens
 */

export class ErrorPatternCollector {
  constructor() {
    this.name = 'ImageAssociationErrorPatternCollector';
    this.description = 'Coleta padrões de erros no ImageAssociation';
    this.version = '1.0.0';
    this.isActive = true;
    this.collectedData = [];
    
    this.errorData = {
      associationErrors: {},
      semanticErrors: [],
      visualProcessingErrors: [],
      categoricalErrors: [],
      persistentErrors: {},
      errorClusters: [],
      learningIndicators: [],
      conceptualMappings: {}
    };
    this.sessionStartTime = Date.now();
    this.errorThresholds = {
      persistent: 3,
      cluster: 5,
      severity: {
        low: 0.3,
        medium: 0.6,
        high: 0.8
      }
    };
    console.log(`🖼️ ${this.name} v${this.version} inicializado`);
  }

  /**
   * Método padronizado de coleta de dados para integração com testes
   * @param {Object} gameData - Dados do jogo a serem analisados
   * @returns {Object} - Resultado da análise de erros
   */
  collect(gameData) {
    if (!gameData) {
      console.warn("ImageAssociationErrorPatternCollector: Dados do jogo não fornecidos para análise");
      return { errors: [], patterns: [], metrics: {} };
    }

    console.log(`📊 ImageAssociationErrorPatternCollector: Analisando dados da sessão ${gameData.sessionId || 'sem ID'}`);
    
    try {
      // Extrair e categorizar erros dos dados do jogo
      const errorMetrics = this.analyzeErrorPatterns(gameData);
      const errors = [];
      
      // Analisar erros de associação de imagens
      if (gameData.attemptHistory && Array.isArray(gameData.attemptHistory)) {
        gameData.attemptHistory.forEach((attempt, index) => {
          if (!attempt.isCorrect && attempt.targetImage && attempt.selectedImage) {
            const associationError = this.collectAssociationError(
              attempt.targetImage,
              attempt.selectedImage,
              { 
                difficulty: gameData.difficulty || 'medium',
                responseTime: attempt.responseTime || 0,
                attemptNumber: index,
                category: attempt.category || 'general'
              }
            );
            if (associationError) errors.push(associationError);
          }
        });
      }
      
      // Salvar dados coletados para análise futura
      const collectedMetric = {
        timestamp: Date.now(),
        type: 'error_pattern',
        gameType: 'ImageAssociation',
        data: errorMetrics,
        errors: errors,
        sessionData: {
          sessionId: gameData.sessionId,
          level: gameData.level || 1,
          attempt: gameData.attempt || 1
        }
      };

      this.collectedData.push(collectedMetric);
      this.categorizeErrors(errorMetrics);
      
      return {
        errors,
        patterns: errorMetrics,
        metrics: this.generateErrorMetrics(gameData)
      };
    } catch (error) {
      console.error('❌ Erro ao coletar padrões de erro (ImageAssociation):', error);
      return null;
    }
  }

  analyzeErrorPatterns(gameData) {
    const patterns = {
      associationErrors: this.detectAssociationErrors(gameData),
      semanticErrors: this.detectSemanticErrors(gameData),
      visualProcessingErrors: this.detectVisualErrors(gameData),
      categoricalErrors: this.detectCategoricalErrors(gameData),
      severity: this.calculateOverallSeverity(gameData)
    };

    return patterns;
  }

  detectAssociationErrors(gameData) {
    return [];
  }

  detectSemanticErrors(gameData) {
    return [];
  }

  detectVisualErrors(gameData) {
    return [];
  }

  detectCategoricalErrors(gameData) {
    return [];
  }

  calculateOverallSeverity(gameData) {
    return 'low';
  }

  categorizeErrors(errorMetrics) {
    // Categorizar erros por tipo
  }

  /**
   * Coleta erros de associação de imagens
   */
  collectAssociationError(targetImage, selectedImage, context) {
    const errorKey = `${targetImage.id || targetImage.name}->${selectedImage.id || selectedImage.name}`;
    
    const associationError = {
      timestamp: new Date().toISOString(),
      targetImage: targetImage,
      selectedImage: selectedImage,
      errorType: this.identifyAssociationErrorType(targetImage, selectedImage, context),
      context: {
        category: context.category || 'general',
        difficulty: context.difficulty || 'medium',
        responseTime: context.responseTime || 0,
        attemptNumber: context.attemptNumber || 0
      },
      severity: this.calculateAssociationErrorSeverity(targetImage, selectedImage, context),
      semanticDistance: this.calculateSemanticDistance(targetImage, selectedImage)
    };

    if (!this.errorData.associationErrors[errorKey]) {
      this.errorData.associationErrors[errorKey] = [];
    }
    this.errorData.associationErrors[errorKey].push(associationError);

    return associationError;
  }

  /**
   * Identifica o tipo de erro de associação
   */
  identifyAssociationErrorType(targetImage, selectedImage, context) {
    if (!selectedImage) return 'no_selection';
    
    // Erro de categoria (semântico)
    if (targetImage.category && selectedImage.category && 
        targetImage.category !== selectedImage.category) {
      return 'category_error';
    }
    
    // Erro visual (aparência similar, mas conceito diferente)
    if (this.areSimilarVisually(targetImage, selectedImage)) {
      return 'visual_similarity_error';
    }
    
    // Erro funcional (função similar, mas aparência diferente)
    if (this.areSimilarFunctionally(targetImage, selectedImage)) {
      return 'functional_similarity_error';
    }
    
    // Erro contextual (aparecem em contextos similares)
    if (this.shareContext(targetImage, selectedImage)) {
      return 'contextual_error';
    }
    
    return 'general_association_error';
  }

  /**
   * Verifica se duas imagens são visualmente similares
   */
  areSimilarVisually(image1, image2) {
    // Implementação simplificada - na prática, usaria atributos visuais como cores, formas, etc.
    return false;
  }

  /**
   * Verifica se duas imagens têm funções similares
   */
  areSimilarFunctionally(image1, image2) {
    // Implementação simplificada - na prática, usaria atributos funcionais/categorias
    return false;
  }

  /**
   * Verifica se duas imagens aparecem em contextos similares
   */
  shareContext(image1, image2) {
    // Implementação simplificada
    return false;
  }

  /**
   * Calcula a distância semântica entre duas imagens
   */
  calculateSemanticDistance(image1, image2) {
    // Implementação simplificada - na prática, usaria redes semânticas ou similaridade contextual
    return 0.5;
  }

  /**
   * Calcula a severidade do erro de associação
   */
  calculateAssociationErrorSeverity(targetImage, selectedImage, context) {
    let severity = 0.5; // Base
    
    // Ajuste por distância semântica
    const distance = this.calculateSemanticDistance(targetImage, selectedImage);
    severity += distance * 0.3;
    
    // Ajuste por tempo de resposta
    if (context.responseTime > 5000) severity += 0.1; // Muito lento
    if (context.responseTime < 500) severity += 0.1; // Muito rápido, pode indicar impulsividade
    
    // Ajuste por dificuldade
    if (context.difficulty === 'hard') severity -= 0.1; // Mais compreensível errar em níveis difíceis
    
    return Math.min(Math.max(severity, 0), 1); // Limitar entre 0 e 1
  }

  /**
   * Gera métricas de erro com base nos dados coletados
   */
  generateErrorMetrics(gameData) {
    const errorCount = Object.values(this.errorData.associationErrors).reduce(
      (total, errors) => total + errors.length, 0
    );
    
    return {
      totalErrors: errorCount,
      uniqueAssociationErrors: Object.keys(this.errorData.associationErrors).length,
      mostCommonError: this.findMostCommonError(),
      averageSeverity: this.calculateAverageSeverity(),
      visualProcessingScore: this.calculateVisualProcessingScore(gameData),
      conceptualAssociationScore: this.calculateConceptualAssociationScore(gameData),
      improvement: this.calculateImprovementMetric(gameData)
    };
  }

  /**
   * Encontra o erro mais comum
   */
  findMostCommonError() {
    let maxCount = 0;
    let mostCommonError = null;
    
    Object.entries(this.errorData.associationErrors).forEach(([errorKey, errors]) => {
      if (errors.length > maxCount) {
        maxCount = errors.length;
        mostCommonError = errorKey;
      }
    });
    
    return {
      error: mostCommonError,
      count: maxCount
    };
  }

  /**
   * Calcula a severidade média dos erros
   */
  calculateAverageSeverity() {
    let totalSeverity = 0;
    let errorCount = 0;
    
    Object.values(this.errorData.associationErrors).forEach(errors => {
      errors.forEach(error => {
        totalSeverity += error.severity;
        errorCount++;
      });
    });
    
    return errorCount > 0 ? totalSeverity / errorCount : 0;
  }

  /**
   * Calcula pontuação de processamento visual
   */
  calculateVisualProcessingScore(gameData) {
    // Implementação simplificada
    return 0.7;
  }

  /**
   * Calcula pontuação de associação conceitual
   */
  calculateConceptualAssociationScore(gameData) {
    // Implementação simplificada
    return 0.6;
  }

  /**
   * Calcula métrica de melhoria ao longo do tempo
   */
  calculateImprovementMetric(gameData) {
    // Implementação simplificada
    return 0.5;
  }

  /**
   * Método de análise para compatibilidade com outros coletores
   */
  analyze(gameData) {
    return this.collect(gameData);
  }
}

export default ErrorPatternCollector;
