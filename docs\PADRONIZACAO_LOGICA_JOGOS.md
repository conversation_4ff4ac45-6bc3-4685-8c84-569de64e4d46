# Padronização da Lógica de Jogos

Este documento detalha o plano de padronização da lógica de jogos baseado na implementação do ColorMatch e hooks existentes. O objetivo é unificar:

1. **Coleta de métricas terapêuticas** (useGameMetrics)
2. **Sistema de dificuldade adaptativo**
3. **Integração com acessibilidade** (useAccessibility)
4. **Fluxo de jogo padronizado** (início/jogando/completo)
5. **Gestão de sessões e usuários**
6. **Integração com MetricsCollector para análise de IA**

## Componente Principal: `useUnifiedGameLogic.js`

Hook reutilizável que integra todos os hooks existentes do sistema:

```javascript:src/hooks/useUnifiedGameLogic.js
import { useState, useRef, useContext } from 'react';
import { useGameMetrics } from './useGameMetrics';
import { useAccessibility } from './useAccessibility';
import { SystemContext } from '../components/context/SystemContext';
import { v4 as uuidv4 } from 'uuid';

export default function useUnifiedGameLogic(gameName, difficulties) {
  const { user } = useContext(SystemContext);
  const { settings: accessibilitySettings } = useAccessibility();
  
  // Estados principais
  const [difficulty, setDifficulty] = useState('easy');
  const [gameState, setGameState] = useState('start'); // start, playing, paused, completed
  const [showStartScreen, setShowStartScreen] = useState(true);
  const [feedback, setFeedback] = useState({ show: false, type: '', message: '' });
  
  // Gestão de sessão
  const sessionIdRef = useRef(uuidv4());
  const gameStartTime = useRef(null);
  
  // Métricas específicas do jogo
  const gameMetricsRef = useRef(null);
  
  // Hook de métricas do sistema
  const {
    startSession: startMetricsSession,
    recordEvent: recordMetricsEvent,
    endSession: endMetricsSession
  } = useGameMetrics(gameName, user?.id || 'anonymous');

  // Função de síntese de voz unificada
  const speak = (text, options = {}) => {
    if (accessibilitySettings.textToSpeech && 'speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'pt-BR';
      utterance.rate = options.rate || 0.8;
      utterance.pitch = options.pitch || 1;
      speechSynthesis.speak(utterance);
    }
  };

  // Inicializar jogo
  const startGame = (selectedDifficulty) => {
    setDifficulty(selectedDifficulty);
    setShowStartScreen(false);
    setGameState('playing');
    gameStartTime.current = Date.now();
    sessionIdRef.current = uuidv4();
    
    // Inicializar métricas específicas do jogo
    if (gameMetricsRef.current) {
      const difficultyLevel = selectedDifficulty === 'easy' ? 1 : selectedDifficulty === 'medium' ? 2 : 3;
      gameMetricsRef.current.startSession(sessionIdRef.current, user?.id || 'anonymous', difficultyLevel);
    }
    
    // Inicializar métricas do sistema
    startMetricsSession({
      gameType: gameName,
      difficulty: selectedDifficulty,
      userId: user?.id || 'anonymous',
      sessionId: sessionIdRef.current
    });
    
    // Anunciar início do jogo
    speak(`Iniciando ${gameName} no nível ${selectedDifficulty}`);
  };

  // Registrar interação
  const recordInteraction = (interactionType, data) => {
    const eventData = {
      interactionType,
      difficulty,
      sessionId: sessionIdRef.current,
      timestamp: new Date().toISOString(),
      gameTime: Date.now() - gameStartTime.current,
      ...data
    };
    
    // Registrar no sistema de métricas
    recordMetricsEvent(interactionType, eventData);
    
    // Registrar no jogo específico se disponível
    if (gameMetricsRef.current && gameMetricsRef.current.recordInteraction) {
      gameMetricsRef.current.recordInteraction(
        interactionType,
        data.element || 'unknown',
        data.selected || '',
        data.correct || '',
        data.duration || 0
      );
    }
  };

  // Finalizar jogo
  const completeGame = (gameResults = {}) => {
    setGameState('completed');
    
    // Finalizar métricas específicas
    let gameSpecificMetrics = {};
    if (gameMetricsRef.current && gameMetricsRef.current.endSession) {
      gameSpecificMetrics = gameMetricsRef.current.endSession();
    }
    
    // Finalizar métricas do sistema
    const systemMetrics = endMetricsSession({
      ...gameResults,
      gameSpecificMetrics,
      totalTime: Date.now() - gameStartTime.current
    });
    
    console.log(`${gameName} completed:`, { systemMetrics, gameSpecificMetrics });
    
    return { systemMetrics, gameSpecificMetrics };
  };

  // Mostrar feedback
  const showFeedback = (type, message, duration = 2000) => {
    setFeedback({ show: true, type, message });
    
    // Anunciar feedback se habilitado
    if (accessibilitySettings.autoRead) {
      speak(message);
    }
    
    setTimeout(() => {
      setFeedback({ show: false, type: '', message: '' });
    }, duration);
  };

  // Repetir instruções
  const repeatInstructions = (instructions) => {
    if (gameMetricsRef.current && gameMetricsRef.current.recordRepetition) {
      gameMetricsRef.current.recordRepetition();
    }
    speak(instructions);
  };

  // Reiniciar jogo
  const restartGame = () => {
    setGameState('start');
    setShowStartScreen(true);
    setFeedback({ show: false, type: '', message: '' });
    sessionIdRef.current = uuidv4();
    gameStartTime.current = null;
    
    // Resetar métricas específicas
    if (gameMetricsRef.current && gameMetricsRef.current.constructor) {
      gameMetricsRef.current = new gameMetricsRef.current.constructor();
    }
  };

  return {
    // Estados
    difficulty,
    gameState,
    showStartScreen,
    feedback,
    user,
    accessibilitySettings,
    sessionId: sessionIdRef.current,
    
    // Referências para métricas específicas
    gameMetricsRef,
    
    // Funções principais
    startGame,
    completeGame,
    restartGame,
    
    // Interações e feedback
    recordInteraction,
    showFeedback,
    speak,
    repeatInstructions,
    
    // Setters
    setGameState,
    setDifficulty,
    setShowStartScreen
  };
}
```

## Passos de Implementação por Jogo

### 1. Importar o hook unificado
```javascript
import useUnifiedGameLogic from '../../hooks/useUnifiedGameLogic';
import GameSpecificMetrics from './GameSpecificMetrics'; // Se existir
```

### 2. Definir configurações específicas do jogo
```javascript
const GAME_CONFIG = {
  name: 'NomeDoJogo',
  difficulties: {
    easy: { 
      description: 'Nível fácil - Ideal para iniciantes',
      timeLimit: null,
      itemsCount: 6,
      // outras configurações específicas
    },
    medium: { 
      description: 'Nível médio - Desafio equilibrado',
      timeLimit: 90,
      itemsCount: 9,
    },
    hard: { 
      description: 'Nível avançado - Para especialistas',
      timeLimit: 60,
      itemsCount: 12,
    }
  }
};
```

### 3. Inicializar o hook no componente
```javascript
function GameComponent({ onBack }) {
  const {
    // Estados
    difficulty,
    gameState,
    showStartScreen,
    feedback,
    user,
    accessibilitySettings,
    sessionId,
    
    // Referências
    gameMetricsRef,
    
    // Funções
    startGame,
    completeGame,
    recordInteraction,
    showFeedback,
    speak,
    repeatInstructions,
    restartGame
  } = useUnifiedGameLogic(GAME_CONFIG.name);

  // Inicializar métricas específicas do jogo
  useEffect(() => {
    if (GameSpecificMetrics) {
      gameMetricsRef.current = new GameSpecificMetrics();
    }
  }, []);
}
```

### 4. Implementar tela de início padronizada
```jsx
if (showStartScreen) {
  return (
    <GameStartScreen
      gameTitle={GAME_CONFIG.name}
      gameSubtitle="Descrição do jogo"
      gameInstruction="Instruções detalhadas"
      gameIcon="🎮"
      difficulties={Object.entries(GAME_CONFIG.difficulties).map(([id, config]) => ({
        id,
        name: id === 'easy' ? 'Fácil' : id === 'medium' ? 'Médio' : 'Avançado',
        description: config.description,
        icon: id === 'easy' ? '😊' : id === 'medium' ? '🎯' : '🚀',
        preview: <GamePreviewComponent config={config} />
      }))}
      customContent={<GameBenefitsComponent />}
      onStart={startGame}
      onBack={onBack}
    />
  );
}
```

### 5. Registrar interações padronizadas
```javascript
const handleUserInteraction = (interactionData) => {
  const startTime = Date.now();
  
  // Lógica específica do jogo
  const result = processGameInteraction(interactionData);
  
  // Registrar métricas
  recordInteraction('user_interaction', {
    element: interactionData.element,
    selected: interactionData.selected,
    correct: result.isCorrect,
    duration: Date.now() - startTime,
    responseTime: result.responseTime,
    attempts: result.attempts,
    // dados específicos do jogo
    gameSpecific: result.gameSpecificData
  });
  
  // Mostrar feedback
  if (result.isCorrect) {
    showFeedback('success', '✅ Correto!');
  } else {
    showFeedback('error', '❌ Tente novamente!');
  }
};
```

### 6. Implementar controles de acessibilidade
```javascript
const handleTTSInstructions = () => {
  const instructions = generateGameInstructions(difficulty, gameState);
  repeatInstructions(instructions);
};

// Implementar navegação por teclado
useEffect(() => {
  const handleKeyPress = (e) => {
    if (e.key === 'Space') {
      handleTTSInstructions();
    }
    // outras teclas de atalho
  };
  
  window.addEventListener('keydown', handleKeyPress);
  return () => window.removeEventListener('keydown', handleKeyPress);
}, [difficulty, gameState]);
```

### 7. Finalizar jogo com métricas completas
```javascript
const handleGameCompletion = (gameResults) => {
  const finalMetrics = completeGame({
    score: gameResults.score,
    accuracy: gameResults.accuracy,
    totalAttempts: gameResults.totalAttempts,
    correctAnswers: gameResults.correctAnswers,
    timeSpent: gameResults.timeSpent,
    // métricas específicas do jogo
    ...gameResults.gameSpecificMetrics
  });
  
  // Mostrar tela de resultados
  showFeedback('success', 
    `🎉 Parabéns! Pontuação: ${gameResults.score} | Precisão: ${gameResults.accuracy}%`,
    4000
  );
  
  // Auto-restart após delay
  setTimeout(() => restartGame(), 4000);
};
```

## Jogos para Atualizar

| Jogo | Arquivo | Status | Prioridade | Complexidade |
|------|---------|--------|------------|--------------|
| **Contagem de Números** | `src/games/NumberCounting/NumberCountingGame.jsx` | 🔄 Pendente | Alta | Média |
| **Reconhecimento de Letras** | `src/games/LetterRecognition/LetterRecognitionGame.jsx` | 🔄 Pendente | Alta | Média |
| **Correspondência de Cores** | `src/games/ColorMatch/ColorMatchGame.jsx` | ✅ **Completo** | - | - |
| **Jogo da Memória** | `src/games/MemoryGame/MemoryGame.jsx` | 🔄 Pendente | Média | Alta |
| **Sequência Musical** | `src/games/MusicalSequence/MusicalSequenceGame.jsx` | 🔄 Pendente | Média | Alta |
| **Pintura Criativa** | `src/games/CreativePainting/CreativePaintingGame.jsx` | 🔄 Pendente | Baixa | Baixa |
| **Contagem (Português)** | `src/games/ContagemNumeros/ContagemNumerosGame.jsx` | 🔄 Pendente | Média | Média |
| **Padrões Visuais** | `src/games/PadroesVisuais/PadroesVisuaisGame.jsx` | 🔄 Pendente | Média | Alta |
| **Associação de Imagens** | `src/games/ImageAssociation/ImageAssociationGame.jsx` | 🔄 Pendente | Média | Média |
| **Quebra-Cabeça** | `src/games/QuebraCabeca/QuebraCabecaGame.jsx` | 🔄 Pendente | Baixa | Alta |

## Critérios de Padronização

### ✅ Obrigatórios para todos os jogos:
- [ ] Hook `useUnifiedGameLogic` implementado
- [ ] Sistema de métricas específicas do jogo
- [ ] Integração com `useGameMetrics` e `useAccessibility`
- [ ] Tela de início com `GameStartScreen` padronizada
- [ ] Sistema TTS/acessibilidade funcional
- [ ] Feedback visual e sonoro consistente
- [ ] Gestão de sessões com UUID
- [ ] Finalização com métricas completas

### 🎯 Recomendados:
- [ ] Navegação por teclado (teclas de atalho)
- [ ] Sistema de dicas contextuais
- [ ] Adaptação automática de dificuldade
- [ ] Animações e transições suaves
- [ ] Suporte a múltiplos idiomas
- [ ] Modo offline/resiliente

### 📊 Métricas específicas por tipo de jogo:
- **Cognitivos**: Tempo de resposta, padrões de erro, sequências de acerto
- **Visuais**: Rastreamento ocular simulado, preferências de cor, confusões visuais
- **Auditivos**: Resposta a estímulos sonoros, preferências de timbre/frequência
- **Motores**: Precisão de clique, coordenação, tempo de movimento

## Cronograma de Implementação

### Fase 1: Preparação (Estimativa: 2 dias)
1. ✅ **Implementar hook `useUnifiedGameLogic.js`**
2. ✅ **Validar integração com hooks existentes**
3. ✅ **Criar template base para jogos**
4. ✅ **Documentar padrões e exemplos**

### Fase 2: Jogos Prioritários (Estimativa: 3-4 dias)
1. 🔄 **NumberCounting** - Implementação completa
2. 🔄 **LetterRecognition** - Implementação completa  
3. 🔄 **ContagemNumeros** - Implementação completa
4. ✅ **Validação de métricas no dashboard**

### Fase 3: Jogos Intermediários (Estimativa: 4-5 dias)
1. 🔄 **MemoryGame** - Implementação completa
2. 🔄 **MusicalSequence** - Implementação completa
3. 🔄 **ImageAssociation** - Implementação completa
4. 🔄 **PadroesVisuais** - Implementação completa

### Fase 4: Jogos Complementares (Estimativa: 2-3 dias)
1. 🔄 **CreativePainting** - Implementação completa
2. 🔄 **QuebraCabeca** - Implementação completa
3. ✅ **Testes de integração final**
4. ✅ **Otimização de performance**

### Fase 5: Validação e Polimento (Estimativa: 2 dias)
1. ✅ **Testes de acessibilidade completos**
2. ✅ **Validação de métricas terapêuticas**
3. ✅ **Documentação final**
4. ✅ **Deploy e monitoramento**

## Validação Técnica

### Checklist por Jogo:
```javascript
// Template de validação
const validationChecklist = {
  // Funcionalidades básicas
  gameStartsCorrectly: false,
  difficultySystemWorks: false,
  gameLogicFunctional: false,
  gameEndsGracefully: false,
  
  // Métricas e dados
  metricsCollected: false,
  sessionTracking: false,
  userInteractionsLogged: false,
  performanceDataAccurate: false,
  
  // Acessibilidade
  ttsWorking: false,
  keyboardNavigation: false,
  visualFeedbackClear: false,
  instructionsAccessible: false,
  
  // Integração
  systemContextUsed: false,
  errorHandlingRobust: false,
  offlineCapable: false,
  responsiveDesign: false
};
```

### Métricas de Sucesso:
- **Performance**: Tempo de carregamento < 2s
- **Acessibilidade**: Score WCAG AA+ (>95%)
- **Usabilidade**: Taxa de completude > 85% 
- **Estabilidade**: Zero crashes em 100 sessões de teste
- **Métricas**: 100% de eventos capturados corretamente

## Recursos Adicionais

### Arquivos de Apoio:
- `src/hooks/useUnifiedGameLogic.js` - Hook principal
- `src/components/common/GameStartScreen/` - Tela inicial padronizada
- `src/utils/GameValidation.js` - Utilitários de validação
- `src/types/GameTypes.js` - Tipos TypeScript para jogos
- `docs/GAME_DEVELOPMENT_GUIDE.md` - Guia completo de desenvolvimento
- `docs/ESTRATEGIA_COLETA_METRICAS_COMPLETA.md` - **📊 Estratégia completa de métricas**

### Sistema de Métricas Integrado:
- **MetricsCollector**: Coleta de eventos em tempo real
- **MetricsProcessor**: Processamento com IA e detecção de anomalias
- **MetricsAggregator**: Agregação temporal e estatísticas avançadas
- **MultisensoryIntegrator**: Análise de dados sensoriais móveis
- **AdvancedAIReport**: Dashboard Premium com insights de IA

### Exemplo de Integração Completa (ColorMatch):
```javascript
// Hook unificado com métricas integradas
const {
  gameMetricsRef,           // Métricas específicas do jogo
  recordInteraction,        // Sistema geral de métricas
  user, sessionId,          // Contexto de usuário
  startGame, completeGame   // Controle de fluxo
} = useUnifiedGameLogic('ColorMatch');

// Inicialização de métricas específicas
useEffect(() => {
  gameMetricsRef.current = new ColorMatchMetrics();
}, []);

// Registro duplo de métricas (específicas + gerais)
const handleInteraction = (data) => {
  // 1. Métricas específicas do jogo
  gameMetricsRef.current.recordInteraction(
    'color_select', 'color', data.selected, data.correct, data.duration
  );
  
  // 2. Métricas do sistema geral (para IA)
  recordInteraction('user_interaction', {
    element: data.element,
    selected: data.selected,
    correct: data.correct,
    duration: data.duration,
    gameSpecific: {
      colorConfusion: data.colorConfusion,
      difficulty: difficulty,
      sensorData: data.sensorData
    }
  });
};
```

### Ferramentas de Debug:
- Console de métricas em tempo real
- Validador de acessibilidade integrado
- Simulador de diferentes perfis de usuário
- Monitor de performance por jogo
- **🔍 Visualizador de dados de IA** (Dashboard Premium)

### Dados Coletados para IA:
- **Padrões Sensoriais**: Preferências visuais/auditivas, sensibilidade
- **Padrões Cognitivos**: Confusões recorrentes, velocidade de processamento
- **Padrões Comportamentais**: Tempo ótimo, modalidades preferidas
- **Dados Multissensoriais**: Touch, movimento, contexto ambiental
- **Métricas de Engajamento**: Atenção sustentada, fadiga, motivação

> **Objetivo Final**: Sistema de jogos terapêuticos totalmente padronizado, acessível e com coleta de dados robusta para análise de IA e acompanhamento terapêutico integrado ao **Dashboard Premium** com insights avançados.
