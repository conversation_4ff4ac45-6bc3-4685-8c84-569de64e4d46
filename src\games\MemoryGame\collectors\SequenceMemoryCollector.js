/**
 * 🧩 SEQUENCE MEMORY COLLECTOR V3
 * Coletor especializado para análise de memória sequencial
 * Localização: src/games/MemoryGame/collectors/SequenceMemoryCollector.js
 */

export class SequenceMemoryCollector {
  constructor() {
    this.id = 'sequence_memory_v3';
    this.name = 'Memória Sequencial V3';
    this.version = '3.0.0';
    this.category = 'sequential_processing';
    
    this.data = {
      sequences: [],
      sequenceAccuracy: [],
      positionErrors: [],
      serialPositionEffect: [],
      workingMemoryLoad: [],
      chunking: [],
      temporalOrder: [],
      rehearsalStrategies: []
    };
  }

  // Coletar dados de tentativas de sequência
  collect(data) {
    const {
      originalSequence,
      userSequence,
      responseTime,
      accuracy,
      difficulty,
      sequenceLength,
      timestamp
    } = data;

    // Análise detalhada da sequência
    const sequenceAnalysis = this.analyzeSequence(originalSequence, userSequence);
    
    this.data.sequences.push({
      original: originalSequence,
      response: userSequence,
      accuracy,
      sequenceLength,
      analysis: sequenceAnalysis,
      responseTime,
      timestamp
    });

    // Precisão sequencial
    this.data.sequenceAccuracy.push({
      accuracy,
      sequenceLength,
      difficulty,
      timestamp
    });

    // Análise de erros por posição
    const positionErrorAnalysis = this.analyzePositionErrors(originalSequence, userSequence);
    this.data.positionErrors.push(positionErrorAnalysis);

    // Efeito de posição serial
    const serialEffect = this.analyzeSerialPositionEffect(originalSequence, userSequence);
    this.data.serialPositionEffect.push(serialEffect);

    // Carga de memória de trabalho
    const workingMemoryLoad = this.calculateWorkingMemoryLoad(sequenceLength, accuracy);
    this.data.workingMemoryLoad.push({
      load: workingMemoryLoad,
      performance: accuracy,
      efficiency: accuracy / workingMemoryLoad,
      timestamp
    });

    // Análise de chunking
    const chunkingAnalysis = this.analyzeChunking(originalSequence, userSequence);
    this.data.chunking.push(chunkingAnalysis);

    // Ordem temporal
    const temporalOrderAnalysis = this.analyzeTemporalOrder(originalSequence, userSequence, responseTime);
    this.data.temporalOrder.push(temporalOrderAnalysis);
  }

  analyzeSequence(original, response) {
    if (!original || !response) return { similarity: 0, errors: [] };

    const similarity = this.calculateSequenceSimilarity(original, response);
    const errors = this.identifySequenceErrors(original, response);
    const orderPreservation = this.analyzeOrderPreservation(original, response);

    return {
      similarity,
      errors,
      orderPreservation,
      totalErrors: errors.length,
      errorRate: errors.length / original.length,
      preservationScore: orderPreservation.score
    };
  }

  calculateSequenceSimilarity(seq1, seq2) {
    if (!seq1 || !seq2) return 0;
    
    const maxLength = Math.max(seq1.length, seq2.length);
    let matches = 0;

    for (let i = 0; i < maxLength; i++) {
      if (seq1[i] === seq2[i]) matches++;
    }

    return matches / maxLength;
  }

  identifySequenceErrors(original, response) {
    const errors = [];
    
    for (let i = 0; i < Math.max(original.length, response.length); i++) {
      if (original[i] !== response[i]) {
        errors.push({
          position: i,
          expected: original[i],
          actual: response[i],
          type: this.categorizeSequenceError(original, response, i),
          severity: this.calculateErrorSeverity(original, response, i)
        });
      }
    }

    return errors;
  }

  categorizeSequenceError(original, response, position) {
    const expected = original[position];
    const actual = response[position];

    if (!actual) return 'omission';
    if (!expected) return 'insertion';

    // Verificar se é transposição (troca de posições)
    const isTransposition = this.checkTransposition(original, response, position);
    if (isTransposition) return 'transposition';

    // Verificar se é substituição próxima
    if (this.isNearbySubstitution(original, actual, position)) {
      return 'nearby_substitution';
    }

    return 'substitution';
  }

  checkTransposition(original, response, position) {
    const current = response[position];
    
    // Verificar se o elemento atual aparece em posição próxima na sequência original
    for (let i = Math.max(0, position - 2); i <= Math.min(original.length - 1, position + 2); i++) {
      if (i !== position && original[i] === current) {
        return true;
      }
    }
    
    return false;
  }

  isNearbySubstitution(original, actual, position) {
    // Verificar se a resposta está próxima na sequência original
    const window = 2;
    for (let i = Math.max(0, position - window); i <= Math.min(original.length - 1, position + window); i++) {
      if (original[i] === actual) return true;
    }
    return false;
  }

  calculateErrorSeverity(original, response, position) {
    const sequenceLength = original.length;
    
    // Posições no início e fim são mais críticas
    const positionWeight = position === 0 || position === sequenceLength - 1 ? 1.5 : 1.0;
    
    // Erros de omissão são mais severos
    const typeWeight = !response[position] ? 2.0 : 1.0;
    
    return positionWeight * typeWeight;
  }

  analyzeOrderPreservation(original, response) {
    let preservedPairs = 0;
    let totalPairs = Math.max(0, original.length - 1);

    for (let i = 0; i < totalPairs; i++) {
      const originalPair = [original[i], original[i + 1]];
      const responsePair = [response[i], response[i + 1]];
      
      if (originalPair[0] === responsePair[0] && originalPair[1] === responsePair[1]) {
        preservedPairs++;
      }
    }

    return {
      preservedPairs,
      totalPairs,
      score: totalPairs > 0 ? preservedPairs / totalPairs : 0
    };
  }

  analyzePositionErrors(original, response) {
    const positionAnalysis = {
      beginning: { errors: 0, total: 0 },
      middle: { errors: 0, total: 0 },
      end: { errors: 0, total: 0 }
    };

    const length = original.length;
    
    for (let i = 0; i < length; i++) {
      let section;
      if (i < length * 0.33) {
        section = 'beginning';
      } else if (i > length * 0.67) {
        section = 'end';
      } else {
        section = 'middle';
      }

      positionAnalysis[section].total++;
      if (original[i] !== response[i]) {
        positionAnalysis[section].errors++;
      }
    }

    // Calcular taxas de erro por seção
    Object.keys(positionAnalysis).forEach(section => {
      const data = positionAnalysis[section];
      data.errorRate = data.total > 0 ? data.errors / data.total : 0;
    });

    return positionAnalysis;
  }

  analyzeSerialPositionEffect(original, response) {
    const positions = [];
    
    for (let i = 0; i < original.length; i++) {
      const isCorrect = original[i] === response[i];
      positions.push({
        position: i,
        relativePosition: i / (original.length - 1), // 0 = início, 1 = fim
        correct: isCorrect,
        primacy: i < original.length * 0.33,
        recency: i > original.length * 0.67
      });
    }

    // Calcular efeitos de primazia e recência
    const primacyItems = positions.filter(p => p.primacy);
    const recencyItems = positions.filter(p => p.recency);
    const middleItems = positions.filter(p => !p.primacy && !p.recency);

    return {
      positions,
      primacyEffect: primacyItems.length > 0 ? primacyItems.filter(p => p.correct).length / primacyItems.length : 0,
      recencyEffect: recencyItems.length > 0 ? recencyItems.filter(p => p.correct).length / recencyItems.length : 0,
      middlePerformance: middleItems.length > 0 ? middleItems.filter(p => p.correct).length / middleItems.length : 0
    };
  }

  calculateWorkingMemoryLoad(sequenceLength, accuracy) {
    // Carga baseada no comprimento e performance
    const baseLoad = Math.min(100, sequenceLength * 12);
    const performanceModifier = (1 - accuracy) * 25;
    
    return Math.min(100, baseLoad + performanceModifier);
  }

  analyzeChunking(original, response) {
    // Detectar possíveis estratégias de chunking
    const possibleChunks = this.detectChunks(response);
    const chunkingEffectiveness = this.calculateChunkingEffectiveness(original, response, possibleChunks);

    return {
      detectedChunks: possibleChunks,
      effectiveness: chunkingEffectiveness,
      optimalChunkSize: this.calculateOptimalChunkSize(original.length),
      actualChunkSize: possibleChunks.length > 0 ? possibleChunks.reduce((sum, chunk) => sum + chunk.length, 0) / possibleChunks.length : 0
    };
  }

  detectChunks(sequence) {
    // Algoritmo simples para detectar possíveis chunks baseado em padrões
    const chunks = [];
    let currentChunk = [];

    for (let i = 0; i < sequence.length; i++) {
      currentChunk.push(sequence[i]);
      
      // Detectar quebra de chunk (placeholder - pode ser refinado)
      if (currentChunk.length >= 3 || i === sequence.length - 1) {
        chunks.push([...currentChunk]);
        currentChunk = [];
      }
    }

    return chunks.filter(chunk => chunk.length > 0);
  }

  calculateChunkingEffectiveness(original, response, chunks) {
    // Effectiveness baseada na precisão dentro dos chunks
    let totalCorrect = 0;
    let totalItems = 0;

    chunks.forEach((chunk, chunkIndex) => {
      chunk.forEach((item, itemIndex) => {
        const overallIndex = chunks.slice(0, chunkIndex).reduce((sum, c) => sum + c.length, 0) + itemIndex;
        if (overallIndex < original.length && original[overallIndex] === item) {
          totalCorrect++;
        }
        totalItems++;
      });
    });

    return totalItems > 0 ? totalCorrect / totalItems : 0;
  }

  calculateOptimalChunkSize(sequenceLength) {
    // Tamanho ótimo de chunk baseado na pesquisa cognitiva (Miller's 7±2)
    if (sequenceLength <= 4) return sequenceLength;
    if (sequenceLength <= 7) return Math.ceil(sequenceLength / 2);
    return 3; // Chunks de 3 itens são geralmente eficazes
  }

  analyzeTemporalOrder(original, response, responseTime) {
    const orderAccuracy = this.calculateSequenceSimilarity(original, response);
    const temporalConsistency = this.calculateTemporalConsistency(responseTime, original.length);

    return {
      orderAccuracy,
      temporalConsistency,
      averageTimePerItem: responseTime / original.length,
      timingPattern: this.analyzeTimingPattern(responseTime, original.length)
    };
  }

  calculateTemporalConsistency(totalTime, itemCount) {
    const averageTimePerItem = totalTime / itemCount;
    // Consistência baseada na uniformidade do tempo por item
    // Placeholder - em implementação real, precisaríamos dados de tempo por item
    return Math.max(0, 1 - (Math.abs(averageTimePerItem - 1000) / 2000));
  }

  analyzeTimingPattern(totalTime, itemCount) {
    const avgTimePerItem = totalTime / itemCount;
    
    if (avgTimePerItem < 500) return 'rapid';
    if (avgTimePerItem < 1000) return 'moderate';
    if (avgTimePerItem < 2000) return 'slow';
    return 'very_slow';
  }

  // Gerar relatório de análise sequencial
  generateReport() {
    const totalAttempts = this.data.sequences.length;
    if (totalAttempts === 0) return null;

    const avgAccuracy = this.data.sequenceAccuracy.reduce((sum, item) => sum + item.accuracy, 0) / totalAttempts;
    
    // Análise de efeitos de posição serial
    const serialEffects = this.calculateAverageSerialEffects();
    
    // Análise de chunking
    const chunkingAnalysis = this.calculateAverageChunking();

    return {
      collectorId: this.id,
      totalAttempts,
      avgAccuracy: Math.round(avgAccuracy * 100) / 100,
      serialPositionEffects: serialEffects,
      chunkingStrategy: chunkingAnalysis,
      recommendations: this.generateSequenceRecommendations(avgAccuracy, serialEffects),
      cognitiveInsights: {
        sequentialProcessingCapacity: this.calculateSequentialCapacity(),
        workingMemoryEfficiency: this.calculateWorkingMemoryEfficiency(),
        orderPreservationAbility: this.calculateOrderPreservation(),
        temporalProcessingSpeed: this.calculateTemporalProcessingSpeed()
      }
    };
  }

  calculateAverageSerialEffects() {
    if (this.data.serialPositionEffect.length === 0) return null;

    const effects = this.data.serialPositionEffect;
    const avgPrimacy = effects.reduce((sum, effect) => sum + effect.primacyEffect, 0) / effects.length;
    const avgRecency = effects.reduce((sum, effect) => sum + effect.recencyEffect, 0) / effects.length;
    const avgMiddle = effects.reduce((sum, effect) => sum + effect.middlePerformance, 0) / effects.length;

    return {
      primacyEffect: avgPrimacy,
      recencyEffect: avgRecency,
      middlePerformance: avgMiddle,
      serialCurvePattern: this.identifySerialCurvePattern(avgPrimacy, avgRecency, avgMiddle)
    };
  }

  identifySerialCurvePattern(primacy, recency, middle) {
    if (primacy > middle && recency > middle) return 'classic_u_shape';
    if (primacy > recency && primacy > middle) return 'primacy_dominant';
    if (recency > primacy && recency > middle) return 'recency_dominant';
    if (middle >= primacy && middle >= recency) return 'flat_or_inverted';
    return 'irregular';
  }

  calculateAverageChunking() {
    if (this.data.chunking.length === 0) return null;

    const chunks = this.data.chunking;
    const avgEffectiveness = chunks.reduce((sum, chunk) => sum + chunk.effectiveness, 0) / chunks.length;
    const avgChunkSize = chunks.reduce((sum, chunk) => sum + chunk.actualChunkSize, 0) / chunks.length;

    return {
      effectiveness: avgEffectiveness,
      averageChunkSize: avgChunkSize,
      strategy: this.identifyChunkingStrategy(avgChunkSize, avgEffectiveness)
    };
  }

  identifyChunkingStrategy(avgSize, effectiveness) {
    if (effectiveness > 0.8 && avgSize >= 2 && avgSize <= 4) return 'effective_chunking';
    if (avgSize < 2) return 'minimal_chunking';
    if (avgSize > 5) return 'oversized_chunks';
    return 'developing_chunking';
  }

  calculateSequentialCapacity() {
    if (this.data.sequenceAccuracy.length === 0) return 50;

    const recent = this.data.sequenceAccuracy.slice(-5);
    const maxLength = Math.max(...recent.map(item => item.sequenceLength));
    const avgAccuracy = recent.reduce((sum, item) => sum + item.accuracy, 0) / recent.length;

    return Math.min(100, (maxLength * 10) + (avgAccuracy * 40));
  }

  calculateWorkingMemoryEfficiency() {
    if (this.data.workingMemoryLoad.length === 0) return 50;

    const recent = this.data.workingMemoryLoad.slice(-5);
    const avgEfficiency = recent.reduce((sum, item) => sum + item.efficiency, 0) / recent.length;

    return Math.min(100, avgEfficiency * 100);
  }

  calculateOrderPreservation() {
    if (this.data.sequences.length === 0) return 50;

    const recent = this.data.sequences.slice(-5);
    const avgPreservation = recent.reduce((sum, seq) => sum + (seq.analysis.preservationScore || 0), 0) / recent.length;

    return avgPreservation * 100;
  }

  calculateTemporalProcessingSpeed() {
    if (this.data.temporalOrder.length === 0) return 50;

    const recent = this.data.temporalOrder.slice(-5);
    const avgTimePerItem = recent.reduce((sum, order) => sum + order.averageTimePerItem, 0) / recent.length;

    // Velocidade baseada no tempo por item (menor tempo = maior velocidade)
    return Math.max(0, 100 - (avgTimePerItem / 30));
  }

  generateSequenceRecommendations(accuracy, serialEffects) {
    const recommendations = [];

    if (accuracy < 0.7) {
      recommendations.push('Praticar com sequências mais curtas');
    }

    if (serialEffects && serialEffects.middlePerformance < 0.5) {
      recommendations.push('Exercícios para melhorar retenção de itens intermediários');
    }

    if (serialEffects && serialEffects.primacyEffect < 0.7) {
      recommendations.push('Treinar estratégias de atenção inicial');
    }

    return recommendations;
  }

  reset() {
    this.data = {
      sequences: [],
      sequenceAccuracy: [],
      positionErrors: [],
      serialPositionEffect: [],
      workingMemoryLoad: [],
      chunking: [],
      temporalOrder: [],
      rehearsalStrategies: []
    };
  }
}

export default SequenceMemoryCollector;
