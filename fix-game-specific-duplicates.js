#!/usr/bin/env node

/**
 * Script para corrigir métodos duplicados no GameSpecificProcessors.js
 */

import fs from 'fs';

const filePath = 'src/api/services/processors/GameSpecificProcessors.js';

// Métodos duplicados identificados
const duplicatedMethods = [
  'generateImmediateRecommendations',
  'generateShortTermRecommendations', 
  'generateLongTermRecommendations',
  'generateEnvironmentalRecommendations',
  'calculateOverallScore',
  'calculateConfidence',
  'calculateProgressRate',
  'estimateTimeToTarget',
  'determineCurrentLevel',
  'determineTargetLevel',
  'identifyMilestone',
  'generateNextSteps',
  'identifyStrengthAreas',
  'identifyDevelopmentAreas',
  'determineCognitiveStyle',
  'generateStyleRecommendations',
  'assessAdaptationNeeds',
  'assessPacingNeeds',
  'assessSupportNeeds'
];

function removeDuplicatedMethods(filePath) {
  try {
    console.log(`🔧 Processando ${filePath}...`);
    
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Para cada método duplicado, encontrar e remover duplicatas
    duplicatedMethods.forEach(methodName => {
      const methodRegex = new RegExp(`\\s+${methodName}\\s*\\([^{]*\\)\\s*\\{`, 'g');
      const matches = [...content.matchAll(methodRegex)];
      
      if (matches.length > 1) {
        console.log(`  ⚠️ Encontradas ${matches.length} ocorrências de ${methodName}`);
        
        // Remover todas as ocorrências exceto a primeira
        for (let i = matches.length - 1; i >= 1; i--) {
          const match = matches[i];
          const startIndex = match.index;
          
          // Encontrar o final do método (contando chaves)
          let braceCount = 0;
          let endIndex = startIndex;
          let inMethod = false;
          
          for (let j = startIndex; j < content.length; j++) {
            const char = content[j];
            
            if (char === '{') {
              braceCount++;
              inMethod = true;
            } else if (char === '}') {
              braceCount--;
              if (inMethod && braceCount === 0) {
                endIndex = j + 1;
                break;
              }
            }
          }
          
          // Remover o método duplicado
          if (endIndex > startIndex) {
            const beforeMethod = content.substring(0, startIndex);
            const afterMethod = content.substring(endIndex);
            content = beforeMethod + afterMethod;
            modified = true;
            console.log(`    ✅ Removida duplicata ${i} de ${methodName}`);
            
            // Recalcular matches após remoção
            const newMatches = [...content.matchAll(methodRegex)];
            matches.length = newMatches.length;
            for (let k = 0; k < newMatches.length; k++) {
              matches[k] = newMatches[k];
            }
          }
        }
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`  ✅ Arquivo ${filePath} corrigido`);
    } else {
      console.log(`  ℹ️ Nenhuma duplicata encontrada em ${filePath}`);
    }
    
  } catch (error) {
    console.error(`  ❌ Erro ao processar ${filePath}:`, error.message);
  }
}

// Processar o arquivo
console.log('🚀 Iniciando correção de métodos duplicados no GameSpecificProcessors...\n');

if (fs.existsSync(filePath)) {
  removeDuplicatedMethods(filePath);
} else {
  console.log(`⚠️ Arquivo não encontrado: ${filePath}`);
}

console.log('\n✅ Correção de métodos duplicados concluída!');
