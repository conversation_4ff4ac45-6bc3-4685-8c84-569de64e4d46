const e=e=>e&&e.version&&e.exportDate&&e.data&&"object"==typeof e.data&&e.metadata&&"object"==typeof e.metadata,r=r=>{if(!e(r))return null;const{data:p,metadata:h}=r,{userProfiles:g,gameProgress:f,gameMetrics:v,accessibilitySettings:y}=p;return!f||Object.keys(f).length,!v||Object.keys(v).length,{performance:{gameMetrics:v||{},gameProgress:f||{},userProfiles:g||[],sessionCount:a(f),avgAccuracy:t(f),timeSpent:o(f),completionRate:c(f)},neuroPedagogical:{cognitiveProfile:n(f),recommendedActivities:i(f),progressIndicators:s(f),skillsDistribution:l(f)},aiReport:{gameProgress:f||{},userProfiles:g||[],gameMetrics:v||{},learningPatterns:u(f),preferredTimeframes:d(f),skillsGrowth:m(f),emergingPatterns:{patterns:[],suggestions:["Explorar novas atividades para estimular diferentes habilidades","Continuar com a rotina atual para consolidar aprendizados"],confidenceScore:75}},multisensory:{visualData:{visualScore:75,colorDiscrimination:80,spatialPerception:70,visualTracking:75},auditoryData:{auditoryScore:70,soundDiscrimination:75,rhythmicPerception:65,sequentialProcessing:70},tactileData:{tactileScore:65,pressureSensitivity:70,textureDiscrimination:65,fineMotor:60},sensoryIntegration:{overallScore:70,visualAuditory:75,visualTactile:65,auditoryTactile:70},crossModalTransfer:{transferScore:65,primaryModality:"visual",transferEfficiency:"moderada",recommendations:["Exercícios de integração visual-auditiva","Atividades multissensoriais variadas"]}},metadata:{lastUpdate:(new Date).toISOString(),source:"backup_adapter",originalMetadata:h,serverError:h.serverError||null,version:r.version}}};function a(e){if(!e)return 0;let r=0;return Object.values(e).forEach(e=>{Array.isArray(e)&&(r+=e.length)}),r}function t(e){if(!e)return 0;let r=0,a=0;return Object.values(e).forEach(e=>{Array.isArray(e)&&e.forEach(e=>{const t=e.accuracy||(e.correctCount&&e.moveCount?e.correctCount/e.moveCount*100:null)||e.score||0;r+=t,a++})}),a>0?Math.round(r/a):0}function o(e){if(!e)return 0;let r=0;return Object.values(e).forEach(e=>{Array.isArray(e)&&e.forEach(e=>{r+=e.timeSpent||e.duration||0})}),r}function c(e){if(!e)return 0;let r=0,a=0;return Object.values(e).forEach(e=>{Array.isArray(e)&&e.forEach(e=>{(e.completed||!0===e.completed)&&r++,a++})}),a>0?Math.round(r/a*100):0}function n(e){const r={"number-counting":["matemática","raciocínio","atenção"],"visual-patterns":["percepção visual","atenção","memória visual"],"memory-game":["memória de trabalho","concentração"],"color-match":["atenção visual","tomada de decisão","velocidade de processamento"]},a={"atenção":0,"memória":0,"raciocínio":0,"percepção":0,velocidade:0,"concentração":0};if(!e)return a;let t={};return Object.entries(e).forEach(([e,o])=>{if(Array.isArray(o)&&o.length>0){let c=e.replace("betina_","").replace("_history","");const n=r[c]||["atenção"],i=o.reduce((e,r)=>e+(r.score||r.accuracy||0),0)/o.length;n.forEach(e=>{const r=function(e){return{"atenção visual":"atenção","atenção auditiva":"atenção","memória visual":"memória","memória auditiva":"memória","memória de trabalho":"memória","raciocínio lógico":"raciocínio","raciocínio espacial":"raciocínio","percepção visual":"percepção","percepção auditiva":"percepção","velocidade de processamento":"velocidade","tomada de decisão":"velocidade","concentração":"concentração",foco:"concentração","matemática":"raciocínio"}[e.toLowerCase()]||e.toLowerCase()}(e);void 0!==a[r]&&(t[r]||(t[r]=0),a[r]+=i,t[r]++)})}}),Object.keys(a).forEach(e=>{t[e]&&t[e]>0&&(a[e]=Math.round(a[e]/t[e]))}),a}function i(e){if(!e)return[];const r=[],a=n(e);return Object.entries(a).filter(([e,r])=>r<70&&r>0).map(([e,r])=>e).forEach(e=>{switch(e){case"atenção":r.push("Recomendado: Atividades focadas em atenção visual e sustentada");break;case"memória":r.push("Recomendado: Exercícios de memória de trabalho");break;case"raciocínio":r.push("Recomendado: Atividades de raciocínio lógico e sequencial");break;case"percepção":r.push("Recomendado: Exercícios de discriminação visual e espacial");break;case"velocidade":r.push("Recomendado: Atividades para melhorar tempo de resposta");break;case"concentração":r.push("Recomendado: Exercícios para desenvolvimento do foco atencional")}}),0===r.length&&r.push("Continue com a rotina atual de atividades"),r}function s(e){if(!e)return{};const r={};return Object.entries(e).forEach(([e,a])=>{if(Array.isArray(a)&&a.length>=2){const t=[...a].sort((e,r)=>new Date(e.timestamp)-new Date(r.timestamp)),o=t.slice(0,Math.floor(t.length/2)),c=t.slice(Math.floor(t.length/2)),n=o.reduce((e,r)=>e+(r.score||r.accuracy||0),0)/o.length,i=c.reduce((e,r)=>e+(r.score||r.accuracy||0),0)/c.length,s=i>0?Math.round((i-n)/n*100):0,l=e.replace("betina_","").replace("_history","").replace(/-/g," ").replace(/(^|\s)\S/g,e=>e.toUpperCase());r[l]={sessions:a.length,improvement:s,trend:s>10?"crescimento":s<-10?"declínio":"estável",lastScore:t[t.length-1].score||t[t.length-1].accuracy||0}}}),r}function l(e){if(!e)return{};const r=n(e);return Object.keys(r).forEach(e=>{r[e]=Math.max(1,r[e])}),r}function u(e){if(!e)return{};const r={consistencyScore:0,preferredGameType:"",learningCurve:"estável",bestPerformanceMetric:"",challengeAreas:[]},a={};let t=0;if(Object.entries(e).forEach(([e,r])=>{if(Array.isArray(r)&&r.length>0){const o=e.replace("betina_","").replace("_history","").replace(/-/g," ").replace(/(^|\s)\S/g,e=>e.toUpperCase());a[o]={count:r.length,avgScore:r.reduce((e,r)=>e+(r.score||r.accuracy||0),0)/r.length},t+=r.length}}),Object.keys(a).length>0){const e=Object.entries(a).sort(([,e],[,r])=>r.count-e.count);r.preferredGameType=e[0][0];const o=Object.values(a).map(e=>e.count),c=t/Object.keys(a).length,n=o.reduce((e,r)=>e+Math.pow(r-c,2),0)/o.length;r.consistencyScore=Math.min(100,Math.max(0,100-n/c*10));const i=Object.entries(a).filter(([,e])=>e.avgScore<70).map(([e])=>e);i.length>0&&(r.challengeAreas=i);const s=Object.entries(a).sort(([,e],[,r])=>r.avgScore-e.avgScore)[0];s&&(r.bestPerformanceMetric=s[0])}return r}function d(e){if(!e)return{};const r={morning:0,afternoon:0,evening:0,night:0};let a=0;Object.values(e).forEach(e=>{Array.isArray(e)&&e.forEach(e=>{if(e.timestamp){const t=new Date(e.timestamp).getHours();t>=6&&t<12?r.morning++:t>=12&&t<18?r.afternoon++:t>=18?r.evening++:r.night++,a++}})});const t={};if(a>0){Object.entries(r).forEach(([e,r])=>{t[e]=Math.round(r/a*100)});const e=Object.entries(r).sort(([,e],[,r])=>r-e)[0]?.[0]||"afternoon";t.preferred=e}else t.morning=25,t.afternoon=25,t.evening=25,t.night=25,t.preferred="afternoon";return t}function m(e){if(!e)return{};const r=[];Object.entries(e).forEach(([e,a])=>{Array.isArray(a)&&a.forEach(a=>{a.timestamp&&r.push({game:e.replace("betina_","").replace("_history",""),timestamp:new Date(a.timestamp),score:a.score||a.accuracy||0})})}),r.sort((e,r)=>e.timestamp-r.timestamp);const a={};r.forEach(e=>{const r=e.timestamp.toISOString().slice(0,7);a[r]||(a[r]={scores:[],games:new Set}),a[r].scores.push(e.score),a[r].games.add(e.game)});const t={labels:[],avgScores:[],diversity:[],trend:"estável"};if(Object.entries(a).forEach(([e,r])=>{t.labels.push(e);const a=r.scores.reduce((e,r)=>e+r,0)/r.scores.length;t.avgScores.push(Math.round(a)),t.diversity.push(r.games.size)}),t.avgScores.length>=2){const e=t.avgScores.slice(0,Math.floor(t.avgScores.length/2)),r=t.avgScores.slice(Math.floor(t.avgScores.length/2)),a=e.reduce((e,r)=>e+r,0)/e.length,o=(r.reduce((e,r)=>e+r,0)/r.length-a)/a*100;t.trend=o>10?"crescimento":o<-10?"declínio":"estável"}return t}export{r as extractGameDataFromBackup,e as isValidBackupFormat};
