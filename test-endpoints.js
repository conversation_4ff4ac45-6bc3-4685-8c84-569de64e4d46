#!/usr/bin/env node

/**
 * Script para testar todos os endpoints da API
 */

import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3000/api';

// Função para fazer requisições
async function makeRequest(method, endpoint, data = null, token = null) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    }
  };

  if (token) {
    options.headers['Authorization'] = `Bearer ${token}`;
  }

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return {
      status: response.status,
      success: response.ok,
      data: result
    };
  } catch (error) {
    return {
      status: 0,
      success: false,
      error: error.message
    };
  }
}

// Testes
async function runTests() {
  console.log('🧪 Testando endpoints da API Portal Betina V3...\n');

  // 1. Health Check
  console.log('1. 🏥 Testando Health Check...');
  const health = await makeRequest('GET', '/public/health');
  console.log(`   Status: ${health.status} - ${health.success ? '✅' : '❌'}`);
  if (health.success) {
    console.log(`   Resposta: ${health.data.message}`);
  }
  console.log();

  // 2. Lista de Jogos
  console.log('2. 🎮 Testando Lista de Jogos...');
  const games = await makeRequest('GET', '/public/games');
  console.log(`   Status: ${games.status} - ${games.success ? '✅' : '❌'}`);
  if (games.success) {
    console.log(`   Total de jogos: ${games.data.data.total}`);
  }
  console.log();

  // 3. Atividades
  console.log('3. 📋 Testando Atividades...');
  const activities = await makeRequest('GET', '/public/activities');
  console.log(`   Status: ${activities.status} - ${activities.success ? '✅' : '❌'}`);
  console.log();

  // 4. Login
  console.log('4. 🔐 Testando Login...');
  const login = await makeRequest('POST', '/auth/login', {
    email: '<EMAIL>',
    password: 'admin123'
  });
  console.log(`   Status: ${login.status} - ${login.success ? '✅' : '❌'}`);
  
  let token = null;
  if (login.success && login.data.data && login.data.data.token) {
    token = login.data.data.token;
    console.log(`   Token obtido: ${token.substring(0, 20)}...`);
  } else {
    console.log(`   Erro: ${login.data?.message || login.error}`);
  }
  console.log();

  // 5. Dashboard (se tiver token)
  if (token) {
    console.log('5. 📊 Testando Dashboard...');
    const dashboard = await makeRequest('GET', '/dashboard/overview', null, token);
    console.log(`   Status: ${dashboard.status} - ${dashboard.success ? '✅' : '❌'}`);
    if (!dashboard.success) {
      console.log(`   Erro: ${dashboard.data?.message || dashboard.error}`);
    }
    console.log();
  }

  // 6. Métricas
  console.log('6. 📈 Testando Métricas...');
  const metrics = await makeRequest('GET', '/public/metrics');
  console.log(`   Status: ${metrics.status} - ${metrics.success ? '✅' : '❌'}`);
  console.log();

  console.log('✅ Testes concluídos!');
}

runTests().catch(console.error);
