/**
 * @file PatternTransformationCollector.js
 * @description Coletor especializado para análise de transformação de padrões
 * @version 3.0.0
 */

export class PatternTransformationCollector {
  constructor() {
    this.name = 'PatternTransformationCollector';
    this.version = '3.0.0';
    this.description = 'Analisa habilidades de transformação e manipulação de padrões visuais';
  }

  async collect(gameState) {
    try {
      const originalPattern = gameState.originalPattern || [];
      const transformationRules = gameState.transformationRules || [];
      const playerTransformation = gameState.playerTransformation || [];
      const correctTransformation = gameState.correctTransformation || [];

      return {
        // Métricas de transformação
        transformationAccuracy: this.calculateTransformationAccuracy(correctTransformation, playerTransformation),
        ruleApplicationAccuracy: this.assessRuleApplication(transformationRules, originalPattern, playerTransformation),
        transformationComplexity: this.evaluateTransformationComplexity(transformationRules),
        spatialTransformationScore: this.assessSpatialTransformation(originalPattern, playerTransformation),
        
        // Análise cognitiva
        mentalRotationAbility: this.assessMentalRotation(gameState),
        visualSpatialProcessing: this.evaluateVisualSpatialProcessing(originalPattern, playerTransformation),
        abstractReasoningIndex: this.assessAbstractReasoning(transformationRules, gameState.difficulty),
        cognitiveFlexibilityScore: this.evaluateCognitiveFlexibility(gameState.sessionAttempts || []),
        
        // Processamento executivo
        ruleRetentionScore: this.assessRuleRetention(transformationRules, gameState.transformationHistory || []),
        workingMemoryCapacity: this.evaluateWorkingMemoryCapacity(originalPattern, transformationRules),
        inhibitionOfIncorrectTransforms: this.assessTransformationInhibition(gameState),
        
        // Habilidades visuoespaciais
        spatialVisualizationIndex: this.assessSpatialVisualization(originalPattern, playerTransformation),
        geometricTransformationAccuracy: this.evaluateGeometricTransformations(gameState),
        patternManipulationSkill: this.assessPatternManipulation(originalPattern, playerTransformation, transformationRules),
        
        timestamp: Date.now(),
        sessionId: gameState.sessionId || 'unknown'
      };
    } catch (error) {
      console.error('Erro no PatternTransformationCollector:', error);
      return null;
    }
  }

  calculateTransformationAccuracy(correct, player) {
    if (!correct.length || !player.length) return 0;
    
    let accurateTransformations = 0;
    const maxLength = Math.max(correct.length, player.length);
    
    for (let i = 0; i < Math.min(correct.length, player.length); i++) {
      if (this.transformationsMatch(correct[i], player[i])) {
        accurateTransformations++;
      }
    }
    
    // Penaliza diferenças de tamanho
    const lengthPenalty = Math.abs(correct.length - player.length) * 5;
    const baseAccuracy = (accurateTransformations / correct.length) * 100;
    
    return Math.max(0, baseAccuracy - lengthPenalty);
  }

  transformationsMatch(transform1, transform2) {
    if (!transform1 || !transform2) return false;
    
    // Verifica correspondência das propriedades transformadas
    const properties = ['shape', 'color', 'size', 'orientation', 'position'];
    
    for (const prop of properties) {
      if (transform1[prop] !== undefined && transform2[prop] !== undefined) {
        if (!this.propertyValuesMatch(transform1[prop], transform2[prop], prop)) {
          return false;
        }
      }
    }
    
    return true;
  }

  propertyValuesMatch(value1, value2, property) {
    if (property === 'position') {
      // Para posições, permite pequena tolerância
      return Math.abs(value1.x - value2.x) < 10 && Math.abs(value1.y - value2.y) < 10;
    } else if (property === 'orientation') {
      // Para orientação, normaliza para 360 graus
      const normalizedValue1 = ((value1 % 360) + 360) % 360;
      const normalizedValue2 = ((value2 % 360) + 360) % 360;
      return Math.abs(normalizedValue1 - normalizedValue2) < 15; // 15 graus de tolerância
    } else {
      return value1 === value2;
    }
  }

  assessRuleApplication(rules, originalPattern, playerTransformation) {
    if (!rules.length || !originalPattern.length || !playerTransformation.length) return 0;
    
    let correctApplications = 0;
    
    for (let i = 0; i < Math.min(originalPattern.length, playerTransformation.length); i++) {
      const originalElement = originalPattern[i];
      const transformedElement = playerTransformation[i];
      
      const expectedTransformation = this.applyTransformationRules(originalElement, rules);
      
      if (this.transformationsMatch(expectedTransformation, transformedElement)) {
        correctApplications++;
      }
    }
    
    return (correctApplications / originalPattern.length) * 100;
  }

  applyTransformationRules(element, rules) {
    let transformedElement = { ...element };
    
    for (const rule of rules) {
      transformedElement = this.applySingleRule(transformedElement, rule);
    }
    
    return transformedElement;
  }

  applySingleRule(element, rule) {
    const transformed = { ...element };
    
    switch (rule.type) {
      case 'rotate':
        transformed.orientation = (transformed.orientation || 0) + rule.degrees;
        break;
      case 'scale':
        transformed.size = this.scaleSize(transformed.size, rule.factor);
        break;
      case 'translate':
        transformed.position = {
          x: (transformed.position?.x || 0) + rule.deltaX,
          y: (transformed.position?.y || 0) + rule.deltaY
        };
        break;
      case 'changeColor':
        transformed.color = rule.newColor;
        break;
      case 'changeShape':
        transformed.shape = rule.newShape;
        break;
      case 'flip':
        transformed.flipped = !transformed.flipped;
        if (rule.axis === 'horizontal') {
          transformed.position.y = -transformed.position.y;
        } else if (rule.axis === 'vertical') {
          transformed.position.x = -transformed.position.x;
        }
        break;
      default:
        break;
    }
    
    return transformed;
  }

  scaleSize(currentSize, factor) {
    const sizeMap = { small: 1, medium: 2, large: 3 };
    const reverseSizeMap = { 1: 'small', 2: 'medium', 3: 'large' };
    
    const numericSize = sizeMap[currentSize] || 2;
    const scaledSize = Math.max(1, Math.min(3, Math.round(numericSize * factor)));
    
    return reverseSizeMap[scaledSize] || currentSize;
  }

  evaluateTransformationComplexity(rules) {
    if (!rules.length) return 0;
    
    const complexityScores = {
      translate: 1,
      scale: 2,
      rotate: 3,
      changeColor: 2,
      changeShape: 3,
      flip: 4,
      composite: 5
    };
    
    const totalComplexity = rules.reduce((sum, rule) => {
      return sum + (complexityScores[rule.type] || 1);
    }, 0);
    
    return Math.min(100, (totalComplexity / rules.length) * 20);
  }

  assessSpatialTransformation(originalPattern, transformedPattern) {
    if (!originalPattern.length || !transformedPattern.length) return 50;
    
    const spatialMetrics = {
      positionAccuracy: this.assessPositionTransformation(originalPattern, transformedPattern),
      orientationAccuracy: this.assessOrientationTransformation(originalPattern, transformedPattern),
      scaleAccuracy: this.assessScaleTransformation(originalPattern, transformedPattern),
      spatialRelationshipPreservation: this.assessSpatialRelationships(originalPattern, transformedPattern)
    };
    
    return (
      spatialMetrics.positionAccuracy * 0.3 +
      spatialMetrics.orientationAccuracy * 0.3 +
      spatialMetrics.scaleAccuracy * 0.2 +
      spatialMetrics.spatialRelationshipPreservation * 0.2
    );
  }

  assessPositionTransformation(original, transformed) {
    let positionAccuracy = 0;
    
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalPos = original[i].position;
      const transformedPos = transformed[i].position;
      
      if (originalPos && transformedPos) {
        const distance = Math.sqrt(
          Math.pow(transformedPos.x - originalPos.x, 2) +
          Math.pow(transformedPos.y - originalPos.y, 2)
        );
        
        // Menor distância = melhor precisão (assumindo que não era para mover muito)
        const accuracy = Math.max(0, 100 - distance / 5);
        positionAccuracy += accuracy;
      } else {
        positionAccuracy += 50; // Posição não definida
      }
    }
    
    return original.length > 0 ? positionAccuracy / original.length : 0;
  }

  assessOrientationTransformation(original, transformed) {
    let orientationAccuracy = 0;
    
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalOrientation = original[i].orientation || 0;
      const transformedOrientation = transformed[i].orientation || 0;
      
      const angleDifference = Math.abs(transformedOrientation - originalOrientation);
      const normalizedDifference = Math.min(angleDifference, 360 - angleDifference);
      
      const accuracy = Math.max(0, 100 - normalizedDifference / 1.8); // 180 graus = 0% accuracy
      orientationAccuracy += accuracy;
    }
    
    return original.length > 0 ? orientationAccuracy / original.length : 100;
  }

  assessScaleTransformation(original, transformed) {
    let scaleAccuracy = 0;
    const sizeValues = { small: 1, medium: 2, large: 3 };
    
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalSize = sizeValues[original[i].size] || 2;
      const transformedSize = sizeValues[transformed[i].size] || 2;
      
      const scaleDifference = Math.abs(transformedSize - originalSize);
      const accuracy = Math.max(0, 100 - scaleDifference * 33.33); // Diferença de 1 = 33% penalty
      
      scaleAccuracy += accuracy;
    }
    
    return original.length > 0 ? scaleAccuracy / original.length : 100;
  }

  assessSpatialRelationships(original, transformed) {
    if (original.length < 2 || transformed.length < 2) return 100;
    
    let relationshipPreservation = 0;
    let totalRelationships = 0;
    
    for (let i = 0; i < original.length - 1; i++) {
      for (let j = i + 1; j < original.length; j++) {
        if (i < transformed.length && j < transformed.length) {
          const originalRelation = this.calculateSpatialRelationship(original[i], original[j]);
          const transformedRelation = this.calculateSpatialRelationship(transformed[i], transformed[j]);
          
          const relationshipMatch = this.compareSpatialRelationships(originalRelation, transformedRelation);
          relationshipPreservation += relationshipMatch;
          totalRelationships++;
        }
      }
    }
    
    return totalRelationships > 0 ? (relationshipPreservation / totalRelationships) * 100 : 100;
  }

  calculateSpatialRelationship(element1, element2) {
    if (!element1.position || !element2.position) {
      return { distance: 0, angle: 0 };
    }
    
    const deltaX = element2.position.x - element1.position.x;
    const deltaY = element2.position.y - element1.position.y;
    
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);
    
    return { distance, angle };
  }

  compareSpatialRelationships(relation1, relation2) {
    const distanceMatch = 1 - Math.abs(relation1.distance - relation2.distance) / Math.max(relation1.distance, relation2.distance, 1);
    const angleMatch = 1 - Math.abs(relation1.angle - relation2.angle) / 180;
    
    return Math.max(0, (distanceMatch + angleMatch) / 2);
  }

  assessMentalRotation(gameState) {
    const rotationTasks = this.extractRotationTasks(gameState);
    
    if (!rotationTasks.length) return 50;
    
    let rotationScore = 0;
    
    for (const task of rotationTasks) {
      const taskScore = this.evaluateRotationTask(task);
      rotationScore += taskScore;
    }
    
    return rotationScore / rotationTasks.length;
  }

  extractRotationTasks(gameState) {
    const transformationRules = gameState.transformationRules || [];
    const rotationTasks = [];
    
    for (const rule of transformationRules) {
      if (rule.type === 'rotate') {
        rotationTasks.push({
          degrees: rule.degrees,
          accuracy: this.calculateRotationAccuracy(rule, gameState),
          responseTime: gameState.responseTime || 0
        });
      }
    }
    
    return rotationTasks;
  }

  calculateRotationAccuracy(rotationRule, gameState) {
    // Calcula precisão da rotação baseada no resultado
    const expectedDegrees = rotationRule.degrees;
    const playerTransformation = gameState.playerTransformation || [];
    const originalPattern = gameState.originalPattern || [];
    
    if (!playerTransformation.length || !originalPattern.length) return 0;
    
    let rotationAccuracy = 0;
    
    for (let i = 0; i < Math.min(playerTransformation.length, originalPattern.length); i++) {
      const originalOrientation = originalPattern[i].orientation || 0;
      const playerOrientation = playerTransformation[i].orientation || 0;
      const expectedOrientation = originalOrientation + expectedDegrees;
      
      const error = Math.abs(playerOrientation - expectedOrientation);
      const normalizedError = Math.min(error, 360 - error);
      
      rotationAccuracy += Math.max(0, 100 - normalizedError / 1.8);
    }
    
    return originalPattern.length > 0 ? rotationAccuracy / originalPattern.length : 0;
  }

  evaluateRotationTask(task) {
    // Avalia desempenho em tarefa de rotação mental
    const accuracyScore = task.accuracy;
    const speedScore = this.evaluateRotationSpeed(task.degrees, task.responseTime);
    
    return (accuracyScore * 0.7 + speedScore * 0.3);
  }

  evaluateRotationSpeed(degrees, responseTime) {
    // Avalia velocidade de rotação mental (tempo esperado aumenta com ângulo)
    const expectedTime = Math.abs(degrees) * 20 + 2000; // 20ms por grau + 2s base
    const speedRatio = expectedTime / Math.max(responseTime, 1000);
    
    return Math.min(100, speedRatio * 100);
  }

  evaluateVisualSpatialProcessing(originalPattern, transformedPattern) {
    if (!originalPattern.length || !transformedPattern.length) return 50;
    
    const processingMetrics = {
      spatialMemoryAccuracy: this.assessSpatialMemory(originalPattern, transformedPattern),
      visualAttentionScore: this.assessVisualAttention(originalPattern, transformedPattern),
      spatialWorkingMemory: this.assessSpatialWorkingMemory(originalPattern),
      visualProcessingSpeed: this.assessVisualProcessingSpeed(originalPattern, transformedPattern)
    };
    
    return (
      processingMetrics.spatialMemoryAccuracy * 0.3 +
      processingMetrics.visualAttentionScore * 0.2 +
      processingMetrics.spatialWorkingMemory * 0.3 +
      processingMetrics.visualProcessingSpeed * 0.2
    );
  }

  assessSpatialMemory(original, transformed) {
    // Avalia memória espacial comparando padrões
    let memoryScore = 0;
    
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalElement = original[i];
      const transformedElement = transformed[i];
      
      if (originalElement.position && transformedElement.position) {
        // Verifica se relações espaciais foram mantidas
        const spatialConsistency = this.checkSpatialConsistency(originalElement, transformedElement, original, transformed, i);
        memoryScore += spatialConsistency;
      } else {
        memoryScore += 50; // Pontuação neutra para elementos sem posição
      }
    }
    
    return original.length > 0 ? memoryScore / original.length : 50;
  }

  checkSpatialConsistency(element1, element2, pattern1, pattern2, index) {
    // Verifica se as relações espaciais foram mantidas após transformação
    let consistencyScore = 0;
    let validComparisons = 0;
    
    for (let i = 0; i < pattern1.length; i++) {
      if (i !== index && i < pattern2.length) {
        const originalRelation = this.calculateSpatialRelationship(element1, pattern1[i]);
        const transformedRelation = this.calculateSpatialRelationship(element2, pattern2[i]);
        
        const relationMatch = this.compareSpatialRelationships(originalRelation, transformedRelation);
        consistencyScore += relationMatch;
        validComparisons++;
      }
    }
    
    return validComparisons > 0 ? (consistencyScore / validComparisons) * 100 : 100;
  }

  assessVisualAttention(original, transformed) {
    // Avalia atenção visual baseada na preservação de detalhes
    let attentionScore = 0;
    
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalElement = original[i];
      const transformedElement = transformed[i];
      
      const detailPreservation = this.calculateDetailPreservation(originalElement, transformedElement);
      attentionScore += detailPreservation;
    }
    
    return original.length > 0 ? attentionScore / original.length : 100;
  }

  calculateDetailPreservation(original, transformed) {
    // Calcula preservação de detalhes (características que não deveriam mudar)
    const preservableFeatures = ['shape', 'color'];
    let preservedFeatures = 0;
    
    for (const feature of preservableFeatures) {
      if (original[feature] === transformed[feature]) {
        preservedFeatures++;
      }
    }
    
    return (preservedFeatures / preservableFeatures.length) * 100;
  }

  assessSpatialWorkingMemory(pattern) {
    // Avalia capacidade de memória de trabalho espacial
    const patternComplexity = this.calculatePatternComplexity(pattern);
    const memoryLoad = Math.min(100, patternComplexity * 10);
    
    return memoryLoad;
  }

  calculatePatternComplexity(pattern) {
    if (!pattern.length) return 0;
    
    const complexityFactors = {
      elementCount: Math.min(pattern.length / 5, 1), // Normalizado para 5 elementos
      uniqueShapes: new Set(pattern.map(e => e.shape)).size,
      uniqueColors: new Set(pattern.map(e => e.color)).size,
      spatialDistribution: this.calculateSpatialDistribution(pattern)
    };
    
    return (
      complexityFactors.elementCount * 3 +
      complexityFactors.uniqueShapes * 2 +
      complexityFactors.uniqueColors * 2 +
      complexityFactors.spatialDistribution * 3
    ) / 10;
  }

  calculateSpatialDistribution(pattern) {
    // Calcula distribuição espacial dos elementos
    const positions = pattern.map(e => e.position).filter(pos => pos);
    
    if (positions.length < 2) return 0.5;
    
    let totalDistance = 0;
    let distanceCount = 0;
    
    for (let i = 0; i < positions.length - 1; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const distance = Math.sqrt(
          Math.pow(positions[j].x - positions[i].x, 2) +
          Math.pow(positions[j].y - positions[i].y, 2)
        );
        totalDistance += distance;
        distanceCount++;
      }
    }
    
    const averageDistance = totalDistance / distanceCount;
    return Math.min(1, averageDistance / 200); // Normalizado para 200 pixels
  }

  assessVisualProcessingSpeed(original, transformed) {
    // Avalia velocidade de processamento visual (baseado na complexidade vs tempo)
    const complexity = this.calculatePatternComplexity(original);
    const expectedProcessingTime = complexity * 3000; // 3 segundos por unidade de complexidade
    
    // Assumindo que temos tempo de resposta disponível
    const actualTime = transformed.processingTime || expectedProcessingTime;
    const speedRatio = expectedProcessingTime / Math.max(actualTime, 1000);
    
    return Math.min(100, speedRatio * 100);
  }

  assessAbstractReasoning(transformationRules, difficulty) {
    if (!transformationRules.length) return 50;
    
    const reasoningMetrics = {
      ruleComplexity: this.evaluateRuleComplexity(transformationRules),
      abstractionLevel: this.evaluateAbstractionLevel(transformationRules),
      logicalConsistency: this.assessLogicalConsistency(transformationRules)
    };
    
    const difficultyMultipliers = {
      easy: 0.7,
      medium: 1.0,
      hard: 1.3
    };
    
    const baseScore = (
      reasoningMetrics.ruleComplexity * 0.4 +
      reasoningMetrics.abstractionLevel * 0.4 +
      reasoningMetrics.logicalConsistency * 0.2
    );
    
    return Math.min(100, baseScore * (difficultyMultipliers[difficulty] || 1.0));
  }

  evaluateRuleComplexity(rules) {
    const complexityScores = {
      translate: 1,
      scale: 2,
      rotate: 3,
      changeColor: 2,
      changeShape: 3,
      flip: 4,
      conditional: 5,
      composite: 6
    };
    
    const totalComplexity = rules.reduce((sum, rule) => {
      return sum + (complexityScores[rule.type] || 1);
    }, 0);
    
    return Math.min(100, (totalComplexity / rules.length) * 15);
  }

  evaluateAbstractionLevel(rules) {
    const abstractionLevels = {
      translate: 1, // Concreto
      scale: 2,
      rotate: 2,
      changeColor: 1,
      changeShape: 3, // Mais abstrato
      flip: 3,
      conditional: 4, // Muito abstrato
      composite: 5 // Altamente abstrato
    };
    
    const totalAbstraction = rules.reduce((sum, rule) => {
      return sum + (abstractionLevels[rule.type] || 1);
    }, 0);
    
    return Math.min(100, (totalAbstraction / rules.length) * 20);
  }

  assessLogicalConsistency(rules) {
    // Avalia consistência lógica das regras
    if (rules.length < 2) return 100;
    
    const ruleTypes = rules.map(rule => rule.type);
    const uniqueTypes = new Set(ruleTypes);
    
    // Verifica se há contradições lógicas
    const contradictions = this.findRuleContradictions(rules);
    
    const consistencyScore = Math.max(0, 100 - contradictions.length * 20);
    return consistencyScore;
  }

  findRuleContradictions(rules) {
    const contradictions = [];
    
    for (let i = 0; i < rules.length - 1; i++) {
      for (let j = i + 1; j < rules.length; j++) {
        if (this.rulesContradict(rules[i], rules[j])) {
          contradictions.push({ rule1: rules[i], rule2: rules[j] });
        }
      }
    }
    
    return contradictions;
  }

  rulesContradict(rule1, rule2) {
    // Verifica se duas regras se contradizem
    if (rule1.type === 'rotate' && rule2.type === 'rotate') {
      // Duas rotações consecutivas podem ser redundantes
      return false; // Na verdade, podem ser combinadas
    }
    
    if (rule1.type === 'changeColor' && rule2.type === 'changeColor') {
      return rule1.newColor !== rule2.newColor; // Cores diferentes = contradição
    }
    
    if (rule1.type === 'changeShape' && rule2.type === 'changeShape') {
      return rule1.newShape !== rule2.newShape; // Formas diferentes = contradição
    }
    
    return false; // Por padrão, sem contradição
  }

  evaluateCognitiveFlexibility(sessionAttempts) {
    if (sessionAttempts.length < 3) return 50;
    
    const flexibilityMetrics = {
      strategyVariation: this.assessStrategyVariation(sessionAttempts),
      adaptationSpeed: this.assessAdaptationSpeed(sessionAttempts),
      errorRecovery: this.assessErrorRecovery(sessionAttempts)
    };
    
    return (
      flexibilityMetrics.strategyVariation * 0.4 +
      flexibilityMetrics.adaptationSpeed * 0.3 +
      flexibilityMetrics.errorRecovery * 0.3
    );
  }

  assessStrategyVariation(attempts) {
    const strategies = attempts.map(attempt => attempt.strategy || 'unknown');
    const uniqueStrategies = new Set(strategies);
    
    // Mais estratégias = maior flexibilidade
    const variationScore = (uniqueStrategies.size / attempts.length) * 100;
    return Math.min(100, variationScore);
  }

  assessAdaptationSpeed(attempts) {
    // Avalia rapidez de adaptação a novos padrões
    let adaptationScore = 0;
    
    for (let i = 1; i < attempts.length; i++) {
      const currentAccuracy = attempts[i].accuracy || 0;
      const previousAccuracy = attempts[i - 1].accuracy || 0;
      
      if (currentAccuracy > previousAccuracy) {
        adaptationScore += 20; // Bônus por melhoria
      }
    }
    
    return Math.min(100, adaptationScore);
  }

  assessErrorRecovery(attempts) {
    // Avalia capacidade de recuperação de erros
    let recoveryScore = 0;
    let recoveryOpportunities = 0;
    
    for (let i = 2; i < attempts.length; i++) {
      const twoAgo = attempts[i - 2].accuracy || 0;
      const oneAgo = attempts[i - 1].accuracy || 0;
      const current = attempts[i].accuracy || 0;
      
      if (oneAgo < twoAgo) { // Houve declínio
        recoveryOpportunities++;
        if (current > oneAgo) { // Houve recuperação
          recoveryScore++;
        }
      }
    }
    
    return recoveryOpportunities > 0 ? (recoveryScore / recoveryOpportunities) * 100 : 100;
  }

  assessRuleRetention(transformationRules, transformationHistory) {
    if (!transformationRules.length || !transformationHistory.length) return 50;
    
    // Verifica se as regras foram aplicadas consistentemente ao longo do tempo
    let retentionScore = 0;
    
    for (const rule of transformationRules) {
      const ruleApplications = transformationHistory.filter(entry => entry.rule === rule.type);
      const consistentApplications = ruleApplications.filter(app => app.correct).length;
      
      const ruleRetention = ruleApplications.length > 0 ? 
        (consistentApplications / ruleApplications.length) * 100 : 50;
      
      retentionScore += ruleRetention;
    }
    
    return retentionScore / transformationRules.length;
  }

  evaluateWorkingMemoryCapacity(originalPattern, transformationRules) {
    // Avalia capacidade de memória de trabalho baseada na complexidade da tarefa
    const patternComplexity = this.calculatePatternComplexity(originalPattern);
    const ruleComplexity = this.evaluateRuleComplexity(transformationRules);
    
    const totalComplexity = patternComplexity + ruleComplexity;
    const memoryLoad = Math.min(100, totalComplexity * 2);
    
    return memoryLoad;
  }

  assessTransformationInhibition(gameState) {
    // Avalia inibição de transformações incorretas
    const responseTime = gameState.responseTime || 0;
    const corrections = gameState.corrections || 0;
    const attempts = gameState.attempts || 1;
    
    // Tempo adequado indica deliberação (inibição de respostas rápidas)
    const timeScore = this.evaluateDeliberationTime(responseTime);
    
    // Poucas correções indicam boa inibição inicial
    const correctionScore = Math.max(0, 100 - (corrections / attempts) * 50);
    
    return (timeScore + correctionScore) / 2;
  }

  evaluateDeliberationTime(responseTime) {
    // Avalia tempo de deliberação
    const optimalRange = { min: 3000, max: 15000 }; // 3-15 segundos
    
    if (responseTime < optimalRange.min) {
      return 60; // Muito rápido, possível impulsividade
    } else if (responseTime > optimalRange.max) {
      return Math.max(40, 100 - ((responseTime - optimalRange.max) / 1000) * 2);
    } else {
      return 100; // Tempo ideal para deliberação
    }
  }

  assessSpatialVisualization(originalPattern, transformedPattern) {
    if (!originalPattern.length || !transformedPattern.length) return 50;
    
    const visualizationMetrics = {
      spatialAccuracy: this.assessSpatialTransformation(originalPattern, transformedPattern),
      visualMemoryPreservation: this.assessVisualMemoryPreservation(originalPattern, transformedPattern),
      spatialRelationshipUnderstanding: this.assessSpatialRelationships(originalPattern, transformedPattern)
    };
    
    return (
      visualizationMetrics.spatialAccuracy * 0.4 +
      visualizationMetrics.visualMemoryPreservation * 0.3 +
      visualizationMetrics.spatialRelationshipUnderstanding * 0.3
    );
  }

  assessVisualMemoryPreservation(original, transformed) {
    // Avalia preservação da memória visual durante transformação
    let preservationScore = 0;
    
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const originalElement = original[i];
      const transformedElement = transformed[i];
      
      // Características que devem ser preservadas
      const preservableFeatures = ['shape', 'color', 'pattern'];
      let preservedCount = 0;
      
      for (const feature of preservableFeatures) {
        if (originalElement[feature] === transformedElement[feature]) {
          preservedCount++;
        }
      }
      
      preservationScore += (preservedCount / preservableFeatures.length) * 100;
    }
    
    return original.length > 0 ? preservationScore / original.length : 100;
  }

  evaluateGeometricTransformations(gameState) {
    // Avalia precisão em transformações geométricas específicas
    const transformationRules = gameState.transformationRules || [];
    const geometricRules = transformationRules.filter(rule => 
      ['rotate', 'scale', 'flip', 'translate'].includes(rule.type)
    );
    
    if (!geometricRules.length) return 50;
    
    let geometricAccuracy = 0;
    
    for (const rule of geometricRules) {
      const ruleAccuracy = this.evaluateGeometricRule(rule, gameState);
      geometricAccuracy += ruleAccuracy;
    }
    
    return geometricAccuracy / geometricRules.length;
  }

  evaluateGeometricRule(rule, gameState) {
    // Avalia precisão na aplicação de uma regra geométrica específica
    switch (rule.type) {
      case 'rotate':
        return this.calculateRotationAccuracy(rule, gameState);
      case 'scale':
        return this.calculateScaleAccuracy(rule, gameState);
      case 'flip':
        return this.calculateFlipAccuracy(rule, gameState);
      case 'translate':
        return this.calculateTranslationAccuracy(rule, gameState);
      default:
        return 50;
    }
  }

  calculateScaleAccuracy(scaleRule, gameState) {
    const originalPattern = gameState.originalPattern || [];
    const playerTransformation = gameState.playerTransformation || [];
    
    if (!originalPattern.length || !playerTransformation.length) return 0;
    
    let scaleAccuracy = 0;
    const expectedFactor = scaleRule.factor;
    
    for (let i = 0; i < Math.min(originalPattern.length, playerTransformation.length); i++) {
      const originalSize = this.getSizeValue(originalPattern[i].size);
      const transformedSize = this.getSizeValue(playerTransformation[i].size);
      const actualFactor = transformedSize / originalSize;
      
      const factorError = Math.abs(actualFactor - expectedFactor);
      const accuracy = Math.max(0, 100 - factorError * 50);
      
      scaleAccuracy += accuracy;
    }
    
    return originalPattern.length > 0 ? scaleAccuracy / originalPattern.length : 0;
  }

  getSizeValue(size) {
    const sizeValues = { small: 1, medium: 2, large: 3 };
    return sizeValues[size] || 2;
  }

  calculateFlipAccuracy(flipRule, gameState) {
    const originalPattern = gameState.originalPattern || [];
    const playerTransformation = gameState.playerTransformation || [];
    
    if (!originalPattern.length || !playerTransformation.length) return 0;
    
    let flipAccuracy = 0;
    
    for (let i = 0; i < Math.min(originalPattern.length, playerTransformation.length); i++) {
      const originalPos = originalPattern[i].position;
      const transformedPos = playerTransformation[i].position;
      
      if (originalPos && transformedPos) {
        const flipCorrect = this.checkFlipCorrectness(originalPos, transformedPos, flipRule.axis);
        flipAccuracy += flipCorrect ? 100 : 0;
      } else {
        flipAccuracy += 50; // Posição não definida
      }
    }
    
    return originalPattern.length > 0 ? flipAccuracy / originalPattern.length : 0;
  }

  checkFlipCorrectness(originalPos, transformedPos, axis) {
    if (axis === 'horizontal') {
      return Math.abs(transformedPos.y + originalPos.y) < 20; // y deve ser invertido
    } else if (axis === 'vertical') {
      return Math.abs(transformedPos.x + originalPos.x) < 20; // x deve ser invertido
    }
    return false;
  }

  calculateTranslationAccuracy(translateRule, gameState) {
    const originalPattern = gameState.originalPattern || [];
    const playerTransformation = gameState.playerTransformation || [];
    
    if (!originalPattern.length || !playerTransformation.length) return 0;
    
    let translationAccuracy = 0;
    const expectedDeltaX = translateRule.deltaX || 0;
    const expectedDeltaY = translateRule.deltaY || 0;
    
    for (let i = 0; i < Math.min(originalPattern.length, playerTransformation.length); i++) {
      const originalPos = originalPattern[i].position;
      const transformedPos = playerTransformation[i].position;
      
      if (originalPos && transformedPos) {
        const actualDeltaX = transformedPos.x - originalPos.x;
        const actualDeltaY = transformedPos.y - originalPos.y;
        
        const errorX = Math.abs(actualDeltaX - expectedDeltaX);
        const errorY = Math.abs(actualDeltaY - expectedDeltaY);
        const totalError = Math.sqrt(errorX * errorX + errorY * errorY);
        
        const accuracy = Math.max(0, 100 - totalError / 5); // 5 pixels = 20% error
        translationAccuracy += accuracy;
      } else {
        translationAccuracy += 50; // Posição não definida
      }
    }
    
    return originalPattern.length > 0 ? translationAccuracy / originalPattern.length : 0;
  }

  assessPatternManipulation(originalPattern, transformedPattern, transformationRules) {
    // Avalia habilidade geral de manipulação de padrões
    if (!originalPattern.length || !transformedPattern.length || !transformationRules.length) return 50;
    
    const manipulationMetrics = {
      transformationAccuracy: this.calculateTransformationAccuracy(
        this.applyAllRules(originalPattern, transformationRules), 
        transformedPattern
      ),
      manipulationEfficiency: this.assessManipulationEfficiency(originalPattern, transformedPattern, transformationRules),
      patternIntegrity: this.assessPatternIntegrity(originalPattern, transformedPattern),
      manipulationStrategy: this.assessManipulationStrategy(transformationRules)
    };
    
    return (
      manipulationMetrics.transformationAccuracy * 0.4 +
      manipulationMetrics.manipulationEfficiency * 0.2 +
      manipulationMetrics.patternIntegrity * 0.2 +
      manipulationMetrics.manipulationStrategy * 0.2
    );
  }

  applyAllRules(pattern, rules) {
    // Aplica todas as regras ao padrão original
    let transformedPattern = pattern.map(element => ({ ...element }));
    
    for (const rule of rules) {
      transformedPattern = transformedPattern.map(element => this.applySingleRule(element, rule));
    }
    
    return transformedPattern;
  }

  assessManipulationEfficiency(original, transformed, rules) {
    // Avalia eficiência na manipulação (mínimo de passos necessários)
    const necessarySteps = rules.length;
    const actualComplexity = this.calculateManipulationComplexity(original, transformed);
    
    const efficiencyRatio = necessarySteps / Math.max(actualComplexity, 1);
    return Math.min(100, efficiencyRatio * 100);
  }

  calculateManipulationComplexity(original, transformed) {
    // Calcula complexidade da manipulação realizada
    let complexity = 0;
    
    for (let i = 0; i < Math.min(original.length, transformed.length); i++) {
      const changes = this.countElementChanges(original[i], transformed[i]);
      complexity += changes;
    }
    
    return complexity;
  }

  countElementChanges(originalElement, transformedElement) {
    // Conta mudanças em um elemento
    const properties = ['shape', 'color', 'size', 'orientation', 'position'];
    let changes = 0;
    
    for (const prop of properties) {
      if (!this.propertyValuesMatch(originalElement[prop], transformedElement[prop], prop)) {
        changes++;
      }
    }
    
    return changes;
  }

  assessPatternIntegrity(original, transformed) {
    // Avalia se a integridade do padrão foi mantida
    const structuralIntegrity = this.assessStructuralIntegrity(original, transformed);
    const visualCoherence = this.assessVisualCoherence(transformed);
    
    return (structuralIntegrity + visualCoherence) / 2;
  }

  assessStructuralIntegrity(original, transformed) {
    // Avalia se a estrutura do padrão foi mantida
    if (original.length !== transformed.length) {
      return 50; // Penaliza mudança no número de elementos
    }
    
    const relationshipPreservation = this.assessSpatialRelationships(original, transformed);
    return relationshipPreservation;
  }

  assessVisualCoherence(pattern) {
    // Avalia coerência visual do padrão transformado
    if (pattern.length < 2) return 100;
    
    const coherenceMetrics = {
      colorHarmony: this.assessColorHarmony(pattern),
      sizeConsistency: this.assessSizeConsistency(pattern),
      spatialBalance: this.assessSpatialBalance(pattern)
    };
    
    return (
      coherenceMetrics.colorHarmony * 0.4 +
      coherenceMetrics.sizeConsistency * 0.3 +
      coherenceMetrics.spatialBalance * 0.3
    );
  }

  assessColorHarmony(pattern) {
    // Avalia harmonia de cores no padrão
    const colors = pattern.map(element => element.color).filter(color => color);
    const uniqueColors = new Set(colors);
    
    // Padrão ideal tem 2-4 cores diferentes
    if (uniqueColors.size >= 2 && uniqueColors.size <= 4) {
      return 100;
    } else if (uniqueColors.size === 1) {
      return 70; // Monocromático é aceitável
    } else {
      return Math.max(30, 100 - (uniqueColors.size - 4) * 15); // Muitas cores
    }
  }

  assessSizeConsistency(pattern) {
    // Avalia consistência de tamanhos
    const sizes = pattern.map(element => element.size).filter(size => size);
    const uniqueSizes = new Set(sizes);
    
    // Variação moderada de tamanhos é ideal
    if (uniqueSizes.size >= 2 && uniqueSizes.size <= 3) {
      return 100;
    } else if (uniqueSizes.size === 1) {
      return 80; // Tamanho uniforme é bom
    } else {
      return 60; // Muita variação
    }
  }

  assessSpatialBalance(pattern) {
    // Avalia equilíbrio espacial do padrão
    const positions = pattern.map(element => element.position).filter(pos => pos);
    
    if (positions.length < 2) return 100;
    
    // Calcula centro de massa
    const centerX = positions.reduce((sum, pos) => sum + pos.x, 0) / positions.length;
    const centerY = positions.reduce((sum, pos) => sum + pos.y, 0) / positions.length;
    
    // Calcula desvio do centro
    let totalDeviation = 0;
    for (const pos of positions) {
      const deviation = Math.sqrt(
        Math.pow(pos.x - centerX, 2) + Math.pow(pos.y - centerY, 2)
      );
      totalDeviation += deviation;
    }
    
    const averageDeviation = totalDeviation / positions.length;
    const balanceScore = Math.max(0, 100 - averageDeviation / 5);
    
    return balanceScore;
  }

  assessManipulationStrategy(rules) {
    // Avalia estratégia de manipulação baseada na sequência de regras
    if (!rules.length) return 50;
    
    const strategyMetrics = {
      logicalOrder: this.assessLogicalOrder(rules),
      complexity: this.evaluateRuleComplexity(rules),
      efficiency: this.assessRuleEfficiency(rules)
    };
    
    return (
      strategyMetrics.logicalOrder * 0.4 +
      strategyMetrics.complexity * 0.3 +
      strategyMetrics.efficiency * 0.3
    );
  }

  assessLogicalOrder(rules) {
    // Avalia se as regras estão em ordem lógica
    const optimalOrder = ['translate', 'scale', 'rotate', 'changeColor', 'changeShape', 'flip'];
    
    let orderScore = 0;
    let lastOptimalIndex = -1;
    
    for (const rule of rules) {
      const currentOptimalIndex = optimalOrder.indexOf(rule.type);
      if (currentOptimalIndex >= lastOptimalIndex) {
        orderScore++;
        lastOptimalIndex = currentOptimalIndex;
      }
    }
    
    return (orderScore / rules.length) * 100;
  }

  assessRuleEfficiency(rules) {
    // Avalia eficiência das regras (sem redundâncias)
    const ruleTypes = rules.map(rule => rule.type);
    const uniqueTypes = new Set(ruleTypes);
    
    // Eficiência = número de tipos únicos / número total de regras
    const efficiency = uniqueTypes.size / rules.length;
    return efficiency * 100;
  }
}
