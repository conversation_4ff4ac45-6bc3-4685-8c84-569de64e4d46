/**
 * 📈 PROGRESSION MASTERY COLLECTOR
 * Coletor especializado em análise de progressão e domínio entre níveis de dificuldade
 * Portal Betina V3 - FASE 2.1
 */

export class ProgressionMasteryCollector {
  constructor() {
    this.difficultyLevels = {
      easy: {
        name: '<PERSON><PERSON>cil',
        colors: 3,
        items: 6,
        timeLimit: 90,
        masteryThreshold: 0.85,
        progressionThreshold: 0.75
      },
      medium: {
        name: '<PERSON><PERSON><PERSON>', 
        colors: 5,
        items: 9,
        timeLimit: 90,
        masteryThreshold: 0.80,
        progressionThreshold: 0.70
      },
      hard: {
        name: '<PERSON>fícil',
        colors: 8,
        items: 12,
        timeLimit: 60,
        masteryThreshold: 0.75,
        progressionThreshold: 0.65
      }
    };
    
    this.masteryIndicators = {
      accuracy: 'Precisão',
      speed: 'Velocidade',
      consistency: 'Consistência',
      confidence: 'Confiança',
      efficiency: 'Eficiência'
    };
    
    this.progressionStages = {
      introduction: 'Introdução',
      practice: 'Prática',
      consolidation: 'Consolidação',
      mastery: 'Domínio',
      maintenance: 'Manutenção'
    };
    
    this.adaptiveFactors = {
      performance_based: 'Baseado em Performance',
      time_based: 'Baseado em Tempo',
      consistency_based: 'Baseado em Consistência',
      user_preference: 'Preferência do Usuário'
    };
  }

  /**
   * Método padronizado de coleta de dados
   */
  collect(data) {
    return this.analyze(data);
  }

  /**
   * Análise principal da progressão e domínio
   */
  async analyze(data) {
    try {
      console.log('📈 ProgressionMasteryCollector: Iniciando análise de progressão...');
      
      if (!this.validateProgressionData(data)) {
        return this.generateFallbackAnalysis('Dados de progressão inválidos');
      }

      const progressionData = this.extractProgressionData(data);
      
      // Análises principais
      const currentMastery = this.analyzeCurrentMastery(progressionData);
      const progressionHistory = this.analyzeProgressionHistory(progressionData);
      const difficultyReadiness = this.assessDifficultyReadiness(progressionData);
      const masteryEvolution = this.analyzeMasteryEvolution(progressionData);
      const adaptiveRecommendations = this.generateAdaptiveRecommendations(progressionData);
      const retentionAnalysis = this.analyzeRetentionPatterns(progressionData);

      const results = {
        // Estado atual de domínio
        currentLevel: currentMastery.level,
        masteryScore: currentMastery.score,
        masteryIndicators: currentMastery.indicators,
        readinessForNext: currentMastery.readiness,
        
        // Histórico de progressão
        progressionPath: progressionHistory.path,
        milestones: progressionHistory.milestones,
        regressions: progressionHistory.regressions,
        plateaus: progressionHistory.plateaus,
        
        // Prontidão para mudança de dificuldade
        nextLevelReadiness: difficultyReadiness.readiness,
        readinessFactors: difficultyReadiness.factors,
        recommendedAction: difficultyReadiness.action,
        adaptationStrategy: difficultyReadiness.strategy,
        
        // Evolução do domínio
        masteryTrend: masteryEvolution.trend,
        improvementRate: masteryEvolution.rate,
        stabilityPeriod: masteryEvolution.stability,
        growthPhase: masteryEvolution.phase,
        
        // Análise por nível de dificuldade
        easyLevelMastery: this.analyzeLevelMastery(progressionData, 'easy'),
        mediumLevelMastery: this.analyzeLevelMastery(progressionData, 'medium'),
        hardLevelMastery: this.analyzeLevelMastery(progressionData, 'hard'),
        
        // Padrões de retenção
        shortTermRetention: retentionAnalysis.shortTerm,
        longTermRetention: retentionAnalysis.longTerm,
        forgettingCurve: retentionAnalysis.forgetting,
        transferLearning: retentionAnalysis.transfer,
        
        // Métricas de eficiência
        learningEfficiency: this.calculateLearningEfficiency(progressionData),
        timeToMastery: this.estimateTimeToMastery(progressionData),
        optimalChallenge: this.determineOptimalChallenge(progressionData),
        
        // Fatores de progressão
        motivationLevel: this.assessMotivationLevel(progressionData),
        engagementConsistency: this.assessEngagementConsistency(progressionData),
        challengePreference: this.identifyChallengePreferen ce(progressionData),
        
        // Recomendações adaptativas
        immediateRecommendations: adaptiveRecommendations.immediate,
        mediumTermGoals: adaptiveRecommendations.mediumTerm,
        longTermObjectives: adaptiveRecommendations.longTerm,
        
        // Análise comparativa
        peerComparison: this.generatePeerComparison(progressionData),
        ageAppropriate: this.assessAgeAppropriateness(currentMastery, data.userAge),
        expectedProgression: this.calculateExpectedProgression(progressionData),
        
        // Contexto da análise
        totalSessions: progressionData.sessions.length,
        timeSpan: progressionData.timeSpan,
        currentDifficulty: progressionData.currentDifficulty,
        
        // Indicadores de risco
        riskFactors: this.identifyRiskFactors(progressionData),
        interventionNeeds: this.assessInterventionNeeds(progressionData),
        
        // Recomendações específicas
        recommendations: this.generateProgressionRecommendations({
          currentMastery, progressionHistory, difficultyReadiness,
          masteryEvolution, adaptiveRecommendations, retentionAnalysis
        }),
        
        // Metadados
        analysisTimestamp: new Date().toISOString(),
        collectorVersion: '2.1.0',
        dataQuality: this.assessDataQuality(progressionData)
      };

      console.log('✅ ProgressionMasteryCollector: Análise concluída:', {
        level: results.currentLevel,
        mastery: Math.round(results.masteryScore * 100),
        readiness: results.nextLevelReadiness,
        trend: results.masteryTrend
      });

      return results;

    } catch (error) {
      console.error('❌ ProgressionMasteryCollector: Erro na análise:', error);
      return this.generateFallbackAnalysis(error.message);
    }
  }

  /**
   * Valida dados de progressão de entrada
   */
  validateProgressionData(data) {
    if (!data) return false;
    
    // Verificar se há dados de múltiplas sessões ou níveis
    const hasSessionData = data.sessions || data.sessionHistory || data.selectedItems;
    const hasDifficultyInfo = data.difficulty || data.currentLevel;
    
    return hasSessionData && hasDifficultyInfo;
  }

  /**
   * Extrai dados de progressão dos dados de entrada
   */
  extractProgressionData(data) {
    let sessions = [];
    let currentDifficulty = data.difficulty || 'medium';
    let currentSession = null;

    // Extrair sessões históricas ou criar sessão atual
    if (data.sessions && Array.isArray(data.sessions)) {
      sessions = data.sessions.map(session => this.normalizeSessionData(session));
    } else if (data.selectedItems) {
      // Criar sessão atual com base nos dados atuais
      currentSession = this.createCurrentSession(data);
      sessions = [currentSession];
    }

    // Organizar por dificuldade
    const sessionsByDifficulty = {
      easy: sessions.filter(s => s.difficulty === 'easy'),
      medium: sessions.filter(s => s.difficulty === 'medium'),
      hard: sessions.filter(s => s.difficulty === 'hard')
    };

    // Calcular span temporal
    const timestamps = sessions.map(s => s.timestamp).filter(t => t);
    const timeSpan = timestamps.length > 1 
      ? Math.max(...timestamps) - Math.min(...timestamps)
      : 0;

    return {
      sessions,
      sessionsByDifficulty,
      currentDifficulty,
      currentSession,
      timeSpan,
      userAge: data.userAge || null,
      totalSessions: sessions.length
    };
  }

  /**
   * Normaliza dados de sessão
   */
  normalizeSessionData(session) {
    return {
      timestamp: session.timestamp || Date.now(),
      difficulty: session.difficulty || 'medium',
      accuracy: session.accuracy || 0,
      completionTime: session.completionTime || 0,
      interactions: session.interactions || session.selectedItems || [],
      score: session.score || 0,
      completed: session.completed || false,
      errors: session.errors || 0,
      responseTime: session.averageResponseTime || 0
    };
  }

  /**
   * Cria sessão atual com base nos dados
   */
  createCurrentSession(data) {
    const interactions = data.selectedItems || [];
    const correct = interactions.filter(item => item.correct).length;
    const accuracy = interactions.length > 0 ? correct / interactions.length : 0;
    
    const responseTimes = interactions
      .map(item => item.responseTime)
      .filter(rt => rt > 0);
    const averageResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length
      : 0;

    return {
      timestamp: Date.now(),
      difficulty: data.difficulty || 'medium',
      accuracy,
      completionTime: data.sessionDuration || 0,
      interactions,
      score: Math.round(accuracy * 100),
      completed: data.completed || false,
      errors: interactions.length - correct,
      responseTime: averageResponseTime
    };
  }

  /**
   * Analisa domínio atual
   */
  analyzeCurrentMastery(progressionData) {
    const { currentSession, currentDifficulty, sessionsByDifficulty } = progressionData;
    
    // Analisar sessões do nível atual
    const currentLevelSessions = sessionsByDifficulty[currentDifficulty] || [];
    
    if (currentLevelSessions.length === 0) {
      return {
        level: currentDifficulty,
        score: 0,
        indicators: {},
        readiness: false
      };
    }

    // Calcular indicadores de domínio
    const indicators = this.calculateMasteryIndicators(currentLevelSessions);
    
    // Score geral de domínio
    const masteryScore = this.calculateOverallMasteryScore(indicators);
    
    // Prontidão para próximo nível
    const readiness = this.assessReadinessForNext(masteryScore, currentDifficulty);

    return {
      level: currentDifficulty,
      score: masteryScore,
      indicators,
      readiness,
      sessionsAnalyzed: currentLevelSessions.length,
      threshold: this.difficultyLevels[currentDifficulty].masteryThreshold
    };
  }

  /**
   * Calcula indicadores de domínio
   */
  calculateMasteryIndicators(sessions) {
    if (sessions.length === 0) {
      return {
        accuracy: 0,
        speed: 0,
        consistency: 0,
        confidence: 0,
        efficiency: 0
      };
    }

    // Precisão média
    const accuracy = sessions.reduce((sum, s) => sum + s.accuracy, 0) / sessions.length;
    
    // Velocidade (normalizada)
    const responseTimes = sessions.map(s => s.responseTime).filter(rt => rt > 0);
    const speed = responseTimes.length > 0
      ? Math.max(0, 1 - (responseTimes.reduce((sum, rt) => sum + rt, 0) / responseTimes.length) / 3000)
      : 0;
    
    // Consistência
    const accuracies = sessions.map(s => s.accuracy);
    const consistency = this.calculateConsistency(accuracies);
    
    // Confiança (baseada na estabilidade)
    const confidence = this.calculateConfidence(sessions);
    
    // Eficiência (combinação de precisão e velocidade)
    const efficiency = (accuracy * 0.7 + speed * 0.3);

    return {
      accuracy,
      speed,
      consistency,
      confidence,
      efficiency
    };
  }

  /**
   * Calcula score geral de domínio
   */
  calculateOverallMasteryScore(indicators) {
    const weights = {
      accuracy: 0.35,
      speed: 0.15,
      consistency: 0.25,
      confidence: 0.15,
      efficiency: 0.10
    };

    return Object.entries(weights).reduce((score, [indicator, weight]) => {
      return score + (indicators[indicator] || 0) * weight;
    }, 0);
  }

  /**
   * Avalia prontidão para próximo nível
   */
  assessReadinessForNext(masteryScore, currentDifficulty) {
    const threshold = this.difficultyLevels[currentDifficulty].masteryThreshold;
    return masteryScore >= threshold;
  }

  /**
   * Analisa histórico de progressão
   */
  analyzeProgressionHistory(progressionData) {
    const { sessions } = progressionData;
    
    if (sessions.length < 2) {
      return {
        path: ['insufficient_data'],
        milestones: [],
        regressions: [],
        plateaus: []
      };
    }

    // Analisar caminho de progressão
    const path = this.traceDifficultyProgression(sessions);
    
    // Identificar marcos importantes
    const milestones = this.identifyMilestones(sessions);
    
    // Detectar regressões
    const regressions = this.detectRegressions(sessions);
    
    // Identificar platôs
    const plateaus = this.identifyPlateaus(sessions);

    return {
      path,
      milestones,
      regressions,
      plateaus,
      totalProgress: this.calculateTotalProgress(sessions)
    };
  }

  /**
   * Rastreia progressão de dificuldade
   */
  traceDifficultyProgression(sessions) {
    const progression = [];
    let currentDiff = null;
    
    sessions.forEach(session => {
      if (session.difficulty !== currentDiff) {
        progression.push({
          difficulty: session.difficulty,
          timestamp: session.timestamp,
          transition: currentDiff ? `${currentDiff} -> ${session.difficulty}` : 'initial'
        });
        currentDiff = session.difficulty;
      }
    });
    
    return progression;
  }

  /**
   * Identifica marcos importantes
   */
  identifyMilestones(sessions) {
    const milestones = [];
    
    // Marcos de precisão
    const precisionMilestones = [0.5, 0.7, 0.8, 0.9];
    const maxAccuracy = Math.max(...sessions.map(s => s.accuracy));
    
    precisionMilestones.forEach(threshold => {
      if (maxAccuracy >= threshold) {
        const firstAchievement = sessions.find(s => s.accuracy >= threshold);
        if (firstAchievement) {
          milestones.push({
            type: 'accuracy',
            threshold,
            achievedAt: firstAchievement.timestamp,
            session: firstAchievement
          });
        }
      }
    });

    // Marcos de velocidade
    const speedImprovement = this.calculateSpeedImprovement(sessions);
    if (speedImprovement > 0.2) {
      milestones.push({
        type: 'speed_improvement',
        improvement: speedImprovement,
        achievedAt: sessions[sessions.length - 1].timestamp
      });
    }

    // Marcos de consistência
    const consistencyAchievement = this.findConsistencyMilestone(sessions);
    if (consistencyAchievement) {
      milestones.push(consistencyAchievement);
    }

    return milestones.sort((a, b) => a.achievedAt - b.achievedAt);
  }

  /**
   * Detecta regressões
   */
  detectRegressions(sessions) {
    const regressions = [];
    const windowSize = Math.min(5, Math.floor(sessions.length / 3));
    
    for (let i = windowSize; i < sessions.length - windowSize; i++) {
      const before = sessions.slice(i - windowSize, i);
      const after = sessions.slice(i, i + windowSize);
      
      const beforeAvg = before.reduce((sum, s) => sum + s.accuracy, 0) / before.length;
      const afterAvg = after.reduce((sum, s) => sum + s.accuracy, 0) / after.length;
      
      if (beforeAvg - afterAvg > 0.15) { // Regressão significativa
        regressions.push({
          startIndex: i,
          severity: beforeAvg - afterAvg,
          beforeAccuracy: beforeAvg,
          afterAccuracy: afterAvg,
          timestamp: sessions[i].timestamp
        });
      }
    }
    
    return regressions;
  }

  /**
   * Identifica platôs
   */
  identifyPlateaus(sessions) {
    const plateaus = [];
    const minPlateauLength = Math.max(3, Math.floor(sessions.length / 5));
    
    for (let i = 0; i <= sessions.length - minPlateauLength; i++) {
      const segment = sessions.slice(i, i + minPlateauLength);
      const accuracies = segment.map(s => s.accuracy);
      const variance = this.calculateVariance(accuracies);
      
      if (variance < 0.01) { // Muito pouca variação
        plateaus.push({
          startIndex: i,
          length: minPlateauLength,
          averageAccuracy: accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length,
          timestamp: segment[0].timestamp
        });
      }
    }
    
    return plateaus;
  }

  /**
   * Avalia prontidão para mudança de dificuldade
   */
  assessDifficultyReadiness(progressionData) {
    const currentMastery = this.analyzeCurrentMastery(progressionData);
    const { currentDifficulty, sessionsByDifficulty } = progressionData;
    
    // Fatores de prontidão
    const factors = this.evaluateReadinessFactors(currentMastery, sessionsByDifficulty[currentDifficulty]);
    
    // Ação recomendada
    const action = this.determineRecommendedAction(factors, currentDifficulty);
    
    // Estratégia de adaptação
    const strategy = this.developAdaptationStrategy(factors, action);
    
    // Prontidão geral
    const readiness = this.calculateOverallReadiness(factors);

    return {
      readiness,
      factors,
      action,
      strategy,
      confidence: this.calculateReadinessConfidence(factors)
    };
  }

  /**
   * Avalia fatores de prontidão
   */
  evaluateReadinessFactors(currentMastery, sessions) {
    return {
      accuracy: {
        score: currentMastery.indicators.accuracy,
        ready: currentMastery.indicators.accuracy >= currentMastery.threshold,
        weight: 0.4
      },
      consistency: {
        score: currentMastery.indicators.consistency,
        ready: currentMastery.indicators.consistency >= 0.7,
        weight: 0.3
      },
      confidence: {
        score: currentMastery.indicators.confidence,
        ready: currentMastery.indicators.confidence >= 0.7,
        weight: 0.2
      },
      experience: {
        score: Math.min(1, sessions.length / 10),
        ready: sessions.length >= 5,
        weight: 0.1
      }
    };
  }

  /**
   * Determina ação recomendada
   */
  determineRecommendedAction(factors, currentDifficulty) {
    const readyFactors = Object.values(factors).filter(f => f.ready).length;
    const totalFactors = Object.keys(factors).length;
    const readinessRatio = readyFactors / totalFactors;
    
    if (readinessRatio >= 0.75) {
      return this.getNextDifficulty(currentDifficulty) ? 'advance' : 'maintain_mastery';
    } else if (readinessRatio >= 0.5) {
      return 'consolidate';
    } else {
      return 'reinforce';
    }
  }

  /**
   * Desenvolve estratégia de adaptação
   */
  developAdaptationStrategy(factors, action) {
    const strategies = [];
    
    switch (action) {
      case 'advance':
        strategies.push('Introduzir próximo nível gradualmente');
        strategies.push('Manter suporte para consolidação');
        break;
      case 'consolidate':
        strategies.push('Focar em pontos fracos identificados');
        strategies.push('Aumentar prática em áreas específicas');
        break;
      case 'reinforce':
        strategies.push('Voltar ao básico se necessário');
        strategies.push('Aumentar tempo de prática');
        break;
      default:
        strategies.push('Manter nível atual com variações');
    }
    
    return strategies;
  }

  /**
   * Calcula prontidão geral
   */
  calculateOverallReadiness(factors) {
    return Object.values(factors).reduce((total, factor) => {
      return total + (factor.ready ? factor.weight : 0);
    }, 0);
  }

  /**
   * Obtém próxima dificuldade
   */
  getNextDifficulty(currentDifficulty) {
    const levels = ['easy', 'medium', 'hard'];
    const currentIndex = levels.indexOf(currentDifficulty);
    return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
  }

  /**
   * Analisa evolução do domínio
   */
  analyzeMasteryEvolution(progressionData) {
    const { sessions } = progressionData;
    
    if (sessions.length < 3) {
      return {
        trend: 'insufficient_data',
        rate: 0,
        stability: 0,
        phase: 'unknown'
      };
    }

    // Calcular tendência de domínio
    const masteryScores = sessions.map(session => {
      const indicators = this.calculateMasteryIndicators([session]);
      return this.calculateOverallMasteryScore(indicators);
    });

    const trend = this.calculateTrend(masteryScores);
    const improvementRate = this.calculateImprovementRate(masteryScores);
    const stabilityPeriod = this.calculateStabilityPeriod(masteryScores);
    const growthPhase = this.identifyGrowthPhase(masteryScores, trend);

    return {
      trend: trend.direction,
      rate: improvementRate,
      stability: stabilityPeriod,
      phase: growthPhase,
      trendStrength: trend.strength
    };
  }

  /**
   * Calcula tendência
   */
  calculateTrend(values) {
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;
    
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    
    return {
      slope,
      direction: slope > 0.05 ? 'improving' : slope < -0.05 ? 'declining' : 'stable',
      strength: Math.abs(slope)
    };
  }

  /**
   * Calcula taxa de melhoria
   */
  calculateImprovementRate(values) {
    if (values.length < 2) return 0;
    
    let totalImprovement = 0;
    let improvements = 0;
    
    for (let i = 1; i < values.length; i++) {
      const change = values[i] - values[i - 1];
      if (change > 0) {
        totalImprovement += change;
        improvements++;
      }
    }
    
    return improvements > 0 ? totalImprovement / improvements : 0;
  }

  /**
   * Calcula período de estabilidade
   */
  calculateStabilityPeriod(values) {
    if (values.length < 3) return 0;
    
    let maxStableLength = 0;
    let currentStableLength = 1;
    const threshold = 0.05;
    
    for (let i = 1; i < values.length; i++) {
      if (Math.abs(values[i] - values[i - 1]) < threshold) {
        currentStableLength++;
      } else {
        maxStableLength = Math.max(maxStableLength, currentStableLength);
        currentStableLength = 1;
      }
    }
    
    return Math.max(maxStableLength, currentStableLength) / values.length;
  }

  /**
   * Identifica fase de crescimento
   */
  identifyGrowthPhase(values, trend) {
    const latest = values.slice(-3);
    const average = latest.reduce((sum, val) => sum + val, 0) / latest.length;
    
    if (trend.direction === 'improving' && average < 0.3) return 'early_learning';
    if (trend.direction === 'improving' && average < 0.7) return 'skill_building';
    if (trend.direction === 'improving' && average >= 0.7) return 'mastery_development';
    if (trend.direction === 'stable' && average >= 0.8) return 'mastery_maintenance';
    if (trend.direction === 'stable' && average < 0.8) return 'plateau';
    if (trend.direction === 'declining') return 'regression';
    
    return 'unknown';
  }

  /**
   * Analisa domínio por nível
   */
  analyzeLevelMastery(progressionData, level) {
    const levelSessions = progressionData.sessionsByDifficulty[level] || [];
    
    if (levelSessions.length === 0) {
      return {
        attempted: false,
        mastery: 0,
        sessions: 0,
        progress: 'not_attempted'
      };
    }

    const indicators = this.calculateMasteryIndicators(levelSessions);
    const masteryScore = this.calculateOverallMasteryScore(indicators);
    const threshold = this.difficultyLevels[level].masteryThreshold;
    
    return {
      attempted: true,
      mastery: masteryScore,
      sessions: levelSessions.length,
      progress: masteryScore >= threshold ? 'mastered' : 
               masteryScore >= threshold * 0.8 ? 'near_mastery' :
               masteryScore >= threshold * 0.6 ? 'developing' : 'beginning',
      indicators,
      threshold,
      lastAttempt: Math.max(...levelSessions.map(s => s.timestamp))
    };
  }

  /**
   * Analisa padrões de retenção
   */
  analyzeRetentionPatterns(progressionData) {
    const { sessions } = progressionData;
    
    if (sessions.length < 5) {
      return {
        shortTerm: 0.5,
        longTerm: 0.5,
        forgetting: 'insufficient_data',
        transfer: 0.5
      };
    }

    // Retenção de curto prazo (últimas 3 sessões)
    const recentSessions = sessions.slice(-3);
    const shortTermRetention = this.calculateRetention(recentSessions);
    
    // Retenção de longo prazo (comparar início e fim)
    const earlyPerformance = sessions.slice(0, 3);
    const laterPerformance = sessions.slice(-3);
    const longTermRetention = this.calculateRetention([...earlyPerformance, ...laterPerformance]);
    
    // Curva de esquecimento
    const forgettingCurve = this.analyzeForgettingCurve(sessions);
    
    // Transferência de aprendizado entre níveis
    const transferLearning = this.analyzeTransferLearning(progressionData);

    return {
      shortTerm: shortTermRetention,
      longTerm: longTermRetention,
      forgetting: forgettingCurve,
      transfer: transferLearning
    };
  }

  /**
   * Calcula retenção
   */
  calculateRetention(sessions) {
    if (sessions.length < 2) return 0.5;
    
    const accuracies = sessions.map(s => s.accuracy);
    const trend = this.calculateTrend(accuracies);
    
    // Retenção baseada na estabilidade e tendência
    return Math.max(0, Math.min(1, 0.5 + trend.slope));
  }

  /**
   * Analisa curva de esquecimento
   */
  analyzeForgettingCurve(sessions) {
    // Procurar padrões de declínio após períodos de inatividade
    let forgettingDetected = false;
    
    for (let i = 1; i < sessions.length; i++) {
      const timeDiff = sessions[i].timestamp - sessions[i - 1].timestamp;
      const performanceDiff = sessions[i].accuracy - sessions[i - 1].accuracy;
      
      // Se houve pausa longa e declínio na performance
      if (timeDiff > 7 * 24 * 60 * 60 * 1000 && performanceDiff < -0.1) { // 7 dias
        forgettingDetected = true;
        break;
      }
    }
    
    return forgettingDetected ? 'detected' : 'not_detected';
  }

  /**
   * Analisa transferência de aprendizado
   */
  analyzeTransferLearning(progressionData) {
    const { sessionsByDifficulty } = progressionData;
    
    // Verificar se performance em nível anterior ajuda no próximo
    const levels = ['easy', 'medium', 'hard'];
    let transferScore = 0;
    let comparisons = 0;
    
    for (let i = 0; i < levels.length - 1; i++) {
      const currentLevel = sessionsByDifficulty[levels[i]];
      const nextLevel = sessionsByDifficulty[levels[i + 1]];
      
      if (currentLevel.length > 0 && nextLevel.length > 0) {
        const currentMastery = currentLevel[currentLevel.length - 1].accuracy;
        const nextInitial = nextLevel[0].accuracy;
        
        // Transferência positiva se performance inicial no próximo nível
        // é proporcional ao domínio do nível anterior
        transferScore += Math.min(1, nextInitial / Math.max(currentMastery * 0.7, 0.1));
        comparisons++;
      }
    }
    
    return comparisons > 0 ? transferScore / comparisons : 0.5;
  }

  // Continuar com métodos auxiliares e de avaliação...

  /**
   * Calcula eficiência de aprendizado
   */
  calculateLearningEfficiency(progressionData) {
    const { sessions } = progressionData;
    
    if (sessions.length < 3) return 0.5;
    
    const initialPerformance = sessions.slice(0, 2).reduce((sum, s) => sum + s.accuracy, 0) / 2;
    const finalPerformance = sessions.slice(-2).reduce((sum, s) => sum + s.accuracy, 0) / 2;
    const improvement = finalPerformance - initialPerformance;
    
    // Eficiência como melhoria por sessão
    return Math.max(0, improvement / sessions.length);
  }

  /**
   * Estima tempo para domínio
   */
  estimateTimeToMastery(progressionData) {
    const currentMastery = this.analyzeCurrentMastery(progressionData);
    const evolution = this.analyzeMasteryEvolution(progressionData);
    
    if (currentMastery.readiness) return 0; // Já dominou
    if (evolution.rate <= 0) return -1; // Não está melhorando
    
    const remaining = currentMastery.threshold - currentMastery.score;
    return Math.ceil(remaining / evolution.rate);
  }

  /**
   * Determina desafio ótimo
   */
  determineOptimalChallenge(progressionData) {
    const currentMastery = this.analyzeCurrentMastery(progressionData);
    const readiness = this.assessDifficultyReadiness(progressionData);
    
    if (readiness.readiness > 0.8) {
      return this.getNextDifficulty(progressionData.currentDifficulty) || progressionData.currentDifficulty;
    } else if (currentMastery.score < 0.6) {
      return this.getPreviousDifficulty(progressionData.currentDifficulty) || progressionData.currentDifficulty;
    }
    
    return progressionData.currentDifficulty;
  }

  /**
   * Obtém dificuldade anterior
   */
  getPreviousDifficulty(currentDifficulty) {
    const levels = ['easy', 'medium', 'hard'];
    const currentIndex = levels.indexOf(currentDifficulty);
    return currentIndex > 0 ? levels[currentIndex - 1] : null;
  }

  /**
   * Avalia nível de motivação
   */
  assessMotivationLevel(progressionData) {
    const { sessions } = progressionData;
    
    if (sessions.length < 2) return 0.5;
    
    // Baseado na consistência de participação e melhoria
    const recentSessions = sessions.slice(-5);
    const completionRate = recentSessions.filter(s => s.completed).length / recentSessions.length;
    const improvementTrend = this.calculateTrend(recentSessions.map(s => s.accuracy));
    
    return (completionRate * 0.6 + Math.max(0, improvementTrend.slope) * 0.4);
  }

  /**
   * Avalia consistência de engajamento
   */
  assessEngagementConsistency(progressionData) {
    const { sessions } = progressionData;
    
    if (sessions.length < 3) return 0.5;
    
    // Analisar regularidade de participação
    const intervals = [];
    for (let i = 1; i < sessions.length; i++) {
      intervals.push(sessions[i].timestamp - sessions[i - 1].timestamp);
    }
    
    const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
    const variance = this.calculateVariance(intervals);
    
    // Consistência como inverso da variação normalizada
    return Math.max(0, 1 - (variance / Math.max(avgInterval, 1)));
  }

  /**
   * Identifica preferência de desafio
   */
  identifyChallengePreferen ce(progressionData) {
    const { sessionsByDifficulty } = progressionData;
    
    const preferences = {};
    
    Object.entries(sessionsByDifficulty).forEach(([level, sessions]) => {
      if (sessions.length > 0) {
        const avgAccuracy = sessions.reduce((sum, s) => sum + s.accuracy, 0) / sessions.length;
        const completionRate = sessions.filter(s => s.completed).length / sessions.length;
        
        preferences[level] = {
          accuracy: avgAccuracy,
          completionRate,
          sessionCount: sessions.length,
          preference: avgAccuracy * 0.4 + completionRate * 0.4 + Math.min(sessions.length / 10, 1) * 0.2
        };
      }
    });
    
    const preferredLevel = Object.entries(preferences)
      .sort(([_, a], [__, b]) => b.preference - a.preference)[0];
    
    return preferredLevel ? preferredLevel[0] : 'medium';
  }

  /**
   * Gera recomendações adaptativas
   */
  generateAdaptiveRecommendations(progressionData) {
    const currentMastery = this.analyzeCurrentMastery(progressionData);
    const readiness = this.assessDifficultyReadiness(progressionData);
    const evolution = this.analyzeMasteryEvolution(progressionData);
    
    return {
      immediate: this.generateImmediateRecommendations(currentMastery, readiness),
      mediumTerm: this.generateMediumTermGoals(evolution, readiness),
      longTerm: this.generateLongTermObjectives(progressionData)
    };
  }

  /**
   * Gera recomendações imediatas
   */
  generateImmediateRecommendations(currentMastery, readiness) {
    const recommendations = [];
    
    if (readiness.action === 'advance') {
      recommendations.push('Introduzir próximo nível de dificuldade');
    } else if (readiness.action === 'consolidate') {
      recommendations.push('Focar na consolidação do nível atual');
    } else if (readiness.action === 'reinforce') {
      recommendations.push('Reforçar fundamentos básicos');
    }
    
    // Recomendações específicas baseadas em pontos fracos
    if (currentMastery.indicators.accuracy < 0.7) {
      recommendations.push('Praticar precisão com exercícios focados');
    }
    
    if (currentMastery.indicators.consistency < 0.6) {
      recommendations.push('Trabalhar estabilidade de performance');
    }
    
    return recommendations;
  }

  /**
   * Gera metas de médio prazo
   */
  generateMediumTermGoals(evolution, readiness) {
    const goals = [];
    
    if (evolution.phase === 'early_learning') {
      goals.push('Alcançar 70% de precisão consistente');
    } else if (evolution.phase === 'skill_building') {
      goals.push('Desenvolver automatismo nas respostas');
    } else if (evolution.phase === 'mastery_development') {
      goals.push('Preparar transição para próximo nível');
    }
    
    return goals;
  }

  /**
   * Gera objetivos de longo prazo
   */
  generateLongTermObjectives(progressionData) {
    const objectives = [];
    
    const hardLevelMastery = this.analyzeLevelMastery(progressionData, 'hard');
    
    if (!hardLevelMastery.attempted) {
      objectives.push('Progredir até o nível difícil');
    } else if (hardLevelMastery.progress !== 'mastered') {
      objectives.push('Dominar completamente o nível difícil');
    } else {
      objectives.push('Manter domínio e explorar variações');
    }
    
    return objectives;
  }

  // Continuar com métodos de avaliação, comparação e utilidades...

  /**
   * Funções auxiliares
   */
  calculateConsistency(values) {
    if (values.length < 2) return 0.5;
    
    const variance = this.calculateVariance(values);
    return Math.max(0, 1 - variance);
  }

  calculateConfidence(sessions) {
    // Confiança baseada na estabilidade da performance recente
    const recentSessions = sessions.slice(-Math.min(5, sessions.length));
    const accuracies = recentSessions.map(s => s.accuracy);
    
    return this.calculateConsistency(accuracies);
  }

  calculateVariance(values) {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return variance;
  }

  calculateSpeedImprovement(sessions) {
    if (sessions.length < 2) return 0;
    
    const firstHalf = sessions.slice(0, Math.floor(sessions.length / 2));
    const secondHalf = sessions.slice(Math.floor(sessions.length / 2));
    
    const firstAvgRT = firstHalf.reduce((sum, s) => sum + s.responseTime, 0) / firstHalf.length;
    const secondAvgRT = secondHalf.reduce((sum, s) => sum + s.responseTime, 0) / secondHalf.length;
    
    return firstAvgRT > 0 ? Math.max(0, (firstAvgRT - secondAvgRT) / firstAvgRT) : 0;
  }

  findConsistencyMilestone(sessions) {
    // Encontrar primeira vez que alcançou consistência alta
    const windowSize = Math.min(5, Math.floor(sessions.length / 2));
    
    for (let i = windowSize; i <= sessions.length; i++) {
      const window = sessions.slice(i - windowSize, i);
      const accuracies = window.map(s => s.accuracy);
      const consistency = this.calculateConsistency(accuracies);
      
      if (consistency > 0.8) {
        return {
          type: 'consistency',
          threshold: 0.8,
          achievedAt: window[window.length - 1].timestamp,
          sessions: window
        };
      }
    }
    
    return null;
  }

  calculateTotalProgress(sessions) {
    if (sessions.length < 2) return 0;
    
    const initialAccuracy = sessions[0].accuracy;
    const finalAccuracy = sessions[sessions.length - 1].accuracy;
    
    return Math.max(0, finalAccuracy - initialAccuracy);
  }

  calculateReadinessConfidence(factors) {
    const factorScores = Object.values(factors).map(f => f.score);
    const avgScore = factorScores.reduce((sum, score) => sum + score, 0) / factorScores.length;
    const variance = this.calculateVariance(factorScores);
    
    return Math.max(0, avgScore - variance);
  }

  generatePeerComparison(progressionData) {
    // Comparação simplificada com benchmarks esperados
    const currentMastery = this.analyzeCurrentMastery(progressionData);
    
    return {
      percentile: this.estimatePercentile(currentMastery.score, progressionData.currentDifficulty),
      aboveAverage: currentMastery.score > 0.7,
      comparison: currentMastery.score > 0.8 ? 'excellent' : 
                 currentMastery.score > 0.7 ? 'good' :
                 currentMastery.score > 0.6 ? 'average' : 'needs_improvement'
    };
  }

  estimatePercentile(score, difficulty) {
    // Estimativa simplificada de percentil
    const benchmarks = {
      easy: { p25: 0.6, p50: 0.75, p75: 0.85, p90: 0.95 },
      medium: { p25: 0.5, p50: 0.65, p75: 0.8, p90: 0.9 },
      hard: { p25: 0.4, p50: 0.55, p75: 0.7, p90: 0.85 }
    };
    
    const benchmark = benchmarks[difficulty] || benchmarks.medium;
    
    if (score >= benchmark.p90) return 90;
    if (score >= benchmark.p75) return 75;
    if (score >= benchmark.p50) return 50;
    if (score >= benchmark.p25) return 25;
    return 10;
  }

  assessAgeAppropriateness(currentMastery, userAge) {
    if (!userAge) return { appropriate: 'unknown' };
    
    // Expectativas básicas por idade (simplificado)
    const expectedMastery = Math.max(0.4, Math.min(0.9, 0.4 + (userAge - 4) * 0.1));
    const appropriate = currentMastery.score >= expectedMastery * 0.8;
    
    return {
      appropriate,
      expected: expectedMastery,
      actual: currentMastery.score,
      ratio: currentMastery.score / expectedMastery
    };
  }

  calculateExpectedProgression(progressionData) {
    const { sessions, userAge } = progressionData;
    
    // Progressão esperada baseada no número de sessões e idade
    const sessionFactor = Math.min(1, sessions.length / 20);
    const ageFactor = userAge ? Math.max(0.5, Math.min(1, (userAge - 3) / 7)) : 0.7;
    
    return sessionFactor * ageFactor;
  }

  identifyRiskFactors(progressionData) {
    const riskFactors = [];
    
    const evolution = this.analyzeMasteryEvolution(progressionData);
    const currentMastery = this.analyzeCurrentMastery(progressionData);
    
    if (evolution.trend === 'declining') {
      riskFactors.push('performance_decline');
    }
    
    if (currentMastery.indicators.consistency < 0.4) {
      riskFactors.push('high_variability');
    }
    
    if (progressionData.sessions.length > 10 && currentMastery.score < 0.5) {
      riskFactors.push('slow_progress');
    }
    
    const motivation = this.assessMotivationLevel(progressionData);
    if (motivation < 0.4) {
      riskFactors.push('low_motivation');
    }
    
    return riskFactors;
  }

  assessInterventionNeeds(progressionData) {
    const interventions = [];
    const riskFactors = this.identifyRiskFactors(progressionData);
    
    if (riskFactors.includes('performance_decline')) {
      interventions.push('review_strategy');
    }
    
    if (riskFactors.includes('high_variability')) {
      interventions.push('consistency_training');
    }
    
    if (riskFactors.includes('slow_progress')) {
      interventions.push('adjust_difficulty');
    }
    
    if (riskFactors.includes('low_motivation')) {
      interventions.push('motivation_enhancement');
    }
    
    return interventions.length > 0 ? interventions : ['monitoring'];
  }

  assessDataQuality(progressionData) {
    let qualityScore = 1.0;
    const issues = [];

    if (progressionData.sessions.length < 3) {
      qualityScore -= 0.4;
      issues.push('Poucos dados de sessão para análise robusta');
    }

    const sessionsWithoutAccuracy = progressionData.sessions.filter(s => 
      isNaN(s.accuracy) || s.accuracy < 0 || s.accuracy > 1
    ).length;
    
    if (sessionsWithoutAccuracy > 0) {
      qualityScore -= 0.3;
      issues.push(`${sessionsWithoutAccuracy} sessões com dados de precisão inválidos`);
    }

    const timeInconsistencies = progressionData.sessions.filter(s => 
      !s.timestamp || s.timestamp <= 0
    ).length;
    
    if (timeInconsistencies > 0) {
      qualityScore -= 0.2;
      issues.push(`${timeInconsistencies} sessões sem timestamp válido`);
    }

    return {
      score: Math.max(0, qualityScore),
      issues,
      level: qualityScore > 0.8 ? 'high' : qualityScore > 0.5 ? 'medium' : 'low'
    };
  }

  generateProgressionRecommendations(analysisResults) {
    const recommendations = [];
    const { 
      currentMastery, progressionHistory, difficultyReadiness,
      masteryEvolution, adaptiveRecommendations, retentionAnalysis 
    } = analysisResults;

    // Recomendações baseadas no domínio atual
    if (currentMastery.score < 0.6) {
      recommendations.push({
        category: 'mastery_building',
        priority: 'high',
        recommendation: 'Focar no fortalecimento das habilidades básicas no nível atual',
        rationale: `Score de domínio baixo (${Math.round(currentMastery.score * 100)}%)`
      });
    }

    // Recomendações baseadas na prontidão
    if (difficultyReadiness.readiness > 0.8) {
      recommendations.push({
        category: 'progression',
        priority: 'medium',
        recommendation: 'Introduzir próximo nível de dificuldade gradualmente',
        rationale: 'Alta prontidão para avançar detectada'
      });
    }

    // Recomendações baseadas na evolução
    if (masteryEvolution.trend === 'declining') {
      recommendations.push({
        category: 'intervention',
        priority: 'high',
        recommendation: 'Revisar estratégia de ensino e identificar causas do declínio',
        rationale: 'Tendência de declínio na performance detectada'
      });
    }

    // Recomendações baseadas na retenção
    if (retentionAnalysis.shortTerm < 0.6) {
      recommendations.push({
        category: 'retention',
        priority: 'medium',
        recommendation: 'Implementar técnicas de reforço para melhorar retenção',
        rationale: 'Baixa retenção de curto prazo identificada'
      });
    }

    // Recomendações baseadas no histórico
    if (progressionHistory.plateaus.length > 0) {
      recommendations.push({
        category: 'plateau_management',
        priority: 'medium',
        recommendation: 'Diversificar atividades para superar plateaus identificados',
        rationale: `${progressionHistory.plateaus.length} platô(s) detectado(s)`
      });
    }

    return recommendations.length > 0 ? recommendations : [{
      category: 'maintenance',
      priority: 'low',
      recommendation: 'Continuar monitoramento da progressão atual',
      rationale: 'Progressão dentro dos parâmetros esperados'
    }];
  }

  generateFallbackAnalysis(errorMessage) {
    return {
      currentLevel: 'unknown',
      masteryScore: 0,
      masteryIndicators: {},
      readinessForNext: false,
      progressionPath: [],
      milestones: [],
      regressions: [],
      plateaus: [],
      nextLevelReadiness: 0,
      readinessFactors: {},
      recommendedAction: 'maintain',
      adaptationStrategy: [],
      masteryTrend: 'unknown',
      improvementRate: 0,
      stabilityPeriod: 0,
      growthPhase: 'unknown',
      easyLevelMastery: { attempted: false },
      mediumLevelMastery: { attempted: false },
      hardLevelMastery: { attempted: false },
      shortTermRetention: 0.5,
      longTermRetention: 0.5,
      forgettingCurve: 'unknown',
      transferLearning: 0.5,
      learningEfficiency: 0,
      timeToMastery: -1,
      optimalChallenge: 'medium',
      motivationLevel: 0.5,
      engagementConsistency: 0.5,
      challengePreference: 'medium',
      immediateRecommendations: [],
      mediumTermGoals: [],
      longTermObjectives: [],
      peerComparison: { percentile: 50 },
      ageAppropriate: { appropriate: 'unknown' },
      expectedProgression: 0.5,
      totalSessions: 0,
      timeSpan: 0,
      currentDifficulty: 'unknown',
      riskFactors: ['data_collection_error'],
      interventionNeeds: ['improve_data_collection'],
      recommendations: [{
        category: 'data_collection',
        priority: 'high',
        recommendation: 'Melhorar coleta de dados de progressão',
        rationale: `Erro na análise: ${errorMessage}`
      }],
      analysisTimestamp: new Date().toISOString(),
      collectorVersion: '2.1.0',
      dataQuality: { score: 0, issues: [errorMessage], level: 'error' },
      status: 'fallback',
      error: errorMessage
    };
  }
}
