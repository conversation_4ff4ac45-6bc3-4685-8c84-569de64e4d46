/**
 * 🎨 LAYOUT UNIFICADO DOS DASHBOARDS PREMIUM
 * @file DashboardLayout.jsx
 * @description Layout consistente para todos os dashboards premium
 * @version 3.0.0
 * @premium true
 */

import React, { useState } from 'react'
import DashboardNavigation from '../DashboardNavigation/DashboardNavigation'
import LoadingSpinner from '../../common/LoadingSpinner'
import styles from './DashboardLayout.module.css'

const DashboardLayout = ({
  children,
  title,
  subtitle,
  icon,
  loading = false,
  error = null,
  activeDashboard,
  onDashboardChange,
  availableDashboards,
  showNavigation = true,
  actions = null,
  refreshAction = null,
  className = ''
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    if (refreshAction) {
      setIsRefreshing(true)
      try {
        await refreshAction()
      } catch (error) {
        console.error('Erro ao atualizar dashboard:', error)
      } finally {
        setIsRefreshing(false)
      }
    }
  }

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <LoadingSpinner message={`Carregando ${title}...`} />
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <div className={styles.errorIcon}>⚠️</div>
        <h3 className={styles.errorTitle}>Erro ao carregar dashboard</h3>
        <p className={styles.errorMessage}>{error}</p>
        {refreshAction && (
          <button 
            className={styles.retryButton}
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            {isRefreshing ? '🔄 Tentando novamente...' : '🔄 Tentar novamente'}
          </button>
        )}
      </div>
    )
  }

  return (
    <div className={`${styles.dashboardLayout} ${className}`}>
      {/* Navegação dos Dashboards */}
      {showNavigation && (
        <DashboardNavigation
          activeDashboard={activeDashboard}
          onDashboardChange={onDashboardChange}
          availableDashboards={availableDashboards}
        />
      )}

      {/* Header do Dashboard */}
      <div className={styles.dashboardHeader}>
        <div className={styles.headerContent}>
          <div className={styles.titleSection}>
            {icon && <span className={styles.titleIcon}>{icon}</span>}
            <div className={styles.titleText}>
              <h1 className={styles.title}>{title}</h1>
              {subtitle && <p className={styles.subtitle}>{subtitle}</p>}
            </div>
          </div>

          <div className={styles.headerActions}>
            {refreshAction && (
              <button
                className={`${styles.actionButton} ${styles.refreshButton}`}
                onClick={handleRefresh}
                disabled={isRefreshing}
                title="Atualizar dados"
              >
                <span className={`${styles.buttonIcon} ${isRefreshing ? styles.spinning : ''}`}>
                  🔄
                </span>
                <span className={styles.buttonText}>
                  {isRefreshing ? 'Atualizando...' : 'Atualizar'}
                </span>
              </button>
            )}
            
            {actions}
          </div>
        </div>
      </div>

      {/* Conteúdo do Dashboard */}
      <div className={styles.dashboardContent}>
        {children}
      </div>

      {/* Footer com informações */}
      <div className={styles.dashboardFooter}>
        <div className={styles.footerContent}>
          <div className={styles.footerInfo}>
            <span className={styles.footerIcon}>🔒</span>
            <span className={styles.footerText}>Dashboard Premium</span>
          </div>
          
          <div className={styles.footerStatus}>
            <div className={styles.statusDot}></div>
            <span className={styles.statusText}>Sistema Online</span>
          </div>
          
          <div className={styles.footerTime}>
            Última atualização: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardLayout
