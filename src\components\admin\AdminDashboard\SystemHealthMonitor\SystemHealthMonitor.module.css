/**
 * 🔧 SYSTEM HEALTH MONITOR V3 - UI/UX MODERNO
 * @file SystemHealthMonitor.module.css
 * @description Monitor de Saúde do Sistema com design futurístico
 * @version 3.0.0
 * @features Glassmorphism, Real-time indicators, Animations
 */

/* ===== VARIÁVEIS LOCAIS ===== */
:root {
  --health-success: #10b981;
  --health-warning: #f59e0b;
  --health-error: #ef4444;
  --health-info: #3b82f6;

  --health-bg-primary: #0f172a;
  --health-bg-secondary: #1e293b;
  --health-bg-glass: rgba(255, 255, 255, 0.1);

  --health-text-primary: #f8fafc;
  --health-text-secondary: #cbd5e1;
  --health-text-muted: #94a3b8;

  --health-border-radius: 16px;
  --health-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* ===== CONTAINER PRINCIPAL ===== */
.systemHealthMonitor {
  padding: 0;
  max-width: 100%;
  margin: 0;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== HEADER MODERNO ===== */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 24px;
  padding: 24px 32px;
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  box-shadow: var(--health-shadow);
}

.title {
  color: var(--health-text-primary);
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title::before {
  content: '🔧';
  font-size: 28px;
}

.headerInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.lastUpdate {
  color: var(--health-text-muted);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.lastUpdate::before {
  content: '🕒';
  font-size: 16px;
}

.refreshButton {
  background: linear-gradient(135deg, var(--health-info), #6366f1);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.refreshButton::before {
  content: '🔄';
  font-size: 16px;
}

.refreshButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.refreshButton:disabled {
  background: rgba(148, 163, 184, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== STATUS GERAL MODERNO ===== */
.overallStatus {
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  padding: 40px 32px;
  margin: 0 32px 32px 32px;
  box-shadow: var(--health-shadow);
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.overallStatus::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all 0.3s ease;
}

.statusHealthy::before {
  background: linear-gradient(90deg, var(--health-success), #34d399);
}

.statusWarning::before {
  background: linear-gradient(90deg, var(--health-warning), #fbbf24);
}

.statusCritical::before {
  background: linear-gradient(90deg, var(--health-error), #f87171);
}

.statusIcon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.statusText {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusHealthyText {
  color: var(--health-success);
  text-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.statusWarningText {
  color: var(--health-warning);
  text-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

.statusCriticalText {
  color: var(--health-error);
  text-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.statusDescription {
  color: var(--health-text-secondary);
  font-size: 16px;
  font-weight: 500;
}

/* ===== GRID DE MÉTRICAS MODERNO ===== */
.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin: 0 32px 32px 32px;
}

.metricCard {
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  padding: 24px;
  box-shadow: var(--health-shadow);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metricCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--health-info), #6366f1);
  transition: all 0.3s ease;
}

.metricCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.metricHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.metricTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--health-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metricStatus {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.metricStatus::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.statusOk {
  background: rgba(16, 185, 129, 0.2);
  color: var(--health-success);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.statusOk::before {
  background: var(--health-success);
}

.statusWarning {
  background: rgba(245, 158, 11, 0.2);
  color: var(--health-warning);
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.statusWarning::before {
  background: var(--health-warning);
}

.statusError {
  background: rgba(239, 68, 68, 0.2);
  color: var(--health-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.statusError::before {
  background: var(--health-error);
}

.metricValue {
  font-size: 24px;
  font-weight: 700;
  color: var(--health-text-primary);
  margin-bottom: 6px;
  line-height: 1;
}

.metricDescription {
  color: var(--health-text-secondary);
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* ===== BARRA DE PROGRESSO MODERNA ===== */
.progressBar {
  width: 100%;
  height: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.progressBar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progressFill {
  height: 100%;
  transition: width 0.8s ease;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

.progressFill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShine 2s infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progressOk {
  background: linear-gradient(90deg, var(--health-success), #34d399);
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.progressWarning {
  background: linear-gradient(90deg, var(--health-warning), #fbbf24);
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

.progressCritical {
  background: linear-gradient(90deg, var(--health-error), #f87171);
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
}

/* ===== GRID DE SERVIÇOS MODERNO ===== */
.servicesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin: 0 32px 32px 32px;
}

.serviceCard {
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  padding: 20px;
  box-shadow: var(--health-shadow);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.serviceCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}

.serviceCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
}

.serviceIcon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.serviceInfo {
  flex: 1;
}

.serviceName {
  font-weight: 600;
  color: var(--health-text-primary);
  margin-bottom: 6px;
  font-size: 16px;
}

.serviceStatus {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.serviceStatus::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.serviceHealthy {
  color: var(--health-success);
}

.serviceHealthy::before {
  background: var(--health-success);
}

.serviceCard:has(.serviceHealthy)::before {
  background: var(--health-success);
}

.serviceDown {
  color: var(--health-error);
}

.serviceDown::before {
  background: var(--health-error);
}

.serviceCard:has(.serviceDown)::before {
  background: var(--health-error);
}

.serviceDegraded {
  color: var(--health-warning);
}

.serviceDegraded::before {
  background: var(--health-warning);
}

.serviceCard:has(.serviceDegraded)::before {
  background: var(--health-warning);
}

/* ===== SEÇÃO DE ALERTAS MODERNA ===== */
.alertsSection {
  margin: 32px 32px 0 32px;
}

.alertsTitle {
  font-size: 20px;
  font-weight: 700;
  color: var(--health-text-primary);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.alertsTitle::before {
  content: '🚨';
  font-size: 24px;
}

.alertsList {
  background: var(--health-bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--health-border-radius);
  box-shadow: var(--health-shadow);
  overflow: hidden;
}

.alertItem {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 16px;
  transition: all 0.3s ease;
}

.alertItem:hover {
  background: rgba(255, 255, 255, 0.05);
}

.alertItem:last-child {
  border-bottom: none;
}

.alertItem:last-child {
  border-bottom: none;
}

/* ===== ÍCONES E CONTEÚDO DOS ALERTAS ===== */
.alertIcon {
  font-size: 20px;
  margin-top: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.alertCritical .alertIcon {
  color: var(--health-error);
  background: rgba(239, 68, 68, 0.2);
}

.alertWarning .alertIcon {
  color: var(--health-warning);
  background: rgba(245, 158, 11, 0.2);
}

.alertInfo .alertIcon {
  color: var(--health-info);
  background: rgba(59, 130, 246, 0.2);
}

.alertContent {
  flex: 1;
}

.alertTitle {
  font-weight: 600;
  color: var(--health-text-primary);
  margin-bottom: 6px;
  font-size: 16px;
}

.alertMessage {
  color: var(--health-text-secondary);
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.5;
}

.alertTime {
  color: var(--health-text-muted);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.alertTime::before {
  content: '🕒';
  font-size: 14px;
}

.noAlerts {
  text-align: center;
  padding: 60px 32px;
  color: var(--health-text-muted);
  font-size: 16px;
}

.noAlerts::before {
  content: '✅';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.loading {
  text-align: center;
  padding: 80px 32px;
  color: var(--health-text-secondary);
}

.spinner {
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top: 4px solid var(--health-info);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

/* ===== RESPONSIVIDADE MODERNA ===== */
@media (max-width: 1024px) {
  .metricsGrid,
  .servicesGrid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    margin: 0 24px 32px 24px;
  }

  .header,
  .overallStatus,
  .alertsSection {
    margin: 0 24px 32px 24px;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: stretch;
    padding: 20px 24px;
    margin: 0 16px 24px 16px;
  }

  .headerInfo {
    align-items: flex-start;
  }

  .metricsGrid,
  .servicesGrid {
    grid-template-columns: 1fr;
    margin: 0 16px 24px 16px;
    gap: 16px;
  }

  .overallStatus {
    padding: 32px 24px;
    margin: 0 16px 24px 16px;
  }

  .alertsSection {
    margin: 24px 16px 0 16px;
  }

  .statusIcon {
    font-size: 48px;
  }

  .statusText {
    font-size: 24px;
  }

  .metricValue {
    font-size: 28px;
  }

  .serviceCard {
    padding: 16px;
  }

  .alertItem {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .header {
    margin: 0 8px 20px 8px;
    padding: 16px 20px;
  }

  .metricsGrid,
  .servicesGrid,
  .overallStatus,
  .alertsSection {
    margin: 0 8px 20px 8px;
  }

  .title {
    font-size: 20px;
  }

  .statusIcon {
    font-size: 40px;
  }

  .statusText {
    font-size: 20px;
  }

  .metricCard,
  .serviceCard {
    padding: 16px;
  }

  .metricValue {
    font-size: 24px;
  }

  .serviceIcon {
    font-size: 24px;
    width: 48px;
    height: 48px;
  }
}
